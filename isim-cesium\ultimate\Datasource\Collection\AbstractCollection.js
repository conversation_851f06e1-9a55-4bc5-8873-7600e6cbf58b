/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/24 10:56:58
 * @description extract some to AbstractCollection
 * @version 1.0
 * */

import { AssociativeArray, defaultValue, defined, DeveloperError, createGuid, RuntimeError, PrimitiveCollection } from 'cesium';

export default class AbstractCollection {
  id;
  _type;
  entity;
  pluginOption;
  _owner;
  _primitives = new PrimitiveCollection();
  features = {};

  constructor(pluginOption) {
    if (!pluginOption || !pluginOption.entity) throw new RuntimeError(`PluginCollection 实例创建失败，缺少必要参数`);

    this.id = defaultValue(pluginOption.id, createGuid());
    this.pluginOption = pluginOption ?? {};
    this.entity = pluginOption.entity;
  }

  init(owner) {
    this._owner = owner;
    owner._primitiveCollection.add(this._primitives);
    this._type = owner.name;
  }

  update(time) {
    const features = this.features;
    for (let key in features) {
      const feature = features[key];
      this._updateOne(time, feature);
    }
  }

  _updateOne(time, feature) {
    feature.update(time);
  }

  add(plugin) {
    if (!defined(plugin)) {
      throw new DeveloperError('plugin is required.');
    }
    plugin.id = this.id;
    plugin.entity = this.entity;
    this.pluginOption = {
      ...this.pluginOption,
      ...plugin
    };
    return plugin;
  }

  contains(key) {
    return !!this.features[key];
  }

  remove(feature) {
    return this._primitives.remove(feature);
  }

  removeByKey(key) {
    const feature = this.features[key];
    feature && this.remove(feature);
  }

  removeAll() {
    const features = this.features;
    for (let key in features) {
      const feature = features[key];
      this.remove(feature);
    }
    this._primitives.removeAll();
  }

  _removeEntity(plugin) {
    return false;
  }
  //   从source中删除自己
  destroy() {
    if (!this._owner) return;
    this.removeAll();
    this._owner.remove(this);
  }

  get show() {
    return this._primitives.show;
  }
  set show(value) {
    this._primitives.show = value;
  }
}
