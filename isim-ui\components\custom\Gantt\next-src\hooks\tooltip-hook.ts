/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { GanttDataType } from 'isim-ui';

export const useTooltip = <T extends GanttDataType>() => {
  const targetRef = ref();
  const tooltipRef = ref();
  const visible = ref(false);
  const currentTask = ref<T>();

  const handleMouseover = (e: MouseEvent, item: T) => {
    targetRef.value = e.currentTarget;
    currentTask.value = item;
    visible.value = true;
  };
  const handleMouseout = (_e: MouseEvent) => {
    visible.value = false;
  };

  return {
    targetRef,
    tooltipRef,
    visible,
    currentTask,
    handleMouseover,
    handleMouseout
  };
};
