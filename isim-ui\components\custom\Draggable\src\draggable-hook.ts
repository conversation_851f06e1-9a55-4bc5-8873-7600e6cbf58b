/**
 * @Author: songjimin
 * @Date: 2022-04-06 12:09:11
 * @Version: 0.0.1
 * @Content:
 */
import { computed, ExtractPropTypes } from 'vue';
import { onMounted, onUnmounted, reactive, ref, toRefs, watch } from 'vue';
import type { PropType, SetupContext } from 'vue';
import { handleDataByUnit, joinUnitForNumber } from './utils';
import { useZIndex } from 'element-plus';

export interface DraggableEmitsType {
  'handle-close': (e: MouseEvent) => void;
  'handle-sure': (e: MouseEvent) => void;
  'handle-cancel': (e: MouseEvent) => void;
  'handle-drag-start': (e: MouseEvent) => void;
  'handle-drag-move': (e: MouseEvent, data: { top: number; left: number }) => void;
  'handle-drag-end': (e: MouseEvent) => void;
}
export const draggableEmits = {
  'handle-close': () => true,
  'handle-sure': () => true,
  'handle-cancel': () => true,
  'handle-drag-start': () => true,
  'handle-drag-move': () => true,
  'handle-drag-end': () => true
};

export type DraggableEmits = SetupContext<DraggableEmitsType>['emit'];

export function generateProps() {
  return {
    show: {
      type: Boolean,
      default: true
    },
    appendTo: {
      type: String
    },
    title: {
      type: String,
      default: ''
    },
    allowOut: {
      type: Boolean,
      default: false
    },
    headerShow: {
      type: Boolean,
      default: true
    },
    closeShow: {
      type: Boolean,
      default: true
    },
    closeIcon: {
      type: String,
      default: 'y-icon-guanbi'
    },
    footerShow: {
      type: Boolean,
      default: true
    },
    left: {
      type: [Number, String],
      default: 0
    },
    right: {
      type: [Number, String] as PropType<string | number>,
      default: 0
    },
    top: {
      type: [Number, String] as PropType<string | number>,
      default: 0
    },
    bottom: {
      type: [Number, String] as PropType<string | number>,
      default: 0
    },
    w: {
      type: [Number, String] as PropType<string | number>,
      default: 20
    },
    h: {
      type: [Number, String] as PropType<string | number>,
      default: 20
    },
    classNameHandle: {
      type: String,
      default: 'handleResize'
    },
    minHeight: {
      type: Number,
      default: 200
    },
    minWidth: {
      type: Number,
      default: 200
    },
    draggable: {
      type: Boolean,
      default: true
    },
    resizable: {
      type: Boolean,
      default: false
    },
    headerClassName: {
      type: String,
      default: ''
    },
    contentClass: {
      type: String,
      default: ''
    },
    contentStyle: {
      type: String,
      default: ''
    },
    parent: {
      type: String,
      default: ''
    },
    sureButtonText: {
      type: String,
      default: '确认'
    },
    sureButtonType: {
      type: String as PropType<'primary' | 'default'>,
      default: 'primary'
    },
    cancelButtonText: {
      type: String,
      default: '取消'
    },
    cancelButtonType: {
      type: String as PropType<'primary' | 'default'>,
      default: 'default'
    },
    dragElement: String as PropType<string | HTMLElement>,
    leftRestrict: Function as PropType<() => number>,
    topRestrict: Function as PropType<() => number>,
    type: {
      type: String as PropType<'plate' | 'modal'>,
      default: 'modal'
    },
    stick: Boolean,
    stickDistance: {
      type: Number,
      default: 20
    },
    filter: {
      type: Boolean,
      default: true
    },
    border: {
      type: Boolean,
      default: true
    },
    borderColor: {
      type: String,
      default: ''
    },
    transparent: {
      type: Boolean,
      default: false
    },
    zIndex: {
      type: Number
    }
  };
}

export type DraggableProps = ExtractPropTypes<ReturnType<typeof generateProps>>;

export interface InitialState {
  posX: number | string;
  posY: number | string;
  width: number | string;
  height: number | string;
}

// 初始化状态
export function useDragState(_props: DraggableProps) {
  const props = _props as DraggableProps;
  const { parent: parentProp } = toRefs(props);
  // const parentProp = props.parent;
  const _posX = props.right || props.left;
  const _posY = props.bottom || props.top;
  const sizeState = reactive<InitialState>({
    posX: _posX,
    posY: _posY,
    width: props.w,
    height: props.h
  });
  const cacheSizeState = {
    posX: _posX,
    posY: _posY,
    width: props.w,
    height: props.h
  };

  const updateWidth = (width: string) => {
    sizeState.width = width;
  };

  const updateHeight = (height: string | number) => {
    sizeState.height = height;
  };

  const updateY = (posY: string | number) => {
    sizeState.posY = posY;
  };

  const dragRef = ref();
  const { currentZIndex } = useZIndex();
  const currentIndex = ref(props.zIndex ?? currentZIndex.value);
  const parentHtml = ref();

  const getInitialPos = (parentHtml: HTMLElement) => {
    // 水平位置从右边开始算
    const _horPos = parentHtml.clientWidth - dragRef.value.clientWidth;
    const _verPos = parentHtml.clientHeight - dragRef.value.clientHeight;
    // 水平方向的位置
    if (props.right) {
      if (typeof props.right === 'string' && props.right.includes('%')) {
        sizeState.posX = _horPos - parentHtml.clientWidth * handleDataByUnit(props.right);
      } else {
        sizeState.posX = _horPos - handleDataByUnit(props.right);
      }
    }
    // 垂直方向的位置
    if (props.bottom) {
      if (typeof props.bottom === 'string' && props.bottom.includes('%')) {
        sizeState.posY = _verPos - parentHtml.clientWidth * handleDataByUnit(props.bottom);
      } else {
        sizeState.posY = _verPos - handleDataByUnit(props.bottom);
      }
    }
  };
  const getParentTag = () => {
    const _parentHtml = (parentProp.value && document.querySelector(parentProp.value)) || dragRef.value.parentNode;

    getInitialPos(_parentHtml);
    return _parentHtml;
  };
  // 响应位置变化
  watch(
    () => [props.left, props.right],
    ([left, right]) => {
      if (right) {
        sizeState.posX = right;
        return;
      }
      if (left) {
        sizeState.posX = left;
      } else {
        console.warn('there is at least one left ant right');
      }
    }
  );
  // 响应位置变化
  watch(
    () => [props.top, props.bottom],
    ([top, bottom]) => {
      if (bottom) {
        sizeState.posY = bottom;
        return;
      }
      if (top) {
        sizeState.posY = top;
      } else {
        console.warn('there is at least one top ant bottom');
      }
    }
  );
  // 响应尺寸变化
  watch(
    () => [props.w, props.h],
    ([width, height]) => {
      sizeState.width = width;
      sizeState.height = height;
    }
  );
  // 监听全屏事件
  const handleResizeChagne = () => {
    getInitialPos(parentHtml.value);
    sizeState.width = cacheSizeState.width;
    sizeState.height = cacheSizeState.height;
  };
  onMounted(() => {
    parentHtml.value = getParentTag();
    window.addEventListener('resize', handleResizeChagne);
  });
  onUnmounted(() => {
    window.removeEventListener('resize', handleResizeChagne);
  });
  const boxStyle = computed(() => {
    return {
      left: joinUnitForNumber(sizeState.posX),
      top: joinUnitForNumber(sizeState.posY),
      width: joinUnitForNumber(sizeState.width),
      height: joinUnitForNumber(sizeState.height)
    };
  });
  return {
    parentHtml,
    boxStyle,
    dragRef,
    currentIndex,
    ...toRefs(sizeState),
    cacheSizeState,

    updateWidth,
    updateHeight,
    updateY
  };
}

export type UseDragState = ReturnType<typeof useDragState>;
