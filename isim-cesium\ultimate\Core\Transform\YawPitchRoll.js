/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/13 10:57:56
 * */

import Euler from './Euler.js';

import { defaultValue, defined, DeveloperError, HeadingPitchRoll, Matrix3, Matrix4, Quaternion, Transforms, Math as CesiumMath } from 'cesium';
/**
 * Sensor
 */
const DirectionYPR = Object.freeze({
  TO_SKY: 0,
  TO_NORTH: 1,
  TO_GROUND: 2,
  TO_EAST: 3
});

export default class YawPitchRoll {
  constructor(yaw, pitch, roll) {
    this.yaw = defaultValue(yaw, 0.0);
    this.pitch = defaultValue(pitch, 0.0);
    this.roll = defaultValue(roll, 0.0);
  }

  static fromRadians(yaw, pitch, roll, direction = DirectionYPR.TO_SKY, result) {
    // >>includeStart('debug', pragmas.debug);
    if (!defined(yaw)) {
      throw new DeveloperError('yaw is required');
    }
    if (!defined(pitch)) {
      throw new DeveloperError('pitch is required');
    }
    if (!defined(roll)) {
      throw new DeveloperError('roll is required');
    }

    // >>includeEnd('debug');
    if (!defined(result)) {
      result = new YawPitchRoll();
    }

    result.yaw = yaw;
    result.pitch = getPitchByDirection(pitch, direction);
    result.roll = roll;
    return result;
  }

  static fromDegrees(yaw, pitch, roll, direction = DirectionYPR.TO_SKY, result) {
    // >>includeStart('debug', pragmas.debug);
    if (!defined(yaw)) {
      throw new DeveloperError('yaw is required');
    }
    if (!defined(pitch)) {
      throw new DeveloperError('pitch is required');
    }
    if (!defined(roll)) {
      throw new DeveloperError('roll is required');
    }
    // >>includeEnd('debug');
    if (!defined(result)) {
      result = new YawPitchRoll();
    }

    result.yaw = yaw * CesiumMath.RADIANS_PER_DEGREE;
    result.roll = roll * CesiumMath.RADIANS_PER_DEGREE;
    result.pitch = getPitchByDirection(pitch * CesiumMath.RADIANS_PER_DEGREE, direction);
    return result;
  }

  static fromHeadingPitchRollDegrees(heading, pitch, roll) {
    // >>includeStart('debug', pragmas.debug);
    if (!defined(heading)) {
      throw new DeveloperError('heading is required');
    }
    if (!defined(pitch)) {
      throw new DeveloperError('pitch is required');
    }
    if (!defined(roll)) {
      throw new DeveloperError('roll is required');
    }
    // >>includeEnd('debug');
    const hpr = HeadingPitchRoll.fromDegrees(heading, pitch, roll);
    return YawPitchRoll.fromHeadingPitchRoll(hpr);
  }

  static fromHeadingPitchRoll(hpr) {
    if (!defined(hpr)) {
      throw new DeveloperError('hpr is required');
    }
    const euler = new Euler(hpr.roll, -hpr.pitch, -hpr.heading, 'ZYX');
    euler.reorder('ZXY');
    return new YawPitchRoll(-euler.z, euler.x, euler.y);
  }

  /**
   * @description HeadingPitchRoll-quaternion
   */
  static fromQuaternion(quaternion, result) {
    if (!defined(quaternion)) {
      throw new DeveloperError('quaternion is required');
    }
    // >>includeEnd('debug');
    if (!defined(result)) {
      result = new YawPitchRoll();
    }
    const hpr = HeadingPitchRoll.fromQuaternion(quaternion);
    return YawPitchRoll.fromHeadingPitchRoll(hpr);
  }

  static clone(ypr, result) {
    if (!defined(ypr)) {
      return undefined;
    }
    if (!defined(result)) {
      return new YawPitchRoll(ypr.yaw, ypr.pitch, ypr.roll);
    }
    result.yaw = ypr.yaw;
    result.pitch = ypr.pitch;
    result.roll = ypr.roll;
    return result;
  }

  static equals(left, right) {
    return left === right || (defined(left) && defined(right) && left.yaw === right.yaw && left.pitch === right.pitch && left.roll === right.roll);
  }

  toTransforms(cartesian) {
    if (!defined(cartesian)) {
      return undefined;
    }
    const result = Transforms.eastNorthUpToFixedFrame(cartesian);
    return this.fromMatrixToTransforms(result);
  }

  /**
   * LocalFrame Transforms
   * @param {*} cartesian
   * @returns
   */
  localFrameToTransforms(cartesian) {
    if (!defined(cartesian)) {
      return undefined;
    }
    const result = Matrix4.fromTranslation(cartesian, new Matrix4());
    return this.fromMatrixToTransforms(result);
  }

  /**
   *
   * @param {*} cartesian
   * @returns
   */
  toQuaternion(cartesian) {
    const result = Matrix3.fromRotationZ(-this.yaw);

    if (this.pitch) {
      const mx = Matrix3.fromRotationX(this.pitch);
      Matrix3.multiply(result, mx, result);
    }

    if (this.roll) {
      const my = Matrix3.fromRotationY(this.roll);
      Matrix3.multiply(result, my, result);
    }
    return Quaternion.fromRotationMatrix(result);
  }

  localFrameToQuaternion(cartesian) {
    const result = Transforms.eastNorthUpToFixedFrame(cartesian);
    return this.fromMatrixToQuaternion(result);
  }

  /**
   * @param {*} cartesian
   * @returns
   */
  toHeadingPitchRoll(cartesian) {
    const modelMatrix = this.toTransforms(cartesian);
    return Transforms.fixedFrameToHeadingPitchRoll(modelMatrix);
  }

  toHeadingPitchRollDegrees(cartesian) {
    const hpr = this.toHeadingPitchRoll(cartesian);
    return [CesiumMath.toDegrees(hpr.heading), CesiumMath.toDegrees(hpr.pitch), CesiumMath.toDegrees(hpr.roll)];
  }

  /**
   * @param {*} cartesian
   */
  localFrameToHeadingPitchRoll(cartesian) {
    const quaternion = this.localFrameToQuaternion(cartesian);
    return HeadingPitchRoll.fromQuaternion(quaternion);
  }

  localFrameToHeadingPitchRollDegrees(cartesian) {
    const hpr = this.localFrameToHeadingPitchRoll(cartesian);
    return [CesiumMath.toDegrees(hpr.heading), CesiumMath.toDegrees(hpr.pitch), CesiumMath.toDegrees(hpr.roll)];
  }

  fromMatrixToTransforms(modelMatrix) {
    const result = modelMatrix.clone();
    if (this.yaw) {
      const mz = Matrix3.fromRotationZ(-this.yaw);
      Matrix4.multiplyByMatrix3(result, mz, result);
    }

    if (this.pitch) {
      const mx = Matrix3.fromRotationX(this.pitch);
      Matrix4.multiplyByMatrix3(result, mx, result);
    }

    if (this.roll) {
      const my = Matrix3.fromRotationY(this.roll);
      Matrix4.multiplyByMatrix3(result, my, result);
    }
    return result;
  }

  fromMatrixToQuaternion(modelMatrix) {
    const mat3 = Matrix4.getMatrix3(this.fromMatrixToTransforms(modelMatrix), new Matrix3());
    return Quaternion.fromRotationMatrix(mat3);
  }

  fromMatrixToHeadingPitchRoll(modelMatrix) {
    const quaternion = this.fromMatrixToQuaternion(modelMatrix);
    return HeadingPitchRoll.fromQuaternion(quaternion);
  }

  clone(result) {
    return YawPitchRoll.clone(this, result);
  }

  equals(right) {
    return YawPitchRoll.equals(this, right);
  }

  toDegrees() {
    return [CesiumMath.toDegrees(this.yaw), CesiumMath.toDegrees(this.pitch), CesiumMath.toDegrees(this.roll)];
  }

  toString() {
    return `(${this.yaw}, ${this.pitch}, ${this.roll})`;
  }
}

/**
 * @private
 * @param {Number} pitch radians
 * @param {Number} direction
 */
function getPitchByDirection(pitch, direction) {
  if (direction === DirectionYPR.TO_SKY) {
    return pitch;
  }

  if (direction === DirectionYPR.TO_NORTH) {
    return pitch - Math.PI / 2;
  }

  if (direction === DirectionYPR.TO_GROUND) {
    return pitch - Math.PI;
  }
}

YawPitchRoll.Direction = DirectionYPR;
