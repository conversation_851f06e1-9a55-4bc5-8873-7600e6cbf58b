/**
 * @Author: 宋计民
 * @Date: 2023/11/9 13:58
 * @Version: 1.0
 * @Content: 事件的核心模块
 */

import { Cartesian2, Entity, KeyboardEventModifier, ScreenSpaceEventType, Viewer } from 'cesium';
import { getViewerName, onViewerCreated, onViewerDestroyed } from 'isim-cesium';
import { hasInjectionContext } from 'vue';

/**
 * 事件的区域
 * 在entity上
 * 在非entity上 非entity上定义为空白区域
 * 全局模式 任意地方都触发
 */
export const enum AreaEnum {
  entityArea,
  emptyArea,
  globalArea
}

export interface MotionEvent {
  startPosition: Cartesian2;
  endPosition: Cartesian2;
}

export interface PositionedEvent {
  position: Cartesian2;
}

/**
 * 回到函数的类型
 */
export type EventCallback<T, U> = (
  options: {
    position: T extends ScreenSpaceEventType.MOUSE_MOVE ? MotionEvent : PositionedEvent;
  } & (U extends AreaEnum.entityArea ? { entity: Entity } : {})
) => void;

/**
 * 事件下的以点击区域为键的事件集合
 */
type AreaEventMap<T, U extends AreaEnum = AreaEnum> = Map<AreaEnum, Set<EventCallback<T, U>>>;

/**
 * 记录cesium的事件类型是否注册到viewer上
 */
interface EventScreenMapValue<T extends ScreenSpaceEventType> {
  areaEventMap: AreaEventMap<T>;
  isRegistry: boolean;
}

type ScreenMapKey = ScreenSpaceEventType | string; // 考虑组合键事件 ctrl+click

type EventScreenMap<T extends ScreenSpaceEventType = ScreenSpaceEventType> = Map<ScreenMapKey, EventScreenMapValue<T>>;

type ViewerEventMap = Map<string, EventScreenMap>;

/**
 * @example {
 *   viewerName: {}
 * }
 */
const viewerEventMap = new Map();

/**
 * 事件配置参数
 */
export interface EventOptions {
  viewerName?: string;
  isEntity?: boolean;
  isEmpty?: boolean;
  once?: boolean; // 暂未支持
}

export type AreaByOptions<K extends EventOptions> = K['isEntity'] extends true
  ? AreaEnum.entityArea
  : K['isEmpty'] extends true
    ? AreaEnum.emptyArea
    : AreaEnum.globalArea;

/**
 * 根据配置获取操作区域
 * @param options
 */
function getAreaTypeByOptions(options: EventOptions) {
  return options.isEntity ? AreaEnum.entityArea : options.isEmpty ? AreaEnum.emptyArea : AreaEnum.globalArea;
}

/**
 * 根据viewerName获取viewer事件集合
 * @param viewerMap
 * @param viewerName
 */
function getEventMapByViewerName(viewerMap: ViewerEventMap, viewerName = getViewerName()) {
  if (!viewerMap.has(viewerName)) {
    viewerMap.set(viewerName, new Map());
    onViewerDestroyed(() => viewerMap.delete(viewerName), { viewerName });
  }
  return viewerMap.get(viewerName)!;
}

/**
 * 根据事件操作类型获取事件相关对象
 * 事件集合 和 事件是否注册参数
 * @param viewerMap
 * @param eventType
 * @param modifier
 */
function getEventMapByScreenSpaceType(viewerMap: EventScreenMap, eventType: ScreenSpaceEventType, modifier?: KeyboardEventModifier) {
  const _eventType: ScreenMapKey = modifier ? `${eventType}_${modifier}` : eventType;
  if (!viewerMap.has(_eventType)) {
    viewerMap.set(_eventType, {
      areaEventMap: new Map(),
      isRegistry: false
    });
  }
  return viewerMap.get(_eventType)!;
}

/**
 * 根据点击区域类型获取区域相关函数集合
 * @param areaEventMap
 * @param area
 */
function getAreaEventByAreaEnum<T, U extends AreaEnum>(areaEventMap: AreaEventMap<T, U>, area: AreaEnum) {
  if (!areaEventMap.has(area)) {
    areaEventMap.set(area, new Set());
  }
  return areaEventMap.get(area)!;
}

/**
 * 获取事件集合快捷方法
 * @param viewerNam
 * @param eventType
 * @param areaType
 * @param modifier
 */
function getViewerEventAreaSet(viewerNam: string, eventType: ScreenSpaceEventType, areaType: AreaEnum, modifier?: KeyboardEventModifier) {
  const viewerEvent = getEventMapByViewerName(viewerEventMap, viewerNam);
  const eventTypeMap = getEventMapByScreenSpaceType(viewerEvent, eventType, modifier);
  const areaEventMap = eventTypeMap.areaEventMap;
  return getAreaEventByAreaEnum(areaEventMap, areaType);
}

export function createEventHandler<T extends ScreenSpaceEventType, U extends KeyboardEventModifier>(
  eventType: T,
  isSetup: boolean = false,
  modifier?: U
) {
  return function <K extends EventOptions>(fn: EventCallback<T, AreaByOptions<K>>, options?: K) {
    const areaType = getAreaTypeByOptions(options ?? {});
    onViewerCreated(
      (viewer) => {
        const viewerName = getViewerName(options?.viewerName);
        const viewerEvent = getEventMapByViewerName(viewerEventMap, viewerName);
        const eventTypeMap = getEventMapByScreenSpaceType(viewerEvent, eventType, modifier);
        const areaEventMap = eventTypeMap.areaEventMap;
        const areaEventSet = getAreaEventByAreaEnum(areaEventMap, areaType);
        areaEventSet.add(fn);
        if (!eventTypeMap.isRegistry) {
          setEventInputAction(viewer, viewerName, eventType, modifier);
        }
      },
      options?.viewerName
    );

    const closeEvent = () => {
      const areaEventSet = getViewerEventAreaSet(getViewerName(options?.viewerName), eventType, areaType, modifier);
      areaEventSet.delete(fn);
    };
    if (isSetup && hasInjectionContext()) {
      onScopeDispose(closeEvent);
    }
    return closeEvent;
  };
}

/**
 * 注册执行事件
 * @param viewer
 * @param viewerName
 * @param eventType
 * @param modifier
 */
function setEventInputAction<T extends ScreenSpaceEventType>(viewer: Viewer, viewerName: string, eventType: T, modifier?: KeyboardEventModifier) {
  viewer.screenSpaceEventHandler.setInputAction(
    (pos: any) => {
      viewerName = getViewerName(viewerName);
      const viewerEvent = getEventMapByViewerName(viewerEventMap, viewerName);
      const eventTypeMap = getEventMapByScreenSpaceType(viewerEvent, eventType, modifier);
      const areaEventMap = eventTypeMap.areaEventMap;
      let entity: Entity | undefined;
      if (eventType === ScreenSpaceEventType.MOUSE_MOVE) {
        entity = getEntityByPosition(pos.endPosition, viewer);
      }
      entity = getEntityByPosition(pos.position, viewer);
      if (entity) {
        const entityEventList = getAreaEventByAreaEnum(areaEventMap, AreaEnum.entityArea);
        entityEventList.forEach((item) => item({ entity, position: pos }));
      } else {
        const emptyEventList = getAreaEventByAreaEnum(areaEventMap, AreaEnum.emptyArea);
        emptyEventList.forEach((item) => item({ position: pos }));
      }
      const globalEventList = getAreaEventByAreaEnum(areaEventMap, AreaEnum.globalArea);
      globalEventList.forEach((item) => item({ position: pos }));
    },
    eventType,
    modifier
  );
}

export function getEntityByPosition(position: Cartesian2, viewer: Viewer) {
  if (!position) {
    return null;
  }
  // 选取实体的范围
  const pick = viewer.scene.pick(position, 30, 30);
  return pick && isEntity(pick?.id) && pick.id;
}

/**
 * 根据屏幕坐标获取entityList
 * @param position
 * @param viewer
 */
export function getEntitiesByPosition(position: Cartesian2, viewer: Viewer) {
  const pick = viewer.scene.drillPick(position, 20, 30, 30);
  const entities = new Set<Entity>();
  pick.forEach((item) => {
    const en = item.id;
    if (isEntity(en) && !entities.has(en)) {
      entities.add(en);
    }
  });
  return Array.from(entities);
}

export function getPrimitiveByPosition(position: Cartesian2, viewer: Viewer) {
  if (!position) {
    return null;
  }
  const pick = viewer.scene.pick(position);
  return pick && pick.primitive;
}

export function hasPrimitiveByPosition(position: Cartesian2, viewer: Viewer) {
  if (!position) {
    return null;
  }
  const pick = viewer.scene.pick(position);
  if (pick?.primitive) {
    return pick;
  }
}

export function isEntity(data: any) {
  return data instanceof Entity;
}
