import { Gantt } from './Gantt.ts';
import { GanttOption } from './GanttOption.ts';

/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
export class GanttCurrentTimeLine extends GanttOption {
  graphic: Gantt;
  lineWidth: number = 4;
  left: number = 0;

  constructor(gantt: Gantt) {
    super(gantt.options);
    this.graphic = gantt;
    this.calcLeft();
  }

  calcLeft() {
    this.left = this.startX + (this.currentWidth * (this.currentTime - this.minTime)) / (this.maxTime - this.minTime);
  }

  get lineStartX() {
    return this.left - this.lineWidth / 2;
  }
  get lineStopX() {
    return this.left + this.lineWidth / 2;
  }

  clear() {
    const ctx = this.graphic.ctx;
    const width = this.lineWidth / 2;
    ctx.clearRect(this.left - width, 0, this.lineWidth, this.height);
  }

  render() {
    const ctx = this.graphic.ctx;
    const width = this.lineWidth / 2;
    // ctx.clearRect(this.left - width, 0, this.lineWidth, this.height);
    // this.clear();
    this.calcLeft();
    ctx.fillStyle = 'red';
    ctx.fillRect(this.left - width, 0, this.lineWidth, this.height);
  }
}
