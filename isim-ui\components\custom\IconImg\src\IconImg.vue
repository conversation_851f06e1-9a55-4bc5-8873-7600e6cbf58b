<!--
* <AUTHOR> 宋计民
* @Date :  2022/11/5
* @Version : 1.0
* @Content : rtIconImg
-->
<template>
  <i v-if="props.type?.startsWith('sim-icon-')" :class="props.type"></i>
  <img v-else class="rt-icon-img" :src="url" alt="图片加载失败" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { defineIconImageProps } from 'isim-ui';
defineOptions({
  name: 'RtIconImg'
});
const props = defineProps({
  ...defineIconImageProps()
});

const url = computed(() => {
  return `/assets/imgs/icon-image/${props.type}.png`;
});
</script>
<style scoped lang="less">
.rt-icon-img {
  width: 30px;
}

.y-icon {
  font-size: 18px;
  color: var(--orange-color);
}

.y-icon-pinggu {
  transform: rotate(90deg);
}
</style>
