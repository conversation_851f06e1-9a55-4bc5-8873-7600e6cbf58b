import Quaternion from './Quaternion.js';
import Matrix4 from './Matrix4.js';

const { Matrix3 } = Cesium;

const _matrix01 = new Matrix4();
const _quaternion = new Quaternion();

export default class Euler {
  constructor(x = 0, y = 0, z = 0, order = Euler.DefaultOrder) {
    this._x = x;
    this._y = y;
    this._z = z;
    this._order = order;
  }

  get x() {
    return this._x;
  }

  set x(value) {
    this._x = value;
    this._onChangeCallback();
  }

  get y() {
    return this._y;
  }

  set y(value) {
    this._y = value;
    this._onChangeCallback();
  }

  get z() {
    return this._z;
  }

  set z(value) {
    this._z = value;
    this._onChangeCallback();
  }

  get order() {
    return this._order;
  }

  set order(value) {
    this._order = value;
    this._onChangeCallback();
  }

  set(x, y, z, order) {
    this._x = x;
    this._y = y;
    this._z = z;
    this._order = order || this._order;

    this._onChangeCallback();

    return this;
  }

  clone() {
    return new this.constructor(this._x, this._y, this._z, this._order);
  }

  copy(euler) {
    this._x = euler._x;
    this._y = euler._y;
    this._z = euler._z;
    this._order = euler._order;

    this._onChangeCallback();

    return this;
  }

  /**
   * Convert matrix
   */
  setFromRotationMatrix(matrix, order, update) {
    const m11 = matrix[0],
      m12 = matrix[3],
      m13 = matrix[6];
    const m21 = matrix[1],
      m22 = matrix[4],
      m23 = matrix[7];
    const m31 = matrix[2],
      m32 = matrix[5],
      m33 = matrix[8];

    order = order || this._order;

    switch (order) {
      case 'XYZ':
        this._y = Math.asin(clamp(m13, -1, 1));

        if (Math.abs(m13) < 0.9999999) {
          this._x = Math.atan2(-m23, m33);
          this._z = Math.atan2(-m12, m11);
        } else {
          this._x = Math.atan2(m32, m22);
          this._z = 0;
        }

        break;

      case 'ZXY':
        this._x = Math.asin(clamp(m32, -1, 1));

        if (Math.abs(m32) < 0.9999999) {
          this._y = Math.atan2(-m31, m33);
          this._z = Math.atan2(-m12, m22);
        } else {
          this._y = 0;
          this._z = Math.atan2(m21, m11);
        }

        break;

      case 'ZYX':
        this._y = Math.asin(-clamp(m31, -1, 1));

        if (Math.abs(m31) < 0.9999999) {
          this._x = Math.atan2(m32, m33);
          this._z = Math.atan2(m21, m11);
        } else {
          this._x = 0;
          this._z = Math.atan2(-m12, m22);
        }

        break;

      default:
        console.warn(`unknown order: ${order}`);
    }

    this._order = order;

    if (update !== false) this._onChangeCallback();

    return this;
  }

  reorder(newOrder) {
    _quaternion.setFromEuler(this);
    return this.setFromQuaternion(_quaternion, newOrder);
  }

  setFromQuaternion(q, order, update) {
    const _matrix = Matrix3.fromQuaternion(q, new Matrix3());
    _matrix01.makeRotationFromQuaternion(q);

    return this.setFromRotationMatrix(_matrix, order, update);
  }

  _onChangeCallback() {}
}

Euler.prototype.isEuler = true;
Euler.DefaultOrder = 'XYZ';
Euler.RotationOrders = ['XYZ', 'ZXY', 'ZYX'];

/**
 * @private
 */
function clamp(value, min, max) {
  return Math.max(min, Math.min(max, value));
}
