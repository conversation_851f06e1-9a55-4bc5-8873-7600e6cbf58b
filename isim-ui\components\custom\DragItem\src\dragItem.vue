<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/27
* @Version : 1.0
* @Content : dragItem
-->
<template>
  <div class="sim-drag-item">
    <slot />
  </div>
</template>

<script lang="ts">
export default {
  name: 'SimDragItem'
};
</script>
<script lang="ts" setup></script>
<style scoped lang="less">
.sim-drag-item {
  position: relative;
  width: 252px;
  min-height: 40px;
  box-sizing: border-box;
  /* 主色-15 */
  background: var(--primary-color-1_5);
  /* 主色-30 */
  border: 1px solid var(--primary-color-3);
  border-radius: 6px;
  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 0 20px;
  margin-top: 8px;
  .drag-icon {
    content: '';
    background-image: url('../asset/list-icon.png');
    width: 8px;
    height: 16px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
  &:before {
    .drag-icon;
    left: 4px;
  }
  &:after {
    .drag-icon;
    right: 4px;
  }
  &:hover {
    background: linear-gradient(
      180deg,
      rgba(var(--btn-top-color-val), 1) 0%,
      rgba(var(--btn-center-color-val), 1) 60%,
      rgba(var(--btn-bottom-color-val), 1) 100%
    );
  }
}
</style>
