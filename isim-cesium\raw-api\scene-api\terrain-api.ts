import {
  getViewer,
  ViewerNameType,
  defaultConfig,
  hasImageryProviderByViewer,
  imageryProviderIsShow,
  hiddenImageryProvider,
  visibleImageryProvider,
  createWebTileMapServiceImageryProvider,
  addImageryProvider,
  onViewerCreated
} from 'isim-cesium';
import { CesiumTerrainProvider, EllipsoidTerrainProvider, GeographicTilingScheme, Rectangle } from 'cesium';
import { getGlobalConfigByField } from '@/global-page-config.ts';

export function addTerrain(terrainConfig: ConstructorParameters<typeof CesiumTerrainProvider>[0], viewerName?: ViewerNameType) {
  onViewerCreated(() => {
    const viewer = getViewer(viewerName);
    viewer.terrainProvider = new CesiumTerrainProvider(terrainConfig);
  });
}

export function resetTerrain(viewerName?: ViewerNameType) {
  const viewer = getViewer(viewerName);
  viewer.terrainProvider = new EllipsoidTerrainProvider();
}

export function hasTerrain(viewerName?: ViewerNameType) {
  const viewer = getViewer(viewerName);
  return !(viewer.terrainProvider instanceof EllipsoidTerrainProvider);
}

export function addDefaultTerrain(viewerName?: ViewerNameType) {
  addTerrain({ url: defaultConfig.defaultTerrainUrl }, viewerName);
}

/**
 * 加载海图操作
 */
export function loadOceanImageryLayer() {
  const name = 'global_chart';
  if (hasImageryProviderByViewer(name)) {
    if (imageryProviderIsShow(name)) {
      hiddenImageryProvider(name);
      return;
    }
    visibleImageryProvider(name);
  }
  const bbox = [-179.9999971999999, 179.9999971999999, -61.7333333, 78.3666667];
  const rectangle = Rectangle.fromDegrees(bbox[0], bbox[2], bbox[1], bbox[3]);
  const wtmsLayer = createWebTileMapServiceImageryProvider({
    url: getGlobalConfigByField('oceanImageryProxy'),
    layer: `${name}_group`,
    tilingScheme: new GeographicTilingScheme(),
    format: `image/png`,
    rectangle,
    style: 'default'
  });
  addImageryProvider(name, wtmsLayer);
}
