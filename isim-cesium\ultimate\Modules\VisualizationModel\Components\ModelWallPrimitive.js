/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/08/10 22:15:23
 * @description ModelWallPrimitive
 * @version 1.0
 * */
import {
  Cartesian2,
  Cartesian3,
  Cartographic,
  defaultValue,
  GeometryInstance,
  HeadingPitchRoll,
  Material,
  MaterialAppearance,
  Primitive,
  PrimitiveCollection,
  WallGeometry
} from 'cesium';
import { createModel } from '../VisualizerMethod.js';
import AbstractPrimitive from '../AbstractVisualizer.js';

export default class ModelWallPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._gapSpace = defaultValue(options.gapSpace, 1);
    this._url = options.url;
    this._image = options.image;
    this._heading = defaultValue(options.heading, 0);
    this._modelHeight = defaultValue(options.modelHeight, 1.5);

    this._positions = options.positions;
    this._hpr = HeadingPitchRoll.fromDegrees(this._heading, 0, 0);

    this._wallPositions = null;
    this._maximumHeights = null;
    this._minimumHeights = null;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._nodePrimitive = this._nodePrimitive && this._nodePrimitive.destroy();
      this._wallPrimitive = this._wallPrimitive && this._wallPrimitive.destroy();
      this._nodePrimitive = this._updateModel();
      this._wallPrimitive = this._updateWall();
    }

    this._nodePrimitive?.update(frameState);
    this._wallPrimitive?.update(frameState);
  }

  _updateWall() {
    const geometry = new WallGeometry({
      positions: this._wallPositions,
      minimumHeights: this._minimumHeights,
      maximumHeights: this._maximumHeights
    });

    return new Primitive({
      geometryInstances: new GeometryInstance({
        geometry
      }),
      appearance: new MaterialAppearance({
        material: Material.fromType(Material.ImageType, {
          image: this._image,
          repeat: new Cartesian2((this._wallPositions.length - 1) * 10, 8)
        })
      })
    });
  }

  _updateModel() {
    if (!this._url) {
      return;
    }
    const primitives = new PrimitiveCollection();
    const positions = this._positions;
    this._wallPositions = [];
    this._maximumHeights = [];
    this._minimumHeights = [];
    let pos, posNext, distance, t, posNew, carto;
    for (let i = 0; i < positions.length - 1; i++) {
      pos = positions[i];
      if (pos && !Cartesian3.equals(pos, posNew)) {
        carto = Cartographic.fromCartesian(pos);
        this._minimumHeights.push(carto.height);
        this._maximumHeights.push(carto.height + this._modelHeight);
        this._wallPositions.push(pos);
        createModel(primitives, this._url, pos, this._hpr);
      }
      posNext = positions[i + 1];
      distance = Cartesian3.distance(pos, posNext);
      t = Math.round(distance / this._gapSpace);
      for (let j = 1 / t; j < 1.0; j += 1 / t) {
        posNew = new Cartesian3();
        Cartesian3.lerp(pos, posNext, j, posNew);
        carto = Cartographic.fromCartesian(posNew);
        this._minimumHeights.push(carto.height);
        this._maximumHeights.push(carto.height + this._modelHeight);
        this._wallPositions.push(posNew);
        createModel(primitives, this._url, posNew, this._hpr);
      }
    }

    if (posNext && !Cartesian3.equals(posNext, posNew)) {
      carto = Cartographic.fromCartesian(posNext);
      this._minimumHeights.push(carto.height);
      this._maximumHeights.push(carto.height + this._modelHeight);
      this._wallPositions.push(posNext);
      createModel(primitives, this._url, posNext, this._hpr);
    }

    return primitives;
  }

  destroy() {
    this._nodePrimitive = this._nodePrimitive && this._nodePrimitive.destroy();
    this._wallPrimitive = this._wallPrimitive && this._wallPrimitive.destroy();
    return super.destroy();
  }
}
