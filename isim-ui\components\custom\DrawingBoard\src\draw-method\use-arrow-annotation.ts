/**
 * @Author: 宋计民
 * @Date: 2023-08-30 17:06
 * @Version: 1.0
 * @Content: 绘制箭头
 */
import { AnnotationConfig, AnnotationGraphic, AnnotationOptionsType, refreshAnnotations, setAnnotations } from './annotation-graphic';
import { useAnnotationsInject } from '../use-annotations-inject';

export function useArrowAnnotation(config: AnnotationConfig) {
  const { ctx, drawBoxRef, clearAnnotations, bindMouseUpAndLeave } = useAnnotationsInject();
  const start = () => {
    const canvas = drawBoxRef.value;
    canvas.onmousedown = (e) => {
      const startX = e.offsetX;
      const startY = e.offsetY;
      const arrow = new ArrowAnnotation(ctx.value, { ...toRaw(config), startX, startY });
      setAnnotations(arrow);
      canvas.onmousemove = (ev) => {
        arrow.stopX = ev.offsetX;
        arrow.stopY = ev.offsetY;
        clearAnnotations();
        refreshAnnotations();
      };
      bindMouseUpAndLeave();
    };
  };
  return {
    start
  };
}

interface ScreenPos {
  x: number;
  y: number;
}

export class ArrowAnnotation extends AnnotationGraphic {
  color: string;
  ctx: CanvasRenderingContext2D;
  startX: number;
  startY: number;
  width: number;
  stopX: number;
  stopY: number;
  arrowWidth: number;

  constructor(ctx: CanvasRenderingContext2D, options: AnnotationOptionsType) {
    super();
    const { color, startX, startY, width } = options;
    this.color = color;
    this.width = width;
    this.startX = startX;
    this.startY = startY;
    this.stopX = startX;
    this.stopY = startY;
    this.ctx = ctx;
    this.arrowWidth = 5;
  }

  getAngle() {
    const dx = this.stopX - this.startX;
    const dy = this.stopY - this.startY;
    return Math.atan2(dy, dx);
  }
  createRectangle() {}
  createArrow() {
    console.log(this.getAngle());
  }

  calculatePointOnLine(start: ScreenPos, stop: ScreenPos, distance: number) {
    const { x: x1, y: y1 } = start;
    const { x: x2, y: y2 } = stop;
    const dx = x1 - x2;
    const dy = y1 - y2;
    const len = Math.sqrt(dx ** 2 + dy ** 2);
    const ratio = distance / len;
    const x = x2 + dx * ratio;
    const y = y2 + dy * ratio;
    return {
      x,
      y
    };
  }
  draw(): void {
    const ctx = this.ctx;
    const startX = this.startX;
    const startY = this.startY;
    const stopX = this.stopX;
    const stopY = this.stopY;
    const angle = this.getAngle();
    const arrowHeight = 20;
    const arrowWidth = 20;
    ctx.beginPath();
    ctx.lineWidth = this.arrowWidth;
    ctx.moveTo(startX, startY);
    const { x, y } = this.calculatePointOnLine({ x: startX, y: startY }, { x: stopX, y: stopY }, arrowHeight);
    ctx.lineTo(x, y);
    ctx.strokeStyle = this.color;
    ctx.stroke();

    ctx.save();
    ctx.translate(stopX - Math.cos(angle), stopY - Math.sin(angle));
    ctx.rotate(angle);
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(-arrowHeight, arrowWidth / 2);
    ctx.lineTo(-arrowHeight, -arrowWidth / 2);
    ctx.closePath();
    ctx.fillStyle = this.color;
    ctx.fill();
    ctx.restore();
  }
}
