/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import '@vue/runtime-core';
type SimUIType = typeof import('isim-ui');
declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    SimButton: SimUIType['SimButton'];
    SimAttribute: SimUIType['SimAttribute'];
    SimAttributeItem: SimUIType['SimAttributeItem'];
    SimBehavior: SimUIType['SimBehavior'];
    SimBehaviorRead: SimUIType['SimBehaviorRead'];
    SimCard: SimUIType['SimCard'];
    SimFold: SimUIType['SimFold'];
    SimDraggable: SimUIType['SimDraggable'];
    SimDraggable2: SimUIType['SimDraggable2'];
    SimLink: SimUIType['SimLink'];
    SimIcon: SimUIType['SimIcon'];
    SimSuffix: SimUIType['SimSuffix'];
    SimMessageBox: SimUIType['SimMessageBox'];
    SimDrawingBoard: SimUIType['SimDrawingBoard'];
    SimPickFile: SimUIType['SimPickFile'];
    SimDragItem: SimUIType['SimDragItem'];
    SimResizable: SimUIType['SimResizable'];
    SimSplit: SimUIType['SimSplit'];
    SimSplitItem: SimUIType['SimSplitItem'];
    SimGanttTimeline: SimUIType['SimGanttTimeline'];
    SimGanttTimelineNew: SimUIType['SimGanttTimelineNew'];
    SimGanttContent: SimUIType['SimGanttContent'];
  }
}
