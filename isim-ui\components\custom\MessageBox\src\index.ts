import { VNode, isVNode, createVNode, render, ComponentPublicInstance } from 'vue';

import { isElement, isString, isFunction, isObject, isUndefined } from '@/utils';

import { MessageBoxState, Action, Callback, MessageBoxInfo } from './MessageBox.type';

import MessageBoxCom from './MessageBox.vue';
import { hasOwn } from '@vueuse/core';

/**
 * message实例集合
 */
const messageInstance = new Map<
  ComponentPublicInstance<{ doClose: () => void }>,
  {
    options: any;
    callback: Callback | undefined;
    resolve: (res: any) => void;
    reject: (res: any) => void;
  }
>();

/**
 * 创建div
 */
const genContainer = () => {
  return document.createElement('div');
};

/**
 * 获取添加弹窗位置
 * @param props
 */
const getAppendToElement = (props: MessageBoxState) => {
  let appendTo: HTMLElement | null = document.body;

  // 是否指定添加位置
  if (props.appendTo) {
    // 传入的class/id
    if (isString(props.appendTo)) {
      appendTo = document.querySelector<HTMLElement>(props.appendTo);
    }
    // 传入html对象
    if (isElement(props.appendTo)) {
      appendTo = props.appendTo;
    }
  }
  return appendTo;
};

/**
 * 初始化
 * @param props
 * @param container
 * @param appContext
 */
const initInstance = (props: any, container: HTMLElement, appContext: MessageBoxInfo['_context'] = null) => {
  // 判断是否是函数
  // message 是否是自定义渲染子集
  // const children = isFunction(props.message) || isVNode(props.message) ? { default: isFunction(props.message) ? props.message : () => props.message } : null
  // 创建虚拟DOM
  const vNode = createVNode(
    MessageBoxCom,
    props,
    isFunction(props.message) || isVNode(props.message) ? { default: isFunction(props.message) ? props.message : () => props.message } : null
  );
  vNode.appContext = appContext;

  // 渲染
  render(vNode, container);
  // 添加至指定节点
  getAppendToElement(props)?.appendChild(container!);
  // console.log('vNode', vNode)
  return vNode.component;
};

const showMessage = (options: any) => {
  const container = genContainer();

  options.onVanish = () => {
    render(null, container);
    messageInstance.delete(vm);
  };

  options.onAction = (action: Action) => {
    const curVm = messageInstance.get(vm)!;

    let resolve: Action | { value: string; action: Action } = action;

    // 处理 prompt 模式
    if (options.showInput) {
      resolve = { value: vm.inputValue, action };
    } else {
      resolve = action;
    }

    if (options.callback) {
      options.callback(resolve, vm);
    } else if (action === 'cancel' || action === 'close') {
      if (options.distinguishCancelAndClose && action !== 'cancel') {
        curVm.reject('close');
      } else {
        curVm.reject('cancel');
      }
    } else {
      curVm.resolve(resolve);
    }
  };

  const instance = initInstance(options, container);
  // @ts-ignore
  const vm = instance.exposed.state as ComponentPublicInstance<
    {
      visible: boolean;
      doClose: () => void;
    } & MessageBoxState
  >;

  // console.log('options', options)

  for (const prop in options) {
    // && !hasOwn(vm.$props, prop)
    if (hasOwn(options, prop)) {
      vm[prop as keyof ComponentPublicInstance] = options[prop];
    }
  }

  vm.visible = true;
  return vm;
};

const MessageBoxFn = (options: Partial<MessageBoxState> | string | VNode): Promise<Action> => {
  let callback: Callback | undefined;

  // 传入的是string类型
  if (isString(options) || isVNode(options)) {
    options = {
      message: options
    };
  } else {
    callback = options.callback!;
  }

  return new Promise((resolve, reject) => {
    const vm = showMessage(options);

    messageInstance.set(vm, { options, callback, resolve, reject });
  });
};
//
const MESSAGE_BOX_VARIANTS = ['alert', 'confirm', 'prompt'] as const;
const MESSAGE_BOX_DEFAULT_OPTS = {
  alert: { closeOnPressEscape: false, closeOnClickModal: false, showCancelButton: false },
  confirm: { showCancelButton: true },
  prompt: { showCancelButton: true, showInput: true }
};

type MessageBoxType = typeof MessageBoxFn & MessageBoxInfo;

const MessageBox = MessageBoxFn as MessageBoxType;

const messageBoxFactory = (boxType: (typeof MESSAGE_BOX_VARIANTS)[number]) => {
  return (message: string | VNode, title: string, options?: Partial<MessageBoxState> | string) => {
    let titleOrOpts = '';
    if (isObject(title)) {
      options = title;
      titleOrOpts = '';
    } else if (isUndefined(title)) {
      titleOrOpts = '';
    } else {
      titleOrOpts = title as string;
    }

    return MessageBoxFn(
      Object.assign(
        {
          title: titleOrOpts,
          message,
          type: '',
          ...MESSAGE_BOX_DEFAULT_OPTS[boxType]
        },
        options,
        { boxType }
      )
    );
  };
};

MESSAGE_BOX_VARIANTS.forEach((boxType: (typeof MESSAGE_BOX_VARIANTS)[number]) => {
  MessageBox[boxType] = messageBoxFactory(boxType);
});

export default MessageBox;
