<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/1
* @Version : 1.0
* @Content : Model
-->
<template>
  <el-dialog v-model="visible" @close="handleClose" v-bind="{ ...props, ...$attrs }" :style="modelStyle" :show-close="false" class="sim-dialog">
    <template #header v-if="headerShow">
      <div class="sim-dialog__header">
        <slot name="header">
          <span class="sim-dialog__title">{{ title }}</span>
        </slot>
        <div v-if="closeShow" class="sim-dialog__close" @click="handleClose">
          <sim-icon name="guanbi" />
        </div>
      </div>
    </template>
    <div class="sim-dialog__content">
      <slot />
    </div>
    <template #footer v-if="footerShow">
      <slot name="footer">
        <sim-button v-if="showConfirmButton" :size="buttonSize" type="primary" :class="confirmButtonClass" @click="handleSure"
          >{{ confirmButtonText }}
        </sim-button>
        <sim-button v-if="showCancelButton" :size="buttonSize" :class="cancelButtonClass" @click="handleCancel">{{ cancelButtonText }}</sim-button>
      </slot>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { SimButton } from 'isim-ui';
import { computed, type PropType } from 'vue';
import { isString } from '@/utils/data-type';
import { dialogProps } from 'element-plus';

defineOptions({
  name: 'SimDialog',
  inheritAttrs: false
});

const props = defineProps({
  ...dialogProps,
  title: {
    type: String,
    default: ''
  },
  customClass: {
    type: String,
    default: ''
  },
  footerShow: {
    type: Boolean,
    default: true
  },
  headerShow: {
    type: Boolean,
    default: true
  },
  closeShow: {
    type: Boolean,
    default: true
  },
  width: {
    type: [Number, String],
    default: '500px'
  },
  height: {
    type: [Number, String],
    default: '500px'
  },
  appendToBody: {
    type: Boolean,
    default: false
  },
  target: {
    type: [String, HTMLElement],
    default: 'body'
  },
  //是否显示取消按钮
  showCancelButton: {
    type: Boolean,
    default: true
  },
  // 是否显示确定按钮
  showConfirmButton: {
    type: Boolean,
    default: true
  },
  // 取消按钮的文本内容
  cancelButtonText: {
    type: String,
    default: '取消'
  },
  // 确定按钮的文本内容
  confirmButtonText: {
    type: String,
    default: '确定'
  },
  // 取消按钮的自定义类名
  cancelButtonClass: {
    type: String,
    default: ''
  },
  // 确定按钮的自定义类名
  confirmButtonClass: {
    type: String,
    default: ''
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  },
  closeOnPressEscape: {
    type: Boolean,
    default: false
  },
  buttonSize: {
    type: String as PropType<'medium' | 'small'>,
    default: 'small'
  }
});

const emits = defineEmits(['update:visible', 'handle-sure', 'handle-cancel', 'handle-close']);

const visible = defineModel<boolean>({ default: false });

const updateVisible = (isShow: boolean) => {
  visible.value = isShow;
};

const handleCancel = (e: MouseEvent) => {
  updateVisible(false);
  emits('handle-cancel', e);
};

const handleSure = (e: MouseEvent) => {
  emits('handle-sure', e);
};

const handleClose = (e: MouseEvent) => {
  updateVisible(false);
  emits('handle-close', e);
};

const appendUnit = (data?: number | string) => {
  return `${data}px`;
};

const widthNum = computed(() => (isString(props.width) ? props.width : appendUnit(props.width)));

const heightNum = computed(() => (isString(props.height) ? props.height : appendUnit(props.height)));

// const contentHeight = computed(() => {
//   if (props.footerShow && props.headerShow) {
//     return `calc(100% - var(--sim-model-header-height) - var(--sim-model-footer-height)`;
//   }
//   if (props.footerShow) {
//     return `calc(100% - var(--sim-model-footer-height)`;
//   }
//   if (props.headerShow) {
//     return `calc(100% - var(--sim-model-header-height)`;
//   }
//   return '100%';
// });
const modelStyle = computed(() => ({
  width: widthNum.value,
  height: heightNum.value
}));

// const contentStyle = computed(() => ({ height: contentHeight.value }));
</script>

<style lang="less">
.el-overlay {
  .el-overlay-dialog {
    overflow: unset;
  }
  .el-dialog {
    --el-dialog-padding-primary: 0;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
  }
}
.sim-dialog {
  --sim-model-header-height: 48px;
  --sim-model-footer-height: 48px;
  display: flex;
  flex-direction: column;
  .el-dialog__header {
    padding: 0 10px !important;
  }
  .sim-dialog__header {
    position: relative;
    width: 100%;
    height: var(--sim-model-header-height);
    display: flex;
    align-items: center;
    overflow-x: clip;
    box-sizing: border-box;
    color: white;
    padding-left: 10px;
    background-color: var(--secondary-header-bg-color);

    .sim-dialog__title {
      font-size: 18px;
    }

    .sim-dialog__close {
      position: absolute;
      top: 50%;
      right: 0;
      width: 47px;
      height: 29px;
      transform: translateY(-50%);
      text-align: center;
      line-height: 36px;
      cursor: pointer;
      img.close {
        margin-left: 4px;
      }
    }
  }
  .el-dialog__body {
    height: 100px;
    flex: 1;
    .sim-dialog__content {
      height: 100%;
    }
  }
  .el-dialog__footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 8px;
    height: var(--sim-model-footer-height);
    background: var(--alpha-bg-color);
  }
}
</style>
