/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/30 22:02:33
 * @description Vector3 tests
 * @version 2.0
 * */

const { Cartesian3, defaultValue, defined, Ellipsoid, Matrix3, Matrix4, Quaternion, Transforms } = Cesium;
const vectorResult = new Cartesian3();
const velocityResult = new Cartesian3();
const positionScratch = new Cartesian3();

class Vector3 extends Cartesian3 {
  static calculateVector(position1, position2, normalize) {
    normalize = defaultValue(normalize, true);

    if (!defined(position1) || !defined(position2)) {
      return undefined;
    }

    if (Cartesian3.equals(position1, position2)) {
      return normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
    }

    const vector = Cartesian3.subtract(position2, position1, vectorResult);
    if (normalize) {
      return Cartesian3.normalize(vector, vector);
    }

    return vector;
  }

  static getRotation(position1, position2, ellipsoid) {
    ellipsoid = defaultValue(ellipsoid, Ellipsoid.WGS84);
    const vector = Vector3.calculateVector(position1, position2);
    if (!vector) {
      return undefined;
    }
    position1.clone(positionScratch);
    const rotation = new Matrix3();
    Transforms.rotationMatrixFromPositionVelocity(positionScratch, vector, ellipsoid, rotation);
    return rotation;
  }

  static getQuaternion(position1, position2, ellipsoid) {
    const rotation = Vector3.getRotation(position1, position2, ellipsoid);
    const result = new Quaternion();
    return Quaternion.fromRotationMatrix(rotation, result);
  }

  static getMatrix(position1, position2, ellipsoid) {
    const modelMatrix = new Matrix4();
    Matrix4.fromRotationTranslation(Vector3.getRotation(position1, position2, ellipsoid), position1, modelMatrix);
    return modelMatrix;
  }

  /**
   * create getConeMatrix for ConePrimitive
   *
   * @param {Cartesian3} position1
   * @param {Cartesian3} position2
   * @param {number} rotationY
   * @param {Ellipsoid} ellipsoid
   * @returns {Matrix4}
   */
  static getConeMatrix(position1, position2, rotationY, ellipsoid) {
    const rotation = Vector3.getRotation(position1, position2, ellipsoid);
    rotationY = defaultValue(rotationY, Math.PI / 2);
    const m = Matrix3.fromRotationY(rotationY);
    Matrix3.multiply(rotation, m, rotation);
    const modelMatrix = new Matrix4();
    Matrix4.fromRotationTranslation(rotation, position1, modelMatrix);
    return modelMatrix;
  }

  /**
   * @static
   * @param {*} position1
   * @param {*} position2
   * @param {*} ellipsoid
   * @return {*}
   */
  static getNegativeMatrix(position1, position2, ellipsoid) {
    const modelMatrix = new Matrix4();
    Matrix4.fromRotationTranslation(Vector3.getRotation(position2, position1, ellipsoid), position1, modelMatrix);
    return modelMatrix;
  }

  static getApproximateMatrix(position1, position2, ellipsoid) {
    const modelMatrix = new Matrix4();
    Matrix4.fromRotationTranslation(Vector3.getRotation(position1, position2, ellipsoid), position2, modelMatrix);
    return modelMatrix;
  }

  /**
   * @param {Cartesian3} position1
   * @param {Cartesian3} position2
   * @param {Array} sarParams [sarHorizonHalfAngle,sarVerticalHalfAngle,sarAzimuth,sarElevation] radians
   * @param {Number} scanRate
   */
  static getPushBroomScannerMatrix(position1, position2, sarParams, scanRate) {
    const modelMatrix = Vector3.getApproximateMatrix(position1, position2);
    return getScannerMatrixByModelMatrix(modelMatrix, sarParams, scanRate);
  }

  static getPBScannerMatrixByPositionOrientation(position, quaternion, sarParams, scanRate) {
    const modelMatrix = Matrix4.fromRotationTranslation(Matrix3.fromQuaternion(quaternion), position);
    return getScannerMatrixByModelMatrix(modelMatrix, sarParams, scanRate, -1);
  }
}

/**
 * @description get SCANNER MATRIX
 * @private
 */
function getScannerMatrixByModelMatrix(modelMatrix, sarParams, scanRate, rotateIndex = 1) {
  const my = Matrix3.fromRotationY((rotateIndex * Math.PI) / 2);
  Matrix4.multiplyByMatrix3(modelMatrix, my, modelMatrix);
  const mx = Matrix3.fromRotationX(sarParams[2]);
  Matrix4.multiplyByMatrix3(modelMatrix, mx, modelMatrix);
  const elevation = Math.PI / 2 - sarParams[3] + scanRate * sarParams[0] * 2;
  const myy = Matrix3.fromRotationY(elevation);
  Matrix4.multiplyByMatrix3(modelMatrix, myy, modelMatrix);
  return { modelMatrix, elevation };
}

export default Vector3;
