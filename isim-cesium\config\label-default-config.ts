/**
 * @Author: 宋计民
 * @Date: 2023-08-24 10:43
 * @Version: 1.0
 * @Content: label-default-config.ts
 */
import { Cartesian2, Color, HorizontalOrigin, LabelStyle, VerticalOrigin, NearFarScalar } from 'cesium';

export const labelDefaultConfig = {
  font: '14px sans-serif',
  pixelOffset: new Cartesian2(18, 0),
  verticalOrigin: VerticalOrigin.CENTER,
  horizontalOrigin: HorizontalOrigin.LEFT,
  showBackground: true,
  fillColor: Color.WHITE,
  style: LabelStyle.FILL_AND_OUTLINE,
  // disableDepthTestDistance: Number.POSITIVE_INFINITY,
  translucencyByDistance: new NearFarScalar(0, 1, 5000000, 0)
};
