uniform float step;
uniform float speed;
uniform vec4 color;

czm_material czm_getMaterial(czm_materialInput materialInput)
{
    czm_material material = czm_getDefaultMaterial(materialInput);
    float offset = fract(czm_frameNumber / 100.0 * speed);
    vec2 st = materialInput.st * step;
    float t = mod(st.t + offset,1.0);
    material.diffuse = vec3(color);
    if(t < 0.4){
        material.alpha = 0.0;
    }else{
        material.alpha = (1.0 - t) / 0.6;
    }

    return material;
}