<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/4
* @Version : 1.0
* @Content : Table
-->
<template>
  <div class="sim-table-wrap">
    <el-table v-bind="$attrs" ref="tableRef" class="sim-table" border :resizable="resizable">
      <template v-for="(_, name) in $slots" #[name]>
        <slot :name="name" :sort-list="sortList"></slot>
      </template>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { ElTable } from 'element-plus';
import { PropType } from 'vue';

defineOptions({
  name: 'SimTable'
});
type OrderType = 'ascending' | 'descending' | null;

interface ColumnSort {
  name: string;
  orderName: number;
  orderType: OrderType;
}

const props = defineProps({
  resizable: {
    type: Boolean,
    default: true
  },
  sortList: {
    type: Array as PropType<ColumnSort[]>,
    default: () => []
  }
});
const emits = defineEmits(['sort-change']);

// 排序字段项
const sortOrders = ['ASC', 'DESC', null];
/**
 * 处理排序字段
 * @param type
 */
const handleOrderType = (type: OrderType): OrderType => {
  let index = sortOrders.findIndex((it) => it === type);

  if (index >= sortOrders.length - 1) {
    index = 0;
  } else {
    index++;
  }
  return sortOrders[index] as OrderType;
};

const sortChange = () => {
  emits('sort-change', props.sortList);
};
provide('sortProvide', { sortList: props.sortList, sortOrders, handleOrderType, sortChange });

const tableRef = shallowRef<InstanceType<typeof ElTable>>();
defineExpose({ tableRef });
</script>

<style lang="less"></style>
