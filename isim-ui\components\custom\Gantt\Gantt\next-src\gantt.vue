<!--
* @Author: 宋计民
* @Date: 2024/4/24
* @Version: 1.0
* @Content: gantt.vue
-->
<template>
  <div class="sim-gantt2">
    <div class="sim-gantt2__left">
      <div class="sim-gantt2-left__head">
        <search-button :data="data" @change="scrollTo" />
      </div>
      <div class="sim-gantt2-left__content" ref="leftTreeRef">
        <gantt-lef-tree
          v-model:exclude-list="excludeList"
          v-model:selected-row-list="selectedRowDataList"
          :height="props.rowHeight"
          :gap="props.rowGap"
          :row-text-format="leftRowTextFormat"
          :show-list="leftTreeList"
        />
      </div>
    </div>
    <div class="sim-gantt2__right sim-gantt2-right">
      <div class="sim-gantt2-right__timeline">
        <slot name="timeline">
          <timeline
            :min-time="minTime"
            :max-time="maxTime"
            v-model:currentTime="currentTime"
            v-model:showStartTime="selectStartTime"
            v-model:showEndTime="selectEndTime"
            @change="timelineChange"
            ref="timeRef"
            @wheel.prevent="WheelEventOption.wheelEvent"
          />
        </slot>
      </div>
      <div class="sim-gantt2-right__content">
        <canvas ref="canvasRef" width="100%" height="100%" />
        <!--        <select-node-area-->
        <!--          class="select-node-area"-->
        <!--          v-for="item in selectedRowList"-->
        <!--          :key="item.id"-->
        <!--          :data="item"-->
        <!--          :show-start-time="selectStartTime"-->
        <!--          :show-end-time="selectEndTime"-->
        <!--        />-->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T extends GanttDataBase">
import Timeline from './gantt-timeline-new.vue';
import { Gantt, GanttDataBase, GanttDataType, GanttMode, rowFormatLabelType, SelectNodeAreaType } from './graphic';
import { keyDownAndClick, useGanttEvent } from './hooks/use-gantt-event.ts';
import { PropType } from 'vue';
import { useResizeObserver } from '@vueuse/core';
import GanttLefTree from './gantt-lef-tree.vue';
import { queryParentNode, TreeDataToListData } from '@/utils';
import { useWatchProps } from './hooks/use-watch-props.ts';
import { GanttRow } from './graphic/GanttRow.ts';
import SearchButton from './component/search-input/search-button.vue';

defineOptions({
  name: 'SimGanttContent'
});

const emits = defineEmits(['node-click']);

const props = defineProps({
  // 任务树形数据
  data: {
    type: Object as PropType<GanttDataType<T>[]>,
    default: () => {
      return [];
    }
  },
  // 甘特图任务节点高度
  rowHeight: {
    type: Number,
    default: 24
  },
  // 甘特图任务节点间距
  rowGap: {
    type: Number,
    default: 6
  },
  mode: {
    type: String as PropType<GanttMode>,
    default: 'default'
  },
  splitLine: {
    type: Boolean,
    default: false
  },
  // 时间轴开始时间
  minTime: {
    type: Number
  },
  // 时间轴结束时间
  maxTime: {
    type: Number
  },
  // 左侧树任务名称格式化
  leftRowTextFormat: {
    type: Function as PropType<rowFormatLabelType>,
    default: (data: GanttDataType) => data.label
  },
  // 甘特图任务名称格式化
  taskTextFormat: {
    type: Function as PropType<rowFormatLabelType>,
    default: (data: GanttDataType) => data.label
  },
  // 是否显示连线
  connectLine: {
    type: Boolean,
    default: true
  }
});

const currentTime = defineModel<number>('currentTime');
const selectStartTime = ref<number>(props.minTime!);
const selectEndTime = ref<number>(props.maxTime!);

const timeRef = ref();
const canvasRef = ref<HTMLCanvasElement>();
const leftTreeRef = ref<HTMLCanvasElement>();
const graphicMap = shallowRef<Gantt>();
const context2D = ref<CanvasRenderingContext2D>();

// 收起的子节点数据
const excludeList = ref(new Set<string>());
// 经过深度优先遍历拍平后的数据
const list = computed(() => {
  // const dataValue: GanttDataType<T>[] = JSON.parse(JSON.stringify(props.data));
  const dataValue: GanttDataType<T>[] = props.data;
  return TreeDataToListData(dataValue, [...excludeList.value]);
});
watch(
  () => list.value,
  () => {
    // console.log('list.value', list.value);
    toValue(graphicMap)!.setData(list.value);
  },
  { deep: true }
);

// 点击选中的行
const selectedRowDataList = ref<GanttDataType[]>([]);
watch(
  () => selectedRowDataList.value,
  (newValue: GanttDataType[]) => {
    const graphic = toValue(graphicMap)!;
    graphic.setSelectedtRowByIdList(newValue.map((v) => v.id));
    emits('node-click', selectedRowDataList.value);
    setSelectedRowList();
  }
);
// 点击选中的行的图形
const selectedRowList = ref<SelectNodeAreaType[]>([]);
const setSelectedRowList = () => {
  const graphic = toValue(graphicMap)!;
  selectedRowList.value = graphic.selectedRowCollection.values().map((v) => {
    const shape = graphic.graphicCollection.getById(v.id)!;
    return {
      id: v.id,
      data: shape.data,
      x: shape.x / window.devicePixelRatio,
      y: shape.y / window.devicePixelRatio
    };
  });
};

// 滚动事件
const WheelEventOption = {
  wheelEvent: (e: WheelEvent) => {
    e.preventDefault();
    const graphic = toValue(graphicMap)!;
    let step = (graphic.maxTime - graphic.minTime) / 100;
    if (e.deltaY < 0) step = -step;

    if (e.shiftKey) {
      graphic.horizonScroll(e.deltaY > 0);
      timeRef.value.setShowTime({
        showStartTime: graphic.showStartTime,
        showEndTime: graphic.showEndTime
      });
      return;
    }
    if (e.ctrlKey) {
      let showStartTime = (graphic.showStartTime += step);
      let showEndTime = (graphic.showEndTime -= step);
      if (showStartTime > showEndTime) {
        const center = graphic.showStartTime + (graphic.showEndTime - graphic.showStartTime) / 2;
        showStartTime = center;
        showEndTime = center;
      }

      graphic.zoom(showStartTime, showEndTime);
      timeRef.value.setShowTime({
        showStartTime: graphic.showStartTime,
        showEndTime: graphic.showEndTime
      });
      return;
    }
    if (e.altKey) {
      currentTime.value! += step;
      return;
    }

    graphic.verticalScroll(e.deltaY > 0);
  }
};

useGanttEvent(canvasRef as Ref<HTMLElement>, WheelEventOption);
useGanttEvent(leftTreeRef as Ref<HTMLElement>, WheelEventOption);

// 监听时间轴change事件
const timelineChange = ({ showStartTime, showEndTime, currentTime }: { showStartTime: number; showEndTime: number; currentTime: number }) => {
  const graphic = toValue(graphicMap);
  if (!graphic) return;
  graphic.setCurrentTime(currentTime);
  graphic.zoom(showStartTime, showEndTime);
};

// 当前展示的数据
const leftTreeList = ref<GanttDataType[]>([]);
const onScroll = (_scrollTop: number, _percentY: number) => {
  const graphic = toValue(graphicMap);
  leftTreeList.value = graphic!.graphicCollection
    .values()
    .filter((v) => v.isShow)
    .map((v) => {
      return {
        ...v.data,
        top: v.y / window.devicePixelRatio
      };
    });
  setSelectedRowList();
};
// 跳转到对应节点的位置
const scrollTo = (data: GanttDataType) => {
  const graphic = toValue(graphicMap)!;
  const parentList = queryParentNode(props.data, data.id).map((v) => v.id);
  // 如果需要跳转的节点的上级节点有被收起的，则将其展开
  parentList.forEach((id) => {
    if (excludeList.value.has(id)) excludeList.value.delete(id);
  });

  setTimeout(() => {
    const shape = graphic.graphicCollection.getById(data.id)!;
    if (!shape) return;
    graphic.scrollBar.scrollToByRowIndex(shape.row.index);
    graphic.render();
  });
};

// 点击节点事件
const handleClick = (e: MouseEvent) => {
  const graphic = toValue(graphicMap)!;
  // 连线在节点后渲染，所以先判断连线
  // 选中滚动条

  // 选中连线
  const lineList = graphic.pickEdge(e.offsetX, e.offsetY);
  if (lineList.length) {
    const selectedLine = lineList[lineList.length - 1];
    const { source, target } = selectedLine;
    graphic.setSelectedConnectLine(selectedLine);
    selectedRowDataList.value = [source.data, target.data];
    return;
  }
  graphic.clearSelectedConnectLine();

  // 选中节点
  const rowList = graphic.pickNode(e.offsetX, e.offsetY);
  // console.log('rowList', rowList[0]);
  selectedRowDataList.value = rowList.map((row) => row.data);
};
// 连线点击事件
const startOnLine = (e: MouseEvent) => {
  const graphic = toValue(graphicMap)!;
  const rowList = graphic.pickNode(e.offsetX, e.offsetY);
  const row = rowList[0];
  if (!row) return;
  // if (graphic.online.isOnlineById(row.id)) return;
  graphic.online.push(row);
};
// 连线事件
keyDownAndClick(canvasRef, {
  clickEvent: handleClick,
  keyDownAndClick: {
    keys: ['A', 'a'],
    click: startOnLine
  }
});
const onCreateEdge = (rowList: GanttRow[]) => {
  const graphic = toValue(graphicMap)!;
  const startRow = rowList[0];
  const endRow = rowList[1];
  const startData = list.value.find((v) => v.id === startRow.id);
  if (!startData || startData?.target?.includes(endRow.id)) return;
  if (!startData.target) startData.target = [];
  startData.target!.push(endRow.id);
  graphic.setData(list.value);
};

// 监听窗口变化
useResizeObserver(canvasRef, () => {
  const canvasDom = toValue(canvasRef)!;
  const graphic = toValue(graphicMap)!;
  canvasDom.width = canvasDom.offsetWidth * window.devicePixelRatio;
  canvasDom.height = canvasDom.offsetHeight * window.devicePixelRatio;

  // 解决拉伸高度造成底部渲染失效问题
  // 先计算滚动页面高度，再计算滚动条高度
  // graphic.scrollBar.calcStartY();
  // graphic.scrollBar.checkStartY();
  graphic.scrollBar.resize();
  graphic.render();
});

useWatchProps(graphicMap, props);

onMounted(() => {
  const canvasDom = toValue(canvasRef)!;
  const ganttBoxDom = canvasDom.parentNode as HTMLDivElement;
  canvasDom.width = ganttBoxDom.clientWidth;
  canvasDom.height = ganttBoxDom.clientHeight;
  const ctx = canvasRef.value!.getContext('2d')!;

  const graphic = new Gantt({
    container: canvasDom,
    minTime: props.minTime ?? 0,
    maxTime: props.maxTime ?? 0,
    currentTime: props.minTime ?? 0,
    // width: 600,
    // height: 500,
    startX: 0,
    mode: 'default',
    rowHeight: 24,
    rowGap: 6,
    rowFormatLabel: props.taskTextFormat,
    fontSize: 16,
    connectLine: {
      show: props.connectLine,
      type: 'line'
    }
  });
  graphic.setData(list.value);
  // 注册滚动回调事件
  graphic.scrollBar.onScroll(onScroll);
  // 注册连线事件
  graphic.online.onCreateEdge(onCreateEdge);
  graphicMap.value = graphic;
  context2D.value = ctx;
});
</script>

<style scoped lang="less">
.sim-gantt2 {
  display: flex;
  width: 100%;
  height: 100%;
  @border: 1px solid var(--border-color);
  border: @border;
  --row-height-val: v-bind('props.rowHeight');
  --row-gap-val: v-bind('props.rowGap');
  --row-height: calc(var(--row-height-val) * 1px);
  --row-gap: calc(var(--row-gap-val) * 1px);
  --gantt2-left-width: 200px;
  --gantt-header-height: 40px;

  //background-color: skyblue;
  background-color: #263448;
  &__left {
    width: var(--gantt2-left-width);
    height: 100%;
    border-right: @border;
  }
  &-left {
    &__head {
      height: var(--gantt-header-height);
      border-bottom: @border;
    }
    &__content {
      height: calc(100% - var(--gantt-header-height));
    }
  }
  &__right {
    width: calc(100% - var(--gantt2-left-width));
    height: 100%;
  }
  &-right {
    &__timeline {
      height: var(--gantt-header-height);
      border-bottom: @border;
    }
    &__content {
      position: relative;
      height: 100%;
      width: 100%;
      height: calc(100% - var(--gantt-header-height));
      & > canvas {
        width: 100%;
        //height: calc(100% - var(--gantt-header-height));
        height: 100%;
      }
      .select-node-area {
        position: absolute;
        left: 0;
        top: 0;
        border: 1px solid red;
        width: 100px;
        height: var(--row-height);
      }
    }
  }
  .canvas--line {
    background-image: repeating-linear-gradient(
      180deg,
      transparent 0px,
      transparent calc(var(--row-height) + calc(var(--row-gap) / 2)),
      black calc(var(--row-height) + calc(var(--row-gap) / 2) + 2px),
      black calc(var(--row-height) + calc(var(--row-gap) / 2) + 2px)
    );
  }
}
</style>
