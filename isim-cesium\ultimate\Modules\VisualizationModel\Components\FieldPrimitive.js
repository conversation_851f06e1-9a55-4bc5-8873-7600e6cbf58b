/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/08/09 19:07:52
 * @description FieldPrimitive
 * @version 1.0
 * */
import PolygonVolumePrimitive from '../../../Scene/Primitives/PolygonVolumePrimitive.js';
import AbstractPrimitive from '../AbstractVisualizer.js';
import MinePrimitive from './MinePrimitive.js';
import { Cartesian3, Color, defaultValue, HeightReference, PrimitiveCollection } from 'cesium';

const MINE_OPTIONS = {
  heightReference: HeightReference.CLAMP_TO_GROUND
};

export default class FieldPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    /**
     * @type {Array} degreesArray [[lon,lat],[lon,lat]]
     * @description 需要注意 gapSpace 的单位问题
     */
    this._positions = options.positions;
    this._gapSpace = defaultValue(options.gapSpace, 1);
    this._units = defaultValue(options.units, 'kilometers');
    this._mineOptions = {
      ...MINE_OPTIONS,
      ...options.mineOptions
    };
    this._fillOptions = {
      clampToGround: true,
      depthFailShow: false,
      color: Color.YELLOW.withAlpha(0.2),
      ...options.fillOptions
    };
    this._getDistribution();
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._update3D();
    }

    this._primitive3D?.update(frameState);
    this._primitive?.update(frameState);
  }

  _update3D() {
    const positions = this._positions.reduce((pre, cur) => {
      pre.push(Cartesian3.fromDegrees(...cur));
      return pre;
    }, []);

    this._primitive3D = new PolygonVolumePrimitive({ ...this._fillOptions, id: this._id, positions });
  }

  _getDistribution() {
    const polygon = turf.polygon([[...this._positions, this._positions[0]]]);
    const extend = turf.bbox(polygon);
    const grid = turf.pointGrid(extend, this._gapSpace, { units: this._units });
    this._primitive = new PrimitiveCollection();
    let cart;
    grid.features.forEach((item) => {
      if (turf.booleanPointInPolygon(item, polygon)) {
        cart = Cartesian3.fromDegrees(...item.geometry.coordinates);
        this._primitive.add(
          new MinePrimitive({
            ...this._mineOptions,
            position: cart
          })
        );
      }
    });
  }
}
