/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/08/13 19:16:52
 * @description HandlerEvent Base Settings
 * */

import { ScreenSpaceEventType, Viewer } from 'cesium';
import AbstractHandler from './AbstractHandler';

const eventCallback = (event: any) => undefined;

export default class HandlerEvent extends AbstractHandler {
  constructor(viewer: Viewer) {
    super({ viewer });
  }

  leftDown(callback = eventCallback) {
    this.handler?.setInputAction((event) => {
      callback(event);
    }, ScreenSpaceEventType.LEFT_DOWN);
  }

  leftUp(callback = eventCallback) {
    this.handler?.setInputAction((event) => {
      callback(event);
    }, ScreenSpaceEventType.LEFT_UP);
  }

  leftClick(callback = eventCallback) {
    this.handler?.setInputAction((event) => {
      callback(event);
    }, ScreenSpaceEventType.LEFT_CLICK);
  }

  rightClick(callback = eventCallback) {
    this.handler?.setInputAction((event) => {
      callback(event);
    }, ScreenSpaceEventType.RIGHT_CLICK);
  }

  mouseMove(callback = eventCallback) {
    this.handler?.setInputAction((event) => {
      callback(event);
    }, ScreenSpaceEventType.MOUSE_MOVE);
  }

  doubleClick(callback = eventCallback) {
    this.handler?.setInputAction((event) => {
      callback(event);
    }, ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
  }
}
