/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { isNumber, isObject, isString } from '@/utils';

export function dataToSchema(data: any, result: Record<string, any> = {}) {
  Object.keys(data).forEach((item) => {
    objectToSchema(item, data[item], result);
  });
  return result;
}

export function objectToSchema(title: string, data: Record<string, any>, result: any) {
  if (isString(data)) {
    result[title] = {
      type: 'string',
      title
    };
    return result;
  }
  if (isNumber(data)) {
    result[title] = {
      type: 'number',
      title
    };
    return result;
  }
  if (isObject(data)) {
    result[title] = {
      type: 'object',
      title,
      properties: {}
    };
    Object.keys(data).forEach((item) => {
      objectToSchema(item, data[item], result[title].properties);
    });
    return result;
  }
}
