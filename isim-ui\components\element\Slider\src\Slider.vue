<!--
* <AUTHOR> 杨光伟
* @Date :  2023/4/11
* @Version : 1.0
* @Content : Slider
-->
<template>
  <div class="sim-slider">
    <div v-if="disabledMin > ($attrs.min ?? 0)" class="disabled-min" :title="`最小值：${disabledMin}`"></div>
    <div v-if="disabledMax < ($attrs.max ?? 100)" class="disabled-max" :title="`最大值${disabledMax}`"></div>
    <div class="slider-controller">
      <i class="slider-line" />
      <i class="slider-line" />
      <i class="slider-line" />
    </div>
    <el-slider ref="sliderRef" v-bind="$attrs" @input="changeValue"></el-slider>
  </div>
</template>

<script lang="ts">
export default {
  name: 'SimSlider'
};
</script>
<script lang="ts" setup>
import { ref } from 'vue';
import { ElSlider } from 'element-plus';
const slider = ref<InstanceType<typeof ElSlider>>();
const props = defineProps({
  // 滑动的最小阈值
  disabledMin: {
    type: Number,
    default: 0
  },
  // 滑动的最大阈值
  disabledMax: {
    type: Number,
    default: 100
  }
});
const emits = defineEmits(['update:modelValue']);
const changeValue = (value: number[] | number) => {
  console.log(value, props.disabledMin, props.disabledMax);
  if (value < props.disabledMin) {
    value = props.disabledMin;
  } else if (value > props.disabledMax) {
    value = props.disabledMax;
  }
  emits('update:modelValue', value);
};
defineExpose(slider.value);
</script>
<style lang="less" scoped>
.sim-slider {
  width: 100%;
  height: 32px;
  flex: 1;
  position: relative;
  .el-slider {
    :deep(.el-slider__runway) {
      border: 1px solid rgba(var(--primary-color-val), 0.5);
      padding: 1px 5px;
      background: rgba(var(--primary-color-val), 0.15);
      border-radius: 100px;
      z-index: 100;
      .el-slider__bar {
        left: 1px !important;
      }
      .el-slider__button-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        .el-slider__button {
          width: 24px;
          height: 16px;
          background: radial-gradient(rgba(var(--primary-color-val), 0.2980392156862745) 0%, rgba(var(--primary-color-val), 0) 169%),
            linear-gradient(180deg, var(--primary-color-dep) 0%, var(--primary-color-dep) 73%, var(--primary-color-dep) 100%);
          border: 1px solid var(--primary-color);
          box-shadow: inset 0 0 4px var(--primary-color);
          border-radius: 4px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          &:after,
          &:before {
            display: inline-block;
            width: 2px;
            height: 6px;
            content: '';
            background: var(--primary-color);
            border-radius: 1px;
            margin-left: 3px;
          }
          &:before {
            margin-left: 5px;
          }
        }
        &:after {
          display: inline-block;
          width: 2px;
          height: 6px;
          content: '';
          background: var(--primary-color);
          border-radius: 1px;
          position: absolute;
          left: 12px;
          transition: var(--el-transition-duration-fast);
        }
        &.hover,
        &.dragging {
          &:after {
            left: 11px;
            transform: scale(1.2);
          }
        }
      }
      &.is-disabled {
        .el-slider__button-wrapper.hover {
          &:after {
            left: 12px;
            transform: none;
          }
        }
      }
    }
  }
  .slider-controller {
    height: 100%;
    position: absolute;
    cursor: pointer;
    //top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    z-index: 100;
  }
  .disabled-min,
  .disabled-max {
    width: 2px;
    height: 14px;
    background: rgb(255, 128, 0);
    position: absolute;
    top: 50%;
    left: calc(100% * (v-bind('props.disabledMin') / v-bind('$attrs.max')));
    z-index: 100;
    transform: translateY(-50%);
    cursor: pointer;
  }
  .disabled-max {
    left: calc(100% * (v-bind('props.disabledMax') / v-bind('$attrs.max')));
  }
}
</style>
