<!--
* <AUTHOR> 宋计民
* @Date :  2023/5/5
* @Version : 1.0
* @Content : Card
-->
<template>
  <div class="sim-card">
    <slot name="header">
      <div v-if="title" class="sim-card__header">
        <n-ellipsis>{{ title }}</n-ellipsis>
      </div>
    </slot>
    <div class="sim-card__content">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'SimCard'
});
defineProps({
  title: {
    type: String,
    default: ''
  }
});
</script>
<style scoped lang="less">
.sim-card {
  background: var(--primary-bg-color);
  //border: 1px solid var(--border-color);
  box-sizing: border-box;
  min-height: 80px;
  --sim-card-header-height: 28px;
  color: #000;
  &__header {
    height: var(--sim-card-header-height);
    line-height: var(--sim-card-header-height);
    padding: 0 8px;
    box-sizing: border-box;
    background-color: var(--secondary-header-bg-color);
    background-size: 100% 100%;
    color: var(--secondary-text-color);
  }
  &__content {
    height: calc(100% - var(--sim-card-header-height));
    overflow-y: auto;
  }
}
</style>
