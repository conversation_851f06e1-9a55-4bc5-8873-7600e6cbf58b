<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120" height="120" viewBox="0 0 120 120">
  <defs>
    <linearGradient id="linear-gradient" x1="1" y1="0.65" x2="0.023" y2="0.411" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#cdebf8"/>
      <stop offset="1" stop-color="#93b9fa"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e5e2e2"/>
      <stop offset="1" stop-color="#c5c5c5"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ab6724"/>
      <stop offset="1" stop-color="#ffd790"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.959" y1="0.529" x2="0" y2="0.516" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0091d2"/>
      <stop offset="1" stop-color="#5175cc"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0.945" y1="0.439" x2="0.046" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b3cef7"/>
      <stop offset="1" stop-color="#4077b7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="-0.003" y1="0.508" x2="1.001" y2="0.508" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.502"/>
      <stop offset="0.042" stop-color="#5197ff" stop-opacity="0.502"/>
      <stop offset="0.482" stop-color="#236dff" stop-opacity="0.561"/>
      <stop offset="0.516" stop-color="#004cff" stop-opacity="0.639"/>
      <stop offset="1" stop-color="#209eff" stop-opacity="0.561"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b4edff"/>
      <stop offset="0.77" stop-color="#95d3ff" stop-opacity="0.302"/>
      <stop offset="0.99" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="0.378" y1="0.195" x2="0.685" y2="0.697" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#09f" stop-opacity="0.58"/>
      <stop offset="0.134" stop-color="#5ec6ff" stop-opacity="0.58"/>
      <stop offset="1" stop-color="#3177ff" stop-opacity="0.8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="-0.003" y1="0.508" x2="1.001" y2="0.508" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.502"/>
      <stop offset="0.042" stop-color="#1200ff" stop-opacity="0.502"/>
      <stop offset="0.692" stop-color="#004cff" stop-opacity="0.451"/>
      <stop offset="1" stop-color="#209eff" stop-opacity="0.6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#617bff" stop-opacity="0.78"/>
      <stop offset="0.77" stop-color="#95d3ff" stop-opacity="0.62"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="0.945" y1="0.439" x2="0.046" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#bbd6ff"/>
      <stop offset="1" stop-color="#4077b7"/>
    </linearGradient>
    <clipPath id="clip-性能编辑">
      <rect width="120" height="120"/>
    </clipPath>
  </defs>
  <g id="性能编辑" clip-path="url(#clip-性能编辑)">
    <g id="组_62" data-name="组 62" transform="translate(4.749 6.813)">
      <path id="路径_175" data-name="路径 175" d="M51.37,40.55a22.59,22.59,0,0,0-11.48-4.34c-.63-.06-1.25-.08-1.87-.09h-.61q-.75,0-1.5.06h-.34a10.749,10.749,0,0,0-1.6.19h-.22c-.44.07-.88.15-1.31.25l-.37.09-.37.09c-.55.15-1.09.3-1.62.48l-.13.05c-.5.17-1,.37-1.47.58l-.31.13-.23.1c-.34.16-.67.33-1,.51l-.36.18c-.5.28-1,.58-1.45.89l-.35.26c-.34.24-.68.49-1,.75-.16.12-.3.26-.46.39s-.36.3-.53.46-.21.21-.32.32l-.24.23c-.35.37-.69.74-1,1.13l-.15.19c-.29.37-.56.74-.82,1.13l-.14.2c-.26.42-.51.85-.74,1.29-.05.1-.09.2-.14.29a14.37,14.37,0,0,0-.6,1.4h0c-.11.31-.22.63-.31.94a2.424,2.424,0,0,1-.1.45c-.03.15-.07.26-.1.4a15.27,15.27,0,0,0-.27,1.53,3.25,3.25,0,0,1-.07.82l-.17,3.27a5.453,5.453,0,0,1,.06-.81,15.271,15.271,0,0,1,.27-1.53,1.534,1.534,0,0,1,.1-.41,12.891,12.891,0,0,1,.41-1.38s0,0,0-.06a14.2,14.2,0,0,1,.6-1.39c.05-.1.09-.2.14-.29.23-.44.48-.88.75-1.3l.13-.2c.26-.38.53-.76.82-1.12l.15-.19c.32-.39.66-.77,1-1.13l.24-.24c.27-.26.56-.52.85-.77l.46-.4c.32-.26.66-.5,1-.75l.35-.25c.47-.31.95-.61,1.45-.89l.36-.18c.4-.22.81-.43,1.23-.62l.31-.13q.72-.31,1.47-.57h.13q.8-.27,1.62-.48l.37-.09c.55-.14,1.11-.25,1.68-.35h.22c.53-.08,1.06-.14,1.6-.19h.34a17.416,17.416,0,0,1,1.77,0h.34a11.788,11.788,0,0,1,1.87.1,22.67,22.67,0,0,1,11.48,4.33c4.47,3.37,7.16,8.32,6.87,13.63l.18-3.28C58.53,48.86,55.84,43.92,51.37,40.55Z" fill="#90a8b2" stroke="#fff" stroke-width="0.5"/>
      <path id="联合_2" data-name="联合 2" d="M86.049,332.88h-.58l-6.54-.619a2.05,2.05,0,0,1-1-.381,1.409,1.409,0,0,1-.6-1.2L77.5,327.6a1.337,1.337,0,0,0,.011.147,1.49,1.49,0,0,0,.58.85,2,2,0,0,0,1,.39l6.54.61a1.324,1.324,0,0,0,.28,0,1.518,1.518,0,0,0,.3,0h.09a2.3,2.3,0,0,0,.37-.12,1.713,1.713,0,0,0,.38-.26h.06a2.048,2.048,0,0,0,.221-.27.914.914,0,0,0,.13-.26,2.551,2.551,0,0,0,.05-.26l.59-5.2c.69-.03,1.34-.09,2-.16h.52c.55-.069,1.1-.139,1.64-.229h.34c.62-.11,1.22-.24,1.83-.38.79-.18,1.58-.381,2.361-.62l3.19,4.59a2.006,2.006,0,0,0,.35.37,2.392,2.392,0,0,0,.53.27h.19a2.732,2.732,0,0,0,.41.06h.47a2.078,2.078,0,0,0,.48-.15l5.66-2.72a1.86,1.86,0,0,0,.4-.26.918.918,0,0,1,.131-.14l.12-.149a1.209,1.209,0,0,0,.08-.16c-.008-.037-.018-.074-.03-.11a1.621,1.621,0,0,0,.059-.32l-.18,3.28a.918.918,0,0,1,0,.281,1.192,1.192,0,0,1-.079.159,1.465,1.465,0,0,1-.08.17l-.121.14-.1.12a1.846,1.846,0,0,1-.4.27l-5.6,2.74a2.175,2.175,0,0,1-.4.139h-.69a1.977,1.977,0,0,1-.41-.07h-.19a1.585,1.585,0,0,1-.52-.27,1.394,1.394,0,0,1-.36-.37l-3.19-4.559c-.78.239-1.571.45-2.361.63h-.19c-.54.12-1.09.23-1.64.32h-.34c-.54.09-1.09.17-1.63.23l-.53.059a19.792,19.792,0,0,1-2,.15l-.59,5.21a.816.816,0,0,1,0,.22,1.052,1.052,0,0,1-.12.27,1.788,1.788,0,0,1-.22.26,1.7,1.7,0,0,1-.381.26,2.379,2.379,0,0,1-.36.12Zm-8.54-5.469-.011.192A1.338,1.338,0,0,1,77.509,327.411ZM64.4,326.9h-.121a1.914,1.914,0,0,1-.41-.06,1.643,1.643,0,0,1-.23.031,1.943,1.943,0,0,1-.589-.3l-4.94-3.72a1.432,1.432,0,0,1-.61-1.2l.18-3.28a1.364,1.364,0,0,0,.33.94,1.4,1.4,0,0,0,.28.26l4.93,3.72a1.787,1.787,0,0,0,.6.3h.221a3.268,3.268,0,0,0,.44,0h.09a1.772,1.772,0,0,0,.519-.11H65.2a2,2,0,0,0,.53-.33l4.14-3.88c.689.37,1.339.7,2,1a33.862,33.862,0,0,0,6.17,2.111l-.17,3.28a34.228,34.228,0,0,1-8.169-3.02l-4.14,3.89a2.239,2.239,0,0,1-.45.3h-.19a1.257,1.257,0,0,1-.211.07Zm41.13-9.37.229-.17c.471-.3.95-.64,1.41-1l.49-.42c.36-.3.72-.59,1.07-.91a27.735,27.735,0,0,0,2.65-2.94l5.77,2.171a1.654,1.654,0,0,0,.36.1h.86c.085-.026.168-.056.25-.09h.1a1.992,1.992,0,0,0,.391-.26l.069-.06a1.417,1.417,0,0,0,.26-.35l2.61-5q.046-.105.082-.214l-.162,2.943a1.231,1.231,0,0,1,0,.27,2.332,2.332,0,0,1-.1.24l-2.679,5.09a1.624,1.624,0,0,1-.27.35h0a1.753,1.753,0,0,1-.39.26h-.08l-.249.08h-.9a2.483,2.483,0,0,1-.36-.1l-5.771-2.18a25.961,25.961,0,0,1-2.65,2.939c-.31.3-.66.581-1,.86-.18.149-.35.31-.53.45-.53.419-1.08.82-1.64,1.21Zm-44.771-2.22a24.21,24.21,0,0,1-3.24-4.919l-6.12,1H51.06a1.8,1.8,0,0,1-.571-.09h-.12a1.839,1.839,0,0,1-.5-.26,1.509,1.509,0,0,1-.56-.8l-1.44-5.4a1.27,1.27,0,0,1,0-.36l.206-3.062a1.35,1.35,0,0,0,.015.182l1.44,5.37a1.545,1.545,0,0,0,.56.8,2.109,2.109,0,0,0,.5.26h.121a2.069,2.069,0,0,0,.57.08,2.3,2.3,0,0,0,.37,0l6.13-1a25.287,25.287,0,0,0,4.52,6.46l-.18,3.27C61.639,316.31,61.189,315.831,60.759,315.311Zm61.39-7.25a1.231,1.231,0,0,1,0,.27l-.018.056Zm-6.819-.52.011-.045-.011.206Zm.18-3.11c.069-.26.159-.59.2-.76s.08-.27.13-.51.111-.56.14-.72.04-.22.09-.48c.017-.088.036-.189.054-.29l-.063,1.16c-.02.4-.1.81-.1,1.21a1.194,1.194,0,0,1,0,.39c-.04.27-.1.651-.121.81s-.049.23-.09.471-.1.55-.139.72a2.343,2.343,0,0,0-.131.509l-.138.555Zm-60.161-4.9.19-3.23a23.907,23.907,0,0,0,0,2.54l-.19,3.22A23.721,23.721,0,0,1,55.349,299.531Zm60.84.931v.719a3.827,3.827,0,0,1-.066.491Zm-68.1.739-.015.218A1.363,1.363,0,0,1,48.09,301.2Zm68.1-.91h-.13l.18-3.28,6.1-1a1.735,1.735,0,0,0,.529-.17,1.689,1.689,0,0,0,.23-.15,1.213,1.213,0,0,0,.22-.149l.06-.07a1.976,1.976,0,0,0,.21-.3.532.532,0,0,0,0-.12,1.117,1.117,0,0,0,.14-.35l-.17,3.281a2.741,2.741,0,0,1-.049.27.744.744,0,0,1-.06.12.5.5,0,0,1-.05.13,1.907,1.907,0,0,1-.21.291l-.06.069a1,1,0,0,1-.2.14,1.263,1.263,0,0,1-.22.15,1.659,1.659,0,0,1-.391.13h-.12l-6,1-.01.181ZM50.45,292.5a1.824,1.824,0,0,1-.481-.25,1.452,1.452,0,0,1-.6-1.2l.18-3.271a1.43,1.43,0,0,0,.6,1.191,2.174,2.174,0,0,0,.481.259l5.74,2.171-.18,3.27Zm58.99-10.22,4.14-3.88a1.387,1.387,0,0,0,.29-.38.532.532,0,0,0,0-.12,1.189,1.189,0,0,0,.111-.41l-.18,3.28a1.354,1.354,0,0,1,0,.27.537.537,0,0,1-.06.13.581.581,0,0,1-.05.12,1.327,1.327,0,0,1-.29.391l-4.14,3.88ZM62.87,276.9a1.336,1.336,0,0,1-.24-.84l.17-3.27a1.368,1.368,0,0,0,.25.83l3.2,4.58-.18,3.28Zm30.83-3.43.58-5.22-.18,3.271-.58,5.23Z" transform="translate(-47.869 -244.841)" fill="url(#linear-gradient-2)"/>
      <ellipse id="椭圆_1" data-name="椭圆 1" cx="20.5" cy="15.5" rx="20.5" ry="15.5" transform="translate(17.251 38.187)" fill="url(#linear-gradient-3)"/>
      <path id="路径_186" data-name="路径 186" d="M60.82,77.39a1.35,1.35,0,0,1-.62,2l-5.66,2.72a1.89,1.89,0,0,1-2.43-.56L48.92,77a35.08,35.08,0,0,1-8.66,1.49l-.59,5.2A1.68,1.68,0,0,1,37.74,85l-6.54-.61a2,2,0,0,1-1-.39,1.41,1.41,0,0,1-.6-1.26l.58-5.23A33.85,33.85,0,0,1,22,74.46l-4.14,3.88a2,2,0,0,1-2.51.09l-4.93-3.72a1.32,1.32,0,0,1-.17-2.1l4.15-3.88a25.28,25.28,0,0,1-4.52-6.46l-6.13,1A2,2,0,0,1,2.22,63a1.55,1.55,0,0,1-.56-.8L.22,56.76A1.48,1.48,0,0,1,1.57,55l6.1-1a21.32,21.32,0,0,1,.83-7.39L2.76,44.39a2.19,2.19,0,0,1-.48-.26,1.34,1.34,0,0,1-.45-1.72l2.6-5a1.9,1.9,0,0,1,2.34-.74l5.76,2.18a25.14,25.14,0,0,1,2.66-2.94,28.651,28.651,0,0,1,3.19-2.54l-3.2-4.58a1.35,1.35,0,0,1,.62-2L21.46,24a2,2,0,0,1,2.08.2,1.54,1.54,0,0,1,.36.36l3.18,4.56a35.06,35.06,0,0,1,8.66-1.48l.59-5.22a1.69,1.69,0,0,1,1.93-1.31l6.54.62a2.05,2.05,0,0,1,1,.38,1.4,1.4,0,0,1,.59,1.27l-.58,5.22A34.38,34.38,0,0,1,54,31.71l4.14-3.88a2,2,0,0,1,2.51-.09l4.94,3.72a1.32,1.32,0,0,1,.15,2.1L61.6,37.44a25.2,25.2,0,0,1,4.53,6.47l6.12-1.06a2.07,2.07,0,0,1,1.56.36,1.57,1.57,0,0,1,.56.81l1.44,5.39a1.47,1.47,0,0,1-1.35,1.76l-6.1,1a21.61,21.61,0,0,1-.82,7.4l5.74,2.16a1.91,1.91,0,0,1,.47.26,1.35,1.35,0,0,1,.46,1.72l-2.61,5a1.88,1.88,0,0,1-2.33.73L63.5,67.27a27.81,27.81,0,0,1-2.65,2.94,29,29,0,0,1-3.2,2.54ZM36.12,70c11.13,1.05,21-5.65,22.05-15,.63-5.62-2.1-10.91-6.8-14.45a22.59,22.59,0,0,0-11.48-4.34c-11.14-1.05-21,5.65-22.06,15-.62,5.62,2.1,10.9,6.8,14.45A22.67,22.67,0,0,0,36.12,70" fill="url(#linear-gradient-4)"/>
    </g>
    <g id="组_63" data-name="组 63" transform="translate(3.143 9.188)">
      <path id="路径_202" data-name="路径 202" d="M94.17,73a14.37,14.37,0,0,0-7.3-2.76,14.18,14.18,0,0,0-1.45-.06h-.43a6.4,6.4,0,0,0-1.07.06l-.49.06a4.982,4.982,0,0,0-1,.17h-.45c-.4.1-.79.2-1.17.33h-.17a12.93,12.93,0,0,0-1.27.51h-.1c-.22.1-.43.21-.64.32l-.22.12c-.32.17-.63.36-.93.56l-.22.17c-.22.15-.43.31-.63.47l-.3.25c-.11.1-.23.19-.33.29a1.419,1.419,0,0,0-.12.13,8.789,8.789,0,0,0-.66.7,2.091,2.091,0,0,0-.2.22,8.922,8.922,0,0,0-.73,1c-.06.08-.1.17-.15.25-.16.27-.31.55-.45.83l-.17.39c-.1.26-.2.52-.28.79a2.183,2.183,0,0,0-.09.23.435.435,0,0,1-.07.29l-.06.26a9.454,9.454,0,0,0-.17,1,4.911,4.911,0,0,0,0,.53l-.18,3.28v-.53a8.323,8.323,0,0,1,.17-1l.06-.26a1.788,1.788,0,0,1,.16-.53c.06-.18.18-.53.28-.78l.17-.39c.14-.29.29-.56.45-.83a2.2,2.2,0,0,1,.15-.26,8.81,8.81,0,0,1,.73-1c.06-.08.13-.15.2-.23.21-.24.43-.47.66-.7a4.421,4.421,0,0,1,.46-.41,3.472,3.472,0,0,1,.29-.25c.2-.17.42-.33.63-.48l.22-.16q.45-.3.93-.57l.22-.11.74-.37a12.892,12.892,0,0,1,1.27-.5h.16a12.149,12.149,0,0,1,1.18-.33l.45-.1c.33-.06.65-.12,1-.16l.5-.06a6.343,6.343,0,0,0,1.06-.06h.43A15,15,0,0,1,94,76.24a10.22,10.22,0,0,1,4.37,8.67l.18-3.28A10.23,10.23,0,0,0,94.17,73Z" fill="#558194"/>
      <path id="联合_3" data-name="联合 3" d="M71.932,310.681H71.8l-4.16-.4a1.2,1.2,0,0,1-.64-.239.87.87,0,0,1-.38-.76l.178-3.242a21.665,21.665,0,0,1-5.038-1.968l-2.63,2.47a1.132,1.132,0,0,1-.291.19h-.53a1.133,1.133,0,0,1-.259,0h-.14a1.5,1.5,0,0,1-.38-.19l-3.14-2.371a.869.869,0,0,1-.4-.8l.18-3.23a.879.879,0,0,0,.381.76l3.14,2.36a.994.994,0,0,0,.38.19h.139a1.324,1.324,0,0,0,.28,0h.05a1.257,1.257,0,0,0,.34-.069h.07a1.184,1.184,0,0,0,.339-.211l2.631-2.52a22.2,22.2,0,0,0,5.16,2l-.15,3.42-.13-.032a.9.9,0,0,0,.38.732,1.3,1.3,0,0,0,.64.24l4.16.39a1.192,1.192,0,0,0,.43,0,1.114,1.114,0,0,0,.27-.13.571.571,0,0,0,.13-.08.83.83,0,0,0,.21-.21.8.8,0,0,0,.13-.35l.37-3.31c.58-.03,1.17-.09,1.75-.16l.48-.07c.47-.069.931-.149,1.39-.249l.39-.08q.76-.17,1.5-.391l2,2.9a1.235,1.235,0,0,0,.22.23,1.354,1.354,0,0,0,.34.17h.68a1.112,1.112,0,0,0,.31-.1l3.59-1.72a.925.925,0,0,0,.25-.17l.08-.09.08-.09.013-.025-.154,2.965v.29l-.06.1a.373.373,0,0,1-.08.1l-.059.07a1.238,1.238,0,0,1-.25.17l-3.66,1.73-.249.08h-.33a1.229,1.229,0,0,1-.27,0h-.12a1.307,1.307,0,0,1-.33-.18.851.851,0,0,1-.23-.23l-2-2.9c-.5.15-.99.28-1.5.4h-.11l-.28.049c-.46.09-.92.18-1.38.24l-.491.07a12.913,12.913,0,0,1-1.75.159l-.37,3.31a.732.732,0,0,1,0,.14c-.03.075-.063.148-.1.22a1.016,1.016,0,0,1-.131.16l-.08.06-.13.079-.08.06-.19.06a1.335,1.335,0,0,1-.14.007A1.316,1.316,0,0,1,71.932,310.681Zm14.68-7.52a.441.441,0,0,1,0,.2s.04,0,.03.031l-.046.085Zm-2.08-3.51c.09-.049.16-.11.25-.17.259-.19.529-.38.8-.59l.311-.27c.229-.18.46-.37.679-.571a16.394,16.394,0,0,0,1.68-1.879l3.66,1.38a1.342,1.342,0,0,0,.82,0h.08a1.083,1.083,0,0,0,.239-.12l.111-.08a.748.748,0,0,0,.229-.27l1.651-3.07a1.007,1.007,0,0,0,.09-.33l-.18,3.27a.6.6,0,0,1,0,.18.475.475,0,0,1-.06.15l-1.66,3.2a.814.814,0,0,1-.18.24H93l-.11.08a1.492,1.492,0,0,1-.131.08h-.25a1.32,1.32,0,0,1-.28,0,1.26,1.26,0,0,1-.46-.09l-3.67-1.38a11.149,11.149,0,0,1-.789,1q-.423.477-.89.91c-.211.2-.44.38-.671.57l-.31.27c-.34.26-.69.52-1.05.76ZM54,296.171l-3.881.67h-.21a1.413,1.413,0,0,1-.37,0h-.08a1.328,1.328,0,0,1-.319-.16.993.993,0,0,1-.35-.509l-.921-3.451a.6.6,0,0,1,0-.26l.17-3.1a.792.792,0,0,0,.01.087l.921,3.42a.91.91,0,0,0,.35.51,1.6,1.6,0,0,0,.319.169h.08a1.067,1.067,0,0,0,.36.06.974.974,0,0,0,.24,0l3.88-.67a16.253,16.253,0,0,0,2.88,4.109l-.18,3.28A16.626,16.626,0,0,1,54,296.171Zm36.72-1.79.007-.024-.007.124Zm.18-3.171c.1-.349.169-.709.249-1.059a2.392,2.392,0,0,0,.1-.45l.17-3.191,3.88-.67a1.18,1.18,0,0,0,.33-.11.82.82,0,0,0,.15-.09.737.737,0,0,0,.13-.1.553.553,0,0,0,.044-.046L95.8,288.3a1.073,1.073,0,0,1,0,.17v.16a.71.71,0,0,1-.13.18l-.121.08-.139.1a1.027,1.027,0,0,1-.25.08H95.1l-3.79.65-.09,1.63a15.119,15.119,0,0,1-.17,1.53c-.03.16-.04.13-.11.5s-.127.673-.213.976Zm-42.851-2.02-.01.174A.789.789,0,0,1,48.051,289.191Zm1.44-4.33a1.477,1.477,0,0,1-.3-.16,1,1,0,0,1-.39-.78l.18-3.27a.93.93,0,0,0,.39.77,1.338,1.338,0,0,0,.29.159l3.66,1.381-.18,3.27Zm46.49.16a.718.718,0,0,1,.06.26v.079a.57.57,0,0,1-.086.134Zm-8.93-7.851,2.59-2.47a1,1,0,0,0,.169-.24v-.069a.812.812,0,0,0,.06-.26l-.18,3.27a.549.549,0,0,1,0,.18v.15a1.084,1.084,0,0,1-.18.239l-2.64,2.471Zm-29.68-2.2a.8.8,0,0,1-.16-.52l.18-3.28a.835.835,0,0,0,.18.49l2,2.911-.18,3.27Zm19.67-3.41.38-3.31-.18,3.271-.38,3.32Z" transform="translate(13.609 -206.171)" fill="url(#linear-gradient)"/>
      <path id="路径_214" data-name="路径 214" d="M100.18,96.38a.86.86,0,0,1-.4,1.3L96.19,99.4a1.32,1.32,0,0,1-1.33-.12,1.2,1.2,0,0,1-.22-.23l-2-2.9a23.209,23.209,0,0,1-5.51.95l-.37,3.31a1.06,1.06,0,0,1-1.23.83l-4.16-.39a1.3,1.3,0,0,1-.64-.24.91.91,0,0,1-.38-.81l.36-3.32a22.189,22.189,0,0,1-5.16-2L72.9,97a1.29,1.29,0,0,1-1.6.06L68.16,94.7a.84.84,0,0,1-.1-1.34l2.63-2.47a16.28,16.28,0,0,1-2.88-4.11l-3.88.67a1.27,1.27,0,0,1-1-.23.91.91,0,0,1-.35-.51l-.92-3.42a1,1,0,0,1,.86-1.13l3.88-.66a13.64,13.64,0,0,1,.53-4.7l-3.66-1.38a1.35,1.35,0,0,1-.29-.16.88.88,0,0,1-.3-1.1L64.34,71a1.2,1.2,0,0,1,1.48-.48l3.67,1.39A15.43,15.43,0,0,1,71.18,70a16.69,16.69,0,0,1,2-1.61l-2-2.91a.86.86,0,0,1,.4-1.29l3.59-1.73a1.32,1.32,0,0,1,1.33.13,1,1,0,0,1,.23.23l2,2.9a22.56,22.56,0,0,1,5.5-1l.37-3.31a1.06,1.06,0,0,1,1.23-.83L90,61a1.28,1.28,0,0,1,.65.24.9.9,0,0,1,.38.8l-.38,3.33a21.25,21.25,0,0,1,5.17,1.95l2.64-2.47a1.28,1.28,0,0,1,1.59,0l3.14,2.36a.86.86,0,0,1,.11,1.34L100.66,71a15.94,15.94,0,0,1,2.87,4.1l3.9-.67a1.27,1.27,0,0,1,1,.23,1,1,0,0,1,.36.51l.92,3.44a.94.94,0,0,1-.86,1.11l-3.88.67a13.66,13.66,0,0,1-.52,4.7l3.65,1.37a1.46,1.46,0,0,1,.3.17.85.85,0,0,1,.29,1.09L107,90.91a1.21,1.21,0,0,1-1.48.47L101.86,90a16.39,16.39,0,0,1-1.68,1.88,18.409,18.409,0,0,1-2,1.6Zm-15.7-4.72c7.08.67,13.35-3.59,14-9.51A10.14,10.14,0,0,0,94.17,73a14.37,14.37,0,0,0-7.3-2.76c-7.08-.67-13.35,3.59-14,9.51a10.16,10.16,0,0,0,4.32,9.19,14.42,14.42,0,0,0,7.3,2.75" fill="url(#linear-gradient-6)"/>
    </g>
    <path id="路径_169" data-name="路径 169" d="M89,71.43h0a1.858,1.858,0,0,1-.1.2h0a.75.75,0,0,1-.17.19,1,1,0,0,1-.15.11l-.12.09L57.53,90a2.66,2.66,0,0,1-.65.25h-.1a3.47,3.47,0,0,1-.73.08h0a3.31,3.31,0,0,1-.77-.09h0a2.14,2.14,0,0,1-.68-.26L23.49,72.07a1,1,0,0,1-.62-.87v13a1,1,0,0,0,.62.87l31.06,17.94.3.14.26.09.15.084h1.191l.046,0,.224,0h.159l.16-.084a3.189,3.189,0,0,0,.46-.2L88.35,85.11h0l.11-.08.15-.11h0a.83.83,0,0,0,.13-.14h0v-.06a.61.61,0,0,0,0-.12h0a.31.31,0,0,1,0-.08.49.49,0,0,0,0-.12h0V71.35A.78.78,0,0,1,89,71.43Z" transform="translate(1.145 0.79)" stroke="#fff" stroke-linejoin="bevel" stroke-miterlimit="10" stroke-width="0.5" fill="url(#linear-gradient-7)"/>
    <path id="路径_170" data-name="路径 170" d="M88.38,70.34a.92.92,0,0,1,0,1.73L57.53,90a3.26,3.26,0,0,1-3,0L23.49,72.07a.91.91,0,0,1,0-1.73L54.34,52.41a3.27,3.27,0,0,1,3,0Z" transform="translate(1.145 0.79)" stroke="#fff" stroke-miterlimit="10" stroke-width="0.2" fill="url(#linear-gradient-8)"/>
    <path id="路径_171" data-name="路径 171" d="M96.82,50.8h0a1.14,1.14,0,0,1-.1.19h0a.72.72,0,0,1-.17.18l-.14.11-.13.1-16.8,9.8a2.58,2.58,0,0,1-.64.25h-.1a3.549,3.549,0,0,1-.73.08h0a3.77,3.77,0,0,1-.76-.08h0a2.59,2.59,0,0,1-.69-.26L59.63,51.44a1.05,1.05,0,0,1-.62-.87v16.3a1.06,1.06,0,0,0,.62.87l16.88,9.74a2.1,2.1,0,0,0,.3.15l.26.08H78.6a.45.45,0,0,0,.11,0H79a2.37,2.37,0,0,0,.46-.2L96.2,67.74h0l.1-.07a1.274,1.274,0,0,0,.15-.11h0l.13-.14h0v-.06a.61.61,0,0,0,.05-.12h0a.24.24,0,0,1,0-.08.49.49,0,0,0,0-.12h0V50.74a.93.93,0,0,1,.19.06Z" transform="translate(1.145 0.79)" stroke="#fff" stroke-width="0.5" fill="url(#linear-gradient-9)"/>
    <path id="路径_172" data-name="路径 172" d="M96.24,49.71a.92.92,0,0,1,0,1.73L79.48,61.18a3.33,3.33,0,0,1-3,0L59.63,51.44a.92.92,0,0,1,0-1.73L76.38,40a3.26,3.26,0,0,1,3,0Z" transform="translate(1.145 0.79)" stroke="#fff" stroke-miterlimit="10" stroke-width="0.2" fill="url(#linear-gradient-8)"/>
    <path id="路径_173" data-name="路径 173" d="M77.67,53.72h0a.75.75,0,0,1-.1.2h0a1.541,1.541,0,0,1-.17.19s-.1.07-.15.11l-.13.09L57.51,65.74a2.51,2.51,0,0,1-.65.25h-.1a2.92,2.92,0,0,1-.73.09h0a3.31,3.31,0,0,1-.77-.09h0a2.52,2.52,0,0,1-.68-.26L34.82,54.36a1.07,1.07,0,0,1-.63-.87l-.07,24.45a1.05,1.05,0,0,0,.63.87L54.46,90.2l.31.14.25.09h1.93a2.37,2.37,0,0,0,.46-.2L77,78.82h0a.39.39,0,0,0,.1-.08.83.83,0,0,0,.15-.11h0a.57.57,0,0,0,.13-.14h0a.61.61,0,0,0,0-.12h0a.19.19,0,0,1,0-.08.49.49,0,0,0,0-.12h0L77.7,53.5a.78.78,0,0,1-.03.22Z" transform="translate(1.145 0.79)" stroke="#fff" stroke-linejoin="bevel" stroke-miterlimit="10" stroke-width="0.4" fill="url(#linear-gradient-11)"/>
    <path id="路径_174" data-name="路径 174" d="M77.08,52.64a.91.91,0,0,1,0,1.72L57.51,65.74a3.26,3.26,0,0,1-3,0L34.82,54.36c-.83-.48-.84-1.25,0-1.73L54.39,41.25a3.33,3.33,0,0,1,3,0Z" transform="translate(1.145 0.79)" stroke="#fff" stroke-miterlimit="10" stroke-width="0.2" fill="url(#linear-gradient-12)"/>
    <g id="组_61" data-name="组 61" transform="translate(6.6 8.697)">
      <path id="路径_187" data-name="路径 187" d="M86,13.82a16.16,16.16,0,0,0-8.18-3.08c-.55-.05-1.09-.07-1.63-.07H75.7a6.623,6.623,0,0,0-1.18.07l-.56.07a5.871,5.871,0,0,0-1.1.18.683.683,0,0,0-.34.06h-.16c-.46.1-.9.23-1.34.37h-.17a12.354,12.354,0,0,0-1.42.57h-.11c-.24.11-.47.24-.71.36l-.26.13a9.005,9.005,0,0,0-1,.64l-.24.17c-.25.18-.49.35-.72.54l-.32.28-.39.33-.13.14c-.26.25-.5.51-.74.78-.07.09-.15.17-.22.25a11.2,11.2,0,0,0-.82,1.13c-.06.09-.11.19-.17.29a10.362,10.362,0,0,0-.5.93c-.07.14-.12.29-.19.43s-.22.58-.32.88l-.1.27a.505.505,0,0,1-.07.31c-.07.1,0,.2-.07.29a9.93,9.93,0,0,0-.19,1.09v.58l-.18,3.28v-.58a9.93,9.93,0,0,1,.19-1.09.448.448,0,0,1,.07-.29,3.457,3.457,0,0,0,.17-.58c.06-.19.2-.59.32-.88s.13-.29.19-.44c.15-.32.32-.62.5-.93.06-.09.11-.19.17-.29A11.069,11.069,0,0,1,64.4,19c.07-.09.15-.17.22-.25.24-.27.48-.54.75-.79s.33-.31.51-.46l.32-.28c.23-.19.48-.37.72-.54l.24-.18c.33-.22.68-.43,1-.63l.26-.13c.26-.14.53-.28.81-.41a13.51,13.51,0,0,1,1.42-.56l.17-.06c.44-.14.88-.26,1.34-.37l.5-.1c.36-.07.73-.14,1.1-.19l.56-.06c.39,0,.78-.06,1.18-.07h.49a13.013,13.013,0,0,1,1.64.07,16.13,16.13,0,0,1,8.17,3.09,11.47,11.47,0,0,1,4.89,9.69l.18-3.28A11.48,11.48,0,0,0,86,13.82Z" fill="#5b69c3" stroke="#fff" stroke-width="0.5"/>
      <path id="联合_1" data-name="联合 1" d="M74.681,315.351h-.14l-4.659-.45a1.339,1.339,0,0,1-.72-.27,1,1,0,0,1-.42-.841l.18-3.27a1,1,0,0,0,.42.84,1.476,1.476,0,0,0,.72.27l4.759.491a1.308,1.308,0,0,0,.481,0,1.775,1.775,0,0,0,.31-.14l.14-.09a1,1,0,0,0,.229-.239.92.92,0,0,0,.15-.4l.41-3.7c.65-.03,1.3-.1,1.939-.17l.54-.08c.5-.08,1.01-.16,1.5-.27l.48-.1c.57-.13,1.14-.27,1.7-.45l2.27,3.25a1.011,1.011,0,0,0,.25.26.994.994,0,0,0,.38.19h.751a1.083,1.083,0,0,0,.349-.11l4-1.93a1.159,1.159,0,0,0,.28-.191l.019-.021-.149,2.7a1.48,1.48,0,0,1,0,.2.568.568,0,0,1,0,.111l-.06.12-.09.1-.07.09a.912.912,0,0,1-.28.19l-4,1.94a1.548,1.548,0,0,1-.281.09h-.39a1.419,1.419,0,0,1-.29,0h-.131a1.5,1.5,0,0,1-.38-.19,1.246,1.246,0,0,1-.25-.26l-2.27-3.25c-.56.17-1.13.32-1.7.45h-.149l-.33.059c-.49.1-.99.19-1.49.26l-.55.08a15.679,15.679,0,0,1-1.939.17l-.41,3.71v.14a1.183,1.183,0,0,1-.12.25,1.149,1.149,0,0,1-.14.169.106.106,0,0,1-.09.06l-.14.1-.1.06-.211.07c-.051,0-.1.007-.155.007S74.732,315.356,74.681,315.351Zm-15.47-4.46h-.16a1.186,1.186,0,0,1-.419-.21l-3.51-2.641a1,1,0,0,1-.43-.859l.18-3.281a1,1,0,0,0,.43.86l3.52,2.65a1.284,1.284,0,0,0,.419.22h.16a1.7,1.7,0,0,0,.26.06h.06a1.526,1.526,0,0,0,.37-.07h.08a1.217,1.217,0,0,0,.37-.23l3-2.759a24.167,24.167,0,0,0,5.79,2.139l-.14,3.28a24.142,24.142,0,0,1-5.79-2.2l-3,2.78a1.463,1.463,0,0,1-.36.26H59.5q-.072,0-.145,0T59.211,310.891Zm31.82-3.53a1.787,1.787,0,0,1,0,.22.612.612,0,0,1,.19.05l-.06.11-.09.11-.071.079Zm-2.171-3.92c.391-.28.79-.56,1.17-.859.111-.09.08-.08.34-.29s.52-.43.77-.661a18.254,18.254,0,0,0,1.88-2.15l4.109,1.551a1.554,1.554,0,0,0,.52.09,1.475,1.475,0,0,0,.41-.06h.08a1,1,0,0,0,.27-.14l.12-.08a1.077,1.077,0,0,0,.26-.31l1.74-3.37a1.006,1.006,0,0,0,.1-.371l-.18,3.28a.611.611,0,0,1,0,.19,1.332,1.332,0,0,1-.07.18l-1.849,3.57a1.139,1.139,0,0,1-.21.28l-.121.08-.149.1h-.281a1.114,1.114,0,0,1-.31,0,1.338,1.338,0,0,1-.52-.1l-4.109-1.551a18.344,18.344,0,0,1-1.88,2.1l-.76.65-.34.28c-.38.3-.779.59-1.17.86Zm-34.15-4.279-4.35.74h-.25a1.38,1.38,0,0,1-.41-.06h-.08a1.032,1.032,0,0,1-.349-.18,1,1,0,0,1-.4-.57l-1-3.84a.7.7,0,0,1,0-.28l.169-3.072a.673.673,0,0,0,.011.073l1,3.84a1,1,0,0,0,.4.57,1.3,1.3,0,0,0,.349.19h.08a1.861,1.861,0,0,0,.41.06h.27l4.35-.73a18,18,0,0,0,3.22,4.6l-.18,3.28A18.008,18.008,0,0,1,54.711,299.161Zm41.11-1.95.006-.021-.006.1Zm.18-3.19c.11-.391.19-.79.27-1.18a4.186,4.186,0,0,0,.128-.526l-.008,1.5a16.729,16.729,0,0,1-.2,1.721,3.813,3.813,0,0,0-.131.56c-.068.363-.137.726-.234,1.089ZM53.141,291.47l.147-2.656q-.011.583.023,1.165l-.175,3.19A15.3,15.3,0,0,1,53.141,291.47Zm43.259.774c0,.009,0,.017,0,.026s0,.03,0,.045Zm0-.211v.211C96.4,292.174,96.4,292.1,96.4,292.034Zm0-.023h0v.023Zm.17-3.26,4.35-.74a1.4,1.4,0,0,0,.36-.12,6.908,6.908,0,0,0,.32-.221v-.06a.769.769,0,0,0,.069-.084l-.159,2.885a1.234,1.234,0,0,1,0,.2l-.08.18a1.267,1.267,0,0,1-.139.21.989.989,0,0,1-.131.09,1.047,1.047,0,0,1-.18.11,1.093,1.093,0,0,1-.28.09h-.06l-4.239.72Zm-48.52,2.94-.011.207A.664.664,0,0,1,48.051,291.691Zm1.59-5.23a1.833,1.833,0,0,1-.33-.18,1,1,0,0,1-.43-.86l.18-3.27a1,1,0,0,0,.43.85,1.729,1.729,0,0,0,.33.18l4.09,1.54L53.731,288Zm52.05.68a.946.946,0,0,1,.05.17.376.376,0,0,1,0,.09.773.773,0,0,1-.072.125Zm-10.14-8.9,3-2.769a1.089,1.089,0,0,0,.2-.27v-.08a.99.99,0,0,0,.08-.291l-.18,3.281a1.48,1.48,0,0,1,0,.2.19.19,0,0,1,0,.09v.08a1.051,1.051,0,0,1-.28.27l-3,2.77Zm-33.06-2.89a.885.885,0,0,1-.18-.589l.18-3.281a.922.922,0,0,0,.17.59l2.28,3.28-.18,3.281Zm21.98-3.379.41-3.721-.17,3.281-.42,3.72Z" transform="translate(1.599 -266.631)" fill="url(#linear-gradient)"/>
      <path id="路径_199" data-name="路径 199" d="M92.74,40a1,1,0,0,1-.44,1.44l-4,1.93a1.48,1.48,0,0,1-1.48-.14,1,1,0,0,1-.25-.26L84.3,39.72a25,25,0,0,1-6.16,1.07l-.41,3.7a1.22,1.22,0,0,1-1.38.94L71.66,45a1.49,1.49,0,0,1-.72-.27,1,1,0,0,1-.42-.9l.41-3.72A24.14,24.14,0,0,1,65.14,38l-3,2.76a1.43,1.43,0,0,1-1.78.06L56.9,38.13a1,1,0,0,1-.12-1.5l2.95-2.76a18,18,0,0,1-3.22-4.6L52.16,30a1.41,1.41,0,0,1-1.11-.26,1,1,0,0,1-.4-.57l-1-3.84a1.05,1.05,0,0,1,1-1.25l4.34-.75a15.43,15.43,0,0,1,.59-5.26l-4.09-1.54a1.74,1.74,0,0,1-.33-.18,1,1,0,0,1-.33-1.22l1.86-3.57a1.34,1.34,0,0,1,1.66-.53l4.09,1.55a17.93,17.93,0,0,1,4.17-3.9L60.26,5.44A1,1,0,0,1,60.71,4l4-1.93a1.49,1.49,0,0,1,1.49.14,1.24,1.24,0,0,1,.25.26l2.26,3.24A25.64,25.64,0,0,1,74.9,4.65L75.31.94A1.21,1.21,0,0,1,76.69,0l4.65.44a1.49,1.49,0,0,1,.72.27,1,1,0,0,1,.42.9l-.41,3.72a24,24,0,0,1,5.79,2.19l3-2.76a1.43,1.43,0,0,1,1.78-.06l3.51,2.64a1,1,0,0,1,.12,1.49l-3,2.77a17.75,17.75,0,0,1,3.22,4.6l4.36-.75a1.45,1.45,0,0,1,1.11.25,1.11,1.11,0,0,1,.4.58l1,3.84a1.07,1.07,0,0,1-1,1.25l-4.35.74a15.21,15.21,0,0,1-.58,5.27l4.08,1.54a1.22,1.22,0,0,1,.34.18,1,1,0,0,1,.32,1.22l-1.85,3.57a1.35,1.35,0,0,1-1.66.53l-4.11-1.55A18.291,18.291,0,0,1,92.74,35a22.081,22.081,0,0,1-2.28,1.81ZM75.16,34.7c7.93.75,15-4,15.7-10.64A11.36,11.36,0,0,0,86,13.82a16.16,16.16,0,0,0-8.18-3.08c-7.92-.75-15,4-15.69,10.64A11.35,11.35,0,0,0,67,31.66a16.09,16.09,0,0,0,8.17,3.08" fill="url(#linear-gradient-14)"/>
    </g>
  </g>
</svg>
