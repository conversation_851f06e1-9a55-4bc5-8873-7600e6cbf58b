<!--
* @Author: 王磊磊
* @Date: 2023/4/11
* @Version: 1.0
* @Content: 面包屑
-->
<template>
  <div class="rt-breadcrumb flex-wrap">
    <el-breadcrumb v-bind="$attrs">
      <el-breadcrumb-item v-for="(item, dex) in list" :key="dex" :class="{ select: dex >= defaultShowDeep - 1 }" @click="handleClick(item, dex)"
        >{{ item[labelField] || item }}
      </el-breadcrumb-item>
    </el-breadcrumb>
    <div class="rt-breadcrumb__line">
      <div class="rt-breadcrumb__modification"></div>
    </div>
    <BreadSvg class="rt-breadcrumb__svg" />
    <slot name="right"></slot>
  </div>
</template>

<script lang="ts">
export default {
  name: 'SimBreadcrumbForProp'
};
</script>
<script setup lang="ts">
import BreadSvg from './svg/breadSvg.vue';
import type { PropType } from 'vue';

defineProps({
  list: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  labelField: {
    type: String,
    default: 'name'
  },

  defaultShowDeep: {
    type: Number,
    default: 0
  }
});

const emits = defineEmits(['item-click']);
const handleClick = (data: any, index: number) => {
  emits('item-click', data, index);
};
</script>

<style scoped lang="less">
.rt-breadcrumb {
  position: relative;
  padding-left: 6px;

  &__svg {
    position: absolute;
    right: 0;
    top: -2px;
  }

  &::before {
    content: '';
    position: absolute;
    z-index: 9;
    top: 1px;
    left: 0;
    bottom: 0;
    margin: auto;
    width: 2px;
    height: 12px;
    background: var(--primary-color);
  }

  &__line {
    position: relative;
    margin-left: 8px;
    height: 1px;
    background: rgba(var(--primary-color-val), 0.3);
    flex: 1;
    bottom: -3px;
  }

  &__modification {
    position: absolute;
    right: 0;
    bottom: -8px;
  }

  :deep(.el-breadcrumb) {
    .el-breadcrumb__item:last-child .el-breadcrumb__inner {
      color: rgba(var(--primary-color-val), 1);
      cursor: auto;
    }
  }

  :deep(.el-breadcrumb__inner) {
    color: rgba(var(--text-color-val), 0.7);
  }

  .select {
    :deep(.el-breadcrumb__inner) {
      color: rgba(var(--text-color-val), 1);
      cursor: pointer;

      &:hover {
        color: rgba(var(--primary-color-val), 1);
      }
    }
  }
}
</style>
