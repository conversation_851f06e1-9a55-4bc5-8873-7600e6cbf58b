<!--
* @Author: 张乐
* @Date: 2023/04/11
* @Version: 1.0
* @Content: DatePicker 组件
-->
<template>
  <sim-block class="sim-data-picker" :class="{ 'sim-readonly': $attrs.readonly }">
    <el-date-picker ref="datePicker" v-bind="$attrs" :default-time="defaultTime" :class="className"></el-date-picker>
  </sim-block>
</template>

<script lang="ts">
export default {
  name: 'SimDatePicker'
};
</script>
<script setup lang="ts">
// 第三方包
import { ElDatePicker } from 'element-plus';

// 组件
import { SimBlock } from '../../../';

// hooks
defineProps({
  className: {
    type: String,
    default: ''
  }
});
const attrs = useAttrs();
const defaultTime = ['daterange', 'datetimerange'].includes(attrs.type as string)
  ? [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]
  : new Date(2000, 1, 1, 0, 0, 0);
const datePicker = ref<InstanceType<typeof ElDatePicker>>();
defineExpose(datePicker);
</script>

<style scoped lang="less">
.rt-data-pick {
  width: 100%;
}
.sim-data-picker {
  height: 32px;
  padding: 0;
  :deep(.el-date-editor--datetimerange),
  :deep(.el-date-editor--daterange) {
    background: transparent;
    box-shadow: none;
  }
  :deep(.el-range-separator) {
    color: var(--text-color);
  }
}
</style>
