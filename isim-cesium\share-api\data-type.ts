export function isMap(data: any): data is Map<any, any> {
  return isDataType(data, 'Map');
}

export function isSet(data: any): data is Set<any> {
  return isDataType(data, 'Set');
}

export function isObject(data: any): data is object {
  return isDataType(data, 'Object');
}

export function isArray(data: any): data is Array<any> {
  return isDataType(data, 'Array');
}

export function isString(data: any): data is string {
  return isDataType(data, 'String');
}

export function isNumber(data: any): data is number {
  return isDataType(data, 'Number');
}

export function isDefine(data: any): boolean {
  return data !== null && data !== undefined;
}

export function isDataType(data: any, type: string) {
  return Object.prototype.toString.call(data) === `[object ${type}]`;
}
