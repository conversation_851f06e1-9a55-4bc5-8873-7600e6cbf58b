<!--
* <AUTHOR> 宋计民
* @Date :  2023/3/27
* @Version : 1.0
* @Content : fold
-->
<template>
  <div class="sim-fold">
    <div class="sim-fold__head" @click="setIsFold($event, true)">
      <i v-if="iconPos === 'left'" class="sim-fold__icon" @click="setIsFold($event, false)">
        <svg class="sim-fold__svg" :class="[isFoldState && 'sim-fold__svg--fold']">
          <path d="M 0 5 l 8 8 l 8 -8 z" stroke-width="3px" fill="white" />
        </svg>
      </i>
      <slot name="header-title">
        <span>{{ title }}</span>
      </slot>
      <i v-if="iconPos === 'right'" class="sim-fold__icon" @click="setIsFold($event, false)">
        <svg class="sim-fold__svg" :class="[isFoldState && 'sim-fold__svg--fold']">
          <path d="M 0 5 l 8 8 l 8 -8 z" stroke-width="3px" fill="white" />
        </svg>
      </i>
    </div>
    <transition name="sim-fold">
      <div v-show="isFoldState" class="sim-fold__content">
        <slot />
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

defineOptions({
  name: 'SimFold'
});
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  iconPos: {
    type: String as PropType<'left' | 'right'>,
    default: 'right'
  },
  clickHeaderFold: {
    type: Boolean,
    default: true
  }
});

const iconPosValue = props.iconPos === 'left' ? 'flex-start' : 'space-between';

const isFoldState = defineModel<boolean>({ default: true });

const setIsFold = (e: MouseEvent, isHeader = false) => {
  e.preventDefault();
  e.stopPropagation();
  if (isHeader && !props.clickHeaderFold) {
    return;
  }
  isFoldState.value = !isFoldState.value;
};
</script>
<style scoped lang="less">
.sim-fold {
  overflow: hidden;
  &__head {
    display: flex;
    justify-content: v-bind('iconPosValue');
    align-items: center;
    padding: 0 5px;
    background-color: var(--model-bg-color-5);
  }
  &__icon {
    .sim-fold__svg {
      height: 20px;
      width: 20px;
      transform: rotateZ(-90deg);
      transition: all 100ms linear;
    }
    .sim-fold__svg--fold {
      transform: rotateZ(0);
    }
  }

  &-enter-active {
    animation: fadeIn 0.3s;
  }
  &-leave-active {
    animation: fadeOut 0.3s;
  }
  &__content {
    background: var(--alpha-bg-color);
  }
  &--enter {
  }
}
</style>
