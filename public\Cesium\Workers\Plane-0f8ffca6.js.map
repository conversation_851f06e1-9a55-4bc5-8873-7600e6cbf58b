{"version": 3, "file": "Plane-0f8ffca6.js", "sources": ["../../../../Source/Core/Plane.js"], "sourcesContent": ["import Cartesian3 from \"./Cartesian3.js\";\nimport Cartesian4 from \"./Cartesian4.js\";\nimport Check from \"./Check.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix4 from \"./Matrix4.js\";\n\n/**\n * A plane in Hessian Normal Form defined by\n * <pre>\n * ax + by + cz + d = 0\n * </pre>\n * where (a, b, c) is the plane's <code>normal</code>, d is the signed\n * <code>distance</code> to the plane, and (x, y, z) is any point on\n * the plane.\n *\n * @alias Plane\n * @constructor\n *\n * @param {Cartesian3} normal The plane's normal (normalized).\n * @param {Number} distance The shortest distance from the origin to the plane.  The sign of\n * <code>distance</code> determines which side of the plane the origin\n * is on.  If <code>distance</code> is positive, the origin is in the half-space\n * in the direction of the normal; if negative, the origin is in the half-space\n * opposite to the normal; if zero, the plane passes through the origin.\n *\n * @example\n * // The plane x=0\n * const plane = new Cesium.Plane(Cesium.Cartesian3.UNIT_X, 0.0);\n *\n * @exception {DeveloperError} Normal must be normalized\n */\nfunction Plane(normal, distance) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"normal\", normal);\n  if (\n    !CesiumMath.equalsEpsilon(\n      Cartesian3.magnitude(normal),\n      1.0,\n      CesiumMath.EPSILON6\n    )\n  ) {\n    throw new DeveloperError(\"normal must be normalized.\");\n  }\n  Check.typeOf.number(\"distance\", distance);\n  //>>includeEnd('debug');\n\n  /**\n   * The plane's normal.\n   *\n   * @type {Cartesian3}\n   */\n  this.normal = Cartesian3.clone(normal);\n\n  /**\n   * The shortest distance from the origin to the plane.  The sign of\n   * <code>distance</code> determines which side of the plane the origin\n   * is on.  If <code>distance</code> is positive, the origin is in the half-space\n   * in the direction of the normal; if negative, the origin is in the half-space\n   * opposite to the normal; if zero, the plane passes through the origin.\n   *\n   * @type {Number}\n   */\n  this.distance = distance;\n}\n\n/**\n * Creates a plane from a normal and a point on the plane.\n *\n * @param {Cartesian3} point The point on the plane.\n * @param {Cartesian3} normal The plane's normal (normalized).\n * @param {Plane} [result] The object onto which to store the result.\n * @returns {Plane} A new plane instance or the modified result parameter.\n *\n * @example\n * const point = Cesium.Cartesian3.fromDegrees(-72.0, 40.0);\n * const normal = ellipsoid.geodeticSurfaceNormal(point);\n * const tangentPlane = Cesium.Plane.fromPointNormal(point, normal);\n *\n * @exception {DeveloperError} Normal must be normalized\n */\nPlane.fromPointNormal = function (point, normal, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"point\", point);\n  Check.typeOf.object(\"normal\", normal);\n  if (\n    !CesiumMath.equalsEpsilon(\n      Cartesian3.magnitude(normal),\n      1.0,\n      CesiumMath.EPSILON6\n    )\n  ) {\n    throw new DeveloperError(\"normal must be normalized.\");\n  }\n  //>>includeEnd('debug');\n\n  const distance = -Cartesian3.dot(normal, point);\n\n  if (!defined(result)) {\n    return new Plane(normal, distance);\n  }\n\n  Cartesian3.clone(normal, result.normal);\n  result.distance = distance;\n  return result;\n};\n\nconst scratchNormal = new Cartesian3();\n/**\n * Creates a plane from the general equation\n *\n * @param {Cartesian4} coefficients The plane's normal (normalized).\n * @param {Plane} [result] The object onto which to store the result.\n * @returns {Plane} A new plane instance or the modified result parameter.\n *\n * @exception {DeveloperError} Normal must be normalized\n */\nPlane.fromCartesian4 = function (coefficients, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"coefficients\", coefficients);\n  //>>includeEnd('debug');\n\n  const normal = Cartesian3.fromCartesian4(coefficients, scratchNormal);\n  const distance = coefficients.w;\n\n  //>>includeStart('debug', pragmas.debug);\n  if (\n    !CesiumMath.equalsEpsilon(\n      Cartesian3.magnitude(normal),\n      1.0,\n      CesiumMath.EPSILON6\n    )\n  ) {\n    throw new DeveloperError(\"normal must be normalized.\");\n  }\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Plane(normal, distance);\n  }\n  Cartesian3.clone(normal, result.normal);\n  result.distance = distance;\n  return result;\n};\n\n/**\n * Computes the signed shortest distance of a point to a plane.\n * The sign of the distance determines which side of the plane the point\n * is on.  If the distance is positive, the point is in the half-space\n * in the direction of the normal; if negative, the point is in the half-space\n * opposite to the normal; if zero, the plane passes through the point.\n *\n * @param {Plane} plane The plane.\n * @param {Cartesian3} point The point.\n * @returns {Number} The signed shortest distance of the point to the plane.\n */\nPlane.getPointDistance = function (plane, point) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"plane\", plane);\n  Check.typeOf.object(\"point\", point);\n  //>>includeEnd('debug');\n\n  return Cartesian3.dot(plane.normal, point) + plane.distance;\n};\n\nconst scratchCartesian = new Cartesian3();\n/**\n * Projects a point onto the plane.\n * @param {Plane} plane The plane to project the point onto\n * @param {Cartesian3} point The point to project onto the plane\n * @param {Cartesian3} [result] The result point.  If undefined, a new Cartesian3 will be created.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if one was not provided.\n */\nPlane.projectPointOntoPlane = function (plane, point, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"plane\", plane);\n  Check.typeOf.object(\"point\", point);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  // projectedPoint = point - (normal.point + scale) * normal\n  const pointDistance = Plane.getPointDistance(plane, point);\n  const scaledNormal = Cartesian3.multiplyByScalar(\n    plane.normal,\n    pointDistance,\n    scratchCartesian\n  );\n\n  return Cartesian3.subtract(point, scaledNormal, result);\n};\n\nconst scratchInverseTranspose = new Matrix4();\nconst scratchPlaneCartesian4 = new Cartesian4();\nconst scratchTransformNormal = new Cartesian3();\n/**\n * Transforms the plane by the given transformation matrix.\n *\n * @param {Plane} plane The plane.\n * @param {Matrix4} transform The transformation matrix.\n * @param {Plane} [result] The object into which to store the result.\n * @returns {Plane} The plane transformed by the given transformation matrix.\n */\nPlane.transform = function (plane, transform, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"plane\", plane);\n  Check.typeOf.object(\"transform\", transform);\n  //>>includeEnd('debug');\n\n  const normal = plane.normal;\n  const distance = plane.distance;\n  const inverseTranspose = Matrix4.inverseTranspose(\n    transform,\n    scratchInverseTranspose\n  );\n  let planeAsCartesian4 = Cartesian4.fromElements(\n    normal.x,\n    normal.y,\n    normal.z,\n    distance,\n    scratchPlaneCartesian4\n  );\n  planeAsCartesian4 = Matrix4.multiplyByVector(\n    inverseTranspose,\n    planeAsCartesian4,\n    planeAsCartesian4\n  );\n\n  // Convert the transformed plane to Hessian Normal Form\n  const transformedNormal = Cartesian3.fromCartesian4(\n    planeAsCartesian4,\n    scratchTransformNormal\n  );\n\n  planeAsCartesian4 = Cartesian4.divideByScalar(\n    planeAsCartesian4,\n    Cartesian3.magnitude(transformedNormal),\n    planeAsCartesian4\n  );\n\n  return Plane.fromCartesian4(planeAsCartesian4, result);\n};\n\n/**\n * Duplicates a Plane instance.\n *\n * @param {Plane} plane The plane to duplicate.\n * @param {Plane} [result] The object onto which to store the result.\n * @returns {Plane} The modified result parameter or a new Plane instance if one was not provided.\n */\nPlane.clone = function (plane, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"plane\", plane);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Plane(plane.normal, plane.distance);\n  }\n\n  Cartesian3.clone(plane.normal, result.normal);\n  result.distance = plane.distance;\n\n  return result;\n};\n\n/**\n * Compares the provided Planes by normal and distance and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Plane} left The first plane.\n * @param {Plane} right The second plane.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nPlane.equals = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  return (\n    left.distance === right.distance &&\n    Cartesian3.equals(left.normal, right.normal)\n  );\n};\n\n/**\n * A constant initialized to the XY plane passing through the origin, with normal in positive Z.\n *\n * @type {Plane}\n * @constant\n */\nPlane.ORIGIN_XY_PLANE = Object.freeze(new Plane(Cartesian3.UNIT_Z, 0.0));\n\n/**\n * A constant initialized to the YZ plane passing through the origin, with normal in positive X.\n *\n * @type {Plane}\n * @constant\n */\nPlane.ORIGIN_YZ_PLANE = Object.freeze(new Plane(Cartesian3.UNIT_X, 0.0));\n\n/**\n * A constant initialized to the ZX plane passing through the origin, with normal in positive Y.\n *\n * @type {Plane}\n * @constant\n */\nPlane.ORIGIN_ZX_PLANE = Object.freeze(new Plane(Cartesian3.UNIT_Y, 0.0));\nexport default Plane;\n"], "names": ["Plane", "normal", "distance", "Check", "typeOf", "object", "CesiumMath", "equalsEpsilon", "Cartesian3", "magnitude", "EPSILON6", "DeveloperError", "number", "this", "clone", "fromPointNormal", "point", "result", "dot", "defined", "scratchNormal", "fromCartesian4", "coefficients", "w", "getPointDistance", "plane", "scratchCartesian", "projectPointOntoPlane", "pointDistance", "scaledNormal", "multiplyByScalar", "subtract", "scratchInverseTranspose", "Matrix4", "scratchPlaneCartesian4", "Cartesian4", "scratchTransformNormal", "transform", "inverseTranspose", "planeAsCartesian4", "fromElements", "x", "y", "z", "multiplyByVector", "transformedNormal", "divideByScalar", "equals", "left", "right", "ORIGIN_XY_PLANE", "Object", "freeze", "UNIT_Z", "ORIGIN_YZ_PLANE", "UNIT_X", "ORIGIN_ZX_PLANE", "UNIT_Y"], "mappings": "qJAiCA,SAASA,EAAMC,EAAQC,GAGrB,GADAC,EAAAA,MAAMC,OAAOC,OAAO,SAAUJ,IAE3BK,EAAUA,WAACC,cACVC,EAAUA,WAACC,UAAUR,GACrB,EACAK,EAAAA,WAAWI,UAGb,MAAM,IAAIC,EAAAA,eAAe,8BAE3BR,EAAAA,MAAMC,OAAOQ,OAAO,WAAYV,GAQhCW,KAAKZ,OAASO,EAAAA,WAAWM,MAAMb,GAW/BY,KAAKX,SAAWA,CAClB,CAiBAF,EAAMe,gBAAkB,SAAUC,EAAOf,EAAQgB,GAI/C,GAFAd,EAAAA,MAAMC,OAAOC,OAAO,QAASW,GAC7Bb,EAAAA,MAAMC,OAAOC,OAAO,SAAUJ,IAE3BK,EAAUA,WAACC,cACVC,EAAUA,WAACC,UAAUR,GACrB,EACAK,EAAAA,WAAWI,UAGb,MAAM,IAAIC,EAAAA,eAAe,8BAI3B,MAAMT,GAAYM,EAAUA,WAACU,IAAIjB,EAAQe,GAEzC,OAAKG,EAAAA,QAAQF,IAIbT,EAAAA,WAAWM,MAAMb,EAAQgB,EAAOhB,QAChCgB,EAAOf,SAAWA,EACXe,GALE,IAAIjB,EAAMC,EAAQC,EAM7B,EAEA,MAAMkB,EAAgB,IAAIZ,EAAAA,WAU1BR,EAAMqB,eAAiB,SAAUC,EAAcL,GAE7Cd,EAAAA,MAAMC,OAAOC,OAAO,eAAgBiB,GAGpC,MAAMrB,EAASO,EAAUA,WAACa,eAAeC,EAAcF,GACjDlB,EAAWoB,EAAaC,EAG9B,IACGjB,EAAUA,WAACC,cACVC,EAAUA,WAACC,UAAUR,GACrB,EACAK,EAAAA,WAAWI,UAGb,MAAM,IAAIC,EAAAA,eAAe,8BAI3B,OAAKQ,EAAAA,QAAQF,IAGbT,EAAAA,WAAWM,MAAMb,EAAQgB,EAAOhB,QAChCgB,EAAOf,SAAWA,EACXe,GAJE,IAAIjB,EAAMC,EAAQC,EAK7B,EAaAF,EAAMwB,iBAAmB,SAAUC,EAAOT,GAMxC,OAJAb,EAAAA,MAAMC,OAAOC,OAAO,QAASoB,GAC7BtB,EAAAA,MAAMC,OAAOC,OAAO,QAASW,GAGtBR,EAAAA,WAAWU,IAAIO,EAAMxB,OAAQe,GAASS,EAAMvB,QACrD,EAEA,MAAMwB,EAAmB,IAAIlB,EAAAA,WAQ7BR,EAAM2B,sBAAwB,SAAUF,EAAOT,EAAOC,GAEpDd,EAAAA,MAAMC,OAAOC,OAAO,QAASoB,GAC7BtB,EAAAA,MAAMC,OAAOC,OAAO,QAASW,GAGxBG,EAAAA,QAAQF,KACXA,EAAS,IAAIT,EAAAA,YAIf,MAAMoB,EAAgB5B,EAAMwB,iBAAiBC,EAAOT,GAC9Ca,EAAerB,EAAAA,WAAWsB,iBAC9BL,EAAMxB,OACN2B,EACAF,GAGF,OAAOlB,EAAUA,WAACuB,SAASf,EAAOa,EAAcZ,EAClD,EAEA,MAAMe,EAA0B,IAAIC,EAAAA,QAC9BC,EAAyB,IAAIC,EAAAA,WAC7BC,EAAyB,IAAI5B,EAAAA,WASnCR,EAAMqC,UAAY,SAAUZ,EAAOY,EAAWpB,GAE5Cd,EAAAA,MAAMC,OAAOC,OAAO,QAASoB,GAC7BtB,EAAAA,MAAMC,OAAOC,OAAO,YAAagC,GAGjC,MAAMpC,EAASwB,EAAMxB,OACfC,EAAWuB,EAAMvB,SACjBoC,EAAmBL,EAAAA,QAAQK,iBAC/BD,EACAL,GAEF,IAAIO,EAAoBJ,EAAAA,WAAWK,aACjCvC,EAAOwC,EACPxC,EAAOyC,EACPzC,EAAO0C,EACPzC,EACAgC,GAEFK,EAAoBN,EAAOA,QAACW,iBAC1BN,EACAC,EACAA,GAIF,MAAMM,EAAoBrC,EAAAA,WAAWa,eACnCkB,EACAH,GASF,OANAG,EAAoBJ,EAAUA,WAACW,eAC7BP,EACA/B,EAAUA,WAACC,UAAUoC,GACrBN,GAGKvC,EAAMqB,eAAekB,EAAmBtB,EACjD,EASAjB,EAAMc,MAAQ,SAAUW,EAAOR,GAK7B,OAHAd,EAAAA,MAAMC,OAAOC,OAAO,QAASoB,GAGxBN,EAAAA,QAAQF,IAIbT,EAAUA,WAACM,MAAMW,EAAMxB,OAAQgB,EAAOhB,QACtCgB,EAAOf,SAAWuB,EAAMvB,SAEjBe,GANE,IAAIjB,EAAMyB,EAAMxB,OAAQwB,EAAMvB,SAOzC,EAUAF,EAAM+C,OAAS,SAAUC,EAAMC,GAM7B,OAJA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAI3BD,EAAK9C,WAAa+C,EAAM/C,UACxBM,EAAAA,WAAWuC,OAAOC,EAAK/C,OAAQgD,EAAMhD,OAEzC,EAQAD,EAAMkD,gBAAkBC,OAAOC,OAAO,IAAIpD,EAAMQ,aAAW6C,OAAQ,IAQnErD,EAAMsD,gBAAkBH,OAAOC,OAAO,IAAIpD,EAAMQ,aAAW+C,OAAQ,IAQnEvD,EAAMwD,gBAAkBL,OAAOC,OAAO,IAAIpD,EAAMQ,aAAWiD,OAAQ"}