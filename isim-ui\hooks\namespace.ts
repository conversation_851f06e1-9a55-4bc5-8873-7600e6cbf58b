import { computed, unref } from 'vue';
/*
 * @Author: 宋计民
 * @Date: 2022-04-13 17:54:27
 * @Version: 0.0.1
 * @Content:
 */
const defaultNamespace = 'i';
const statePrefix = 'is-';

// eslint-disable-next-line max-params
function _bem(namespace: string, block: string, blockSurffix: string, element: string, modifier: string) {
  let cls = `${namespace}-${block}`;
  if (blockSurffix) {
    cls += `-${blockSurffix}`;
  }
  if (element) {
    cls += `__${element}`;
  }

  if (modifier) {
    cls += `--${modifier}`;
  }
  return cls;
}

export function useNamespace(block: string) {
  const namespace = computed(() => defaultNamespace);

  const b = (blockSuffix = '') => _bem(unref(namespace), block, blockSuffix, '', '');

  const e = (element = '') => (element ? _bem(unref(namespace), block, '', element, '') : '');

  const m = (modifier = '') => (modifier ? _bem(unref(namespace), block, '', '', modifier) : '');

  const be = (blockSuffix?: string, element?: string) => (blockSuffix && element ? _bem(unref(namespace), block, blockSuffix, element, '') : '');

  const em = (element?: string, modifier?: string) => (element && modifier ? _bem(unref(namespace), block, '', element, modifier) : '');

  const bm = (blockSuffix?: string, modifier?: string) => (blockSuffix && modifier ? _bem(unref(namespace), block, blockSuffix, '', modifier) : '');

  const bem = (blockSuffix?: string, element?: string, modifier?: string) =>
    blockSuffix && element && modifier ? _bem(unref(namespace), block, blockSuffix, element, modifier) : '';
  const is: {
    (name: string, state: boolean | undefined): string;
    (name: string): string;
  } = (name: string, ...args: [boolean | undefined] | []) => {
    const state = args.length >= 1 ? args[0] : true;
    return name && state ? `${statePrefix}${name}` : '';
  };
  return {
    b,
    e,
    m,
    be,
    em,
    bm,
    bem,
    is
  };
}
