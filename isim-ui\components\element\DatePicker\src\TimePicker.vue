<!--
* @Author: 张乐
* @Date: 2023/04/11
* @Version: 1.0
* @Content: DatePicker 组件
-->
<template>
  <sim-block class="sim-time-picker" :class="{ 'sim-readonly': $attrs.readonly }">
    <el-time-picker ref="timePicker" v-bind="$attrs"></el-time-picker>
  </sim-block>
</template>

<script lang="ts">
export default {
  name: 'SimTimePicker'
};
</script>
<script setup lang="ts">
// 第三方包
import { ElDatePicker } from 'element-plus';

// 组件
import { SimBlock } from '../../../';

// hooks
defineProps({
  className: {
    type: String,
    default: ''
  }
});

const timePicker = ref<InstanceType<typeof ElDatePicker>>();
defineExpose(timePicker);
</script>

<style scoped lang="less">
.rt-data-pick {
  width: 100%;
}
.sim-data-picker {
  height: 32px;
  padding: 0;
  :deep(.el-date-editor--datetimerange),
  :deep(.el-date-editor--daterange) {
    background: transparent;
    box-shadow: none;
  }
  :deep(.el-range-separator) {
    color: var(--text-color);
  }
}
</style>
