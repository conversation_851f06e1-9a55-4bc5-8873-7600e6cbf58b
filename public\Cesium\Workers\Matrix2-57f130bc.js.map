{"version": 3, "file": "Matrix2-57f130bc.js", "sources": ["../../../../Source/Core/Cartesian3.js", "../../../../Source/Core/scaleToGeodeticSurface.js", "../../../../Source/Core/Cartographic.js", "../../../../Source/Core/Ellipsoid.js", "../../../../Source/Core/Matrix3.js", "../../../../Source/Core/Cartesian4.js", "../../../../Source/Core/Matrix4.js", "../../../../Source/Core/Rectangle.js", "../../../../Source/Core/Cartesian2.js", "../../../../Source/Core/Matrix2.js"], "sourcesContent": ["import Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\n\n/**\n * A 3D Cartesian point.\n * @alias Cartesian3\n * @constructor\n *\n * @param {Number} [x=0.0] The X component.\n * @param {Number} [y=0.0] The Y component.\n * @param {Number} [z=0.0] The Z component.\n *\n * @see Cartesian2\n * @see Cartesian4\n * @see Packable\n */\nfunction Cartesian3(x, y, z) {\n  /**\n   * The X component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.x = defaultValue(x, 0.0);\n\n  /**\n   * The Y component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.y = defaultValue(y, 0.0);\n\n  /**\n   * The Z component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.z = defaultValue(z, 0.0);\n}\n\n/**\n * Converts the provided Spherical into Cartesian3 coordinates.\n *\n * @param {Spherical} spherical The Spherical to be converted to Cartesian3.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if one was not provided.\n */\nCartesian3.fromSpherical = function (spherical, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"spherical\", spherical);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  const clock = spherical.clock;\n  const cone = spherical.cone;\n  const magnitude = defaultValue(spherical.magnitude, 1.0);\n  const radial = magnitude * Math.sin(cone);\n  result.x = radial * Math.cos(clock);\n  result.y = radial * Math.sin(clock);\n  result.z = magnitude * Math.cos(cone);\n  return result;\n};\n\n/**\n * Creates a Cartesian3 instance from x, y and z coordinates.\n *\n * @param {Number} x The x coordinate.\n * @param {Number} y The y coordinate.\n * @param {Number} z The z coordinate.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if one was not provided.\n */\nCartesian3.fromElements = function (x, y, z, result) {\n  if (!defined(result)) {\n    return new Cartesian3(x, y, z);\n  }\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return result;\n};\n\n/**\n * Duplicates a Cartesian3 instance.\n *\n * @param {Cartesian3} cartesian The Cartesian to duplicate.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if one was not provided. (Returns undefined if cartesian is undefined)\n */\nCartesian3.clone = function (cartesian, result) {\n  if (!defined(cartesian)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    return new Cartesian3(cartesian.x, cartesian.y, cartesian.z);\n  }\n\n  result.x = cartesian.x;\n  result.y = cartesian.y;\n  result.z = cartesian.z;\n  return result;\n};\n\n/**\n * Creates a Cartesian3 instance from an existing Cartesian4.  This simply takes the\n * x, y, and z properties of the Cartesian4 and drops w.\n * @function\n *\n * @param {Cartesian4} cartesian The Cartesian4 instance to create a Cartesian3 instance from.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if one was not provided.\n */\nCartesian3.fromCartesian4 = Cartesian3.clone;\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nCartesian3.packedLength = 3;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {Cartesian3} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nCartesian3.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  array[startingIndex++] = value.x;\n  array[startingIndex++] = value.y;\n  array[startingIndex] = value.z;\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {Cartesian3} [result] The object into which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if one was not provided.\n */\nCartesian3.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n  result.x = array[startingIndex++];\n  result.y = array[startingIndex++];\n  result.z = array[startingIndex];\n  return result;\n};\n\n/**\n * Flattens an array of Cartesian3s into an array of components.\n *\n * @param {Cartesian3[]} array The array of cartesians to pack.\n * @param {Number[]} [result] The array onto which to store the result. If this is a typed array, it must have array.length * 3 components, else a {@link DeveloperError} will be thrown. If it is a regular array, it will be resized to have (array.length * 3) elements.\n * @returns {Number[]} The packed array.\n */\nCartesian3.packArray = function (array, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  const length = array.length;\n  const resultLength = length * 3;\n  if (!defined(result)) {\n    result = new Array(resultLength);\n  } else if (!Array.isArray(result) && result.length !== resultLength) {\n    throw new DeveloperError(\n      \"If result is a typed array, it must have exactly array.length * 3 elements\"\n    );\n  } else if (result.length !== resultLength) {\n    result.length = resultLength;\n  }\n\n  for (let i = 0; i < length; ++i) {\n    Cartesian3.pack(array[i], result, i * 3);\n  }\n  return result;\n};\n\n/**\n * Unpacks an array of cartesian components into an array of Cartesian3s.\n *\n * @param {Number[]} array The array of components to unpack.\n * @param {Cartesian3[]} [result] The array onto which to store the result.\n * @returns {Cartesian3[]} The unpacked array.\n */\nCartesian3.unpackArray = function (array, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  Check.typeOf.number.greaterThanOrEquals(\"array.length\", array.length, 3);\n  if (array.length % 3 !== 0) {\n    throw new DeveloperError(\"array length must be a multiple of 3.\");\n  }\n  //>>includeEnd('debug');\n\n  const length = array.length;\n  if (!defined(result)) {\n    result = new Array(length / 3);\n  } else {\n    result.length = length / 3;\n  }\n\n  for (let i = 0; i < length; i += 3) {\n    const index = i / 3;\n    result[index] = Cartesian3.unpack(array, i, result[index]);\n  }\n  return result;\n};\n\n/**\n * Creates a Cartesian3 from three consecutive elements in an array.\n * @function\n *\n * @param {Number[]} array The array whose three consecutive elements correspond to the x, y, and z components, respectively.\n * @param {Number} [startingIndex=0] The offset into the array of the first element, which corresponds to the x component.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if one was not provided.\n *\n * @example\n * // Create a Cartesian3 with (1.0, 2.0, 3.0)\n * const v = [1.0, 2.0, 3.0];\n * const p = Cesium.Cartesian3.fromArray(v);\n *\n * // Create a Cartesian3 with (1.0, 2.0, 3.0) using an offset into an array\n * const v2 = [0.0, 0.0, 1.0, 2.0, 3.0];\n * const p2 = Cesium.Cartesian3.fromArray(v2, 2);\n */\nCartesian3.fromArray = Cartesian3.unpack;\n\n/**\n * Computes the value of the maximum component for the supplied Cartesian.\n *\n * @param {Cartesian3} cartesian The cartesian to use.\n * @returns {Number} The value of the maximum component.\n */\nCartesian3.maximumComponent = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return Math.max(cartesian.x, cartesian.y, cartesian.z);\n};\n\n/**\n * Computes the value of the minimum component for the supplied Cartesian.\n *\n * @param {Cartesian3} cartesian The cartesian to use.\n * @returns {Number} The value of the minimum component.\n */\nCartesian3.minimumComponent = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return Math.min(cartesian.x, cartesian.y, cartesian.z);\n};\n\n/**\n * Compares two Cartesians and computes a Cartesian which contains the minimum components of the supplied Cartesians.\n *\n * @param {Cartesian3} first A cartesian to compare.\n * @param {Cartesian3} second A cartesian to compare.\n * @param {Cartesian3} result The object into which to store the result.\n * @returns {Cartesian3} A cartesian with the minimum components.\n */\nCartesian3.minimumByComponent = function (first, second, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"first\", first);\n  Check.typeOf.object(\"second\", second);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.min(first.x, second.x);\n  result.y = Math.min(first.y, second.y);\n  result.z = Math.min(first.z, second.z);\n\n  return result;\n};\n\n/**\n * Compares two Cartesians and computes a Cartesian which contains the maximum components of the supplied Cartesians.\n *\n * @param {Cartesian3} first A cartesian to compare.\n * @param {Cartesian3} second A cartesian to compare.\n * @param {Cartesian3} result The object into which to store the result.\n * @returns {Cartesian3} A cartesian with the maximum components.\n */\nCartesian3.maximumByComponent = function (first, second, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"first\", first);\n  Check.typeOf.object(\"second\", second);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.max(first.x, second.x);\n  result.y = Math.max(first.y, second.y);\n  result.z = Math.max(first.z, second.z);\n  return result;\n};\n\n/**\n * Computes the provided Cartesian's squared magnitude.\n *\n * @param {Cartesian3} cartesian The Cartesian instance whose squared magnitude is to be computed.\n * @returns {Number} The squared magnitude.\n */\nCartesian3.magnitudeSquared = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return (\n    cartesian.x * cartesian.x +\n    cartesian.y * cartesian.y +\n    cartesian.z * cartesian.z\n  );\n};\n\n/**\n * Computes the Cartesian's magnitude (length).\n *\n * @param {Cartesian3} cartesian The Cartesian instance whose magnitude is to be computed.\n * @returns {Number} The magnitude.\n */\nCartesian3.magnitude = function (cartesian) {\n  return Math.sqrt(Cartesian3.magnitudeSquared(cartesian));\n};\n\nconst distanceScratch = new Cartesian3();\n\n/**\n * Computes the distance between two points.\n *\n * @param {Cartesian3} left The first point to compute the distance from.\n * @param {Cartesian3} right The second point to compute the distance to.\n * @returns {Number} The distance between two points.\n *\n * @example\n * // Returns 1.0\n * const d = Cesium.Cartesian3.distance(new Cesium.Cartesian3(1.0, 0.0, 0.0), new Cesium.Cartesian3(2.0, 0.0, 0.0));\n */\nCartesian3.distance = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  Cartesian3.subtract(left, right, distanceScratch);\n  return Cartesian3.magnitude(distanceScratch);\n};\n\n/**\n * Computes the squared distance between two points.  Comparing squared distances\n * using this function is more efficient than comparing distances using {@link Cartesian3#distance}.\n *\n * @param {Cartesian3} left The first point to compute the distance from.\n * @param {Cartesian3} right The second point to compute the distance to.\n * @returns {Number} The distance between two points.\n *\n * @example\n * // Returns 4.0, not 2.0\n * const d = Cesium.Cartesian3.distanceSquared(new Cesium.Cartesian3(1.0, 0.0, 0.0), new Cesium.Cartesian3(3.0, 0.0, 0.0));\n */\nCartesian3.distanceSquared = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  Cartesian3.subtract(left, right, distanceScratch);\n  return Cartesian3.magnitudeSquared(distanceScratch);\n};\n\n/**\n * Computes the normalized form of the supplied Cartesian.\n *\n * @param {Cartesian3} cartesian The Cartesian to be normalized.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.normalize = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const magnitude = Cartesian3.magnitude(cartesian);\n\n  result.x = cartesian.x / magnitude;\n  result.y = cartesian.y / magnitude;\n  result.z = cartesian.z / magnitude;\n\n  //>>includeStart('debug', pragmas.debug);\n  if (isNaN(result.x) || isNaN(result.y) || isNaN(result.z)) {\n    throw new DeveloperError(\"normalized result is not a number\");\n  }\n  //>>includeEnd('debug');\n\n  return result;\n};\n\n/**\n * Computes the dot (scalar) product of two Cartesians.\n *\n * @param {Cartesian3} left The first Cartesian.\n * @param {Cartesian3} right The second Cartesian.\n * @returns {Number} The dot product.\n */\nCartesian3.dot = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  return left.x * right.x + left.y * right.y + left.z * right.z;\n};\n\n/**\n * Computes the componentwise product of two Cartesians.\n *\n * @param {Cartesian3} left The first Cartesian.\n * @param {Cartesian3} right The second Cartesian.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.multiplyComponents = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x * right.x;\n  result.y = left.y * right.y;\n  result.z = left.z * right.z;\n  return result;\n};\n\n/**\n * Computes the componentwise quotient of two Cartesians.\n *\n * @param {Cartesian3} left The first Cartesian.\n * @param {Cartesian3} right The second Cartesian.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.divideComponents = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x / right.x;\n  result.y = left.y / right.y;\n  result.z = left.z / right.z;\n  return result;\n};\n\n/**\n * Computes the componentwise sum of two Cartesians.\n *\n * @param {Cartesian3} left The first Cartesian.\n * @param {Cartesian3} right The second Cartesian.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.add = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x + right.x;\n  result.y = left.y + right.y;\n  result.z = left.z + right.z;\n  return result;\n};\n\n/**\n * Computes the componentwise difference of two Cartesians.\n *\n * @param {Cartesian3} left The first Cartesian.\n * @param {Cartesian3} right The second Cartesian.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.subtract = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x - right.x;\n  result.y = left.y - right.y;\n  result.z = left.z - right.z;\n  return result;\n};\n\n/**\n * Multiplies the provided Cartesian componentwise by the provided scalar.\n *\n * @param {Cartesian3} cartesian The Cartesian to be scaled.\n * @param {Number} scalar The scalar to multiply with.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.multiplyByScalar = function (cartesian, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = cartesian.x * scalar;\n  result.y = cartesian.y * scalar;\n  result.z = cartesian.z * scalar;\n  return result;\n};\n\n/**\n * Divides the provided Cartesian componentwise by the provided scalar.\n *\n * @param {Cartesian3} cartesian The Cartesian to be divided.\n * @param {Number} scalar The scalar to divide by.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.divideByScalar = function (cartesian, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = cartesian.x / scalar;\n  result.y = cartesian.y / scalar;\n  result.z = cartesian.z / scalar;\n  return result;\n};\n\n/**\n * Negates the provided Cartesian.\n *\n * @param {Cartesian3} cartesian The Cartesian to be negated.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.negate = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = -cartesian.x;\n  result.y = -cartesian.y;\n  result.z = -cartesian.z;\n  return result;\n};\n\n/**\n * Computes the absolute value of the provided Cartesian.\n *\n * @param {Cartesian3} cartesian The Cartesian whose absolute value is to be computed.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.abs = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.abs(cartesian.x);\n  result.y = Math.abs(cartesian.y);\n  result.z = Math.abs(cartesian.z);\n  return result;\n};\n\nconst lerpScratch = new Cartesian3();\n/**\n * Computes the linear interpolation or extrapolation at t using the provided cartesians.\n *\n * @param {Cartesian3} start The value corresponding to t at 0.0.\n * @param {Cartesian3} end The value corresponding to t at 1.0.\n * @param {Number} t The point along t at which to interpolate.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nCartesian3.lerp = function (start, end, t, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"start\", start);\n  Check.typeOf.object(\"end\", end);\n  Check.typeOf.number(\"t\", t);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  Cartesian3.multiplyByScalar(end, t, lerpScratch);\n  result = Cartesian3.multiplyByScalar(start, 1.0 - t, result);\n  return Cartesian3.add(lerpScratch, result, result);\n};\n\nconst angleBetweenScratch = new Cartesian3();\nconst angleBetweenScratch2 = new Cartesian3();\n/**\n * Returns the angle, in radians, between the provided Cartesians.\n *\n * @param {Cartesian3} left The first Cartesian.\n * @param {Cartesian3} right The second Cartesian.\n * @returns {Number} The angle between the Cartesians.\n */\nCartesian3.angleBetween = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  Cartesian3.normalize(left, angleBetweenScratch);\n  Cartesian3.normalize(right, angleBetweenScratch2);\n  const cosine = Cartesian3.dot(angleBetweenScratch, angleBetweenScratch2);\n  const sine = Cartesian3.magnitude(\n    Cartesian3.cross(\n      angleBetweenScratch,\n      angleBetweenScratch2,\n      angleBetweenScratch\n    )\n  );\n  return Math.atan2(sine, cosine);\n};\n\nconst mostOrthogonalAxisScratch = new Cartesian3();\n/**\n * Returns the axis that is most orthogonal to the provided Cartesian.\n *\n * @param {Cartesian3} cartesian The Cartesian on which to find the most orthogonal axis.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The most orthogonal axis.\n */\nCartesian3.mostOrthogonalAxis = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const f = Cartesian3.normalize(cartesian, mostOrthogonalAxisScratch);\n  Cartesian3.abs(f, f);\n\n  if (f.x <= f.y) {\n    if (f.x <= f.z) {\n      result = Cartesian3.clone(Cartesian3.UNIT_X, result);\n    } else {\n      result = Cartesian3.clone(Cartesian3.UNIT_Z, result);\n    }\n  } else if (f.y <= f.z) {\n    result = Cartesian3.clone(Cartesian3.UNIT_Y, result);\n  } else {\n    result = Cartesian3.clone(Cartesian3.UNIT_Z, result);\n  }\n\n  return result;\n};\n\n/**\n * Projects vector a onto vector b\n * @param {Cartesian3} a The vector that needs projecting\n * @param {Cartesian3} b The vector to project onto\n * @param {Cartesian3} result The result cartesian\n * @returns {Cartesian3} The modified result parameter\n */\nCartesian3.projectVector = function (a, b, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"a\", a);\n  Check.defined(\"b\", b);\n  Check.defined(\"result\", result);\n  //>>includeEnd('debug');\n\n  const scalar = Cartesian3.dot(a, b) / Cartesian3.dot(b, b);\n  return Cartesian3.multiplyByScalar(b, scalar, result);\n};\n\n/**\n * Compares the provided Cartesians componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Cartesian3} [left] The first Cartesian.\n * @param {Cartesian3} [right] The second Cartesian.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nCartesian3.equals = function (left, right) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      left.x === right.x &&\n      left.y === right.y &&\n      left.z === right.z)\n  );\n};\n\n/**\n * @private\n */\nCartesian3.equalsArray = function (cartesian, array, offset) {\n  return (\n    cartesian.x === array[offset] &&\n    cartesian.y === array[offset + 1] &&\n    cartesian.z === array[offset + 2]\n  );\n};\n\n/**\n * Compares the provided Cartesians componentwise and returns\n * <code>true</code> if they pass an absolute or relative tolerance test,\n * <code>false</code> otherwise.\n *\n * @param {Cartesian3} [left] The first Cartesian.\n * @param {Cartesian3} [right] The second Cartesian.\n * @param {Number} [relativeEpsilon=0] The relative epsilon tolerance to use for equality testing.\n * @param {Number} [absoluteEpsilon=relativeEpsilon] The absolute epsilon tolerance to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n */\nCartesian3.equalsEpsilon = function (\n  left,\n  right,\n  relativeEpsilon,\n  absoluteEpsilon\n) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      CesiumMath.equalsEpsilon(\n        left.x,\n        right.x,\n        relativeEpsilon,\n        absoluteEpsilon\n      ) &&\n      CesiumMath.equalsEpsilon(\n        left.y,\n        right.y,\n        relativeEpsilon,\n        absoluteEpsilon\n      ) &&\n      CesiumMath.equalsEpsilon(\n        left.z,\n        right.z,\n        relativeEpsilon,\n        absoluteEpsilon\n      ))\n  );\n};\n\n/**\n * Computes the cross (outer) product of two Cartesians.\n *\n * @param {Cartesian3} left The first Cartesian.\n * @param {Cartesian3} right The second Cartesian.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The cross product.\n */\nCartesian3.cross = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const leftX = left.x;\n  const leftY = left.y;\n  const leftZ = left.z;\n  const rightX = right.x;\n  const rightY = right.y;\n  const rightZ = right.z;\n\n  const x = leftY * rightZ - leftZ * rightY;\n  const y = leftZ * rightX - leftX * rightZ;\n  const z = leftX * rightY - leftY * rightX;\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return result;\n};\n\n/**\n * Computes the midpoint between the right and left Cartesian.\n * @param {Cartesian3} left The first Cartesian.\n * @param {Cartesian3} right The second Cartesian.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The midpoint.\n */\nCartesian3.midpoint = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = (left.x + right.x) * 0.5;\n  result.y = (left.y + right.y) * 0.5;\n  result.z = (left.z + right.z) * 0.5;\n\n  return result;\n};\n\n/**\n * Returns a Cartesian3 position from longitude and latitude values given in degrees.\n *\n * @param {Number} longitude The longitude, in degrees\n * @param {Number} latitude The latitude, in degrees\n * @param {Number} [height=0.0] The height, in meters, above the ellipsoid.\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the position lies.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The position\n *\n * @example\n * const position = Cesium.Cartesian3.fromDegrees(-115.0, 37.0);\n */\nCartesian3.fromDegrees = function (\n  longitude,\n  latitude,\n  height,\n  ellipsoid,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"longitude\", longitude);\n  Check.typeOf.number(\"latitude\", latitude);\n  //>>includeEnd('debug');\n\n  longitude = CesiumMath.toRadians(longitude);\n  latitude = CesiumMath.toRadians(latitude);\n  return Cartesian3.fromRadians(longitude, latitude, height, ellipsoid, result);\n};\n\nlet scratchN = new Cartesian3();\nlet scratchK = new Cartesian3();\nconst wgs84RadiiSquared = new Cartesian3(\n  6378137.0 * 6378137.0,\n  6378137.0 * 6378137.0,\n  6356752.3142451793 * 6356752.3142451793\n);\n\n/**\n * Returns a Cartesian3 position from longitude and latitude values given in radians.\n *\n * @param {Number} longitude The longitude, in radians\n * @param {Number} latitude The latitude, in radians\n * @param {Number} [height=0.0] The height, in meters, above the ellipsoid.\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the position lies.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The position\n *\n * @example\n * const position = Cesium.Cartesian3.fromRadians(-2.007, 0.645);\n */\nCartesian3.fromRadians = function (\n  longitude,\n  latitude,\n  height,\n  ellipsoid,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"longitude\", longitude);\n  Check.typeOf.number(\"latitude\", latitude);\n  //>>includeEnd('debug');\n\n  height = defaultValue(height, 0.0);\n  const radiiSquared = defined(ellipsoid)\n    ? ellipsoid.radiiSquared\n    : wgs84RadiiSquared;\n\n  const cosLatitude = Math.cos(latitude);\n  scratchN.x = cosLatitude * Math.cos(longitude);\n  scratchN.y = cosLatitude * Math.sin(longitude);\n  scratchN.z = Math.sin(latitude);\n  scratchN = Cartesian3.normalize(scratchN, scratchN);\n\n  Cartesian3.multiplyComponents(radiiSquared, scratchN, scratchK);\n  const gamma = Math.sqrt(Cartesian3.dot(scratchN, scratchK));\n  scratchK = Cartesian3.divideByScalar(scratchK, gamma, scratchK);\n  scratchN = Cartesian3.multiplyByScalar(scratchN, height, scratchN);\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n  return Cartesian3.add(scratchK, scratchN, result);\n};\n\n/**\n * Returns an array of Cartesian3 positions given an array of longitude and latitude values given in degrees.\n *\n * @param {Number[]} coordinates A list of longitude and latitude values. Values alternate [longitude, latitude, longitude, latitude...].\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the coordinates lie.\n * @param {Cartesian3[]} [result] An array of Cartesian3 objects to store the result.\n * @returns {Cartesian3[]} The array of positions.\n *\n * @example\n * const positions = Cesium.Cartesian3.fromDegreesArray([-115.0, 37.0, -107.0, 33.0]);\n */\nCartesian3.fromDegreesArray = function (coordinates, ellipsoid, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"coordinates\", coordinates);\n  if (coordinates.length < 2 || coordinates.length % 2 !== 0) {\n    throw new DeveloperError(\n      \"the number of coordinates must be a multiple of 2 and at least 2\"\n    );\n  }\n  //>>includeEnd('debug');\n\n  const length = coordinates.length;\n  if (!defined(result)) {\n    result = new Array(length / 2);\n  } else {\n    result.length = length / 2;\n  }\n\n  for (let i = 0; i < length; i += 2) {\n    const longitude = coordinates[i];\n    const latitude = coordinates[i + 1];\n    const index = i / 2;\n    result[index] = Cartesian3.fromDegrees(\n      longitude,\n      latitude,\n      0,\n      ellipsoid,\n      result[index]\n    );\n  }\n\n  return result;\n};\n\n/**\n * Returns an array of Cartesian3 positions given an array of longitude and latitude values given in radians.\n *\n * @param {Number[]} coordinates A list of longitude and latitude values. Values alternate [longitude, latitude, longitude, latitude...].\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the coordinates lie.\n * @param {Cartesian3[]} [result] An array of Cartesian3 objects to store the result.\n * @returns {Cartesian3[]} The array of positions.\n *\n * @example\n * const positions = Cesium.Cartesian3.fromRadiansArray([-2.007, 0.645, -1.867, .575]);\n */\nCartesian3.fromRadiansArray = function (coordinates, ellipsoid, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"coordinates\", coordinates);\n  if (coordinates.length < 2 || coordinates.length % 2 !== 0) {\n    throw new DeveloperError(\n      \"the number of coordinates must be a multiple of 2 and at least 2\"\n    );\n  }\n  //>>includeEnd('debug');\n\n  const length = coordinates.length;\n  if (!defined(result)) {\n    result = new Array(length / 2);\n  } else {\n    result.length = length / 2;\n  }\n\n  for (let i = 0; i < length; i += 2) {\n    const longitude = coordinates[i];\n    const latitude = coordinates[i + 1];\n    const index = i / 2;\n    result[index] = Cartesian3.fromRadians(\n      longitude,\n      latitude,\n      0,\n      ellipsoid,\n      result[index]\n    );\n  }\n\n  return result;\n};\n\n/**\n * Returns an array of Cartesian3 positions given an array of longitude, latitude and height values where longitude and latitude are given in degrees.\n *\n * @param {Number[]} coordinates A list of longitude, latitude and height values. Values alternate [longitude, latitude, height, longitude, latitude, height...].\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the position lies.\n * @param {Cartesian3[]} [result] An array of Cartesian3 objects to store the result.\n * @returns {Cartesian3[]} The array of positions.\n *\n * @example\n * const positions = Cesium.Cartesian3.fromDegreesArrayHeights([-115.0, 37.0, 100000.0, -107.0, 33.0, 150000.0]);\n */\nCartesian3.fromDegreesArrayHeights = function (coordinates, ellipsoid, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"coordinates\", coordinates);\n  if (coordinates.length < 3 || coordinates.length % 3 !== 0) {\n    throw new DeveloperError(\n      \"the number of coordinates must be a multiple of 3 and at least 3\"\n    );\n  }\n  //>>includeEnd('debug');\n\n  const length = coordinates.length;\n  if (!defined(result)) {\n    result = new Array(length / 3);\n  } else {\n    result.length = length / 3;\n  }\n\n  for (let i = 0; i < length; i += 3) {\n    const longitude = coordinates[i];\n    const latitude = coordinates[i + 1];\n    const height = coordinates[i + 2];\n    const index = i / 3;\n    result[index] = Cartesian3.fromDegrees(\n      longitude,\n      latitude,\n      height,\n      ellipsoid,\n      result[index]\n    );\n  }\n\n  return result;\n};\n\n/**\n * Returns an array of Cartesian3 positions given an array of longitude, latitude and height values where longitude and latitude are given in radians.\n *\n * @param {Number[]} coordinates A list of longitude, latitude and height values. Values alternate [longitude, latitude, height, longitude, latitude, height...].\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the position lies.\n * @param {Cartesian3[]} [result] An array of Cartesian3 objects to store the result.\n * @returns {Cartesian3[]} The array of positions.\n *\n * @example\n * const positions = Cesium.Cartesian3.fromRadiansArrayHeights([-2.007, 0.645, 100000.0, -1.867, .575, 150000.0]);\n */\nCartesian3.fromRadiansArrayHeights = function (coordinates, ellipsoid, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"coordinates\", coordinates);\n  if (coordinates.length < 3 || coordinates.length % 3 !== 0) {\n    throw new DeveloperError(\n      \"the number of coordinates must be a multiple of 3 and at least 3\"\n    );\n  }\n  //>>includeEnd('debug');\n\n  const length = coordinates.length;\n  if (!defined(result)) {\n    result = new Array(length / 3);\n  } else {\n    result.length = length / 3;\n  }\n\n  for (let i = 0; i < length; i += 3) {\n    const longitude = coordinates[i];\n    const latitude = coordinates[i + 1];\n    const height = coordinates[i + 2];\n    const index = i / 3;\n    result[index] = Cartesian3.fromRadians(\n      longitude,\n      latitude,\n      height,\n      ellipsoid,\n      result[index]\n    );\n  }\n\n  return result;\n};\n\n/**\n * An immutable Cartesian3 instance initialized to (0.0, 0.0, 0.0).\n *\n * @type {Cartesian3}\n * @constant\n */\nCartesian3.ZERO = Object.freeze(new Cartesian3(0.0, 0.0, 0.0));\n\n/**\n * An immutable Cartesian3 instance initialized to (1.0, 1.0, 1.0).\n *\n * @type {Cartesian3}\n * @constant\n */\nCartesian3.ONE = Object.freeze(new Cartesian3(1.0, 1.0, 1.0));\n\n/**\n * An immutable Cartesian3 instance initialized to (1.0, 0.0, 0.0).\n *\n * @type {Cartesian3}\n * @constant\n */\nCartesian3.UNIT_X = Object.freeze(new Cartesian3(1.0, 0.0, 0.0));\n\n/**\n * An immutable Cartesian3 instance initialized to (0.0, 1.0, 0.0).\n *\n * @type {Cartesian3}\n * @constant\n */\nCartesian3.UNIT_Y = Object.freeze(new Cartesian3(0.0, 1.0, 0.0));\n\n/**\n * An immutable Cartesian3 instance initialized to (0.0, 0.0, 1.0).\n *\n * @type {Cartesian3}\n * @constant\n */\nCartesian3.UNIT_Z = Object.freeze(new Cartesian3(0.0, 0.0, 1.0));\n\n/**\n * Duplicates this Cartesian3 instance.\n *\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if one was not provided.\n */\nCartesian3.prototype.clone = function (result) {\n  return Cartesian3.clone(this, result);\n};\n\n/**\n * Compares this Cartesian against the provided Cartesian componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Cartesian3} [right] The right hand side Cartesian.\n * @returns {Boolean} <code>true</code> if they are equal, <code>false</code> otherwise.\n */\nCartesian3.prototype.equals = function (right) {\n  return Cartesian3.equals(this, right);\n};\n\n/**\n * Compares this Cartesian against the provided Cartesian componentwise and returns\n * <code>true</code> if they pass an absolute or relative tolerance test,\n * <code>false</code> otherwise.\n *\n * @param {Cartesian3} [right] The right hand side Cartesian.\n * @param {Number} [relativeEpsilon=0] The relative epsilon tolerance to use for equality testing.\n * @param {Number} [absoluteEpsilon=relativeEpsilon] The absolute epsilon tolerance to use for equality testing.\n * @returns {Boolean} <code>true</code> if they are within the provided epsilon, <code>false</code> otherwise.\n */\nCartesian3.prototype.equalsEpsilon = function (\n  right,\n  relativeEpsilon,\n  absoluteEpsilon\n) {\n  return Cartesian3.equalsEpsilon(\n    this,\n    right,\n    relativeEpsilon,\n    absoluteEpsilon\n  );\n};\n\n/**\n * Creates a string representing this Cartesian in the format '(x, y, z)'.\n *\n * @returns {String} A string representing this Cartesian in the format '(x, y, z)'.\n */\nCartesian3.prototype.toString = function () {\n  return \"(\" + this.x + \", \" + this.y + \", \" + this.z + \")\";\n};\nexport default Cartesian3;\n", "import Cartesian3 from \"./Cartesian3.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\n\nconst scaleToGeodeticSurfaceIntersection = new Cartesian3();\nconst scaleToGeodeticSurfaceGradient = new Cartesian3();\n\n/**\n * Scales the provided Cartesian position along the geodetic surface normal\n * so that it is on the surface of this ellipsoid.  If the position is\n * at the center of the ellipsoid, this function returns undefined.\n *\n * @param {Cartesian3} cartesian The Cartesian position to scale.\n * @param {Cartesian3} oneOverRadii One over radii of the ellipsoid.\n * @param {Cartesian3} oneOverRadiiSquared One over radii squared of the ellipsoid.\n * @param {Number} centerToleranceSquared Tolerance for closeness to the center.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter, a new Cartesian3 instance if none was provided, or undefined if the position is at the center.\n *\n * @function scaleToGeodeticSurface\n *\n * @private\n */\nfunction scaleToGeodeticSurface(\n  cartesian,\n  oneOverRadii,\n  oneOverRadiiSquared,\n  centerToleranceSquared,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(cartesian)) {\n    throw new DeveloperError(\"cartesian is required.\");\n  }\n  if (!defined(oneOverRadii)) {\n    throw new DeveloperError(\"oneOverRadii is required.\");\n  }\n  if (!defined(oneOverRadiiSquared)) {\n    throw new DeveloperError(\"oneOverRadiiSquared is required.\");\n  }\n  if (!defined(centerToleranceSquared)) {\n    throw new DeveloperError(\"centerToleranceSquared is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const positionX = cartesian.x;\n  const positionY = cartesian.y;\n  const positionZ = cartesian.z;\n\n  const oneOverRadiiX = oneOverRadii.x;\n  const oneOverRadiiY = oneOverRadii.y;\n  const oneOverRadiiZ = oneOverRadii.z;\n\n  const x2 = positionX * positionX * oneOverRadiiX * oneOverRadiiX;\n  const y2 = positionY * positionY * oneOverRadiiY * oneOverRadiiY;\n  const z2 = positionZ * positionZ * oneOverRadiiZ * oneOverRadiiZ;\n\n  // Compute the squared ellipsoid norm.\n  const squaredNorm = x2 + y2 + z2;\n  const ratio = Math.sqrt(1.0 / squaredNorm);\n\n  // As an initial approximation, assume that the radial intersection is the projection point.\n  const intersection = Cartesian3.multiplyByScalar(\n    cartesian,\n    ratio,\n    scaleToGeodeticSurfaceIntersection\n  );\n\n  // If the position is near the center, the iteration will not converge.\n  if (squaredNorm < centerToleranceSquared) {\n    return !isFinite(ratio)\n      ? undefined\n      : Cartesian3.clone(intersection, result);\n  }\n\n  const oneOverRadiiSquaredX = oneOverRadiiSquared.x;\n  const oneOverRadiiSquaredY = oneOverRadiiSquared.y;\n  const oneOverRadiiSquaredZ = oneOverRadiiSquared.z;\n\n  // Use the gradient at the intersection point in place of the true unit normal.\n  // The difference in magnitude will be absorbed in the multiplier.\n  const gradient = scaleToGeodeticSurfaceGradient;\n  gradient.x = intersection.x * oneOverRadiiSquaredX * 2.0;\n  gradient.y = intersection.y * oneOverRadiiSquaredY * 2.0;\n  gradient.z = intersection.z * oneOverRadiiSquaredZ * 2.0;\n\n  // Compute the initial guess at the normal vector multiplier, lambda.\n  let lambda =\n    ((1.0 - ratio) * Cartesian3.magnitude(cartesian)) /\n    (0.5 * Cartesian3.magnitude(gradient));\n  let correction = 0.0;\n\n  let func;\n  let denominator;\n  let xMultiplier;\n  let yMultiplier;\n  let zMultiplier;\n  let xMultiplier2;\n  let yMultiplier2;\n  let zMultiplier2;\n  let xMultiplier3;\n  let yMultiplier3;\n  let zMultiplier3;\n\n  do {\n    lambda -= correction;\n\n    xMultiplier = 1.0 / (1.0 + lambda * oneOverRadiiSquaredX);\n    yMultiplier = 1.0 / (1.0 + lambda * oneOverRadiiSquaredY);\n    zMultiplier = 1.0 / (1.0 + lambda * oneOverRadiiSquaredZ);\n\n    xMultiplier2 = xMultiplier * xMultiplier;\n    yMultiplier2 = yMultiplier * yMultiplier;\n    zMultiplier2 = zMultiplier * zMultiplier;\n\n    xMultiplier3 = xMultiplier2 * xMultiplier;\n    yMultiplier3 = yMultiplier2 * yMultiplier;\n    zMultiplier3 = zMultiplier2 * zMultiplier;\n\n    func = x2 * xMultiplier2 + y2 * yMultiplier2 + z2 * zMultiplier2 - 1.0;\n\n    // \"denominator\" here refers to the use of this expression in the velocity and acceleration\n    // computations in the sections to follow.\n    denominator =\n      x2 * xMultiplier3 * oneOverRadiiSquaredX +\n      y2 * yMultiplier3 * oneOverRadiiSquaredY +\n      z2 * zMultiplier3 * oneOverRadiiSquaredZ;\n\n    const derivative = -2.0 * denominator;\n\n    correction = func / derivative;\n  } while (Math.abs(func) > CesiumMath.EPSILON12);\n\n  if (!defined(result)) {\n    return new Cartesian3(\n      positionX * xMultiplier,\n      positionY * yMultiplier,\n      positionZ * zMultiplier\n    );\n  }\n  result.x = positionX * xMultiplier;\n  result.y = positionY * yMultiplier;\n  result.z = positionZ * zMultiplier;\n  return result;\n}\nexport default scaleToGeodeticSurface;\n", "import Cartesian3 from \"./Cartesian3.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport CesiumMath from \"./Math.js\";\nimport scaleToGeodeticSurface from \"./scaleToGeodeticSurface.js\";\n\n/**\n * A position defined by longitude, latitude, and height.\n * @alias Cartographic\n * @constructor\n *\n * @param {Number} [longitude=0.0] The longitude, in radians.\n * @param {Number} [latitude=0.0] The latitude, in radians.\n * @param {Number} [height=0.0] The height, in meters, above the ellipsoid.\n *\n * @see Ellipsoid\n */\nfunction Cartographic(longitude, latitude, height) {\n  /**\n   * The longitude, in radians.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.longitude = defaultValue(longitude, 0.0);\n\n  /**\n   * The latitude, in radians.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.latitude = defaultValue(latitude, 0.0);\n\n  /**\n   * The height, in meters, above the ellipsoid.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.height = defaultValue(height, 0.0);\n}\n\n/**\n * Creates a new Cartographic instance from longitude and latitude\n * specified in radians.\n *\n * @param {Number} longitude The longitude, in radians.\n * @param {Number} latitude The latitude, in radians.\n * @param {Number} [height=0.0] The height, in meters, above the ellipsoid.\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if one was not provided.\n */\nCartographic.fromRadians = function (longitude, latitude, height, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"longitude\", longitude);\n  Check.typeOf.number(\"latitude\", latitude);\n  //>>includeEnd('debug');\n\n  height = defaultValue(height, 0.0);\n\n  if (!defined(result)) {\n    return new Cartographic(longitude, latitude, height);\n  }\n\n  result.longitude = longitude;\n  result.latitude = latitude;\n  result.height = height;\n  return result;\n};\n\n/**\n * Creates a new Cartographic instance from longitude and latitude\n * specified in degrees.  The values in the resulting object will\n * be in radians.\n *\n * @param {Number} longitude The longitude, in degrees.\n * @param {Number} latitude The latitude, in degrees.\n * @param {Number} [height=0.0] The height, in meters, above the ellipsoid.\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if one was not provided.\n */\nCartographic.fromDegrees = function (longitude, latitude, height, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"longitude\", longitude);\n  Check.typeOf.number(\"latitude\", latitude);\n  //>>includeEnd('debug');\n  longitude = CesiumMath.toRadians(longitude);\n  latitude = CesiumMath.toRadians(latitude);\n\n  return Cartographic.fromRadians(longitude, latitude, height, result);\n};\n\nconst cartesianToCartographicN = new Cartesian3();\nconst cartesianToCartographicP = new Cartesian3();\nconst cartesianToCartographicH = new Cartesian3();\nconst wgs84OneOverRadii = new Cartesian3(\n  1.0 / 6378137.0,\n  1.0 / 6378137.0,\n  1.0 / 6356752.3142451793\n);\nconst wgs84OneOverRadiiSquared = new Cartesian3(\n  1.0 / (6378137.0 * 6378137.0),\n  1.0 / (6378137.0 * 6378137.0),\n  1.0 / (6356752.3142451793 * 6356752.3142451793)\n);\nconst wgs84CenterToleranceSquared = CesiumMath.EPSILON1;\n\n/**\n * Creates a new Cartographic instance from a Cartesian position. The values in the\n * resulting object will be in radians.\n *\n * @param {Cartesian3} cartesian The Cartesian position to convert to cartographic representation.\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the position lies.\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter, new Cartographic instance if none was provided, or undefined if the cartesian is at the center of the ellipsoid.\n */\nCartographic.fromCartesian = function (cartesian, ellipsoid, result) {\n  const oneOverRadii = defined(ellipsoid)\n    ? ellipsoid.oneOverRadii\n    : wgs84OneOverRadii;\n  const oneOverRadiiSquared = defined(ellipsoid)\n    ? ellipsoid.oneOverRadiiSquared\n    : wgs84OneOverRadiiSquared;\n  const centerToleranceSquared = defined(ellipsoid)\n    ? ellipsoid._centerToleranceSquared\n    : wgs84CenterToleranceSquared;\n\n  //`cartesian is required.` is thrown from scaleToGeodeticSurface\n  const p = scaleToGeodeticSurface(\n    cartesian,\n    oneOverRadii,\n    oneOverRadiiSquared,\n    centerToleranceSquared,\n    cartesianToCartographicP\n  );\n\n  if (!defined(p)) {\n    return undefined;\n  }\n\n  let n = Cartesian3.multiplyComponents(\n    p,\n    oneOverRadiiSquared,\n    cartesianToCartographicN\n  );\n  n = Cartesian3.normalize(n, n);\n\n  const h = Cartesian3.subtract(cartesian, p, cartesianToCartographicH);\n\n  const longitude = Math.atan2(n.y, n.x);\n  const latitude = Math.asin(n.z);\n  const height =\n    CesiumMath.sign(Cartesian3.dot(h, cartesian)) * Cartesian3.magnitude(h);\n\n  if (!defined(result)) {\n    return new Cartographic(longitude, latitude, height);\n  }\n  result.longitude = longitude;\n  result.latitude = latitude;\n  result.height = height;\n  return result;\n};\n\n/**\n * Creates a new Cartesian3 instance from a Cartographic input. The values in the inputted\n * object should be in radians.\n *\n * @param {Cartographic} cartographic Input to be converted into a Cartesian3 output.\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the position lies.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The position\n */\nCartographic.toCartesian = function (cartographic, ellipsoid, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"cartographic\", cartographic);\n  //>>includeEnd('debug');\n\n  return Cartesian3.fromRadians(\n    cartographic.longitude,\n    cartographic.latitude,\n    cartographic.height,\n    ellipsoid,\n    result\n  );\n};\n\n/**\n * Duplicates a Cartographic instance.\n *\n * @param {Cartographic} cartographic The cartographic to duplicate.\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if one was not provided. (Returns undefined if cartographic is undefined)\n */\nCartographic.clone = function (cartographic, result) {\n  if (!defined(cartographic)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    return new Cartographic(\n      cartographic.longitude,\n      cartographic.latitude,\n      cartographic.height\n    );\n  }\n  result.longitude = cartographic.longitude;\n  result.latitude = cartographic.latitude;\n  result.height = cartographic.height;\n  return result;\n};\n\n/**\n * Compares the provided cartographics componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Cartographic} [left] The first cartographic.\n * @param {Cartographic} [right] The second cartographic.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nCartographic.equals = function (left, right) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      left.longitude === right.longitude &&\n      left.latitude === right.latitude &&\n      left.height === right.height)\n  );\n};\n\n/**\n * Compares the provided cartographics componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Cartographic} [left] The first cartographic.\n * @param {Cartographic} [right] The second cartographic.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n */\nCartographic.equalsEpsilon = function (left, right, epsilon) {\n  epsilon = defaultValue(epsilon, 0);\n\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      Math.abs(left.longitude - right.longitude) <= epsilon &&\n      Math.abs(left.latitude - right.latitude) <= epsilon &&\n      Math.abs(left.height - right.height) <= epsilon)\n  );\n};\n\n/**\n * An immutable Cartographic instance initialized to (0.0, 0.0, 0.0).\n *\n * @type {Cartographic}\n * @constant\n */\nCartographic.ZERO = Object.freeze(new Cartographic(0.0, 0.0, 0.0));\n\n/**\n * Duplicates this instance.\n *\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if one was not provided.\n */\nCartographic.prototype.clone = function (result) {\n  return Cartographic.clone(this, result);\n};\n\n/**\n * Compares the provided against this cartographic componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Cartographic} [right] The second cartographic.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nCartographic.prototype.equals = function (right) {\n  return Cartographic.equals(this, right);\n};\n\n/**\n * Compares the provided against this cartographic componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Cartographic} [right] The second cartographic.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n */\nCartographic.prototype.equalsEpsilon = function (right, epsilon) {\n  return Cartographic.equalsEpsilon(this, right, epsilon);\n};\n\n/**\n * Creates a string representing this cartographic in the format '(longitude, latitude, height)'.\n *\n * @returns {String} A string representing the provided cartographic in the format '(longitude, latitude, height)'.\n */\nCartographic.prototype.toString = function () {\n  return \"(\" + this.longitude + \", \" + this.latitude + \", \" + this.height + \")\";\n};\nexport default Cartographic;\n", "import Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\nimport scaleToGeodeticSurface from \"./scaleToGeodeticSurface.js\";\n\nfunction initialize(ellipsoid, x, y, z) {\n  x = defaultValue(x, 0.0);\n  y = defaultValue(y, 0.0);\n  z = defaultValue(z, 0.0);\n\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number.greaterThanOrEquals(\"x\", x, 0.0);\n  Check.typeOf.number.greaterThanOrEquals(\"y\", y, 0.0);\n  Check.typeOf.number.greaterThanOrEquals(\"z\", z, 0.0);\n  //>>includeEnd('debug');\n\n  ellipsoid._radii = new Cartesian3(x, y, z);\n\n  ellipsoid._radiiSquared = new Cartesian3(x * x, y * y, z * z);\n\n  ellipsoid._radiiToTheFourth = new Cartesian3(\n    x * x * x * x,\n    y * y * y * y,\n    z * z * z * z\n  );\n\n  ellipsoid._oneOverRadii = new Cartesian3(\n    x === 0.0 ? 0.0 : 1.0 / x,\n    y === 0.0 ? 0.0 : 1.0 / y,\n    z === 0.0 ? 0.0 : 1.0 / z\n  );\n\n  ellipsoid._oneOverRadiiSquared = new Cartesian3(\n    x === 0.0 ? 0.0 : 1.0 / (x * x),\n    y === 0.0 ? 0.0 : 1.0 / (y * y),\n    z === 0.0 ? 0.0 : 1.0 / (z * z)\n  );\n\n  ellipsoid._minimumRadius = Math.min(x, y, z);\n\n  ellipsoid._maximumRadius = Math.max(x, y, z);\n\n  ellipsoid._centerToleranceSquared = CesiumMath.EPSILON1;\n\n  if (ellipsoid._radiiSquared.z !== 0) {\n    ellipsoid._squaredXOverSquaredZ =\n      ellipsoid._radiiSquared.x / ellipsoid._radiiSquared.z;\n  }\n}\n\n/**\n * A quadratic surface defined in Cartesian coordinates by the equation\n * <code>(x / a)^2 + (y / b)^2 + (z / c)^2 = 1</code>.  Primarily used\n * by Cesium to represent the shape of planetary bodies.\n *\n * Rather than constructing this object directly, one of the provided\n * constants is normally used.\n * @alias Ellipsoid\n * @constructor\n *\n * @param {Number} [x=0] The radius in the x direction.\n * @param {Number} [y=0] The radius in the y direction.\n * @param {Number} [z=0] The radius in the z direction.\n *\n * @exception {DeveloperError} All radii components must be greater than or equal to zero.\n *\n * @see Ellipsoid.fromCartesian3\n * @see Ellipsoid.WGS84\n * @see Ellipsoid.UNIT_SPHERE\n */\nfunction Ellipsoid(x, y, z) {\n  this._radii = undefined;\n  this._radiiSquared = undefined;\n  this._radiiToTheFourth = undefined;\n  this._oneOverRadii = undefined;\n  this._oneOverRadiiSquared = undefined;\n  this._minimumRadius = undefined;\n  this._maximumRadius = undefined;\n  this._centerToleranceSquared = undefined;\n  this._squaredXOverSquaredZ = undefined;\n\n  initialize(this, x, y, z);\n}\n\nObject.defineProperties(Ellipsoid.prototype, {\n  /**\n   * Gets the radii of the ellipsoid.\n   * @memberof Ellipsoid.prototype\n   * @type {Cartesian3}\n   * @readonly\n   */\n  radii: {\n    get: function () {\n      return this._radii;\n    },\n  },\n  /**\n   * Gets the squared radii of the ellipsoid.\n   * @memberof Ellipsoid.prototype\n   * @type {Cartesian3}\n   * @readonly\n   */\n  radiiSquared: {\n    get: function () {\n      return this._radiiSquared;\n    },\n  },\n  /**\n   * Gets the radii of the ellipsoid raise to the fourth power.\n   * @memberof Ellipsoid.prototype\n   * @type {Cartesian3}\n   * @readonly\n   */\n  radiiToTheFourth: {\n    get: function () {\n      return this._radiiToTheFourth;\n    },\n  },\n  /**\n   * Gets one over the radii of the ellipsoid.\n   * @memberof Ellipsoid.prototype\n   * @type {Cartesian3}\n   * @readonly\n   */\n  oneOverRadii: {\n    get: function () {\n      return this._oneOverRadii;\n    },\n  },\n  /**\n   * Gets one over the squared radii of the ellipsoid.\n   * @memberof Ellipsoid.prototype\n   * @type {Cartesian3}\n   * @readonly\n   */\n  oneOverRadiiSquared: {\n    get: function () {\n      return this._oneOverRadiiSquared;\n    },\n  },\n  /**\n   * Gets the minimum radius of the ellipsoid.\n   * @memberof Ellipsoid.prototype\n   * @type {Number}\n   * @readonly\n   */\n  minimumRadius: {\n    get: function () {\n      return this._minimumRadius;\n    },\n  },\n  /**\n   * Gets the maximum radius of the ellipsoid.\n   * @memberof Ellipsoid.prototype\n   * @type {Number}\n   * @readonly\n   */\n  maximumRadius: {\n    get: function () {\n      return this._maximumRadius;\n    },\n  },\n});\n\n/**\n * Duplicates an Ellipsoid instance.\n *\n * @param {Ellipsoid} ellipsoid The ellipsoid to duplicate.\n * @param {Ellipsoid} [result] The object onto which to store the result, or undefined if a new\n *                    instance should be created.\n * @returns {Ellipsoid} The cloned Ellipsoid. (Returns undefined if ellipsoid is undefined)\n */\nEllipsoid.clone = function (ellipsoid, result) {\n  if (!defined(ellipsoid)) {\n    return undefined;\n  }\n  const radii = ellipsoid._radii;\n\n  if (!defined(result)) {\n    return new Ellipsoid(radii.x, radii.y, radii.z);\n  }\n\n  Cartesian3.clone(radii, result._radii);\n  Cartesian3.clone(ellipsoid._radiiSquared, result._radiiSquared);\n  Cartesian3.clone(ellipsoid._radiiToTheFourth, result._radiiToTheFourth);\n  Cartesian3.clone(ellipsoid._oneOverRadii, result._oneOverRadii);\n  Cartesian3.clone(ellipsoid._oneOverRadiiSquared, result._oneOverRadiiSquared);\n  result._minimumRadius = ellipsoid._minimumRadius;\n  result._maximumRadius = ellipsoid._maximumRadius;\n  result._centerToleranceSquared = ellipsoid._centerToleranceSquared;\n\n  return result;\n};\n\n/**\n * Computes an Ellipsoid from a Cartesian specifying the radii in x, y, and z directions.\n *\n * @param {Cartesian3} [cartesian=Cartesian3.ZERO] The ellipsoid's radius in the x, y, and z directions.\n * @param {Ellipsoid} [result] The object onto which to store the result, or undefined if a new\n *                    instance should be created.\n * @returns {Ellipsoid} A new Ellipsoid instance.\n *\n * @exception {DeveloperError} All radii components must be greater than or equal to zero.\n *\n * @see Ellipsoid.WGS84\n * @see Ellipsoid.UNIT_SPHERE\n */\nEllipsoid.fromCartesian3 = function (cartesian, result) {\n  if (!defined(result)) {\n    result = new Ellipsoid();\n  }\n\n  if (!defined(cartesian)) {\n    return result;\n  }\n\n  initialize(result, cartesian.x, cartesian.y, cartesian.z);\n  return result;\n};\n\n/**\n * An Ellipsoid instance initialized to the WGS84 standard.\n *\n * @type {Ellipsoid}\n * @constant\n */\nEllipsoid.WGS84 = Object.freeze(\n  new Ellipsoid(6378137.0, 6378137.0, 6356752.3142451793)\n);\n\n/**\n * An Ellipsoid instance initialized to radii of (1.0, 1.0, 1.0).\n *\n * @type {Ellipsoid}\n * @constant\n */\nEllipsoid.UNIT_SPHERE = Object.freeze(new Ellipsoid(1.0, 1.0, 1.0));\n\n/**\n * An Ellipsoid instance initialized to a sphere with the lunar radius.\n *\n * @type {Ellipsoid}\n * @constant\n */\nEllipsoid.MOON = Object.freeze(\n  new Ellipsoid(\n    CesiumMath.LUNAR_RADIUS,\n    CesiumMath.LUNAR_RADIUS,\n    CesiumMath.LUNAR_RADIUS\n  )\n);\n\n/**\n * Duplicates an Ellipsoid instance.\n *\n * @param {Ellipsoid} [result] The object onto which to store the result, or undefined if a new\n *                    instance should be created.\n * @returns {Ellipsoid} The cloned Ellipsoid.\n */\nEllipsoid.prototype.clone = function (result) {\n  return Ellipsoid.clone(this, result);\n};\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nEllipsoid.packedLength = Cartesian3.packedLength;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {Ellipsoid} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nEllipsoid.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  Cartesian3.pack(value._radii, array, startingIndex);\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {Ellipsoid} [result] The object into which to store the result.\n * @returns {Ellipsoid} The modified result parameter or a new Ellipsoid instance if one was not provided.\n */\nEllipsoid.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  const radii = Cartesian3.unpack(array, startingIndex);\n  return Ellipsoid.fromCartesian3(radii, result);\n};\n\n/**\n * Computes the unit vector directed from the center of this ellipsoid toward the provided Cartesian position.\n * @function\n *\n * @param {Cartesian3} cartesian The Cartesian for which to to determine the geocentric normal.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if none was provided.\n */\nEllipsoid.prototype.geocentricSurfaceNormal = Cartesian3.normalize;\n\n/**\n * Computes the normal of the plane tangent to the surface of the ellipsoid at the provided position.\n *\n * @param {Cartographic} cartographic The cartographic position for which to to determine the geodetic normal.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if none was provided.\n */\nEllipsoid.prototype.geodeticSurfaceNormalCartographic = function (\n  cartographic,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartographic\", cartographic);\n  //>>includeEnd('debug');\n\n  const longitude = cartographic.longitude;\n  const latitude = cartographic.latitude;\n  const cosLatitude = Math.cos(latitude);\n\n  const x = cosLatitude * Math.cos(longitude);\n  const y = cosLatitude * Math.sin(longitude);\n  const z = Math.sin(latitude);\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return Cartesian3.normalize(result, result);\n};\n\n/**\n * Computes the normal of the plane tangent to the surface of the ellipsoid at the provided position.\n *\n * @param {Cartesian3} cartesian The Cartesian position for which to to determine the surface normal.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if none was provided, or undefined if a normal cannot be found.\n */\nEllipsoid.prototype.geodeticSurfaceNormal = function (cartesian, result) {\n  if (\n    Cartesian3.equalsEpsilon(cartesian, Cartesian3.ZERO, CesiumMath.EPSILON14)\n  ) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n  result = Cartesian3.multiplyComponents(\n    cartesian,\n    this._oneOverRadiiSquared,\n    result\n  );\n  return Cartesian3.normalize(result, result);\n};\n\nconst cartographicToCartesianNormal = new Cartesian3();\nconst cartographicToCartesianK = new Cartesian3();\n\n/**\n * Converts the provided cartographic to Cartesian representation.\n *\n * @param {Cartographic} cartographic The cartographic position.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if none was provided.\n *\n * @example\n * //Create a Cartographic and determine it's Cartesian representation on a WGS84 ellipsoid.\n * const position = new Cesium.Cartographic(Cesium.Math.toRadians(21), Cesium.Math.toRadians(78), 5000);\n * const cartesianPosition = Cesium.Ellipsoid.WGS84.cartographicToCartesian(position);\n */\nEllipsoid.prototype.cartographicToCartesian = function (cartographic, result) {\n  //`cartographic is required` is thrown from geodeticSurfaceNormalCartographic.\n  const n = cartographicToCartesianNormal;\n  const k = cartographicToCartesianK;\n  this.geodeticSurfaceNormalCartographic(cartographic, n);\n  Cartesian3.multiplyComponents(this._radiiSquared, n, k);\n  const gamma = Math.sqrt(Cartesian3.dot(n, k));\n  Cartesian3.divideByScalar(k, gamma, k);\n  Cartesian3.multiplyByScalar(n, cartographic.height, n);\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n  return Cartesian3.add(k, n, result);\n};\n\n/**\n * Converts the provided array of cartographics to an array of Cartesians.\n *\n * @param {Cartographic[]} cartographics An array of cartographic positions.\n * @param {Cartesian3[]} [result] The object onto which to store the result.\n * @returns {Cartesian3[]} The modified result parameter or a new Array instance if none was provided.\n *\n * @example\n * //Convert an array of Cartographics and determine their Cartesian representation on a WGS84 ellipsoid.\n * const positions = [new Cesium.Cartographic(Cesium.Math.toRadians(21), Cesium.Math.toRadians(78), 0),\n *                  new Cesium.Cartographic(Cesium.Math.toRadians(21.321), Cesium.Math.toRadians(78.123), 100),\n *                  new Cesium.Cartographic(Cesium.Math.toRadians(21.645), Cesium.Math.toRadians(78.456), 250)];\n * const cartesianPositions = Cesium.Ellipsoid.WGS84.cartographicArrayToCartesianArray(positions);\n */\nEllipsoid.prototype.cartographicArrayToCartesianArray = function (\n  cartographics,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"cartographics\", cartographics);\n  //>>includeEnd('debug')\n\n  const length = cartographics.length;\n  if (!defined(result)) {\n    result = new Array(length);\n  } else {\n    result.length = length;\n  }\n  for (let i = 0; i < length; i++) {\n    result[i] = this.cartographicToCartesian(cartographics[i], result[i]);\n  }\n  return result;\n};\n\nconst cartesianToCartographicN = new Cartesian3();\nconst cartesianToCartographicP = new Cartesian3();\nconst cartesianToCartographicH = new Cartesian3();\n\n/**\n * Converts the provided cartesian to cartographic representation.\n * The cartesian is undefined at the center of the ellipsoid.\n *\n * @param {Cartesian3} cartesian The Cartesian position to convert to cartographic representation.\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter, new Cartographic instance if none was provided, or undefined if the cartesian is at the center of the ellipsoid.\n *\n * @example\n * //Create a Cartesian and determine it's Cartographic representation on a WGS84 ellipsoid.\n * const position = new Cesium.Cartesian3(17832.12, 83234.52, 952313.73);\n * const cartographicPosition = Cesium.Ellipsoid.WGS84.cartesianToCartographic(position);\n */\nEllipsoid.prototype.cartesianToCartographic = function (cartesian, result) {\n  //`cartesian is required.` is thrown from scaleToGeodeticSurface\n  const p = this.scaleToGeodeticSurface(cartesian, cartesianToCartographicP);\n\n  if (!defined(p)) {\n    return undefined;\n  }\n\n  const n = this.geodeticSurfaceNormal(p, cartesianToCartographicN);\n  const h = Cartesian3.subtract(cartesian, p, cartesianToCartographicH);\n\n  const longitude = Math.atan2(n.y, n.x);\n  const latitude = Math.asin(n.z);\n  const height =\n    CesiumMath.sign(Cartesian3.dot(h, cartesian)) * Cartesian3.magnitude(h);\n\n  if (!defined(result)) {\n    return new Cartographic(longitude, latitude, height);\n  }\n  result.longitude = longitude;\n  result.latitude = latitude;\n  result.height = height;\n  return result;\n};\n\n/**\n * Converts the provided array of cartesians to an array of cartographics.\n *\n * @param {Cartesian3[]} cartesians An array of Cartesian positions.\n * @param {Cartographic[]} [result] The object onto which to store the result.\n * @returns {Cartographic[]} The modified result parameter or a new Array instance if none was provided.\n *\n * @example\n * //Create an array of Cartesians and determine their Cartographic representation on a WGS84 ellipsoid.\n * const positions = [new Cesium.Cartesian3(17832.12, 83234.52, 952313.73),\n *                  new Cesium.Cartesian3(17832.13, 83234.53, 952313.73),\n *                  new Cesium.Cartesian3(17832.14, 83234.54, 952313.73)]\n * const cartographicPositions = Cesium.Ellipsoid.WGS84.cartesianArrayToCartographicArray(positions);\n */\nEllipsoid.prototype.cartesianArrayToCartographicArray = function (\n  cartesians,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"cartesians\", cartesians);\n  //>>includeEnd('debug');\n\n  const length = cartesians.length;\n  if (!defined(result)) {\n    result = new Array(length);\n  } else {\n    result.length = length;\n  }\n  for (let i = 0; i < length; ++i) {\n    result[i] = this.cartesianToCartographic(cartesians[i], result[i]);\n  }\n  return result;\n};\n\n/**\n * Scales the provided Cartesian position along the geodetic surface normal\n * so that it is on the surface of this ellipsoid.  If the position is\n * at the center of the ellipsoid, this function returns undefined.\n *\n * @param {Cartesian3} cartesian The Cartesian position to scale.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter, a new Cartesian3 instance if none was provided, or undefined if the position is at the center.\n */\nEllipsoid.prototype.scaleToGeodeticSurface = function (cartesian, result) {\n  return scaleToGeodeticSurface(\n    cartesian,\n    this._oneOverRadii,\n    this._oneOverRadiiSquared,\n    this._centerToleranceSquared,\n    result\n  );\n};\n\n/**\n * Scales the provided Cartesian position along the geocentric surface normal\n * so that it is on the surface of this ellipsoid.\n *\n * @param {Cartesian3} cartesian The Cartesian position to scale.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter or a new Cartesian3 instance if none was provided.\n */\nEllipsoid.prototype.scaleToGeocentricSurface = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  const positionX = cartesian.x;\n  const positionY = cartesian.y;\n  const positionZ = cartesian.z;\n  const oneOverRadiiSquared = this._oneOverRadiiSquared;\n\n  const beta =\n    1.0 /\n    Math.sqrt(\n      positionX * positionX * oneOverRadiiSquared.x +\n        positionY * positionY * oneOverRadiiSquared.y +\n        positionZ * positionZ * oneOverRadiiSquared.z\n    );\n\n  return Cartesian3.multiplyByScalar(cartesian, beta, result);\n};\n\n/**\n * Transforms a Cartesian X, Y, Z position to the ellipsoid-scaled space by multiplying\n * its components by the result of {@link Ellipsoid#oneOverRadii}.\n *\n * @param {Cartesian3} position The position to transform.\n * @param {Cartesian3} [result] The position to which to copy the result, or undefined to create and\n *        return a new instance.\n * @returns {Cartesian3} The position expressed in the scaled space.  The returned instance is the\n *          one passed as the result parameter if it is not undefined, or a new instance of it is.\n */\nEllipsoid.prototype.transformPositionToScaledSpace = function (\n  position,\n  result\n) {\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  return Cartesian3.multiplyComponents(position, this._oneOverRadii, result);\n};\n\n/**\n * Transforms a Cartesian X, Y, Z position from the ellipsoid-scaled space by multiplying\n * its components by the result of {@link Ellipsoid#radii}.\n *\n * @param {Cartesian3} position The position to transform.\n * @param {Cartesian3} [result] The position to which to copy the result, or undefined to create and\n *        return a new instance.\n * @returns {Cartesian3} The position expressed in the unscaled space.  The returned instance is the\n *          one passed as the result parameter if it is not undefined, or a new instance of it is.\n */\nEllipsoid.prototype.transformPositionFromScaledSpace = function (\n  position,\n  result\n) {\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  return Cartesian3.multiplyComponents(position, this._radii, result);\n};\n\n/**\n * Compares this Ellipsoid against the provided Ellipsoid componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Ellipsoid} [right] The other Ellipsoid.\n * @returns {Boolean} <code>true</code> if they are equal, <code>false</code> otherwise.\n */\nEllipsoid.prototype.equals = function (right) {\n  return (\n    this === right ||\n    (defined(right) && Cartesian3.equals(this._radii, right._radii))\n  );\n};\n\n/**\n * Creates a string representing this Ellipsoid in the format '(radii.x, radii.y, radii.z)'.\n *\n * @returns {String} A string representing this ellipsoid in the format '(radii.x, radii.y, radii.z)'.\n */\nEllipsoid.prototype.toString = function () {\n  return this._radii.toString();\n};\n\n/**\n * Computes a point which is the intersection of the surface normal with the z-axis.\n *\n * @param {Cartesian3} position the position. must be on the surface of the ellipsoid.\n * @param {Number} [buffer = 0.0] A buffer to subtract from the ellipsoid size when checking if the point is inside the ellipsoid.\n *                                In earth case, with common earth datums, there is no need for this buffer since the intersection point is always (relatively) very close to the center.\n *                                In WGS84 datum, intersection point is at max z = +-42841.31151331382 (0.673% of z-axis).\n *                                Intersection point could be outside the ellipsoid if the ratio of MajorAxis / AxisOfRotation is bigger than the square root of 2\n * @param {Cartesian3} [result] The cartesian to which to copy the result, or undefined to create and\n *        return a new instance.\n * @returns {Cartesian3 | undefined} the intersection point if it's inside the ellipsoid, undefined otherwise\n *\n * @exception {DeveloperError} position is required.\n * @exception {DeveloperError} Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y).\n * @exception {DeveloperError} Ellipsoid.radii.z must be greater than 0.\n */\nEllipsoid.prototype.getSurfaceNormalIntersectionWithZAxis = function (\n  position,\n  buffer,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"position\", position);\n\n  if (\n    !CesiumMath.equalsEpsilon(\n      this._radii.x,\n      this._radii.y,\n      CesiumMath.EPSILON15\n    )\n  ) {\n    throw new DeveloperError(\n      \"Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)\"\n    );\n  }\n\n  Check.typeOf.number.greaterThan(\"Ellipsoid.radii.z\", this._radii.z, 0);\n  //>>includeEnd('debug');\n\n  buffer = defaultValue(buffer, 0.0);\n\n  const squaredXOverSquaredZ = this._squaredXOverSquaredZ;\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  result.x = 0.0;\n  result.y = 0.0;\n  result.z = position.z * (1 - squaredXOverSquaredZ);\n\n  if (Math.abs(result.z) >= this._radii.z - buffer) {\n    return undefined;\n  }\n\n  return result;\n};\n\nconst abscissas = [\n  0.14887433898163,\n  0.43339539412925,\n  0.67940956829902,\n  0.86506336668898,\n  0.97390652851717,\n  0.0,\n];\nconst weights = [\n  0.29552422471475,\n  0.26926671930999,\n  0.21908636251598,\n  0.14945134915058,\n  0.066671344308684,\n  0.0,\n];\n\n/**\n * Compute the 10th order Gauss-Legendre Quadrature of the given definite integral.\n *\n * @param {Number} a The lower bound for the integration.\n * @param {Number} b The upper bound for the integration.\n * @param {Ellipsoid~RealValuedScalarFunction} func The function to integrate.\n * @returns {Number} The value of the integral of the given function over the given domain.\n *\n * @private\n */\nfunction gaussLegendreQuadrature(a, b, func) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"a\", a);\n  Check.typeOf.number(\"b\", b);\n  Check.typeOf.func(\"func\", func);\n  //>>includeEnd('debug');\n\n  // The range is half of the normal range since the five weights add to one (ten weights add to two).\n  // The values of the abscissas are multiplied by two to account for this.\n  const xMean = 0.5 * (b + a);\n  const xRange = 0.5 * (b - a);\n\n  let sum = 0.0;\n  for (let i = 0; i < 5; i++) {\n    const dx = xRange * abscissas[i];\n    sum += weights[i] * (func(xMean + dx) + func(xMean - dx));\n  }\n\n  // Scale the sum to the range of x.\n  sum *= xRange;\n  return sum;\n}\n\n/**\n * A real valued scalar function.\n * @callback Ellipsoid~RealValuedScalarFunction\n *\n * @param {Number} x The value used to evaluate the function.\n * @returns {Number} The value of the function at x.\n *\n * @private\n */\n\n/**\n * Computes an approximation of the surface area of a rectangle on the surface of an ellipsoid using\n * Gauss-Legendre 10th order quadrature.\n *\n * @param {Rectangle} rectangle The rectangle used for computing the surface area.\n * @returns {Number} The approximate area of the rectangle on the surface of this ellipsoid.\n */\nEllipsoid.prototype.surfaceArea = function (rectangle) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n  const minLongitude = rectangle.west;\n  let maxLongitude = rectangle.east;\n  const minLatitude = rectangle.south;\n  const maxLatitude = rectangle.north;\n\n  while (maxLongitude < minLongitude) {\n    maxLongitude += CesiumMath.TWO_PI;\n  }\n\n  const radiiSquared = this._radiiSquared;\n  const a2 = radiiSquared.x;\n  const b2 = radiiSquared.y;\n  const c2 = radiiSquared.z;\n  const a2b2 = a2 * b2;\n  return gaussLegendreQuadrature(minLatitude, maxLatitude, function (lat) {\n    // phi represents the angle measured from the north pole\n    // sin(phi) = sin(pi / 2 - lat) = cos(lat), cos(phi) is similar\n    const sinPhi = Math.cos(lat);\n    const cosPhi = Math.sin(lat);\n    return (\n      Math.cos(lat) *\n      gaussLegendreQuadrature(minLongitude, maxLongitude, function (lon) {\n        const cosTheta = Math.cos(lon);\n        const sinTheta = Math.sin(lon);\n        return Math.sqrt(\n          a2b2 * cosPhi * cosPhi +\n            c2 *\n              (b2 * cosTheta * cosTheta + a2 * sinTheta * sinTheta) *\n              sinPhi *\n              sinPhi\n        );\n      })\n    );\n  });\n};\n\nexport default Ellipsoid;\n", "import Cartesian3 from \"./Cartesian3.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\n\n/**\n * A 3x3 matrix, indexable as a column-major order array.\n * Constructor parameters are in row-major order for code readability.\n * @alias Matrix3\n * @constructor\n * @implements {ArrayLike<number>}\n *\n * @param {Number} [column0Row0=0.0] The value for column 0, row 0.\n * @param {Number} [column1Row0=0.0] The value for column 1, row 0.\n * @param {Number} [column2Row0=0.0] The value for column 2, row 0.\n * @param {Number} [column0Row1=0.0] The value for column 0, row 1.\n * @param {Number} [column1Row1=0.0] The value for column 1, row 1.\n * @param {Number} [column2Row1=0.0] The value for column 2, row 1.\n * @param {Number} [column0Row2=0.0] The value for column 0, row 2.\n * @param {Number} [column1Row2=0.0] The value for column 1, row 2.\n * @param {Number} [column2Row2=0.0] The value for column 2, row 2.\n *\n * @see Matrix3.fromColumnMajorArray\n * @see Matrix3.fromRowMajorArray\n * @see Matrix3.fromQuaternion\n * @see Matrix3.fromScale\n * @see Matrix3.fromUniformScale\n * @see Matrix2\n * @see Matrix4\n */\nfunction Matrix3(\n  column0Row0,\n  column1Row0,\n  column2Row0,\n  column0Row1,\n  column1Row1,\n  column2Row1,\n  column0Row2,\n  column1Row2,\n  column2Row2\n) {\n  this[0] = defaultValue(column0Row0, 0.0);\n  this[1] = defaultValue(column0Row1, 0.0);\n  this[2] = defaultValue(column0Row2, 0.0);\n  this[3] = defaultValue(column1Row0, 0.0);\n  this[4] = defaultValue(column1Row1, 0.0);\n  this[5] = defaultValue(column1Row2, 0.0);\n  this[6] = defaultValue(column2Row0, 0.0);\n  this[7] = defaultValue(column2Row1, 0.0);\n  this[8] = defaultValue(column2Row2, 0.0);\n}\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nMatrix3.packedLength = 9;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {Matrix3} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nMatrix3.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  array[startingIndex++] = value[0];\n  array[startingIndex++] = value[1];\n  array[startingIndex++] = value[2];\n  array[startingIndex++] = value[3];\n  array[startingIndex++] = value[4];\n  array[startingIndex++] = value[5];\n  array[startingIndex++] = value[6];\n  array[startingIndex++] = value[7];\n  array[startingIndex++] = value[8];\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {Matrix3} [result] The object into which to store the result.\n * @returns {Matrix3} The modified result parameter or a new Matrix3 instance if one was not provided.\n */\nMatrix3.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Matrix3();\n  }\n\n  result[0] = array[startingIndex++];\n  result[1] = array[startingIndex++];\n  result[2] = array[startingIndex++];\n  result[3] = array[startingIndex++];\n  result[4] = array[startingIndex++];\n  result[5] = array[startingIndex++];\n  result[6] = array[startingIndex++];\n  result[7] = array[startingIndex++];\n  result[8] = array[startingIndex++];\n  return result;\n};\n\n/**\n * Duplicates a Matrix3 instance.\n *\n * @param {Matrix3} matrix The matrix to duplicate.\n * @param {Matrix3} [result] The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter or a new Matrix3 instance if one was not provided. (Returns undefined if matrix is undefined)\n */\nMatrix3.clone = function (matrix, result) {\n  if (!defined(matrix)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    return new Matrix3(\n      matrix[0],\n      matrix[3],\n      matrix[6],\n      matrix[1],\n      matrix[4],\n      matrix[7],\n      matrix[2],\n      matrix[5],\n      matrix[8]\n    );\n  }\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[3];\n  result[4] = matrix[4];\n  result[5] = matrix[5];\n  result[6] = matrix[6];\n  result[7] = matrix[7];\n  result[8] = matrix[8];\n  return result;\n};\n\n/**\n * Creates a Matrix3 from 9 consecutive elements in an array.\n *\n * @param {Number[]} array The array whose 9 consecutive elements correspond to the positions of the matrix.  Assumes column-major order.\n * @param {Number} [startingIndex=0] The offset into the array of the first element, which corresponds to first column first row position in the matrix.\n * @param {Matrix3} [result] The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter or a new Matrix3 instance if one was not provided.\n *\n * @example\n * // Create the Matrix3:\n * // [1.0, 2.0, 3.0]\n * // [1.0, 2.0, 3.0]\n * // [1.0, 2.0, 3.0]\n *\n * const v = [1.0, 1.0, 1.0, 2.0, 2.0, 2.0, 3.0, 3.0, 3.0];\n * const m = Cesium.Matrix3.fromArray(v);\n *\n * // Create same Matrix3 with using an offset into an array\n * const v2 = [0.0, 0.0, 1.0, 1.0, 1.0, 2.0, 2.0, 2.0, 3.0, 3.0, 3.0];\n * const m2 = Cesium.Matrix3.fromArray(v2, 2);\n */\nMatrix3.fromArray = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Matrix3();\n  }\n\n  result[0] = array[startingIndex];\n  result[1] = array[startingIndex + 1];\n  result[2] = array[startingIndex + 2];\n  result[3] = array[startingIndex + 3];\n  result[4] = array[startingIndex + 4];\n  result[5] = array[startingIndex + 5];\n  result[6] = array[startingIndex + 6];\n  result[7] = array[startingIndex + 7];\n  result[8] = array[startingIndex + 8];\n  return result;\n};\n\n/**\n * Creates a Matrix3 instance from a column-major order array.\n *\n * @param {Number[]} values The column-major order array.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The modified result parameter, or a new Matrix3 instance if one was not provided.\n */\nMatrix3.fromColumnMajorArray = function (values, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"values\", values);\n  //>>includeEnd('debug');\n\n  return Matrix3.clone(values, result);\n};\n\n/**\n * Creates a Matrix3 instance from a row-major order array.\n * The resulting matrix will be in column-major order.\n *\n * @param {Number[]} values The row-major order array.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The modified result parameter, or a new Matrix3 instance if one was not provided.\n */\nMatrix3.fromRowMajorArray = function (values, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"values\", values);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix3(\n      values[0],\n      values[1],\n      values[2],\n      values[3],\n      values[4],\n      values[5],\n      values[6],\n      values[7],\n      values[8]\n    );\n  }\n  result[0] = values[0];\n  result[1] = values[3];\n  result[2] = values[6];\n  result[3] = values[1];\n  result[4] = values[4];\n  result[5] = values[7];\n  result[6] = values[2];\n  result[7] = values[5];\n  result[8] = values[8];\n  return result;\n};\n\n/**\n * Computes a 3x3 rotation matrix from the provided quaternion.\n *\n * @param {Quaternion} quaternion the quaternion to use.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The 3x3 rotation matrix from this quaternion.\n */\nMatrix3.fromQuaternion = function (quaternion, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"quaternion\", quaternion);\n  //>>includeEnd('debug');\n\n  const x2 = quaternion.x * quaternion.x;\n  const xy = quaternion.x * quaternion.y;\n  const xz = quaternion.x * quaternion.z;\n  const xw = quaternion.x * quaternion.w;\n  const y2 = quaternion.y * quaternion.y;\n  const yz = quaternion.y * quaternion.z;\n  const yw = quaternion.y * quaternion.w;\n  const z2 = quaternion.z * quaternion.z;\n  const zw = quaternion.z * quaternion.w;\n  const w2 = quaternion.w * quaternion.w;\n\n  const m00 = x2 - y2 - z2 + w2;\n  const m01 = 2.0 * (xy - zw);\n  const m02 = 2.0 * (xz + yw);\n\n  const m10 = 2.0 * (xy + zw);\n  const m11 = -x2 + y2 - z2 + w2;\n  const m12 = 2.0 * (yz - xw);\n\n  const m20 = 2.0 * (xz - yw);\n  const m21 = 2.0 * (yz + xw);\n  const m22 = -x2 - y2 + z2 + w2;\n\n  if (!defined(result)) {\n    return new Matrix3(m00, m01, m02, m10, m11, m12, m20, m21, m22);\n  }\n  result[0] = m00;\n  result[1] = m10;\n  result[2] = m20;\n  result[3] = m01;\n  result[4] = m11;\n  result[5] = m21;\n  result[6] = m02;\n  result[7] = m12;\n  result[8] = m22;\n  return result;\n};\n\n/**\n * Computes a 3x3 rotation matrix from the provided headingPitchRoll. (see http://en.wikipedia.org/wiki/Conversion_between_quaternions_and_Euler_angles )\n *\n * @param {HeadingPitchRoll} headingPitchRoll the headingPitchRoll to use.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The 3x3 rotation matrix from this headingPitchRoll.\n */\nMatrix3.fromHeadingPitchRoll = function (headingPitchRoll, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"headingPitchRoll\", headingPitchRoll);\n  //>>includeEnd('debug');\n\n  const cosTheta = Math.cos(-headingPitchRoll.pitch);\n  const cosPsi = Math.cos(-headingPitchRoll.heading);\n  const cosPhi = Math.cos(headingPitchRoll.roll);\n  const sinTheta = Math.sin(-headingPitchRoll.pitch);\n  const sinPsi = Math.sin(-headingPitchRoll.heading);\n  const sinPhi = Math.sin(headingPitchRoll.roll);\n\n  const m00 = cosTheta * cosPsi;\n  const m01 = -cosPhi * sinPsi + sinPhi * sinTheta * cosPsi;\n  const m02 = sinPhi * sinPsi + cosPhi * sinTheta * cosPsi;\n\n  const m10 = cosTheta * sinPsi;\n  const m11 = cosPhi * cosPsi + sinPhi * sinTheta * sinPsi;\n  const m12 = -sinPhi * cosPsi + cosPhi * sinTheta * sinPsi;\n\n  const m20 = -sinTheta;\n  const m21 = sinPhi * cosTheta;\n  const m22 = cosPhi * cosTheta;\n\n  if (!defined(result)) {\n    return new Matrix3(m00, m01, m02, m10, m11, m12, m20, m21, m22);\n  }\n  result[0] = m00;\n  result[1] = m10;\n  result[2] = m20;\n  result[3] = m01;\n  result[4] = m11;\n  result[5] = m21;\n  result[6] = m02;\n  result[7] = m12;\n  result[8] = m22;\n  return result;\n};\n\n/**\n * Computes a Matrix3 instance representing a non-uniform scale.\n *\n * @param {Cartesian3} scale The x, y, and z scale factors.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The modified result parameter, or a new Matrix3 instance if one was not provided.\n *\n * @example\n * // Creates\n * //   [7.0, 0.0, 0.0]\n * //   [0.0, 8.0, 0.0]\n * //   [0.0, 0.0, 9.0]\n * const m = Cesium.Matrix3.fromScale(new Cesium.Cartesian3(7.0, 8.0, 9.0));\n */\nMatrix3.fromScale = function (scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"scale\", scale);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix3(scale.x, 0.0, 0.0, 0.0, scale.y, 0.0, 0.0, 0.0, scale.z);\n  }\n\n  result[0] = scale.x;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = scale.y;\n  result[5] = 0.0;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = scale.z;\n  return result;\n};\n\n/**\n * Computes a Matrix3 instance representing a uniform scale.\n *\n * @param {Number} scale The uniform scale factor.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The modified result parameter, or a new Matrix3 instance if one was not provided.\n *\n * @example\n * // Creates\n * //   [2.0, 0.0, 0.0]\n * //   [0.0, 2.0, 0.0]\n * //   [0.0, 0.0, 2.0]\n * const m = Cesium.Matrix3.fromUniformScale(2.0);\n */\nMatrix3.fromUniformScale = function (scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"scale\", scale);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix3(scale, 0.0, 0.0, 0.0, scale, 0.0, 0.0, 0.0, scale);\n  }\n\n  result[0] = scale;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = scale;\n  result[5] = 0.0;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = scale;\n  return result;\n};\n\n/**\n * Computes a Matrix3 instance representing the cross product equivalent matrix of a Cartesian3 vector.\n *\n * @param {Cartesian3} vector the vector on the left hand side of the cross product operation.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The modified result parameter, or a new Matrix3 instance if one was not provided.\n *\n * @example\n * // Creates\n * //   [0.0, -9.0,  8.0]\n * //   [9.0,  0.0, -7.0]\n * //   [-8.0, 7.0,  0.0]\n * const m = Cesium.Matrix3.fromCrossProduct(new Cesium.Cartesian3(7.0, 8.0, 9.0));\n */\nMatrix3.fromCrossProduct = function (vector, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"vector\", vector);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix3(\n      0.0,\n      -vector.z,\n      vector.y,\n      vector.z,\n      0.0,\n      -vector.x,\n      -vector.y,\n      vector.x,\n      0.0\n    );\n  }\n\n  result[0] = 0.0;\n  result[1] = vector.z;\n  result[2] = -vector.y;\n  result[3] = -vector.z;\n  result[4] = 0.0;\n  result[5] = vector.x;\n  result[6] = vector.y;\n  result[7] = -vector.x;\n  result[8] = 0.0;\n  return result;\n};\n\n/**\n * Creates a rotation matrix around the x-axis.\n *\n * @param {Number} angle The angle, in radians, of the rotation.  Positive angles are counterclockwise.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The modified result parameter, or a new Matrix3 instance if one was not provided.\n *\n * @example\n * // Rotate a point 45 degrees counterclockwise around the x-axis.\n * const p = new Cesium.Cartesian3(5, 6, 7);\n * const m = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(45.0));\n * const rotated = Cesium.Matrix3.multiplyByVector(m, p, new Cesium.Cartesian3());\n */\nMatrix3.fromRotationX = function (angle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"angle\", angle);\n  //>>includeEnd('debug');\n\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n\n  if (!defined(result)) {\n    return new Matrix3(\n      1.0,\n      0.0,\n      0.0,\n      0.0,\n      cosAngle,\n      -sinAngle,\n      0.0,\n      sinAngle,\n      cosAngle\n    );\n  }\n\n  result[0] = 1.0;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = cosAngle;\n  result[5] = sinAngle;\n  result[6] = 0.0;\n  result[7] = -sinAngle;\n  result[8] = cosAngle;\n\n  return result;\n};\n\n/**\n * Creates a rotation matrix around the y-axis.\n *\n * @param {Number} angle The angle, in radians, of the rotation.  Positive angles are counterclockwise.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The modified result parameter, or a new Matrix3 instance if one was not provided.\n *\n * @example\n * // Rotate a point 45 degrees counterclockwise around the y-axis.\n * const p = new Cesium.Cartesian3(5, 6, 7);\n * const m = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(45.0));\n * const rotated = Cesium.Matrix3.multiplyByVector(m, p, new Cesium.Cartesian3());\n */\nMatrix3.fromRotationY = function (angle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"angle\", angle);\n  //>>includeEnd('debug');\n\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n\n  if (!defined(result)) {\n    return new Matrix3(\n      cosAngle,\n      0.0,\n      sinAngle,\n      0.0,\n      1.0,\n      0.0,\n      -sinAngle,\n      0.0,\n      cosAngle\n    );\n  }\n\n  result[0] = cosAngle;\n  result[1] = 0.0;\n  result[2] = -sinAngle;\n  result[3] = 0.0;\n  result[4] = 1.0;\n  result[5] = 0.0;\n  result[6] = sinAngle;\n  result[7] = 0.0;\n  result[8] = cosAngle;\n\n  return result;\n};\n\n/**\n * Creates a rotation matrix around the z-axis.\n *\n * @param {Number} angle The angle, in radians, of the rotation.  Positive angles are counterclockwise.\n * @param {Matrix3} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix3} The modified result parameter, or a new Matrix3 instance if one was not provided.\n *\n * @example\n * // Rotate a point 45 degrees counterclockwise around the z-axis.\n * const p = new Cesium.Cartesian3(5, 6, 7);\n * const m = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(45.0));\n * const rotated = Cesium.Matrix3.multiplyByVector(m, p, new Cesium.Cartesian3());\n */\nMatrix3.fromRotationZ = function (angle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"angle\", angle);\n  //>>includeEnd('debug');\n\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n\n  if (!defined(result)) {\n    return new Matrix3(\n      cosAngle,\n      -sinAngle,\n      0.0,\n      sinAngle,\n      cosAngle,\n      0.0,\n      0.0,\n      0.0,\n      1.0\n    );\n  }\n\n  result[0] = cosAngle;\n  result[1] = sinAngle;\n  result[2] = 0.0;\n  result[3] = -sinAngle;\n  result[4] = cosAngle;\n  result[5] = 0.0;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = 1.0;\n\n  return result;\n};\n\n/**\n * Creates an Array from the provided Matrix3 instance.\n * The array will be in column-major order.\n *\n * @param {Matrix3} matrix The matrix to use..\n * @param {Number[]} [result] The Array onto which to store the result.\n * @returns {Number[]} The modified Array parameter or a new Array instance if one was not provided.\n */\nMatrix3.toArray = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return [\n      matrix[0],\n      matrix[1],\n      matrix[2],\n      matrix[3],\n      matrix[4],\n      matrix[5],\n      matrix[6],\n      matrix[7],\n      matrix[8],\n    ];\n  }\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[3];\n  result[4] = matrix[4];\n  result[5] = matrix[5];\n  result[6] = matrix[6];\n  result[7] = matrix[7];\n  result[8] = matrix[8];\n  return result;\n};\n\n/**\n * Computes the array index of the element at the provided row and column.\n *\n * @param {Number} column The zero-based index of the column.\n * @param {Number} row The zero-based index of the row.\n * @returns {Number} The index of the element at the provided row and column.\n *\n * @exception {DeveloperError} row must be 0, 1, or 2.\n * @exception {DeveloperError} column must be 0, 1, or 2.\n *\n * @example\n * const myMatrix = new Cesium.Matrix3();\n * const column1Row0Index = Cesium.Matrix3.getElementIndex(1, 0);\n * const column1Row0 = myMatrix[column1Row0Index]\n * myMatrix[column1Row0Index] = 10.0;\n */\nMatrix3.getElementIndex = function (column, row) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number.greaterThanOrEquals(\"row\", row, 0);\n  Check.typeOf.number.lessThanOrEquals(\"row\", row, 2);\n  Check.typeOf.number.greaterThanOrEquals(\"column\", column, 0);\n  Check.typeOf.number.lessThanOrEquals(\"column\", column, 2);\n  //>>includeEnd('debug');\n\n  return column * 3 + row;\n};\n\n/**\n * Retrieves a copy of the matrix column at the provided index as a Cartesian3 instance.\n *\n * @param {Matrix3} matrix The matrix to use.\n * @param {Number} index The zero-based index of the column to retrieve.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0, 1, or 2.\n */\nMatrix3.getColumn = function (matrix, index, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 2);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const startIndex = index * 3;\n  const x = matrix[startIndex];\n  const y = matrix[startIndex + 1];\n  const z = matrix[startIndex + 2];\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return result;\n};\n\n/**\n * Computes a new matrix that replaces the specified column in the provided matrix with the provided Cartesian3 instance.\n *\n * @param {Matrix3} matrix The matrix to use.\n * @param {Number} index The zero-based index of the column to set.\n * @param {Cartesian3} cartesian The Cartesian whose values will be assigned to the specified column.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0, 1, or 2.\n */\nMatrix3.setColumn = function (matrix, index, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 2);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result = Matrix3.clone(matrix, result);\n  const startIndex = index * 3;\n  result[startIndex] = cartesian.x;\n  result[startIndex + 1] = cartesian.y;\n  result[startIndex + 2] = cartesian.z;\n  return result;\n};\n\n/**\n * Retrieves a copy of the matrix row at the provided index as a Cartesian3 instance.\n *\n * @param {Matrix3} matrix The matrix to use.\n * @param {Number} index The zero-based index of the row to retrieve.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0, 1, or 2.\n */\nMatrix3.getRow = function (matrix, index, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 2);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const x = matrix[index];\n  const y = matrix[index + 3];\n  const z = matrix[index + 6];\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return result;\n};\n\n/**\n * Computes a new matrix that replaces the specified row in the provided matrix with the provided Cartesian3 instance.\n *\n * @param {Matrix3} matrix The matrix to use.\n * @param {Number} index The zero-based index of the row to set.\n * @param {Cartesian3} cartesian The Cartesian whose values will be assigned to the specified row.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0, 1, or 2.\n */\nMatrix3.setRow = function (matrix, index, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 2);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result = Matrix3.clone(matrix, result);\n  result[index] = cartesian.x;\n  result[index + 3] = cartesian.y;\n  result[index + 6] = cartesian.z;\n  return result;\n};\n\nconst scratchColumn = new Cartesian3();\n\n/**\n * Extracts the non-uniform scale assuming the matrix is an affine transformation.\n *\n * @param {Matrix3} matrix The matrix.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nMatrix3.getScale = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Cartesian3.magnitude(\n    Cartesian3.fromElements(matrix[0], matrix[1], matrix[2], scratchColumn)\n  );\n  result.y = Cartesian3.magnitude(\n    Cartesian3.fromElements(matrix[3], matrix[4], matrix[5], scratchColumn)\n  );\n  result.z = Cartesian3.magnitude(\n    Cartesian3.fromElements(matrix[6], matrix[7], matrix[8], scratchColumn)\n  );\n  return result;\n};\n\nconst scratchScale = new Cartesian3();\n\n/**\n * Computes the maximum scale assuming the matrix is an affine transformation.\n * The maximum scale is the maximum length of the column vectors.\n *\n * @param {Matrix3} matrix The matrix.\n * @returns {Number} The maximum scale.\n */\nMatrix3.getMaximumScale = function (matrix) {\n  Matrix3.getScale(matrix, scratchScale);\n  return Cartesian3.maximumComponent(scratchScale);\n};\n\n/**\n * Computes the product of two matrices.\n *\n * @param {Matrix3} left The first matrix.\n * @param {Matrix3} right The second matrix.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n */\nMatrix3.multiply = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const column0Row0 =\n    left[0] * right[0] + left[3] * right[1] + left[6] * right[2];\n  const column0Row1 =\n    left[1] * right[0] + left[4] * right[1] + left[7] * right[2];\n  const column0Row2 =\n    left[2] * right[0] + left[5] * right[1] + left[8] * right[2];\n\n  const column1Row0 =\n    left[0] * right[3] + left[3] * right[4] + left[6] * right[5];\n  const column1Row1 =\n    left[1] * right[3] + left[4] * right[4] + left[7] * right[5];\n  const column1Row2 =\n    left[2] * right[3] + left[5] * right[4] + left[8] * right[5];\n\n  const column2Row0 =\n    left[0] * right[6] + left[3] * right[7] + left[6] * right[8];\n  const column2Row1 =\n    left[1] * right[6] + left[4] * right[7] + left[7] * right[8];\n  const column2Row2 =\n    left[2] * right[6] + left[5] * right[7] + left[8] * right[8];\n\n  result[0] = column0Row0;\n  result[1] = column0Row1;\n  result[2] = column0Row2;\n  result[3] = column1Row0;\n  result[4] = column1Row1;\n  result[5] = column1Row2;\n  result[6] = column2Row0;\n  result[7] = column2Row1;\n  result[8] = column2Row2;\n  return result;\n};\n\n/**\n * Computes the sum of two matrices.\n *\n * @param {Matrix3} left The first matrix.\n * @param {Matrix3} right The second matrix.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n */\nMatrix3.add = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = left[0] + right[0];\n  result[1] = left[1] + right[1];\n  result[2] = left[2] + right[2];\n  result[3] = left[3] + right[3];\n  result[4] = left[4] + right[4];\n  result[5] = left[5] + right[5];\n  result[6] = left[6] + right[6];\n  result[7] = left[7] + right[7];\n  result[8] = left[8] + right[8];\n  return result;\n};\n\n/**\n * Computes the difference of two matrices.\n *\n * @param {Matrix3} left The first matrix.\n * @param {Matrix3} right The second matrix.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n */\nMatrix3.subtract = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = left[0] - right[0];\n  result[1] = left[1] - right[1];\n  result[2] = left[2] - right[2];\n  result[3] = left[3] - right[3];\n  result[4] = left[4] - right[4];\n  result[5] = left[5] - right[5];\n  result[6] = left[6] - right[6];\n  result[7] = left[7] - right[7];\n  result[8] = left[8] - right[8];\n  return result;\n};\n\n/**\n * Computes the product of a matrix and a column vector.\n *\n * @param {Matrix3} matrix The matrix.\n * @param {Cartesian3} cartesian The column.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nMatrix3.multiplyByVector = function (matrix, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const vX = cartesian.x;\n  const vY = cartesian.y;\n  const vZ = cartesian.z;\n\n  const x = matrix[0] * vX + matrix[3] * vY + matrix[6] * vZ;\n  const y = matrix[1] * vX + matrix[4] * vY + matrix[7] * vZ;\n  const z = matrix[2] * vX + matrix[5] * vY + matrix[8] * vZ;\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return result;\n};\n\n/**\n * Computes the product of a matrix and a scalar.\n *\n * @param {Matrix3} matrix The matrix.\n * @param {Number} scalar The number to multiply by.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n */\nMatrix3.multiplyByScalar = function (matrix, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = matrix[0] * scalar;\n  result[1] = matrix[1] * scalar;\n  result[2] = matrix[2] * scalar;\n  result[3] = matrix[3] * scalar;\n  result[4] = matrix[4] * scalar;\n  result[5] = matrix[5] * scalar;\n  result[6] = matrix[6] * scalar;\n  result[7] = matrix[7] * scalar;\n  result[8] = matrix[8] * scalar;\n  return result;\n};\n\n/**\n * Computes the product of a matrix times a (non-uniform) scale, as if the scale were a scale matrix.\n *\n * @param {Matrix3} matrix The matrix on the left-hand side.\n * @param {Cartesian3} scale The non-uniform scale on the right-hand side.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n *\n *\n * @example\n * // Instead of Cesium.Matrix3.multiply(m, Cesium.Matrix3.fromScale(scale), m);\n * Cesium.Matrix3.multiplyByScale(m, scale, m);\n *\n * @see Matrix3.fromScale\n * @see Matrix3.multiplyByUniformScale\n */\nMatrix3.multiplyByScale = function (matrix, scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"scale\", scale);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = matrix[0] * scale.x;\n  result[1] = matrix[1] * scale.x;\n  result[2] = matrix[2] * scale.x;\n  result[3] = matrix[3] * scale.y;\n  result[4] = matrix[4] * scale.y;\n  result[5] = matrix[5] * scale.y;\n  result[6] = matrix[6] * scale.z;\n  result[7] = matrix[7] * scale.z;\n  result[8] = matrix[8] * scale.z;\n  return result;\n};\n\n/**\n * Creates a negated copy of the provided matrix.\n *\n * @param {Matrix3} matrix The matrix to negate.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n */\nMatrix3.negate = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = -matrix[0];\n  result[1] = -matrix[1];\n  result[2] = -matrix[2];\n  result[3] = -matrix[3];\n  result[4] = -matrix[4];\n  result[5] = -matrix[5];\n  result[6] = -matrix[6];\n  result[7] = -matrix[7];\n  result[8] = -matrix[8];\n  return result;\n};\n\n/**\n * Computes the transpose of the provided matrix.\n *\n * @param {Matrix3} matrix The matrix to transpose.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n */\nMatrix3.transpose = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const column0Row0 = matrix[0];\n  const column0Row1 = matrix[3];\n  const column0Row2 = matrix[6];\n  const column1Row0 = matrix[1];\n  const column1Row1 = matrix[4];\n  const column1Row2 = matrix[7];\n  const column2Row0 = matrix[2];\n  const column2Row1 = matrix[5];\n  const column2Row2 = matrix[8];\n\n  result[0] = column0Row0;\n  result[1] = column0Row1;\n  result[2] = column0Row2;\n  result[3] = column1Row0;\n  result[4] = column1Row1;\n  result[5] = column1Row2;\n  result[6] = column2Row0;\n  result[7] = column2Row1;\n  result[8] = column2Row2;\n  return result;\n};\n\nconst UNIT = new Cartesian3(1, 1, 1);\n\n/**\n * Extracts the rotation assuming the matrix is an affine transformation.\n *\n * @param {Matrix3} matrix The matrix.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter\n */\nMatrix3.getRotation = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const inverseScale = Cartesian3.divideComponents(\n    UNIT,\n    Matrix3.getScale(matrix, scratchScale),\n    scratchScale\n  );\n  result = Matrix3.multiplyByScale(matrix, inverseScale, result);\n\n  return result;\n};\n\nfunction computeFrobeniusNorm(matrix) {\n  let norm = 0.0;\n  for (let i = 0; i < 9; ++i) {\n    const temp = matrix[i];\n    norm += temp * temp;\n  }\n\n  return Math.sqrt(norm);\n}\n\nconst rowVal = [1, 0, 0];\nconst colVal = [2, 2, 1];\n\nfunction offDiagonalFrobeniusNorm(matrix) {\n  // Computes the \"off-diagonal\" Frobenius norm.\n  // Assumes matrix is symmetric.\n\n  let norm = 0.0;\n  for (let i = 0; i < 3; ++i) {\n    const temp = matrix[Matrix3.getElementIndex(colVal[i], rowVal[i])];\n    norm += 2.0 * temp * temp;\n  }\n\n  return Math.sqrt(norm);\n}\n\nfunction shurDecomposition(matrix, result) {\n  // This routine was created based upon Matrix Computations, 3rd ed., by Golub and Van Loan,\n  // section 8.4.2 The 2by2 Symmetric Schur Decomposition.\n  //\n  // The routine takes a matrix, which is assumed to be symmetric, and\n  // finds the largest off-diagonal term, and then creates\n  // a matrix (result) which can be used to help reduce it\n\n  const tolerance = CesiumMath.EPSILON15;\n\n  let maxDiagonal = 0.0;\n  let rotAxis = 1;\n\n  // find pivot (rotAxis) based on max diagonal of matrix\n  for (let i = 0; i < 3; ++i) {\n    const temp = Math.abs(\n      matrix[Matrix3.getElementIndex(colVal[i], rowVal[i])]\n    );\n    if (temp > maxDiagonal) {\n      rotAxis = i;\n      maxDiagonal = temp;\n    }\n  }\n\n  let c = 1.0;\n  let s = 0.0;\n\n  const p = rowVal[rotAxis];\n  const q = colVal[rotAxis];\n\n  if (Math.abs(matrix[Matrix3.getElementIndex(q, p)]) > tolerance) {\n    const qq = matrix[Matrix3.getElementIndex(q, q)];\n    const pp = matrix[Matrix3.getElementIndex(p, p)];\n    const qp = matrix[Matrix3.getElementIndex(q, p)];\n\n    const tau = (qq - pp) / 2.0 / qp;\n    let t;\n\n    if (tau < 0.0) {\n      t = -1.0 / (-tau + Math.sqrt(1.0 + tau * tau));\n    } else {\n      t = 1.0 / (tau + Math.sqrt(1.0 + tau * tau));\n    }\n\n    c = 1.0 / Math.sqrt(1.0 + t * t);\n    s = t * c;\n  }\n\n  result = Matrix3.clone(Matrix3.IDENTITY, result);\n\n  result[Matrix3.getElementIndex(p, p)] = result[\n    Matrix3.getElementIndex(q, q)\n  ] = c;\n  result[Matrix3.getElementIndex(q, p)] = s;\n  result[Matrix3.getElementIndex(p, q)] = -s;\n\n  return result;\n}\n\nconst jMatrix = new Matrix3();\nconst jMatrixTranspose = new Matrix3();\n\n/**\n * Computes the eigenvectors and eigenvalues of a symmetric matrix.\n * <p>\n * Returns a diagonal matrix and unitary matrix such that:\n * <code>matrix = unitary matrix * diagonal matrix * transpose(unitary matrix)</code>\n * </p>\n * <p>\n * The values along the diagonal of the diagonal matrix are the eigenvalues. The columns\n * of the unitary matrix are the corresponding eigenvectors.\n * </p>\n *\n * @param {Matrix3} matrix The matrix to decompose into diagonal and unitary matrix. Expected to be symmetric.\n * @param {Object} [result] An object with unitary and diagonal properties which are matrices onto which to store the result.\n * @returns {Object} An object with unitary and diagonal properties which are the unitary and diagonal matrices, respectively.\n *\n * @example\n * const a = //... symetric matrix\n * const result = {\n *     unitary : new Cesium.Matrix3(),\n *     diagonal : new Cesium.Matrix3()\n * };\n * Cesium.Matrix3.computeEigenDecomposition(a, result);\n *\n * const unitaryTranspose = Cesium.Matrix3.transpose(result.unitary, new Cesium.Matrix3());\n * const b = Cesium.Matrix3.multiply(result.unitary, result.diagonal, new Cesium.Matrix3());\n * Cesium.Matrix3.multiply(b, unitaryTranspose, b); // b is now equal to a\n *\n * const lambda = Cesium.Matrix3.getColumn(result.diagonal, 0, new Cesium.Cartesian3()).x;  // first eigenvalue\n * const v = Cesium.Matrix3.getColumn(result.unitary, 0, new Cesium.Cartesian3());          // first eigenvector\n * const c = Cesium.Cartesian3.multiplyByScalar(v, lambda, new Cesium.Cartesian3());        // equal to Cesium.Matrix3.multiplyByVector(a, v)\n */\nMatrix3.computeEigenDecomposition = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  //>>includeEnd('debug');\n\n  // This routine was created based upon Matrix Computations, 3rd ed., by Golub and Van Loan,\n  // section 8.4.3 The Classical Jacobi Algorithm\n\n  const tolerance = CesiumMath.EPSILON20;\n  const maxSweeps = 10;\n\n  let count = 0;\n  let sweep = 0;\n\n  if (!defined(result)) {\n    result = {};\n  }\n\n  const unitaryMatrix = (result.unitary = Matrix3.clone(\n    Matrix3.IDENTITY,\n    result.unitary\n  ));\n  const diagMatrix = (result.diagonal = Matrix3.clone(matrix, result.diagonal));\n\n  const epsilon = tolerance * computeFrobeniusNorm(diagMatrix);\n\n  while (sweep < maxSweeps && offDiagonalFrobeniusNorm(diagMatrix) > epsilon) {\n    shurDecomposition(diagMatrix, jMatrix);\n    Matrix3.transpose(jMatrix, jMatrixTranspose);\n    Matrix3.multiply(diagMatrix, jMatrix, diagMatrix);\n    Matrix3.multiply(jMatrixTranspose, diagMatrix, diagMatrix);\n    Matrix3.multiply(unitaryMatrix, jMatrix, unitaryMatrix);\n\n    if (++count > 2) {\n      ++sweep;\n      count = 0;\n    }\n  }\n\n  return result;\n};\n\n/**\n * Computes a matrix, which contains the absolute (unsigned) values of the provided matrix's elements.\n *\n * @param {Matrix3} matrix The matrix with signed elements.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n */\nMatrix3.abs = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = Math.abs(matrix[0]);\n  result[1] = Math.abs(matrix[1]);\n  result[2] = Math.abs(matrix[2]);\n  result[3] = Math.abs(matrix[3]);\n  result[4] = Math.abs(matrix[4]);\n  result[5] = Math.abs(matrix[5]);\n  result[6] = Math.abs(matrix[6]);\n  result[7] = Math.abs(matrix[7]);\n  result[8] = Math.abs(matrix[8]);\n\n  return result;\n};\n\n/**\n * Computes the determinant of the provided matrix.\n *\n * @param {Matrix3} matrix The matrix to use.\n * @returns {Number} The value of the determinant of the matrix.\n */\nMatrix3.determinant = function (matrix) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  //>>includeEnd('debug');\n\n  const m11 = matrix[0];\n  const m21 = matrix[3];\n  const m31 = matrix[6];\n  const m12 = matrix[1];\n  const m22 = matrix[4];\n  const m32 = matrix[7];\n  const m13 = matrix[2];\n  const m23 = matrix[5];\n  const m33 = matrix[8];\n\n  return (\n    m11 * (m22 * m33 - m23 * m32) +\n    m12 * (m23 * m31 - m21 * m33) +\n    m13 * (m21 * m32 - m22 * m31)\n  );\n};\n\n/**\n * Computes the inverse of the provided matrix.\n *\n * @param {Matrix3} matrix The matrix to invert.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n *\n * @exception {DeveloperError} matrix is not invertible.\n */\nMatrix3.inverse = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const m11 = matrix[0];\n  const m21 = matrix[1];\n  const m31 = matrix[2];\n  const m12 = matrix[3];\n  const m22 = matrix[4];\n  const m32 = matrix[5];\n  const m13 = matrix[6];\n  const m23 = matrix[7];\n  const m33 = matrix[8];\n\n  const determinant = Matrix3.determinant(matrix);\n\n  //>>includeStart('debug', pragmas.debug);\n  if (Math.abs(determinant) <= CesiumMath.EPSILON15) {\n    throw new DeveloperError(\"matrix is not invertible\");\n  }\n  //>>includeEnd('debug');\n\n  result[0] = m22 * m33 - m23 * m32;\n  result[1] = m23 * m31 - m21 * m33;\n  result[2] = m21 * m32 - m22 * m31;\n  result[3] = m13 * m32 - m12 * m33;\n  result[4] = m11 * m33 - m13 * m31;\n  result[5] = m12 * m31 - m11 * m32;\n  result[6] = m12 * m23 - m13 * m22;\n  result[7] = m13 * m21 - m11 * m23;\n  result[8] = m11 * m22 - m12 * m21;\n\n  const scale = 1.0 / determinant;\n  return Matrix3.multiplyByScalar(result, scale, result);\n};\n\nconst scratchTransposeMatrix = new Matrix3();\n\n/**\n * Computes the inverse transpose of a matrix.\n *\n * @param {Matrix3} matrix The matrix to transpose and invert.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n */\nMatrix3.inverseTranspose = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  return Matrix3.inverse(\n    Matrix3.transpose(matrix, scratchTransposeMatrix),\n    result\n  );\n};\n\n/**\n * Compares the provided matrices componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Matrix3} [left] The first matrix.\n * @param {Matrix3} [right] The second matrix.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nMatrix3.equals = function (left, right) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      left[0] === right[0] &&\n      left[1] === right[1] &&\n      left[2] === right[2] &&\n      left[3] === right[3] &&\n      left[4] === right[4] &&\n      left[5] === right[5] &&\n      left[6] === right[6] &&\n      left[7] === right[7] &&\n      left[8] === right[8])\n  );\n};\n\n/**\n * Compares the provided matrices componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Matrix3} [left] The first matrix.\n * @param {Matrix3} [right] The second matrix.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n */\nMatrix3.equalsEpsilon = function (left, right, epsilon) {\n  epsilon = defaultValue(epsilon, 0);\n\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      Math.abs(left[0] - right[0]) <= epsilon &&\n      Math.abs(left[1] - right[1]) <= epsilon &&\n      Math.abs(left[2] - right[2]) <= epsilon &&\n      Math.abs(left[3] - right[3]) <= epsilon &&\n      Math.abs(left[4] - right[4]) <= epsilon &&\n      Math.abs(left[5] - right[5]) <= epsilon &&\n      Math.abs(left[6] - right[6]) <= epsilon &&\n      Math.abs(left[7] - right[7]) <= epsilon &&\n      Math.abs(left[8] - right[8]) <= epsilon)\n  );\n};\n\n/**\n * An immutable Matrix3 instance initialized to the identity matrix.\n *\n * @type {Matrix3}\n * @constant\n */\nMatrix3.IDENTITY = Object.freeze(\n  new Matrix3(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0)\n);\n\n/**\n * An immutable Matrix3 instance initialized to the zero matrix.\n *\n * @type {Matrix3}\n * @constant\n */\nMatrix3.ZERO = Object.freeze(\n  new Matrix3(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)\n);\n\n/**\n * The index into Matrix3 for column 0, row 0.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN0ROW0 = 0;\n\n/**\n * The index into Matrix3 for column 0, row 1.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN0ROW1 = 1;\n\n/**\n * The index into Matrix3 for column 0, row 2.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN0ROW2 = 2;\n\n/**\n * The index into Matrix3 for column 1, row 0.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN1ROW0 = 3;\n\n/**\n * The index into Matrix3 for column 1, row 1.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN1ROW1 = 4;\n\n/**\n * The index into Matrix3 for column 1, row 2.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN1ROW2 = 5;\n\n/**\n * The index into Matrix3 for column 2, row 0.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN2ROW0 = 6;\n\n/**\n * The index into Matrix3 for column 2, row 1.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN2ROW1 = 7;\n\n/**\n * The index into Matrix3 for column 2, row 2.\n *\n * @type {Number}\n * @constant\n */\nMatrix3.COLUMN2ROW2 = 8;\n\nObject.defineProperties(Matrix3.prototype, {\n  /**\n   * Gets the number of items in the collection.\n   * @memberof Matrix3.prototype\n   *\n   * @type {Number}\n   */\n  length: {\n    get: function () {\n      return Matrix3.packedLength;\n    },\n  },\n});\n\n/**\n * Duplicates the provided Matrix3 instance.\n *\n * @param {Matrix3} [result] The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter or a new Matrix3 instance if one was not provided.\n */\nMatrix3.prototype.clone = function (result) {\n  return Matrix3.clone(this, result);\n};\n\n/**\n * Compares this matrix to the provided matrix componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Matrix3} [right] The right hand side matrix.\n * @returns {Boolean} <code>true</code> if they are equal, <code>false</code> otherwise.\n */\nMatrix3.prototype.equals = function (right) {\n  return Matrix3.equals(this, right);\n};\n\n/**\n * @private\n */\nMatrix3.equalsArray = function (matrix, array, offset) {\n  return (\n    matrix[0] === array[offset] &&\n    matrix[1] === array[offset + 1] &&\n    matrix[2] === array[offset + 2] &&\n    matrix[3] === array[offset + 3] &&\n    matrix[4] === array[offset + 4] &&\n    matrix[5] === array[offset + 5] &&\n    matrix[6] === array[offset + 6] &&\n    matrix[7] === array[offset + 7] &&\n    matrix[8] === array[offset + 8]\n  );\n};\n\n/**\n * Compares this matrix to the provided matrix componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Matrix3} [right] The right hand side matrix.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if they are within the provided epsilon, <code>false</code> otherwise.\n */\nMatrix3.prototype.equalsEpsilon = function (right, epsilon) {\n  return Matrix3.equalsEpsilon(this, right, epsilon);\n};\n\n/**\n * Creates a string representing this Matrix with each row being\n * on a separate line and in the format '(column0, column1, column2)'.\n *\n * @returns {String} A string representing the provided Matrix with each row being on a separate line and in the format '(column0, column1, column2)'.\n */\nMatrix3.prototype.toString = function () {\n  return (\n    \"(\" +\n    this[0] +\n    \", \" +\n    this[3] +\n    \", \" +\n    this[6] +\n    \")\\n\" +\n    \"(\" +\n    this[1] +\n    \", \" +\n    this[4] +\n    \", \" +\n    this[7] +\n    \")\\n\" +\n    \"(\" +\n    this[2] +\n    \", \" +\n    this[5] +\n    \", \" +\n    this[8] +\n    \")\"\n  );\n};\nexport default Matrix3;\n", "import Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\n\n/**\n * A 4D Cartesian point.\n * @alias Cartesian4\n * @constructor\n *\n * @param {Number} [x=0.0] The X component.\n * @param {Number} [y=0.0] The Y component.\n * @param {Number} [z=0.0] The Z component.\n * @param {Number} [w=0.0] The W component.\n *\n * @see Cartesian2\n * @see Cartesian3\n * @see Packable\n */\nfunction Cartesian4(x, y, z, w) {\n  /**\n   * The X component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.x = defaultValue(x, 0.0);\n\n  /**\n   * The Y component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.y = defaultValue(y, 0.0);\n\n  /**\n   * The Z component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.z = defaultValue(z, 0.0);\n\n  /**\n   * The W component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.w = defaultValue(w, 0.0);\n}\n\n/**\n * Creates a Cartesian4 instance from x, y, z and w coordinates.\n *\n * @param {Number} x The x coordinate.\n * @param {Number} y The y coordinate.\n * @param {Number} z The z coordinate.\n * @param {Number} w The w coordinate.\n * @param {Cartesian4} [result] The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter or a new Cartesian4 instance if one was not provided.\n */\nCartesian4.fromElements = function (x, y, z, w, result) {\n  if (!defined(result)) {\n    return new Cartesian4(x, y, z, w);\n  }\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  result.w = w;\n  return result;\n};\n\n/**\n * Creates a Cartesian4 instance from a {@link Color}. <code>red</code>, <code>green</code>, <code>blue</code>,\n * and <code>alpha</code> map to <code>x</code>, <code>y</code>, <code>z</code>, and <code>w</code>, respectively.\n *\n * @param {Color} color The source color.\n * @param {Cartesian4} [result] The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter or a new Cartesian4 instance if one was not provided.\n */\nCartesian4.fromColor = function (color, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"color\", color);\n  //>>includeEnd('debug');\n  if (!defined(result)) {\n    return new Cartesian4(color.red, color.green, color.blue, color.alpha);\n  }\n\n  result.x = color.red;\n  result.y = color.green;\n  result.z = color.blue;\n  result.w = color.alpha;\n  return result;\n};\n\n/**\n * Duplicates a Cartesian4 instance.\n *\n * @param {Cartesian4} cartesian The Cartesian to duplicate.\n * @param {Cartesian4} [result] The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter or a new Cartesian4 instance if one was not provided. (Returns undefined if cartesian is undefined)\n */\nCartesian4.clone = function (cartesian, result) {\n  if (!defined(cartesian)) {\n    return undefined;\n  }\n\n  if (!defined(result)) {\n    return new Cartesian4(cartesian.x, cartesian.y, cartesian.z, cartesian.w);\n  }\n\n  result.x = cartesian.x;\n  result.y = cartesian.y;\n  result.z = cartesian.z;\n  result.w = cartesian.w;\n  return result;\n};\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nCartesian4.packedLength = 4;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {Cartesian4} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nCartesian4.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  array[startingIndex++] = value.x;\n  array[startingIndex++] = value.y;\n  array[startingIndex++] = value.z;\n  array[startingIndex] = value.w;\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {Cartesian4} [result] The object into which to store the result.\n * @returns {Cartesian4}  The modified result parameter or a new Cartesian4 instance if one was not provided.\n */\nCartesian4.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Cartesian4();\n  }\n  result.x = array[startingIndex++];\n  result.y = array[startingIndex++];\n  result.z = array[startingIndex++];\n  result.w = array[startingIndex];\n  return result;\n};\n\n/**\n     * Flattens an array of Cartesian4s into and array of components.\n     *\n     * @param {Cartesian4[]} array The array of cartesians to pack.\n     * @param {Number[]} [result] The array onto which to store the result. If this is a typed array, it must have array.length * 4 components, else a {@link DeveloperError} will be thrown. If it is a regular array, it will be resized to have (array.length * 4) elements.\n\n     * @returns {Number[]} The packed array.\n     */\nCartesian4.packArray = function (array, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  const length = array.length;\n  const resultLength = length * 4;\n  if (!defined(result)) {\n    result = new Array(resultLength);\n  } else if (!Array.isArray(result) && result.length !== resultLength) {\n    throw new DeveloperError(\n      \"If result is a typed array, it must have exactly array.length * 4 elements\"\n    );\n  } else if (result.length !== resultLength) {\n    result.length = resultLength;\n  }\n\n  for (let i = 0; i < length; ++i) {\n    Cartesian4.pack(array[i], result, i * 4);\n  }\n  return result;\n};\n\n/**\n * Unpacks an array of cartesian components into and array of Cartesian4s.\n *\n * @param {Number[]} array The array of components to unpack.\n * @param {Cartesian4[]} [result] The array onto which to store the result.\n * @returns {Cartesian4[]} The unpacked array.\n */\nCartesian4.unpackArray = function (array, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  Check.typeOf.number.greaterThanOrEquals(\"array.length\", array.length, 4);\n  if (array.length % 4 !== 0) {\n    throw new DeveloperError(\"array length must be a multiple of 4.\");\n  }\n  //>>includeEnd('debug');\n\n  const length = array.length;\n  if (!defined(result)) {\n    result = new Array(length / 4);\n  } else {\n    result.length = length / 4;\n  }\n\n  for (let i = 0; i < length; i += 4) {\n    const index = i / 4;\n    result[index] = Cartesian4.unpack(array, i, result[index]);\n  }\n  return result;\n};\n\n/**\n * Creates a Cartesian4 from four consecutive elements in an array.\n * @function\n *\n * @param {Number[]} array The array whose four consecutive elements correspond to the x, y, z, and w components, respectively.\n * @param {Number} [startingIndex=0] The offset into the array of the first element, which corresponds to the x component.\n * @param {Cartesian4} [result] The object onto which to store the result.\n * @returns {Cartesian4}  The modified result parameter or a new Cartesian4 instance if one was not provided.\n *\n * @example\n * // Create a Cartesian4 with (1.0, 2.0, 3.0, 4.0)\n * const v = [1.0, 2.0, 3.0, 4.0];\n * const p = Cesium.Cartesian4.fromArray(v);\n *\n * // Create a Cartesian4 with (1.0, 2.0, 3.0, 4.0) using an offset into an array\n * const v2 = [0.0, 0.0, 1.0, 2.0, 3.0, 4.0];\n * const p2 = Cesium.Cartesian4.fromArray(v2, 2);\n */\nCartesian4.fromArray = Cartesian4.unpack;\n\n/**\n * Computes the value of the maximum component for the supplied Cartesian.\n *\n * @param {Cartesian4} cartesian The cartesian to use.\n * @returns {Number} The value of the maximum component.\n */\nCartesian4.maximumComponent = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return Math.max(cartesian.x, cartesian.y, cartesian.z, cartesian.w);\n};\n\n/**\n * Computes the value of the minimum component for the supplied Cartesian.\n *\n * @param {Cartesian4} cartesian The cartesian to use.\n * @returns {Number} The value of the minimum component.\n */\nCartesian4.minimumComponent = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return Math.min(cartesian.x, cartesian.y, cartesian.z, cartesian.w);\n};\n\n/**\n * Compares two Cartesians and computes a Cartesian which contains the minimum components of the supplied Cartesians.\n *\n * @param {Cartesian4} first A cartesian to compare.\n * @param {Cartesian4} second A cartesian to compare.\n * @param {Cartesian4} result The object into which to store the result.\n * @returns {Cartesian4} A cartesian with the minimum components.\n */\nCartesian4.minimumByComponent = function (first, second, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"first\", first);\n  Check.typeOf.object(\"second\", second);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.min(first.x, second.x);\n  result.y = Math.min(first.y, second.y);\n  result.z = Math.min(first.z, second.z);\n  result.w = Math.min(first.w, second.w);\n\n  return result;\n};\n\n/**\n * Compares two Cartesians and computes a Cartesian which contains the maximum components of the supplied Cartesians.\n *\n * @param {Cartesian4} first A cartesian to compare.\n * @param {Cartesian4} second A cartesian to compare.\n * @param {Cartesian4} result The object into which to store the result.\n * @returns {Cartesian4} A cartesian with the maximum components.\n */\nCartesian4.maximumByComponent = function (first, second, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"first\", first);\n  Check.typeOf.object(\"second\", second);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.max(first.x, second.x);\n  result.y = Math.max(first.y, second.y);\n  result.z = Math.max(first.z, second.z);\n  result.w = Math.max(first.w, second.w);\n\n  return result;\n};\n\n/**\n * Computes the provided Cartesian's squared magnitude.\n *\n * @param {Cartesian4} cartesian The Cartesian instance whose squared magnitude is to be computed.\n * @returns {Number} The squared magnitude.\n */\nCartesian4.magnitudeSquared = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return (\n    cartesian.x * cartesian.x +\n    cartesian.y * cartesian.y +\n    cartesian.z * cartesian.z +\n    cartesian.w * cartesian.w\n  );\n};\n\n/**\n * Computes the Cartesian's magnitude (length).\n *\n * @param {Cartesian4} cartesian The Cartesian instance whose magnitude is to be computed.\n * @returns {Number} The magnitude.\n */\nCartesian4.magnitude = function (cartesian) {\n  return Math.sqrt(Cartesian4.magnitudeSquared(cartesian));\n};\n\nconst distanceScratch = new Cartesian4();\n\n/**\n * Computes the 4-space distance between two points.\n *\n * @param {Cartesian4} left The first point to compute the distance from.\n * @param {Cartesian4} right The second point to compute the distance to.\n * @returns {Number} The distance between two points.\n *\n * @example\n * // Returns 1.0\n * const d = Cesium.Cartesian4.distance(\n *   new Cesium.Cartesian4(1.0, 0.0, 0.0, 0.0),\n *   new Cesium.Cartesian4(2.0, 0.0, 0.0, 0.0));\n */\nCartesian4.distance = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  Cartesian4.subtract(left, right, distanceScratch);\n  return Cartesian4.magnitude(distanceScratch);\n};\n\n/**\n * Computes the squared distance between two points.  Comparing squared distances\n * using this function is more efficient than comparing distances using {@link Cartesian4#distance}.\n *\n * @param {Cartesian4} left The first point to compute the distance from.\n * @param {Cartesian4} right The second point to compute the distance to.\n * @returns {Number} The distance between two points.\n *\n * @example\n * // Returns 4.0, not 2.0\n * const d = Cesium.Cartesian4.distance(\n *   new Cesium.Cartesian4(1.0, 0.0, 0.0, 0.0),\n *   new Cesium.Cartesian4(3.0, 0.0, 0.0, 0.0));\n */\nCartesian4.distanceSquared = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  Cartesian4.subtract(left, right, distanceScratch);\n  return Cartesian4.magnitudeSquared(distanceScratch);\n};\n\n/**\n * Computes the normalized form of the supplied Cartesian.\n *\n * @param {Cartesian4} cartesian The Cartesian to be normalized.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.normalize = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const magnitude = Cartesian4.magnitude(cartesian);\n\n  result.x = cartesian.x / magnitude;\n  result.y = cartesian.y / magnitude;\n  result.z = cartesian.z / magnitude;\n  result.w = cartesian.w / magnitude;\n\n  //>>includeStart('debug', pragmas.debug);\n  if (\n    isNaN(result.x) ||\n    isNaN(result.y) ||\n    isNaN(result.z) ||\n    isNaN(result.w)\n  ) {\n    throw new DeveloperError(\"normalized result is not a number\");\n  }\n  //>>includeEnd('debug');\n\n  return result;\n};\n\n/**\n * Computes the dot (scalar) product of two Cartesians.\n *\n * @param {Cartesian4} left The first Cartesian.\n * @param {Cartesian4} right The second Cartesian.\n * @returns {Number} The dot product.\n */\nCartesian4.dot = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  return (\n    left.x * right.x + left.y * right.y + left.z * right.z + left.w * right.w\n  );\n};\n\n/**\n * Computes the componentwise product of two Cartesians.\n *\n * @param {Cartesian4} left The first Cartesian.\n * @param {Cartesian4} right The second Cartesian.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.multiplyComponents = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x * right.x;\n  result.y = left.y * right.y;\n  result.z = left.z * right.z;\n  result.w = left.w * right.w;\n  return result;\n};\n\n/**\n * Computes the componentwise quotient of two Cartesians.\n *\n * @param {Cartesian4} left The first Cartesian.\n * @param {Cartesian4} right The second Cartesian.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.divideComponents = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x / right.x;\n  result.y = left.y / right.y;\n  result.z = left.z / right.z;\n  result.w = left.w / right.w;\n  return result;\n};\n\n/**\n * Computes the componentwise sum of two Cartesians.\n *\n * @param {Cartesian4} left The first Cartesian.\n * @param {Cartesian4} right The second Cartesian.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.add = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x + right.x;\n  result.y = left.y + right.y;\n  result.z = left.z + right.z;\n  result.w = left.w + right.w;\n  return result;\n};\n\n/**\n * Computes the componentwise difference of two Cartesians.\n *\n * @param {Cartesian4} left The first Cartesian.\n * @param {Cartesian4} right The second Cartesian.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.subtract = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x - right.x;\n  result.y = left.y - right.y;\n  result.z = left.z - right.z;\n  result.w = left.w - right.w;\n  return result;\n};\n\n/**\n * Multiplies the provided Cartesian componentwise by the provided scalar.\n *\n * @param {Cartesian4} cartesian The Cartesian to be scaled.\n * @param {Number} scalar The scalar to multiply with.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.multiplyByScalar = function (cartesian, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = cartesian.x * scalar;\n  result.y = cartesian.y * scalar;\n  result.z = cartesian.z * scalar;\n  result.w = cartesian.w * scalar;\n  return result;\n};\n\n/**\n * Divides the provided Cartesian componentwise by the provided scalar.\n *\n * @param {Cartesian4} cartesian The Cartesian to be divided.\n * @param {Number} scalar The scalar to divide by.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.divideByScalar = function (cartesian, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = cartesian.x / scalar;\n  result.y = cartesian.y / scalar;\n  result.z = cartesian.z / scalar;\n  result.w = cartesian.w / scalar;\n  return result;\n};\n\n/**\n * Negates the provided Cartesian.\n *\n * @param {Cartesian4} cartesian The Cartesian to be negated.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.negate = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = -cartesian.x;\n  result.y = -cartesian.y;\n  result.z = -cartesian.z;\n  result.w = -cartesian.w;\n  return result;\n};\n\n/**\n * Computes the absolute value of the provided Cartesian.\n *\n * @param {Cartesian4} cartesian The Cartesian whose absolute value is to be computed.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.abs = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.abs(cartesian.x);\n  result.y = Math.abs(cartesian.y);\n  result.z = Math.abs(cartesian.z);\n  result.w = Math.abs(cartesian.w);\n  return result;\n};\n\nconst lerpScratch = new Cartesian4();\n/**\n * Computes the linear interpolation or extrapolation at t using the provided cartesians.\n *\n * @param {Cartesian4} start The value corresponding to t at 0.0.\n * @param {Cartesian4}end The value corresponding to t at 1.0.\n * @param {Number} t The point along t at which to interpolate.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nCartesian4.lerp = function (start, end, t, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"start\", start);\n  Check.typeOf.object(\"end\", end);\n  Check.typeOf.number(\"t\", t);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  Cartesian4.multiplyByScalar(end, t, lerpScratch);\n  result = Cartesian4.multiplyByScalar(start, 1.0 - t, result);\n  return Cartesian4.add(lerpScratch, result, result);\n};\n\nconst mostOrthogonalAxisScratch = new Cartesian4();\n/**\n * Returns the axis that is most orthogonal to the provided Cartesian.\n *\n * @param {Cartesian4} cartesian The Cartesian on which to find the most orthogonal axis.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The most orthogonal axis.\n */\nCartesian4.mostOrthogonalAxis = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const f = Cartesian4.normalize(cartesian, mostOrthogonalAxisScratch);\n  Cartesian4.abs(f, f);\n\n  if (f.x <= f.y) {\n    if (f.x <= f.z) {\n      if (f.x <= f.w) {\n        result = Cartesian4.clone(Cartesian4.UNIT_X, result);\n      } else {\n        result = Cartesian4.clone(Cartesian4.UNIT_W, result);\n      }\n    } else if (f.z <= f.w) {\n      result = Cartesian4.clone(Cartesian4.UNIT_Z, result);\n    } else {\n      result = Cartesian4.clone(Cartesian4.UNIT_W, result);\n    }\n  } else if (f.y <= f.z) {\n    if (f.y <= f.w) {\n      result = Cartesian4.clone(Cartesian4.UNIT_Y, result);\n    } else {\n      result = Cartesian4.clone(Cartesian4.UNIT_W, result);\n    }\n  } else if (f.z <= f.w) {\n    result = Cartesian4.clone(Cartesian4.UNIT_Z, result);\n  } else {\n    result = Cartesian4.clone(Cartesian4.UNIT_W, result);\n  }\n\n  return result;\n};\n\n/**\n * Compares the provided Cartesians componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Cartesian4} [left] The first Cartesian.\n * @param {Cartesian4} [right] The second Cartesian.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nCartesian4.equals = function (left, right) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      left.x === right.x &&\n      left.y === right.y &&\n      left.z === right.z &&\n      left.w === right.w)\n  );\n};\n\n/**\n * @private\n */\nCartesian4.equalsArray = function (cartesian, array, offset) {\n  return (\n    cartesian.x === array[offset] &&\n    cartesian.y === array[offset + 1] &&\n    cartesian.z === array[offset + 2] &&\n    cartesian.w === array[offset + 3]\n  );\n};\n\n/**\n * Compares the provided Cartesians componentwise and returns\n * <code>true</code> if they pass an absolute or relative tolerance test,\n * <code>false</code> otherwise.\n *\n * @param {Cartesian4} [left] The first Cartesian.\n * @param {Cartesian4} [right] The second Cartesian.\n * @param {Number} [relativeEpsilon=0] The relative epsilon tolerance to use for equality testing.\n * @param {Number} [absoluteEpsilon=relativeEpsilon] The absolute epsilon tolerance to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n */\nCartesian4.equalsEpsilon = function (\n  left,\n  right,\n  relativeEpsilon,\n  absoluteEpsilon\n) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      CesiumMath.equalsEpsilon(\n        left.x,\n        right.x,\n        relativeEpsilon,\n        absoluteEpsilon\n      ) &&\n      CesiumMath.equalsEpsilon(\n        left.y,\n        right.y,\n        relativeEpsilon,\n        absoluteEpsilon\n      ) &&\n      CesiumMath.equalsEpsilon(\n        left.z,\n        right.z,\n        relativeEpsilon,\n        absoluteEpsilon\n      ) &&\n      CesiumMath.equalsEpsilon(\n        left.w,\n        right.w,\n        relativeEpsilon,\n        absoluteEpsilon\n      ))\n  );\n};\n\n/**\n * An immutable Cartesian4 instance initialized to (0.0, 0.0, 0.0, 0.0).\n *\n * @type {Cartesian4}\n * @constant\n */\nCartesian4.ZERO = Object.freeze(new Cartesian4(0.0, 0.0, 0.0, 0.0));\n\n/**\n * An immutable Cartesian4 instance initialized to (1.0, 1.0, 1.0, 1.0).\n *\n * @type {Cartesian4}\n * @constant\n */\nCartesian4.ONE = Object.freeze(new Cartesian4(1.0, 1.0, 1.0, 1.0));\n\n/**\n * An immutable Cartesian4 instance initialized to (1.0, 0.0, 0.0, 0.0).\n *\n * @type {Cartesian4}\n * @constant\n */\nCartesian4.UNIT_X = Object.freeze(new Cartesian4(1.0, 0.0, 0.0, 0.0));\n\n/**\n * An immutable Cartesian4 instance initialized to (0.0, 1.0, 0.0, 0.0).\n *\n * @type {Cartesian4}\n * @constant\n */\nCartesian4.UNIT_Y = Object.freeze(new Cartesian4(0.0, 1.0, 0.0, 0.0));\n\n/**\n * An immutable Cartesian4 instance initialized to (0.0, 0.0, 1.0, 0.0).\n *\n * @type {Cartesian4}\n * @constant\n */\nCartesian4.UNIT_Z = Object.freeze(new Cartesian4(0.0, 0.0, 1.0, 0.0));\n\n/**\n * An immutable Cartesian4 instance initialized to (0.0, 0.0, 0.0, 1.0).\n *\n * @type {Cartesian4}\n * @constant\n */\nCartesian4.UNIT_W = Object.freeze(new Cartesian4(0.0, 0.0, 0.0, 1.0));\n\n/**\n * Duplicates this Cartesian4 instance.\n *\n * @param {Cartesian4} [result] The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter or a new Cartesian4 instance if one was not provided.\n */\nCartesian4.prototype.clone = function (result) {\n  return Cartesian4.clone(this, result);\n};\n\n/**\n * Compares this Cartesian against the provided Cartesian componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Cartesian4} [right] The right hand side Cartesian.\n * @returns {Boolean} <code>true</code> if they are equal, <code>false</code> otherwise.\n */\nCartesian4.prototype.equals = function (right) {\n  return Cartesian4.equals(this, right);\n};\n\n/**\n * Compares this Cartesian against the provided Cartesian componentwise and returns\n * <code>true</code> if they pass an absolute or relative tolerance test,\n * <code>false</code> otherwise.\n *\n * @param {Cartesian4} [right] The right hand side Cartesian.\n * @param {Number} [relativeEpsilon=0] The relative epsilon tolerance to use for equality testing.\n * @param {Number} [absoluteEpsilon=relativeEpsilon] The absolute epsilon tolerance to use for equality testing.\n * @returns {Boolean} <code>true</code> if they are within the provided epsilon, <code>false</code> otherwise.\n */\nCartesian4.prototype.equalsEpsilon = function (\n  right,\n  relativeEpsilon,\n  absoluteEpsilon\n) {\n  return Cartesian4.equalsEpsilon(\n    this,\n    right,\n    relativeEpsilon,\n    absoluteEpsilon\n  );\n};\n\n/**\n * Creates a string representing this Cartesian in the format '(x, y, z, w)'.\n *\n * @returns {String} A string representing the provided Cartesian in the format '(x, y, z, w)'.\n */\nCartesian4.prototype.toString = function () {\n  return \"(\" + this.x + \", \" + this.y + \", \" + this.z + \", \" + this.w + \")\";\n};\n\n// scratchU8Array and scratchF32Array are views into the same buffer\nconst scratchF32Array = new Float32Array(1);\nconst scratchU8Array = new Uint8Array(scratchF32Array.buffer);\n\nconst testU32 = new Uint32Array([0x11223344]);\nconst testU8 = new Uint8Array(testU32.buffer);\nconst littleEndian = testU8[0] === 0x44;\n\n/**\n * Packs an arbitrary floating point value to 4 values representable using uint8.\n *\n * @param {Number} value A floating point number.\n * @param {Cartesian4} [result] The Cartesian4 that will contain the packed float.\n * @returns {Cartesian4} A Cartesian4 representing the float packed to values in x, y, z, and w.\n */\nCartesian4.packFloat = function (value, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"value\", value);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian4();\n  }\n\n  // scratchU8Array and scratchF32Array are views into the same buffer\n  scratchF32Array[0] = value;\n\n  if (littleEndian) {\n    result.x = scratchU8Array[0];\n    result.y = scratchU8Array[1];\n    result.z = scratchU8Array[2];\n    result.w = scratchU8Array[3];\n  } else {\n    // convert from big-endian to little-endian\n    result.x = scratchU8Array[3];\n    result.y = scratchU8Array[2];\n    result.z = scratchU8Array[1];\n    result.w = scratchU8Array[0];\n  }\n  return result;\n};\n\n/**\n * Unpacks a float packed using Cartesian4.packFloat.\n *\n * @param {Cartesian4} packedFloat A Cartesian4 containing a float packed to 4 values representable using uint8.\n * @returns {Number} The unpacked float.\n * @private\n */\nCartesian4.unpackFloat = function (packedFloat) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"packedFloat\", packedFloat);\n  //>>includeEnd('debug');\n\n  // scratchU8Array and scratchF32Array are views into the same buffer\n  if (littleEndian) {\n    scratchU8Array[0] = packedFloat.x;\n    scratchU8Array[1] = packedFloat.y;\n    scratchU8Array[2] = packedFloat.z;\n    scratchU8Array[3] = packedFloat.w;\n  } else {\n    // convert from little-endian to big-endian\n    scratchU8Array[0] = packedFloat.w;\n    scratchU8Array[1] = packedFloat.z;\n    scratchU8Array[2] = packedFloat.y;\n    scratchU8Array[3] = packedFloat.x;\n  }\n  return scratchF32Array[0];\n};\nexport default Cartesian4;\n", "import Cartesian3 from \"./Cartesian3.js\";\nimport Cartesian4 from \"./Cartesian4.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix3 from \"./Matrix3.js\";\nimport RuntimeError from \"./RuntimeError.js\";\n\n/**\n * A 4x4 matrix, indexable as a column-major order array.\n * Constructor parameters are in row-major order for code readability.\n * @alias Matrix4\n * @constructor\n * @implements {ArrayLike<number>}\n *\n * @param {Number} [column0Row0=0.0] The value for column 0, row 0.\n * @param {Number} [column1Row0=0.0] The value for column 1, row 0.\n * @param {Number} [column2Row0=0.0] The value for column 2, row 0.\n * @param {Number} [column3Row0=0.0] The value for column 3, row 0.\n * @param {Number} [column0Row1=0.0] The value for column 0, row 1.\n * @param {Number} [column1Row1=0.0] The value for column 1, row 1.\n * @param {Number} [column2Row1=0.0] The value for column 2, row 1.\n * @param {Number} [column3Row1=0.0] The value for column 3, row 1.\n * @param {Number} [column0Row2=0.0] The value for column 0, row 2.\n * @param {Number} [column1Row2=0.0] The value for column 1, row 2.\n * @param {Number} [column2Row2=0.0] The value for column 2, row 2.\n * @param {Number} [column3Row2=0.0] The value for column 3, row 2.\n * @param {Number} [column0Row3=0.0] The value for column 0, row 3.\n * @param {Number} [column1Row3=0.0] The value for column 1, row 3.\n * @param {Number} [column2Row3=0.0] The value for column 2, row 3.\n * @param {Number} [column3Row3=0.0] The value for column 3, row 3.\n *\n * @see Matrix4.fromColumnMajorArray\n * @see Matrix4.fromRowMajorArray\n * @see Matrix4.fromRotationTranslation\n * @see Matrix4.fromTranslationRotationScale\n * @see Matrix4.fromTranslationQuaternionRotationScale\n * @see Matrix4.fromTranslation\n * @see Matrix4.fromScale\n * @see Matrix4.fromUniformScale\n * @see Matrix4.fromCamera\n * @see Matrix4.computePerspectiveFieldOfView\n * @see Matrix4.computeOrthographicOffCenter\n * @see Matrix4.computePerspectiveOffCenter\n * @see Matrix4.computeInfinitePerspectiveOffCenter\n * @see Matrix4.computeViewportTransformation\n * @see Matrix4.computeView\n * @see Matrix2\n * @see Matrix3\n * @see Packable\n */\nfunction Matrix4(\n  column0Row0,\n  column1Row0,\n  column2Row0,\n  column3Row0,\n  column0Row1,\n  column1Row1,\n  column2Row1,\n  column3Row1,\n  column0Row2,\n  column1Row2,\n  column2Row2,\n  column3Row2,\n  column0Row3,\n  column1Row3,\n  column2Row3,\n  column3Row3\n) {\n  this[0] = defaultValue(column0Row0, 0.0);\n  this[1] = defaultValue(column0Row1, 0.0);\n  this[2] = defaultValue(column0Row2, 0.0);\n  this[3] = defaultValue(column0Row3, 0.0);\n  this[4] = defaultValue(column1Row0, 0.0);\n  this[5] = defaultValue(column1Row1, 0.0);\n  this[6] = defaultValue(column1Row2, 0.0);\n  this[7] = defaultValue(column1Row3, 0.0);\n  this[8] = defaultValue(column2Row0, 0.0);\n  this[9] = defaultValue(column2Row1, 0.0);\n  this[10] = defaultValue(column2Row2, 0.0);\n  this[11] = defaultValue(column2Row3, 0.0);\n  this[12] = defaultValue(column3Row0, 0.0);\n  this[13] = defaultValue(column3Row1, 0.0);\n  this[14] = defaultValue(column3Row2, 0.0);\n  this[15] = defaultValue(column3Row3, 0.0);\n}\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nMatrix4.packedLength = 16;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {Matrix4} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nMatrix4.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  array[startingIndex++] = value[0];\n  array[startingIndex++] = value[1];\n  array[startingIndex++] = value[2];\n  array[startingIndex++] = value[3];\n  array[startingIndex++] = value[4];\n  array[startingIndex++] = value[5];\n  array[startingIndex++] = value[6];\n  array[startingIndex++] = value[7];\n  array[startingIndex++] = value[8];\n  array[startingIndex++] = value[9];\n  array[startingIndex++] = value[10];\n  array[startingIndex++] = value[11];\n  array[startingIndex++] = value[12];\n  array[startingIndex++] = value[13];\n  array[startingIndex++] = value[14];\n  array[startingIndex] = value[15];\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {Matrix4} [result] The object into which to store the result.\n * @returns {Matrix4} The modified result parameter or a new Matrix4 instance if one was not provided.\n */\nMatrix4.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Matrix4();\n  }\n\n  result[0] = array[startingIndex++];\n  result[1] = array[startingIndex++];\n  result[2] = array[startingIndex++];\n  result[3] = array[startingIndex++];\n  result[4] = array[startingIndex++];\n  result[5] = array[startingIndex++];\n  result[6] = array[startingIndex++];\n  result[7] = array[startingIndex++];\n  result[8] = array[startingIndex++];\n  result[9] = array[startingIndex++];\n  result[10] = array[startingIndex++];\n  result[11] = array[startingIndex++];\n  result[12] = array[startingIndex++];\n  result[13] = array[startingIndex++];\n  result[14] = array[startingIndex++];\n  result[15] = array[startingIndex];\n  return result;\n};\n\n/**\n * Duplicates a Matrix4 instance.\n *\n * @param {Matrix4} matrix The matrix to duplicate.\n * @param {Matrix4} [result] The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter or a new Matrix4 instance if one was not provided. (Returns undefined if matrix is undefined)\n */\nMatrix4.clone = function (matrix, result) {\n  if (!defined(matrix)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    return new Matrix4(\n      matrix[0],\n      matrix[4],\n      matrix[8],\n      matrix[12],\n      matrix[1],\n      matrix[5],\n      matrix[9],\n      matrix[13],\n      matrix[2],\n      matrix[6],\n      matrix[10],\n      matrix[14],\n      matrix[3],\n      matrix[7],\n      matrix[11],\n      matrix[15]\n    );\n  }\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[3];\n  result[4] = matrix[4];\n  result[5] = matrix[5];\n  result[6] = matrix[6];\n  result[7] = matrix[7];\n  result[8] = matrix[8];\n  result[9] = matrix[9];\n  result[10] = matrix[10];\n  result[11] = matrix[11];\n  result[12] = matrix[12];\n  result[13] = matrix[13];\n  result[14] = matrix[14];\n  result[15] = matrix[15];\n  return result;\n};\n\n/**\n * Creates a Matrix4 from 16 consecutive elements in an array.\n * @function\n *\n * @param {Number[]} array The array whose 16 consecutive elements correspond to the positions of the matrix.  Assumes column-major order.\n * @param {Number} [startingIndex=0] The offset into the array of the first element, which corresponds to first column first row position in the matrix.\n * @param {Matrix4} [result] The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter or a new Matrix4 instance if one was not provided.\n *\n * @example\n * // Create the Matrix4:\n * // [1.0, 2.0, 3.0, 4.0]\n * // [1.0, 2.0, 3.0, 4.0]\n * // [1.0, 2.0, 3.0, 4.0]\n * // [1.0, 2.0, 3.0, 4.0]\n *\n * const v = [1.0, 1.0, 1.0, 1.0, 2.0, 2.0, 2.0, 2.0, 3.0, 3.0, 3.0, 3.0, 4.0, 4.0, 4.0, 4.0];\n * const m = Cesium.Matrix4.fromArray(v);\n *\n * // Create same Matrix4 with using an offset into an array\n * const v2 = [0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 2.0, 2.0, 2.0, 2.0, 3.0, 3.0, 3.0, 3.0, 4.0, 4.0, 4.0, 4.0];\n * const m2 = Cesium.Matrix4.fromArray(v2, 2);\n */\nMatrix4.fromArray = Matrix4.unpack;\n\n/**\n * Computes a Matrix4 instance from a column-major order array.\n *\n * @param {Number[]} values The column-major order array.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n */\nMatrix4.fromColumnMajorArray = function (values, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"values\", values);\n  //>>includeEnd('debug');\n\n  return Matrix4.clone(values, result);\n};\n\n/**\n * Computes a Matrix4 instance from a row-major order array.\n * The resulting matrix will be in column-major order.\n *\n * @param {Number[]} values The row-major order array.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n */\nMatrix4.fromRowMajorArray = function (values, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"values\", values);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix4(\n      values[0],\n      values[1],\n      values[2],\n      values[3],\n      values[4],\n      values[5],\n      values[6],\n      values[7],\n      values[8],\n      values[9],\n      values[10],\n      values[11],\n      values[12],\n      values[13],\n      values[14],\n      values[15]\n    );\n  }\n  result[0] = values[0];\n  result[1] = values[4];\n  result[2] = values[8];\n  result[3] = values[12];\n  result[4] = values[1];\n  result[5] = values[5];\n  result[6] = values[9];\n  result[7] = values[13];\n  result[8] = values[2];\n  result[9] = values[6];\n  result[10] = values[10];\n  result[11] = values[14];\n  result[12] = values[3];\n  result[13] = values[7];\n  result[14] = values[11];\n  result[15] = values[15];\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance from a Matrix3 representing the rotation\n * and a Cartesian3 representing the translation.\n *\n * @param {Matrix3} rotation The upper left portion of the matrix representing the rotation.\n * @param {Cartesian3} [translation=Cartesian3.ZERO] The upper right portion of the matrix representing the translation.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n */\nMatrix4.fromRotationTranslation = function (rotation, translation, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rotation\", rotation);\n  //>>includeEnd('debug');\n\n  translation = defaultValue(translation, Cartesian3.ZERO);\n\n  if (!defined(result)) {\n    return new Matrix4(\n      rotation[0],\n      rotation[3],\n      rotation[6],\n      translation.x,\n      rotation[1],\n      rotation[4],\n      rotation[7],\n      translation.y,\n      rotation[2],\n      rotation[5],\n      rotation[8],\n      translation.z,\n      0.0,\n      0.0,\n      0.0,\n      1.0\n    );\n  }\n\n  result[0] = rotation[0];\n  result[1] = rotation[1];\n  result[2] = rotation[2];\n  result[3] = 0.0;\n  result[4] = rotation[3];\n  result[5] = rotation[4];\n  result[6] = rotation[5];\n  result[7] = 0.0;\n  result[8] = rotation[6];\n  result[9] = rotation[7];\n  result[10] = rotation[8];\n  result[11] = 0.0;\n  result[12] = translation.x;\n  result[13] = translation.y;\n  result[14] = translation.z;\n  result[15] = 1.0;\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance from a translation, rotation, and scale (TRS)\n * representation with the rotation represented as a quaternion.\n *\n * @param {Cartesian3} translation The translation transformation.\n * @param {Quaternion} rotation The rotation transformation.\n * @param {Cartesian3} scale The non-uniform scale transformation.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n *\n * @example\n * const result = Cesium.Matrix4.fromTranslationQuaternionRotationScale(\n *   new Cesium.Cartesian3(1.0, 2.0, 3.0), // translation\n *   Cesium.Quaternion.IDENTITY,           // rotation\n *   new Cesium.Cartesian3(7.0, 8.0, 9.0), // scale\n *   result);\n */\nMatrix4.fromTranslationQuaternionRotationScale = function (\n  translation,\n  rotation,\n  scale,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"translation\", translation);\n  Check.typeOf.object(\"rotation\", rotation);\n  Check.typeOf.object(\"scale\", scale);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Matrix4();\n  }\n\n  const scaleX = scale.x;\n  const scaleY = scale.y;\n  const scaleZ = scale.z;\n\n  const x2 = rotation.x * rotation.x;\n  const xy = rotation.x * rotation.y;\n  const xz = rotation.x * rotation.z;\n  const xw = rotation.x * rotation.w;\n  const y2 = rotation.y * rotation.y;\n  const yz = rotation.y * rotation.z;\n  const yw = rotation.y * rotation.w;\n  const z2 = rotation.z * rotation.z;\n  const zw = rotation.z * rotation.w;\n  const w2 = rotation.w * rotation.w;\n\n  const m00 = x2 - y2 - z2 + w2;\n  const m01 = 2.0 * (xy - zw);\n  const m02 = 2.0 * (xz + yw);\n\n  const m10 = 2.0 * (xy + zw);\n  const m11 = -x2 + y2 - z2 + w2;\n  const m12 = 2.0 * (yz - xw);\n\n  const m20 = 2.0 * (xz - yw);\n  const m21 = 2.0 * (yz + xw);\n  const m22 = -x2 - y2 + z2 + w2;\n\n  result[0] = m00 * scaleX;\n  result[1] = m10 * scaleX;\n  result[2] = m20 * scaleX;\n  result[3] = 0.0;\n  result[4] = m01 * scaleY;\n  result[5] = m11 * scaleY;\n  result[6] = m21 * scaleY;\n  result[7] = 0.0;\n  result[8] = m02 * scaleZ;\n  result[9] = m12 * scaleZ;\n  result[10] = m22 * scaleZ;\n  result[11] = 0.0;\n  result[12] = translation.x;\n  result[13] = translation.y;\n  result[14] = translation.z;\n  result[15] = 1.0;\n\n  return result;\n};\n\n/**\n * Creates a Matrix4 instance from a {@link TranslationRotationScale} instance.\n *\n * @param {TranslationRotationScale} translationRotationScale The instance.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n */\nMatrix4.fromTranslationRotationScale = function (\n  translationRotationScale,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"translationRotationScale\", translationRotationScale);\n  //>>includeEnd('debug');\n\n  return Matrix4.fromTranslationQuaternionRotationScale(\n    translationRotationScale.translation,\n    translationRotationScale.rotation,\n    translationRotationScale.scale,\n    result\n  );\n};\n\n/**\n * Creates a Matrix4 instance from a Cartesian3 representing the translation.\n *\n * @param {Cartesian3} translation The upper right portion of the matrix representing the translation.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n *\n * @see Matrix4.multiplyByTranslation\n */\nMatrix4.fromTranslation = function (translation, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"translation\", translation);\n  //>>includeEnd('debug');\n\n  return Matrix4.fromRotationTranslation(Matrix3.IDENTITY, translation, result);\n};\n\n/**\n * Computes a Matrix4 instance representing a non-uniform scale.\n *\n * @param {Cartesian3} scale The x, y, and z scale factors.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n *\n * @example\n * // Creates\n * //   [7.0, 0.0, 0.0, 0.0]\n * //   [0.0, 8.0, 0.0, 0.0]\n * //   [0.0, 0.0, 9.0, 0.0]\n * //   [0.0, 0.0, 0.0, 1.0]\n * const m = Cesium.Matrix4.fromScale(new Cesium.Cartesian3(7.0, 8.0, 9.0));\n */\nMatrix4.fromScale = function (scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"scale\", scale);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix4(\n      scale.x,\n      0.0,\n      0.0,\n      0.0,\n      0.0,\n      scale.y,\n      0.0,\n      0.0,\n      0.0,\n      0.0,\n      scale.z,\n      0.0,\n      0.0,\n      0.0,\n      0.0,\n      1.0\n    );\n  }\n\n  result[0] = scale.x;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = 0.0;\n  result[5] = scale.y;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = 0.0;\n  result[9] = 0.0;\n  result[10] = scale.z;\n  result[11] = 0.0;\n  result[12] = 0.0;\n  result[13] = 0.0;\n  result[14] = 0.0;\n  result[15] = 1.0;\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance representing a uniform scale.\n *\n * @param {Number} scale The uniform scale factor.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n *\n * @example\n * // Creates\n * //   [2.0, 0.0, 0.0, 0.0]\n * //   [0.0, 2.0, 0.0, 0.0]\n * //   [0.0, 0.0, 2.0, 0.0]\n * //   [0.0, 0.0, 0.0, 1.0]\n * const m = Cesium.Matrix4.fromUniformScale(2.0);\n */\nMatrix4.fromUniformScale = function (scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"scale\", scale);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix4(\n      scale,\n      0.0,\n      0.0,\n      0.0,\n      0.0,\n      scale,\n      0.0,\n      0.0,\n      0.0,\n      0.0,\n      scale,\n      0.0,\n      0.0,\n      0.0,\n      0.0,\n      1.0\n    );\n  }\n\n  result[0] = scale;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = 0.0;\n  result[5] = scale;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = 0.0;\n  result[9] = 0.0;\n  result[10] = scale;\n  result[11] = 0.0;\n  result[12] = 0.0;\n  result[13] = 0.0;\n  result[14] = 0.0;\n  result[15] = 1.0;\n  return result;\n};\n\nconst fromCameraF = new Cartesian3();\nconst fromCameraR = new Cartesian3();\nconst fromCameraU = new Cartesian3();\n\n/**\n * Computes a Matrix4 instance from a Camera.\n *\n * @param {Camera} camera The camera to use.\n * @param {Matrix4} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix4} The modified result parameter, or a new Matrix4 instance if one was not provided.\n */\nMatrix4.fromCamera = function (camera, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"camera\", camera);\n  //>>includeEnd('debug');\n\n  const position = camera.position;\n  const direction = camera.direction;\n  const up = camera.up;\n\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"camera.position\", position);\n  Check.typeOf.object(\"camera.direction\", direction);\n  Check.typeOf.object(\"camera.up\", up);\n  //>>includeEnd('debug');\n\n  Cartesian3.normalize(direction, fromCameraF);\n  Cartesian3.normalize(\n    Cartesian3.cross(fromCameraF, up, fromCameraR),\n    fromCameraR\n  );\n  Cartesian3.normalize(\n    Cartesian3.cross(fromCameraR, fromCameraF, fromCameraU),\n    fromCameraU\n  );\n\n  const sX = fromCameraR.x;\n  const sY = fromCameraR.y;\n  const sZ = fromCameraR.z;\n  const fX = fromCameraF.x;\n  const fY = fromCameraF.y;\n  const fZ = fromCameraF.z;\n  const uX = fromCameraU.x;\n  const uY = fromCameraU.y;\n  const uZ = fromCameraU.z;\n  const positionX = position.x;\n  const positionY = position.y;\n  const positionZ = position.z;\n  const t0 = sX * -positionX + sY * -positionY + sZ * -positionZ;\n  const t1 = uX * -positionX + uY * -positionY + uZ * -positionZ;\n  const t2 = fX * positionX + fY * positionY + fZ * positionZ;\n\n  // The code below this comment is an optimized\n  // version of the commented lines.\n  // Rather that create two matrices and then multiply,\n  // we just bake in the multiplcation as part of creation.\n  // const rotation = new Matrix4(\n  //                 sX,  sY,  sZ, 0.0,\n  //                 uX,  uY,  uZ, 0.0,\n  //                -fX, -fY, -fZ, 0.0,\n  //                 0.0,  0.0,  0.0, 1.0);\n  // const translation = new Matrix4(\n  //                 1.0, 0.0, 0.0, -position.x,\n  //                 0.0, 1.0, 0.0, -position.y,\n  //                 0.0, 0.0, 1.0, -position.z,\n  //                 0.0, 0.0, 0.0, 1.0);\n  // return rotation.multiply(translation);\n  if (!defined(result)) {\n    return new Matrix4(\n      sX,\n      sY,\n      sZ,\n      t0,\n      uX,\n      uY,\n      uZ,\n      t1,\n      -fX,\n      -fY,\n      -fZ,\n      t2,\n      0.0,\n      0.0,\n      0.0,\n      1.0\n    );\n  }\n  result[0] = sX;\n  result[1] = uX;\n  result[2] = -fX;\n  result[3] = 0.0;\n  result[4] = sY;\n  result[5] = uY;\n  result[6] = -fY;\n  result[7] = 0.0;\n  result[8] = sZ;\n  result[9] = uZ;\n  result[10] = -fZ;\n  result[11] = 0.0;\n  result[12] = t0;\n  result[13] = t1;\n  result[14] = t2;\n  result[15] = 1.0;\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance representing a perspective transformation matrix.\n *\n * @param {Number} fovY The field of view along the Y axis in radians.\n * @param {Number} aspectRatio The aspect ratio.\n * @param {Number} near The distance to the near plane in meters.\n * @param {Number} far The distance to the far plane in meters.\n * @param {Matrix4} result The object in which the result will be stored.\n * @returns {Matrix4} The modified result parameter.\n *\n * @exception {DeveloperError} fovY must be in (0, PI].\n * @exception {DeveloperError} aspectRatio must be greater than zero.\n * @exception {DeveloperError} near must be greater than zero.\n * @exception {DeveloperError} far must be greater than zero.\n */\nMatrix4.computePerspectiveFieldOfView = function (\n  fovY,\n  aspectRatio,\n  near,\n  far,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number.greaterThan(\"fovY\", fovY, 0.0);\n  Check.typeOf.number.lessThan(\"fovY\", fovY, Math.PI);\n  Check.typeOf.number.greaterThan(\"near\", near, 0.0);\n  Check.typeOf.number.greaterThan(\"far\", far, 0.0);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const bottom = Math.tan(fovY * 0.5);\n\n  const column1Row1 = 1.0 / bottom;\n  const column0Row0 = column1Row1 / aspectRatio;\n  const column2Row2 = (far + near) / (near - far);\n  const column3Row2 = (2.0 * far * near) / (near - far);\n\n  result[0] = column0Row0;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = 0.0;\n  result[5] = column1Row1;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = 0.0;\n  result[9] = 0.0;\n  result[10] = column2Row2;\n  result[11] = -1.0;\n  result[12] = 0.0;\n  result[13] = 0.0;\n  result[14] = column3Row2;\n  result[15] = 0.0;\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance representing an orthographic transformation matrix.\n *\n * @param {Number} left The number of meters to the left of the camera that will be in view.\n * @param {Number} right The number of meters to the right of the camera that will be in view.\n * @param {Number} bottom The number of meters below of the camera that will be in view.\n * @param {Number} top The number of meters above of the camera that will be in view.\n * @param {Number} near The distance to the near plane in meters.\n * @param {Number} far The distance to the far plane in meters.\n * @param {Matrix4} result The object in which the result will be stored.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.computeOrthographicOffCenter = function (\n  left,\n  right,\n  bottom,\n  top,\n  near,\n  far,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"left\", left);\n  Check.typeOf.number(\"right\", right);\n  Check.typeOf.number(\"bottom\", bottom);\n  Check.typeOf.number(\"top\", top);\n  Check.typeOf.number(\"near\", near);\n  Check.typeOf.number(\"far\", far);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  let a = 1.0 / (right - left);\n  let b = 1.0 / (top - bottom);\n  let c = 1.0 / (far - near);\n\n  const tx = -(right + left) * a;\n  const ty = -(top + bottom) * b;\n  const tz = -(far + near) * c;\n  a *= 2.0;\n  b *= 2.0;\n  c *= -2.0;\n\n  result[0] = a;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = 0.0;\n  result[5] = b;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = 0.0;\n  result[9] = 0.0;\n  result[10] = c;\n  result[11] = 0.0;\n  result[12] = tx;\n  result[13] = ty;\n  result[14] = tz;\n  result[15] = 1.0;\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance representing an off center perspective transformation.\n *\n * @param {Number} left The number of meters to the left of the camera that will be in view.\n * @param {Number} right The number of meters to the right of the camera that will be in view.\n * @param {Number} bottom The number of meters below of the camera that will be in view.\n * @param {Number} top The number of meters above of the camera that will be in view.\n * @param {Number} near The distance to the near plane in meters.\n * @param {Number} far The distance to the far plane in meters.\n * @param {Matrix4} result The object in which the result will be stored.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.computePerspectiveOffCenter = function (\n  left,\n  right,\n  bottom,\n  top,\n  near,\n  far,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"left\", left);\n  Check.typeOf.number(\"right\", right);\n  Check.typeOf.number(\"bottom\", bottom);\n  Check.typeOf.number(\"top\", top);\n  Check.typeOf.number(\"near\", near);\n  Check.typeOf.number(\"far\", far);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const column0Row0 = (2.0 * near) / (right - left);\n  const column1Row1 = (2.0 * near) / (top - bottom);\n  const column2Row0 = (right + left) / (right - left);\n  const column2Row1 = (top + bottom) / (top - bottom);\n  const column2Row2 = -(far + near) / (far - near);\n  const column2Row3 = -1.0;\n  const column3Row2 = (-2.0 * far * near) / (far - near);\n\n  result[0] = column0Row0;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = 0.0;\n  result[5] = column1Row1;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = column2Row0;\n  result[9] = column2Row1;\n  result[10] = column2Row2;\n  result[11] = column2Row3;\n  result[12] = 0.0;\n  result[13] = 0.0;\n  result[14] = column3Row2;\n  result[15] = 0.0;\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance representing an infinite off center perspective transformation.\n *\n * @param {Number} left The number of meters to the left of the camera that will be in view.\n * @param {Number} right The number of meters to the right of the camera that will be in view.\n * @param {Number} bottom The number of meters below of the camera that will be in view.\n * @param {Number} top The number of meters above of the camera that will be in view.\n * @param {Number} near The distance to the near plane in meters.\n * @param {Matrix4} result The object in which the result will be stored.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.computeInfinitePerspectiveOffCenter = function (\n  left,\n  right,\n  bottom,\n  top,\n  near,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"left\", left);\n  Check.typeOf.number(\"right\", right);\n  Check.typeOf.number(\"bottom\", bottom);\n  Check.typeOf.number(\"top\", top);\n  Check.typeOf.number(\"near\", near);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const column0Row0 = (2.0 * near) / (right - left);\n  const column1Row1 = (2.0 * near) / (top - bottom);\n  const column2Row0 = (right + left) / (right - left);\n  const column2Row1 = (top + bottom) / (top - bottom);\n  const column2Row2 = -1.0;\n  const column2Row3 = -1.0;\n  const column3Row2 = -2.0 * near;\n\n  result[0] = column0Row0;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = 0.0;\n  result[5] = column1Row1;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = column2Row0;\n  result[9] = column2Row1;\n  result[10] = column2Row2;\n  result[11] = column2Row3;\n  result[12] = 0.0;\n  result[13] = 0.0;\n  result[14] = column3Row2;\n  result[15] = 0.0;\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance that transforms from normalized device coordinates to window coordinates.\n *\n * @param {Object} [viewport = { x : 0.0, y : 0.0, width : 0.0, height : 0.0 }] The viewport's corners as shown in Example 1.\n * @param {Number} [nearDepthRange=0.0] The near plane distance in window coordinates.\n * @param {Number} [farDepthRange=1.0] The far plane distance in window coordinates.\n * @param {Matrix4} [result] The object in which the result will be stored.\n * @returns {Matrix4} The modified result parameter.\n *\n * @example\n * // Create viewport transformation using an explicit viewport and depth range.\n * const m = Cesium.Matrix4.computeViewportTransformation({\n *     x : 0.0,\n *     y : 0.0,\n *     width : 1024.0,\n *     height : 768.0\n * }, 0.0, 1.0, new Cesium.Matrix4());\n */\nMatrix4.computeViewportTransformation = function (\n  viewport,\n  nearDepthRange,\n  farDepthRange,\n  result\n) {\n  if (!defined(result)) {\n    result = new Matrix4();\n  }\n\n  viewport = defaultValue(viewport, defaultValue.EMPTY_OBJECT);\n  const x = defaultValue(viewport.x, 0.0);\n  const y = defaultValue(viewport.y, 0.0);\n  const width = defaultValue(viewport.width, 0.0);\n  const height = defaultValue(viewport.height, 0.0);\n  nearDepthRange = defaultValue(nearDepthRange, 0.0);\n  farDepthRange = defaultValue(farDepthRange, 1.0);\n\n  const halfWidth = width * 0.5;\n  const halfHeight = height * 0.5;\n  const halfDepth = (farDepthRange - nearDepthRange) * 0.5;\n\n  const column0Row0 = halfWidth;\n  const column1Row1 = halfHeight;\n  const column2Row2 = halfDepth;\n  const column3Row0 = x + halfWidth;\n  const column3Row1 = y + halfHeight;\n  const column3Row2 = nearDepthRange + halfDepth;\n  const column3Row3 = 1.0;\n\n  result[0] = column0Row0;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = 0.0;\n  result[4] = 0.0;\n  result[5] = column1Row1;\n  result[6] = 0.0;\n  result[7] = 0.0;\n  result[8] = 0.0;\n  result[9] = 0.0;\n  result[10] = column2Row2;\n  result[11] = 0.0;\n  result[12] = column3Row0;\n  result[13] = column3Row1;\n  result[14] = column3Row2;\n  result[15] = column3Row3;\n  return result;\n};\n\n/**\n * Computes a Matrix4 instance that transforms from world space to view space.\n *\n * @param {Cartesian3} position The position of the camera.\n * @param {Cartesian3} direction The forward direction.\n * @param {Cartesian3} up The up direction.\n * @param {Cartesian3} right The right direction.\n * @param {Matrix4} result The object in which the result will be stored.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.computeView = function (position, direction, up, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"position\", position);\n  Check.typeOf.object(\"direction\", direction);\n  Check.typeOf.object(\"up\", up);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = right.x;\n  result[1] = up.x;\n  result[2] = -direction.x;\n  result[3] = 0.0;\n  result[4] = right.y;\n  result[5] = up.y;\n  result[6] = -direction.y;\n  result[7] = 0.0;\n  result[8] = right.z;\n  result[9] = up.z;\n  result[10] = -direction.z;\n  result[11] = 0.0;\n  result[12] = -Cartesian3.dot(right, position);\n  result[13] = -Cartesian3.dot(up, position);\n  result[14] = Cartesian3.dot(direction, position);\n  result[15] = 1.0;\n  return result;\n};\n\n/**\n * Computes an Array from the provided Matrix4 instance.\n * The array will be in column-major order.\n *\n * @param {Matrix4} matrix The matrix to use..\n * @param {Number[]} [result] The Array onto which to store the result.\n * @returns {Number[]} The modified Array parameter or a new Array instance if one was not provided.\n *\n * @example\n * //create an array from an instance of Matrix4\n * // m = [10.0, 14.0, 18.0, 22.0]\n * //     [11.0, 15.0, 19.0, 23.0]\n * //     [12.0, 16.0, 20.0, 24.0]\n * //     [13.0, 17.0, 21.0, 25.0]\n * const a = Cesium.Matrix4.toArray(m);\n *\n * // m remains the same\n * //creates a = [10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0]\n */\nMatrix4.toArray = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return [\n      matrix[0],\n      matrix[1],\n      matrix[2],\n      matrix[3],\n      matrix[4],\n      matrix[5],\n      matrix[6],\n      matrix[7],\n      matrix[8],\n      matrix[9],\n      matrix[10],\n      matrix[11],\n      matrix[12],\n      matrix[13],\n      matrix[14],\n      matrix[15],\n    ];\n  }\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[3];\n  result[4] = matrix[4];\n  result[5] = matrix[5];\n  result[6] = matrix[6];\n  result[7] = matrix[7];\n  result[8] = matrix[8];\n  result[9] = matrix[9];\n  result[10] = matrix[10];\n  result[11] = matrix[11];\n  result[12] = matrix[12];\n  result[13] = matrix[13];\n  result[14] = matrix[14];\n  result[15] = matrix[15];\n  return result;\n};\n\n/**\n * Computes the array index of the element at the provided row and column.\n *\n * @param {Number} row The zero-based index of the row.\n * @param {Number} column The zero-based index of the column.\n * @returns {Number} The index of the element at the provided row and column.\n *\n * @exception {DeveloperError} row must be 0, 1, 2, or 3.\n * @exception {DeveloperError} column must be 0, 1, 2, or 3.\n *\n * @example\n * const myMatrix = new Cesium.Matrix4();\n * const column1Row0Index = Cesium.Matrix4.getElementIndex(1, 0);\n * const column1Row0 = myMatrix[column1Row0Index];\n * myMatrix[column1Row0Index] = 10.0;\n */\nMatrix4.getElementIndex = function (column, row) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number.greaterThanOrEquals(\"row\", row, 0);\n  Check.typeOf.number.lessThanOrEquals(\"row\", row, 3);\n\n  Check.typeOf.number.greaterThanOrEquals(\"column\", column, 0);\n  Check.typeOf.number.lessThanOrEquals(\"column\", column, 3);\n  //>>includeEnd('debug');\n\n  return column * 4 + row;\n};\n\n/**\n * Retrieves a copy of the matrix column at the provided index as a Cartesian4 instance.\n *\n * @param {Matrix4} matrix The matrix to use.\n * @param {Number} index The zero-based index of the column to retrieve.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0, 1, 2, or 3.\n *\n * @example\n * //returns a Cartesian4 instance with values from the specified column\n * // m = [10.0, 11.0, 12.0, 13.0]\n * //     [14.0, 15.0, 16.0, 17.0]\n * //     [18.0, 19.0, 20.0, 21.0]\n * //     [22.0, 23.0, 24.0, 25.0]\n *\n * //Example 1: Creates an instance of Cartesian\n * const a = Cesium.Matrix4.getColumn(m, 2, new Cesium.Cartesian4());\n *\n * @example\n * //Example 2: Sets values for Cartesian instance\n * const a = new Cesium.Cartesian4();\n * Cesium.Matrix4.getColumn(m, 2, a);\n *\n * // a.x = 12.0; a.y = 16.0; a.z = 20.0; a.w = 24.0;\n */\nMatrix4.getColumn = function (matrix, index, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 3);\n\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const startIndex = index * 4;\n  const x = matrix[startIndex];\n  const y = matrix[startIndex + 1];\n  const z = matrix[startIndex + 2];\n  const w = matrix[startIndex + 3];\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  result.w = w;\n  return result;\n};\n\n/**\n * Computes a new matrix that replaces the specified column in the provided matrix with the provided Cartesian4 instance.\n *\n * @param {Matrix4} matrix The matrix to use.\n * @param {Number} index The zero-based index of the column to set.\n * @param {Cartesian4} cartesian The Cartesian whose values will be assigned to the specified column.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0, 1, 2, or 3.\n *\n * @example\n * //creates a new Matrix4 instance with new column values from the Cartesian4 instance\n * // m = [10.0, 11.0, 12.0, 13.0]\n * //     [14.0, 15.0, 16.0, 17.0]\n * //     [18.0, 19.0, 20.0, 21.0]\n * //     [22.0, 23.0, 24.0, 25.0]\n *\n * const a = Cesium.Matrix4.setColumn(m, 2, new Cesium.Cartesian4(99.0, 98.0, 97.0, 96.0), new Cesium.Matrix4());\n *\n * // m remains the same\n * // a = [10.0, 11.0, 99.0, 13.0]\n * //     [14.0, 15.0, 98.0, 17.0]\n * //     [18.0, 19.0, 97.0, 21.0]\n * //     [22.0, 23.0, 96.0, 25.0]\n */\nMatrix4.setColumn = function (matrix, index, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 3);\n\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result = Matrix4.clone(matrix, result);\n  const startIndex = index * 4;\n  result[startIndex] = cartesian.x;\n  result[startIndex + 1] = cartesian.y;\n  result[startIndex + 2] = cartesian.z;\n  result[startIndex + 3] = cartesian.w;\n  return result;\n};\n\n/**\n * Computes a new matrix that replaces the translation in the rightmost column of the provided\n * matrix with the provided translation. This assumes the matrix is an affine transformation.\n *\n * @param {Matrix4} matrix The matrix to use.\n * @param {Cartesian3} translation The translation that replaces the translation of the provided matrix.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.setTranslation = function (matrix, translation, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"translation\", translation);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[3];\n\n  result[4] = matrix[4];\n  result[5] = matrix[5];\n  result[6] = matrix[6];\n  result[7] = matrix[7];\n\n  result[8] = matrix[8];\n  result[9] = matrix[9];\n  result[10] = matrix[10];\n  result[11] = matrix[11];\n\n  result[12] = translation.x;\n  result[13] = translation.y;\n  result[14] = translation.z;\n  result[15] = matrix[15];\n\n  return result;\n};\n\nconst scaleScratch = new Cartesian3();\n/**\n * Computes a new matrix that replaces the scale with the provided scale.\n * This assumes the matrix is an affine transformation.\n *\n * @param {Matrix4} matrix The matrix to use.\n * @param {Cartesian3} scale The scale that replaces the scale of the provided matrix.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.setScale = function (matrix, scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"scale\", scale);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const existingScale = Matrix4.getScale(matrix, scaleScratch);\n  const newScale = Cartesian3.divideComponents(\n    scale,\n    existingScale,\n    scaleScratch\n  );\n  return Matrix4.multiplyByScale(matrix, newScale, result);\n};\n\n/**\n * Retrieves a copy of the matrix row at the provided index as a Cartesian4 instance.\n *\n * @param {Matrix4} matrix The matrix to use.\n * @param {Number} index The zero-based index of the row to retrieve.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0, 1, 2, or 3.\n *\n * @example\n * //returns a Cartesian4 instance with values from the specified column\n * // m = [10.0, 11.0, 12.0, 13.0]\n * //     [14.0, 15.0, 16.0, 17.0]\n * //     [18.0, 19.0, 20.0, 21.0]\n * //     [22.0, 23.0, 24.0, 25.0]\n *\n * //Example 1: Returns an instance of Cartesian\n * const a = Cesium.Matrix4.getRow(m, 2, new Cesium.Cartesian4());\n *\n * @example\n * //Example 2: Sets values for a Cartesian instance\n * const a = new Cesium.Cartesian4();\n * Cesium.Matrix4.getRow(m, 2, a);\n *\n * // a.x = 18.0; a.y = 19.0; a.z = 20.0; a.w = 21.0;\n */\nMatrix4.getRow = function (matrix, index, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 3);\n\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const x = matrix[index];\n  const y = matrix[index + 4];\n  const z = matrix[index + 8];\n  const w = matrix[index + 12];\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  result.w = w;\n  return result;\n};\n\n/**\n * Computes a new matrix that replaces the specified row in the provided matrix with the provided Cartesian4 instance.\n *\n * @param {Matrix4} matrix The matrix to use.\n * @param {Number} index The zero-based index of the row to set.\n * @param {Cartesian4} cartesian The Cartesian whose values will be assigned to the specified row.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0, 1, 2, or 3.\n *\n * @example\n * //create a new Matrix4 instance with new row values from the Cartesian4 instance\n * // m = [10.0, 11.0, 12.0, 13.0]\n * //     [14.0, 15.0, 16.0, 17.0]\n * //     [18.0, 19.0, 20.0, 21.0]\n * //     [22.0, 23.0, 24.0, 25.0]\n *\n * const a = Cesium.Matrix4.setRow(m, 2, new Cesium.Cartesian4(99.0, 98.0, 97.0, 96.0), new Cesium.Matrix4());\n *\n * // m remains the same\n * // a = [10.0, 11.0, 12.0, 13.0]\n * //     [14.0, 15.0, 16.0, 17.0]\n * //     [99.0, 98.0, 97.0, 96.0]\n * //     [22.0, 23.0, 24.0, 25.0]\n */\nMatrix4.setRow = function (matrix, index, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 3);\n\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result = Matrix4.clone(matrix, result);\n  result[index] = cartesian.x;\n  result[index + 4] = cartesian.y;\n  result[index + 8] = cartesian.z;\n  result[index + 12] = cartesian.w;\n  return result;\n};\n\nconst scratchColumn = new Cartesian3();\n\n/**\n * Extracts the non-uniform scale assuming the matrix is an affine transformation.\n *\n * @param {Matrix4} matrix The matrix.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter\n */\nMatrix4.getScale = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Cartesian3.magnitude(\n    Cartesian3.fromElements(matrix[0], matrix[1], matrix[2], scratchColumn)\n  );\n  result.y = Cartesian3.magnitude(\n    Cartesian3.fromElements(matrix[4], matrix[5], matrix[6], scratchColumn)\n  );\n  result.z = Cartesian3.magnitude(\n    Cartesian3.fromElements(matrix[8], matrix[9], matrix[10], scratchColumn)\n  );\n  return result;\n};\n\nconst scratchScale = new Cartesian3();\n\n/**\n * Computes the maximum scale assuming the matrix is an affine transformation.\n * The maximum scale is the maximum length of the column vectors in the upper-left\n * 3x3 matrix.\n *\n * @param {Matrix4} matrix The matrix.\n * @returns {Number} The maximum scale.\n */\nMatrix4.getMaximumScale = function (matrix) {\n  Matrix4.getScale(matrix, scratchScale);\n  return Cartesian3.maximumComponent(scratchScale);\n};\n\n/**\n * Computes the product of two matrices.\n *\n * @param {Matrix4} left The first matrix.\n * @param {Matrix4} right The second matrix.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.multiply = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const left0 = left[0];\n  const left1 = left[1];\n  const left2 = left[2];\n  const left3 = left[3];\n  const left4 = left[4];\n  const left5 = left[5];\n  const left6 = left[6];\n  const left7 = left[7];\n  const left8 = left[8];\n  const left9 = left[9];\n  const left10 = left[10];\n  const left11 = left[11];\n  const left12 = left[12];\n  const left13 = left[13];\n  const left14 = left[14];\n  const left15 = left[15];\n\n  const right0 = right[0];\n  const right1 = right[1];\n  const right2 = right[2];\n  const right3 = right[3];\n  const right4 = right[4];\n  const right5 = right[5];\n  const right6 = right[6];\n  const right7 = right[7];\n  const right8 = right[8];\n  const right9 = right[9];\n  const right10 = right[10];\n  const right11 = right[11];\n  const right12 = right[12];\n  const right13 = right[13];\n  const right14 = right[14];\n  const right15 = right[15];\n\n  const column0Row0 =\n    left0 * right0 + left4 * right1 + left8 * right2 + left12 * right3;\n  const column0Row1 =\n    left1 * right0 + left5 * right1 + left9 * right2 + left13 * right3;\n  const column0Row2 =\n    left2 * right0 + left6 * right1 + left10 * right2 + left14 * right3;\n  const column0Row3 =\n    left3 * right0 + left7 * right1 + left11 * right2 + left15 * right3;\n\n  const column1Row0 =\n    left0 * right4 + left4 * right5 + left8 * right6 + left12 * right7;\n  const column1Row1 =\n    left1 * right4 + left5 * right5 + left9 * right6 + left13 * right7;\n  const column1Row2 =\n    left2 * right4 + left6 * right5 + left10 * right6 + left14 * right7;\n  const column1Row3 =\n    left3 * right4 + left7 * right5 + left11 * right6 + left15 * right7;\n\n  const column2Row0 =\n    left0 * right8 + left4 * right9 + left8 * right10 + left12 * right11;\n  const column2Row1 =\n    left1 * right8 + left5 * right9 + left9 * right10 + left13 * right11;\n  const column2Row2 =\n    left2 * right8 + left6 * right9 + left10 * right10 + left14 * right11;\n  const column2Row3 =\n    left3 * right8 + left7 * right9 + left11 * right10 + left15 * right11;\n\n  const column3Row0 =\n    left0 * right12 + left4 * right13 + left8 * right14 + left12 * right15;\n  const column3Row1 =\n    left1 * right12 + left5 * right13 + left9 * right14 + left13 * right15;\n  const column3Row2 =\n    left2 * right12 + left6 * right13 + left10 * right14 + left14 * right15;\n  const column3Row3 =\n    left3 * right12 + left7 * right13 + left11 * right14 + left15 * right15;\n\n  result[0] = column0Row0;\n  result[1] = column0Row1;\n  result[2] = column0Row2;\n  result[3] = column0Row3;\n  result[4] = column1Row0;\n  result[5] = column1Row1;\n  result[6] = column1Row2;\n  result[7] = column1Row3;\n  result[8] = column2Row0;\n  result[9] = column2Row1;\n  result[10] = column2Row2;\n  result[11] = column2Row3;\n  result[12] = column3Row0;\n  result[13] = column3Row1;\n  result[14] = column3Row2;\n  result[15] = column3Row3;\n  return result;\n};\n\n/**\n * Computes the sum of two matrices.\n *\n * @param {Matrix4} left The first matrix.\n * @param {Matrix4} right The second matrix.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.add = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = left[0] + right[0];\n  result[1] = left[1] + right[1];\n  result[2] = left[2] + right[2];\n  result[3] = left[3] + right[3];\n  result[4] = left[4] + right[4];\n  result[5] = left[5] + right[5];\n  result[6] = left[6] + right[6];\n  result[7] = left[7] + right[7];\n  result[8] = left[8] + right[8];\n  result[9] = left[9] + right[9];\n  result[10] = left[10] + right[10];\n  result[11] = left[11] + right[11];\n  result[12] = left[12] + right[12];\n  result[13] = left[13] + right[13];\n  result[14] = left[14] + right[14];\n  result[15] = left[15] + right[15];\n  return result;\n};\n\n/**\n * Computes the difference of two matrices.\n *\n * @param {Matrix4} left The first matrix.\n * @param {Matrix4} right The second matrix.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.subtract = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = left[0] - right[0];\n  result[1] = left[1] - right[1];\n  result[2] = left[2] - right[2];\n  result[3] = left[3] - right[3];\n  result[4] = left[4] - right[4];\n  result[5] = left[5] - right[5];\n  result[6] = left[6] - right[6];\n  result[7] = left[7] - right[7];\n  result[8] = left[8] - right[8];\n  result[9] = left[9] - right[9];\n  result[10] = left[10] - right[10];\n  result[11] = left[11] - right[11];\n  result[12] = left[12] - right[12];\n  result[13] = left[13] - right[13];\n  result[14] = left[14] - right[14];\n  result[15] = left[15] - right[15];\n  return result;\n};\n\n/**\n * Computes the product of two matrices assuming the matrices are affine transformation matrices,\n * where the upper left 3x3 elements are any matrix, and\n * the upper three elements in the fourth column are the translation.\n * The bottom row is assumed to be [0, 0, 0, 1].\n * The matrix is not verified to be in the proper form.\n * This method is faster than computing the product for general 4x4\n * matrices using {@link Matrix4.multiply}.\n *\n * @param {Matrix4} left The first matrix.\n * @param {Matrix4} right The second matrix.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @example\n * const m1 = new Cesium.Matrix4(1.0, 6.0, 7.0, 0.0, 2.0, 5.0, 8.0, 0.0, 3.0, 4.0, 9.0, 0.0, 0.0, 0.0, 0.0, 1.0);\n * const m2 = Cesium.Transforms.eastNorthUpToFixedFrame(new Cesium.Cartesian3(1.0, 1.0, 1.0));\n * const m3 = Cesium.Matrix4.multiplyTransformation(m1, m2, new Cesium.Matrix4());\n */\nMatrix4.multiplyTransformation = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const left0 = left[0];\n  const left1 = left[1];\n  const left2 = left[2];\n  const left4 = left[4];\n  const left5 = left[5];\n  const left6 = left[6];\n  const left8 = left[8];\n  const left9 = left[9];\n  const left10 = left[10];\n  const left12 = left[12];\n  const left13 = left[13];\n  const left14 = left[14];\n\n  const right0 = right[0];\n  const right1 = right[1];\n  const right2 = right[2];\n  const right4 = right[4];\n  const right5 = right[5];\n  const right6 = right[6];\n  const right8 = right[8];\n  const right9 = right[9];\n  const right10 = right[10];\n  const right12 = right[12];\n  const right13 = right[13];\n  const right14 = right[14];\n\n  const column0Row0 = left0 * right0 + left4 * right1 + left8 * right2;\n  const column0Row1 = left1 * right0 + left5 * right1 + left9 * right2;\n  const column0Row2 = left2 * right0 + left6 * right1 + left10 * right2;\n\n  const column1Row0 = left0 * right4 + left4 * right5 + left8 * right6;\n  const column1Row1 = left1 * right4 + left5 * right5 + left9 * right6;\n  const column1Row2 = left2 * right4 + left6 * right5 + left10 * right6;\n\n  const column2Row0 = left0 * right8 + left4 * right9 + left8 * right10;\n  const column2Row1 = left1 * right8 + left5 * right9 + left9 * right10;\n  const column2Row2 = left2 * right8 + left6 * right9 + left10 * right10;\n\n  const column3Row0 =\n    left0 * right12 + left4 * right13 + left8 * right14 + left12;\n  const column3Row1 =\n    left1 * right12 + left5 * right13 + left9 * right14 + left13;\n  const column3Row2 =\n    left2 * right12 + left6 * right13 + left10 * right14 + left14;\n\n  result[0] = column0Row0;\n  result[1] = column0Row1;\n  result[2] = column0Row2;\n  result[3] = 0.0;\n  result[4] = column1Row0;\n  result[5] = column1Row1;\n  result[6] = column1Row2;\n  result[7] = 0.0;\n  result[8] = column2Row0;\n  result[9] = column2Row1;\n  result[10] = column2Row2;\n  result[11] = 0.0;\n  result[12] = column3Row0;\n  result[13] = column3Row1;\n  result[14] = column3Row2;\n  result[15] = 1.0;\n  return result;\n};\n\n/**\n * Multiplies a transformation matrix (with a bottom row of <code>[0.0, 0.0, 0.0, 1.0]</code>)\n * by a 3x3 rotation matrix.  This is an optimization\n * for <code>Matrix4.multiply(m, Matrix4.fromRotationTranslation(rotation), m);</code> with less allocations and arithmetic operations.\n *\n * @param {Matrix4} matrix The matrix on the left-hand side.\n * @param {Matrix3} rotation The 3x3 rotation matrix on the right-hand side.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @example\n * // Instead of Cesium.Matrix4.multiply(m, Cesium.Matrix4.fromRotationTranslation(rotation), m);\n * Cesium.Matrix4.multiplyByMatrix3(m, rotation, m);\n */\nMatrix4.multiplyByMatrix3 = function (matrix, rotation, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"rotation\", rotation);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const left0 = matrix[0];\n  const left1 = matrix[1];\n  const left2 = matrix[2];\n  const left4 = matrix[4];\n  const left5 = matrix[5];\n  const left6 = matrix[6];\n  const left8 = matrix[8];\n  const left9 = matrix[9];\n  const left10 = matrix[10];\n\n  const right0 = rotation[0];\n  const right1 = rotation[1];\n  const right2 = rotation[2];\n  const right4 = rotation[3];\n  const right5 = rotation[4];\n  const right6 = rotation[5];\n  const right8 = rotation[6];\n  const right9 = rotation[7];\n  const right10 = rotation[8];\n\n  const column0Row0 = left0 * right0 + left4 * right1 + left8 * right2;\n  const column0Row1 = left1 * right0 + left5 * right1 + left9 * right2;\n  const column0Row2 = left2 * right0 + left6 * right1 + left10 * right2;\n\n  const column1Row0 = left0 * right4 + left4 * right5 + left8 * right6;\n  const column1Row1 = left1 * right4 + left5 * right5 + left9 * right6;\n  const column1Row2 = left2 * right4 + left6 * right5 + left10 * right6;\n\n  const column2Row0 = left0 * right8 + left4 * right9 + left8 * right10;\n  const column2Row1 = left1 * right8 + left5 * right9 + left9 * right10;\n  const column2Row2 = left2 * right8 + left6 * right9 + left10 * right10;\n\n  result[0] = column0Row0;\n  result[1] = column0Row1;\n  result[2] = column0Row2;\n  result[3] = 0.0;\n  result[4] = column1Row0;\n  result[5] = column1Row1;\n  result[6] = column1Row2;\n  result[7] = 0.0;\n  result[8] = column2Row0;\n  result[9] = column2Row1;\n  result[10] = column2Row2;\n  result[11] = 0.0;\n  result[12] = matrix[12];\n  result[13] = matrix[13];\n  result[14] = matrix[14];\n  result[15] = matrix[15];\n  return result;\n};\n\n/**\n * Multiplies a transformation matrix (with a bottom row of <code>[0.0, 0.0, 0.0, 1.0]</code>)\n * by an implicit translation matrix defined by a {@link Cartesian3}.  This is an optimization\n * for <code>Matrix4.multiply(m, Matrix4.fromTranslation(position), m);</code> with less allocations and arithmetic operations.\n *\n * @param {Matrix4} matrix The matrix on the left-hand side.\n * @param {Cartesian3} translation The translation on the right-hand side.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @example\n * // Instead of Cesium.Matrix4.multiply(m, Cesium.Matrix4.fromTranslation(position), m);\n * Cesium.Matrix4.multiplyByTranslation(m, position, m);\n */\nMatrix4.multiplyByTranslation = function (matrix, translation, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"translation\", translation);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const x = translation.x;\n  const y = translation.y;\n  const z = translation.z;\n\n  const tx = x * matrix[0] + y * matrix[4] + z * matrix[8] + matrix[12];\n  const ty = x * matrix[1] + y * matrix[5] + z * matrix[9] + matrix[13];\n  const tz = x * matrix[2] + y * matrix[6] + z * matrix[10] + matrix[14];\n\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[3];\n  result[4] = matrix[4];\n  result[5] = matrix[5];\n  result[6] = matrix[6];\n  result[7] = matrix[7];\n  result[8] = matrix[8];\n  result[9] = matrix[9];\n  result[10] = matrix[10];\n  result[11] = matrix[11];\n  result[12] = tx;\n  result[13] = ty;\n  result[14] = tz;\n  result[15] = matrix[15];\n  return result;\n};\n\nconst uniformScaleScratch = new Cartesian3();\n\n/**\n * Multiplies an affine transformation matrix (with a bottom row of <code>[0.0, 0.0, 0.0, 1.0]</code>)\n * by an implicit uniform scale matrix.  This is an optimization\n * for <code>Matrix4.multiply(m, Matrix4.fromUniformScale(scale), m);</code>, where\n * <code>m</code> must be an affine matrix.\n * This function performs fewer allocations and arithmetic operations.\n *\n * @param {Matrix4} matrix The affine matrix on the left-hand side.\n * @param {Number} scale The uniform scale on the right-hand side.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n *\n * @example\n * // Instead of Cesium.Matrix4.multiply(m, Cesium.Matrix4.fromUniformScale(scale), m);\n * Cesium.Matrix4.multiplyByUniformScale(m, scale, m);\n *\n * @see Matrix4.fromUniformScale\n * @see Matrix4.multiplyByScale\n */\nMatrix4.multiplyByUniformScale = function (matrix, scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.number(\"scale\", scale);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  uniformScaleScratch.x = scale;\n  uniformScaleScratch.y = scale;\n  uniformScaleScratch.z = scale;\n  return Matrix4.multiplyByScale(matrix, uniformScaleScratch, result);\n};\n\n/**\n * Multiplies an affine transformation matrix (with a bottom row of <code>[0.0, 0.0, 0.0, 1.0]</code>)\n * by an implicit non-uniform scale matrix. This is an optimization\n * for <code>Matrix4.multiply(m, Matrix4.fromUniformScale(scale), m);</code>, where\n * <code>m</code> must be an affine matrix.\n * This function performs fewer allocations and arithmetic operations.\n *\n * @param {Matrix4} matrix The affine matrix on the left-hand side.\n * @param {Cartesian3} scale The non-uniform scale on the right-hand side.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n *\n * @example\n * // Instead of Cesium.Matrix4.multiply(m, Cesium.Matrix4.fromScale(scale), m);\n * Cesium.Matrix4.multiplyByScale(m, scale, m);\n *\n * @see Matrix4.fromScale\n * @see Matrix4.multiplyByUniformScale\n */\nMatrix4.multiplyByScale = function (matrix, scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"scale\", scale);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const scaleX = scale.x;\n  const scaleY = scale.y;\n  const scaleZ = scale.z;\n\n  // Faster than Cartesian3.equals\n  if (scaleX === 1.0 && scaleY === 1.0 && scaleZ === 1.0) {\n    return Matrix4.clone(matrix, result);\n  }\n\n  result[0] = scaleX * matrix[0];\n  result[1] = scaleX * matrix[1];\n  result[2] = scaleX * matrix[2];\n  result[3] = 0.0;\n  result[4] = scaleY * matrix[4];\n  result[5] = scaleY * matrix[5];\n  result[6] = scaleY * matrix[6];\n  result[7] = 0.0;\n  result[8] = scaleZ * matrix[8];\n  result[9] = scaleZ * matrix[9];\n  result[10] = scaleZ * matrix[10];\n  result[11] = 0.0;\n  result[12] = matrix[12];\n  result[13] = matrix[13];\n  result[14] = matrix[14];\n  result[15] = 1.0;\n  return result;\n};\n\n/**\n * Computes the product of a matrix and a column vector.\n *\n * @param {Matrix4} matrix The matrix.\n * @param {Cartesian4} cartesian The vector.\n * @param {Cartesian4} result The object onto which to store the result.\n * @returns {Cartesian4} The modified result parameter.\n */\nMatrix4.multiplyByVector = function (matrix, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const vX = cartesian.x;\n  const vY = cartesian.y;\n  const vZ = cartesian.z;\n  const vW = cartesian.w;\n\n  const x = matrix[0] * vX + matrix[4] * vY + matrix[8] * vZ + matrix[12] * vW;\n  const y = matrix[1] * vX + matrix[5] * vY + matrix[9] * vZ + matrix[13] * vW;\n  const z = matrix[2] * vX + matrix[6] * vY + matrix[10] * vZ + matrix[14] * vW;\n  const w = matrix[3] * vX + matrix[7] * vY + matrix[11] * vZ + matrix[15] * vW;\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  result.w = w;\n  return result;\n};\n\n/**\n * Computes the product of a matrix and a {@link Cartesian3}.  This is equivalent to calling {@link Matrix4.multiplyByVector}\n * with a {@link Cartesian4} with a <code>w</code> component of zero.\n *\n * @param {Matrix4} matrix The matrix.\n * @param {Cartesian3} cartesian The point.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n *\n * @example\n * const p = new Cesium.Cartesian3(1.0, 2.0, 3.0);\n * const result = Cesium.Matrix4.multiplyByPointAsVector(matrix, p, new Cesium.Cartesian3());\n * // A shortcut for\n * //   Cartesian3 p = ...\n * //   Cesium.Matrix4.multiplyByVector(matrix, new Cesium.Cartesian4(p.x, p.y, p.z, 0.0), result);\n */\nMatrix4.multiplyByPointAsVector = function (matrix, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const vX = cartesian.x;\n  const vY = cartesian.y;\n  const vZ = cartesian.z;\n\n  const x = matrix[0] * vX + matrix[4] * vY + matrix[8] * vZ;\n  const y = matrix[1] * vX + matrix[5] * vY + matrix[9] * vZ;\n  const z = matrix[2] * vX + matrix[6] * vY + matrix[10] * vZ;\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return result;\n};\n\n/**\n * Computes the product of a matrix and a {@link Cartesian3}. This is equivalent to calling {@link Matrix4.multiplyByVector}\n * with a {@link Cartesian4} with a <code>w</code> component of 1, but returns a {@link Cartesian3} instead of a {@link Cartesian4}.\n *\n * @param {Matrix4} matrix The matrix.\n * @param {Cartesian3} cartesian The point.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n *\n * @example\n * const p = new Cesium.Cartesian3(1.0, 2.0, 3.0);\n * const result = Cesium.Matrix4.multiplyByPoint(matrix, p, new Cesium.Cartesian3());\n */\nMatrix4.multiplyByPoint = function (matrix, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const vX = cartesian.x;\n  const vY = cartesian.y;\n  const vZ = cartesian.z;\n\n  const x = matrix[0] * vX + matrix[4] * vY + matrix[8] * vZ + matrix[12];\n  const y = matrix[1] * vX + matrix[5] * vY + matrix[9] * vZ + matrix[13];\n  const z = matrix[2] * vX + matrix[6] * vY + matrix[10] * vZ + matrix[14];\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return result;\n};\n\n/**\n * Computes the product of a matrix and a scalar.\n *\n * @param {Matrix4} matrix The matrix.\n * @param {Number} scalar The number to multiply by.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @example\n * //create a Matrix4 instance which is a scaled version of the supplied Matrix4\n * // m = [10.0, 11.0, 12.0, 13.0]\n * //     [14.0, 15.0, 16.0, 17.0]\n * //     [18.0, 19.0, 20.0, 21.0]\n * //     [22.0, 23.0, 24.0, 25.0]\n *\n * const a = Cesium.Matrix4.multiplyByScalar(m, -2, new Cesium.Matrix4());\n *\n * // m remains the same\n * // a = [-20.0, -22.0, -24.0, -26.0]\n * //     [-28.0, -30.0, -32.0, -34.0]\n * //     [-36.0, -38.0, -40.0, -42.0]\n * //     [-44.0, -46.0, -48.0, -50.0]\n */\nMatrix4.multiplyByScalar = function (matrix, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = matrix[0] * scalar;\n  result[1] = matrix[1] * scalar;\n  result[2] = matrix[2] * scalar;\n  result[3] = matrix[3] * scalar;\n  result[4] = matrix[4] * scalar;\n  result[5] = matrix[5] * scalar;\n  result[6] = matrix[6] * scalar;\n  result[7] = matrix[7] * scalar;\n  result[8] = matrix[8] * scalar;\n  result[9] = matrix[9] * scalar;\n  result[10] = matrix[10] * scalar;\n  result[11] = matrix[11] * scalar;\n  result[12] = matrix[12] * scalar;\n  result[13] = matrix[13] * scalar;\n  result[14] = matrix[14] * scalar;\n  result[15] = matrix[15] * scalar;\n  return result;\n};\n\n/**\n * Computes a negated copy of the provided matrix.\n *\n * @param {Matrix4} matrix The matrix to negate.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @example\n * //create a new Matrix4 instance which is a negation of a Matrix4\n * // m = [10.0, 11.0, 12.0, 13.0]\n * //     [14.0, 15.0, 16.0, 17.0]\n * //     [18.0, 19.0, 20.0, 21.0]\n * //     [22.0, 23.0, 24.0, 25.0]\n *\n * const a = Cesium.Matrix4.negate(m, new Cesium.Matrix4());\n *\n * // m remains the same\n * // a = [-10.0, -11.0, -12.0, -13.0]\n * //     [-14.0, -15.0, -16.0, -17.0]\n * //     [-18.0, -19.0, -20.0, -21.0]\n * //     [-22.0, -23.0, -24.0, -25.0]\n */\nMatrix4.negate = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = -matrix[0];\n  result[1] = -matrix[1];\n  result[2] = -matrix[2];\n  result[3] = -matrix[3];\n  result[4] = -matrix[4];\n  result[5] = -matrix[5];\n  result[6] = -matrix[6];\n  result[7] = -matrix[7];\n  result[8] = -matrix[8];\n  result[9] = -matrix[9];\n  result[10] = -matrix[10];\n  result[11] = -matrix[11];\n  result[12] = -matrix[12];\n  result[13] = -matrix[13];\n  result[14] = -matrix[14];\n  result[15] = -matrix[15];\n  return result;\n};\n\n/**\n * Computes the transpose of the provided matrix.\n *\n * @param {Matrix4} matrix The matrix to transpose.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @example\n * //returns transpose of a Matrix4\n * // m = [10.0, 11.0, 12.0, 13.0]\n * //     [14.0, 15.0, 16.0, 17.0]\n * //     [18.0, 19.0, 20.0, 21.0]\n * //     [22.0, 23.0, 24.0, 25.0]\n *\n * const a = Cesium.Matrix4.transpose(m, new Cesium.Matrix4());\n *\n * // m remains the same\n * // a = [10.0, 14.0, 18.0, 22.0]\n * //     [11.0, 15.0, 19.0, 23.0]\n * //     [12.0, 16.0, 20.0, 24.0]\n * //     [13.0, 17.0, 21.0, 25.0]\n */\nMatrix4.transpose = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const matrix1 = matrix[1];\n  const matrix2 = matrix[2];\n  const matrix3 = matrix[3];\n  const matrix6 = matrix[6];\n  const matrix7 = matrix[7];\n  const matrix11 = matrix[11];\n\n  result[0] = matrix[0];\n  result[1] = matrix[4];\n  result[2] = matrix[8];\n  result[3] = matrix[12];\n  result[4] = matrix1;\n  result[5] = matrix[5];\n  result[6] = matrix[9];\n  result[7] = matrix[13];\n  result[8] = matrix2;\n  result[9] = matrix6;\n  result[10] = matrix[10];\n  result[11] = matrix[14];\n  result[12] = matrix3;\n  result[13] = matrix7;\n  result[14] = matrix11;\n  result[15] = matrix[15];\n  return result;\n};\n\n/**\n * Computes a matrix, which contains the absolute (unsigned) values of the provided matrix's elements.\n *\n * @param {Matrix4} matrix The matrix with signed elements.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.abs = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = Math.abs(matrix[0]);\n  result[1] = Math.abs(matrix[1]);\n  result[2] = Math.abs(matrix[2]);\n  result[3] = Math.abs(matrix[3]);\n  result[4] = Math.abs(matrix[4]);\n  result[5] = Math.abs(matrix[5]);\n  result[6] = Math.abs(matrix[6]);\n  result[7] = Math.abs(matrix[7]);\n  result[8] = Math.abs(matrix[8]);\n  result[9] = Math.abs(matrix[9]);\n  result[10] = Math.abs(matrix[10]);\n  result[11] = Math.abs(matrix[11]);\n  result[12] = Math.abs(matrix[12]);\n  result[13] = Math.abs(matrix[13]);\n  result[14] = Math.abs(matrix[14]);\n  result[15] = Math.abs(matrix[15]);\n\n  return result;\n};\n\n/**\n * Compares the provided matrices componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Matrix4} [left] The first matrix.\n * @param {Matrix4} [right] The second matrix.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n *\n * @example\n * //compares two Matrix4 instances\n *\n * // a = [10.0, 14.0, 18.0, 22.0]\n * //     [11.0, 15.0, 19.0, 23.0]\n * //     [12.0, 16.0, 20.0, 24.0]\n * //     [13.0, 17.0, 21.0, 25.0]\n *\n * // b = [10.0, 14.0, 18.0, 22.0]\n * //     [11.0, 15.0, 19.0, 23.0]\n * //     [12.0, 16.0, 20.0, 24.0]\n * //     [13.0, 17.0, 21.0, 25.0]\n *\n * if(Cesium.Matrix4.equals(a,b)) {\n *      console.log(\"Both matrices are equal\");\n * } else {\n *      console.log(\"They are not equal\");\n * }\n *\n * //Prints \"Both matrices are equal\" on the console\n */\nMatrix4.equals = function (left, right) {\n  // Given that most matrices will be transformation matrices, the elements\n  // are tested in order such that the test is likely to fail as early\n  // as possible.  I _think_ this is just as friendly to the L1 cache\n  // as testing in index order.  It is certainty faster in practice.\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      // Translation\n      left[12] === right[12] &&\n      left[13] === right[13] &&\n      left[14] === right[14] &&\n      // Rotation/scale\n      left[0] === right[0] &&\n      left[1] === right[1] &&\n      left[2] === right[2] &&\n      left[4] === right[4] &&\n      left[5] === right[5] &&\n      left[6] === right[6] &&\n      left[8] === right[8] &&\n      left[9] === right[9] &&\n      left[10] === right[10] &&\n      // Bottom row\n      left[3] === right[3] &&\n      left[7] === right[7] &&\n      left[11] === right[11] &&\n      left[15] === right[15])\n  );\n};\n\n/**\n * Compares the provided matrices componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Matrix4} [left] The first matrix.\n * @param {Matrix4} [right] The second matrix.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n *\n * @example\n * //compares two Matrix4 instances\n *\n * // a = [10.5, 14.5, 18.5, 22.5]\n * //     [11.5, 15.5, 19.5, 23.5]\n * //     [12.5, 16.5, 20.5, 24.5]\n * //     [13.5, 17.5, 21.5, 25.5]\n *\n * // b = [10.0, 14.0, 18.0, 22.0]\n * //     [11.0, 15.0, 19.0, 23.0]\n * //     [12.0, 16.0, 20.0, 24.0]\n * //     [13.0, 17.0, 21.0, 25.0]\n *\n * if(Cesium.Matrix4.equalsEpsilon(a,b,0.1)){\n *      console.log(\"Difference between both the matrices is less than 0.1\");\n * } else {\n *      console.log(\"Difference between both the matrices is not less than 0.1\");\n * }\n *\n * //Prints \"Difference between both the matrices is not less than 0.1\" on the console\n */\nMatrix4.equalsEpsilon = function (left, right, epsilon) {\n  epsilon = defaultValue(epsilon, 0);\n\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      Math.abs(left[0] - right[0]) <= epsilon &&\n      Math.abs(left[1] - right[1]) <= epsilon &&\n      Math.abs(left[2] - right[2]) <= epsilon &&\n      Math.abs(left[3] - right[3]) <= epsilon &&\n      Math.abs(left[4] - right[4]) <= epsilon &&\n      Math.abs(left[5] - right[5]) <= epsilon &&\n      Math.abs(left[6] - right[6]) <= epsilon &&\n      Math.abs(left[7] - right[7]) <= epsilon &&\n      Math.abs(left[8] - right[8]) <= epsilon &&\n      Math.abs(left[9] - right[9]) <= epsilon &&\n      Math.abs(left[10] - right[10]) <= epsilon &&\n      Math.abs(left[11] - right[11]) <= epsilon &&\n      Math.abs(left[12] - right[12]) <= epsilon &&\n      Math.abs(left[13] - right[13]) <= epsilon &&\n      Math.abs(left[14] - right[14]) <= epsilon &&\n      Math.abs(left[15] - right[15]) <= epsilon)\n  );\n};\n\n/**\n * Gets the translation portion of the provided matrix, assuming the matrix is an affine transformation matrix.\n *\n * @param {Matrix4} matrix The matrix to use.\n * @param {Cartesian3} result The object onto which to store the result.\n * @returns {Cartesian3} The modified result parameter.\n */\nMatrix4.getTranslation = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = matrix[12];\n  result.y = matrix[13];\n  result.z = matrix[14];\n  return result;\n};\n\n/**\n * Gets the upper left 3x3 matrix of the provided matrix.\n *\n * @param {Matrix4} matrix The matrix to use.\n * @param {Matrix3} result The object onto which to store the result.\n * @returns {Matrix3} The modified result parameter.\n *\n * @example\n * // returns a Matrix3 instance from a Matrix4 instance\n *\n * // m = [10.0, 14.0, 18.0, 22.0]\n * //     [11.0, 15.0, 19.0, 23.0]\n * //     [12.0, 16.0, 20.0, 24.0]\n * //     [13.0, 17.0, 21.0, 25.0]\n *\n * const b = new Cesium.Matrix3();\n * Cesium.Matrix4.getMatrix3(m,b);\n *\n * // b = [10.0, 14.0, 18.0]\n * //     [11.0, 15.0, 19.0]\n * //     [12.0, 16.0, 20.0]\n */\nMatrix4.getMatrix3 = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[4];\n  result[4] = matrix[5];\n  result[5] = matrix[6];\n  result[6] = matrix[8];\n  result[7] = matrix[9];\n  result[8] = matrix[10];\n  return result;\n};\n\nconst scratchInverseRotation = new Matrix3();\nconst scratchMatrix3Zero = new Matrix3();\nconst scratchBottomRow = new Cartesian4();\nconst scratchExpectedBottomRow = new Cartesian4(0.0, 0.0, 0.0, 1.0);\n\n/**\n * Computes the inverse of the provided matrix using Cramers Rule.\n * If the determinant is zero, the matrix can not be inverted, and an exception is thrown.\n * If the matrix is a proper rigid transformation, it is more efficient\n * to invert it with {@link Matrix4.inverseTransformation}.\n *\n * @param {Matrix4} matrix The matrix to invert.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n *\n * @exception {RuntimeError} matrix is not invertible because its determinate is zero.\n */\nMatrix4.inverse = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n  //\n  // Ported from:\n  //   ftp://download.intel.com/design/PentiumIII/sml/24504301.pdf\n  //\n  const src0 = matrix[0];\n  const src1 = matrix[4];\n  const src2 = matrix[8];\n  const src3 = matrix[12];\n  const src4 = matrix[1];\n  const src5 = matrix[5];\n  const src6 = matrix[9];\n  const src7 = matrix[13];\n  const src8 = matrix[2];\n  const src9 = matrix[6];\n  const src10 = matrix[10];\n  const src11 = matrix[14];\n  const src12 = matrix[3];\n  const src13 = matrix[7];\n  const src14 = matrix[11];\n  const src15 = matrix[15];\n\n  // calculate pairs for first 8 elements (cofactors)\n  let tmp0 = src10 * src15;\n  let tmp1 = src11 * src14;\n  let tmp2 = src9 * src15;\n  let tmp3 = src11 * src13;\n  let tmp4 = src9 * src14;\n  let tmp5 = src10 * src13;\n  let tmp6 = src8 * src15;\n  let tmp7 = src11 * src12;\n  let tmp8 = src8 * src14;\n  let tmp9 = src10 * src12;\n  let tmp10 = src8 * src13;\n  let tmp11 = src9 * src12;\n\n  // calculate first 8 elements (cofactors)\n  const dst0 =\n    tmp0 * src5 +\n    tmp3 * src6 +\n    tmp4 * src7 -\n    (tmp1 * src5 + tmp2 * src6 + tmp5 * src7);\n  const dst1 =\n    tmp1 * src4 +\n    tmp6 * src6 +\n    tmp9 * src7 -\n    (tmp0 * src4 + tmp7 * src6 + tmp8 * src7);\n  const dst2 =\n    tmp2 * src4 +\n    tmp7 * src5 +\n    tmp10 * src7 -\n    (tmp3 * src4 + tmp6 * src5 + tmp11 * src7);\n  const dst3 =\n    tmp5 * src4 +\n    tmp8 * src5 +\n    tmp11 * src6 -\n    (tmp4 * src4 + tmp9 * src5 + tmp10 * src6);\n  const dst4 =\n    tmp1 * src1 +\n    tmp2 * src2 +\n    tmp5 * src3 -\n    (tmp0 * src1 + tmp3 * src2 + tmp4 * src3);\n  const dst5 =\n    tmp0 * src0 +\n    tmp7 * src2 +\n    tmp8 * src3 -\n    (tmp1 * src0 + tmp6 * src2 + tmp9 * src3);\n  const dst6 =\n    tmp3 * src0 +\n    tmp6 * src1 +\n    tmp11 * src3 -\n    (tmp2 * src0 + tmp7 * src1 + tmp10 * src3);\n  const dst7 =\n    tmp4 * src0 +\n    tmp9 * src1 +\n    tmp10 * src2 -\n    (tmp5 * src0 + tmp8 * src1 + tmp11 * src2);\n\n  // calculate pairs for second 8 elements (cofactors)\n  tmp0 = src2 * src7;\n  tmp1 = src3 * src6;\n  tmp2 = src1 * src7;\n  tmp3 = src3 * src5;\n  tmp4 = src1 * src6;\n  tmp5 = src2 * src5;\n  tmp6 = src0 * src7;\n  tmp7 = src3 * src4;\n  tmp8 = src0 * src6;\n  tmp9 = src2 * src4;\n  tmp10 = src0 * src5;\n  tmp11 = src1 * src4;\n\n  // calculate second 8 elements (cofactors)\n  const dst8 =\n    tmp0 * src13 +\n    tmp3 * src14 +\n    tmp4 * src15 -\n    (tmp1 * src13 + tmp2 * src14 + tmp5 * src15);\n  const dst9 =\n    tmp1 * src12 +\n    tmp6 * src14 +\n    tmp9 * src15 -\n    (tmp0 * src12 + tmp7 * src14 + tmp8 * src15);\n  const dst10 =\n    tmp2 * src12 +\n    tmp7 * src13 +\n    tmp10 * src15 -\n    (tmp3 * src12 + tmp6 * src13 + tmp11 * src15);\n  const dst11 =\n    tmp5 * src12 +\n    tmp8 * src13 +\n    tmp11 * src14 -\n    (tmp4 * src12 + tmp9 * src13 + tmp10 * src14);\n  const dst12 =\n    tmp2 * src10 +\n    tmp5 * src11 +\n    tmp1 * src9 -\n    (tmp4 * src11 + tmp0 * src9 + tmp3 * src10);\n  const dst13 =\n    tmp8 * src11 +\n    tmp0 * src8 +\n    tmp7 * src10 -\n    (tmp6 * src10 + tmp9 * src11 + tmp1 * src8);\n  const dst14 =\n    tmp6 * src9 +\n    tmp11 * src11 +\n    tmp3 * src8 -\n    (tmp10 * src11 + tmp2 * src8 + tmp7 * src9);\n  const dst15 =\n    tmp10 * src10 +\n    tmp4 * src8 +\n    tmp9 * src9 -\n    (tmp8 * src9 + tmp11 * src10 + tmp5 * src8);\n\n  // calculate determinant\n  let det = src0 * dst0 + src1 * dst1 + src2 * dst2 + src3 * dst3;\n\n  if (Math.abs(det) < CesiumMath.EPSILON21) {\n    // Special case for a zero scale matrix that can occur, for example,\n    // when a model's node has a [0, 0, 0] scale.\n    if (\n      Matrix3.equalsEpsilon(\n        Matrix4.getMatrix3(matrix, scratchInverseRotation),\n        scratchMatrix3Zero,\n        CesiumMath.EPSILON7\n      ) &&\n      Cartesian4.equals(\n        Matrix4.getRow(matrix, 3, scratchBottomRow),\n        scratchExpectedBottomRow\n      )\n    ) {\n      result[0] = 0.0;\n      result[1] = 0.0;\n      result[2] = 0.0;\n      result[3] = 0.0;\n      result[4] = 0.0;\n      result[5] = 0.0;\n      result[6] = 0.0;\n      result[7] = 0.0;\n      result[8] = 0.0;\n      result[9] = 0.0;\n      result[10] = 0.0;\n      result[11] = 0.0;\n      result[12] = -matrix[12];\n      result[13] = -matrix[13];\n      result[14] = -matrix[14];\n      result[15] = 1.0;\n      return result;\n    }\n\n    throw new RuntimeError(\n      \"matrix is not invertible because its determinate is zero.\"\n    );\n  }\n\n  // calculate matrix inverse\n  det = 1.0 / det;\n\n  result[0] = dst0 * det;\n  result[1] = dst1 * det;\n  result[2] = dst2 * det;\n  result[3] = dst3 * det;\n  result[4] = dst4 * det;\n  result[5] = dst5 * det;\n  result[6] = dst6 * det;\n  result[7] = dst7 * det;\n  result[8] = dst8 * det;\n  result[9] = dst9 * det;\n  result[10] = dst10 * det;\n  result[11] = dst11 * det;\n  result[12] = dst12 * det;\n  result[13] = dst13 * det;\n  result[14] = dst14 * det;\n  result[15] = dst15 * det;\n  return result;\n};\n\n/**\n * Computes the inverse of the provided matrix assuming it is a proper rigid matrix,\n * where the upper left 3x3 elements are a rotation matrix,\n * and the upper three elements in the fourth column are the translation.\n * The bottom row is assumed to be [0, 0, 0, 1].\n * The matrix is not verified to be in the proper form.\n * This method is faster than computing the inverse for a general 4x4\n * matrix using {@link Matrix4.inverse}.\n *\n * @param {Matrix4} matrix The matrix to invert.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.inverseTransformation = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  //This function is an optimized version of the below 4 lines.\n  //const rT = Matrix3.transpose(Matrix4.getMatrix3(matrix));\n  //const rTN = Matrix3.negate(rT);\n  //const rTT = Matrix3.multiplyByVector(rTN, Matrix4.getTranslation(matrix));\n  //return Matrix4.fromRotationTranslation(rT, rTT, result);\n\n  const matrix0 = matrix[0];\n  const matrix1 = matrix[1];\n  const matrix2 = matrix[2];\n  const matrix4 = matrix[4];\n  const matrix5 = matrix[5];\n  const matrix6 = matrix[6];\n  const matrix8 = matrix[8];\n  const matrix9 = matrix[9];\n  const matrix10 = matrix[10];\n\n  const vX = matrix[12];\n  const vY = matrix[13];\n  const vZ = matrix[14];\n\n  const x = -matrix0 * vX - matrix1 * vY - matrix2 * vZ;\n  const y = -matrix4 * vX - matrix5 * vY - matrix6 * vZ;\n  const z = -matrix8 * vX - matrix9 * vY - matrix10 * vZ;\n\n  result[0] = matrix0;\n  result[1] = matrix4;\n  result[2] = matrix8;\n  result[3] = 0.0;\n  result[4] = matrix1;\n  result[5] = matrix5;\n  result[6] = matrix9;\n  result[7] = 0.0;\n  result[8] = matrix2;\n  result[9] = matrix6;\n  result[10] = matrix10;\n  result[11] = 0.0;\n  result[12] = x;\n  result[13] = y;\n  result[14] = z;\n  result[15] = 1.0;\n  return result;\n};\n\nconst scratchTransposeMatrix = new Matrix4();\n\n/**\n * Computes the inverse transpose of a matrix.\n *\n * @param {Matrix4} matrix The matrix to transpose and invert.\n * @param {Matrix4} result The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter.\n */\nMatrix4.inverseTranspose = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  return Matrix4.inverse(\n    Matrix4.transpose(matrix, scratchTransposeMatrix),\n    result\n  );\n};\n\n/**\n * An immutable Matrix4 instance initialized to the identity matrix.\n *\n * @type {Matrix4}\n * @constant\n */\nMatrix4.IDENTITY = Object.freeze(\n  new Matrix4(\n    1.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    1.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    1.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    1.0\n  )\n);\n\n/**\n * An immutable Matrix4 instance initialized to the zero matrix.\n *\n * @type {Matrix4}\n * @constant\n */\nMatrix4.ZERO = Object.freeze(\n  new Matrix4(\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0,\n    0.0\n  )\n);\n\n/**\n * The index into Matrix4 for column 0, row 0.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN0ROW0 = 0;\n\n/**\n * The index into Matrix4 for column 0, row 1.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN0ROW1 = 1;\n\n/**\n * The index into Matrix4 for column 0, row 2.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN0ROW2 = 2;\n\n/**\n * The index into Matrix4 for column 0, row 3.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN0ROW3 = 3;\n\n/**\n * The index into Matrix4 for column 1, row 0.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN1ROW0 = 4;\n\n/**\n * The index into Matrix4 for column 1, row 1.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN1ROW1 = 5;\n\n/**\n * The index into Matrix4 for column 1, row 2.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN1ROW2 = 6;\n\n/**\n * The index into Matrix4 for column 1, row 3.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN1ROW3 = 7;\n\n/**\n * The index into Matrix4 for column 2, row 0.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN2ROW0 = 8;\n\n/**\n * The index into Matrix4 for column 2, row 1.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN2ROW1 = 9;\n\n/**\n * The index into Matrix4 for column 2, row 2.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN2ROW2 = 10;\n\n/**\n * The index into Matrix4 for column 2, row 3.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN2ROW3 = 11;\n\n/**\n * The index into Matrix4 for column 3, row 0.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN3ROW0 = 12;\n\n/**\n * The index into Matrix4 for column 3, row 1.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN3ROW1 = 13;\n\n/**\n * The index into Matrix4 for column 3, row 2.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN3ROW2 = 14;\n\n/**\n * The index into Matrix4 for column 3, row 3.\n *\n * @type {Number}\n * @constant\n */\nMatrix4.COLUMN3ROW3 = 15;\n\nObject.defineProperties(Matrix4.prototype, {\n  /**\n   * Gets the number of items in the collection.\n   * @memberof Matrix4.prototype\n   *\n   * @type {Number}\n   */\n  length: {\n    get: function () {\n      return Matrix4.packedLength;\n    },\n  },\n});\n\n/**\n * Duplicates the provided Matrix4 instance.\n *\n * @param {Matrix4} [result] The object onto which to store the result.\n * @returns {Matrix4} The modified result parameter or a new Matrix4 instance if one was not provided.\n */\nMatrix4.prototype.clone = function (result) {\n  return Matrix4.clone(this, result);\n};\n\n/**\n * Compares this matrix to the provided matrix componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Matrix4} [right] The right hand side matrix.\n * @returns {Boolean} <code>true</code> if they are equal, <code>false</code> otherwise.\n */\nMatrix4.prototype.equals = function (right) {\n  return Matrix4.equals(this, right);\n};\n\n/**\n * @private\n */\nMatrix4.equalsArray = function (matrix, array, offset) {\n  return (\n    matrix[0] === array[offset] &&\n    matrix[1] === array[offset + 1] &&\n    matrix[2] === array[offset + 2] &&\n    matrix[3] === array[offset + 3] &&\n    matrix[4] === array[offset + 4] &&\n    matrix[5] === array[offset + 5] &&\n    matrix[6] === array[offset + 6] &&\n    matrix[7] === array[offset + 7] &&\n    matrix[8] === array[offset + 8] &&\n    matrix[9] === array[offset + 9] &&\n    matrix[10] === array[offset + 10] &&\n    matrix[11] === array[offset + 11] &&\n    matrix[12] === array[offset + 12] &&\n    matrix[13] === array[offset + 13] &&\n    matrix[14] === array[offset + 14] &&\n    matrix[15] === array[offset + 15]\n  );\n};\n\n/**\n * Compares this matrix to the provided matrix componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Matrix4} [right] The right hand side matrix.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if they are within the provided epsilon, <code>false</code> otherwise.\n */\nMatrix4.prototype.equalsEpsilon = function (right, epsilon) {\n  return Matrix4.equalsEpsilon(this, right, epsilon);\n};\n\n/**\n * Computes a string representing this Matrix with each row being\n * on a separate line and in the format '(column0, column1, column2, column3)'.\n *\n * @returns {String} A string representing the provided Matrix with each row being on a separate line and in the format '(column0, column1, column2, column3)'.\n */\nMatrix4.prototype.toString = function () {\n  return (\n    \"(\" +\n    this[0] +\n    \", \" +\n    this[4] +\n    \", \" +\n    this[8] +\n    \", \" +\n    this[12] +\n    \")\\n\" +\n    \"(\" +\n    this[1] +\n    \", \" +\n    this[5] +\n    \", \" +\n    this[9] +\n    \", \" +\n    this[13] +\n    \")\\n\" +\n    \"(\" +\n    this[2] +\n    \", \" +\n    this[6] +\n    \", \" +\n    this[10] +\n    \", \" +\n    this[14] +\n    \")\\n\" +\n    \"(\" +\n    this[3] +\n    \", \" +\n    this[7] +\n    \", \" +\n    this[11] +\n    \", \" +\n    this[15] +\n    \")\"\n  );\n};\nexport default Matrix4;\n", "import Cartographic from \"./Cartographic.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport Ellipsoid from \"./Ellipsoid.js\";\nimport CesiumMath from \"./Math.js\";\n\n/**\n * A two dimensional region specified as longitude and latitude coordinates.\n *\n * @alias Rectangle\n * @constructor\n *\n * @param {Number} [west=0.0] The westernmost longitude, in radians, in the range [-Pi, Pi].\n * @param {Number} [south=0.0] The southernmost latitude, in radians, in the range [-Pi/2, Pi/2].\n * @param {Number} [east=0.0] The easternmost longitude, in radians, in the range [-Pi, Pi].\n * @param {Number} [north=0.0] The northernmost latitude, in radians, in the range [-Pi/2, Pi/2].\n *\n * @see Packable\n */\nfunction Rectangle(west, south, east, north) {\n  /**\n   * The westernmost longitude in radians in the range [-Pi, Pi].\n   *\n   * @type {Number}\n   * @default 0.0\n   */\n  this.west = defaultValue(west, 0.0);\n\n  /**\n   * The southernmost latitude in radians in the range [-Pi/2, Pi/2].\n   *\n   * @type {Number}\n   * @default 0.0\n   */\n  this.south = defaultValue(south, 0.0);\n\n  /**\n   * The easternmost longitude in radians in the range [-Pi, Pi].\n   *\n   * @type {Number}\n   * @default 0.0\n   */\n  this.east = defaultValue(east, 0.0);\n\n  /**\n   * The northernmost latitude in radians in the range [-Pi/2, Pi/2].\n   *\n   * @type {Number}\n   * @default 0.0\n   */\n  this.north = defaultValue(north, 0.0);\n}\n\nObject.defineProperties(Rectangle.prototype, {\n  /**\n   * Gets the width of the rectangle in radians.\n   * @memberof Rectangle.prototype\n   * @type {Number}\n   * @readonly\n   */\n  width: {\n    get: function () {\n      return Rectangle.computeWidth(this);\n    },\n  },\n\n  /**\n   * Gets the height of the rectangle in radians.\n   * @memberof Rectangle.prototype\n   * @type {Number}\n   * @readonly\n   */\n  height: {\n    get: function () {\n      return Rectangle.computeHeight(this);\n    },\n  },\n});\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nRectangle.packedLength = 4;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {Rectangle} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nRectangle.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  array[startingIndex++] = value.west;\n  array[startingIndex++] = value.south;\n  array[startingIndex++] = value.east;\n  array[startingIndex] = value.north;\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {Rectangle} [result] The object into which to store the result.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if one was not provided.\n */\nRectangle.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Rectangle();\n  }\n\n  result.west = array[startingIndex++];\n  result.south = array[startingIndex++];\n  result.east = array[startingIndex++];\n  result.north = array[startingIndex];\n  return result;\n};\n\n/**\n * Computes the width of a rectangle in radians.\n * @param {Rectangle} rectangle The rectangle to compute the width of.\n * @returns {Number} The width.\n */\nRectangle.computeWidth = function (rectangle) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n  let east = rectangle.east;\n  const west = rectangle.west;\n  if (east < west) {\n    east += CesiumMath.TWO_PI;\n  }\n  return east - west;\n};\n\n/**\n * Computes the height of a rectangle in radians.\n * @param {Rectangle} rectangle The rectangle to compute the height of.\n * @returns {Number} The height.\n */\nRectangle.computeHeight = function (rectangle) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n  return rectangle.north - rectangle.south;\n};\n\n/**\n * Creates a rectangle given the boundary longitude and latitude in degrees.\n *\n * @param {Number} [west=0.0] The westernmost longitude in degrees in the range [-180.0, 180.0].\n * @param {Number} [south=0.0] The southernmost latitude in degrees in the range [-90.0, 90.0].\n * @param {Number} [east=0.0] The easternmost longitude in degrees in the range [-180.0, 180.0].\n * @param {Number} [north=0.0] The northernmost latitude in degrees in the range [-90.0, 90.0].\n * @param {Rectangle} [result] The object onto which to store the result, or undefined if a new instance should be created.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if none was provided.\n *\n * @example\n * const rectangle = Cesium.Rectangle.fromDegrees(0.0, 20.0, 10.0, 30.0);\n */\nRectangle.fromDegrees = function (west, south, east, north, result) {\n  west = CesiumMath.toRadians(defaultValue(west, 0.0));\n  south = CesiumMath.toRadians(defaultValue(south, 0.0));\n  east = CesiumMath.toRadians(defaultValue(east, 0.0));\n  north = CesiumMath.toRadians(defaultValue(north, 0.0));\n\n  if (!defined(result)) {\n    return new Rectangle(west, south, east, north);\n  }\n\n  result.west = west;\n  result.south = south;\n  result.east = east;\n  result.north = north;\n\n  return result;\n};\n\n/**\n * Creates a rectangle given the boundary longitude and latitude in radians.\n *\n * @param {Number} [west=0.0] The westernmost longitude in radians in the range [-Math.PI, Math.PI].\n * @param {Number} [south=0.0] The southernmost latitude in radians in the range [-Math.PI/2, Math.PI/2].\n * @param {Number} [east=0.0] The easternmost longitude in radians in the range [-Math.PI, Math.PI].\n * @param {Number} [north=0.0] The northernmost latitude in radians in the range [-Math.PI/2, Math.PI/2].\n * @param {Rectangle} [result] The object onto which to store the result, or undefined if a new instance should be created.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if none was provided.\n *\n * @example\n * const rectangle = Cesium.Rectangle.fromRadians(0.0, Math.PI/4, Math.PI/8, 3*Math.PI/4);\n */\nRectangle.fromRadians = function (west, south, east, north, result) {\n  if (!defined(result)) {\n    return new Rectangle(west, south, east, north);\n  }\n\n  result.west = defaultValue(west, 0.0);\n  result.south = defaultValue(south, 0.0);\n  result.east = defaultValue(east, 0.0);\n  result.north = defaultValue(north, 0.0);\n\n  return result;\n};\n\n/**\n * Creates the smallest possible Rectangle that encloses all positions in the provided array.\n *\n * @param {Cartographic[]} cartographics The list of Cartographic instances.\n * @param {Rectangle} [result] The object onto which to store the result, or undefined if a new instance should be created.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if none was provided.\n */\nRectangle.fromCartographicArray = function (cartographics, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"cartographics\", cartographics);\n  //>>includeEnd('debug');\n\n  let west = Number.MAX_VALUE;\n  let east = -Number.MAX_VALUE;\n  let westOverIDL = Number.MAX_VALUE;\n  let eastOverIDL = -Number.MAX_VALUE;\n  let south = Number.MAX_VALUE;\n  let north = -Number.MAX_VALUE;\n\n  for (let i = 0, len = cartographics.length; i < len; i++) {\n    const position = cartographics[i];\n    west = Math.min(west, position.longitude);\n    east = Math.max(east, position.longitude);\n    south = Math.min(south, position.latitude);\n    north = Math.max(north, position.latitude);\n\n    const lonAdjusted =\n      position.longitude >= 0\n        ? position.longitude\n        : position.longitude + CesiumMath.TWO_PI;\n    westOverIDL = Math.min(westOverIDL, lonAdjusted);\n    eastOverIDL = Math.max(eastOverIDL, lonAdjusted);\n  }\n\n  if (east - west > eastOverIDL - westOverIDL) {\n    west = westOverIDL;\n    east = eastOverIDL;\n\n    if (east > CesiumMath.PI) {\n      east = east - CesiumMath.TWO_PI;\n    }\n    if (west > CesiumMath.PI) {\n      west = west - CesiumMath.TWO_PI;\n    }\n  }\n\n  if (!defined(result)) {\n    return new Rectangle(west, south, east, north);\n  }\n\n  result.west = west;\n  result.south = south;\n  result.east = east;\n  result.north = north;\n  return result;\n};\n\n/**\n * Creates the smallest possible Rectangle that encloses all positions in the provided array.\n *\n * @param {Cartesian3[]} cartesians The list of Cartesian instances.\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid the cartesians are on.\n * @param {Rectangle} [result] The object onto which to store the result, or undefined if a new instance should be created.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if none was provided.\n */\nRectangle.fromCartesianArray = function (cartesians, ellipsoid, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"cartesians\", cartesians);\n  //>>includeEnd('debug');\n  ellipsoid = defaultValue(ellipsoid, Ellipsoid.WGS84);\n\n  let west = Number.MAX_VALUE;\n  let east = -Number.MAX_VALUE;\n  let westOverIDL = Number.MAX_VALUE;\n  let eastOverIDL = -Number.MAX_VALUE;\n  let south = Number.MAX_VALUE;\n  let north = -Number.MAX_VALUE;\n\n  for (let i = 0, len = cartesians.length; i < len; i++) {\n    const position = ellipsoid.cartesianToCartographic(cartesians[i]);\n    west = Math.min(west, position.longitude);\n    east = Math.max(east, position.longitude);\n    south = Math.min(south, position.latitude);\n    north = Math.max(north, position.latitude);\n\n    const lonAdjusted =\n      position.longitude >= 0\n        ? position.longitude\n        : position.longitude + CesiumMath.TWO_PI;\n    westOverIDL = Math.min(westOverIDL, lonAdjusted);\n    eastOverIDL = Math.max(eastOverIDL, lonAdjusted);\n  }\n\n  if (east - west > eastOverIDL - westOverIDL) {\n    west = westOverIDL;\n    east = eastOverIDL;\n\n    if (east > CesiumMath.PI) {\n      east = east - CesiumMath.TWO_PI;\n    }\n    if (west > CesiumMath.PI) {\n      west = west - CesiumMath.TWO_PI;\n    }\n  }\n\n  if (!defined(result)) {\n    return new Rectangle(west, south, east, north);\n  }\n\n  result.west = west;\n  result.south = south;\n  result.east = east;\n  result.north = north;\n  return result;\n};\n\n/**\n * Duplicates a Rectangle.\n *\n * @param {Rectangle} rectangle The rectangle to clone.\n * @param {Rectangle} [result] The object onto which to store the result, or undefined if a new instance should be created.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if none was provided. (Returns undefined if rectangle is undefined)\n */\nRectangle.clone = function (rectangle, result) {\n  if (!defined(rectangle)) {\n    return undefined;\n  }\n\n  if (!defined(result)) {\n    return new Rectangle(\n      rectangle.west,\n      rectangle.south,\n      rectangle.east,\n      rectangle.north\n    );\n  }\n\n  result.west = rectangle.west;\n  result.south = rectangle.south;\n  result.east = rectangle.east;\n  result.north = rectangle.north;\n  return result;\n};\n\n/**\n * Compares the provided Rectangles componentwise and returns\n * <code>true</code> if they pass an absolute or relative tolerance test,\n * <code>false</code> otherwise.\n *\n * @param {Rectangle} [left] The first Rectangle.\n * @param {Rectangle} [right] The second Rectangle.\n * @param {Number} [absoluteEpsilon=0] The absolute epsilon tolerance to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n */\nRectangle.equalsEpsilon = function (left, right, absoluteEpsilon) {\n  absoluteEpsilon = defaultValue(absoluteEpsilon, 0);\n\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      Math.abs(left.west - right.west) <= absoluteEpsilon &&\n      Math.abs(left.south - right.south) <= absoluteEpsilon &&\n      Math.abs(left.east - right.east) <= absoluteEpsilon &&\n      Math.abs(left.north - right.north) <= absoluteEpsilon)\n  );\n};\n\n/**\n * Duplicates this Rectangle.\n *\n * @param {Rectangle} [result] The object onto which to store the result.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if none was provided.\n */\nRectangle.prototype.clone = function (result) {\n  return Rectangle.clone(this, result);\n};\n\n/**\n * Compares the provided Rectangle with this Rectangle componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Rectangle} [other] The Rectangle to compare.\n * @returns {Boolean} <code>true</code> if the Rectangles are equal, <code>false</code> otherwise.\n */\nRectangle.prototype.equals = function (other) {\n  return Rectangle.equals(this, other);\n};\n\n/**\n * Compares the provided rectangles and returns <code>true</code> if they are equal,\n * <code>false</code> otherwise.\n *\n * @param {Rectangle} [left] The first Rectangle.\n * @param {Rectangle} [right] The second Rectangle.\n * @returns {Boolean} <code>true</code> if left and right are equal; otherwise <code>false</code>.\n */\nRectangle.equals = function (left, right) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      left.west === right.west &&\n      left.south === right.south &&\n      left.east === right.east &&\n      left.north === right.north)\n  );\n};\n\n/**\n * Compares the provided Rectangle with this Rectangle componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Rectangle} [other] The Rectangle to compare.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if the Rectangles are within the provided epsilon, <code>false</code> otherwise.\n */\nRectangle.prototype.equalsEpsilon = function (other, epsilon) {\n  return Rectangle.equalsEpsilon(this, other, epsilon);\n};\n\n/**\n * Checks a Rectangle's properties and throws if they are not in valid ranges.\n *\n * @param {Rectangle} rectangle The rectangle to validate\n *\n * @exception {DeveloperError} <code>north</code> must be in the interval [<code>-Pi/2</code>, <code>Pi/2</code>].\n * @exception {DeveloperError} <code>south</code> must be in the interval [<code>-Pi/2</code>, <code>Pi/2</code>].\n * @exception {DeveloperError} <code>east</code> must be in the interval [<code>-Pi</code>, <code>Pi</code>].\n * @exception {DeveloperError} <code>west</code> must be in the interval [<code>-Pi</code>, <code>Pi</code>].\n */\nRectangle.validate = function (rectangle) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n\n  const north = rectangle.north;\n  Check.typeOf.number.greaterThanOrEquals(\n    \"north\",\n    north,\n    -CesiumMath.PI_OVER_TWO\n  );\n  Check.typeOf.number.lessThanOrEquals(\"north\", north, CesiumMath.PI_OVER_TWO);\n\n  const south = rectangle.south;\n  Check.typeOf.number.greaterThanOrEquals(\n    \"south\",\n    south,\n    -CesiumMath.PI_OVER_TWO\n  );\n  Check.typeOf.number.lessThanOrEquals(\"south\", south, CesiumMath.PI_OVER_TWO);\n\n  const west = rectangle.west;\n  Check.typeOf.number.greaterThanOrEquals(\"west\", west, -Math.PI);\n  Check.typeOf.number.lessThanOrEquals(\"west\", west, Math.PI);\n\n  const east = rectangle.east;\n  Check.typeOf.number.greaterThanOrEquals(\"east\", east, -Math.PI);\n  Check.typeOf.number.lessThanOrEquals(\"east\", east, Math.PI);\n  //>>includeEnd('debug');\n};\n\n/**\n * Computes the southwest corner of a rectangle.\n *\n * @param {Rectangle} rectangle The rectangle for which to find the corner\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if none was provided.\n */\nRectangle.southwest = function (rectangle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Cartographic(rectangle.west, rectangle.south);\n  }\n  result.longitude = rectangle.west;\n  result.latitude = rectangle.south;\n  result.height = 0.0;\n  return result;\n};\n\n/**\n * Computes the northwest corner of a rectangle.\n *\n * @param {Rectangle} rectangle The rectangle for which to find the corner\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if none was provided.\n */\nRectangle.northwest = function (rectangle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Cartographic(rectangle.west, rectangle.north);\n  }\n  result.longitude = rectangle.west;\n  result.latitude = rectangle.north;\n  result.height = 0.0;\n  return result;\n};\n\n/**\n * Computes the northeast corner of a rectangle.\n *\n * @param {Rectangle} rectangle The rectangle for which to find the corner\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if none was provided.\n */\nRectangle.northeast = function (rectangle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Cartographic(rectangle.east, rectangle.north);\n  }\n  result.longitude = rectangle.east;\n  result.latitude = rectangle.north;\n  result.height = 0.0;\n  return result;\n};\n\n/**\n * Computes the southeast corner of a rectangle.\n *\n * @param {Rectangle} rectangle The rectangle for which to find the corner\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if none was provided.\n */\nRectangle.southeast = function (rectangle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Cartographic(rectangle.east, rectangle.south);\n  }\n  result.longitude = rectangle.east;\n  result.latitude = rectangle.south;\n  result.height = 0.0;\n  return result;\n};\n\n/**\n * Computes the center of a rectangle.\n *\n * @param {Rectangle} rectangle The rectangle for which to find the center\n * @param {Cartographic} [result] The object onto which to store the result.\n * @returns {Cartographic} The modified result parameter or a new Cartographic instance if none was provided.\n */\nRectangle.center = function (rectangle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n\n  let east = rectangle.east;\n  const west = rectangle.west;\n\n  if (east < west) {\n    east += CesiumMath.TWO_PI;\n  }\n\n  const longitude = CesiumMath.negativePiToPi((west + east) * 0.5);\n  const latitude = (rectangle.south + rectangle.north) * 0.5;\n\n  if (!defined(result)) {\n    return new Cartographic(longitude, latitude);\n  }\n\n  result.longitude = longitude;\n  result.latitude = latitude;\n  result.height = 0.0;\n  return result;\n};\n\n/**\n * Computes the intersection of two rectangles.  This function assumes that the rectangle's coordinates are\n * latitude and longitude in radians and produces a correct intersection, taking into account the fact that\n * the same angle can be represented with multiple values as well as the wrapping of longitude at the\n * anti-meridian.  For a simple intersection that ignores these factors and can be used with projected\n * coordinates, see {@link Rectangle.simpleIntersection}.\n *\n * @param {Rectangle} rectangle On rectangle to find an intersection\n * @param {Rectangle} otherRectangle Another rectangle to find an intersection\n * @param {Rectangle} [result] The object onto which to store the result.\n * @returns {Rectangle|undefined} The modified result parameter, a new Rectangle instance if none was provided or undefined if there is no intersection.\n */\nRectangle.intersection = function (rectangle, otherRectangle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  Check.typeOf.object(\"otherRectangle\", otherRectangle);\n  //>>includeEnd('debug');\n\n  let rectangleEast = rectangle.east;\n  let rectangleWest = rectangle.west;\n\n  let otherRectangleEast = otherRectangle.east;\n  let otherRectangleWest = otherRectangle.west;\n\n  if (rectangleEast < rectangleWest && otherRectangleEast > 0.0) {\n    rectangleEast += CesiumMath.TWO_PI;\n  } else if (otherRectangleEast < otherRectangleWest && rectangleEast > 0.0) {\n    otherRectangleEast += CesiumMath.TWO_PI;\n  }\n\n  if (rectangleEast < rectangleWest && otherRectangleWest < 0.0) {\n    otherRectangleWest += CesiumMath.TWO_PI;\n  } else if (otherRectangleEast < otherRectangleWest && rectangleWest < 0.0) {\n    rectangleWest += CesiumMath.TWO_PI;\n  }\n\n  const west = CesiumMath.negativePiToPi(\n    Math.max(rectangleWest, otherRectangleWest)\n  );\n  const east = CesiumMath.negativePiToPi(\n    Math.min(rectangleEast, otherRectangleEast)\n  );\n\n  if (\n    (rectangle.west < rectangle.east ||\n      otherRectangle.west < otherRectangle.east) &&\n    east <= west\n  ) {\n    return undefined;\n  }\n\n  const south = Math.max(rectangle.south, otherRectangle.south);\n  const north = Math.min(rectangle.north, otherRectangle.north);\n\n  if (south >= north) {\n    return undefined;\n  }\n\n  if (!defined(result)) {\n    return new Rectangle(west, south, east, north);\n  }\n  result.west = west;\n  result.south = south;\n  result.east = east;\n  result.north = north;\n  return result;\n};\n\n/**\n * Computes a simple intersection of two rectangles.  Unlike {@link Rectangle.intersection}, this function\n * does not attempt to put the angular coordinates into a consistent range or to account for crossing the\n * anti-meridian.  As such, it can be used for rectangles where the coordinates are not simply latitude\n * and longitude (i.e. projected coordinates).\n *\n * @param {Rectangle} rectangle On rectangle to find an intersection\n * @param {Rectangle} otherRectangle Another rectangle to find an intersection\n * @param {Rectangle} [result] The object onto which to store the result.\n * @returns {Rectangle|undefined} The modified result parameter, a new Rectangle instance if none was provided or undefined if there is no intersection.\n */\nRectangle.simpleIntersection = function (rectangle, otherRectangle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  Check.typeOf.object(\"otherRectangle\", otherRectangle);\n  //>>includeEnd('debug');\n\n  const west = Math.max(rectangle.west, otherRectangle.west);\n  const south = Math.max(rectangle.south, otherRectangle.south);\n  const east = Math.min(rectangle.east, otherRectangle.east);\n  const north = Math.min(rectangle.north, otherRectangle.north);\n\n  if (south >= north || west >= east) {\n    return undefined;\n  }\n\n  if (!defined(result)) {\n    return new Rectangle(west, south, east, north);\n  }\n\n  result.west = west;\n  result.south = south;\n  result.east = east;\n  result.north = north;\n  return result;\n};\n\n/**\n * Computes a rectangle that is the union of two rectangles.\n *\n * @param {Rectangle} rectangle A rectangle to enclose in rectangle.\n * @param {Rectangle} otherRectangle A rectangle to enclose in a rectangle.\n * @param {Rectangle} [result] The object onto which to store the result.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if none was provided.\n */\nRectangle.union = function (rectangle, otherRectangle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  Check.typeOf.object(\"otherRectangle\", otherRectangle);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Rectangle();\n  }\n\n  let rectangleEast = rectangle.east;\n  let rectangleWest = rectangle.west;\n\n  let otherRectangleEast = otherRectangle.east;\n  let otherRectangleWest = otherRectangle.west;\n\n  if (rectangleEast < rectangleWest && otherRectangleEast > 0.0) {\n    rectangleEast += CesiumMath.TWO_PI;\n  } else if (otherRectangleEast < otherRectangleWest && rectangleEast > 0.0) {\n    otherRectangleEast += CesiumMath.TWO_PI;\n  }\n\n  if (rectangleEast < rectangleWest && otherRectangleWest < 0.0) {\n    otherRectangleWest += CesiumMath.TWO_PI;\n  } else if (otherRectangleEast < otherRectangleWest && rectangleWest < 0.0) {\n    rectangleWest += CesiumMath.TWO_PI;\n  }\n\n  const west = CesiumMath.negativePiToPi(\n    Math.min(rectangleWest, otherRectangleWest)\n  );\n  const east = CesiumMath.negativePiToPi(\n    Math.max(rectangleEast, otherRectangleEast)\n  );\n\n  result.west = west;\n  result.south = Math.min(rectangle.south, otherRectangle.south);\n  result.east = east;\n  result.north = Math.max(rectangle.north, otherRectangle.north);\n\n  return result;\n};\n\n/**\n * Computes a rectangle by enlarging the provided rectangle until it contains the provided cartographic.\n *\n * @param {Rectangle} rectangle A rectangle to expand.\n * @param {Cartographic} cartographic A cartographic to enclose in a rectangle.\n * @param {Rectangle} [result] The object onto which to store the result.\n * @returns {Rectangle} The modified result parameter or a new Rectangle instance if one was not provided.\n */\nRectangle.expand = function (rectangle, cartographic, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  Check.typeOf.object(\"cartographic\", cartographic);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Rectangle();\n  }\n\n  result.west = Math.min(rectangle.west, cartographic.longitude);\n  result.south = Math.min(rectangle.south, cartographic.latitude);\n  result.east = Math.max(rectangle.east, cartographic.longitude);\n  result.north = Math.max(rectangle.north, cartographic.latitude);\n\n  return result;\n};\n\n/**\n * Returns true if the cartographic is on or inside the rectangle, false otherwise.\n *\n * @param {Rectangle} rectangle The rectangle\n * @param {Cartographic} cartographic The cartographic to test.\n * @returns {Boolean} true if the provided cartographic is inside the rectangle, false otherwise.\n */\nRectangle.contains = function (rectangle, cartographic) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  Check.typeOf.object(\"cartographic\", cartographic);\n  //>>includeEnd('debug');\n\n  let longitude = cartographic.longitude;\n  const latitude = cartographic.latitude;\n\n  const west = rectangle.west;\n  let east = rectangle.east;\n\n  if (east < west) {\n    east += CesiumMath.TWO_PI;\n    if (longitude < 0.0) {\n      longitude += CesiumMath.TWO_PI;\n    }\n  }\n  return (\n    (longitude > west ||\n      CesiumMath.equalsEpsilon(longitude, west, CesiumMath.EPSILON14)) &&\n    (longitude < east ||\n      CesiumMath.equalsEpsilon(longitude, east, CesiumMath.EPSILON14)) &&\n    latitude >= rectangle.south &&\n    latitude <= rectangle.north\n  );\n};\n\nconst subsampleLlaScratch = new Cartographic();\n/**\n * Samples a rectangle so that it includes a list of Cartesian points suitable for passing to\n * {@link BoundingSphere#fromPoints}.  Sampling is necessary to account\n * for rectangles that cover the poles or cross the equator.\n *\n * @param {Rectangle} rectangle The rectangle to subsample.\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid to use.\n * @param {Number} [surfaceHeight=0.0] The height of the rectangle above the ellipsoid.\n * @param {Cartesian3[]} [result] The array of Cartesians onto which to store the result.\n * @returns {Cartesian3[]} The modified result parameter or a new Array of Cartesians instances if none was provided.\n */\nRectangle.subsample = function (rectangle, ellipsoid, surfaceHeight, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n\n  ellipsoid = defaultValue(ellipsoid, Ellipsoid.WGS84);\n  surfaceHeight = defaultValue(surfaceHeight, 0.0);\n\n  if (!defined(result)) {\n    result = [];\n  }\n  let length = 0;\n\n  const north = rectangle.north;\n  const south = rectangle.south;\n  const east = rectangle.east;\n  const west = rectangle.west;\n\n  const lla = subsampleLlaScratch;\n  lla.height = surfaceHeight;\n\n  lla.longitude = west;\n  lla.latitude = north;\n  result[length] = ellipsoid.cartographicToCartesian(lla, result[length]);\n  length++;\n\n  lla.longitude = east;\n  result[length] = ellipsoid.cartographicToCartesian(lla, result[length]);\n  length++;\n\n  lla.latitude = south;\n  result[length] = ellipsoid.cartographicToCartesian(lla, result[length]);\n  length++;\n\n  lla.longitude = west;\n  result[length] = ellipsoid.cartographicToCartesian(lla, result[length]);\n  length++;\n\n  if (north < 0.0) {\n    lla.latitude = north;\n  } else if (south > 0.0) {\n    lla.latitude = south;\n  } else {\n    lla.latitude = 0.0;\n  }\n\n  for (let i = 1; i < 8; ++i) {\n    lla.longitude = -Math.PI + i * CesiumMath.PI_OVER_TWO;\n    if (Rectangle.contains(rectangle, lla)) {\n      result[length] = ellipsoid.cartographicToCartesian(lla, result[length]);\n      length++;\n    }\n  }\n\n  if (lla.latitude === 0.0) {\n    lla.longitude = west;\n    result[length] = ellipsoid.cartographicToCartesian(lla, result[length]);\n    length++;\n    lla.longitude = east;\n    result[length] = ellipsoid.cartographicToCartesian(lla, result[length]);\n    length++;\n  }\n  result.length = length;\n  return result;\n};\n\n/**\n * The largest possible rectangle.\n *\n * @type {Rectangle}\n * @constant\n */\nRectangle.MAX_VALUE = Object.freeze(\n  new Rectangle(\n    -Math.PI,\n    -CesiumMath.PI_OVER_TWO,\n    Math.PI,\n    CesiumMath.PI_OVER_TWO\n  )\n);\nexport default Rectangle;\n", "import Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\n\n/**\n * A 2D Cartesian point.\n * @alias Cartesian2\n * @constructor\n *\n * @param {Number} [x=0.0] The X component.\n * @param {Number} [y=0.0] The Y component.\n *\n * @see Cartesian3\n * @see Cartesian4\n * @see Packable\n */\nfunction Cartesian2(x, y) {\n  /**\n   * The X component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.x = defaultValue(x, 0.0);\n\n  /**\n   * The Y component.\n   * @type {Number}\n   * @default 0.0\n   */\n  this.y = defaultValue(y, 0.0);\n}\n\n/**\n * Creates a Cartesian2 instance from x and y coordinates.\n *\n * @param {Number} x The x coordinate.\n * @param {Number} y The y coordinate.\n * @param {Cartesian2} [result] The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter or a new Cartesian2 instance if one was not provided.\n */\nCartesian2.fromElements = function (x, y, result) {\n  if (!defined(result)) {\n    return new Cartesian2(x, y);\n  }\n\n  result.x = x;\n  result.y = y;\n  return result;\n};\n\n/**\n * Duplicates a Cartesian2 instance.\n *\n * @param {Cartesian2} cartesian The Cartesian to duplicate.\n * @param {Cartesian2} [result] The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter or a new Cartesian2 instance if one was not provided. (Returns undefined if cartesian is undefined)\n */\nCartesian2.clone = function (cartesian, result) {\n  if (!defined(cartesian)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    return new Cartesian2(cartesian.x, cartesian.y);\n  }\n\n  result.x = cartesian.x;\n  result.y = cartesian.y;\n  return result;\n};\n\n/**\n * Creates a Cartesian2 instance from an existing Cartesian3.  This simply takes the\n * x and y properties of the Cartesian3 and drops z.\n * @function\n *\n * @param {Cartesian3} cartesian The Cartesian3 instance to create a Cartesian2 instance from.\n * @param {Cartesian2} [result] The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter or a new Cartesian2 instance if one was not provided.\n */\nCartesian2.fromCartesian3 = Cartesian2.clone;\n\n/**\n * Creates a Cartesian2 instance from an existing Cartesian4.  This simply takes the\n * x and y properties of the Cartesian4 and drops z and w.\n * @function\n *\n * @param {Cartesian4} cartesian The Cartesian4 instance to create a Cartesian2 instance from.\n * @param {Cartesian2} [result] The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter or a new Cartesian2 instance if one was not provided.\n */\nCartesian2.fromCartesian4 = Cartesian2.clone;\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nCartesian2.packedLength = 2;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {Cartesian2} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nCartesian2.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  array[startingIndex++] = value.x;\n  array[startingIndex] = value.y;\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {Cartesian2} [result] The object into which to store the result.\n * @returns {Cartesian2} The modified result parameter or a new Cartesian2 instance if one was not provided.\n */\nCartesian2.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Cartesian2();\n  }\n  result.x = array[startingIndex++];\n  result.y = array[startingIndex];\n  return result;\n};\n\n/**\n     * Flattens an array of Cartesian2s into and array of components.\n     *\n     * @param {Cartesian2[]} array The array of cartesians to pack.\n     * @param {Number[]} [result] The array onto which to store the result. If this is a typed array, it must have array.length * 2 components, else a {@link DeveloperError} will be thrown. If it is a regular array, it will be resized to have (array.length * 2) elements.\n\n     * @returns {Number[]} The packed array.\n     */\nCartesian2.packArray = function (array, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  const length = array.length;\n  const resultLength = length * 2;\n  if (!defined(result)) {\n    result = new Array(resultLength);\n  } else if (!Array.isArray(result) && result.length !== resultLength) {\n    throw new DeveloperError(\n      \"If result is a typed array, it must have exactly array.length * 2 elements\"\n    );\n  } else if (result.length !== resultLength) {\n    result.length = resultLength;\n  }\n\n  for (let i = 0; i < length; ++i) {\n    Cartesian2.pack(array[i], result, i * 2);\n  }\n  return result;\n};\n\n/**\n * Unpacks an array of cartesian components into and array of Cartesian2s.\n *\n * @param {Number[]} array The array of components to unpack.\n * @param {Cartesian2[]} [result] The array onto which to store the result.\n * @returns {Cartesian2[]} The unpacked array.\n */\nCartesian2.unpackArray = function (array, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  Check.typeOf.number.greaterThanOrEquals(\"array.length\", array.length, 2);\n  if (array.length % 2 !== 0) {\n    throw new DeveloperError(\"array length must be a multiple of 2.\");\n  }\n  //>>includeEnd('debug');\n\n  const length = array.length;\n  if (!defined(result)) {\n    result = new Array(length / 2);\n  } else {\n    result.length = length / 2;\n  }\n\n  for (let i = 0; i < length; i += 2) {\n    const index = i / 2;\n    result[index] = Cartesian2.unpack(array, i, result[index]);\n  }\n  return result;\n};\n\n/**\n * Creates a Cartesian2 from two consecutive elements in an array.\n * @function\n *\n * @param {Number[]} array The array whose two consecutive elements correspond to the x and y components, respectively.\n * @param {Number} [startingIndex=0] The offset into the array of the first element, which corresponds to the x component.\n * @param {Cartesian2} [result] The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter or a new Cartesian2 instance if one was not provided.\n *\n * @example\n * // Create a Cartesian2 with (1.0, 2.0)\n * const v = [1.0, 2.0];\n * const p = Cesium.Cartesian2.fromArray(v);\n *\n * // Create a Cartesian2 with (1.0, 2.0) using an offset into an array\n * const v2 = [0.0, 0.0, 1.0, 2.0];\n * const p2 = Cesium.Cartesian2.fromArray(v2, 2);\n */\nCartesian2.fromArray = Cartesian2.unpack;\n\n/**\n * Computes the value of the maximum component for the supplied Cartesian.\n *\n * @param {Cartesian2} cartesian The cartesian to use.\n * @returns {Number} The value of the maximum component.\n */\nCartesian2.maximumComponent = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return Math.max(cartesian.x, cartesian.y);\n};\n\n/**\n * Computes the value of the minimum component for the supplied Cartesian.\n *\n * @param {Cartesian2} cartesian The cartesian to use.\n * @returns {Number} The value of the minimum component.\n */\nCartesian2.minimumComponent = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return Math.min(cartesian.x, cartesian.y);\n};\n\n/**\n * Compares two Cartesians and computes a Cartesian which contains the minimum components of the supplied Cartesians.\n *\n * @param {Cartesian2} first A cartesian to compare.\n * @param {Cartesian2} second A cartesian to compare.\n * @param {Cartesian2} result The object into which to store the result.\n * @returns {Cartesian2} A cartesian with the minimum components.\n */\nCartesian2.minimumByComponent = function (first, second, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"first\", first);\n  Check.typeOf.object(\"second\", second);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.min(first.x, second.x);\n  result.y = Math.min(first.y, second.y);\n\n  return result;\n};\n\n/**\n * Compares two Cartesians and computes a Cartesian which contains the maximum components of the supplied Cartesians.\n *\n * @param {Cartesian2} first A cartesian to compare.\n * @param {Cartesian2} second A cartesian to compare.\n * @param {Cartesian2} result The object into which to store the result.\n * @returns {Cartesian2} A cartesian with the maximum components.\n */\nCartesian2.maximumByComponent = function (first, second, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"first\", first);\n  Check.typeOf.object(\"second\", second);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.max(first.x, second.x);\n  result.y = Math.max(first.y, second.y);\n  return result;\n};\n\n/**\n * Computes the provided Cartesian's squared magnitude.\n *\n * @param {Cartesian2} cartesian The Cartesian instance whose squared magnitude is to be computed.\n * @returns {Number} The squared magnitude.\n */\nCartesian2.magnitudeSquared = function (cartesian) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  //>>includeEnd('debug');\n\n  return cartesian.x * cartesian.x + cartesian.y * cartesian.y;\n};\n\n/**\n * Computes the Cartesian's magnitude (length).\n *\n * @param {Cartesian2} cartesian The Cartesian instance whose magnitude is to be computed.\n * @returns {Number} The magnitude.\n */\nCartesian2.magnitude = function (cartesian) {\n  return Math.sqrt(Cartesian2.magnitudeSquared(cartesian));\n};\n\nconst distanceScratch = new Cartesian2();\n\n/**\n * Computes the distance between two points.\n *\n * @param {Cartesian2} left The first point to compute the distance from.\n * @param {Cartesian2} right The second point to compute the distance to.\n * @returns {Number} The distance between two points.\n *\n * @example\n * // Returns 1.0\n * const d = Cesium.Cartesian2.distance(new Cesium.Cartesian2(1.0, 0.0), new Cesium.Cartesian2(2.0, 0.0));\n */\nCartesian2.distance = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  Cartesian2.subtract(left, right, distanceScratch);\n  return Cartesian2.magnitude(distanceScratch);\n};\n\n/**\n * Computes the squared distance between two points.  Comparing squared distances\n * using this function is more efficient than comparing distances using {@link Cartesian2#distance}.\n *\n * @param {Cartesian2} left The first point to compute the distance from.\n * @param {Cartesian2} right The second point to compute the distance to.\n * @returns {Number} The distance between two points.\n *\n * @example\n * // Returns 4.0, not 2.0\n * const d = Cesium.Cartesian2.distance(new Cesium.Cartesian2(1.0, 0.0), new Cesium.Cartesian2(3.0, 0.0));\n */\nCartesian2.distanceSquared = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  Cartesian2.subtract(left, right, distanceScratch);\n  return Cartesian2.magnitudeSquared(distanceScratch);\n};\n\n/**\n * Computes the normalized form of the supplied Cartesian.\n *\n * @param {Cartesian2} cartesian The Cartesian to be normalized.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.normalize = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const magnitude = Cartesian2.magnitude(cartesian);\n\n  result.x = cartesian.x / magnitude;\n  result.y = cartesian.y / magnitude;\n\n  //>>includeStart('debug', pragmas.debug);\n  if (isNaN(result.x) || isNaN(result.y)) {\n    throw new DeveloperError(\"normalized result is not a number\");\n  }\n  //>>includeEnd('debug');\n\n  return result;\n};\n\n/**\n * Computes the dot (scalar) product of two Cartesians.\n *\n * @param {Cartesian2} left The first Cartesian.\n * @param {Cartesian2} right The second Cartesian.\n * @returns {Number} The dot product.\n */\nCartesian2.dot = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  return left.x * right.x + left.y * right.y;\n};\n\n/**\n * Computes the magnitude of the cross product that would result from implicitly setting the Z coordinate of the input vectors to 0\n *\n * @param {Cartesian2} left The first Cartesian.\n * @param {Cartesian2} right The second Cartesian.\n * @returns {Number} The cross product.\n */\nCartesian2.cross = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  return left.x * right.y - left.y * right.x;\n};\n\n/**\n * Computes the componentwise product of two Cartesians.\n *\n * @param {Cartesian2} left The first Cartesian.\n * @param {Cartesian2} right The second Cartesian.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.multiplyComponents = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x * right.x;\n  result.y = left.y * right.y;\n  return result;\n};\n\n/**\n * Computes the componentwise quotient of two Cartesians.\n *\n * @param {Cartesian2} left The first Cartesian.\n * @param {Cartesian2} right The second Cartesian.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.divideComponents = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x / right.x;\n  result.y = left.y / right.y;\n  return result;\n};\n\n/**\n * Computes the componentwise sum of two Cartesians.\n *\n * @param {Cartesian2} left The first Cartesian.\n * @param {Cartesian2} right The second Cartesian.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.add = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x + right.x;\n  result.y = left.y + right.y;\n  return result;\n};\n\n/**\n * Computes the componentwise difference of two Cartesians.\n *\n * @param {Cartesian2} left The first Cartesian.\n * @param {Cartesian2} right The second Cartesian.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.subtract = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = left.x - right.x;\n  result.y = left.y - right.y;\n  return result;\n};\n\n/**\n * Multiplies the provided Cartesian componentwise by the provided scalar.\n *\n * @param {Cartesian2} cartesian The Cartesian to be scaled.\n * @param {Number} scalar The scalar to multiply with.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.multiplyByScalar = function (cartesian, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = cartesian.x * scalar;\n  result.y = cartesian.y * scalar;\n  return result;\n};\n\n/**\n * Divides the provided Cartesian componentwise by the provided scalar.\n *\n * @param {Cartesian2} cartesian The Cartesian to be divided.\n * @param {Number} scalar The scalar to divide by.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.divideByScalar = function (cartesian, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = cartesian.x / scalar;\n  result.y = cartesian.y / scalar;\n  return result;\n};\n\n/**\n * Negates the provided Cartesian.\n *\n * @param {Cartesian2} cartesian The Cartesian to be negated.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.negate = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = -cartesian.x;\n  result.y = -cartesian.y;\n  return result;\n};\n\n/**\n * Computes the absolute value of the provided Cartesian.\n *\n * @param {Cartesian2} cartesian The Cartesian whose absolute value is to be computed.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.abs = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Math.abs(cartesian.x);\n  result.y = Math.abs(cartesian.y);\n  return result;\n};\n\nconst lerpScratch = new Cartesian2();\n/**\n * Computes the linear interpolation or extrapolation at t using the provided cartesians.\n *\n * @param {Cartesian2} start The value corresponding to t at 0.0.\n * @param {Cartesian2} end The value corresponding to t at 1.0.\n * @param {Number} t The point along t at which to interpolate.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nCartesian2.lerp = function (start, end, t, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"start\", start);\n  Check.typeOf.object(\"end\", end);\n  Check.typeOf.number(\"t\", t);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  Cartesian2.multiplyByScalar(end, t, lerpScratch);\n  result = Cartesian2.multiplyByScalar(start, 1.0 - t, result);\n  return Cartesian2.add(lerpScratch, result, result);\n};\n\nconst angleBetweenScratch = new Cartesian2();\nconst angleBetweenScratch2 = new Cartesian2();\n/**\n * Returns the angle, in radians, between the provided Cartesians.\n *\n * @param {Cartesian2} left The first Cartesian.\n * @param {Cartesian2} right The second Cartesian.\n * @returns {Number} The angle between the Cartesians.\n */\nCartesian2.angleBetween = function (left, right) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  //>>includeEnd('debug');\n\n  Cartesian2.normalize(left, angleBetweenScratch);\n  Cartesian2.normalize(right, angleBetweenScratch2);\n  return CesiumMath.acosClamped(\n    Cartesian2.dot(angleBetweenScratch, angleBetweenScratch2)\n  );\n};\n\nconst mostOrthogonalAxisScratch = new Cartesian2();\n/**\n * Returns the axis that is most orthogonal to the provided Cartesian.\n *\n * @param {Cartesian2} cartesian The Cartesian on which to find the most orthogonal axis.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The most orthogonal axis.\n */\nCartesian2.mostOrthogonalAxis = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const f = Cartesian2.normalize(cartesian, mostOrthogonalAxisScratch);\n  Cartesian2.abs(f, f);\n\n  if (f.x <= f.y) {\n    result = Cartesian2.clone(Cartesian2.UNIT_X, result);\n  } else {\n    result = Cartesian2.clone(Cartesian2.UNIT_Y, result);\n  }\n\n  return result;\n};\n\n/**\n * Compares the provided Cartesians componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Cartesian2} [left] The first Cartesian.\n * @param {Cartesian2} [right] The second Cartesian.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nCartesian2.equals = function (left, right) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      left.x === right.x &&\n      left.y === right.y)\n  );\n};\n\n/**\n * @private\n */\nCartesian2.equalsArray = function (cartesian, array, offset) {\n  return cartesian.x === array[offset] && cartesian.y === array[offset + 1];\n};\n\n/**\n * Compares the provided Cartesians componentwise and returns\n * <code>true</code> if they pass an absolute or relative tolerance test,\n * <code>false</code> otherwise.\n *\n * @param {Cartesian2} [left] The first Cartesian.\n * @param {Cartesian2} [right] The second Cartesian.\n * @param {Number} [relativeEpsilon=0] The relative epsilon tolerance to use for equality testing.\n * @param {Number} [absoluteEpsilon=relativeEpsilon] The absolute epsilon tolerance to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n */\nCartesian2.equalsEpsilon = function (\n  left,\n  right,\n  relativeEpsilon,\n  absoluteEpsilon\n) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      CesiumMath.equalsEpsilon(\n        left.x,\n        right.x,\n        relativeEpsilon,\n        absoluteEpsilon\n      ) &&\n      CesiumMath.equalsEpsilon(\n        left.y,\n        right.y,\n        relativeEpsilon,\n        absoluteEpsilon\n      ))\n  );\n};\n\n/**\n * An immutable Cartesian2 instance initialized to (0.0, 0.0).\n *\n * @type {Cartesian2}\n * @constant\n */\nCartesian2.ZERO = Object.freeze(new Cartesian2(0.0, 0.0));\n\n/**\n * An immutable Cartesian2 instance initialized to (1.0, 1.0).\n *\n * @type {Cartesian2}\n * @constant\n */\nCartesian2.ONE = Object.freeze(new Cartesian2(1.0, 1.0));\n\n/**\n * An immutable Cartesian2 instance initialized to (1.0, 0.0).\n *\n * @type {Cartesian2}\n * @constant\n */\nCartesian2.UNIT_X = Object.freeze(new Cartesian2(1.0, 0.0));\n\n/**\n * An immutable Cartesian2 instance initialized to (0.0, 1.0).\n *\n * @type {Cartesian2}\n * @constant\n */\nCartesian2.UNIT_Y = Object.freeze(new Cartesian2(0.0, 1.0));\n\n/**\n * Duplicates this Cartesian2 instance.\n *\n * @param {Cartesian2} [result] The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter or a new Cartesian2 instance if one was not provided.\n */\nCartesian2.prototype.clone = function (result) {\n  return Cartesian2.clone(this, result);\n};\n\n/**\n * Compares this Cartesian against the provided Cartesian componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Cartesian2} [right] The right hand side Cartesian.\n * @returns {Boolean} <code>true</code> if they are equal, <code>false</code> otherwise.\n */\nCartesian2.prototype.equals = function (right) {\n  return Cartesian2.equals(this, right);\n};\n\n/**\n * Compares this Cartesian against the provided Cartesian componentwise and returns\n * <code>true</code> if they pass an absolute or relative tolerance test,\n * <code>false</code> otherwise.\n *\n * @param {Cartesian2} [right] The right hand side Cartesian.\n * @param {Number} [relativeEpsilon=0] The relative epsilon tolerance to use for equality testing.\n * @param {Number} [absoluteEpsilon=relativeEpsilon] The absolute epsilon tolerance to use for equality testing.\n * @returns {Boolean} <code>true</code> if they are within the provided epsilon, <code>false</code> otherwise.\n */\nCartesian2.prototype.equalsEpsilon = function (\n  right,\n  relativeEpsilon,\n  absoluteEpsilon\n) {\n  return Cartesian2.equalsEpsilon(\n    this,\n    right,\n    relativeEpsilon,\n    absoluteEpsilon\n  );\n};\n\n/**\n * Creates a string representing this Cartesian in the format '(x, y)'.\n *\n * @returns {String} A string representing the provided Cartesian in the format '(x, y)'.\n */\nCartesian2.prototype.toString = function () {\n  return \"(\" + this.x + \", \" + this.y + \")\";\n};\nexport default Cartesian2;\n", "import Cartesian2 from \"./Cartesian2.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\n\n/**\n * A 2x2 matrix, indexable as a column-major order array.\n * Constructor parameters are in row-major order for code readability.\n * @alias Matrix2\n * @constructor\n * @implements {ArrayLike<number>}\n *\n * @param {Number} [column0Row0=0.0] The value for column 0, row 0.\n * @param {Number} [column1Row0=0.0] The value for column 1, row 0.\n * @param {Number} [column0Row1=0.0] The value for column 0, row 1.\n * @param {Number} [column1Row1=0.0] The value for column 1, row 1.\n *\n * @see Matrix2.fromColumnMajorArray\n * @see Matrix2.fromRowMajorArray\n * @see Matrix2.fromScale\n * @see Matrix2.fromUniformScale\n * @see Matrix3\n * @see Matrix4\n */\nfunction Matrix2(column0Row0, column1Row0, column0Row1, column1Row1) {\n  this[0] = defaultValue(column0Row0, 0.0);\n  this[1] = defaultValue(column0Row1, 0.0);\n  this[2] = defaultValue(column1Row0, 0.0);\n  this[3] = defaultValue(column1Row1, 0.0);\n}\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nMatrix2.packedLength = 4;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {Matrix2} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nMatrix2.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  array[startingIndex++] = value[0];\n  array[startingIndex++] = value[1];\n  array[startingIndex++] = value[2];\n  array[startingIndex++] = value[3];\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {Matrix2} [result] The object into which to store the result.\n * @returns {Matrix2} The modified result parameter or a new Matrix2 instance if one was not provided.\n */\nMatrix2.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Matrix2();\n  }\n\n  result[0] = array[startingIndex++];\n  result[1] = array[startingIndex++];\n  result[2] = array[startingIndex++];\n  result[3] = array[startingIndex++];\n  return result;\n};\n\n/**\n * Duplicates a Matrix2 instance.\n *\n * @param {Matrix2} matrix The matrix to duplicate.\n * @param {Matrix2} [result] The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter or a new Matrix2 instance if one was not provided. (Returns undefined if matrix is undefined)\n */\nMatrix2.clone = function (matrix, result) {\n  if (!defined(matrix)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    return new Matrix2(matrix[0], matrix[2], matrix[1], matrix[3]);\n  }\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[3];\n  return result;\n};\n\n/**\n * Creates a Matrix2 from 4 consecutive elements in an array.\n *\n * @param {Number[]} array The array whose 4 consecutive elements correspond to the positions of the matrix.  Assumes column-major order.\n * @param {Number} [startingIndex=0] The offset into the array of the first element, which corresponds to first column first row position in the matrix.\n * @param {Matrix2} [result] The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter or a new Matrix2 instance if one was not provided.\n *\n * @example\n * // Create the Matrix2:\n * // [1.0, 2.0]\n * // [1.0, 2.0]\n *\n * const v = [1.0, 1.0, 2.0, 2.0];\n * const m = Cesium.Matrix2.fromArray(v);\n *\n * // Create same Matrix2 with using an offset into an array\n * const v2 = [0.0, 0.0, 1.0, 1.0, 2.0, 2.0];\n * const m2 = Cesium.Matrix2.fromArray(v2, 2);\n */\nMatrix2.fromArray = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new Matrix2();\n  }\n\n  result[0] = array[startingIndex];\n  result[1] = array[startingIndex + 1];\n  result[2] = array[startingIndex + 2];\n  result[3] = array[startingIndex + 3];\n  return result;\n};\n\n/**\n * Creates a Matrix2 instance from a column-major order array.\n *\n * @param {Number[]} values The column-major order array.\n * @param {Matrix2} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix2} The modified result parameter, or a new Matrix2 instance if one was not provided.\n */\nMatrix2.fromColumnMajorArray = function (values, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"values\", values);\n  //>>includeEnd('debug');\n\n  return Matrix2.clone(values, result);\n};\n\n/**\n * Creates a Matrix2 instance from a row-major order array.\n * The resulting matrix will be in column-major order.\n *\n * @param {Number[]} values The row-major order array.\n * @param {Matrix2} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix2} The modified result parameter, or a new Matrix2 instance if one was not provided.\n */\nMatrix2.fromRowMajorArray = function (values, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"values\", values);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix2(values[0], values[1], values[2], values[3]);\n  }\n  result[0] = values[0];\n  result[1] = values[2];\n  result[2] = values[1];\n  result[3] = values[3];\n  return result;\n};\n\n/**\n * Computes a Matrix2 instance representing a non-uniform scale.\n *\n * @param {Cartesian2} scale The x and y scale factors.\n * @param {Matrix2} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix2} The modified result parameter, or a new Matrix2 instance if one was not provided.\n *\n * @example\n * // Creates\n * //   [7.0, 0.0]\n * //   [0.0, 8.0]\n * const m = Cesium.Matrix2.fromScale(new Cesium.Cartesian2(7.0, 8.0));\n */\nMatrix2.fromScale = function (scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"scale\", scale);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix2(scale.x, 0.0, 0.0, scale.y);\n  }\n\n  result[0] = scale.x;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = scale.y;\n  return result;\n};\n\n/**\n * Computes a Matrix2 instance representing a uniform scale.\n *\n * @param {Number} scale The uniform scale factor.\n * @param {Matrix2} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix2} The modified result parameter, or a new Matrix2 instance if one was not provided.\n *\n * @example\n * // Creates\n * //   [2.0, 0.0]\n * //   [0.0, 2.0]\n * const m = Cesium.Matrix2.fromUniformScale(2.0);\n */\nMatrix2.fromUniformScale = function (scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"scale\", scale);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return new Matrix2(scale, 0.0, 0.0, scale);\n  }\n\n  result[0] = scale;\n  result[1] = 0.0;\n  result[2] = 0.0;\n  result[3] = scale;\n  return result;\n};\n\n/**\n * Creates a rotation matrix.\n *\n * @param {Number} angle The angle, in radians, of the rotation.  Positive angles are counterclockwise.\n * @param {Matrix2} [result] The object in which the result will be stored, if undefined a new instance will be created.\n * @returns {Matrix2} The modified result parameter, or a new Matrix2 instance if one was not provided.\n *\n * @example\n * // Rotate a point 45 degrees counterclockwise.\n * const p = new Cesium.Cartesian2(5, 6);\n * const m = Cesium.Matrix2.fromRotation(Cesium.Math.toRadians(45.0));\n * const rotated = Cesium.Matrix2.multiplyByVector(m, p, new Cesium.Cartesian2());\n */\nMatrix2.fromRotation = function (angle, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"angle\", angle);\n  //>>includeEnd('debug');\n\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n\n  if (!defined(result)) {\n    return new Matrix2(cosAngle, -sinAngle, sinAngle, cosAngle);\n  }\n  result[0] = cosAngle;\n  result[1] = sinAngle;\n  result[2] = -sinAngle;\n  result[3] = cosAngle;\n  return result;\n};\n\n/**\n * Creates an Array from the provided Matrix2 instance.\n * The array will be in column-major order.\n *\n * @param {Matrix2} matrix The matrix to use..\n * @param {Number[]} [result] The Array onto which to store the result.\n * @returns {Number[]} The modified Array parameter or a new Array instance if one was not provided.\n */\nMatrix2.toArray = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    return [matrix[0], matrix[1], matrix[2], matrix[3]];\n  }\n  result[0] = matrix[0];\n  result[1] = matrix[1];\n  result[2] = matrix[2];\n  result[3] = matrix[3];\n  return result;\n};\n\n/**\n * Computes the array index of the element at the provided row and column.\n *\n * @param {Number} row The zero-based index of the row.\n * @param {Number} column The zero-based index of the column.\n * @returns {Number} The index of the element at the provided row and column.\n *\n * @exception {DeveloperError} row must be 0 or 1.\n * @exception {DeveloperError} column must be 0 or 1.\n *\n * @example\n * const myMatrix = new Cesium.Matrix2();\n * const column1Row0Index = Cesium.Matrix2.getElementIndex(1, 0);\n * const column1Row0 = myMatrix[column1Row0Index]\n * myMatrix[column1Row0Index] = 10.0;\n */\nMatrix2.getElementIndex = function (column, row) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number.greaterThanOrEquals(\"row\", row, 0);\n  Check.typeOf.number.lessThanOrEquals(\"row\", row, 1);\n\n  Check.typeOf.number.greaterThanOrEquals(\"column\", column, 0);\n  Check.typeOf.number.lessThanOrEquals(\"column\", column, 1);\n  //>>includeEnd('debug');\n\n  return column * 2 + row;\n};\n\n/**\n * Retrieves a copy of the matrix column at the provided index as a Cartesian2 instance.\n *\n * @param {Matrix2} matrix The matrix to use.\n * @param {Number} index The zero-based index of the column to retrieve.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0 or 1.\n */\nMatrix2.getColumn = function (matrix, index, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 1);\n\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const startIndex = index * 2;\n  const x = matrix[startIndex];\n  const y = matrix[startIndex + 1];\n\n  result.x = x;\n  result.y = y;\n  return result;\n};\n\n/**\n * Computes a new matrix that replaces the specified column in the provided matrix with the provided Cartesian2 instance.\n *\n * @param {Matrix2} matrix The matrix to use.\n * @param {Number} index The zero-based index of the column to set.\n * @param {Cartesian2} cartesian The Cartesian whose values will be assigned to the specified column.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0 or 1.\n */\nMatrix2.setColumn = function (matrix, index, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 1);\n\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result = Matrix2.clone(matrix, result);\n  const startIndex = index * 2;\n  result[startIndex] = cartesian.x;\n  result[startIndex + 1] = cartesian.y;\n  return result;\n};\n\n/**\n * Retrieves a copy of the matrix row at the provided index as a Cartesian2 instance.\n *\n * @param {Matrix2} matrix The matrix to use.\n * @param {Number} index The zero-based index of the row to retrieve.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0 or 1.\n */\nMatrix2.getRow = function (matrix, index, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 1);\n\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const x = matrix[index];\n  const y = matrix[index + 2];\n\n  result.x = x;\n  result.y = y;\n  return result;\n};\n\n/**\n * Computes a new matrix that replaces the specified row in the provided matrix with the provided Cartesian2 instance.\n *\n * @param {Matrix2} matrix The matrix to use.\n * @param {Number} index The zero-based index of the row to set.\n * @param {Cartesian2} cartesian The Cartesian whose values will be assigned to the specified row.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n *\n * @exception {DeveloperError} index must be 0 or 1.\n */\nMatrix2.setRow = function (matrix, index, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n\n  Check.typeOf.number.greaterThanOrEquals(\"index\", index, 0);\n  Check.typeOf.number.lessThanOrEquals(\"index\", index, 1);\n\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result = Matrix2.clone(matrix, result);\n  result[index] = cartesian.x;\n  result[index + 2] = cartesian.y;\n  return result;\n};\n\nconst scratchColumn = new Cartesian2();\n\n/**\n * Extracts the non-uniform scale assuming the matrix is an affine transformation.\n *\n * @param {Matrix2} matrix The matrix.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nMatrix2.getScale = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result.x = Cartesian2.magnitude(\n    Cartesian2.fromElements(matrix[0], matrix[1], scratchColumn)\n  );\n  result.y = Cartesian2.magnitude(\n    Cartesian2.fromElements(matrix[2], matrix[3], scratchColumn)\n  );\n  return result;\n};\n\nconst scratchScale = new Cartesian2();\n\n/**\n * Computes the maximum scale assuming the matrix is an affine transformation.\n * The maximum scale is the maximum length of the column vectors.\n *\n * @param {Matrix2} matrix The matrix.\n * @returns {Number} The maximum scale.\n */\nMatrix2.getMaximumScale = function (matrix) {\n  Matrix2.getScale(matrix, scratchScale);\n  return Cartesian2.maximumComponent(scratchScale);\n};\n\n/**\n * Computes the product of two matrices.\n *\n * @param {Matrix2} left The first matrix.\n * @param {Matrix2} right The second matrix.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n */\nMatrix2.multiply = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const column0Row0 = left[0] * right[0] + left[2] * right[1];\n  const column1Row0 = left[0] * right[2] + left[2] * right[3];\n  const column0Row1 = left[1] * right[0] + left[3] * right[1];\n  const column1Row1 = left[1] * right[2] + left[3] * right[3];\n\n  result[0] = column0Row0;\n  result[1] = column0Row1;\n  result[2] = column1Row0;\n  result[3] = column1Row1;\n  return result;\n};\n\n/**\n * Computes the sum of two matrices.\n *\n * @param {Matrix2} left The first matrix.\n * @param {Matrix2} right The second matrix.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n */\nMatrix2.add = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = left[0] + right[0];\n  result[1] = left[1] + right[1];\n  result[2] = left[2] + right[2];\n  result[3] = left[3] + right[3];\n  return result;\n};\n\n/**\n * Computes the difference of two matrices.\n *\n * @param {Matrix2} left The first matrix.\n * @param {Matrix2} right The second matrix.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n */\nMatrix2.subtract = function (left, right, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"left\", left);\n  Check.typeOf.object(\"right\", right);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = left[0] - right[0];\n  result[1] = left[1] - right[1];\n  result[2] = left[2] - right[2];\n  result[3] = left[3] - right[3];\n  return result;\n};\n\n/**\n * Computes the product of a matrix and a column vector.\n *\n * @param {Matrix2} matrix The matrix.\n * @param {Cartesian2} cartesian The column.\n * @param {Cartesian2} result The object onto which to store the result.\n * @returns {Cartesian2} The modified result parameter.\n */\nMatrix2.multiplyByVector = function (matrix, cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"cartesian\", cartesian);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const x = matrix[0] * cartesian.x + matrix[2] * cartesian.y;\n  const y = matrix[1] * cartesian.x + matrix[3] * cartesian.y;\n\n  result.x = x;\n  result.y = y;\n  return result;\n};\n\n/**\n * Computes the product of a matrix and a scalar.\n *\n * @param {Matrix2} matrix The matrix.\n * @param {Number} scalar The number to multiply by.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n */\nMatrix2.multiplyByScalar = function (matrix, scalar, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.number(\"scalar\", scalar);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = matrix[0] * scalar;\n  result[1] = matrix[1] * scalar;\n  result[2] = matrix[2] * scalar;\n  result[3] = matrix[3] * scalar;\n  return result;\n};\n\n/**\n * Computes the product of a matrix times a (non-uniform) scale, as if the scale were a scale matrix.\n *\n * @param {Matrix2} matrix The matrix on the left-hand side.\n * @param {Cartesian2} scale The non-uniform scale on the right-hand side.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n *\n *\n * @example\n * // Instead of Cesium.Matrix2.multiply(m, Cesium.Matrix2.fromScale(scale), m);\n * Cesium.Matrix2.multiplyByScale(m, scale, m);\n *\n * @see Matrix2.fromScale\n * @see Matrix2.multiplyByUniformScale\n */\nMatrix2.multiplyByScale = function (matrix, scale, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"scale\", scale);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = matrix[0] * scale.x;\n  result[1] = matrix[1] * scale.x;\n  result[2] = matrix[2] * scale.y;\n  result[3] = matrix[3] * scale.y;\n  return result;\n};\n\n/**\n * Creates a negated copy of the provided matrix.\n *\n * @param {Matrix2} matrix The matrix to negate.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n */\nMatrix2.negate = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = -matrix[0];\n  result[1] = -matrix[1];\n  result[2] = -matrix[2];\n  result[3] = -matrix[3];\n  return result;\n};\n\n/**\n * Computes the transpose of the provided matrix.\n *\n * @param {Matrix2} matrix The matrix to transpose.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n */\nMatrix2.transpose = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  const column0Row0 = matrix[0];\n  const column0Row1 = matrix[2];\n  const column1Row0 = matrix[1];\n  const column1Row1 = matrix[3];\n\n  result[0] = column0Row0;\n  result[1] = column0Row1;\n  result[2] = column1Row0;\n  result[3] = column1Row1;\n  return result;\n};\n\n/**\n * Computes a matrix, which contains the absolute (unsigned) values of the provided matrix's elements.\n *\n * @param {Matrix2} matrix The matrix with signed elements.\n * @param {Matrix2} result The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter.\n */\nMatrix2.abs = function (matrix, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"matrix\", matrix);\n  Check.typeOf.object(\"result\", result);\n  //>>includeEnd('debug');\n\n  result[0] = Math.abs(matrix[0]);\n  result[1] = Math.abs(matrix[1]);\n  result[2] = Math.abs(matrix[2]);\n  result[3] = Math.abs(matrix[3]);\n\n  return result;\n};\n\n/**\n * Compares the provided matrices componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Matrix2} [left] The first matrix.\n * @param {Matrix2} [right] The second matrix.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nMatrix2.equals = function (left, right) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      left[0] === right[0] &&\n      left[1] === right[1] &&\n      left[2] === right[2] &&\n      left[3] === right[3])\n  );\n};\n\n/**\n * @private\n */\nMatrix2.equalsArray = function (matrix, array, offset) {\n  return (\n    matrix[0] === array[offset] &&\n    matrix[1] === array[offset + 1] &&\n    matrix[2] === array[offset + 2] &&\n    matrix[3] === array[offset + 3]\n  );\n};\n\n/**\n * Compares the provided matrices componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Matrix2} [left] The first matrix.\n * @param {Matrix2} [right] The second matrix.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if left and right are within the provided epsilon, <code>false</code> otherwise.\n */\nMatrix2.equalsEpsilon = function (left, right, epsilon) {\n  epsilon = defaultValue(epsilon, 0);\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      Math.abs(left[0] - right[0]) <= epsilon &&\n      Math.abs(left[1] - right[1]) <= epsilon &&\n      Math.abs(left[2] - right[2]) <= epsilon &&\n      Math.abs(left[3] - right[3]) <= epsilon)\n  );\n};\n\n/**\n * An immutable Matrix2 instance initialized to the identity matrix.\n *\n * @type {Matrix2}\n * @constant\n */\nMatrix2.IDENTITY = Object.freeze(new Matrix2(1.0, 0.0, 0.0, 1.0));\n\n/**\n * An immutable Matrix2 instance initialized to the zero matrix.\n *\n * @type {Matrix2}\n * @constant\n */\nMatrix2.ZERO = Object.freeze(new Matrix2(0.0, 0.0, 0.0, 0.0));\n\n/**\n * The index into Matrix2 for column 0, row 0.\n *\n * @type {Number}\n * @constant\n *\n * @example\n * const matrix = new Cesium.Matrix2();\n * matrix[Cesium.Matrix2.COLUMN0ROW0] = 5.0; // set column 0, row 0 to 5.0\n */\nMatrix2.COLUMN0ROW0 = 0;\n\n/**\n * The index into Matrix2 for column 0, row 1.\n *\n * @type {Number}\n * @constant\n *\n * @example\n * const matrix = new Cesium.Matrix2();\n * matrix[Cesium.Matrix2.COLUMN0ROW1] = 5.0; // set column 0, row 1 to 5.0\n */\nMatrix2.COLUMN0ROW1 = 1;\n\n/**\n * The index into Matrix2 for column 1, row 0.\n *\n * @type {Number}\n * @constant\n *\n * @example\n * const matrix = new Cesium.Matrix2();\n * matrix[Cesium.Matrix2.COLUMN1ROW0] = 5.0; // set column 1, row 0 to 5.0\n */\nMatrix2.COLUMN1ROW0 = 2;\n\n/**\n * The index into Matrix2 for column 1, row 1.\n *\n * @type {Number}\n * @constant\n *\n * @example\n * const matrix = new Cesium.Matrix2();\n * matrix[Cesium.Matrix2.COLUMN1ROW1] = 5.0; // set column 1, row 1 to 5.0\n */\nMatrix2.COLUMN1ROW1 = 3;\n\nObject.defineProperties(Matrix2.prototype, {\n  /**\n   * Gets the number of items in the collection.\n   * @memberof Matrix2.prototype\n   *\n   * @type {Number}\n   */\n  length: {\n    get: function () {\n      return Matrix2.packedLength;\n    },\n  },\n});\n\n/**\n * Duplicates the provided Matrix2 instance.\n *\n * @param {Matrix2} [result] The object onto which to store the result.\n * @returns {Matrix2} The modified result parameter or a new Matrix2 instance if one was not provided.\n */\nMatrix2.prototype.clone = function (result) {\n  return Matrix2.clone(this, result);\n};\n\n/**\n * Compares this matrix to the provided matrix componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {Matrix2} [right] The right hand side matrix.\n * @returns {Boolean} <code>true</code> if they are equal, <code>false</code> otherwise.\n */\nMatrix2.prototype.equals = function (right) {\n  return Matrix2.equals(this, right);\n};\n\n/**\n * Compares this matrix to the provided matrix componentwise and returns\n * <code>true</code> if they are within the provided epsilon,\n * <code>false</code> otherwise.\n *\n * @param {Matrix2} [right] The right hand side matrix.\n * @param {Number} [epsilon=0] The epsilon to use for equality testing.\n * @returns {Boolean} <code>true</code> if they are within the provided epsilon, <code>false</code> otherwise.\n */\nMatrix2.prototype.equalsEpsilon = function (right, epsilon) {\n  return Matrix2.equalsEpsilon(this, right, epsilon);\n};\n\n/**\n * Creates a string representing this Matrix with each row being\n * on a separate line and in the format '(column0, column1)'.\n *\n * @returns {String} A string representing the provided Matrix with each row being on a separate line and in the format '(column0, column1)'.\n */\nMatrix2.prototype.toString = function () {\n  return (\n    \"(\" +\n    this[0] +\n    \", \" +\n    this[2] +\n    \")\\n\" +\n    \"(\" +\n    this[1] +\n    \", \" +\n    this[3] +\n    \")\"\n  );\n};\nexport default Matrix2;\n"], "names": ["Cartesian3", "x", "y", "z", "this", "defaultValue", "fromSpherical", "spherical", "result", "Check", "typeOf", "object", "defined", "clock", "cone", "magnitude", "radial", "Math", "sin", "cos", "fromElements", "clone", "cartesian", "fromCartesian4", "<PERSON><PERSON><PERSON><PERSON>", "pack", "value", "array", "startingIndex", "unpack", "packArray", "length", "result<PERSON><PERSON><PERSON>", "Array", "isArray", "DeveloperError", "i", "unpackArray", "number", "greaterThanOrEquals", "index", "fromArray", "maximumComponent", "max", "minimumComponent", "min", "minimumByComponent", "first", "second", "maximumByComponent", "magnitudeSquared", "sqrt", "distanceScratch", "distance", "left", "right", "subtract", "distanceSquared", "normalize", "isNaN", "dot", "multiplyComponents", "divideComponents", "add", "multiplyByScalar", "scalar", "divideByScalar", "negate", "abs", "lerpScratch", "lerp", "start", "end", "t", "angleBetweenScratch", "angleBetweenScratch2", "angleBetween", "cosine", "sine", "cross", "atan2", "mostOrthogonalAxisScratch", "mostOrthogonalAxis", "f", "UNIT_X", "UNIT_Z", "UNIT_Y", "projectVector", "a", "b", "equals", "equalsArray", "offset", "equalsEpsilon", "relativeEpsilon", "absoluteEpsilon", "CesiumMath", "leftX", "leftY", "leftZ", "rightX", "rightY", "rightZ", "midpoint", "fromDegrees", "longitude", "latitude", "height", "ellipsoid", "toRadians", "fromRadians", "scratchN", "scratchK", "wgs84RadiiSquared", "radiiSquared", "cosLatitude", "gamma", "fromDegreesArray", "coordinates", "fromRadiansArray", "fromDegreesArrayHeights", "fromRadiansArrayHeights", "ZERO", "Object", "freeze", "ONE", "prototype", "toString", "scaleToGeodeticSurfaceIntersection", "scaleToGeodeticSurfaceGradient", "scaleToGeodeticSurface", "oneOverRadii", "oneOverRadiiSquared", "centerToleranceSquared", "positionX", "positionY", "positionZ", "oneOverRadiiX", "oneOverRadiiY", "oneOverRadiiZ", "x2", "y2", "z2", "squaredNorm", "ratio", "intersection", "isFinite", "undefined", "oneOverRadiiSquaredX", "oneOverRadiiSquaredY", "oneOverRadiiSquaredZ", "gradient", "func", "denominator", "xMultiplier", "yMultiplier", "zMultiplier", "xMultiplier2", "yMultiplier2", "zMultiplier2", "xMultiplier3", "yMultiplier3", "zMultiplier3", "lambda", "correction", "EPSILON12", "Cartographic", "cartesianToCartographicN", "cartesianToCartographicP", "cartesianToCartographicH", "wgs84OneOverRadii", "wgs84OneOverRadiiSquared", "wgs84CenterToleranceSquared", "EPSILON1", "initialize", "_radii", "_radiiSquared", "_radiiToTheFourth", "_oneOverRadii", "_oneOverRadiiSquared", "_minimumRadius", "_maximumRadius", "_centerToleranceSquared", "_squaredXOverSquaredZ", "Ellipsoid", "fromCartesian", "p", "n", "h", "asin", "sign", "toCartesian", "cartographic", "epsilon", "defineProperties", "radii", "get", "radiiToTheFourth", "minimumRadius", "maximumRadius", "fromCartesian3", "WGS84", "UNIT_SPHERE", "MOON", "LUNAR_RADIUS", "geocentricSurfaceNormal", "geodeticSurfaceNormalCartographic", "geodeticSurfaceNormal", "EPSILON14", "cartographicToCartesianNormal", "cartographicToCartesianK", "cartographicToCartesian", "k", "cartographicArrayToCartesianArray", "cartographics", "cartesianToCartographic", "cartesianArrayToCartographicArray", "cartesians", "scaleToGeocentricSurface", "beta", "transformPositionToScaledSpace", "position", "transformPositionFromScaledSpace", "getSurfaceNormalIntersectionWithZAxis", "buffer", "EPSILON15", "greaterThan", "squaredXOverSquaredZ", "abscissas", "weights", "gaussLegendreQuadrature", "xMean", "xRange", "sum", "dx", "Matrix3", "column0Row0", "column1Row0", "column2Row0", "column0Row1", "column1Row1", "column2Row1", "column0Row2", "column1Row2", "column2Row2", "surfaceArea", "rectangle", "minLongitude", "west", "maxLongitude", "east", "minLatitude", "south", "maxLatitude", "north", "TWO_PI", "a2", "b2", "c2", "a2b2", "lat", "sinPhi", "cosPhi", "lon", "cosTheta", "sinTheta", "matrix", "fromColumnMajorArray", "values", "fromRowMajorArray", "fromQuaternion", "quaternion", "xy", "xz", "xw", "w", "yz", "yw", "zw", "w2", "m00", "m01", "m02", "m10", "m11", "m12", "m20", "m21", "m22", "fromHeadingPitchRoll", "headingPitchRoll", "pitch", "cosPsi", "heading", "roll", "sinPsi", "fromScale", "scale", "fromUniformScale", "fromCrossProduct", "vector", "fromRotationX", "angle", "cosAngle", "sinAngle", "fromRotationY", "fromRotationZ", "toArray", "getElementIndex", "column", "row", "lessThanOrEquals", "getColumn", "startIndex", "setColumn", "getRow", "setRow", "scratchColumn", "getScale", "scratchScale", "getMaximumScale", "multiply", "multiplyByVector", "vX", "vY", "vZ", "multiplyByScale", "transpose", "UNIT", "getRotation", "inverseScale", "rowVal", "colVal", "offDiagonalFrobeniusNorm", "norm", "temp", "shurDecomposition", "tolerance", "maxDiagonal", "rotAxis", "c", "s", "q", "tau", "IDENTITY", "jMatrix", "jMatrixTranspose", "computeEigenDecomposition", "EPSILON20", "count", "sweep", "unitaryMatrix", "unitary", "diagMatrix", "diagonal", "computeFrobeniusNorm", "determinant", "m31", "m32", "m13", "m23", "m33", "inverse", "scratchTransposeMatrix", "Cartesian4", "inverseTranspose", "COLUMN0ROW0", "COLUMN0ROW1", "COLUMN0ROW2", "COLUMN1ROW0", "COLUMN1ROW1", "COLUMN1ROW2", "COLUMN2ROW0", "COLUMN2ROW1", "COLUMN2ROW2", "fromColor", "color", "red", "green", "blue", "alpha", "UNIT_W", "scratchF32Array", "Float32Array", "scratchU8Array", "Uint8Array", "testU32", "Uint32Array", "littleEndian", "Matrix4", "column3Row0", "column3Row1", "column3Row2", "column0Row3", "column1Row3", "column2Row3", "column3Row3", "packFloat", "unpackFloat", "packedFloat", "fromRotationTranslation", "rotation", "translation", "fromTranslationQuaternionRotationScale", "scaleX", "scaleY", "scaleZ", "fromTranslationRotationScale", "translationRotationScale", "fromTranslation", "fromCameraF", "fromCameraR", "fromCameraU", "fromCamera", "camera", "direction", "up", "sX", "sY", "sZ", "fX", "fY", "fZ", "uX", "uY", "uZ", "t0", "t1", "t2", "computePerspectiveFieldOfView", "fovY", "aspectRatio", "near", "far", "lessThan", "PI", "tan", "computeOrthographicOffCenter", "bottom", "top", "tx", "ty", "tz", "computePerspectiveOffCenter", "computeInfinitePerspectiveOffCenter", "computeViewportTransformation", "viewport", "nearDepthRange", "farDepthRange", "EMPTY_OBJECT", "width", "halfWidth", "halfHeight", "half<PERSON><PERSON>h", "computeView", "setTranslation", "scaleScratch", "setScale", "existingScale", "newScale", "left0", "left1", "left2", "left3", "left4", "left5", "left6", "left7", "left8", "left9", "left10", "left11", "left12", "left13", "left14", "left15", "right0", "right1", "right2", "right3", "right4", "right5", "right6", "right7", "right8", "right9", "right10", "right11", "right12", "right13", "right14", "right15", "multiplyTransformation", "multiplyByMatrix3", "multiplyByTranslation", "uniformScaleScratch", "multiplyByUniformScale", "vW", "multiplyByPointAsVector", "multiplyByPoint", "matrix1", "matrix2", "matrix3", "matrix6", "matrix7", "matrix11", "getTranslation", "getMatrix3", "scratchInverseRotation", "scratchMatrix3Zero", "scratchBottomRow", "scratchExpectedBottomRow", "src0", "src1", "src2", "src3", "src4", "src5", "src6", "src7", "src8", "src9", "src10", "src11", "src12", "src13", "src14", "src15", "tmp0", "tmp1", "tmp2", "tmp3", "tmp4", "tmp5", "tmp6", "tmp7", "tmp8", "tmp9", "tmp10", "tmp11", "dst0", "dst1", "dst2", "dst3", "dst4", "dst5", "dst6", "dst7", "dst8", "dst9", "dst10", "dst11", "dst12", "dst13", "dst14", "dst15", "det", "EPSILON21", "EPSILON7", "RuntimeError", "inverseTransformation", "matrix0", "matrix4", "matrix5", "matrix8", "matrix9", "matrix10", "Rectangle", "COLUMN0ROW3", "COLUMN1ROW3", "COLUMN2ROW3", "COLUMN3ROW0", "COLUMN3ROW1", "COLUMN3ROW2", "COLUMN3ROW3", "computeWidth", "computeHeight", "fromCartographicArray", "Number", "MAX_VALUE", "westOverIDL", "eastOverIDL", "len", "lonAdjusted", "fromCartesianArray", "other", "validate", "PI_OVER_TWO", "southwest", "northwest", "northeast", "southeast", "center", "negativePiToPi", "otherRectangle", "rectangleEast", "rectangleWest", "otherRectangleEast", "otherRectangleWest", "simpleIntersection", "union", "expand", "contains", "subsampleLlaScratch", "Cartesian2", "subsample", "surfaceHeight", "lla", "acosClamped", "Matrix2", "fromRotation"], "mappings": "8HAmBA,SAASA,EAAWC,EAAGC,EAAGC,GAMxBC,KAAKH,EAAII,EAAAA,aAAaJ,EAAG,GAOzBG,KAAKF,EAAIG,EAAAA,aAAaH,EAAG,GAOzBE,KAAKD,EAAIE,EAAAA,aAAaF,EAAG,EAC3B,CASAH,EAAWM,cAAgB,SAAUC,EAAWC,GAE9CC,EAAAA,MAAMC,OAAOC,OAAO,YAAaJ,GAG5BK,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAGf,MAAMa,EAAQN,EAAUM,MAClBC,EAAOP,EAAUO,KACjBC,EAAYV,EAAYA,aAACE,EAAUQ,UAAW,GAC9CC,EAASD,EAAYE,KAAKC,IAAIJ,GAIpC,OAHAN,EAAOP,EAAIe,EAASC,KAAKE,IAAIN,GAC7BL,EAAON,EAAIc,EAASC,KAAKC,IAAIL,GAC7BL,EAAOL,EAAIY,EAAYE,KAAKE,IAAIL,GACzBN,CACT,EAWAR,EAAWoB,aAAe,SAAUnB,EAAGC,EAAGC,EAAGK,GAC3C,OAAKI,EAAAA,QAAQJ,IAIbA,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACJK,GANE,IAAIR,EAAWC,EAAGC,EAAGC,EAOhC,EASAH,EAAWqB,MAAQ,SAAUC,EAAWd,GACtC,GAAKI,EAAAA,QAAQU,GAGb,OAAKV,EAAAA,QAAQJ,IAIbA,EAAOP,EAAIqB,EAAUrB,EACrBO,EAAON,EAAIoB,EAAUpB,EACrBM,EAAOL,EAAImB,EAAUnB,EACdK,GANE,IAAIR,EAAWsB,EAAUrB,EAAGqB,EAAUpB,EAAGoB,EAAUnB,EAO9D,EAWAH,EAAWuB,eAAiBvB,EAAWqB,MAMvCrB,EAAWwB,aAAe,EAW1BxB,EAAWyB,KAAO,SAAUC,EAAOC,EAAOC,GAYxC,OAVAnB,EAAAA,MAAMC,OAAOC,OAAO,QAASe,GAC7BjB,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5CD,EAAMC,KAAmBF,EAAMzB,EAC/B0B,EAAMC,KAAmBF,EAAMxB,EAC/ByB,EAAMC,GAAiBF,EAAMvB,EAEtBwB,CACT,EAUA3B,EAAW6B,OAAS,SAAUF,EAAOC,EAAepB,GAalD,OAXAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAEfQ,EAAOP,EAAI0B,EAAMC,KACjBpB,EAAON,EAAIyB,EAAMC,KACjBpB,EAAOL,EAAIwB,EAAMC,GACVpB,CACT,EASAR,EAAW8B,UAAY,SAAUH,EAAOnB,GAEtCC,EAAAA,MAAMG,QAAQ,QAASe,GAGvB,MAAMI,EAASJ,EAAMI,OACfC,EAAwB,EAATD,EACrB,GAAKnB,EAAAA,QAAQJ,GAEN,KAAKyB,MAAMC,QAAQ1B,IAAWA,EAAOuB,SAAWC,EACrD,MAAM,IAAIG,EAAcA,eACtB,8EAEO3B,EAAOuB,SAAWC,IAC3BxB,EAAOuB,OAASC,EACjB,MAPCxB,EAAS,IAAIyB,MAAMD,GASrB,IAAK,IAAII,EAAI,EAAGA,EAAIL,IAAUK,EAC5BpC,EAAWyB,KAAKE,EAAMS,GAAI5B,EAAY,EAAJ4B,GAEpC,OAAO5B,CACT,EASAR,EAAWqC,YAAc,SAAUV,EAAOnB,GAIxC,GAFAC,EAAAA,MAAMG,QAAQ,QAASe,GACvBlB,QAAMC,OAAO4B,OAAOC,oBAAoB,eAAgBZ,EAAMI,OAAQ,GAClEJ,EAAMI,OAAS,GAAM,EACvB,MAAM,IAAII,EAAAA,eAAe,yCAI3B,MAAMJ,EAASJ,EAAMI,OAChBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAAS,EAFzBvB,EAAS,IAAIyB,MAAMF,EAAS,GAK9B,IAAK,IAAIK,EAAI,EAAGA,EAAIL,EAAQK,GAAK,EAAG,CAClC,MAAMI,EAAQJ,EAAI,EAClB5B,EAAOgC,GAASxC,EAAW6B,OAAOF,EAAOS,EAAG5B,EAAOgC,GACpD,CACD,OAAOhC,CACT,EAoBAR,EAAWyC,UAAYzC,EAAW6B,OAQlC7B,EAAW0C,iBAAmB,SAAUpB,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAG1BL,KAAK0B,IAAIrB,EAAUrB,EAAGqB,EAAUpB,EAAGoB,EAAUnB,EACtD,EAQAH,EAAW4C,iBAAmB,SAAUtB,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAG1BL,KAAK4B,IAAIvB,EAAUrB,EAAGqB,EAAUpB,EAAGoB,EAAUnB,EACtD,EAUAH,EAAW8C,mBAAqB,SAAUC,EAAOC,EAAQxC,GAWvD,OATAC,EAAAA,MAAMC,OAAOC,OAAO,QAASoC,GAC7BtC,EAAAA,MAAMC,OAAOC,OAAO,SAAUqC,GAC9BvC,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAK4B,IAAIE,EAAM9C,EAAG+C,EAAO/C,GACpCO,EAAON,EAAIe,KAAK4B,IAAIE,EAAM7C,EAAG8C,EAAO9C,GACpCM,EAAOL,EAAIc,KAAK4B,IAAIE,EAAM5C,EAAG6C,EAAO7C,GAE7BK,CACT,EAUAR,EAAWiD,mBAAqB,SAAUF,EAAOC,EAAQxC,GAUvD,OARAC,EAAAA,MAAMC,OAAOC,OAAO,QAASoC,GAC7BtC,EAAAA,MAAMC,OAAOC,OAAO,SAAUqC,GAC9BvC,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAK0B,IAAII,EAAM9C,EAAG+C,EAAO/C,GACpCO,EAAON,EAAIe,KAAK0B,IAAII,EAAM7C,EAAG8C,EAAO9C,GACpCM,EAAOL,EAAIc,KAAK0B,IAAII,EAAM5C,EAAG6C,EAAO7C,GAC7BK,CACT,EAQAR,EAAWkD,iBAAmB,SAAU5B,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAI/BA,EAAUrB,EAAIqB,EAAUrB,EACxBqB,EAAUpB,EAAIoB,EAAUpB,EACxBoB,EAAUnB,EAAImB,EAAUnB,CAE5B,EAQAH,EAAWe,UAAY,SAAUO,GAC/B,OAAOL,KAAKkC,KAAKnD,EAAWkD,iBAAiB5B,GAC/C,EAEA,MAAM8B,EAAkB,IAAIpD,EAa5BA,EAAWqD,SAAW,SAAUC,EAAMC,GAOpC,OALA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAG7BvD,EAAWwD,SAASF,EAAMC,EAAOH,GAC1BpD,EAAWe,UAAUqC,EAC9B,EAcApD,EAAWyD,gBAAkB,SAAUH,EAAMC,GAO3C,OALA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAG7BvD,EAAWwD,SAASF,EAAMC,EAAOH,GAC1BpD,EAAWkD,iBAAiBE,EACrC,EASApD,EAAW0D,UAAY,SAAUpC,EAAWd,GAE1CC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMO,EAAYf,EAAWe,UAAUO,GAOvC,GALAd,EAAOP,EAAIqB,EAAUrB,EAAIc,EACzBP,EAAON,EAAIoB,EAAUpB,EAAIa,EACzBP,EAAOL,EAAImB,EAAUnB,EAAIY,EAGrB4C,MAAMnD,EAAOP,IAAM0D,MAAMnD,EAAON,IAAMyD,MAAMnD,EAAOL,GACrD,MAAM,IAAIgC,EAAAA,eAAe,qCAI3B,OAAO3B,CACT,EASAR,EAAW4D,IAAM,SAAUN,EAAMC,GAM/B,OAJA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAGtBD,EAAKrD,EAAIsD,EAAMtD,EAAIqD,EAAKpD,EAAIqD,EAAMrD,EAAIoD,EAAKnD,EAAIoD,EAAMpD,CAC9D,EAUAH,EAAW6D,mBAAqB,SAAUP,EAAMC,EAAO/C,GAUrD,OARAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EAC1BM,EAAOL,EAAImD,EAAKnD,EAAIoD,EAAMpD,EACnBK,CACT,EAUAR,EAAW8D,iBAAmB,SAAUR,EAAMC,EAAO/C,GAUnD,OARAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EAC1BM,EAAOL,EAAImD,EAAKnD,EAAIoD,EAAMpD,EACnBK,CACT,EAUAR,EAAW+D,IAAM,SAAUT,EAAMC,EAAO/C,GAUtC,OARAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EAC1BM,EAAOL,EAAImD,EAAKnD,EAAIoD,EAAMpD,EACnBK,CACT,EAUAR,EAAWwD,SAAW,SAAUF,EAAMC,EAAO/C,GAU3C,OARAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EAC1BM,EAAOL,EAAImD,EAAKnD,EAAIoD,EAAMpD,EACnBK,CACT,EAUAR,EAAWgE,iBAAmB,SAAU1C,EAAW2C,EAAQzD,GAUzD,OARAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqB,EAAUrB,EAAIgE,EACzBzD,EAAON,EAAIoB,EAAUpB,EAAI+D,EACzBzD,EAAOL,EAAImB,EAAUnB,EAAI8D,EAClBzD,CACT,EAUAR,EAAWkE,eAAiB,SAAU5C,EAAW2C,EAAQzD,GAUvD,OARAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqB,EAAUrB,EAAIgE,EACzBzD,EAAON,EAAIoB,EAAUpB,EAAI+D,EACzBzD,EAAOL,EAAImB,EAAUnB,EAAI8D,EAClBzD,CACT,EASAR,EAAWmE,OAAS,SAAU7C,EAAWd,GASvC,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,GAAKqB,EAAUrB,EACtBO,EAAON,GAAKoB,EAAUpB,EACtBM,EAAOL,GAAKmB,EAAUnB,EACfK,CACT,EASAR,EAAWoE,IAAM,SAAU9C,EAAWd,GASpC,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAKmD,IAAI9C,EAAUrB,GAC9BO,EAAON,EAAIe,KAAKmD,IAAI9C,EAAUpB,GAC9BM,EAAOL,EAAIc,KAAKmD,IAAI9C,EAAUnB,GACvBK,CACT,EAEA,MAAM6D,EAAc,IAAIrE,EAUxBA,EAAWsE,KAAO,SAAUC,EAAOC,EAAKC,EAAGjE,GAUzC,OARAC,EAAAA,MAAMC,OAAOC,OAAO,QAAS4D,GAC7B9D,EAAAA,MAAMC,OAAOC,OAAO,MAAO6D,GAC3B/D,EAAAA,MAAMC,OAAO4B,OAAO,IAAKmC,GACzBhE,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BR,EAAWgE,iBAAiBQ,EAAKC,EAAGJ,GACpC7D,EAASR,EAAWgE,iBAAiBO,EAAO,EAAME,EAAGjE,GAC9CR,EAAW+D,IAAIM,EAAa7D,EAAQA,EAC7C,EAEA,MAAMkE,EAAsB,IAAI1E,EAC1B2E,EAAuB,IAAI3E,EAQjCA,EAAW4E,aAAe,SAAUtB,EAAMC,GAExC9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAG7BvD,EAAW0D,UAAUJ,EAAMoB,GAC3B1E,EAAW0D,UAAUH,EAAOoB,GAC5B,MAAME,EAAS7E,EAAW4D,IAAIc,EAAqBC,GAC7CG,EAAO9E,EAAWe,UACtBf,EAAW+E,MACTL,EACAC,EACAD,IAGJ,OAAOzD,KAAK+D,MAAMF,EAAMD,EAC1B,EAEA,MAAMI,EAA4B,IAAIjF,EAQtCA,EAAWkF,mBAAqB,SAAU5D,EAAWd,GAEnDC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM2E,EAAInF,EAAW0D,UAAUpC,EAAW2D,GAe1C,OAdAjF,EAAWoE,IAAIe,EAAGA,GAId3E,EAFA2E,EAAElF,GAAKkF,EAAEjF,EACPiF,EAAElF,GAAKkF,EAAEhF,EACFH,EAAWqB,MAAMrB,EAAWoF,OAAQ5E,GAEpCR,EAAWqB,MAAMrB,EAAWqF,OAAQ7E,GAEtC2E,EAAEjF,GAAKiF,EAAEhF,EACTH,EAAWqB,MAAMrB,EAAWsF,OAAQ9E,GAEpCR,EAAWqB,MAAMrB,EAAWqF,OAAQ7E,EAIjD,EASAR,EAAWuF,cAAgB,SAAUC,EAAGC,EAAGjF,GAEzCC,EAAAA,MAAMG,QAAQ,IAAK4E,GACnB/E,EAAAA,MAAMG,QAAQ,IAAK6E,GACnBhF,EAAAA,MAAMG,QAAQ,SAAUJ,GAGxB,MAAMyD,EAASjE,EAAW4D,IAAI4B,EAAGC,GAAKzF,EAAW4D,IAAI6B,EAAGA,GACxD,OAAOzF,EAAWgE,iBAAiByB,EAAGxB,EAAQzD,EAChD,EAUAR,EAAW0F,OAAS,SAAUpC,EAAMC,GAClC,OACED,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRD,EAAKrD,IAAMsD,EAAMtD,GACjBqD,EAAKpD,IAAMqD,EAAMrD,GACjBoD,EAAKnD,IAAMoD,EAAMpD,CAEvB,EAKAH,EAAW2F,YAAc,SAAUrE,EAAWK,EAAOiE,GACnD,OACEtE,EAAUrB,IAAM0B,EAAMiE,IACtBtE,EAAUpB,IAAMyB,EAAMiE,EAAS,IAC/BtE,EAAUnB,IAAMwB,EAAMiE,EAAS,EAEnC,EAaA5F,EAAW6F,cAAgB,SACzBvC,EACAC,EACAuC,EACAC,GAEA,OACEzC,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRyC,EAAAA,WAAWH,cACTvC,EAAKrD,EACLsD,EAAMtD,EACN6F,EACAC,IAEFC,EAAAA,WAAWH,cACTvC,EAAKpD,EACLqD,EAAMrD,EACN4F,EACAC,IAEFC,EAAAA,WAAWH,cACTvC,EAAKnD,EACLoD,EAAMpD,EACN2F,EACAC,EAGR,EAUA/F,EAAW+E,MAAQ,SAAUzB,EAAMC,EAAO/C,GAExCC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMyF,EAAQ3C,EAAKrD,EACbiG,EAAQ5C,EAAKpD,EACbiG,EAAQ7C,EAAKnD,EACbiG,EAAS7C,EAAMtD,EACfoG,EAAS9C,EAAMrD,EACfoG,EAAS/C,EAAMpD,EAEfF,EAAIiG,EAAQI,EAASH,EAAQE,EAC7BnG,EAAIiG,EAAQC,EAASH,EAAQK,EAC7BnG,EAAI8F,EAAQI,EAASH,EAAQE,EAKnC,OAHA5F,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACJK,CACT,EASAR,EAAWuG,SAAW,SAAUjD,EAAMC,EAAO/C,GAW3C,OATAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAyB,IAApBqD,EAAKrD,EAAIsD,EAAMtD,GAC3BO,EAAON,EAAyB,IAApBoD,EAAKpD,EAAIqD,EAAMrD,GAC3BM,EAAOL,EAAyB,IAApBmD,EAAKnD,EAAIoD,EAAMpD,GAEpBK,CACT,EAeAR,EAAWwG,YAAc,SACvBC,EACAC,EACAC,EACAC,EACApG,GASA,OANAC,EAAAA,MAAMC,OAAO4B,OAAO,YAAamE,GACjChG,EAAAA,MAAMC,OAAO4B,OAAO,WAAYoE,GAGhCD,EAAYT,EAAUA,WAACa,UAAUJ,GACjCC,EAAWV,EAAUA,WAACa,UAAUH,GACzB1G,EAAW8G,YAAYL,EAAWC,EAAUC,EAAQC,EAAWpG,EACxE,EAEA,IAAIuG,EAAW,IAAI/G,EACfgH,EAAW,IAAIhH,EACnB,MAAMiH,EAAoB,IAAIjH,EAC5B,eACA,eACA,oBAgBFA,EAAW8G,YAAc,SACvBL,EACAC,EACAC,EACAC,EACApG,GAGAC,EAAAA,MAAMC,OAAO4B,OAAO,YAAamE,GACjChG,EAAAA,MAAMC,OAAO4B,OAAO,WAAYoE,GAGhCC,EAAStG,EAAYA,aAACsG,EAAQ,GAC9B,MAAMO,EAAetG,EAAOA,QAACgG,GACzBA,EAAUM,aACVD,EAEEE,EAAclG,KAAKE,IAAIuF,GAC7BK,EAAS9G,EAAIkH,EAAclG,KAAKE,IAAIsF,GACpCM,EAAS7G,EAAIiH,EAAclG,KAAKC,IAAIuF,GACpCM,EAAS5G,EAAIc,KAAKC,IAAIwF,GACtBK,EAAW/G,EAAW0D,UAAUqD,EAAUA,GAE1C/G,EAAW6D,mBAAmBqD,EAAcH,EAAUC,GACtD,MAAMI,EAAQnG,KAAKkC,KAAKnD,EAAW4D,IAAImD,EAAUC,IAOjD,OANAA,EAAWhH,EAAWkE,eAAe8C,EAAUI,EAAOJ,GACtDD,EAAW/G,EAAWgE,iBAAiB+C,EAAUJ,EAAQI,GAEpDnG,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAERA,EAAW+D,IAAIiD,EAAUD,EAAUvG,EAC5C,EAaAR,EAAWqH,iBAAmB,SAAUC,EAAaV,EAAWpG,GAG9D,GADAC,EAAAA,MAAMG,QAAQ,cAAe0G,GACzBA,EAAYvF,OAAS,GAAKuF,EAAYvF,OAAS,GAAM,EACvD,MAAM,IAAII,EAAcA,eACtB,oEAKJ,MAAMJ,EAASuF,EAAYvF,OACtBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAAS,EAFzBvB,EAAS,IAAIyB,MAAMF,EAAS,GAK9B,IAAK,IAAIK,EAAI,EAAGA,EAAIL,EAAQK,GAAK,EAAG,CAClC,MAAMqE,EAAYa,EAAYlF,GACxBsE,EAAWY,EAAYlF,EAAI,GAC3BI,EAAQJ,EAAI,EAClB5B,EAAOgC,GAASxC,EAAWwG,YACzBC,EACAC,EACA,EACAE,EACApG,EAAOgC,GAEV,CAED,OAAOhC,CACT,EAaAR,EAAWuH,iBAAmB,SAAUD,EAAaV,EAAWpG,GAG9D,GADAC,EAAAA,MAAMG,QAAQ,cAAe0G,GACzBA,EAAYvF,OAAS,GAAKuF,EAAYvF,OAAS,GAAM,EACvD,MAAM,IAAII,EAAcA,eACtB,oEAKJ,MAAMJ,EAASuF,EAAYvF,OACtBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAAS,EAFzBvB,EAAS,IAAIyB,MAAMF,EAAS,GAK9B,IAAK,IAAIK,EAAI,EAAGA,EAAIL,EAAQK,GAAK,EAAG,CAClC,MAAMqE,EAAYa,EAAYlF,GACxBsE,EAAWY,EAAYlF,EAAI,GAC3BI,EAAQJ,EAAI,EAClB5B,EAAOgC,GAASxC,EAAW8G,YACzBL,EACAC,EACA,EACAE,EACApG,EAAOgC,GAEV,CAED,OAAOhC,CACT,EAaAR,EAAWwH,wBAA0B,SAAUF,EAAaV,EAAWpG,GAGrE,GADAC,EAAAA,MAAMG,QAAQ,cAAe0G,GACzBA,EAAYvF,OAAS,GAAKuF,EAAYvF,OAAS,GAAM,EACvD,MAAM,IAAII,EAAcA,eACtB,oEAKJ,MAAMJ,EAASuF,EAAYvF,OACtBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAAS,EAFzBvB,EAAS,IAAIyB,MAAMF,EAAS,GAK9B,IAAK,IAAIK,EAAI,EAAGA,EAAIL,EAAQK,GAAK,EAAG,CAClC,MAAMqE,EAAYa,EAAYlF,GACxBsE,EAAWY,EAAYlF,EAAI,GAC3BuE,EAASW,EAAYlF,EAAI,GACzBI,EAAQJ,EAAI,EAClB5B,EAAOgC,GAASxC,EAAWwG,YACzBC,EACAC,EACAC,EACAC,EACApG,EAAOgC,GAEV,CAED,OAAOhC,CACT,EAaAR,EAAWyH,wBAA0B,SAAUH,EAAaV,EAAWpG,GAGrE,GADAC,EAAAA,MAAMG,QAAQ,cAAe0G,GACzBA,EAAYvF,OAAS,GAAKuF,EAAYvF,OAAS,GAAM,EACvD,MAAM,IAAII,EAAcA,eACtB,oEAKJ,MAAMJ,EAASuF,EAAYvF,OACtBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAAS,EAFzBvB,EAAS,IAAIyB,MAAMF,EAAS,GAK9B,IAAK,IAAIK,EAAI,EAAGA,EAAIL,EAAQK,GAAK,EAAG,CAClC,MAAMqE,EAAYa,EAAYlF,GACxBsE,EAAWY,EAAYlF,EAAI,GAC3BuE,EAASW,EAAYlF,EAAI,GACzBI,EAAQJ,EAAI,EAClB5B,EAAOgC,GAASxC,EAAW8G,YACzBL,EACAC,EACAC,EACAC,EACApG,EAAOgC,GAEV,CAED,OAAOhC,CACT,EAQAR,EAAW0H,KAAOC,OAAOC,OAAO,IAAI5H,EAAW,EAAK,EAAK,IAQzDA,EAAW6H,IAAMF,OAAOC,OAAO,IAAI5H,EAAW,EAAK,EAAK,IAQxDA,EAAWoF,OAASuC,OAAOC,OAAO,IAAI5H,EAAW,EAAK,EAAK,IAQ3DA,EAAWsF,OAASqC,OAAOC,OAAO,IAAI5H,EAAW,EAAK,EAAK,IAQ3DA,EAAWqF,OAASsC,OAAOC,OAAO,IAAI5H,EAAW,EAAK,EAAK,IAQ3DA,EAAW8H,UAAUzG,MAAQ,SAAUb,GACrC,OAAOR,EAAWqB,MAAMjB,KAAMI,EAChC,EASAR,EAAW8H,UAAUpC,OAAS,SAAUnC,GACtC,OAAOvD,EAAW0F,OAAOtF,KAAMmD,EACjC,EAYAvD,EAAW8H,UAAUjC,cAAgB,SACnCtC,EACAuC,EACAC,GAEA,OAAO/F,EAAW6F,cAChBzF,KACAmD,EACAuC,EACAC,EAEJ,EAOA/F,EAAW8H,UAAUC,SAAW,WAC9B,MAAO,IAAM3H,KAAKH,EAAI,KAAOG,KAAKF,EAAI,KAAOE,KAAKD,EAAI,GACxD,EC7pCA,MAAM6H,EAAqC,IAAIhI,EACzCiI,EAAiC,IAAIjI,EAkB3C,SAASkI,EACP5G,EACA6G,EACAC,EACAC,EACA7H,GAGA,IAAKI,EAAAA,QAAQU,GACX,MAAM,IAAIa,EAAAA,eAAe,0BAE3B,IAAKvB,EAAAA,QAAQuH,GACX,MAAM,IAAIhG,EAAAA,eAAe,6BAE3B,IAAKvB,EAAAA,QAAQwH,GACX,MAAM,IAAIjG,EAAAA,eAAe,oCAE3B,IAAKvB,EAAAA,QAAQyH,GACX,MAAM,IAAIlG,EAAAA,eAAe,uCAI3B,MAAMmG,EAAYhH,EAAUrB,EACtBsI,EAAYjH,EAAUpB,EACtBsI,EAAYlH,EAAUnB,EAEtBsI,EAAgBN,EAAalI,EAC7ByI,EAAgBP,EAAajI,EAC7ByI,EAAgBR,EAAahI,EAE7ByI,EAAKN,EAAYA,EAAYG,EAAgBA,EAC7CI,EAAKN,EAAYA,EAAYG,EAAgBA,EAC7CI,EAAKN,EAAYA,EAAYG,EAAgBA,EAG7CI,EAAcH,EAAKC,EAAKC,EACxBE,EAAQ/H,KAAKkC,KAAK,EAAM4F,GAGxBE,EAAejJ,EAAWgE,iBAC9B1C,EACA0H,EACAhB,GAIF,GAAIe,EAAcV,EAChB,OAAQa,SAASF,GAEbhJ,EAAWqB,MAAM4H,EAAczI,QAD/B2I,EAIN,MAAMC,EAAuBhB,EAAoBnI,EAC3CoJ,EAAuBjB,EAAoBlI,EAC3CoJ,EAAuBlB,EAAoBjI,EAI3CoJ,EAAWtB,EACjBsB,EAAStJ,EAAIgJ,EAAahJ,EAAImJ,EAAuB,EACrDG,EAASrJ,EAAI+I,EAAa/I,EAAImJ,EAAuB,EACrDE,EAASpJ,EAAI8I,EAAa9I,EAAImJ,EAAuB,EAGrD,IAKIE,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAfAC,GACA,EAAMnB,GAAShJ,EAAWe,UAAUO,IACrC,GAAMtB,EAAWe,UAAUwI,IAC1Ba,EAAa,EAcjB,EAAG,CACDD,GAAUC,EAEVV,EAAc,GAAO,EAAMS,EAASf,GACpCO,EAAc,GAAO,EAAMQ,EAASd,GACpCO,EAAc,GAAO,EAAMO,EAASb,GAEpCO,EAAeH,EAAcA,EAC7BI,EAAeH,EAAcA,EAC7BI,EAAeH,EAAcA,EAE7BI,EAAeH,EAAeH,EAC9BO,EAAeH,EAAeH,EAC9BO,EAAeH,EAAeH,EAE9BJ,EAAOZ,EAAKiB,EAAehB,EAAKiB,EAAehB,EAAKiB,EAAe,EAInEN,EACEb,EAAKoB,EAAeZ,EACpBP,EAAKoB,EAAeZ,EACpBP,EAAKoB,EAAeZ,EAItBc,EAAaZ,IAFO,EAAMC,EAG3B,OAAQxI,KAAKmD,IAAIoF,GAAQxD,EAAAA,WAAWqE,WAErC,OAAKzJ,EAAAA,QAAQJ,IAObA,EAAOP,EAAIqI,EAAYoB,EACvBlJ,EAAON,EAAIqI,EAAYoB,EACvBnJ,EAAOL,EAAIqI,EAAYoB,EAChBpJ,GATE,IAAIR,EACTsI,EAAYoB,EACZnB,EAAYoB,EACZnB,EAAYoB,EAOlB,CC/HA,SAASU,EAAa7D,EAAWC,EAAUC,GAMzCvG,KAAKqG,UAAYpG,EAAAA,aAAaoG,EAAW,GAOzCrG,KAAKsG,SAAWrG,EAAAA,aAAaqG,EAAU,GAOvCtG,KAAKuG,OAAStG,EAAAA,aAAasG,EAAQ,EACrC,CAYA2D,EAAaxD,YAAc,SAAUL,EAAWC,EAAUC,EAAQnG,GAQhE,OANAC,EAAAA,MAAMC,OAAO4B,OAAO,YAAamE,GACjChG,EAAAA,MAAMC,OAAO4B,OAAO,WAAYoE,GAGhCC,EAAStG,EAAYA,aAACsG,EAAQ,GAEzB/F,EAAAA,QAAQJ,IAIbA,EAAOiG,UAAYA,EACnBjG,EAAOkG,SAAWA,EAClBlG,EAAOmG,OAASA,EACTnG,GANE,IAAI8J,EAAa7D,EAAWC,EAAUC,EAOjD,EAaA2D,EAAa9D,YAAc,SAAUC,EAAWC,EAAUC,EAAQnG,GAQhE,OANAC,EAAAA,MAAMC,OAAO4B,OAAO,YAAamE,GACjChG,EAAAA,MAAMC,OAAO4B,OAAO,WAAYoE,GAEhCD,EAAYT,EAAUA,WAACa,UAAUJ,GACjCC,EAAWV,EAAUA,WAACa,UAAUH,GAEzB4D,EAAaxD,YAAYL,EAAWC,EAAUC,EAAQnG,EAC/D,EAEA,MAAM+J,EAA2B,IAAIvK,EAC/BwK,EAA2B,IAAIxK,EAC/ByK,EAA2B,IAAIzK,EAC/B0K,EAAoB,IAAI1K,EAC5B,EAAM,QACN,EAAM,QACN,EAAM,mBAEF2K,EAA2B,IAAI3K,EACnC,EAAG,eACH,EAAG,eACH,EAAG,oBAEC4K,EAA8B5E,EAAUA,WAAC6E,SC/F/C,SAASC,EAAWlE,EAAW3G,EAAGC,EAAGC,GACnCF,EAAII,EAAYA,aAACJ,EAAG,GACpBC,EAAIG,EAAYA,aAACH,EAAG,GACpBC,EAAIE,EAAYA,aAACF,EAAG,GAGpBM,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,IAAKtC,EAAG,GAChDQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,IAAKrC,EAAG,GAChDO,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,IAAKpC,EAAG,GAGhDyG,EAAUmE,OAAS,IAAI/K,EAAWC,EAAGC,EAAGC,GAExCyG,EAAUoE,cAAgB,IAAIhL,EAAWC,EAAIA,EAAGC,EAAIA,EAAGC,EAAIA,GAE3DyG,EAAUqE,kBAAoB,IAAIjL,EAChCC,EAAIA,EAAIA,EAAIA,EACZC,EAAIA,EAAIA,EAAIA,EACZC,EAAIA,EAAIA,EAAIA,GAGdyG,EAAUsE,cAAgB,IAAIlL,EACtB,IAANC,EAAY,EAAM,EAAMA,EAClB,IAANC,EAAY,EAAM,EAAMA,EAClB,IAANC,EAAY,EAAM,EAAMA,GAG1ByG,EAAUuE,qBAAuB,IAAInL,EAC7B,IAANC,EAAY,EAAM,GAAOA,EAAIA,GACvB,IAANC,EAAY,EAAM,GAAOA,EAAIA,GACvB,IAANC,EAAY,EAAM,GAAOA,EAAIA,IAG/ByG,EAAUwE,eAAiBnK,KAAK4B,IAAI5C,EAAGC,EAAGC,GAE1CyG,EAAUyE,eAAiBpK,KAAK0B,IAAI1C,EAAGC,EAAGC,GAE1CyG,EAAU0E,wBAA0BtF,EAAUA,WAAC6E,SAEb,IAA9BjE,EAAUoE,cAAc7K,IAC1ByG,EAAU2E,sBACR3E,EAAUoE,cAAc/K,EAAI2G,EAAUoE,cAAc7K,EAE1D,CAsBA,SAASqL,EAAUvL,EAAGC,EAAGC,GACvBC,KAAK2K,YAAS5B,EACd/I,KAAK4K,mBAAgB7B,EACrB/I,KAAK6K,uBAAoB9B,EACzB/I,KAAK8K,mBAAgB/B,EACrB/I,KAAK+K,0BAAuBhC,EAC5B/I,KAAKgL,oBAAiBjC,EACtB/I,KAAKiL,oBAAiBlC,EACtB/I,KAAKkL,6BAA0BnC,EAC/B/I,KAAKmL,2BAAwBpC,EAE7B2B,EAAW1K,KAAMH,EAAGC,EAAGC,EACzB,CD6BAmK,EAAamB,cAAgB,SAAUnK,EAAWsF,EAAWpG,GAC3D,MAAM2H,EAAevH,EAAOA,QAACgG,GACzBA,EAAUuB,aACVuC,EACEtC,EAAsBxH,EAAOA,QAACgG,GAChCA,EAAUwB,oBACVuC,EAMEe,EAAIxD,EACR5G,EACA6G,EACAC,EAR6BxH,EAAOA,QAACgG,GACnCA,EAAU0E,wBACVV,EAQFJ,GAGF,IAAK5J,EAAAA,QAAQ8K,GACX,OAGF,IAAIC,EAAI3L,EAAW6D,mBACjB6H,EACAtD,EACAmC,GAEFoB,EAAI3L,EAAW0D,UAAUiI,EAAGA,GAE5B,MAAMC,EAAI5L,EAAWwD,SAASlC,EAAWoK,EAAGjB,GAEtChE,EAAYxF,KAAK+D,MAAM2G,EAAEzL,EAAGyL,EAAE1L,GAC9ByG,EAAWzF,KAAK4K,KAAKF,EAAExL,GACvBwG,EACJX,EAAAA,WAAW8F,KAAK9L,EAAW4D,IAAIgI,EAAGtK,IAActB,EAAWe,UAAU6K,GAEvE,OAAKhL,EAAAA,QAAQJ,IAGbA,EAAOiG,UAAYA,EACnBjG,EAAOkG,SAAWA,EAClBlG,EAAOmG,OAASA,EACTnG,GALE,IAAI8J,EAAa7D,EAAWC,EAAUC,EAMjD,EAWA2D,EAAayB,YAAc,SAAUC,EAAcpF,EAAWpG,GAK5D,OAHAC,EAAAA,MAAMG,QAAQ,eAAgBoL,GAGvBhM,EAAW8G,YAChBkF,EAAavF,UACbuF,EAAatF,SACbsF,EAAarF,OACbC,EACApG,EAEJ,EASA8J,EAAajJ,MAAQ,SAAU2K,EAAcxL,GAC3C,GAAKI,EAAAA,QAAQoL,GAGb,OAAKpL,EAAAA,QAAQJ,IAObA,EAAOiG,UAAYuF,EAAavF,UAChCjG,EAAOkG,SAAWsF,EAAatF,SAC/BlG,EAAOmG,OAASqF,EAAarF,OACtBnG,GATE,IAAI8J,EACT0B,EAAavF,UACbuF,EAAatF,SACbsF,EAAarF,OAOnB,EAUA2D,EAAa5E,OAAS,SAAUpC,EAAMC,GACpC,OACED,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRD,EAAKmD,YAAclD,EAAMkD,WACzBnD,EAAKoD,WAAanD,EAAMmD,UACxBpD,EAAKqD,SAAWpD,EAAMoD,MAE5B,EAYA2D,EAAazE,cAAgB,SAAUvC,EAAMC,EAAO0I,GAGlD,OAFAA,EAAU5L,EAAYA,aAAC4L,EAAS,GAG9B3I,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRtC,KAAKmD,IAAId,EAAKmD,UAAYlD,EAAMkD,YAAcwF,GAC9ChL,KAAKmD,IAAId,EAAKoD,SAAWnD,EAAMmD,WAAauF,GAC5ChL,KAAKmD,IAAId,EAAKqD,OAASpD,EAAMoD,SAAWsF,CAE9C,EAQA3B,EAAa5C,KAAOC,OAAOC,OAAO,IAAI0C,EAAa,EAAK,EAAK,IAQ7DA,EAAaxC,UAAUzG,MAAQ,SAAUb,GACvC,OAAO8J,EAAajJ,MAAMjB,KAAMI,EAClC,EASA8J,EAAaxC,UAAUpC,OAAS,SAAUnC,GACxC,OAAO+G,EAAa5E,OAAOtF,KAAMmD,EACnC,EAWA+G,EAAaxC,UAAUjC,cAAgB,SAAUtC,EAAO0I,GACtD,OAAO3B,EAAazE,cAAczF,KAAMmD,EAAO0I,EACjD,EAOA3B,EAAaxC,UAAUC,SAAW,WAChC,MAAO,IAAM3H,KAAKqG,UAAY,KAAOrG,KAAKsG,SAAW,KAAOtG,KAAKuG,OAAS,GAC5E,ECpNAgB,OAAOuE,iBAAiBV,EAAU1D,UAAW,CAO3CqE,MAAO,CACLC,IAAK,WACH,OAAOhM,KAAK2K,MACb,GAQH7D,aAAc,CACZkF,IAAK,WACH,OAAOhM,KAAK4K,aACb,GAQHqB,iBAAkB,CAChBD,IAAK,WACH,OAAOhM,KAAK6K,iBACb,GAQH9C,aAAc,CACZiE,IAAK,WACH,OAAOhM,KAAK8K,aACb,GAQH9C,oBAAqB,CACnBgE,IAAK,WACH,OAAOhM,KAAK+K,oBACb,GAQHmB,cAAe,CACbF,IAAK,WACH,OAAOhM,KAAKgL,cACb,GAQHmB,cAAe,CACbH,IAAK,WACH,OAAOhM,KAAKiL,cACb,KAYLG,EAAUnK,MAAQ,SAAUuF,EAAWpG,GACrC,IAAKI,EAAAA,QAAQgG,GACX,OAEF,MAAMuF,EAAQvF,EAAUmE,OAExB,OAAKnK,EAAAA,QAAQJ,IAIbR,EAAWqB,MAAM8K,EAAO3L,EAAOuK,QAC/B/K,EAAWqB,MAAMuF,EAAUoE,cAAexK,EAAOwK,eACjDhL,EAAWqB,MAAMuF,EAAUqE,kBAAmBzK,EAAOyK,mBACrDjL,EAAWqB,MAAMuF,EAAUsE,cAAe1K,EAAO0K,eACjDlL,EAAWqB,MAAMuF,EAAUuE,qBAAsB3K,EAAO2K,sBACxD3K,EAAO4K,eAAiBxE,EAAUwE,eAClC5K,EAAO6K,eAAiBzE,EAAUyE,eAClC7K,EAAO8K,wBAA0B1E,EAAU0E,wBAEpC9K,GAZE,IAAIgL,EAAUW,EAAMlM,EAAGkM,EAAMjM,EAAGiM,EAAMhM,EAajD,EAeAqL,EAAUgB,eAAiB,SAAUlL,EAAWd,GAK9C,OAJKI,EAAAA,QAAQJ,KACXA,EAAS,IAAIgL,GAGV5K,EAAAA,QAAQU,IAIbwJ,EAAWtK,EAAQc,EAAUrB,EAAGqB,EAAUpB,EAAGoB,EAAUnB,GAChDK,GAJEA,CAKX,EAQAgL,EAAUiB,MAAQ9E,OAAOC,OACvB,IAAI4D,EAAU,QAAW,QAAW,oBAStCA,EAAUkB,YAAc/E,OAAOC,OAAO,IAAI4D,EAAU,EAAK,EAAK,IAQ9DA,EAAUmB,KAAOhF,OAAOC,OACtB,IAAI4D,EACFxF,EAAAA,WAAW4G,aACX5G,EAAAA,WAAW4G,aACX5G,EAAAA,WAAW4G,eAWfpB,EAAU1D,UAAUzG,MAAQ,SAAUb,GACpC,OAAOgL,EAAUnK,MAAMjB,KAAMI,EAC/B,EAMAgL,EAAUhK,aAAexB,EAAWwB,aAWpCgK,EAAU/J,KAAO,SAAUC,EAAOC,EAAOC,GAUvC,OARAnB,EAAAA,MAAMC,OAAOC,OAAO,QAASe,GAC7BjB,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5C5B,EAAWyB,KAAKC,EAAMqJ,OAAQpJ,EAAOC,GAE9BD,CACT,EAUA6J,EAAU3J,OAAS,SAAUF,EAAOC,EAAepB,GAEjDC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5C,MAAMuK,EAAQnM,EAAW6B,OAAOF,EAAOC,GACvC,OAAO4J,EAAUgB,eAAeL,EAAO3L,EACzC,EAUAgL,EAAU1D,UAAU+E,wBAA0B7M,EAAW0D,UASzD8H,EAAU1D,UAAUgF,kCAAoC,SACtDd,EACAxL,GAGAC,EAAAA,MAAMC,OAAOC,OAAO,eAAgBqL,GAGpC,MAAMvF,EAAYuF,EAAavF,UACzBC,EAAWsF,EAAatF,SACxBS,EAAclG,KAAKE,IAAIuF,GAEvBzG,EAAIkH,EAAclG,KAAKE,IAAIsF,GAC3BvG,EAAIiH,EAAclG,KAAKC,IAAIuF,GAC3BtG,EAAIc,KAAKC,IAAIwF,GAQnB,OANK9F,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAEfQ,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACJH,EAAW0D,UAAUlD,EAAQA,EACtC,EASAgL,EAAU1D,UAAUiF,sBAAwB,SAAUzL,EAAWd,GAC/D,IACER,EAAW6F,cAAcvE,EAAWtB,EAAW0H,KAAM1B,EAAUA,WAACgH,WAYlE,OARKpM,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAEfQ,EAASR,EAAW6D,mBAClBvC,EACAlB,KAAK+K,qBACL3K,GAEKR,EAAW0D,UAAUlD,EAAQA,EACtC,EAEA,MAAMyM,EAAgC,IAAIjN,EACpCkN,EAA2B,IAAIlN,EAcrCwL,EAAU1D,UAAUqF,wBAA0B,SAAUnB,EAAcxL,GAEpE,MAAMmL,EAAIsB,EACJG,EAAIF,EACV9M,KAAK0M,kCAAkCd,EAAcL,GACrD3L,EAAW6D,mBAAmBzD,KAAK4K,cAAeW,EAAGyB,GACrD,MAAMhG,EAAQnG,KAAKkC,KAAKnD,EAAW4D,IAAI+H,EAAGyB,IAO1C,OANApN,EAAWkE,eAAekJ,EAAGhG,EAAOgG,GACpCpN,EAAWgE,iBAAiB2H,EAAGK,EAAarF,OAAQgF,GAE/C/K,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAERA,EAAW+D,IAAIqJ,EAAGzB,EAAGnL,EAC9B,EAgBAgL,EAAU1D,UAAUuF,kCAAoC,SACtDC,EACA9M,GAGAC,EAAAA,MAAMG,QAAQ,gBAAiB0M,GAG/B,MAAMvL,EAASuL,EAAcvL,OACxBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAFhBvB,EAAS,IAAIyB,MAAMF,GAIrB,IAAK,IAAIK,EAAI,EAAGA,EAAIL,EAAQK,IAC1B5B,EAAO4B,GAAKhC,KAAK+M,wBAAwBG,EAAclL,GAAI5B,EAAO4B,IAEpE,OAAO5B,CACT,EAEA,MAAM+J,EAA2B,IAAIvK,EAC/BwK,EAA2B,IAAIxK,EAC/ByK,EAA2B,IAAIzK,EAerCwL,EAAU1D,UAAUyF,wBAA0B,SAAUjM,EAAWd,GAEjE,MAAMkL,EAAItL,KAAK8H,uBAAuB5G,EAAWkJ,GAEjD,IAAK5J,EAAAA,QAAQ8K,GACX,OAGF,MAAMC,EAAIvL,KAAK2M,sBAAsBrB,EAAGnB,GAClCqB,EAAI5L,EAAWwD,SAASlC,EAAWoK,EAAGjB,GAEtChE,EAAYxF,KAAK+D,MAAM2G,EAAEzL,EAAGyL,EAAE1L,GAC9ByG,EAAWzF,KAAK4K,KAAKF,EAAExL,GACvBwG,EACJX,EAAAA,WAAW8F,KAAK9L,EAAW4D,IAAIgI,EAAGtK,IAActB,EAAWe,UAAU6K,GAEvE,OAAKhL,EAAAA,QAAQJ,IAGbA,EAAOiG,UAAYA,EACnBjG,EAAOkG,SAAWA,EAClBlG,EAAOmG,OAASA,EACTnG,GALE,IAAI8J,EAAa7D,EAAWC,EAAUC,EAMjD,EAgBA6E,EAAU1D,UAAU0F,kCAAoC,SACtDC,EACAjN,GAGAC,EAAAA,MAAMG,QAAQ,aAAc6M,GAG5B,MAAM1L,EAAS0L,EAAW1L,OACrBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAFhBvB,EAAS,IAAIyB,MAAMF,GAIrB,IAAK,IAAIK,EAAI,EAAGA,EAAIL,IAAUK,EAC5B5B,EAAO4B,GAAKhC,KAAKmN,wBAAwBE,EAAWrL,GAAI5B,EAAO4B,IAEjE,OAAO5B,CACT,EAWAgL,EAAU1D,UAAUI,uBAAyB,SAAU5G,EAAWd,GAChE,OAAO0H,EACL5G,EACAlB,KAAK8K,cACL9K,KAAK+K,qBACL/K,KAAKkL,wBACL9K,EAEJ,EAUAgL,EAAU1D,UAAU4F,yBAA2B,SAAUpM,EAAWd,GAElEC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAG5BV,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAGf,MAAMsI,EAAYhH,EAAUrB,EACtBsI,EAAYjH,EAAUpB,EACtBsI,EAAYlH,EAAUnB,EACtBiI,EAAsBhI,KAAK+K,qBAE3BwC,EACJ,EACA1M,KAAKkC,KACHmF,EAAYA,EAAYF,EAAoBnI,EAC1CsI,EAAYA,EAAYH,EAAoBlI,EAC5CsI,EAAYA,EAAYJ,EAAoBjI,GAGlD,OAAOH,EAAWgE,iBAAiB1C,EAAWqM,EAAMnN,EACtD,EAYAgL,EAAU1D,UAAU8F,+BAAiC,SACnDC,EACArN,GAMA,OAJKI,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAGRA,EAAW6D,mBAAmBgK,EAAUzN,KAAK8K,cAAe1K,EACrE,EAYAgL,EAAU1D,UAAUgG,iCAAmC,SACrDD,EACArN,GAMA,OAJKI,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAGRA,EAAW6D,mBAAmBgK,EAAUzN,KAAK2K,OAAQvK,EAC9D,EASAgL,EAAU1D,UAAUpC,OAAS,SAAUnC,GACrC,OACEnD,OAASmD,GACR3C,UAAQ2C,IAAUvD,EAAW0F,OAAOtF,KAAK2K,OAAQxH,EAAMwH,OAE5D,EAOAS,EAAU1D,UAAUC,SAAW,WAC7B,OAAO3H,KAAK2K,OAAOhD,UACrB,EAkBAyD,EAAU1D,UAAUiG,sCAAwC,SAC1DF,EACAG,EACAxN,GAKA,GAFAC,EAAAA,MAAMC,OAAOC,OAAO,WAAYkN,IAG7B7H,EAAUA,WAACH,cACVzF,KAAK2K,OAAO9K,EACZG,KAAK2K,OAAO7K,EACZ8F,EAAAA,WAAWiI,WAGb,MAAM,IAAI9L,EAAcA,eACtB,qEAIJ1B,QAAMC,OAAO4B,OAAO4L,YAAY,oBAAqB9N,KAAK2K,OAAO5K,EAAG,GAGpE6N,EAAS3N,EAAYA,aAAC2N,EAAQ,GAE9B,MAAMG,EAAuB/N,KAAKmL,sBAUlC,GARK3K,EAAAA,QAAQJ,KACXA,EAAS,IAAIR,GAGfQ,EAAOP,EAAI,EACXO,EAAON,EAAI,EACXM,EAAOL,EAAI0N,EAAS1N,GAAK,EAAIgO,KAEzBlN,KAAKmD,IAAI5D,EAAOL,IAAMC,KAAK2K,OAAO5K,EAAI6N,GAI1C,OAAOxN,CACT,EAEA,MAAM4N,EAAY,CAChB,gBACA,gBACA,gBACA,gBACA,gBACA,GAEIC,EAAU,CACd,gBACA,gBACA,gBACA,gBACA,iBACA,GAaF,SAASC,EAAwB9I,EAAGC,EAAG+D,GAErC/I,EAAAA,MAAMC,OAAO4B,OAAO,IAAKkD,GACzB/E,EAAAA,MAAMC,OAAO4B,OAAO,IAAKmD,GACzBhF,EAAAA,MAAMC,OAAO8I,KAAK,OAAQA,GAK1B,MAAM+E,EAAQ,IAAO9I,EAAID,GACnBgJ,EAAS,IAAO/I,EAAID,GAE1B,IAAIiJ,EAAM,EACV,IAAK,IAAIrM,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAC1B,MAAMsM,EAAKF,EAASJ,EAAUhM,GAC9BqM,GAAOJ,EAAQjM,IAAMoH,EAAK+E,EAAQG,GAAMlF,EAAK+E,EAAQG,GACtD,CAID,OADAD,GAAOD,EACAC,CACT,CCxsBA,SAASE,EACPC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAEAhP,KAAK,GAAKC,EAAYA,aAACuO,EAAa,GACpCxO,KAAK,GAAKC,EAAYA,aAAC0O,EAAa,GACpC3O,KAAK,GAAKC,EAAYA,aAAC6O,EAAa,GACpC9O,KAAK,GAAKC,EAAYA,aAACwO,EAAa,GACpCzO,KAAK,GAAKC,EAAYA,aAAC2O,EAAa,GACpC5O,KAAK,GAAKC,EAAYA,aAAC8O,EAAa,GACpC/O,KAAK,GAAKC,EAAYA,aAACyO,EAAa,GACpC1O,KAAK,GAAKC,EAAYA,aAAC4O,EAAa,GACpC7O,KAAK,GAAKC,EAAYA,aAAC+O,EAAa,EACtC,CDusBA5D,EAAU1D,UAAUuH,YAAc,SAAUC,GAE1C7O,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAEjC,MAAMC,EAAeD,EAAUE,KAC/B,IAAIC,EAAeH,EAAUI,KAC7B,MAAMC,EAAcL,EAAUM,MACxBC,EAAcP,EAAUQ,MAE9B,KAAOL,EAAeF,GACpBE,GAAgBzJ,EAAUA,WAAC+J,OAG7B,MAAM7I,EAAe9G,KAAK4K,cACpBgF,EAAK9I,EAAajH,EAClBgQ,EAAK/I,EAAahH,EAClBgQ,EAAKhJ,EAAa/G,EAClBgQ,EAAOH,EAAKC,EAClB,OAAO3B,EAAwBqB,EAAaE,GAAa,SAAUO,GAGjE,MAAMC,EAASpP,KAAKE,IAAIiP,GAClBE,EAASrP,KAAKC,IAAIkP,GACxB,OACEnP,KAAKE,IAAIiP,GACT9B,EAAwBiB,EAAcE,GAAc,SAAUc,GAC5D,MAAMC,EAAWvP,KAAKE,IAAIoP,GACpBE,EAAWxP,KAAKC,IAAIqP,GAC1B,OAAOtP,KAAKkC,KACVgN,EAAOG,EAASA,EACdJ,GACGD,EAAKO,EAAWA,EAAWR,EAAKS,EAAWA,GAC5CJ,EACAA,EAEd,GAEA,GACA,ECvuBA1B,EAAQnN,aAAe,EAWvBmN,EAAQlN,KAAO,SAAUC,EAAOC,EAAOC,GAkBrC,OAhBAnB,EAAAA,MAAMC,OAAOC,OAAO,QAASe,GAC7BjB,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5CD,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAExBC,CACT,EAUAgN,EAAQ9M,OAAS,SAAUF,EAAOC,EAAepB,GAoB/C,OAlBAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAImO,GAGfnO,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KACXpB,CACT,EASAmO,EAAQtN,MAAQ,SAAUqP,EAAQlQ,GAChC,GAAKI,EAAAA,QAAQ8P,GAGb,OAAK9P,EAAAA,QAAQJ,IAabA,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACZlQ,GArBE,IAAImO,EACT+B,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GAab,EAuBA/B,EAAQlM,UAAY,SAAUd,EAAOC,EAAepB,GAoBlD,OAlBAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAImO,GAGfnO,EAAO,GAAKmB,EAAMC,GAClBpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAC3BpB,CACT,EASAmO,EAAQgC,qBAAuB,SAAUC,EAAQpQ,GAK/C,OAHAC,EAAAA,MAAMG,QAAQ,SAAUgQ,GAGjBjC,EAAQtN,MAAMuP,EAAQpQ,EAC/B,EAUAmO,EAAQkC,kBAAoB,SAAUD,EAAQpQ,GAK5C,OAHAC,EAAAA,MAAMG,QAAQ,SAAUgQ,GAGnBhQ,EAAAA,QAAQJ,IAabA,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACZpQ,GArBE,IAAImO,EACTiC,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GAab,EASAjC,EAAQmC,eAAiB,SAAUC,EAAYvQ,GAE7CC,EAAAA,MAAMC,OAAOC,OAAO,aAAcoQ,GAGlC,MAAMnI,EAAKmI,EAAW9Q,EAAI8Q,EAAW9Q,EAC/B+Q,EAAKD,EAAW9Q,EAAI8Q,EAAW7Q,EAC/B+Q,EAAKF,EAAW9Q,EAAI8Q,EAAW5Q,EAC/B+Q,EAAKH,EAAW9Q,EAAI8Q,EAAWI,EAC/BtI,EAAKkI,EAAW7Q,EAAI6Q,EAAW7Q,EAC/BkR,EAAKL,EAAW7Q,EAAI6Q,EAAW5Q,EAC/BkR,EAAKN,EAAW7Q,EAAI6Q,EAAWI,EAC/BrI,EAAKiI,EAAW5Q,EAAI4Q,EAAW5Q,EAC/BmR,EAAKP,EAAW5Q,EAAI4Q,EAAWI,EAC/BI,EAAKR,EAAWI,EAAIJ,EAAWI,EAE/BK,EAAM5I,EAAKC,EAAKC,EAAKyI,EACrBE,EAAM,GAAOT,EAAKM,GAClBI,EAAM,GAAOT,EAAKI,GAElBM,EAAM,GAAOX,EAAKM,GAClBM,GAAOhJ,EAAKC,EAAKC,EAAKyI,EACtBM,EAAM,GAAOT,EAAKF,GAElBY,EAAM,GAAOb,EAAKI,GAClBU,EAAM,GAAOX,EAAKF,GAClBc,GAAOpJ,EAAKC,EAAKC,EAAKyI,EAE5B,OAAK3Q,EAAAA,QAAQJ,IAGbA,EAAO,GAAKgR,EACZhR,EAAO,GAAKmR,EACZnR,EAAO,GAAKsR,EACZtR,EAAO,GAAKiR,EACZjR,EAAO,GAAKoR,EACZpR,EAAO,GAAKuR,EACZvR,EAAO,GAAKkR,EACZlR,EAAO,GAAKqR,EACZrR,EAAO,GAAKwR,EACLxR,GAXE,IAAImO,EAAQ6C,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAY/D,EASArD,EAAQsD,qBAAuB,SAAUC,EAAkB1R,GAEzDC,EAAAA,MAAMC,OAAOC,OAAO,mBAAoBuR,GAGxC,MAAM1B,EAAWvP,KAAKE,KAAK+Q,EAAiBC,OACtCC,EAASnR,KAAKE,KAAK+Q,EAAiBG,SACpC/B,EAASrP,KAAKE,IAAI+Q,EAAiBI,MACnC7B,EAAWxP,KAAKC,KAAKgR,EAAiBC,OACtCI,EAAStR,KAAKC,KAAKgR,EAAiBG,SACpChC,EAASpP,KAAKC,IAAIgR,EAAiBI,MAEnCd,EAAMhB,EAAW4B,EACjBX,GAAOnB,EAASiC,EAASlC,EAASI,EAAW2B,EAC7CV,EAAMrB,EAASkC,EAASjC,EAASG,EAAW2B,EAE5CT,EAAMnB,EAAW+B,EACjBX,EAAMtB,EAAS8B,EAAS/B,EAASI,EAAW8B,EAC5CV,GAAOxB,EAAS+B,EAAS9B,EAASG,EAAW8B,EAE7CT,GAAOrB,EACPsB,EAAM1B,EAASG,EACfwB,EAAM1B,EAASE,EAErB,OAAK5P,EAAAA,QAAQJ,IAGbA,EAAO,GAAKgR,EACZhR,EAAO,GAAKmR,EACZnR,EAAO,GAAKsR,EACZtR,EAAO,GAAKiR,EACZjR,EAAO,GAAKoR,EACZpR,EAAO,GAAKuR,EACZvR,EAAO,GAAKkR,EACZlR,EAAO,GAAKqR,EACZrR,EAAO,GAAKwR,EACLxR,GAXE,IAAImO,EAAQ6C,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAY/D,EAgBArD,EAAQ6D,UAAY,SAAUC,EAAOjS,GAKnC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,QAAS8R,GAGxB7R,EAAAA,QAAQJ,IAIbA,EAAO,GAAKiS,EAAMxS,EAClBO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiS,EAAMvS,EAClBM,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiS,EAAMtS,EACXK,GAZE,IAAImO,EAAQ8D,EAAMxS,EAAG,EAAK,EAAK,EAAKwS,EAAMvS,EAAG,EAAK,EAAK,EAAKuS,EAAMtS,EAa7E,EAgBAwO,EAAQ+D,iBAAmB,SAAUD,EAAOjS,GAK1C,OAHAC,EAAAA,MAAMC,OAAO4B,OAAO,QAASmQ,GAGxB7R,EAAAA,QAAQJ,IAIbA,EAAO,GAAKiS,EACZjS,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiS,EACZjS,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiS,EACLjS,GAZE,IAAImO,EAAQ8D,EAAO,EAAK,EAAK,EAAKA,EAAO,EAAK,EAAK,EAAKA,EAanE,EAgBA9D,EAAQgE,iBAAmB,SAAUC,EAAQpS,GAK3C,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,SAAUiS,GAGzBhS,EAAAA,QAAQJ,IAcbA,EAAO,GAAK,EACZA,EAAO,GAAKoS,EAAOzS,EACnBK,EAAO,IAAMoS,EAAO1S,EACpBM,EAAO,IAAMoS,EAAOzS,EACpBK,EAAO,GAAK,EACZA,EAAO,GAAKoS,EAAO3S,EACnBO,EAAO,GAAKoS,EAAO1S,EACnBM,EAAO,IAAMoS,EAAO3S,EACpBO,EAAO,GAAK,EACLA,GAtBE,IAAImO,EACT,GACCiE,EAAOzS,EACRyS,EAAO1S,EACP0S,EAAOzS,EACP,GACCyS,EAAO3S,GACP2S,EAAO1S,EACR0S,EAAO3S,EACP,EAcN,EAeA0O,EAAQkE,cAAgB,SAAUC,EAAOtS,GAEvCC,EAAAA,MAAMC,OAAO4B,OAAO,QAASwQ,GAG7B,MAAMC,EAAW9R,KAAKE,IAAI2R,GACpBE,EAAW/R,KAAKC,IAAI4R,GAE1B,OAAKlS,EAAAA,QAAQJ,IAcbA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKuS,EACZvS,EAAO,GAAKwS,EACZxS,EAAO,GAAK,EACZA,EAAO,IAAMwS,EACbxS,EAAO,GAAKuS,EAELvS,GAvBE,IAAImO,EACT,EACA,EACA,EACA,EACAoE,GACCC,EACD,EACAA,EACAD,EAeN,EAeApE,EAAQsE,cAAgB,SAAUH,EAAOtS,GAEvCC,EAAAA,MAAMC,OAAO4B,OAAO,QAASwQ,GAG7B,MAAMC,EAAW9R,KAAKE,IAAI2R,GACpBE,EAAW/R,KAAKC,IAAI4R,GAE1B,OAAKlS,EAAAA,QAAQJ,IAcbA,EAAO,GAAKuS,EACZvS,EAAO,GAAK,EACZA,EAAO,IAAMwS,EACbxS,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKwS,EACZxS,EAAO,GAAK,EACZA,EAAO,GAAKuS,EAELvS,GAvBE,IAAImO,EACToE,EACA,EACAC,EACA,EACA,EACA,GACCA,EACD,EACAD,EAeN,EAeApE,EAAQuE,cAAgB,SAAUJ,EAAOtS,GAEvCC,EAAAA,MAAMC,OAAO4B,OAAO,QAASwQ,GAG7B,MAAMC,EAAW9R,KAAKE,IAAI2R,GACpBE,EAAW/R,KAAKC,IAAI4R,GAE1B,OAAKlS,EAAAA,QAAQJ,IAcbA,EAAO,GAAKuS,EACZvS,EAAO,GAAKwS,EACZxS,EAAO,GAAK,EACZA,EAAO,IAAMwS,EACbxS,EAAO,GAAKuS,EACZvS,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EAELA,GAvBE,IAAImO,EACToE,GACCC,EACD,EACAA,EACAD,EACA,EACA,EACA,EACA,EAeN,EAUApE,EAAQwE,QAAU,SAAUzC,EAAQlQ,GAKlC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAGzB9P,EAAAA,QAAQJ,IAabA,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACZlQ,GArBE,CACLkQ,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GAab,EAkBA/B,EAAQyE,gBAAkB,SAAUC,EAAQC,GAQ1C,OANA7S,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,MAAO+Q,EAAK,GACpD7S,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,MAAOD,EAAK,GACjD7S,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,SAAU8Q,EAAQ,GAC1D5S,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,SAAUF,EAAQ,GAGvC,EAATA,EAAaC,CACtB,EAYA3E,EAAQ6E,UAAY,SAAU9C,EAAQlO,EAAOhC,GAE3CC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GACrD/B,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMiT,EAAqB,EAARjR,EACbvC,EAAIyQ,EAAO+C,GACXvT,EAAIwQ,EAAO+C,EAAa,GACxBtT,EAAIuQ,EAAO+C,EAAa,GAK9B,OAHAjT,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACJK,CACT,EAaAmO,EAAQ+E,UAAY,SAAUhD,EAAQlO,EAAOlB,EAAWd,GAEtDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GACrD/B,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAI9B,MAAMiT,EAAqB,EAARjR,EAInB,OALAhC,EAASmO,EAAQtN,MAAMqP,EAAQlQ,IAExBiT,GAAcnS,EAAUrB,EAC/BO,EAAOiT,EAAa,GAAKnS,EAAUpB,EACnCM,EAAOiT,EAAa,GAAKnS,EAAUnB,EAC5BK,CACT,EAYAmO,EAAQgF,OAAS,SAAUjD,EAAQlO,EAAOhC,GAExCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GACrD/B,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMP,EAAIyQ,EAAOlO,GACXtC,EAAIwQ,EAAOlO,EAAQ,GACnBrC,EAAIuQ,EAAOlO,EAAQ,GAKzB,OAHAhC,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACJK,CACT,EAaAmO,EAAQiF,OAAS,SAAUlD,EAAQlO,EAAOlB,EAAWd,GAanD,OAXAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GACrD/B,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,IAG9BA,EAASmO,EAAQtN,MAAMqP,EAAQlQ,IACxBgC,GAASlB,EAAUrB,EAC1BO,EAAOgC,EAAQ,GAAKlB,EAAUpB,EAC9BM,EAAOgC,EAAQ,GAAKlB,EAAUnB,EACvBK,CACT,EAEA,MAAMqT,EAAgB,IAAI7T,EAS1B2O,EAAQmF,SAAW,SAAUpD,EAAQlQ,GAenC,OAbAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAID,EAAWe,UACpBf,EAAWoB,aAAasP,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAImD,IAE3DrT,EAAON,EAAIF,EAAWe,UACpBf,EAAWoB,aAAasP,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAImD,IAE3DrT,EAAOL,EAAIH,EAAWe,UACpBf,EAAWoB,aAAasP,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAImD,IAEpDrT,CACT,EAEA,MAAMuT,EAAe,IAAI/T,EASzB2O,EAAQqF,gBAAkB,SAAUtD,GAElC,OADA/B,EAAQmF,SAASpD,EAAQqD,GAClB/T,EAAW0C,iBAAiBqR,EACrC,EAUApF,EAAQsF,SAAW,SAAU3Q,EAAMC,EAAO/C,GAExCC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoO,EACJtL,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACtDwL,EACJzL,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACtD2L,EACJ5L,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAEtDsL,EACJvL,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACtDyL,EACJ1L,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACtD4L,EACJ7L,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAEtDuL,EACJxL,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACtD0L,EACJ3L,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACtD6L,EACJ9L,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAW5D,OATA/C,EAAO,GAAKoO,EACZpO,EAAO,GAAKuO,EACZvO,EAAO,GAAK0O,EACZ1O,EAAO,GAAKqO,EACZrO,EAAO,GAAKwO,EACZxO,EAAO,GAAK2O,EACZ3O,EAAO,GAAKsO,EACZtO,EAAO,GAAKyO,EACZzO,EAAO,GAAK4O,EACL5O,CACT,EAUAmO,EAAQ5K,IAAM,SAAUT,EAAMC,EAAO/C,GAgBnC,OAdAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GACrB/C,CACT,EAUAmO,EAAQnL,SAAW,SAAUF,EAAMC,EAAO/C,GAgBxC,OAdAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GACrB/C,CACT,EAUAmO,EAAQuF,iBAAmB,SAAUxD,EAAQpP,EAAWd,GAEtDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM2T,EAAK7S,EAAUrB,EACfmU,EAAK9S,EAAUpB,EACfmU,EAAK/S,EAAUnB,EAEfF,EAAIyQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAClDnU,EAAIwQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAClDlU,EAAIuQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAKxD,OAHA7T,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACJK,CACT,EAUAmO,EAAQ3K,iBAAmB,SAAU0M,EAAQzM,EAAQzD,GAgBnD,OAdAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACjBzD,CACT,EAkBAmO,EAAQ2F,gBAAkB,SAAU5D,EAAQ+B,EAAOjS,GAgBjD,OAdAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,QAAS8R,GAC7BhS,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMxS,EAC9BO,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMxS,EAC9BO,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMxS,EAC9BO,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMvS,EAC9BM,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMvS,EAC9BM,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMvS,EAC9BM,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMtS,EAC9BK,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMtS,EAC9BK,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMtS,EACvBK,CACT,EASAmO,EAAQxK,OAAS,SAAUuM,EAAQlQ,GAejC,OAbAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACblQ,CACT,EASAmO,EAAQ4F,UAAY,SAAU7D,EAAQlQ,GAEpCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoO,EAAc8B,EAAO,GACrB3B,EAAc2B,EAAO,GACrBxB,EAAcwB,EAAO,GACrB7B,EAAc6B,EAAO,GACrB1B,EAAc0B,EAAO,GACrBvB,EAAcuB,EAAO,GACrB5B,EAAc4B,EAAO,GACrBzB,EAAcyB,EAAO,GACrBtB,EAAcsB,EAAO,GAW3B,OATAlQ,EAAO,GAAKoO,EACZpO,EAAO,GAAKuO,EACZvO,EAAO,GAAK0O,EACZ1O,EAAO,GAAKqO,EACZrO,EAAO,GAAKwO,EACZxO,EAAO,GAAK2O,EACZ3O,EAAO,GAAKsO,EACZtO,EAAO,GAAKyO,EACZzO,EAAO,GAAK4O,EACL5O,CACT,EAEA,MAAMgU,EAAO,IAAIxU,EAAW,EAAG,EAAG,GASlC2O,EAAQ8F,YAAc,SAAU/D,EAAQlQ,GAEtCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMkU,EAAe1U,EAAW8D,iBAC9B0Q,EACA7F,EAAQmF,SAASpD,EAAQqD,GACzBA,GAIF,OAFAvT,EAASmO,EAAQ2F,gBAAgB5D,EAAQgE,EAAclU,EAGzD,EAYA,MAAMmU,EAAS,CAAC,EAAG,EAAG,GAChBC,EAAS,CAAC,EAAG,EAAG,GAEtB,SAASC,EAAyBnE,GAIhC,IAAIoE,EAAO,EACX,IAAK,IAAI1S,EAAI,EAAGA,EAAI,IAAKA,EAAG,CAC1B,MAAM2S,EAAOrE,EAAO/B,EAAQyE,gBAAgBwB,EAAOxS,GAAIuS,EAAOvS,KAC9D0S,GAAQ,EAAMC,EAAOA,CACtB,CAED,OAAO9T,KAAKkC,KAAK2R,EACnB,CAEA,SAASE,EAAkBtE,EAAQlQ,GAQjC,MAAMyU,EAAYjP,EAAUA,WAACiI,UAE7B,IAAIiH,EAAc,EACdC,EAAU,EAGd,IAAK,IAAI/S,EAAI,EAAGA,EAAI,IAAKA,EAAG,CAC1B,MAAM2S,EAAO9T,KAAKmD,IAChBsM,EAAO/B,EAAQyE,gBAAgBwB,EAAOxS,GAAIuS,EAAOvS,MAE/C2S,EAAOG,IACTC,EAAU/S,EACV8S,EAAcH,EAEjB,CAED,IAAIK,EAAI,EACJC,EAAI,EAER,MAAM3J,EAAIiJ,EAAOQ,GACXG,EAAIV,EAAOO,GAEjB,GAAIlU,KAAKmD,IAAIsM,EAAO/B,EAAQyE,gBAAgBkC,EAAG5J,KAAOuJ,EAAW,CAC/D,MAIMM,GAJK7E,EAAO/B,EAAQyE,gBAAgBkC,EAAGA,IAClC5E,EAAO/B,EAAQyE,gBAAgB1H,EAAGA,KAGrB,EAFbgF,EAAO/B,EAAQyE,gBAAgBkC,EAAG5J,IAG7C,IAAIjH,EAGFA,EADE8Q,EAAM,GACH,IAAQA,EAAMtU,KAAKkC,KAAK,EAAMoS,EAAMA,IAErC,GAAOA,EAAMtU,KAAKkC,KAAK,EAAMoS,EAAMA,IAGzCH,EAAI,EAAMnU,KAAKkC,KAAK,EAAMsB,EAAIA,GAC9B4Q,EAAI5Q,EAAI2Q,CACT,CAUD,OARA5U,EAASmO,EAAQtN,MAAMsN,EAAQ6G,SAAUhV,IAElCmO,EAAQyE,gBAAgB1H,EAAGA,IAAMlL,EACtCmO,EAAQyE,gBAAgBkC,EAAGA,IACzBF,EACJ5U,EAAOmO,EAAQyE,gBAAgBkC,EAAG5J,IAAM2J,EACxC7U,EAAOmO,EAAQyE,gBAAgB1H,EAAG4J,KAAOD,EAElC7U,CACT,CAEA,MAAMiV,EAAU,IAAI9G,EACd+G,EAAmB,IAAI/G,EAiC7BA,EAAQgH,0BAA4B,SAAUjF,EAAQlQ,GAEpDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAM9B,MAAMuE,EAAYjP,EAAUA,WAAC4P,UAG7B,IAAIC,EAAQ,EACRC,EAAQ,EAEPlV,EAAAA,QAAQJ,KACXA,EAAS,CAAA,GAGX,MAAMuV,EAAiBvV,EAAOwV,QAAUrH,EAAQtN,MAC9CsN,EAAQ6G,SACRhV,EAAOwV,SAEHC,EAAczV,EAAO0V,SAAWvH,EAAQtN,MAAMqP,EAAQlQ,EAAO0V,UAE7DjK,EAAUgJ,EA/IlB,SAA8BvE,GAC5B,IAAIoE,EAAO,EACX,IAAK,IAAI1S,EAAI,EAAGA,EAAI,IAAKA,EAAG,CAC1B,MAAM2S,EAAOrE,EAAOtO,GACpB0S,GAAQC,EAAOA,CAChB,CAED,OAAO9T,KAAKkC,KAAK2R,EACnB,CAuI8BqB,CAAqBF,GAEjD,KAAOH,EAjBW,IAiBUjB,EAAyBoB,GAAchK,GACjE+I,EAAkBiB,EAAYR,GAC9B9G,EAAQ4F,UAAUkB,EAASC,GAC3B/G,EAAQsF,SAASgC,EAAYR,EAASQ,GACtCtH,EAAQsF,SAASyB,EAAkBO,EAAYA,GAC/CtH,EAAQsF,SAAS8B,EAAeN,EAASM,KAEnCF,EAAQ,MACVC,EACFD,EAAQ,GAIZ,OAAOrV,CACT,EASAmO,EAAQvK,IAAM,SAAUsM,EAAQlQ,GAgB9B,OAdAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAErBlQ,CACT,EAQAmO,EAAQyH,YAAc,SAAU1F,GAE9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAG9B,MAAMkB,EAAMlB,EAAO,GACbqB,EAAMrB,EAAO,GACb2F,EAAM3F,EAAO,GACbmB,EAAMnB,EAAO,GACbsB,EAAMtB,EAAO,GACb4F,EAAM5F,EAAO,GACb6F,EAAM7F,EAAO,GACb8F,EAAM9F,EAAO,GACb+F,EAAM/F,EAAO,GAEnB,OACEkB,GAAOI,EAAMyE,EAAMD,EAAMF,GACzBzE,GAAO2E,EAAMH,EAAMtE,EAAM0E,GACzBF,GAAOxE,EAAMuE,EAAMtE,EAAMqE,EAE7B,EAWA1H,EAAQ+H,QAAU,SAAUhG,EAAQlQ,GAElCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoR,EAAMlB,EAAO,GACbqB,EAAMrB,EAAO,GACb2F,EAAM3F,EAAO,GACbmB,EAAMnB,EAAO,GACbsB,EAAMtB,EAAO,GACb4F,EAAM5F,EAAO,GACb6F,EAAM7F,EAAO,GACb8F,EAAM9F,EAAO,GACb+F,EAAM/F,EAAO,GAEb0F,EAAczH,EAAQyH,YAAY1F,GAGxC,GAAIzP,KAAKmD,IAAIgS,IAAgBpQ,EAAAA,WAAWiI,UACtC,MAAM,IAAI9L,EAAAA,eAAe,4BAI3B3B,EAAO,GAAKwR,EAAMyE,EAAMD,EAAMF,EAC9B9V,EAAO,GAAKgW,EAAMH,EAAMtE,EAAM0E,EAC9BjW,EAAO,GAAKuR,EAAMuE,EAAMtE,EAAMqE,EAC9B7V,EAAO,GAAK+V,EAAMD,EAAMzE,EAAM4E,EAC9BjW,EAAO,GAAKoR,EAAM6E,EAAMF,EAAMF,EAC9B7V,EAAO,GAAKqR,EAAMwE,EAAMzE,EAAM0E,EAC9B9V,EAAO,GAAKqR,EAAM2E,EAAMD,EAAMvE,EAC9BxR,EAAO,GAAK+V,EAAMxE,EAAMH,EAAM4E,EAC9BhW,EAAO,GAAKoR,EAAMI,EAAMH,EAAME,EAE9B,MAAMU,EAAQ,EAAM2D,EACpB,OAAOzH,EAAQ3K,iBAAiBxD,EAAQiS,EAAOjS,EACjD,EAEA,MAAMmW,EAAyB,IAAIhI,ECn0CnC,SAASiI,EAAW3W,EAAGC,EAAGC,EAAGgR,GAM3B/Q,KAAKH,EAAII,EAAAA,aAAaJ,EAAG,GAOzBG,KAAKF,EAAIG,EAAAA,aAAaH,EAAG,GAOzBE,KAAKD,EAAIE,EAAAA,aAAaF,EAAG,GAOzBC,KAAK+Q,EAAI9Q,EAAAA,aAAa8Q,EAAG,EAC3B,CDgzCAxC,EAAQkI,iBAAmB,SAAUnG,EAAQlQ,GAM3C,OAJAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAGvBmO,EAAQ+H,QACb/H,EAAQ4F,UAAU7D,EAAQiG,GAC1BnW,EAEJ,EAUAmO,EAAQjJ,OAAS,SAAUpC,EAAMC,GAC/B,OACED,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,EAExB,EAYAoL,EAAQ9I,cAAgB,SAAUvC,EAAMC,EAAO0I,GAG7C,OAFAA,EAAU5L,EAAYA,aAAC4L,EAAS,GAG9B3I,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRtC,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,CAEtC,EAQA0C,EAAQ6G,SAAW7N,OAAOC,OACxB,IAAI+G,EAAQ,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,IAStDA,EAAQjH,KAAOC,OAAOC,OACpB,IAAI+G,EAAQ,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,IAStDA,EAAQmI,YAAc,EAQtBnI,EAAQoI,YAAc,EAQtBpI,EAAQqI,YAAc,EAQtBrI,EAAQsI,YAAc,EAQtBtI,EAAQuI,YAAc,EAQtBvI,EAAQwI,YAAc,EAQtBxI,EAAQyI,YAAc,EAQtBzI,EAAQ0I,YAAc,EAQtB1I,EAAQ2I,YAAc,EAEtB3P,OAAOuE,iBAAiByC,EAAQ7G,UAAW,CAOzC/F,OAAQ,CACNqK,IAAK,WACH,OAAOuC,EAAQnN,YAChB,KAULmN,EAAQ7G,UAAUzG,MAAQ,SAAUb,GAClC,OAAOmO,EAAQtN,MAAMjB,KAAMI,EAC7B,EASAmO,EAAQ7G,UAAUpC,OAAS,SAAUnC,GACnC,OAAOoL,EAAQjJ,OAAOtF,KAAMmD,EAC9B,EAKAoL,EAAQhJ,YAAc,SAAU+K,EAAQ/O,EAAOiE,GAC7C,OACE8K,EAAO,KAAO/O,EAAMiE,IACpB8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,EAEjC,EAWA+I,EAAQ7G,UAAUjC,cAAgB,SAAUtC,EAAO0I,GACjD,OAAO0C,EAAQ9I,cAAczF,KAAMmD,EAAO0I,EAC5C,EAQA0C,EAAQ7G,UAAUC,SAAW,WAC3B,MACE,IACA3H,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,GALL,OAQAA,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,GAZL,OAeAA,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,GACL,GAEJ,ECjiDAwW,EAAWxV,aAAe,SAAUnB,EAAGC,EAAGC,EAAGgR,EAAG3Q,GAC9C,OAAKI,EAAAA,QAAQJ,IAIbA,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACXK,EAAO2Q,EAAIA,EACJ3Q,GAPE,IAAIoW,EAAW3W,EAAGC,EAAGC,EAAGgR,EAQnC,EAUAyF,EAAWW,UAAY,SAAUC,EAAOhX,GAItC,OAFAC,EAAAA,MAAMC,OAAOC,OAAO,QAAS6W,GAExB5W,EAAAA,QAAQJ,IAIbA,EAAOP,EAAIuX,EAAMC,IACjBjX,EAAON,EAAIsX,EAAME,MACjBlX,EAAOL,EAAIqX,EAAMG,KACjBnX,EAAO2Q,EAAIqG,EAAMI,MACVpX,GAPE,IAAIoW,EAAWY,EAAMC,IAAKD,EAAME,MAAOF,EAAMG,KAAMH,EAAMI,MAQpE,EASAhB,EAAWvV,MAAQ,SAAUC,EAAWd,GACtC,GAAKI,EAAAA,QAAQU,GAIb,OAAKV,EAAAA,QAAQJ,IAIbA,EAAOP,EAAIqB,EAAUrB,EACrBO,EAAON,EAAIoB,EAAUpB,EACrBM,EAAOL,EAAImB,EAAUnB,EACrBK,EAAO2Q,EAAI7P,EAAU6P,EACd3Q,GAPE,IAAIoW,EAAWtV,EAAUrB,EAAGqB,EAAUpB,EAAGoB,EAAUnB,EAAGmB,EAAU6P,EAQ3E,EAMAyF,EAAWpV,aAAe,EAW1BoV,EAAWnV,KAAO,SAAUC,EAAOC,EAAOC,GAaxC,OAXAnB,EAAAA,MAAMC,OAAOC,OAAO,QAASe,GAC7BjB,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5CD,EAAMC,KAAmBF,EAAMzB,EAC/B0B,EAAMC,KAAmBF,EAAMxB,EAC/ByB,EAAMC,KAAmBF,EAAMvB,EAC/BwB,EAAMC,GAAiBF,EAAMyP,EAEtBxP,CACT,EAUAiV,EAAW/U,OAAS,SAAUF,EAAOC,EAAepB,GAclD,OAZAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAIoW,GAEfpW,EAAOP,EAAI0B,EAAMC,KACjBpB,EAAON,EAAIyB,EAAMC,KACjBpB,EAAOL,EAAIwB,EAAMC,KACjBpB,EAAO2Q,EAAIxP,EAAMC,GACVpB,CACT,EAUAoW,EAAW9U,UAAY,SAAUH,EAAOnB,GAEtCC,EAAAA,MAAMG,QAAQ,QAASe,GAGvB,MAAMI,EAASJ,EAAMI,OACfC,EAAwB,EAATD,EACrB,GAAKnB,EAAAA,QAAQJ,GAEN,KAAKyB,MAAMC,QAAQ1B,IAAWA,EAAOuB,SAAWC,EACrD,MAAM,IAAIG,EAAcA,eACtB,8EAEO3B,EAAOuB,SAAWC,IAC3BxB,EAAOuB,OAASC,EACjB,MAPCxB,EAAS,IAAIyB,MAAMD,GASrB,IAAK,IAAII,EAAI,EAAGA,EAAIL,IAAUK,EAC5BwU,EAAWnV,KAAKE,EAAMS,GAAI5B,EAAY,EAAJ4B,GAEpC,OAAO5B,CACT,EASAoW,EAAWvU,YAAc,SAAUV,EAAOnB,GAIxC,GAFAC,EAAAA,MAAMG,QAAQ,QAASe,GACvBlB,QAAMC,OAAO4B,OAAOC,oBAAoB,eAAgBZ,EAAMI,OAAQ,GAClEJ,EAAMI,OAAS,GAAM,EACvB,MAAM,IAAII,EAAAA,eAAe,yCAI3B,MAAMJ,EAASJ,EAAMI,OAChBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAAS,EAFzBvB,EAAS,IAAIyB,MAAMF,EAAS,GAK9B,IAAK,IAAIK,EAAI,EAAGA,EAAIL,EAAQK,GAAK,EAAG,CAClC,MAAMI,EAAQJ,EAAI,EAClB5B,EAAOgC,GAASoU,EAAW/U,OAAOF,EAAOS,EAAG5B,EAAOgC,GACpD,CACD,OAAOhC,CACT,EAoBAoW,EAAWnU,UAAYmU,EAAW/U,OAQlC+U,EAAWlU,iBAAmB,SAAUpB,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAG1BL,KAAK0B,IAAIrB,EAAUrB,EAAGqB,EAAUpB,EAAGoB,EAAUnB,EAAGmB,EAAU6P,EACnE,EAQAyF,EAAWhU,iBAAmB,SAAUtB,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAG1BL,KAAK4B,IAAIvB,EAAUrB,EAAGqB,EAAUpB,EAAGoB,EAAUnB,EAAGmB,EAAU6P,EACnE,EAUAyF,EAAW9T,mBAAqB,SAAUC,EAAOC,EAAQxC,GAYvD,OAVAC,EAAAA,MAAMC,OAAOC,OAAO,QAASoC,GAC7BtC,EAAAA,MAAMC,OAAOC,OAAO,SAAUqC,GAC9BvC,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAK4B,IAAIE,EAAM9C,EAAG+C,EAAO/C,GACpCO,EAAON,EAAIe,KAAK4B,IAAIE,EAAM7C,EAAG8C,EAAO9C,GACpCM,EAAOL,EAAIc,KAAK4B,IAAIE,EAAM5C,EAAG6C,EAAO7C,GACpCK,EAAO2Q,EAAIlQ,KAAK4B,IAAIE,EAAMoO,EAAGnO,EAAOmO,GAE7B3Q,CACT,EAUAoW,EAAW3T,mBAAqB,SAAUF,EAAOC,EAAQxC,GAYvD,OAVAC,EAAAA,MAAMC,OAAOC,OAAO,QAASoC,GAC7BtC,EAAAA,MAAMC,OAAOC,OAAO,SAAUqC,GAC9BvC,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAK0B,IAAII,EAAM9C,EAAG+C,EAAO/C,GACpCO,EAAON,EAAIe,KAAK0B,IAAII,EAAM7C,EAAG8C,EAAO9C,GACpCM,EAAOL,EAAIc,KAAK0B,IAAII,EAAM5C,EAAG6C,EAAO7C,GACpCK,EAAO2Q,EAAIlQ,KAAK0B,IAAII,EAAMoO,EAAGnO,EAAOmO,GAE7B3Q,CACT,EAQAoW,EAAW1T,iBAAmB,SAAU5B,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAI/BA,EAAUrB,EAAIqB,EAAUrB,EACxBqB,EAAUpB,EAAIoB,EAAUpB,EACxBoB,EAAUnB,EAAImB,EAAUnB,EACxBmB,EAAU6P,EAAI7P,EAAU6P,CAE5B,EAQAyF,EAAW7V,UAAY,SAAUO,GAC/B,OAAOL,KAAKkC,KAAKyT,EAAW1T,iBAAiB5B,GAC/C,EAEA,MAAM8B,EAAkB,IAAIwT,EAe5BA,EAAWvT,SAAW,SAAUC,EAAMC,GAOpC,OALA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAG7BqT,EAAWpT,SAASF,EAAMC,EAAOH,GAC1BwT,EAAW7V,UAAUqC,EAC9B,EAgBAwT,EAAWnT,gBAAkB,SAAUH,EAAMC,GAO3C,OALA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAG7BqT,EAAWpT,SAASF,EAAMC,EAAOH,GAC1BwT,EAAW1T,iBAAiBE,EACrC,EASAwT,EAAWlT,UAAY,SAAUpC,EAAWd,GAE1CC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMO,EAAY6V,EAAW7V,UAAUO,GAQvC,GANAd,EAAOP,EAAIqB,EAAUrB,EAAIc,EACzBP,EAAON,EAAIoB,EAAUpB,EAAIa,EACzBP,EAAOL,EAAImB,EAAUnB,EAAIY,EACzBP,EAAO2Q,EAAI7P,EAAU6P,EAAIpQ,EAIvB4C,MAAMnD,EAAOP,IACb0D,MAAMnD,EAAON,IACbyD,MAAMnD,EAAOL,IACbwD,MAAMnD,EAAO2Q,GAEb,MAAM,IAAIhP,EAAAA,eAAe,qCAI3B,OAAO3B,CACT,EASAoW,EAAWhT,IAAM,SAAUN,EAAMC,GAM/B,OAJA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAI3BD,EAAKrD,EAAIsD,EAAMtD,EAAIqD,EAAKpD,EAAIqD,EAAMrD,EAAIoD,EAAKnD,EAAIoD,EAAMpD,EAAImD,EAAK6N,EAAI5N,EAAM4N,CAE5E,EAUAyF,EAAW/S,mBAAqB,SAAUP,EAAMC,EAAO/C,GAWrD,OATAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EAC1BM,EAAOL,EAAImD,EAAKnD,EAAIoD,EAAMpD,EAC1BK,EAAO2Q,EAAI7N,EAAK6N,EAAI5N,EAAM4N,EACnB3Q,CACT,EAUAoW,EAAW9S,iBAAmB,SAAUR,EAAMC,EAAO/C,GAWnD,OATAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EAC1BM,EAAOL,EAAImD,EAAKnD,EAAIoD,EAAMpD,EAC1BK,EAAO2Q,EAAI7N,EAAK6N,EAAI5N,EAAM4N,EACnB3Q,CACT,EAUAoW,EAAW7S,IAAM,SAAUT,EAAMC,EAAO/C,GAWtC,OATAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EAC1BM,EAAOL,EAAImD,EAAKnD,EAAIoD,EAAMpD,EAC1BK,EAAO2Q,EAAI7N,EAAK6N,EAAI5N,EAAM4N,EACnB3Q,CACT,EAUAoW,EAAWpT,SAAW,SAAUF,EAAMC,EAAO/C,GAW3C,OATAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EAC1BM,EAAOL,EAAImD,EAAKnD,EAAIoD,EAAMpD,EAC1BK,EAAO2Q,EAAI7N,EAAK6N,EAAI5N,EAAM4N,EACnB3Q,CACT,EAUAoW,EAAW5S,iBAAmB,SAAU1C,EAAW2C,EAAQzD,GAWzD,OATAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqB,EAAUrB,EAAIgE,EACzBzD,EAAON,EAAIoB,EAAUpB,EAAI+D,EACzBzD,EAAOL,EAAImB,EAAUnB,EAAI8D,EACzBzD,EAAO2Q,EAAI7P,EAAU6P,EAAIlN,EAClBzD,CACT,EAUAoW,EAAW1S,eAAiB,SAAU5C,EAAW2C,EAAQzD,GAWvD,OATAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqB,EAAUrB,EAAIgE,EACzBzD,EAAON,EAAIoB,EAAUpB,EAAI+D,EACzBzD,EAAOL,EAAImB,EAAUnB,EAAI8D,EACzBzD,EAAO2Q,EAAI7P,EAAU6P,EAAIlN,EAClBzD,CACT,EASAoW,EAAWzS,OAAS,SAAU7C,EAAWd,GAUvC,OARAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,GAAKqB,EAAUrB,EACtBO,EAAON,GAAKoB,EAAUpB,EACtBM,EAAOL,GAAKmB,EAAUnB,EACtBK,EAAO2Q,GAAK7P,EAAU6P,EACf3Q,CACT,EASAoW,EAAWxS,IAAM,SAAU9C,EAAWd,GAUpC,OARAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAKmD,IAAI9C,EAAUrB,GAC9BO,EAAON,EAAIe,KAAKmD,IAAI9C,EAAUpB,GAC9BM,EAAOL,EAAIc,KAAKmD,IAAI9C,EAAUnB,GAC9BK,EAAO2Q,EAAIlQ,KAAKmD,IAAI9C,EAAU6P,GACvB3Q,CACT,EAEA,MAAM6D,EAAc,IAAIuS,EAUxBA,EAAWtS,KAAO,SAAUC,EAAOC,EAAKC,EAAGjE,GAUzC,OARAC,EAAAA,MAAMC,OAAOC,OAAO,QAAS4D,GAC7B9D,EAAAA,MAAMC,OAAOC,OAAO,MAAO6D,GAC3B/D,EAAAA,MAAMC,OAAO4B,OAAO,IAAKmC,GACzBhE,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BoW,EAAW5S,iBAAiBQ,EAAKC,EAAGJ,GACpC7D,EAASoW,EAAW5S,iBAAiBO,EAAO,EAAME,EAAGjE,GAC9CoW,EAAW7S,IAAIM,EAAa7D,EAAQA,EAC7C,EAEA,MAAMyE,EAA4B,IAAI2R,EAQtCA,EAAW1R,mBAAqB,SAAU5D,EAAWd,GAEnDC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM2E,EAAIyR,EAAWlT,UAAUpC,EAAW2D,GA2B1C,OA1BA2R,EAAWxS,IAAIe,EAAGA,GAKZ3E,EAHF2E,EAAElF,GAAKkF,EAAEjF,EACPiF,EAAElF,GAAKkF,EAAEhF,EACPgF,EAAElF,GAAKkF,EAAEgM,EACFyF,EAAWvV,MAAMuV,EAAWxR,OAAQ5E,GAEpCoW,EAAWvV,MAAMuV,EAAWiB,OAAQrX,GAEtC2E,EAAEhF,GAAKgF,EAAEgM,EACTyF,EAAWvV,MAAMuV,EAAWvR,OAAQ7E,GAEpCoW,EAAWvV,MAAMuV,EAAWiB,OAAQrX,GAEtC2E,EAAEjF,GAAKiF,EAAEhF,EACdgF,EAAEjF,GAAKiF,EAAEgM,EACFyF,EAAWvV,MAAMuV,EAAWtR,OAAQ9E,GAEpCoW,EAAWvV,MAAMuV,EAAWiB,OAAQrX,GAEtC2E,EAAEhF,GAAKgF,EAAEgM,EACTyF,EAAWvV,MAAMuV,EAAWvR,OAAQ7E,GAEpCoW,EAAWvV,MAAMuV,EAAWiB,OAAQrX,EAIjD,EAUAoW,EAAWlR,OAAS,SAAUpC,EAAMC,GAClC,OACED,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRD,EAAKrD,IAAMsD,EAAMtD,GACjBqD,EAAKpD,IAAMqD,EAAMrD,GACjBoD,EAAKnD,IAAMoD,EAAMpD,GACjBmD,EAAK6N,IAAM5N,EAAM4N,CAEvB,EAKAyF,EAAWjR,YAAc,SAAUrE,EAAWK,EAAOiE,GACnD,OACEtE,EAAUrB,IAAM0B,EAAMiE,IACtBtE,EAAUpB,IAAMyB,EAAMiE,EAAS,IAC/BtE,EAAUnB,IAAMwB,EAAMiE,EAAS,IAC/BtE,EAAU6P,IAAMxP,EAAMiE,EAAS,EAEnC,EAaAgR,EAAW/Q,cAAgB,SACzBvC,EACAC,EACAuC,EACAC,GAEA,OACEzC,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRyC,EAAAA,WAAWH,cACTvC,EAAKrD,EACLsD,EAAMtD,EACN6F,EACAC,IAEFC,EAAAA,WAAWH,cACTvC,EAAKpD,EACLqD,EAAMrD,EACN4F,EACAC,IAEFC,EAAAA,WAAWH,cACTvC,EAAKnD,EACLoD,EAAMpD,EACN2F,EACAC,IAEFC,EAAAA,WAAWH,cACTvC,EAAK6N,EACL5N,EAAM4N,EACNrL,EACAC,EAGR,EAQA6Q,EAAWlP,KAAOC,OAAOC,OAAO,IAAIgP,EAAW,EAAK,EAAK,EAAK,IAQ9DA,EAAW/O,IAAMF,OAAOC,OAAO,IAAIgP,EAAW,EAAK,EAAK,EAAK,IAQ7DA,EAAWxR,OAASuC,OAAOC,OAAO,IAAIgP,EAAW,EAAK,EAAK,EAAK,IAQhEA,EAAWtR,OAASqC,OAAOC,OAAO,IAAIgP,EAAW,EAAK,EAAK,EAAK,IAQhEA,EAAWvR,OAASsC,OAAOC,OAAO,IAAIgP,EAAW,EAAK,EAAK,EAAK,IAQhEA,EAAWiB,OAASlQ,OAAOC,OAAO,IAAIgP,EAAW,EAAK,EAAK,EAAK,IAQhEA,EAAW9O,UAAUzG,MAAQ,SAAUb,GACrC,OAAOoW,EAAWvV,MAAMjB,KAAMI,EAChC,EASAoW,EAAW9O,UAAUpC,OAAS,SAAUnC,GACtC,OAAOqT,EAAWlR,OAAOtF,KAAMmD,EACjC,EAYAqT,EAAW9O,UAAUjC,cAAgB,SACnCtC,EACAuC,EACAC,GAEA,OAAO6Q,EAAW/Q,cAChBzF,KACAmD,EACAuC,EACAC,EAEJ,EAOA6Q,EAAW9O,UAAUC,SAAW,WAC9B,MAAO,IAAM3H,KAAKH,EAAI,KAAOG,KAAKF,EAAI,KAAOE,KAAKD,EAAI,KAAOC,KAAK+Q,EAAI,GACxE,EAGA,MAAM2G,EAAkB,IAAIC,aAAa,GACnCC,EAAiB,IAAIC,WAAWH,EAAgB9J,QAEhDkK,EAAU,IAAIC,YAAY,CAAC,YAE3BC,EAA6B,KADpB,IAAIH,WAAWC,EAAQlK,QACV,GCj0B5B,SAASqK,EACPzJ,EACAC,EACAC,EACAwJ,EACAvJ,EACAC,EACAC,EACAsJ,EACArJ,EACAC,EACAC,EACAoJ,EACAC,EACAC,EACAC,EACAC,GAEAxY,KAAK,GAAKC,EAAYA,aAACuO,EAAa,GACpCxO,KAAK,GAAKC,EAAYA,aAAC0O,EAAa,GACpC3O,KAAK,GAAKC,EAAYA,aAAC6O,EAAa,GACpC9O,KAAK,GAAKC,EAAYA,aAACoY,EAAa,GACpCrY,KAAK,GAAKC,EAAYA,aAACwO,EAAa,GACpCzO,KAAK,GAAKC,EAAYA,aAAC2O,EAAa,GACpC5O,KAAK,GAAKC,EAAYA,aAAC8O,EAAa,GACpC/O,KAAK,GAAKC,EAAYA,aAACqY,EAAa,GACpCtY,KAAK,GAAKC,EAAYA,aAACyO,EAAa,GACpC1O,KAAK,GAAKC,EAAYA,aAAC4O,EAAa,GACpC7O,KAAK,IAAMC,EAAYA,aAAC+O,EAAa,GACrChP,KAAK,IAAMC,EAAYA,aAACsY,EAAa,GACrCvY,KAAK,IAAMC,EAAYA,aAACiY,EAAa,GACrClY,KAAK,IAAMC,EAAYA,aAACkY,EAAa,GACrCnY,KAAK,IAAMC,EAAYA,aAACmY,EAAa,GACrCpY,KAAK,IAAMC,EAAYA,aAACuY,EAAa,EACvC,CDwyBAhC,EAAWiC,UAAY,SAAUnX,EAAOlB,GAwBtC,OAtBAC,EAAAA,MAAMC,OAAO4B,OAAO,QAASZ,GAGxBd,EAAAA,QAAQJ,KACXA,EAAS,IAAIoW,GAIfkB,EAAgB,GAAKpW,EAEjB0W,GACF5X,EAAOP,EAAI+X,EAAe,GAC1BxX,EAAON,EAAI8X,EAAe,GAC1BxX,EAAOL,EAAI6X,EAAe,GAC1BxX,EAAO2Q,EAAI6G,EAAe,KAG1BxX,EAAOP,EAAI+X,EAAe,GAC1BxX,EAAON,EAAI8X,EAAe,GAC1BxX,EAAOL,EAAI6X,EAAe,GAC1BxX,EAAO2Q,EAAI6G,EAAe,IAErBxX,CACT,EASAoW,EAAWkC,YAAc,SAAUC,GAkBjC,OAhBAtY,EAAAA,MAAMC,OAAOC,OAAO,cAAeoY,GAI/BX,GACFJ,EAAe,GAAKe,EAAY9Y,EAChC+X,EAAe,GAAKe,EAAY7Y,EAChC8X,EAAe,GAAKe,EAAY5Y,EAChC6X,EAAe,GAAKe,EAAY5H,IAGhC6G,EAAe,GAAKe,EAAY5H,EAChC6G,EAAe,GAAKe,EAAY5Y,EAChC6X,EAAe,GAAKe,EAAY7Y,EAChC8X,EAAe,GAAKe,EAAY9Y,GAE3B6X,EAAgB,EACzB,ECv1BAO,EAAQ7W,aAAe,GAWvB6W,EAAQ5W,KAAO,SAAUC,EAAOC,EAAOC,GAyBrC,OAvBAnB,EAAAA,MAAMC,OAAOC,OAAO,QAASe,GAC7BjB,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5CD,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,IAC/BC,EAAMC,KAAmBF,EAAM,IAC/BC,EAAMC,KAAmBF,EAAM,IAC/BC,EAAMC,KAAmBF,EAAM,IAC/BC,EAAMC,KAAmBF,EAAM,IAC/BC,EAAMC,GAAiBF,EAAM,IAEtBC,CACT,EAUA0W,EAAQxW,OAAS,SAAUF,EAAOC,EAAepB,GA2B/C,OAzBAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAI6X,GAGf7X,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,IAAMmB,EAAMC,KACnBpB,EAAO,IAAMmB,EAAMC,KACnBpB,EAAO,IAAMmB,EAAMC,KACnBpB,EAAO,IAAMmB,EAAMC,KACnBpB,EAAO,IAAMmB,EAAMC,KACnBpB,EAAO,IAAMmB,EAAMC,GACZpB,CACT,EASA6X,EAAQhX,MAAQ,SAAUqP,EAAQlQ,GAChC,GAAKI,EAAAA,QAAQ8P,GAGb,OAAK9P,EAAAA,QAAQJ,IAoBbA,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACblQ,GAnCE,IAAI6X,EACT3H,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,IACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,IACPA,EAAO,GACPA,EAAO,GACPA,EAAO,IACPA,EAAO,IACPA,EAAO,GACPA,EAAO,GACPA,EAAO,IACPA,EAAO,IAoBb,EAyBA2H,EAAQ5V,UAAY4V,EAAQxW,OAS5BwW,EAAQ1H,qBAAuB,SAAUC,EAAQpQ,GAK/C,OAHAC,EAAAA,MAAMG,QAAQ,SAAUgQ,GAGjByH,EAAQhX,MAAMuP,EAAQpQ,EAC/B,EAUA6X,EAAQxH,kBAAoB,SAAUD,EAAQpQ,GAK5C,OAHAC,EAAAA,MAAMG,QAAQ,SAAUgQ,GAGnBhQ,EAAAA,QAAQJ,IAoBbA,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,IACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,IACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,IAAMoQ,EAAO,IACpBpQ,EAAO,IAAMoQ,EAAO,IACpBpQ,EAAO,IAAMoQ,EAAO,GACpBpQ,EAAO,IAAMoQ,EAAO,GACpBpQ,EAAO,IAAMoQ,EAAO,IACpBpQ,EAAO,IAAMoQ,EAAO,IACbpQ,GAnCE,IAAI6X,EACTzH,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,IACPA,EAAO,IACPA,EAAO,IACPA,EAAO,IACPA,EAAO,IACPA,EAAO,IAoBb,EAWAyH,EAAQW,wBAA0B,SAAUC,EAAUC,EAAa1Y,GAOjE,OALAC,EAAAA,MAAMC,OAAOC,OAAO,WAAYsY,GAGhCC,EAAc7Y,EAAAA,aAAa6Y,EAAalZ,EAAW0H,MAE9C9G,EAAAA,QAAQJ,IAqBbA,EAAO,GAAKyY,EAAS,GACrBzY,EAAO,GAAKyY,EAAS,GACrBzY,EAAO,GAAKyY,EAAS,GACrBzY,EAAO,GAAK,EACZA,EAAO,GAAKyY,EAAS,GACrBzY,EAAO,GAAKyY,EAAS,GACrBzY,EAAO,GAAKyY,EAAS,GACrBzY,EAAO,GAAK,EACZA,EAAO,GAAKyY,EAAS,GACrBzY,EAAO,GAAKyY,EAAS,GACrBzY,EAAO,IAAMyY,EAAS,GACtBzY,EAAO,IAAM,EACbA,EAAO,IAAM0Y,EAAYjZ,EACzBO,EAAO,IAAM0Y,EAAYhZ,EACzBM,EAAO,IAAM0Y,EAAY/Y,EACzBK,EAAO,IAAM,EACNA,GApCE,IAAI6X,EACTY,EAAS,GACTA,EAAS,GACTA,EAAS,GACTC,EAAYjZ,EACZgZ,EAAS,GACTA,EAAS,GACTA,EAAS,GACTC,EAAYhZ,EACZ+Y,EAAS,GACTA,EAAS,GACTA,EAAS,GACTC,EAAY/Y,EACZ,EACA,EACA,EACA,EAqBN,EAmBAkY,EAAQc,uCAAyC,SAC/CD,EACAD,EACAxG,EACAjS,GAGAC,EAAAA,MAAMC,OAAOC,OAAO,cAAeuY,GACnCzY,EAAAA,MAAMC,OAAOC,OAAO,WAAYsY,GAChCxY,EAAAA,MAAMC,OAAOC,OAAO,QAAS8R,GAGxB7R,EAAAA,QAAQJ,KACXA,EAAS,IAAI6X,GAGf,MAAMe,EAAS3G,EAAMxS,EACfoZ,EAAS5G,EAAMvS,EACfoZ,EAAS7G,EAAMtS,EAEfyI,EAAKqQ,EAAShZ,EAAIgZ,EAAShZ,EAC3B+Q,EAAKiI,EAAShZ,EAAIgZ,EAAS/Y,EAC3B+Q,EAAKgI,EAAShZ,EAAIgZ,EAAS9Y,EAC3B+Q,EAAK+H,EAAShZ,EAAIgZ,EAAS9H,EAC3BtI,EAAKoQ,EAAS/Y,EAAI+Y,EAAS/Y,EAC3BkR,EAAK6H,EAAS/Y,EAAI+Y,EAAS9Y,EAC3BkR,EAAK4H,EAAS/Y,EAAI+Y,EAAS9H,EAC3BrI,EAAKmQ,EAAS9Y,EAAI8Y,EAAS9Y,EAC3BmR,EAAK2H,EAAS9Y,EAAI8Y,EAAS9H,EAC3BI,EAAK0H,EAAS9H,EAAI8H,EAAS9H,EAE3BK,EAAM5I,EAAKC,EAAKC,EAAKyI,EACrBE,EAAM,GAAOT,EAAKM,GAClBI,EAAM,GAAOT,EAAKI,GAElBM,EAAM,GAAOX,EAAKM,GAClBM,GAAOhJ,EAAKC,EAAKC,EAAKyI,EACtBM,EAAM,GAAOT,EAAKF,GAElBY,EAAM,GAAOb,EAAKI,GAClBU,EAAM,GAAOX,EAAKF,GAClBc,GAAOpJ,EAAKC,EAAKC,EAAKyI,EAmB5B,OAjBA/Q,EAAO,GAAKgR,EAAM4H,EAClB5Y,EAAO,GAAKmR,EAAMyH,EAClB5Y,EAAO,GAAKsR,EAAMsH,EAClB5Y,EAAO,GAAK,EACZA,EAAO,GAAKiR,EAAM4H,EAClB7Y,EAAO,GAAKoR,EAAMyH,EAClB7Y,EAAO,GAAKuR,EAAMsH,EAClB7Y,EAAO,GAAK,EACZA,EAAO,GAAKkR,EAAM4H,EAClB9Y,EAAO,GAAKqR,EAAMyH,EAClB9Y,EAAO,IAAMwR,EAAMsH,EACnB9Y,EAAO,IAAM,EACbA,EAAO,IAAM0Y,EAAYjZ,EACzBO,EAAO,IAAM0Y,EAAYhZ,EACzBM,EAAO,IAAM0Y,EAAY/Y,EACzBK,EAAO,IAAM,EAENA,CACT,EASA6X,EAAQkB,6BAA+B,SACrCC,EACAhZ,GAMA,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,2BAA4B6Y,GAGzCnB,EAAQc,uCACbK,EAAyBN,YACzBM,EAAyBP,SACzBO,EAAyB/G,MACzBjS,EAEJ,EAWA6X,EAAQoB,gBAAkB,SAAUP,EAAa1Y,GAK/C,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,cAAeuY,GAG5Bb,EAAQW,wBAAwBrK,EAAQ6G,SAAU0D,EAAa1Y,EACxE,EAiBA6X,EAAQ7F,UAAY,SAAUC,EAAOjS,GAKnC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,QAAS8R,GAGxB7R,EAAAA,QAAQJ,IAqBbA,EAAO,GAAKiS,EAAMxS,EAClBO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiS,EAAMvS,EAClBM,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,IAAMiS,EAAMtS,EACnBK,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAM,EACNA,GApCE,IAAI6X,EACT5F,EAAMxS,EACN,EACA,EACA,EACA,EACAwS,EAAMvS,EACN,EACA,EACA,EACA,EACAuS,EAAMtS,EACN,EACA,EACA,EACA,EACA,EAqBN,EAiBAkY,EAAQ3F,iBAAmB,SAAUD,EAAOjS,GAK1C,OAHAC,EAAAA,MAAMC,OAAO4B,OAAO,QAASmQ,GAGxB7R,EAAAA,QAAQJ,IAqBbA,EAAO,GAAKiS,EACZjS,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiS,EACZjS,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,IAAMiS,EACbjS,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAM,EACNA,GApCE,IAAI6X,EACT5F,EACA,EACA,EACA,EACA,EACAA,EACA,EACA,EACA,EACA,EACAA,EACA,EACA,EACA,EACA,EACA,EAqBN,EAEA,MAAMiH,EAAc,IAAI1Z,EAClB2Z,GAAc,IAAI3Z,EAClB4Z,GAAc,IAAI5Z,EASxBqY,EAAQwB,WAAa,SAAUC,EAAQtZ,GAErCC,EAAAA,MAAMC,OAAOC,OAAO,SAAUmZ,GAG9B,MAAMjM,EAAWiM,EAAOjM,SAClBkM,EAAYD,EAAOC,UACnBC,EAAKF,EAAOE,GAGlBvZ,EAAAA,MAAMC,OAAOC,OAAO,kBAAmBkN,GACvCpN,EAAAA,MAAMC,OAAOC,OAAO,mBAAoBoZ,GACxCtZ,EAAAA,MAAMC,OAAOC,OAAO,YAAaqZ,GAGjCha,EAAW0D,UAAUqW,EAAWL,GAChC1Z,EAAW0D,UACT1D,EAAW+E,MAAM2U,EAAaM,EAAIL,IAClCA,IAEF3Z,EAAW0D,UACT1D,EAAW+E,MAAM4U,GAAaD,EAAaE,IAC3CA,IAGF,MAAMK,EAAKN,GAAY1Z,EACjBia,EAAKP,GAAYzZ,EACjBia,EAAKR,GAAYxZ,EACjBia,EAAKV,EAAYzZ,EACjBoa,EAAKX,EAAYxZ,EACjBoa,EAAKZ,EAAYvZ,EACjBoa,EAAKX,GAAY3Z,EACjBua,EAAKZ,GAAY1Z,EACjBua,EAAKb,GAAYzZ,EACjBmI,EAAYuF,EAAS5N,EACrBsI,EAAYsF,EAAS3N,EACrBsI,EAAYqF,EAAS1N,EACrBua,EAAKT,GAAM3R,EAAY4R,GAAM3R,EAAY4R,GAAM3R,EAC/CmS,EAAKJ,GAAMjS,EAAYkS,GAAMjS,EAAYkS,GAAMjS,EAC/CoS,EAAKR,EAAK9R,EAAY+R,EAAK9R,EAAY+R,EAAK9R,EAiBlD,OAAK5H,EAAAA,QAAQJ,IAoBbA,EAAO,GAAKyZ,EACZzZ,EAAO,GAAK+Z,EACZ/Z,EAAO,IAAM4Z,EACb5Z,EAAO,GAAK,EACZA,EAAO,GAAK0Z,EACZ1Z,EAAO,GAAKga,EACZha,EAAO,IAAM6Z,EACb7Z,EAAO,GAAK,EACZA,EAAO,GAAK2Z,EACZ3Z,EAAO,GAAKia,EACZja,EAAO,KAAO8Z,EACd9Z,EAAO,IAAM,EACbA,EAAO,IAAMka,EACbla,EAAO,IAAMma,EACbna,EAAO,IAAMoa,EACbpa,EAAO,IAAM,EACNA,GAnCE,IAAI6X,EACT4B,EACAC,EACAC,EACAO,EACAH,EACAC,EACAC,EACAE,GACCP,GACAC,GACAC,EACDM,EACA,EACA,EACA,EACA,EAoBN,EAiBAvC,EAAQwC,8BAAgC,SACtCC,EACAC,EACAC,EACAC,EACAza,GAGAC,EAAKA,MAACC,OAAO4B,OAAO4L,YAAY,OAAQ4M,EAAM,GAC9Cra,QAAMC,OAAO4B,OAAO4Y,SAAS,OAAQJ,EAAM7Z,KAAKka,IAChD1a,EAAKA,MAACC,OAAO4B,OAAO4L,YAAY,OAAQ8M,EAAM,GAC9Cva,EAAKA,MAACC,OAAO4B,OAAO4L,YAAY,MAAO+M,EAAK,GAC5Cxa,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAEMwO,EAAc,EAFL/N,KAAKma,IAAW,GAAPN,GAGlBlM,EAAcI,EAAc+L,EAC5B3L,GAAe6L,EAAMD,IAASA,EAAOC,GACrCzC,EAAe,EAAMyC,EAAMD,GAASA,EAAOC,GAkBjD,OAhBAza,EAAO,GAAKoO,EACZpO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKwO,EACZxO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,IAAM4O,EACb5O,EAAO,KAAO,EACdA,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAMgY,EACbhY,EAAO,IAAM,EACNA,CACT,EAcA6X,EAAQgD,6BAA+B,SACrC/X,EACAC,EACA+X,EACAC,EACAP,EACAC,EACAza,GAGAC,EAAAA,MAAMC,OAAO4B,OAAO,OAAQgB,GAC5B7C,EAAAA,MAAMC,OAAO4B,OAAO,QAASiB,GAC7B9C,EAAAA,MAAMC,OAAO4B,OAAO,SAAUgZ,GAC9B7a,EAAAA,MAAMC,OAAO4B,OAAO,MAAOiZ,GAC3B9a,EAAAA,MAAMC,OAAO4B,OAAO,OAAQ0Y,GAC5Bva,EAAAA,MAAMC,OAAO4B,OAAO,MAAO2Y,GAC3Bxa,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,IAAIgF,EAAI,GAAOjC,EAAQD,GACnBmC,EAAI,GAAO8V,EAAMD,GACjBlG,EAAI,GAAO6F,EAAMD,GAErB,MAAMQ,IAAOjY,EAAQD,GAAQkC,EACvBiW,IAAOF,EAAMD,GAAU7V,EACvBiW,IAAOT,EAAMD,GAAQ5F,EAqB3B,OApBA5P,GAAK,EACLC,GAAK,EACL2P,IAAM,EAEN5U,EAAO,GAAKgF,EACZhF,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiF,EACZjF,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,IAAM4U,EACb5U,EAAO,IAAM,EACbA,EAAO,IAAMgb,EACbhb,EAAO,IAAMib,EACbjb,EAAO,IAAMkb,EACblb,EAAO,IAAM,EACNA,CACT,EAcA6X,EAAQsD,4BAA8B,SACpCrY,EACAC,EACA+X,EACAC,EACAP,EACAC,EACAza,GAGAC,EAAAA,MAAMC,OAAO4B,OAAO,OAAQgB,GAC5B7C,EAAAA,MAAMC,OAAO4B,OAAO,QAASiB,GAC7B9C,EAAAA,MAAMC,OAAO4B,OAAO,SAAUgZ,GAC9B7a,EAAAA,MAAMC,OAAO4B,OAAO,MAAOiZ,GAC3B9a,EAAAA,MAAMC,OAAO4B,OAAO,OAAQ0Y,GAC5Bva,EAAAA,MAAMC,OAAO4B,OAAO,MAAO2Y,GAC3Bxa,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoO,EAAe,EAAMoM,GAASzX,EAAQD,GACtC0L,EAAe,EAAMgM,GAASO,EAAMD,GACpCxM,GAAevL,EAAQD,IAASC,EAAQD,GACxC2L,GAAesM,EAAMD,IAAWC,EAAMD,GACtClM,IAAgB6L,EAAMD,IAASC,EAAMD,GAErCxC,GAAgB,EAAMyC,EAAMD,GAASC,EAAMD,GAkBjD,OAhBAxa,EAAO,GAAKoO,EACZpO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKwO,EACZxO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKsO,EACZtO,EAAO,GAAKyO,EACZzO,EAAO,IAAM4O,EACb5O,EAAO,KAdc,EAerBA,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAMgY,EACbhY,EAAO,IAAM,EACNA,CACT,EAaA6X,EAAQuD,oCAAsC,SAC5CtY,EACAC,EACA+X,EACAC,EACAP,EACAxa,GAGAC,EAAAA,MAAMC,OAAO4B,OAAO,OAAQgB,GAC5B7C,EAAAA,MAAMC,OAAO4B,OAAO,QAASiB,GAC7B9C,EAAAA,MAAMC,OAAO4B,OAAO,SAAUgZ,GAC9B7a,EAAAA,MAAMC,OAAO4B,OAAO,MAAOiZ,GAC3B9a,EAAAA,MAAMC,OAAO4B,OAAO,OAAQ0Y,GAC5Bva,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoO,EAAe,EAAMoM,GAASzX,EAAQD,GACtC0L,EAAe,EAAMgM,GAASO,EAAMD,GACpCxM,GAAevL,EAAQD,IAASC,EAAQD,GACxC2L,GAAesM,EAAMD,IAAWC,EAAMD,GAGtC9C,GAAe,EAAMwC,EAkB3B,OAhBAxa,EAAO,GAAKoO,EACZpO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKwO,EACZxO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKsO,EACZtO,EAAO,GAAKyO,EACZzO,EAAO,KAdc,EAerBA,EAAO,KAdc,EAerBA,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,IAAMgY,EACbhY,EAAO,IAAM,EACNA,CACT,EAoBA6X,EAAQwD,8BAAgC,SACtCC,EACAC,EACAC,EACAxb,GAEKI,EAAAA,QAAQJ,KACXA,EAAS,IAAI6X,GAGfyD,EAAWzb,EAAAA,aAAayb,EAAUzb,EAAYA,aAAC4b,cAC/C,MAAMhc,EAAII,EAAYA,aAACyb,EAAS7b,EAAG,GAC7BC,EAAIG,EAAYA,aAACyb,EAAS5b,EAAG,GAC7Bgc,EAAQ7b,EAAYA,aAACyb,EAASI,MAAO,GACrCvV,EAAStG,EAAYA,aAACyb,EAASnV,OAAQ,GAC7CoV,EAAiB1b,EAAYA,aAAC0b,EAAgB,GAG9C,MAAMI,EAAoB,GAARD,EACZE,EAAsB,GAATzV,EACb0V,EAA+C,KAJrDL,EAAgB3b,EAAYA,aAAC2b,EAAe,IAITD,GAE7BnN,EAAcuN,EACdnN,EAAcoN,EACdhN,EAAciN,EACd/D,EAAcrY,EAAIkc,EAClB5D,EAAcrY,EAAIkc,EAClB5D,EAAcuD,EAAiBM,EAmBrC,OAhBA7b,EAAO,GAAKoO,EACZpO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKwO,EACZxO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,IAAM4O,EACb5O,EAAO,IAAM,EACbA,EAAO,IAAM8X,EACb9X,EAAO,IAAM+X,EACb/X,EAAO,IAAMgY,EACbhY,EAAO,IAjBa,EAkBbA,CACT,EAYA6X,EAAQiE,YAAc,SAAUzO,EAAUkM,EAAWC,EAAIzW,EAAO/C,GAyB9D,OAvBAC,EAAAA,MAAMC,OAAOC,OAAO,WAAYkN,GAChCpN,EAAAA,MAAMC,OAAOC,OAAO,YAAaoZ,GACjCtZ,EAAAA,MAAMC,OAAOC,OAAO,KAAMqZ,GAC1BvZ,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAK+C,EAAMtD,EAClBO,EAAO,GAAKwZ,EAAG/Z,EACfO,EAAO,IAAMuZ,EAAU9Z,EACvBO,EAAO,GAAK,EACZA,EAAO,GAAK+C,EAAMrD,EAClBM,EAAO,GAAKwZ,EAAG9Z,EACfM,EAAO,IAAMuZ,EAAU7Z,EACvBM,EAAO,GAAK,EACZA,EAAO,GAAK+C,EAAMpD,EAClBK,EAAO,GAAKwZ,EAAG7Z,EACfK,EAAO,KAAOuZ,EAAU5Z,EACxBK,EAAO,IAAM,EACbA,EAAO,KAAOR,EAAW4D,IAAIL,EAAOsK,GACpCrN,EAAO,KAAOR,EAAW4D,IAAIoW,EAAInM,GACjCrN,EAAO,IAAMR,EAAW4D,IAAImW,EAAWlM,GACvCrN,EAAO,IAAM,EACNA,CACT,EAqBA6X,EAAQlF,QAAU,SAAUzC,EAAQlQ,GAKlC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAGzB9P,EAAAA,QAAQJ,IAoBbA,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACblQ,GAnCE,CACLkQ,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,GACPA,EAAO,IACPA,EAAO,IACPA,EAAO,IACPA,EAAO,IACPA,EAAO,IACPA,EAAO,IAoBb,EAkBA2H,EAAQjF,gBAAkB,SAAUC,EAAQC,GAS1C,OAPA7S,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,MAAO+Q,EAAK,GACpD7S,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,MAAOD,EAAK,GAEjD7S,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,SAAU8Q,EAAQ,GAC1D5S,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,SAAUF,EAAQ,GAGvC,EAATA,EAAaC,CACtB,EA6BA+E,EAAQ7E,UAAY,SAAU9C,EAAQlO,EAAOhC,GAE3CC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAE9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GAErD/B,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMiT,EAAqB,EAARjR,EACbvC,EAAIyQ,EAAO+C,GACXvT,EAAIwQ,EAAO+C,EAAa,GACxBtT,EAAIuQ,EAAO+C,EAAa,GACxBtC,EAAIT,EAAO+C,EAAa,GAM9B,OAJAjT,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACXK,EAAO2Q,EAAIA,EACJ3Q,CACT,EA4BA6X,EAAQ3E,UAAY,SAAUhD,EAAQlO,EAAOlB,EAAWd,GAEtDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAE9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GAErD/B,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAI9B,MAAMiT,EAAqB,EAARjR,EAKnB,OANAhC,EAAS6X,EAAQhX,MAAMqP,EAAQlQ,IAExBiT,GAAcnS,EAAUrB,EAC/BO,EAAOiT,EAAa,GAAKnS,EAAUpB,EACnCM,EAAOiT,EAAa,GAAKnS,EAAUnB,EACnCK,EAAOiT,EAAa,GAAKnS,EAAU6P,EAC5B3Q,CACT,EAWA6X,EAAQkE,eAAiB,SAAU7L,EAAQwI,EAAa1Y,GA2BtD,OAzBAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,cAAeuY,GACnCzY,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GAEnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GAEnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IAEpBlQ,EAAO,IAAM0Y,EAAYjZ,EACzBO,EAAO,IAAM0Y,EAAYhZ,EACzBM,EAAO,IAAM0Y,EAAY/Y,EACzBK,EAAO,IAAMkQ,EAAO,IAEblQ,CACT,EAEA,MAAMgc,GAAe,IAAIxc,EAUzBqY,EAAQoE,SAAW,SAAU/L,EAAQ+B,EAAOjS,GAE1CC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,QAAS8R,GAC7BhS,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMkc,EAAgBrE,EAAQvE,SAASpD,EAAQ8L,IACzCG,EAAW3c,EAAW8D,iBAC1B2O,EACAiK,EACAF,IAEF,OAAOnE,EAAQ/D,gBAAgB5D,EAAQiM,EAAUnc,EACnD,EA6BA6X,EAAQ1E,OAAS,SAAUjD,EAAQlO,EAAOhC,GAExCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAE9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GAErD/B,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMP,EAAIyQ,EAAOlO,GACXtC,EAAIwQ,EAAOlO,EAAQ,GACnBrC,EAAIuQ,EAAOlO,EAAQ,GACnB2O,EAAIT,EAAOlO,EAAQ,IAMzB,OAJAhC,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACXK,EAAO2Q,EAAIA,EACJ3Q,CACT,EA4BA6X,EAAQzE,OAAS,SAAUlD,EAAQlO,EAAOlB,EAAWd,GAgBnD,OAdAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAE9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GAErD/B,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,IAG9BA,EAAS6X,EAAQhX,MAAMqP,EAAQlQ,IACxBgC,GAASlB,EAAUrB,EAC1BO,EAAOgC,EAAQ,GAAKlB,EAAUpB,EAC9BM,EAAOgC,EAAQ,GAAKlB,EAAUnB,EAC9BK,EAAOgC,EAAQ,IAAMlB,EAAU6P,EACxB3Q,CACT,EAEA,MAAMqT,GAAgB,IAAI7T,EAS1BqY,EAAQvE,SAAW,SAAUpD,EAAQlQ,GAenC,OAbAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAID,EAAWe,UACpBf,EAAWoB,aAAasP,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAImD,KAE3DrT,EAAON,EAAIF,EAAWe,UACpBf,EAAWoB,aAAasP,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAImD,KAE3DrT,EAAOL,EAAIH,EAAWe,UACpBf,EAAWoB,aAAasP,EAAO,GAAIA,EAAO,GAAIA,EAAO,IAAKmD,KAErDrT,CACT,EAEA,MAAMuT,GAAe,IAAI/T,EAUzBqY,EAAQrE,gBAAkB,SAAUtD,GAElC,OADA2H,EAAQvE,SAASpD,EAAQqD,IAClB/T,EAAW0C,iBAAiBqR,GACrC,EAUAsE,EAAQpE,SAAW,SAAU3Q,EAAMC,EAAO/C,GAExCC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoc,EAAQtZ,EAAK,GACbuZ,EAAQvZ,EAAK,GACbwZ,EAAQxZ,EAAK,GACbyZ,EAAQzZ,EAAK,GACb0Z,EAAQ1Z,EAAK,GACb2Z,EAAQ3Z,EAAK,GACb4Z,EAAQ5Z,EAAK,GACb6Z,EAAQ7Z,EAAK,GACb8Z,EAAQ9Z,EAAK,GACb+Z,EAAQ/Z,EAAK,GACbga,EAASha,EAAK,IACdia,EAASja,EAAK,IACdka,EAASla,EAAK,IACdma,EAASna,EAAK,IACdoa,EAASpa,EAAK,IACdqa,EAASra,EAAK,IAEdsa,EAASra,EAAM,GACfsa,EAASta,EAAM,GACfua,EAASva,EAAM,GACfwa,EAASxa,EAAM,GACfya,EAASza,EAAM,GACf0a,EAAS1a,EAAM,GACf2a,EAAS3a,EAAM,GACf4a,EAAS5a,EAAM,GACf6a,EAAS7a,EAAM,GACf8a,EAAS9a,EAAM,GACf+a,EAAU/a,EAAM,IAChBgb,EAAUhb,EAAM,IAChBib,EAAUjb,EAAM,IAChBkb,EAAUlb,EAAM,IAChBmb,EAAUnb,EAAM,IAChBob,EAAUpb,EAAM,IAEhBqL,EACJgO,EAAQgB,EAASZ,EAAQa,EAAST,EAAQU,EAASN,EAASO,EACxDhP,EACJ8N,EAAQe,EAASX,EAAQY,EAASR,EAAQS,EAASL,EAASM,EACxD7O,EACJ4N,EAAQc,EAASV,EAAQW,EAASP,EAASQ,EAASJ,EAASK,EACzDtF,EACJsE,EAAQa,EAAST,EAAQU,EAASN,EAASO,EAASH,EAASI,EAEzDlP,EACJ+N,EAAQoB,EAAShB,EAAQiB,EAASb,EAAQc,EAASV,EAASW,EACxDnP,EACJ6N,EAAQmB,EAASf,EAAQgB,EAASZ,EAAQa,EAAST,EAASU,EACxDhP,EACJ2N,EAAQkB,EAASd,EAAQe,EAASX,EAASY,EAASR,EAASS,EACzDzF,EACJqE,EAAQiB,EAASb,EAAQc,EAASV,EAASW,EAASP,EAASQ,EAEzDrP,EACJ8N,EAAQwB,EAASpB,EAAQqB,EAASjB,EAAQkB,EAAUd,EAASe,EACzDtP,EACJ4N,EAAQuB,EAASnB,EAAQoB,EAAShB,EAAQiB,EAAUb,EAASc,EACzDnP,EACJ0N,EAAQsB,EAASlB,EAAQmB,EAASf,EAASgB,EAAUZ,EAASa,EAC1D5F,EACJoE,EAAQqB,EAASjB,EAAQkB,EAASd,EAASe,EAAUX,EAASY,EAE1DjG,EACJsE,EAAQ4B,EAAUxB,EAAQyB,EAAUrB,EAAQsB,EAAUlB,EAASmB,EAC3DpG,EACJsE,EAAQ2B,EAAUvB,EAAQwB,EAAUpB,EAAQqB,EAAUjB,EAASkB,EAC3DnG,EACJsE,EAAQ0B,EAAUtB,EAAQuB,EAAUnB,EAASoB,EAAUhB,EAASiB,EAC5D/F,EACJmE,EAAQyB,EAAUrB,EAAQsB,EAAUlB,EAASmB,EAAUf,EAASgB,EAkBlE,OAhBAne,EAAO,GAAKoO,EACZpO,EAAO,GAAKuO,EACZvO,EAAO,GAAK0O,EACZ1O,EAAO,GAAKiY,EACZjY,EAAO,GAAKqO,EACZrO,EAAO,GAAKwO,EACZxO,EAAO,GAAK2O,EACZ3O,EAAO,GAAKkY,EACZlY,EAAO,GAAKsO,EACZtO,EAAO,GAAKyO,EACZzO,EAAO,IAAM4O,EACb5O,EAAO,IAAMmY,EACbnY,EAAO,IAAM8X,EACb9X,EAAO,IAAM+X,EACb/X,EAAO,IAAMgY,EACbhY,EAAO,IAAMoY,EACNpY,CACT,EAUA6X,EAAQtU,IAAM,SAAUT,EAAMC,EAAO/C,GAuBnC,OArBAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IACvB/C,CACT,EAUA6X,EAAQ7U,SAAW,SAAUF,EAAMC,EAAO/C,GAuBxC,OArBAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IAC9B/C,EAAO,IAAM8C,EAAK,IAAMC,EAAM,IACvB/C,CACT,EAqBA6X,EAAQuG,uBAAyB,SAAUtb,EAAMC,EAAO/C,GAEtDC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoc,EAAQtZ,EAAK,GACbuZ,EAAQvZ,EAAK,GACbwZ,EAAQxZ,EAAK,GACb0Z,EAAQ1Z,EAAK,GACb2Z,EAAQ3Z,EAAK,GACb4Z,EAAQ5Z,EAAK,GACb8Z,EAAQ9Z,EAAK,GACb+Z,EAAQ/Z,EAAK,GACbga,EAASha,EAAK,IACdka,EAASla,EAAK,IACdma,EAASna,EAAK,IACdoa,EAASpa,EAAK,IAEdsa,EAASra,EAAM,GACfsa,EAASta,EAAM,GACfua,EAASva,EAAM,GACfya,EAASza,EAAM,GACf0a,EAAS1a,EAAM,GACf2a,EAAS3a,EAAM,GACf6a,EAAS7a,EAAM,GACf8a,EAAS9a,EAAM,GACf+a,EAAU/a,EAAM,IAChBib,EAAUjb,EAAM,IAChBkb,EAAUlb,EAAM,IAChBmb,EAAUnb,EAAM,IAEhBqL,EAAcgO,EAAQgB,EAASZ,EAAQa,EAAST,EAAQU,EACxD/O,EAAc8N,EAAQe,EAASX,EAAQY,EAASR,EAAQS,EACxD5O,EAAc4N,EAAQc,EAASV,EAAQW,EAASP,EAASQ,EAEzDjP,EAAc+N,EAAQoB,EAAShB,EAAQiB,EAASb,EAAQc,EACxDlP,EAAc6N,EAAQmB,EAASf,EAAQgB,EAASZ,EAAQa,EACxD/O,EAAc2N,EAAQkB,EAASd,EAAQe,EAASX,EAASY,EAEzDpP,EAAc8N,EAAQwB,EAASpB,EAAQqB,EAASjB,EAAQkB,EACxDrP,EAAc4N,EAAQuB,EAASnB,EAAQoB,EAAShB,EAAQiB,EACxDlP,EAAc0N,EAAQsB,EAASlB,EAAQmB,EAASf,EAASgB,EAEzDhG,EACJsE,EAAQ4B,EAAUxB,EAAQyB,EAAUrB,EAAQsB,EAAUlB,EAClDjF,EACJsE,EAAQ2B,EAAUvB,EAAQwB,EAAUpB,EAAQqB,EAAUjB,EAClDjF,EACJsE,EAAQ0B,EAAUtB,EAAQuB,EAAUnB,EAASoB,EAAUhB,EAkBzD,OAhBAld,EAAO,GAAKoO,EACZpO,EAAO,GAAKuO,EACZvO,EAAO,GAAK0O,EACZ1O,EAAO,GAAK,EACZA,EAAO,GAAKqO,EACZrO,EAAO,GAAKwO,EACZxO,EAAO,GAAK2O,EACZ3O,EAAO,GAAK,EACZA,EAAO,GAAKsO,EACZtO,EAAO,GAAKyO,EACZzO,EAAO,IAAM4O,EACb5O,EAAO,IAAM,EACbA,EAAO,IAAM8X,EACb9X,EAAO,IAAM+X,EACb/X,EAAO,IAAMgY,EACbhY,EAAO,IAAM,EACNA,CACT,EAgBA6X,EAAQwG,kBAAoB,SAAUnO,EAAQuI,EAAUzY,GAEtDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,WAAYsY,GAChCxY,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoc,EAAQlM,EAAO,GACfmM,EAAQnM,EAAO,GACfoM,EAAQpM,EAAO,GACfsM,EAAQtM,EAAO,GACfuM,EAAQvM,EAAO,GACfwM,EAAQxM,EAAO,GACf0M,EAAQ1M,EAAO,GACf2M,EAAQ3M,EAAO,GACf4M,EAAS5M,EAAO,IAEhBkN,EAAS3E,EAAS,GAClB4E,EAAS5E,EAAS,GAClB6E,EAAS7E,EAAS,GAClB+E,EAAS/E,EAAS,GAClBgF,EAAShF,EAAS,GAClBiF,EAASjF,EAAS,GAClBmF,EAASnF,EAAS,GAClBoF,EAASpF,EAAS,GAClBqF,EAAUrF,EAAS,GAEnBrK,EAAcgO,EAAQgB,EAASZ,EAAQa,EAAST,EAAQU,EACxD/O,EAAc8N,EAAQe,EAASX,EAAQY,EAASR,EAAQS,EACxD5O,EAAc4N,EAAQc,EAASV,EAAQW,EAASP,EAASQ,EAEzDjP,EAAc+N,EAAQoB,EAAShB,EAAQiB,EAASb,EAAQc,EACxDlP,EAAc6N,EAAQmB,EAASf,EAAQgB,EAASZ,EAAQa,EACxD/O,EAAc2N,EAAQkB,EAASd,EAAQe,EAASX,EAASY,EAEzDpP,EAAc8N,EAAQwB,EAASpB,EAAQqB,EAASjB,EAAQkB,EACxDrP,EAAc4N,EAAQuB,EAASnB,EAAQoB,EAAShB,EAAQiB,EACxDlP,EAAc0N,EAAQsB,EAASlB,EAAQmB,EAASf,EAASgB,EAkB/D,OAhBA9d,EAAO,GAAKoO,EACZpO,EAAO,GAAKuO,EACZvO,EAAO,GAAK0O,EACZ1O,EAAO,GAAK,EACZA,EAAO,GAAKqO,EACZrO,EAAO,GAAKwO,EACZxO,EAAO,GAAK2O,EACZ3O,EAAO,GAAK,EACZA,EAAO,GAAKsO,EACZtO,EAAO,GAAKyO,EACZzO,EAAO,IAAM4O,EACb5O,EAAO,IAAM,EACbA,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACblQ,CACT,EAgBA6X,EAAQyG,sBAAwB,SAAUpO,EAAQwI,EAAa1Y,GAE7DC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,cAAeuY,GACnCzY,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMP,EAAIiZ,EAAYjZ,EAChBC,EAAIgZ,EAAYhZ,EAChBC,EAAI+Y,EAAY/Y,EAEhBqb,EAAKvb,EAAIyQ,EAAO,GAAKxQ,EAAIwQ,EAAO,GAAKvQ,EAAIuQ,EAAO,GAAKA,EAAO,IAC5D+K,EAAKxb,EAAIyQ,EAAO,GAAKxQ,EAAIwQ,EAAO,GAAKvQ,EAAIuQ,EAAO,GAAKA,EAAO,IAC5DgL,EAAKzb,EAAIyQ,EAAO,GAAKxQ,EAAIwQ,EAAO,GAAKvQ,EAAIuQ,EAAO,IAAMA,EAAO,IAkBnE,OAhBAlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMgb,EACbhb,EAAO,IAAMib,EACbjb,EAAO,IAAMkb,EACblb,EAAO,IAAMkQ,EAAO,IACblQ,CACT,EAEA,MAAMue,GAAsB,IAAI/e,EAsBhCqY,EAAQ2G,uBAAyB,SAAUtO,EAAQ+B,EAAOjS,GAUxD,OARAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAO4B,OAAO,QAASmQ,GAC7BhS,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9Bue,GAAoB9e,EAAIwS,EACxBsM,GAAoB7e,EAAIuS,EACxBsM,GAAoB5e,EAAIsS,EACjB4F,EAAQ/D,gBAAgB5D,EAAQqO,GAAqBve,EAC9D,EAsBA6X,EAAQ/D,gBAAkB,SAAU5D,EAAQ+B,EAAOjS,GAEjDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,QAAS8R,GAC7BhS,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM4Y,EAAS3G,EAAMxS,EACfoZ,EAAS5G,EAAMvS,EACfoZ,EAAS7G,EAAMtS,EAGrB,OAAe,IAAXiZ,GAA6B,IAAXC,GAA6B,IAAXC,EAC/BjB,EAAQhX,MAAMqP,EAAQlQ,IAG/BA,EAAO,GAAK4Y,EAAS1I,EAAO,GAC5BlQ,EAAO,GAAK4Y,EAAS1I,EAAO,GAC5BlQ,EAAO,GAAK4Y,EAAS1I,EAAO,GAC5BlQ,EAAO,GAAK,EACZA,EAAO,GAAK6Y,EAAS3I,EAAO,GAC5BlQ,EAAO,GAAK6Y,EAAS3I,EAAO,GAC5BlQ,EAAO,GAAK6Y,EAAS3I,EAAO,GAC5BlQ,EAAO,GAAK,EACZA,EAAO,GAAK8Y,EAAS5I,EAAO,GAC5BlQ,EAAO,GAAK8Y,EAAS5I,EAAO,GAC5BlQ,EAAO,IAAM8Y,EAAS5I,EAAO,IAC7BlQ,EAAO,IAAM,EACbA,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAM,EACNA,EACT,EAUA6X,EAAQnE,iBAAmB,SAAUxD,EAAQpP,EAAWd,GAEtDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM2T,EAAK7S,EAAUrB,EACfmU,EAAK9S,EAAUpB,EACfmU,EAAK/S,EAAUnB,EACf8e,EAAK3d,EAAU6P,EAEflR,EAAIyQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAAK3D,EAAO,IAAMuO,EACpE/e,EAAIwQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAAK3D,EAAO,IAAMuO,EACpE9e,EAAIuQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,IAAM2D,EAAK3D,EAAO,IAAMuO,EACrE9N,EAAIT,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,IAAM2D,EAAK3D,EAAO,IAAMuO,EAM3E,OAJAze,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACXK,EAAO2Q,EAAIA,EACJ3Q,CACT,EAkBA6X,EAAQ6G,wBAA0B,SAAUxO,EAAQpP,EAAWd,GAE7DC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM2T,EAAK7S,EAAUrB,EACfmU,EAAK9S,EAAUpB,EACfmU,EAAK/S,EAAUnB,EAEfF,EAAIyQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAClDnU,EAAIwQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAClDlU,EAAIuQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,IAAM2D,EAKzD,OAHA7T,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACJK,CACT,EAeA6X,EAAQ8G,gBAAkB,SAAUzO,EAAQpP,EAAWd,GAErDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM2T,EAAK7S,EAAUrB,EACfmU,EAAK9S,EAAUpB,EACfmU,EAAK/S,EAAUnB,EAEfF,EAAIyQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAAK3D,EAAO,IAC9DxQ,EAAIwQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,GAAK2D,EAAK3D,EAAO,IAC9DvQ,EAAIuQ,EAAO,GAAKyD,EAAKzD,EAAO,GAAK0D,EAAK1D,EAAO,IAAM2D,EAAK3D,EAAO,IAKrE,OAHAlQ,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACXM,EAAOL,EAAIA,EACJK,CACT,EAyBA6X,EAAQrU,iBAAmB,SAAU0M,EAAQzM,EAAQzD,GAuBnD,OArBAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,IAAMkQ,EAAO,IAAMzM,EAC1BzD,EAAO,IAAMkQ,EAAO,IAAMzM,EAC1BzD,EAAO,IAAMkQ,EAAO,IAAMzM,EAC1BzD,EAAO,IAAMkQ,EAAO,IAAMzM,EAC1BzD,EAAO,IAAMkQ,EAAO,IAAMzM,EAC1BzD,EAAO,IAAMkQ,EAAO,IAAMzM,EACnBzD,CACT,EAwBA6X,EAAQlU,OAAS,SAAUuM,EAAQlQ,GAsBjC,OApBAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,KAAOkQ,EAAO,IACrBlQ,EAAO,KAAOkQ,EAAO,IACrBlQ,EAAO,KAAOkQ,EAAO,IACrBlQ,EAAO,KAAOkQ,EAAO,IACrBlQ,EAAO,KAAOkQ,EAAO,IACrBlQ,EAAO,KAAOkQ,EAAO,IACdlQ,CACT,EAwBA6X,EAAQ9D,UAAY,SAAU7D,EAAQlQ,GAEpCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM4e,EAAU1O,EAAO,GACjB2O,EAAU3O,EAAO,GACjB4O,EAAU5O,EAAO,GACjB6O,EAAU7O,EAAO,GACjB8O,EAAU9O,EAAO,GACjB+O,EAAW/O,EAAO,IAkBxB,OAhBAlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,IACnBlQ,EAAO,GAAK4e,EACZ5e,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,IACnBlQ,EAAO,GAAK6e,EACZ7e,EAAO,GAAK+e,EACZ/e,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAMkQ,EAAO,IACpBlQ,EAAO,IAAM8e,EACb9e,EAAO,IAAMgf,EACbhf,EAAO,IAAMif,EACbjf,EAAO,IAAMkQ,EAAO,IACblQ,CACT,EASA6X,EAAQjU,IAAM,SAAUsM,EAAQlQ,GAuB9B,OArBAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,IAAMS,KAAKmD,IAAIsM,EAAO,KAC7BlQ,EAAO,IAAMS,KAAKmD,IAAIsM,EAAO,KAC7BlQ,EAAO,IAAMS,KAAKmD,IAAIsM,EAAO,KAC7BlQ,EAAO,IAAMS,KAAKmD,IAAIsM,EAAO,KAC7BlQ,EAAO,IAAMS,KAAKmD,IAAIsM,EAAO,KAC7BlQ,EAAO,IAAMS,KAAKmD,IAAIsM,EAAO,KAEtBlQ,CACT,EA+BA6X,EAAQ3S,OAAS,SAAUpC,EAAMC,GAK/B,OACED,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IAERD,EAAK,MAAQC,EAAM,KACnBD,EAAK,MAAQC,EAAM,KACnBD,EAAK,MAAQC,EAAM,KAEnBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,MAAQC,EAAM,KAEnBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,MAAQC,EAAM,KACnBD,EAAK,MAAQC,EAAM,GAEzB,EAiCA8U,EAAQxS,cAAgB,SAAUvC,EAAMC,EAAO0I,GAG7C,OAFAA,EAAU5L,EAAYA,aAAC4L,EAAS,GAG9B3I,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRtC,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,IAAMC,EAAM,MAAQ0I,GAClChL,KAAKmD,IAAId,EAAK,IAAMC,EAAM,MAAQ0I,GAClChL,KAAKmD,IAAId,EAAK,IAAMC,EAAM,MAAQ0I,GAClChL,KAAKmD,IAAId,EAAK,IAAMC,EAAM,MAAQ0I,GAClChL,KAAKmD,IAAId,EAAK,IAAMC,EAAM,MAAQ0I,GAClChL,KAAKmD,IAAId,EAAK,IAAMC,EAAM,MAAQ0I,CAExC,EASAoM,EAAQqH,eAAiB,SAAUhP,EAAQlQ,GASzC,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIyQ,EAAO,IAClBlQ,EAAON,EAAIwQ,EAAO,IAClBlQ,EAAOL,EAAIuQ,EAAO,IACXlQ,CACT,EAwBA6X,EAAQsH,WAAa,SAAUjP,EAAQlQ,GAerC,OAbAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,IACZlQ,CACT,EAEA,MAAMof,GAAyB,IAAIjR,EAC7BkR,GAAqB,IAAIlR,EACzBmR,GAAmB,IAAIlJ,EACvBmJ,GAA2B,IAAInJ,EAAW,EAAK,EAAK,EAAK,GAc/DyB,EAAQ3B,QAAU,SAAUhG,EAAQlQ,GAElCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAM9B,MAAMwf,EAAOtP,EAAO,GACduP,EAAOvP,EAAO,GACdwP,EAAOxP,EAAO,GACdyP,EAAOzP,EAAO,IACd0P,EAAO1P,EAAO,GACd2P,EAAO3P,EAAO,GACd4P,EAAO5P,EAAO,GACd6P,EAAO7P,EAAO,IACd8P,EAAO9P,EAAO,GACd+P,EAAO/P,EAAO,GACdgQ,EAAQhQ,EAAO,IACfiQ,EAAQjQ,EAAO,IACfkQ,EAAQlQ,EAAO,GACfmQ,EAAQnQ,EAAO,GACfoQ,EAAQpQ,EAAO,IACfqQ,EAAQrQ,EAAO,IAGrB,IAAIsQ,EAAON,EAAQK,EACfE,EAAON,EAAQG,EACfI,EAAOT,EAAOM,EACdI,EAAOR,EAAQE,EACfO,EAAOX,EAAOK,EACdO,EAAOX,EAAQG,EACfS,EAAOd,EAAOO,EACdQ,EAAOZ,EAAQC,EACfY,EAAOhB,EAAOM,EACdW,EAAOf,EAAQE,EACfc,EAAQlB,EAAOK,EACfc,EAAQlB,EAAOG,EAGnB,MAAMgB,EACJZ,EAAOX,EACPc,EAAOb,EACPc,EAAOb,GACNU,EAAOZ,EAAOa,EAAOZ,EAAOe,EAAOd,GAChCsB,EACJZ,EAAOb,EACPkB,EAAOhB,EACPmB,EAAOlB,GACNS,EAAOZ,EAAOmB,EAAOjB,EAAOkB,EAAOjB,GAChCuB,EACJZ,EAAOd,EACPmB,EAAOlB,EACPqB,EAAQnB,GACPY,EAAOf,EAAOkB,EAAOjB,EAAOsB,EAAQpB,GACjCwB,EACJV,EAAOjB,EACPoB,EAAOnB,EACPsB,EAAQrB,GACPc,EAAOhB,EAAOqB,EAAOpB,EAAOqB,EAAQpB,GACjC0B,EACJf,EAAOhB,EACPiB,EAAOhB,EACPmB,EAAOlB,GACNa,EAAOf,EAAOkB,EAAOjB,EAAOkB,EAAOjB,GAChC8B,EACJjB,EAAOhB,EACPuB,EAAOrB,EACPsB,EAAOrB,GACNc,EAAOjB,EAAOsB,EAAOpB,EAAOuB,EAAOtB,GAChC+B,EACJf,EAAOnB,EACPsB,EAAOrB,EACP0B,EAAQxB,GACPe,EAAOlB,EAAOuB,EAAOtB,EAAOyB,EAAQvB,GACjCgC,EACJf,EAAOpB,EACPyB,EAAOxB,EACPyB,EAAQxB,GACPmB,EAAOrB,EAAOwB,EAAOvB,EAAO0B,EAAQzB,GAGvCc,EAAOd,EAAOK,EACdU,EAAOd,EAAOG,EACdY,EAAOjB,EAAOM,EACdY,EAAOhB,EAAOE,EACde,EAAOnB,EAAOK,EACde,EAAOnB,EAAOG,EACdiB,EAAOtB,EAAOO,EACdgB,EAAOpB,EAAOC,EACdoB,EAAOxB,EAAOM,EACdmB,EAAOvB,EAAOE,EACdsB,EAAQ1B,EAAOK,EACfsB,EAAQ1B,EAAOG,EAGf,MAAMgC,EACJpB,EAAOH,EACPM,EAAOL,EACPM,EAAOL,GACNE,EAAOJ,EAAQK,EAAOJ,EAAQO,EAAON,GAClCsB,EACJpB,EAAOL,EACPU,EAAOR,EACPW,EAAOV,GACNC,EAAOJ,EAAQW,EAAOT,EAAQU,EAAOT,GAClCuB,EACJpB,EAAON,EACPW,EAAOV,EACPa,EAAQX,GACPI,EAAOP,EAAQU,EAAOT,EAAQc,EAAQZ,GACnCwB,EACJlB,EAAOT,EACPY,EAAOX,EACPc,EAAQb,GACPM,EAAOR,EAAQa,EAAOZ,EAAQa,EAAQZ,GACnC0B,EACJtB,EAAOR,EACPW,EAAOV,EACPM,EAAOR,GACNW,EAAOT,EAAQK,EAAOP,EAAOU,EAAOT,GACjC+B,EACJjB,EAAOb,EACPK,EAAOR,EACPe,EAAOb,GACNY,EAAOZ,EAAQe,EAAOd,EAAQM,EAAOT,GAClCkC,EACJpB,EAAOb,EACPkB,EAAQhB,EACRQ,EAAOX,GACNkB,EAAQf,EAAQO,EAAOV,EAAOe,EAAOd,GAClCkC,EACJjB,EAAQhB,EACRU,EAAOZ,EACPiB,EAAOhB,GACNe,EAAOf,EAAOkB,EAAQjB,EAAQW,EAAOb,GAGxC,IAAIoC,EAAM5C,EAAO4B,EAAO3B,EAAO4B,EAAO3B,EAAO4B,EAAO3B,EAAO4B,EAE3D,GAAI9gB,KAAKmD,IAAIwe,GAAO5c,EAAAA,WAAW6c,UAAW,CAGxC,GACElU,EAAQ9I,cACNwS,EAAQsH,WAAWjP,EAAQkP,IAC3BC,GACA7Z,EAAAA,WAAW8c,WAEblM,EAAWlR,OACT2S,EAAQ1E,OAAOjD,EAAQ,EAAGoP,IAC1BC,IAmBF,OAhBAvf,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,IAAM,EACbA,EAAO,IAAM,EACbA,EAAO,KAAOkQ,EAAO,IACrBlQ,EAAO,KAAOkQ,EAAO,IACrBlQ,EAAO,KAAOkQ,EAAO,IACrBlQ,EAAO,IAAM,EACNA,EAGT,MAAM,IAAIuiB,EAAYA,aACpB,4DAEH,CAqBD,OAlBAH,EAAM,EAAMA,EAEZpiB,EAAO,GAAKohB,EAAOgB,EACnBpiB,EAAO,GAAKqhB,EAAOe,EACnBpiB,EAAO,GAAKshB,EAAOc,EACnBpiB,EAAO,GAAKuhB,EAAOa,EACnBpiB,EAAO,GAAKwhB,EAAOY,EACnBpiB,EAAO,GAAKyhB,EAAOW,EACnBpiB,EAAO,GAAK0hB,EAAOU,EACnBpiB,EAAO,GAAK2hB,EAAOS,EACnBpiB,EAAO,GAAK4hB,EAAOQ,EACnBpiB,EAAO,GAAK6hB,EAAOO,EACnBpiB,EAAO,IAAM8hB,EAAQM,EACrBpiB,EAAO,IAAM+hB,EAAQK,EACrBpiB,EAAO,IAAMgiB,EAAQI,EACrBpiB,EAAO,IAAMiiB,EAAQG,EACrBpiB,EAAO,IAAMkiB,EAAQE,EACrBpiB,EAAO,IAAMmiB,EAAQC,EACdpiB,CACT,EAeA6X,EAAQ2K,sBAAwB,SAAUtS,EAAQlQ,GAEhDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAS9B,MAAMyiB,EAAUvS,EAAO,GACjB0O,EAAU1O,EAAO,GACjB2O,EAAU3O,EAAO,GACjBwS,EAAUxS,EAAO,GACjByS,EAAUzS,EAAO,GACjB6O,EAAU7O,EAAO,GACjB0S,EAAU1S,EAAO,GACjB2S,EAAU3S,EAAO,GACjB4S,EAAW5S,EAAO,IAElByD,EAAKzD,EAAO,IACZ0D,EAAK1D,EAAO,IACZ2D,EAAK3D,EAAO,IAEZzQ,GAAKgjB,EAAU9O,EAAKiL,EAAUhL,EAAKiL,EAAUhL,EAC7CnU,GAAKgjB,EAAU/O,EAAKgP,EAAU/O,EAAKmL,EAAUlL,EAC7ClU,GAAKijB,EAAUjP,EAAKkP,EAAUjP,EAAKkP,EAAWjP,EAkBpD,OAhBA7T,EAAO,GAAKyiB,EACZziB,EAAO,GAAK0iB,EACZ1iB,EAAO,GAAK4iB,EACZ5iB,EAAO,GAAK,EACZA,EAAO,GAAK4e,EACZ5e,EAAO,GAAK2iB,EACZ3iB,EAAO,GAAK6iB,EACZ7iB,EAAO,GAAK,EACZA,EAAO,GAAK6e,EACZ7e,EAAO,GAAK+e,EACZ/e,EAAO,IAAM8iB,EACb9iB,EAAO,IAAM,EACbA,EAAO,IAAMP,EACbO,EAAO,IAAMN,EACbM,EAAO,IAAML,EACbK,EAAO,IAAM,EACNA,CACT,EAEA,MAAMmW,GAAyB,IAAI0B,ECtkFnC,SAASkL,GAAU/T,EAAMI,EAAOF,EAAMI,GAOpC1P,KAAKoP,KAAOnP,EAAAA,aAAamP,EAAM,GAQ/BpP,KAAKwP,MAAQvP,EAAAA,aAAauP,EAAO,GAQjCxP,KAAKsP,KAAOrP,EAAAA,aAAaqP,EAAM,GAQ/BtP,KAAK0P,MAAQzP,EAAAA,aAAayP,EAAO,EACnC,CD+iFAuI,EAAQxB,iBAAmB,SAAUnG,EAAQlQ,GAM3C,OAJAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAGvB6X,EAAQ3B,QACb2B,EAAQ9D,UAAU7D,EAAQiG,IAC1BnW,EAEJ,EAQA6X,EAAQ7C,SAAW7N,OAAOC,OACxB,IAAIyQ,EACF,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAUJA,EAAQ3Q,KAAOC,OAAOC,OACpB,IAAIyQ,EACF,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAUJA,EAAQvB,YAAc,EAQtBuB,EAAQtB,YAAc,EAQtBsB,EAAQrB,YAAc,EAQtBqB,EAAQmL,YAAc,EAQtBnL,EAAQpB,YAAc,EAQtBoB,EAAQnB,YAAc,EAQtBmB,EAAQlB,YAAc,EAQtBkB,EAAQoL,YAAc,EAQtBpL,EAAQjB,YAAc,EAQtBiB,EAAQhB,YAAc,EAQtBgB,EAAQf,YAAc,GAQtBe,EAAQqL,YAAc,GAQtBrL,EAAQsL,YAAc,GAQtBtL,EAAQuL,YAAc,GAQtBvL,EAAQwL,YAAc,GAQtBxL,EAAQyL,YAAc,GAEtBnc,OAAOuE,iBAAiBmM,EAAQvQ,UAAW,CAOzC/F,OAAQ,CACNqK,IAAK,WACH,OAAOiM,EAAQ7W,YAChB,KAUL6W,EAAQvQ,UAAUzG,MAAQ,SAAUb,GAClC,OAAO6X,EAAQhX,MAAMjB,KAAMI,EAC7B,EASA6X,EAAQvQ,UAAUpC,OAAS,SAAUnC,GACnC,OAAO8U,EAAQ3S,OAAOtF,KAAMmD,EAC9B,EAKA8U,EAAQ1S,YAAc,SAAU+K,EAAQ/O,EAAOiE,GAC7C,OACE8K,EAAO,KAAO/O,EAAMiE,IACpB8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,MAAQ/O,EAAMiE,EAAS,KAC9B8K,EAAO,MAAQ/O,EAAMiE,EAAS,KAC9B8K,EAAO,MAAQ/O,EAAMiE,EAAS,KAC9B8K,EAAO,MAAQ/O,EAAMiE,EAAS,KAC9B8K,EAAO,MAAQ/O,EAAMiE,EAAS,KAC9B8K,EAAO,MAAQ/O,EAAMiE,EAAS,GAElC,EAWAyS,EAAQvQ,UAAUjC,cAAgB,SAAUtC,EAAO0I,GACjD,OAAOoM,EAAQxS,cAAczF,KAAMmD,EAAO0I,EAC5C,EAQAoM,EAAQvQ,UAAUC,SAAW,WAC3B,MACE,IACA3H,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,IAPL,OAUAA,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,IAhBL,OAmBAA,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,IACL,KACAA,KAAK,IAzBL,OA4BAA,KAAK,GACL,KACAA,KAAK,GACL,KACAA,KAAK,IACL,KACAA,KAAK,IACL,GAEJ,ECp2FAuH,OAAOuE,iBAAiBqX,GAAUzb,UAAW,CAO3CoU,MAAO,CACL9P,IAAK,WACH,OAAOmX,GAAUQ,aAAa3jB,KAC/B,GASHuG,OAAQ,CACNyF,IAAK,WACH,OAAOmX,GAAUS,cAAc5jB,KAChC,KAQLmjB,GAAU/hB,aAAe,EAWzB+hB,GAAU9hB,KAAO,SAAUC,EAAOC,EAAOC,GAavC,OAXAnB,EAAAA,MAAMC,OAAOC,OAAO,QAASe,GAC7BjB,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5CD,EAAMC,KAAmBF,EAAM8N,KAC/B7N,EAAMC,KAAmBF,EAAMkO,MAC/BjO,EAAMC,KAAmBF,EAAMgO,KAC/B/N,EAAMC,GAAiBF,EAAMoO,MAEtBnO,CACT,EAUA4hB,GAAU1hB,OAAS,SAAUF,EAAOC,EAAepB,GAejD,OAbAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAI+iB,IAGf/iB,EAAOgP,KAAO7N,EAAMC,KACpBpB,EAAOoP,MAAQjO,EAAMC,KACrBpB,EAAOkP,KAAO/N,EAAMC,KACpBpB,EAAOsP,MAAQnO,EAAMC,GACdpB,CACT,EAOA+iB,GAAUQ,aAAe,SAAUzU,GAEjC7O,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAEjC,IAAII,EAAOJ,EAAUI,KACrB,MAAMF,EAAOF,EAAUE,KAIvB,OAHIE,EAAOF,IACTE,GAAQ1J,EAAUA,WAAC+J,QAEdL,EAAOF,CAChB,EAOA+T,GAAUS,cAAgB,SAAU1U,GAIlC,OAFA7O,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAE1BA,EAAUQ,MAAQR,EAAUM,KACrC,EAeA2T,GAAU/c,YAAc,SAAUgJ,EAAMI,EAAOF,EAAMI,EAAOtP,GAM1D,OALAgP,EAAOxJ,EAAUA,WAACa,UAAUxG,EAAYA,aAACmP,EAAM,IAC/CI,EAAQ5J,EAAUA,WAACa,UAAUxG,EAAYA,aAACuP,EAAO,IACjDF,EAAO1J,EAAUA,WAACa,UAAUxG,EAAYA,aAACqP,EAAM,IAC/CI,EAAQ9J,EAAUA,WAACa,UAAUxG,EAAYA,aAACyP,EAAO,IAE5ClP,EAAAA,QAAQJ,IAIbA,EAAOgP,KAAOA,EACdhP,EAAOoP,MAAQA,EACfpP,EAAOkP,KAAOA,EACdlP,EAAOsP,MAAQA,EAERtP,GARE,IAAI+iB,GAAU/T,EAAMI,EAAOF,EAAMI,EAS5C,EAeAyT,GAAUzc,YAAc,SAAU0I,EAAMI,EAAOF,EAAMI,EAAOtP,GAC1D,OAAKI,EAAAA,QAAQJ,IAIbA,EAAOgP,KAAOnP,EAAAA,aAAamP,EAAM,GACjChP,EAAOoP,MAAQvP,EAAAA,aAAauP,EAAO,GACnCpP,EAAOkP,KAAOrP,EAAAA,aAAaqP,EAAM,GACjClP,EAAOsP,MAAQzP,EAAAA,aAAayP,EAAO,GAE5BtP,GARE,IAAI+iB,GAAU/T,EAAMI,EAAOF,EAAMI,EAS5C,EASAyT,GAAUU,sBAAwB,SAAU3W,EAAe9M,GAEzDC,EAAAA,MAAMG,QAAQ,gBAAiB0M,GAG/B,IAAIkC,EAAO0U,OAAOC,UACdzU,GAAQwU,OAAOC,UACfC,EAAcF,OAAOC,UACrBE,GAAeH,OAAOC,UACtBvU,EAAQsU,OAAOC,UACfrU,GAASoU,OAAOC,UAEpB,IAAK,IAAI/hB,EAAI,EAAGkiB,EAAMhX,EAAcvL,OAAQK,EAAIkiB,EAAKliB,IAAK,CACxD,MAAMyL,EAAWP,EAAclL,GAC/BoN,EAAOvO,KAAK4B,IAAI2M,EAAM3B,EAASpH,WAC/BiJ,EAAOzO,KAAK0B,IAAI+M,EAAM7B,EAASpH,WAC/BmJ,EAAQ3O,KAAK4B,IAAI+M,EAAO/B,EAASnH,UACjCoJ,EAAQ7O,KAAK0B,IAAImN,EAAOjC,EAASnH,UAEjC,MAAM6d,EACJ1W,EAASpH,WAAa,EAClBoH,EAASpH,UACToH,EAASpH,UAAYT,EAAUA,WAAC+J,OACtCqU,EAAcnjB,KAAK4B,IAAIuhB,EAAaG,GACpCF,EAAcpjB,KAAK0B,IAAI0hB,EAAaE,EACrC,CAcD,OAZI7U,EAAOF,EAAO6U,EAAcD,IAC9B5U,EAAO4U,EACP1U,EAAO2U,EAEH3U,EAAO1J,EAAUA,WAACmV,KACpBzL,GAAc1J,EAAUA,WAAC+J,QAEvBP,EAAOxJ,EAAUA,WAACmV,KACpB3L,GAAcxJ,EAAUA,WAAC+J,SAIxBnP,EAAAA,QAAQJ,IAIbA,EAAOgP,KAAOA,EACdhP,EAAOoP,MAAQA,EACfpP,EAAOkP,KAAOA,EACdlP,EAAOsP,MAAQA,EACRtP,GAPE,IAAI+iB,GAAU/T,EAAMI,EAAOF,EAAMI,EAQ5C,EAUAyT,GAAUiB,mBAAqB,SAAU/W,EAAY7G,EAAWpG,GAE9DC,EAAAA,MAAMG,QAAQ,aAAc6M,GAE5B7G,EAAYvG,EAAAA,aAAauG,EAAW4E,EAAUiB,OAE9C,IAAI+C,EAAO0U,OAAOC,UACdzU,GAAQwU,OAAOC,UACfC,EAAcF,OAAOC,UACrBE,GAAeH,OAAOC,UACtBvU,EAAQsU,OAAOC,UACfrU,GAASoU,OAAOC,UAEpB,IAAK,IAAI/hB,EAAI,EAAGkiB,EAAM7W,EAAW1L,OAAQK,EAAIkiB,EAAKliB,IAAK,CACrD,MAAMyL,EAAWjH,EAAU2G,wBAAwBE,EAAWrL,IAC9DoN,EAAOvO,KAAK4B,IAAI2M,EAAM3B,EAASpH,WAC/BiJ,EAAOzO,KAAK0B,IAAI+M,EAAM7B,EAASpH,WAC/BmJ,EAAQ3O,KAAK4B,IAAI+M,EAAO/B,EAASnH,UACjCoJ,EAAQ7O,KAAK0B,IAAImN,EAAOjC,EAASnH,UAEjC,MAAM6d,EACJ1W,EAASpH,WAAa,EAClBoH,EAASpH,UACToH,EAASpH,UAAYT,EAAUA,WAAC+J,OACtCqU,EAAcnjB,KAAK4B,IAAIuhB,EAAaG,GACpCF,EAAcpjB,KAAK0B,IAAI0hB,EAAaE,EACrC,CAcD,OAZI7U,EAAOF,EAAO6U,EAAcD,IAC9B5U,EAAO4U,EACP1U,EAAO2U,EAEH3U,EAAO1J,EAAUA,WAACmV,KACpBzL,GAAc1J,EAAUA,WAAC+J,QAEvBP,EAAOxJ,EAAUA,WAACmV,KACpB3L,GAAcxJ,EAAUA,WAAC+J,SAIxBnP,EAAAA,QAAQJ,IAIbA,EAAOgP,KAAOA,EACdhP,EAAOoP,MAAQA,EACfpP,EAAOkP,KAAOA,EACdlP,EAAOsP,MAAQA,EACRtP,GAPE,IAAI+iB,GAAU/T,EAAMI,EAAOF,EAAMI,EAQ5C,EASAyT,GAAUliB,MAAQ,SAAUiO,EAAW9O,GACrC,GAAKI,EAAAA,QAAQ0O,GAIb,OAAK1O,EAAAA,QAAQJ,IASbA,EAAOgP,KAAOF,EAAUE,KACxBhP,EAAOoP,MAAQN,EAAUM,MACzBpP,EAAOkP,KAAOJ,EAAUI,KACxBlP,EAAOsP,MAAQR,EAAUQ,MAClBtP,GAZE,IAAI+iB,GACTjU,EAAUE,KACVF,EAAUM,MACVN,EAAUI,KACVJ,EAAUQ,MAShB,EAYAyT,GAAU1d,cAAgB,SAAUvC,EAAMC,EAAOwC,GAG/C,OAFAA,EAAkB1F,EAAYA,aAAC0F,EAAiB,GAG9CzC,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRtC,KAAKmD,IAAId,EAAKkM,KAAOjM,EAAMiM,OAASzJ,GACpC9E,KAAKmD,IAAId,EAAKsM,MAAQrM,EAAMqM,QAAU7J,GACtC9E,KAAKmD,IAAId,EAAKoM,KAAOnM,EAAMmM,OAAS3J,GACpC9E,KAAKmD,IAAId,EAAKwM,MAAQvM,EAAMuM,QAAU/J,CAE5C,EAQAwd,GAAUzb,UAAUzG,MAAQ,SAAUb,GACpC,OAAO+iB,GAAUliB,MAAMjB,KAAMI,EAC/B,EASA+iB,GAAUzb,UAAUpC,OAAS,SAAU+e,GACrC,OAAOlB,GAAU7d,OAAOtF,KAAMqkB,EAChC,EAUAlB,GAAU7d,OAAS,SAAUpC,EAAMC,GACjC,OACED,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRD,EAAKkM,OAASjM,EAAMiM,MACpBlM,EAAKsM,QAAUrM,EAAMqM,OACrBtM,EAAKoM,OAASnM,EAAMmM,MACpBpM,EAAKwM,QAAUvM,EAAMuM,KAE3B,EAWAyT,GAAUzb,UAAUjC,cAAgB,SAAU4e,EAAOxY,GACnD,OAAOsX,GAAU1d,cAAczF,KAAMqkB,EAAOxY,EAC9C,EAYAsX,GAAUmB,SAAW,SAAUpV,GAE7B7O,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAEjC,MAAMQ,EAAQR,EAAUQ,MACxBrP,QAAMC,OAAO4B,OAAOC,oBAClB,QACAuN,GACC9J,EAAUA,WAAC2e,aAEdlkB,QAAMC,OAAO4B,OAAOiR,iBAAiB,QAASzD,EAAO9J,EAAAA,WAAW2e,aAEhE,MAAM/U,EAAQN,EAAUM,MACxBnP,QAAMC,OAAO4B,OAAOC,oBAClB,QACAqN,GACC5J,EAAUA,WAAC2e,aAEdlkB,QAAMC,OAAO4B,OAAOiR,iBAAiB,QAAS3D,EAAO5J,EAAAA,WAAW2e,aAEhE,MAAMnV,EAAOF,EAAUE,KACvB/O,QAAMC,OAAO4B,OAAOC,oBAAoB,OAAQiN,GAAOvO,KAAKka,IAC5D1a,QAAMC,OAAO4B,OAAOiR,iBAAiB,OAAQ/D,EAAMvO,KAAKka,IAExD,MAAMzL,EAAOJ,EAAUI,KACvBjP,QAAMC,OAAO4B,OAAOC,oBAAoB,OAAQmN,GAAOzO,KAAKka,IAC5D1a,QAAMC,OAAO4B,OAAOiR,iBAAiB,OAAQ7D,EAAMzO,KAAKka,GAE1D,EASAoI,GAAUqB,UAAY,SAAUtV,EAAW9O,GAKzC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAG5B1O,EAAAA,QAAQJ,IAGbA,EAAOiG,UAAY6I,EAAUE,KAC7BhP,EAAOkG,SAAW4I,EAAUM,MAC5BpP,EAAOmG,OAAS,EACTnG,GALE,IAAI8J,EAAagF,EAAUE,KAAMF,EAAUM,MAMtD,EASA2T,GAAUsB,UAAY,SAAUvV,EAAW9O,GAKzC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAG5B1O,EAAAA,QAAQJ,IAGbA,EAAOiG,UAAY6I,EAAUE,KAC7BhP,EAAOkG,SAAW4I,EAAUQ,MAC5BtP,EAAOmG,OAAS,EACTnG,GALE,IAAI8J,EAAagF,EAAUE,KAAMF,EAAUQ,MAMtD,EASAyT,GAAUuB,UAAY,SAAUxV,EAAW9O,GAKzC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAG5B1O,EAAAA,QAAQJ,IAGbA,EAAOiG,UAAY6I,EAAUI,KAC7BlP,EAAOkG,SAAW4I,EAAUQ,MAC5BtP,EAAOmG,OAAS,EACTnG,GALE,IAAI8J,EAAagF,EAAUI,KAAMJ,EAAUQ,MAMtD,EASAyT,GAAUwB,UAAY,SAAUzV,EAAW9O,GAKzC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAG5B1O,EAAAA,QAAQJ,IAGbA,EAAOiG,UAAY6I,EAAUI,KAC7BlP,EAAOkG,SAAW4I,EAAUM,MAC5BpP,EAAOmG,OAAS,EACTnG,GALE,IAAI8J,EAAagF,EAAUI,KAAMJ,EAAUM,MAMtD,EASA2T,GAAUyB,OAAS,SAAU1V,EAAW9O,GAEtCC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAGjC,IAAII,EAAOJ,EAAUI,KACrB,MAAMF,EAAOF,EAAUE,KAEnBE,EAAOF,IACTE,GAAQ1J,EAAUA,WAAC+J,QAGrB,MAAMtJ,EAAYT,EAAAA,WAAWif,eAA+B,IAAfzV,EAAOE,IAC9ChJ,EAAiD,IAArC4I,EAAUM,MAAQN,EAAUQ,OAE9C,OAAKlP,EAAAA,QAAQJ,IAIbA,EAAOiG,UAAYA,EACnBjG,EAAOkG,SAAWA,EAClBlG,EAAOmG,OAAS,EACTnG,GANE,IAAI8J,EAAa7D,EAAWC,EAOvC,EAcA6c,GAAUta,aAAe,SAAUqG,EAAW4V,EAAgB1kB,GAE5DC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GACjC7O,EAAAA,MAAMC,OAAOC,OAAO,iBAAkBukB,GAGtC,IAAIC,EAAgB7V,EAAUI,KAC1B0V,EAAgB9V,EAAUE,KAE1B6V,EAAqBH,EAAexV,KACpC4V,EAAqBJ,EAAe1V,KAEpC2V,EAAgBC,GAAiBC,EAAqB,EACxDF,GAAiBnf,EAAUA,WAAC+J,OACnBsV,EAAqBC,GAAsBH,EAAgB,IACpEE,GAAsBrf,EAAUA,WAAC+J,QAG/BoV,EAAgBC,GAAiBE,EAAqB,EACxDA,GAAsBtf,EAAUA,WAAC+J,OACxBsV,EAAqBC,GAAsBF,EAAgB,IACpEA,GAAiBpf,EAAUA,WAAC+J,QAG9B,MAAMP,EAAOxJ,EAAAA,WAAWif,eACtBhkB,KAAK0B,IAAIyiB,EAAeE,IAEpB5V,EAAO1J,EAAAA,WAAWif,eACtBhkB,KAAK4B,IAAIsiB,EAAeE,IAG1B,IACG/V,EAAUE,KAAOF,EAAUI,MAC1BwV,EAAe1V,KAAO0V,EAAexV,OACvCA,GAAQF,EAER,OAGF,MAAMI,EAAQ3O,KAAK0B,IAAI2M,EAAUM,MAAOsV,EAAetV,OACjDE,EAAQ7O,KAAK4B,IAAIyM,EAAUQ,MAAOoV,EAAepV,OAEvD,OAAIF,GAASE,OAAb,EAIKlP,EAAAA,QAAQJ,IAGbA,EAAOgP,KAAOA,EACdhP,EAAOoP,MAAQA,EACfpP,EAAOkP,KAAOA,EACdlP,EAAOsP,MAAQA,EACRtP,GANE,IAAI+iB,GAAU/T,EAAMI,EAAOF,EAAMI,EAO5C,EAaAyT,GAAUgC,mBAAqB,SAAUjW,EAAW4V,EAAgB1kB,GAElEC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GACjC7O,EAAAA,MAAMC,OAAOC,OAAO,iBAAkBukB,GAGtC,MAAM1V,EAAOvO,KAAK0B,IAAI2M,EAAUE,KAAM0V,EAAe1V,MAC/CI,EAAQ3O,KAAK0B,IAAI2M,EAAUM,MAAOsV,EAAetV,OACjDF,EAAOzO,KAAK4B,IAAIyM,EAAUI,KAAMwV,EAAexV,MAC/CI,EAAQ7O,KAAK4B,IAAIyM,EAAUQ,MAAOoV,EAAepV,OAEvD,KAAIF,GAASE,GAASN,GAAQE,GAI9B,OAAK9O,EAAAA,QAAQJ,IAIbA,EAAOgP,KAAOA,EACdhP,EAAOoP,MAAQA,EACfpP,EAAOkP,KAAOA,EACdlP,EAAOsP,MAAQA,EACRtP,GAPE,IAAI+iB,GAAU/T,EAAMI,EAAOF,EAAMI,EAQ5C,EAUAyT,GAAUiC,MAAQ,SAAUlW,EAAW4V,EAAgB1kB,GAErDC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GACjC7O,EAAAA,MAAMC,OAAOC,OAAO,iBAAkBukB,GAGjCtkB,EAAAA,QAAQJ,KACXA,EAAS,IAAI+iB,IAGf,IAAI4B,EAAgB7V,EAAUI,KAC1B0V,EAAgB9V,EAAUE,KAE1B6V,EAAqBH,EAAexV,KACpC4V,EAAqBJ,EAAe1V,KAEpC2V,EAAgBC,GAAiBC,EAAqB,EACxDF,GAAiBnf,EAAUA,WAAC+J,OACnBsV,EAAqBC,GAAsBH,EAAgB,IACpEE,GAAsBrf,EAAUA,WAAC+J,QAG/BoV,EAAgBC,GAAiBE,EAAqB,EACxDA,GAAsBtf,EAAUA,WAAC+J,OACxBsV,EAAqBC,GAAsBF,EAAgB,IACpEA,GAAiBpf,EAAUA,WAAC+J,QAG9B,MAAMP,EAAOxJ,EAAAA,WAAWif,eACtBhkB,KAAK4B,IAAIuiB,EAAeE,IAEpB5V,EAAO1J,EAAAA,WAAWif,eACtBhkB,KAAK0B,IAAIwiB,EAAeE,IAQ1B,OALA7kB,EAAOgP,KAAOA,EACdhP,EAAOoP,MAAQ3O,KAAK4B,IAAIyM,EAAUM,MAAOsV,EAAetV,OACxDpP,EAAOkP,KAAOA,EACdlP,EAAOsP,MAAQ7O,KAAK0B,IAAI2M,EAAUQ,MAAOoV,EAAepV,OAEjDtP,CACT,EAUA+iB,GAAUkC,OAAS,SAAUnW,EAAWtD,EAAcxL,GAepD,OAbAC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GACjC7O,EAAAA,MAAMC,OAAOC,OAAO,eAAgBqL,GAG/BpL,EAAAA,QAAQJ,KACXA,EAAS,IAAI+iB,IAGf/iB,EAAOgP,KAAOvO,KAAK4B,IAAIyM,EAAUE,KAAMxD,EAAavF,WACpDjG,EAAOoP,MAAQ3O,KAAK4B,IAAIyM,EAAUM,MAAO5D,EAAatF,UACtDlG,EAAOkP,KAAOzO,KAAK0B,IAAI2M,EAAUI,KAAM1D,EAAavF,WACpDjG,EAAOsP,MAAQ7O,KAAK0B,IAAI2M,EAAUQ,MAAO9D,EAAatF,UAE/ClG,CACT,EASA+iB,GAAUmC,SAAW,SAAUpW,EAAWtD,GAExCvL,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GACjC7O,EAAAA,MAAMC,OAAOC,OAAO,eAAgBqL,GAGpC,IAAIvF,EAAYuF,EAAavF,UAC7B,MAAMC,EAAWsF,EAAatF,SAExB8I,EAAOF,EAAUE,KACvB,IAAIE,EAAOJ,EAAUI,KAQrB,OANIA,EAAOF,IACTE,GAAQ1J,EAAUA,WAAC+J,OACftJ,EAAY,IACdA,GAAaT,EAAUA,WAAC+J,UAIzBtJ,EAAY+I,GACXxJ,EAAAA,WAAWH,cAAcY,EAAW+I,EAAMxJ,EAAAA,WAAWgH,cACtDvG,EAAYiJ,GACX1J,EAAUA,WAACH,cAAcY,EAAWiJ,EAAM1J,EAAUA,WAACgH,aACvDtG,GAAY4I,EAAUM,OACtBlJ,GAAY4I,EAAUQ,KAE1B,EAEA,MAAM6V,GAAsB,IAAIrb,EChyBhC,SAASsb,GAAW3lB,EAAGC,GAMrBE,KAAKH,EAAII,EAAAA,aAAaJ,EAAG,GAOzBG,KAAKF,EAAIG,EAAAA,aAAaH,EAAG,EAC3B,CD8xBAqjB,GAAUsC,UAAY,SAAUvW,EAAW1I,EAAWkf,EAAetlB,GAEnEC,EAAAA,MAAMC,OAAOC,OAAO,YAAa2O,GAGjC1I,EAAYvG,EAAAA,aAAauG,EAAW4E,EAAUiB,OAC9CqZ,EAAgBzlB,EAAYA,aAACylB,EAAe,GAEvCllB,EAAAA,QAAQJ,KACXA,EAAS,IAEX,IAAIuB,EAAS,EAEb,MAAM+N,EAAQR,EAAUQ,MAClBF,EAAQN,EAAUM,MAClBF,EAAOJ,EAAUI,KACjBF,EAAOF,EAAUE,KAEjBuW,EAAMJ,GACZI,EAAIpf,OAASmf,EAEbC,EAAItf,UAAY+I,EAChBuW,EAAIrf,SAAWoJ,EACftP,EAAOuB,GAAU6E,EAAUuG,wBAAwB4Y,EAAKvlB,EAAOuB,IAC/DA,IAEAgkB,EAAItf,UAAYiJ,EAChBlP,EAAOuB,GAAU6E,EAAUuG,wBAAwB4Y,EAAKvlB,EAAOuB,IAC/DA,IAEAgkB,EAAIrf,SAAWkJ,EACfpP,EAAOuB,GAAU6E,EAAUuG,wBAAwB4Y,EAAKvlB,EAAOuB,IAC/DA,IAEAgkB,EAAItf,UAAY+I,EAChBhP,EAAOuB,GAAU6E,EAAUuG,wBAAwB4Y,EAAKvlB,EAAOuB,IAC/DA,IAGEgkB,EAAIrf,SADFoJ,EAAQ,EACKA,EACNF,EAAQ,EACFA,EAEA,EAGjB,IAAK,IAAIxN,EAAI,EAAGA,EAAI,IAAKA,EACvB2jB,EAAItf,WAAaxF,KAAKka,GAAK/Y,EAAI4D,EAAUA,WAAC2e,YACtCpB,GAAUmC,SAASpW,EAAWyW,KAChCvlB,EAAOuB,GAAU6E,EAAUuG,wBAAwB4Y,EAAKvlB,EAAOuB,IAC/DA,KAaJ,OATqB,IAAjBgkB,EAAIrf,WACNqf,EAAItf,UAAY+I,EAChBhP,EAAOuB,GAAU6E,EAAUuG,wBAAwB4Y,EAAKvlB,EAAOuB,IAC/DA,IACAgkB,EAAItf,UAAYiJ,EAChBlP,EAAOuB,GAAU6E,EAAUuG,wBAAwB4Y,EAAKvlB,EAAOuB,IAC/DA,KAEFvB,EAAOuB,OAASA,EACTvB,CACT,EAQA+iB,GAAUY,UAAYxc,OAAOC,OAC3B,IAAI2b,IACDtiB,KAAKka,IACLnV,EAAUA,WAAC2e,YACZ1jB,KAAKka,GACLnV,EAAAA,WAAW2e,cCj2BfiB,GAAWxkB,aAAe,SAAUnB,EAAGC,EAAGM,GACxC,OAAKI,EAAAA,QAAQJ,IAIbA,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACJM,GALE,IAAIolB,GAAW3lB,EAAGC,EAM7B,EASA0lB,GAAWvkB,MAAQ,SAAUC,EAAWd,GACtC,GAAKI,EAAAA,QAAQU,GAGb,OAAKV,EAAAA,QAAQJ,IAIbA,EAAOP,EAAIqB,EAAUrB,EACrBO,EAAON,EAAIoB,EAAUpB,EACdM,GALE,IAAIolB,GAAWtkB,EAAUrB,EAAGqB,EAAUpB,EAMjD,EAWA0lB,GAAWpZ,eAAiBoZ,GAAWvkB,MAWvCukB,GAAWrkB,eAAiBqkB,GAAWvkB,MAMvCukB,GAAWpkB,aAAe,EAW1BokB,GAAWnkB,KAAO,SAAUC,EAAOC,EAAOC,GAWxC,OATAnB,EAAAA,MAAMC,OAAOC,OAAO,QAASe,GAC7BjB,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5CD,EAAMC,KAAmBF,EAAMzB,EAC/B0B,EAAMC,GAAiBF,EAAMxB,EAEtByB,CACT,EAUAikB,GAAW/jB,OAAS,SAAUF,EAAOC,EAAepB,GAYlD,OAVAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAIolB,IAEfplB,EAAOP,EAAI0B,EAAMC,KACjBpB,EAAON,EAAIyB,EAAMC,GACVpB,CACT,EAUAolB,GAAW9jB,UAAY,SAAUH,EAAOnB,GAEtCC,EAAAA,MAAMG,QAAQ,QAASe,GAGvB,MAAMI,EAASJ,EAAMI,OACfC,EAAwB,EAATD,EACrB,GAAKnB,EAAAA,QAAQJ,GAEN,KAAKyB,MAAMC,QAAQ1B,IAAWA,EAAOuB,SAAWC,EACrD,MAAM,IAAIG,EAAcA,eACtB,8EAEO3B,EAAOuB,SAAWC,IAC3BxB,EAAOuB,OAASC,EACjB,MAPCxB,EAAS,IAAIyB,MAAMD,GASrB,IAAK,IAAII,EAAI,EAAGA,EAAIL,IAAUK,EAC5BwjB,GAAWnkB,KAAKE,EAAMS,GAAI5B,EAAY,EAAJ4B,GAEpC,OAAO5B,CACT,EASAolB,GAAWvjB,YAAc,SAAUV,EAAOnB,GAIxC,GAFAC,EAAAA,MAAMG,QAAQ,QAASe,GACvBlB,QAAMC,OAAO4B,OAAOC,oBAAoB,eAAgBZ,EAAMI,OAAQ,GAClEJ,EAAMI,OAAS,GAAM,EACvB,MAAM,IAAII,EAAAA,eAAe,yCAI3B,MAAMJ,EAASJ,EAAMI,OAChBnB,EAAAA,QAAQJ,GAGXA,EAAOuB,OAASA,EAAS,EAFzBvB,EAAS,IAAIyB,MAAMF,EAAS,GAK9B,IAAK,IAAIK,EAAI,EAAGA,EAAIL,EAAQK,GAAK,EAAG,CAClC,MAAMI,EAAQJ,EAAI,EAClB5B,EAAOgC,GAASojB,GAAW/jB,OAAOF,EAAOS,EAAG5B,EAAOgC,GACpD,CACD,OAAOhC,CACT,EAoBAolB,GAAWnjB,UAAYmjB,GAAW/jB,OAQlC+jB,GAAWljB,iBAAmB,SAAUpB,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAG1BL,KAAK0B,IAAIrB,EAAUrB,EAAGqB,EAAUpB,EACzC,EAQA0lB,GAAWhjB,iBAAmB,SAAUtB,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAG1BL,KAAK4B,IAAIvB,EAAUrB,EAAGqB,EAAUpB,EACzC,EAUA0lB,GAAW9iB,mBAAqB,SAAUC,EAAOC,EAAQxC,GAUvD,OARAC,EAAAA,MAAMC,OAAOC,OAAO,QAASoC,GAC7BtC,EAAAA,MAAMC,OAAOC,OAAO,SAAUqC,GAC9BvC,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAK4B,IAAIE,EAAM9C,EAAG+C,EAAO/C,GACpCO,EAAON,EAAIe,KAAK4B,IAAIE,EAAM7C,EAAG8C,EAAO9C,GAE7BM,CACT,EAUAolB,GAAW3iB,mBAAqB,SAAUF,EAAOC,EAAQxC,GASvD,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,QAASoC,GAC7BtC,EAAAA,MAAMC,OAAOC,OAAO,SAAUqC,GAC9BvC,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAK0B,IAAII,EAAM9C,EAAG+C,EAAO/C,GACpCO,EAAON,EAAIe,KAAK0B,IAAII,EAAM7C,EAAG8C,EAAO9C,GAC7BM,CACT,EAQAolB,GAAW1iB,iBAAmB,SAAU5B,GAKtC,OAHAb,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GAG1BA,EAAUrB,EAAIqB,EAAUrB,EAAIqB,EAAUpB,EAAIoB,EAAUpB,CAC7D,EAQA0lB,GAAW7kB,UAAY,SAAUO,GAC/B,OAAOL,KAAKkC,KAAKyiB,GAAW1iB,iBAAiB5B,GAC/C,EAEA,MAAM8B,GAAkB,IAAIwiB,GAa5BA,GAAWviB,SAAW,SAAUC,EAAMC,GAOpC,OALA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAG7BqiB,GAAWpiB,SAASF,EAAMC,EAAOH,IAC1BwiB,GAAW7kB,UAAUqC,GAC9B,EAcAwiB,GAAWniB,gBAAkB,SAAUH,EAAMC,GAO3C,OALA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAG7BqiB,GAAWpiB,SAASF,EAAMC,EAAOH,IAC1BwiB,GAAW1iB,iBAAiBE,GACrC,EASAwiB,GAAWliB,UAAY,SAAUpC,EAAWd,GAE1CC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMO,EAAY6kB,GAAW7kB,UAAUO,GAMvC,GAJAd,EAAOP,EAAIqB,EAAUrB,EAAIc,EACzBP,EAAON,EAAIoB,EAAUpB,EAAIa,EAGrB4C,MAAMnD,EAAOP,IAAM0D,MAAMnD,EAAON,GAClC,MAAM,IAAIiC,EAAAA,eAAe,qCAI3B,OAAO3B,CACT,EASAolB,GAAWhiB,IAAM,SAAUN,EAAMC,GAM/B,OAJA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAGtBD,EAAKrD,EAAIsD,EAAMtD,EAAIqD,EAAKpD,EAAIqD,EAAMrD,CAC3C,EASA0lB,GAAW7gB,MAAQ,SAAUzB,EAAMC,GAMjC,OAJA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAGtBD,EAAKrD,EAAIsD,EAAMrD,EAAIoD,EAAKpD,EAAIqD,EAAMtD,CAC3C,EAUA2lB,GAAW/hB,mBAAqB,SAAUP,EAAMC,EAAO/C,GASrD,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EACnBM,CACT,EAUAolB,GAAW9hB,iBAAmB,SAAUR,EAAMC,EAAO/C,GASnD,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EACnBM,CACT,EAUAolB,GAAW7hB,IAAM,SAAUT,EAAMC,EAAO/C,GAStC,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EACnBM,CACT,EAUAolB,GAAWpiB,SAAW,SAAUF,EAAMC,EAAO/C,GAS3C,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqD,EAAKrD,EAAIsD,EAAMtD,EAC1BO,EAAON,EAAIoD,EAAKpD,EAAIqD,EAAMrD,EACnBM,CACT,EAUAolB,GAAW5hB,iBAAmB,SAAU1C,EAAW2C,EAAQzD,GASzD,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqB,EAAUrB,EAAIgE,EACzBzD,EAAON,EAAIoB,EAAUpB,EAAI+D,EAClBzD,CACT,EAUAolB,GAAW1hB,eAAiB,SAAU5C,EAAW2C,EAAQzD,GASvD,OAPAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIqB,EAAUrB,EAAIgE,EACzBzD,EAAON,EAAIoB,EAAUpB,EAAI+D,EAClBzD,CACT,EASAolB,GAAWzhB,OAAS,SAAU7C,EAAWd,GAQvC,OANAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,GAAKqB,EAAUrB,EACtBO,EAAON,GAAKoB,EAAUpB,EACfM,CACT,EASAolB,GAAWxhB,IAAM,SAAU9C,EAAWd,GAQpC,OANAC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAIgB,KAAKmD,IAAI9C,EAAUrB,GAC9BO,EAAON,EAAIe,KAAKmD,IAAI9C,EAAUpB,GACvBM,CACT,EAEA,MAAM6D,GAAc,IAAIuhB,GAUxBA,GAAWthB,KAAO,SAAUC,EAAOC,EAAKC,EAAGjE,GAUzC,OARAC,EAAAA,MAAMC,OAAOC,OAAO,QAAS4D,GAC7B9D,EAAAA,MAAMC,OAAOC,OAAO,MAAO6D,GAC3B/D,EAAAA,MAAMC,OAAO4B,OAAO,IAAKmC,GACzBhE,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BolB,GAAW5hB,iBAAiBQ,EAAKC,EAAGJ,IACpC7D,EAASolB,GAAW5hB,iBAAiBO,EAAO,EAAME,EAAGjE,GAC9ColB,GAAW7hB,IAAIM,GAAa7D,EAAQA,EAC7C,EAEA,MAAMkE,GAAsB,IAAIkhB,GAC1BjhB,GAAuB,IAAIihB,GAQjCA,GAAWhhB,aAAe,SAAUtB,EAAMC,GAQxC,OANA9C,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAG7BqiB,GAAWliB,UAAUJ,EAAMoB,IAC3BkhB,GAAWliB,UAAUH,EAAOoB,IACrBqB,EAAUA,WAACggB,YAChBJ,GAAWhiB,IAAIc,GAAqBC,IAExC,EAEA,MAAMM,GAA4B,IAAI2gB,GCzlBtC,SAASK,GAAQrX,EAAaC,EAAaE,EAAaC,GACtD5O,KAAK,GAAKC,EAAYA,aAACuO,EAAa,GACpCxO,KAAK,GAAKC,EAAYA,aAAC0O,EAAa,GACpC3O,KAAK,GAAKC,EAAYA,aAACwO,EAAa,GACpCzO,KAAK,GAAKC,EAAYA,aAAC2O,EAAa,EACtC,CD4lBA4W,GAAW1gB,mBAAqB,SAAU5D,EAAWd,GAEnDC,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAM2E,EAAIygB,GAAWliB,UAAUpC,EAAW2D,IAS1C,OARA2gB,GAAWxhB,IAAIe,EAAGA,GAGhB3E,EADE2E,EAAElF,GAAKkF,EAAEjF,EACF0lB,GAAWvkB,MAAMukB,GAAWxgB,OAAQ5E,GAEpColB,GAAWvkB,MAAMukB,GAAWtgB,OAAQ9E,EAIjD,EAUAolB,GAAWlgB,OAAS,SAAUpC,EAAMC,GAClC,OACED,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRD,EAAKrD,IAAMsD,EAAMtD,GACjBqD,EAAKpD,IAAMqD,EAAMrD,CAEvB,EAKA0lB,GAAWjgB,YAAc,SAAUrE,EAAWK,EAAOiE,GACnD,OAAOtE,EAAUrB,IAAM0B,EAAMiE,IAAWtE,EAAUpB,IAAMyB,EAAMiE,EAAS,EACzE,EAaAggB,GAAW/f,cAAgB,SACzBvC,EACAC,EACAuC,EACAC,GAEA,OACEzC,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRyC,EAAAA,WAAWH,cACTvC,EAAKrD,EACLsD,EAAMtD,EACN6F,EACAC,IAEFC,EAAAA,WAAWH,cACTvC,EAAKpD,EACLqD,EAAMrD,EACN4F,EACAC,EAGR,EAQA6f,GAAWle,KAAOC,OAAOC,OAAO,IAAIge,GAAW,EAAK,IAQpDA,GAAW/d,IAAMF,OAAOC,OAAO,IAAIge,GAAW,EAAK,IAQnDA,GAAWxgB,OAASuC,OAAOC,OAAO,IAAIge,GAAW,EAAK,IAQtDA,GAAWtgB,OAASqC,OAAOC,OAAO,IAAIge,GAAW,EAAK,IAQtDA,GAAW9d,UAAUzG,MAAQ,SAAUb,GACrC,OAAOolB,GAAWvkB,MAAMjB,KAAMI,EAChC,EASAolB,GAAW9d,UAAUpC,OAAS,SAAUnC,GACtC,OAAOqiB,GAAWlgB,OAAOtF,KAAMmD,EACjC,EAYAqiB,GAAW9d,UAAUjC,cAAgB,SACnCtC,EACAuC,EACAC,GAEA,OAAO6f,GAAW/f,cAChBzF,KACAmD,EACAuC,EACAC,EAEJ,EAOA6f,GAAW9d,UAAUC,SAAW,WAC9B,MAAO,IAAM3H,KAAKH,EAAI,KAAOG,KAAKF,EAAI,GACxC,ECxvBA+lB,GAAQzkB,aAAe,EAWvBykB,GAAQxkB,KAAO,SAAUC,EAAOC,EAAOC,GAarC,OAXAnB,EAAAA,MAAMC,OAAOC,OAAO,QAASe,GAC7BjB,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAE5CD,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAC/BC,EAAMC,KAAmBF,EAAM,GAExBC,CACT,EAUAskB,GAAQpkB,OAAS,SAAUF,EAAOC,EAAepB,GAe/C,OAbAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAIylB,IAGfzlB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KAClBpB,EAAO,GAAKmB,EAAMC,KACXpB,CACT,EASAylB,GAAQ5kB,MAAQ,SAAUqP,EAAQlQ,GAChC,GAAKI,EAAAA,QAAQ8P,GAGb,OAAK9P,EAAAA,QAAQJ,IAGbA,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACZlQ,GANE,IAAIylB,GAAQvV,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAO/D,EAsBAuV,GAAQxjB,UAAY,SAAUd,EAAOC,EAAepB,GAelD,OAbAC,EAAAA,MAAMG,QAAQ,QAASe,GAGvBC,EAAgBvB,EAAYA,aAACuB,EAAe,GAEvChB,EAAAA,QAAQJ,KACXA,EAAS,IAAIylB,IAGfzlB,EAAO,GAAKmB,EAAMC,GAClBpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAClCpB,EAAO,GAAKmB,EAAMC,EAAgB,GAC3BpB,CACT,EASAylB,GAAQtV,qBAAuB,SAAUC,EAAQpQ,GAK/C,OAHAC,EAAAA,MAAMG,QAAQ,SAAUgQ,GAGjBqV,GAAQ5kB,MAAMuP,EAAQpQ,EAC/B,EAUAylB,GAAQpV,kBAAoB,SAAUD,EAAQpQ,GAK5C,OAHAC,EAAAA,MAAMG,QAAQ,SAAUgQ,GAGnBhQ,EAAAA,QAAQJ,IAGbA,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACnBpQ,EAAO,GAAKoQ,EAAO,GACZpQ,GANE,IAAIylB,GAAQrV,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAO/D,EAeAqV,GAAQzT,UAAY,SAAUC,EAAOjS,GAKnC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,QAAS8R,GAGxB7R,EAAAA,QAAQJ,IAIbA,EAAO,GAAKiS,EAAMxS,EAClBO,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiS,EAAMvS,EACXM,GAPE,IAAIylB,GAAQxT,EAAMxS,EAAG,EAAK,EAAKwS,EAAMvS,EAQhD,EAeA+lB,GAAQvT,iBAAmB,SAAUD,EAAOjS,GAK1C,OAHAC,EAAAA,MAAMC,OAAO4B,OAAO,QAASmQ,GAGxB7R,EAAAA,QAAQJ,IAIbA,EAAO,GAAKiS,EACZjS,EAAO,GAAK,EACZA,EAAO,GAAK,EACZA,EAAO,GAAKiS,EACLjS,GAPE,IAAIylB,GAAQxT,EAAO,EAAK,EAAKA,EAQxC,EAeAwT,GAAQC,aAAe,SAAUpT,EAAOtS,GAEtCC,EAAAA,MAAMC,OAAO4B,OAAO,QAASwQ,GAG7B,MAAMC,EAAW9R,KAAKE,IAAI2R,GACpBE,EAAW/R,KAAKC,IAAI4R,GAE1B,OAAKlS,EAAAA,QAAQJ,IAGbA,EAAO,GAAKuS,EACZvS,EAAO,GAAKwS,EACZxS,EAAO,IAAMwS,EACbxS,EAAO,GAAKuS,EACLvS,GANE,IAAIylB,GAAQlT,GAAWC,EAAUA,EAAUD,EAOtD,EAUAkT,GAAQ9S,QAAU,SAAUzC,EAAQlQ,GAKlC,OAHAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAGzB9P,EAAAA,QAAQJ,IAGbA,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACnBlQ,EAAO,GAAKkQ,EAAO,GACZlQ,GANE,CAACkQ,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAOpD,EAkBAuV,GAAQ7S,gBAAkB,SAAUC,EAAQC,GAS1C,OAPA7S,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,MAAO+Q,EAAK,GACpD7S,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,MAAOD,EAAK,GAEjD7S,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,SAAU8Q,EAAQ,GAC1D5S,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,SAAUF,EAAQ,GAGvC,EAATA,EAAaC,CACtB,EAYA2S,GAAQzS,UAAY,SAAU9C,EAAQlO,EAAOhC,GAE3CC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAE9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GAErD/B,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMiT,EAAqB,EAARjR,EACbvC,EAAIyQ,EAAO+C,GACXvT,EAAIwQ,EAAO+C,EAAa,GAI9B,OAFAjT,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACJM,CACT,EAaAylB,GAAQvS,UAAY,SAAUhD,EAAQlO,EAAOlB,EAAWd,GAEtDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAE9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GAErD/B,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAI9B,MAAMiT,EAAqB,EAARjR,EAGnB,OAJAhC,EAASylB,GAAQ5kB,MAAMqP,EAAQlQ,IAExBiT,GAAcnS,EAAUrB,EAC/BO,EAAOiT,EAAa,GAAKnS,EAAUpB,EAC5BM,CACT,EAYAylB,GAAQtS,OAAS,SAAUjD,EAAQlO,EAAOhC,GAExCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAE9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GAErD/B,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMP,EAAIyQ,EAAOlO,GACXtC,EAAIwQ,EAAOlO,EAAQ,GAIzB,OAFAhC,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACJM,CACT,EAaAylB,GAAQrS,OAAS,SAAUlD,EAAQlO,EAAOlB,EAAWd,GAcnD,OAZAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAE9BjQ,EAAKA,MAACC,OAAO4B,OAAOC,oBAAoB,QAASC,EAAO,GACxD/B,EAAKA,MAACC,OAAO4B,OAAOiR,iBAAiB,QAAS/Q,EAAO,GAErD/B,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,IAG9BA,EAASylB,GAAQ5kB,MAAMqP,EAAQlQ,IACxBgC,GAASlB,EAAUrB,EAC1BO,EAAOgC,EAAQ,GAAKlB,EAAUpB,EACvBM,CACT,EAEA,MAAMqT,GAAgB,IAAI+R,GAS1BK,GAAQnS,SAAW,SAAUpD,EAAQlQ,GAYnC,OAVAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAOP,EAAI2lB,GAAW7kB,UACpB6kB,GAAWxkB,aAAasP,EAAO,GAAIA,EAAO,GAAImD,KAEhDrT,EAAON,EAAI0lB,GAAW7kB,UACpB6kB,GAAWxkB,aAAasP,EAAO,GAAIA,EAAO,GAAImD,KAEzCrT,CACT,EAEA,MAAMuT,GAAe,IAAI6R,GASzBK,GAAQjS,gBAAkB,SAAUtD,GAElC,OADAuV,GAAQnS,SAASpD,EAAQqD,IAClB6R,GAAWljB,iBAAiBqR,GACrC,EAUAkS,GAAQhS,SAAW,SAAU3Q,EAAMC,EAAO/C,GAExCC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoO,EAActL,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACnDsL,EAAcvL,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACnDwL,EAAczL,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GACnDyL,EAAc1L,EAAK,GAAKC,EAAM,GAAKD,EAAK,GAAKC,EAAM,GAMzD,OAJA/C,EAAO,GAAKoO,EACZpO,EAAO,GAAKuO,EACZvO,EAAO,GAAKqO,EACZrO,EAAO,GAAKwO,EACLxO,CACT,EAUAylB,GAAQliB,IAAM,SAAUT,EAAMC,EAAO/C,GAWnC,OATAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GACrB/C,CACT,EAUAylB,GAAQziB,SAAW,SAAUF,EAAMC,EAAO/C,GAWxC,OATAC,EAAAA,MAAMC,OAAOC,OAAO,OAAQ2C,GAC5B7C,EAAAA,MAAMC,OAAOC,OAAO,QAAS4C,GAC7B9C,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GAC5B/C,EAAO,GAAK8C,EAAK,GAAKC,EAAM,GACrB/C,CACT,EAUAylB,GAAQ/R,iBAAmB,SAAUxD,EAAQpP,EAAWd,GAEtDC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,YAAaW,GACjCb,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMP,EAAIyQ,EAAO,GAAKpP,EAAUrB,EAAIyQ,EAAO,GAAKpP,EAAUpB,EACpDA,EAAIwQ,EAAO,GAAKpP,EAAUrB,EAAIyQ,EAAO,GAAKpP,EAAUpB,EAI1D,OAFAM,EAAOP,EAAIA,EACXO,EAAON,EAAIA,EACJM,CACT,EAUAylB,GAAQjiB,iBAAmB,SAAU0M,EAAQzM,EAAQzD,GAWnD,OATAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAO4B,OAAO,SAAU2B,GAC9BxD,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACxBzD,EAAO,GAAKkQ,EAAO,GAAKzM,EACjBzD,CACT,EAkBAylB,GAAQ3R,gBAAkB,SAAU5D,EAAQ+B,EAAOjS,GAWjD,OATAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,QAAS8R,GAC7BhS,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMxS,EAC9BO,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMxS,EAC9BO,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMvS,EAC9BM,EAAO,GAAKkQ,EAAO,GAAK+B,EAAMvS,EACvBM,CACT,EASAylB,GAAQ9hB,OAAS,SAAUuM,EAAQlQ,GAUjC,OARAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACpBlQ,EAAO,IAAMkQ,EAAO,GACblQ,CACT,EASAylB,GAAQ1R,UAAY,SAAU7D,EAAQlQ,GAEpCC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9B,MAAMoO,EAAc8B,EAAO,GACrB3B,EAAc2B,EAAO,GACrB7B,EAAc6B,EAAO,GACrB1B,EAAc0B,EAAO,GAM3B,OAJAlQ,EAAO,GAAKoO,EACZpO,EAAO,GAAKuO,EACZvO,EAAO,GAAKqO,EACZrO,EAAO,GAAKwO,EACLxO,CACT,EASAylB,GAAQ7hB,IAAM,SAAUsM,EAAQlQ,GAW9B,OATAC,EAAAA,MAAMC,OAAOC,OAAO,SAAU+P,GAC9BjQ,EAAAA,MAAMC,OAAOC,OAAO,SAAUH,GAG9BA,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAC5BlQ,EAAO,GAAKS,KAAKmD,IAAIsM,EAAO,IAErBlQ,CACT,EAUAylB,GAAQvgB,OAAS,SAAUpC,EAAMC,GAC/B,OACED,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,IAClBD,EAAK,KAAOC,EAAM,EAExB,EAKA0iB,GAAQtgB,YAAc,SAAU+K,EAAQ/O,EAAOiE,GAC7C,OACE8K,EAAO,KAAO/O,EAAMiE,IACpB8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,IAC7B8K,EAAO,KAAO/O,EAAMiE,EAAS,EAEjC,EAYAqgB,GAAQpgB,cAAgB,SAAUvC,EAAMC,EAAO0I,GAE7C,OADAA,EAAU5L,EAAYA,aAAC4L,EAAS,GAE9B3I,IAASC,GACR3C,EAAAA,QAAQ0C,IACP1C,EAAAA,QAAQ2C,IACRtC,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,GAChChL,KAAKmD,IAAId,EAAK,GAAKC,EAAM,KAAO0I,CAEtC,EAQAga,GAAQzQ,SAAW7N,OAAOC,OAAO,IAAIqe,GAAQ,EAAK,EAAK,EAAK,IAQ5DA,GAAQve,KAAOC,OAAOC,OAAO,IAAIqe,GAAQ,EAAK,EAAK,EAAK,IAYxDA,GAAQnP,YAAc,EAYtBmP,GAAQlP,YAAc,EAYtBkP,GAAQhP,YAAc,EAYtBgP,GAAQ/O,YAAc,EAEtBvP,OAAOuE,iBAAiB+Z,GAAQne,UAAW,CAOzC/F,OAAQ,CACNqK,IAAK,WACH,OAAO6Z,GAAQzkB,YAChB,KAULykB,GAAQne,UAAUzG,MAAQ,SAAUb,GAClC,OAAOylB,GAAQ5kB,MAAMjB,KAAMI,EAC7B,EASAylB,GAAQne,UAAUpC,OAAS,SAAUnC,GACnC,OAAO0iB,GAAQvgB,OAAOtF,KAAMmD,EAC9B,EAWA0iB,GAAQne,UAAUjC,cAAgB,SAAUtC,EAAO0I,GACjD,OAAOga,GAAQpgB,cAAczF,KAAMmD,EAAO0I,EAC5C,EAQAga,GAAQne,UAAUC,SAAW,WAC3B,MACE,IACA3H,KAAK,GACL,KACAA,KAAK,GAHL,OAMAA,KAAK,GACL,KACAA,KAAK,GACL,GAEJ"}