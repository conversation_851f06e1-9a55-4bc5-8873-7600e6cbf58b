/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { GanttConnectLine, GanttDataType, GanttRow, ScrollBar } from './GanttRow';
import { GanttMode } from './gantt-utils.ts';
import { calculationTime } from '../utils/time-utils.ts';

export interface GanttGraphicOptions {
  row: {
    height: number; // 每行的高度
    gap: number; // 行间距
  };
  y?: number;
  maxTime: number;
  minTime: number;
  showStartTime: number;
  showEndTime: number;
  width: number; // 画布的宽度
  height: number; // 画布的高度
  interval: number;
  minInterval: number;
  mode: GanttMode; // 显示模式
  splitLine?: boolean; // 是否显示每行的分割线
  connectLine?: boolean; // 是否显示连接线
}
// 默认配置
const defaultOptions: Partial<GanttGraphicOptions> = {
  row: {
    height: 25,
    gap: 4
  },
  y: 0,
  minInterval: 1000,
  splitLine: true,
  connectLine: true
};

export class GanttGraphic {
  graphicList: Map<string, GanttRow>;
  graphicIndexList: Map<number, GanttRow>;
  connectLineList: Map<string, GanttConnectLine>;
  dom: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  options: GanttGraphicOptions;
  _scale: number;
  scrollBar: ScrollBar | undefined;
  constructor(dom: HTMLCanvasElement, options: GanttGraphicOptions) {
    this.graphicList = new Map();
    this.graphicIndexList = new Map();
    this.connectLineList = new Map();
    this._scale = 1;
    this.dom = dom;
    this.ctx = dom.getContext('2d') as CanvasRenderingContext2D;
    this.options = Object.assign({}, defaultOptions, options);
    this.options.showStartTime = this.options.minTime;
    this.options.showEndTime = this.options.maxTime as number;
  }
  setData(data: GanttDataType[]) {
    const connectList: Map<string, string[]> = new Map();
    const graphicList = new Map<string, GanttRow>(); //**
    data.forEach((item, index) => {
      this.showStartTime = Math.min(this.showStartTime, item.startTime);
      this.showEndTime = Math.max(this.showEndTime, item.endTime);
      const row = new GanttRow(item, { ctx: this.ctx, index }, this);
      if (this.options.connectLine && item?.target?.length) {
        connectList.set(item.id, item.target);
      }
      // this.add(row);
      graphicList.set(row.id, row); //**
      this.graphicIndexList.set(index, row);
    });
    this.graphicList = graphicList; //**修改为赋值

    if (this.options.connectLine) {
      Array.from(connectList.entries()).forEach(([key, value]) => {
        const source = this.graphicList.get(key)!;
        const targets = value.map((item) => this.graphicList.get(item)!);
        this.connectLineList.set(key, new GanttConnectLine(source, targets));
      });
    }
    this.calcInterval();
    if (!this.scrollBar) {
      this.scrollBar = new ScrollBar(data, { ctx: this.ctx, graphic: this }, this.options);
    }
    this.scrollBar.scrollTo(0);

    this.render();
  }
  set showStartTime(time: number) {
    if (time < this.options.minTime) {
      this.options.showStartTime = this.options.minTime;
      return;
    }
    this.options.showStartTime = time;
  }

  get showStartTime() {
    return this.options.showStartTime;
  }

  set showEndTime(time: number) {
    if (time > this.options.maxTime) {
      this.options.showEndTime = this.options.maxTime;
      return;
    }
    this.options.showEndTime = time;
  }

  get showEndTime() {
    return this.options.showEndTime;
  }

  calcInterval() {
    const interval = (this.showEndTime - this.showStartTime) / this.options.width;
    // console.log('GanttGraphic.ts, 101 ->', interval);
    if (interval < this.options.minInterval) {
      this.options.interval = this.options.minInterval;
    } else {
      this.options.interval = interval;
    }
    this.render();
  }
  set interval(value: number) {
    if (this.options.interval <= this.options.minInterval && value < 0) return;
    this.options.interval = value;
    this.render();
  }
  get interval() {
    return this.options.interval;
  }
  set scale(scale: number) {
    this._scale = scale;
    this.ctx.scale(scale, scale);
    this.render();
  }
  get scale() {
    return this._scale;
  }
  add(row: GanttRow) {
    if (this.graphicList.has(row.id)) {
      console.warn(`${row.id} 已经存在`);
      return;
    }
    this.graphicList.set(row.id, row);
  }
  clearRect() {
    this.ctx.clearRect(0, 0, this.options.width + this._scale, this.options.height * this._scale);
  }
  findGraphic(x: number, y: number) {
    const rowIndex = Math.floor(y / (this.options.row.height + this.options.row.gap / 2));
    const row = this.graphicIndexList.get(rowIndex);
    if (!row) return;
    const result = row.rangeCollection.find((item) => item.isPointInPath(x, y));
    if (!result) return;
    return result;
  }

  moveShowTimeArea(deltaY: number) {
    if ((deltaY <= 0 && this.showStartTime == this.options.minTime) || (deltaY >= 0 && this.showEndTime == this.options.maxTime)) return;

    const { startTime, endTime } = calculationTime({
      minTime: this.options.minTime,
      maxTime: this.options.maxTime,
      currentStartTime: this.showStartTime,
      currentEndTime: this.showEndTime,
      deltaY
    });
    this.showStartTime = startTime;
    this.showEndTime = endTime;
  }

  pick(x: number, y: number) {
    const values = [...this.graphicList.values()];

    return values.filter((row) => row.pick(x, y)).map((row) => row.data);
  }

  render() {
    this.clearRect();
    this.graphicList.forEach((item) => item.render());
    if (this.options.connectLine) {
      this.connectLineList.forEach((item) => item.render(this.ctx));
    }
    this.scrollBar?.render();
  }
}
