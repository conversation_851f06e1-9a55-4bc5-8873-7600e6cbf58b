/**
 * default 普通模式
 * flat 平铺模式
 * both 两种模式都支持 同时显示flat数据和普通模式的数据
 */

export interface GanttDataBase<T = any> {
  id: string;
  startTime: number;
  endTime: number;
  label: string;
  flatData?: GanttDataBase<T>[];
  target?: string[];
  children?: GanttDataBase<T>[];
  top?: number;
  level?: number;
}

export type GanttDataType<T = any> = GanttDataBase<T> & T;

export type GanttLeftTreeDataType<T> = GanttDataBase<T> & { top?: number; level: number };

export type GanttMode = 'default' | 'flat' | 'both';

function generateGanttMode(modeCondition: GanttMode) {
  return function (mode: GanttMode) {
    return mode == modeCondition;
  };
}
export const isFlatMode = generateGanttMode('flat');

export const isBothMode = generateGanttMode('both');

export const enum GanttGraphicType {
  range,
  rangePoint,
  point,
  line,
  splintLine,
  scrollBar
}

export const enum TimeUnit {
  seconds = 1000,
  minutes = 1000 * 60,
  hours = 1000 * 60 * 60,
  days = 1000 * 60 * 60 * 24,
  months = 1000 * 60 * 60 * 24 * 30,
  years = 1000 * 60 * 60 * 24 * 365
}

export interface CoordinatesType {
  x: number;
  y: number;
}

export interface SelectNodeAreaType {
  id: string;
  data: GanttDataBase<any>;
  x: number;
  y: number;
}

// export function isRangeGraphicType(graphic: any): graphic is GanttRange {
//   return graphic.type === GanttGraphicType.range;
// }

// export function isRangePointGraphicType(graphic: any): graphic is GanttRangePoint {
//   return graphic.type === GanttGraphicType.rangePoint;
// }
