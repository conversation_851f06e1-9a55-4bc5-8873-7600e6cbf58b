/*
 * @Author: 宋计民
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
 */
import SimGanttCom from './src/main.vue';
import SimGanttTimeline from './next-src/gantt-timeline.vue';
import SimGanttTimelineNew from './next-src/gantt-timeline-new.vue';
import SimGanttContent from './next-src/gantt.vue';
import { withInstall } from 'isim-ui';

const SimGantt = withInstall(SimGanttCom, {
  SimGanttTimeline,
  SimGanttTimelineNew,
  SimGanttContent
});

export { SimGantt, SimGanttTimeline, SimGanttTimelineNew, SimGanttContent };
export type { GanttDataType } from './next-src/graphic/index.ts';
