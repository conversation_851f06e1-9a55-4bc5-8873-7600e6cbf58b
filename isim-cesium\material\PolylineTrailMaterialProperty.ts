/**
 * @Author: 成超
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
// @ts-nocheck
import Cesium, { Color, createPropertyDescriptor, defaultValue, defined, Event, MaterialProperty } from 'cesium';
import { canvasToURL } from './utils/canvasToURL.ts';
import PolylineTrailSource from './Sharder/PolylineTrailSource.glsl?raw';
import PolylineTrailSourceTwo from './Sharder/PolylineTrailSourceTwo.glsl?raw';

export interface PolylineTrailMaterialOptionsType {
  color?: Color;
  width?: number;
  animation?: boolean;
  duration?: number;
  trailImage?: string;
}

Cesium.Material.PolylineTrailType = 'PolylineTrail';
Cesium.Material.PolylineTrailImage = canvasToURL();
// 根据指定宽度的屏幕坐标渲染纹理的数量
Cesium.Material.PolylineTrailSource = PolylineTrailSource;
// 在连线高度始终为0时，可以使用版本2的glsl，
// 版本2解决了版本1切换角度时，纹理会动的问题，以及因为计算折线角度造成的纹理会变形的问题
// 因为连线2没有将折线角度纳入计算范围，在高度大于0的情况下会导致展示有问题
Cesium.Material.PolylineTrailSourceTwo = PolylineTrailSourceTwo;
const uniforms = {
  // 颜色
  color: new Cesium.Color(1.0, 0.0, 0.0, 1.0),
  // 纹理
  image: Cesium.Material.PolylineTrailImage,
  // 宽度
  width: 50.0,
  // 是否开启动态纹理
  animation: false,
  // 控制时间流速
  duration: 1
};

export interface PolylineTrailMaterialProperty extends MaterialProperty {
  new (options?: PolylineTrailMaterialOptionsType): PolylineTrailMaterialProperty;
}
export const PolylineTrailMaterialProperty: PolylineTrailMaterialProperty = function (options: PolylineTrailMaterialOptionsType) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();

  this.color = options.color ?? uniforms.color;
  // 控制每一段的宽度（像素为基准）
  this.width = options.width ?? uniforms.width;
  // 是否开启动态纹理
  this.animation = options.animation ?? uniforms.animation;
  // 控制时间流速
  this.duration = options.duration ?? uniforms.duration;
  // 纹理
  this.trailImage = options?.trailImage ?? canvasToURL();
};

// Cesium.defineProperties(PolylineTrailMaterialProperty.prototype, {
Object.defineProperties(PolylineTrailMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return false;
    }
  },

  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },

  color: createPropertyDescriptor('color'),
  trailImage: createPropertyDescriptor('trailImage')
});

PolylineTrailMaterialProperty.prototype.getType = function () {
  return 'PolylineTrail';
};

PolylineTrailMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  // console.log('result', result);
  result.color = Cesium.Property.getValueOrClonedDefault(this.color, time, Cesium.Color.WHITE, result.color);

  // result.image = this.trailImage || Cesium.Material.PolylineTrailImage;
  result.image = Cesium.Property.getValueOrClonedDefault(this.trailImage, time, Cesium.Color.WHITE, result.trailImage);

  result.width = this.width;

  result.animation = this.animation;

  result.duration = this.duration;

  return result;
};

PolylineTrailMaterialProperty.prototype.equals = function (other) {
  // return (
  //   this === other ||
  //   (other instanceof PolylineTrailMaterialProperty && Cesium.Property.equals(this.color, other.color) && this.trailImage == other.trailImage)
  // );
  return this === other;
};

Cesium.Material._materialCache.addMaterial(Cesium.Material.PolylineTrailType, {
  fabric: {
    type: Cesium.Material.PolylineTrailType,

    uniforms,

    source: Cesium.Material.PolylineTrailSource
    // source: Cesium.Material.PolylineTrailSourceTwo
  },

  translucent: function () {
    return true;
  }
});
// Cesium.PolylineTrailMaterialProperty = PolylineTrailMaterialProperty;
