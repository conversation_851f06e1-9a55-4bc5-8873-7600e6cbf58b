import { onMounted, ref } from 'vue';

export function useRightContentSize() {
  const rightTag = ref();
  const rightWidth = ref();
  const timeRange = ref(6);

  const updateTimeRangeUp = () => {
    timeRange.value += 1000;
  };
  const updateTimeRangeDown = () => {
    timeRange.value -= 1000;
  };

  onMounted(() => {
    rightWidth.value = (rightTag.value as HTMLElement).clientWidth;
    timeRange.value = Math.floor(rightWidth.value / 200);
  });

  return {
    rightTag,
    rightWidth,
    timeRange,

    updateTimeRangeUp,
    updateTimeRangeDown
  };
}
