/**
 * @Author: 宋计民
 * @Date: 2023/10/16 13:38
 * @Version: 1.0
 * @Content: PolylineFlowMaterial.ts
 */
// @ts-nocheck
import { Color, defaultValue, Material, Event, Property, createPropertyDescriptor, defined, MaterialProperty } from 'cesium';
import ShaderSource from './Sharder/PolylineFlowMaterial.glsl?raw';

const MaterialType = 'CustomPolylineFlow';

const PolylineFlowDefaultValue = {
  color: new Color(1.0, 0.0, 0.0, 0.7),
  speed: 45,
  percent: 0.03,
  gradient: 0.2,
  number: 5
};
export const PolylineFlowMaterial = new Material({
  fabric: {
    type: MaterialType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 45,
      percent: 0.03,
      gradient: 0.2,
      number: 5
    },
    source: ShaderSource
  },
  translucent: function () {
    return true;
  }
});

export interface PolylineFlowMaterialPropertyOptions {
  color?: Color;
  speed?: number;
  percent?: number;
  gradient?: number;
  number?: number;
}

export interface PolylineFlowMaterialProperty extends MaterialProperty {
  new (options?: PolylineFlowMaterialPropertyOptions): PolylineFlowMaterialProperty;
}
export const PolylineFlowMaterialProperty: PolylineFlowMaterialProperty = function (options?: PolylineFlowMaterialPropertyOptions) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;
  this._percent = undefined;
  this._percentSubscription = undefined;
  this._gradient = undefined;
  this._gradientSubscription = undefined;
  this._number = undefined;
  this._numberSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
  this.percent = options.percent;
  this.gradient = options.gradient;
  this.number = options.number;
};

Object.defineProperties(PolylineFlowMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._color) && Property.isConstant(this._glow);
    }
  },

  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },

  color: createPropertyDescriptor('color'),

  /**
   * Gets or sets the numeric Property specifying the strength of the glow, as a percentage of the total line width (less than 1.0).
   * @memberof PolylineGlowMaterialProperty.prototype
   * @type {Property|undefined}
   */
  speed: createPropertyDescriptor('speed'),

  percent: createPropertyDescriptor('percent'),

  gradient: createPropertyDescriptor('gradient'),

  number: createPropertyDescriptor('number')
});

/**
 * Gets the {@link Material} type at the provided time.
 *
 * @param {JulianDate} _time The time for which to retrieve the type.
 * @returns {String} The type of material.
 */
PolylineFlowMaterialProperty.prototype.getType = function (_time) {
  return MaterialType;
};

PolylineFlowMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  result.color = Property.getValueOrClonedDefault(this._color, time, PolylineFlowDefaultValue.color, result.color);
  result.speed = Property.getValueOrDefault(this._speed, time, PolylineFlowDefaultValue.speed, result.speed);
  result.percent = Property.getValueOrDefault(this._percent, time, PolylineFlowDefaultValue.percent, result.percent);
  result.gradient = Property.getValueOrDefault(this._gradient, time, PolylineFlowDefaultValue.gradient, result.gradient);
  result.number = Property.getValueOrDefault(this._number, time, PolylineFlowDefaultValue.number, result.number);
  return result;
};

PolylineFlowMaterialProperty.prototype.equals = function (other) {
  return (
    this === other ||
    (other instanceof PolylineFlowMaterialProperty &&
      Property.equals(this._color, other._color) &&
      Property.equals(this._speed, other._speed) &&
      Property.equals(this._percent, other._percent) &&
      Property.equals(this._gradient, other._gradient) &&
      Property.equals(this._number, other._number))
  );
};
