/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/28 10:01:10
 * @description method
 * @version 1.0
 * */

import { Cartesian3, Cartographic, defined, Dev<PERSON>perError, HeightReference, Math as CesiumMath, Ray } from 'cesium';

const scratchPosition = new Cartesian3();
const scratchCartographic = new Cartographic();

function calculateFovPositions(primitive, cartesian, heightOffset = 0) {
  const { _radius, _angle, _azimuth } = primitive;

  if (!_radius || !_angle || !cartesian) {
    return;
  }
  const cartographic = Cartographic.fromCartesian(cartesian);
  cartographic.height += heightOffset;
  const center = turf.point([CesiumMath.toDegrees(cartographic.longitude), CesiumMath.toDegrees(cartographic.latitude)]);
  const radius = _radius / 1000;
  const bearing1 = _azimuth + _angle / 2;
  const bearing2 = _azimuth - _angle / 2;
  const sector = turf.sector(center, radius, Math.min(bearing1, bearing2), Math.max(bearing1, bearing2), { steps: 32 });
  return getIntersectCartesianArray(primitive, sector, cartographic.height, _radius);
}

const cartScratch = new Cartesian3();

/**
 * @description calculate intersect point
 */
function getIntersectCartesianArray(primitive, sector, height, radius) {
  const coordinates = sector?.geometry?.coordinates[0];
  const scene = primitive._scene;

  const positions = [];

  if (coordinates) {
    const center = Cartesian3.fromDegrees(...coordinates[0], height);
    positions.push(center);
    let ray, cart, result, resultObj, distance;
    for (let i = 1; i < coordinates.length - 1; i++) {
      cart = Cartesian3.fromDegrees(...coordinates[i], height);
      Cartesian3.subtract(cart, center, cartScratch);
      ray = new Ray(center, cartScratch);
      resultObj = scene.pickFromRay(ray, []);
      result = resultObj?.position;
      if (result && resultObj?.object?.id !== primitive._id) {
        distance = Cartesian3.distance(center, result);
        if (distance > 0.3 && distance <= radius) {
          positions.push(result);
        } else {
          positions.push(cart);
        }
      } else {
        positions.push(cart);
      }
    }
  }

  return positions;
}

function getUpdateHeightCallback(primitive, ellipsoid, cartoPosition) {
  return function (clampedPosition) {
    if (primitive._heightOffset) {
      const clampedCart = ellipsoid.cartesianToCartographic(clampedPosition, scratchCartographic);
      clampedCart.height += primitive._heightOffset;
      if (primitive._heightReference === HeightReference.RELATIVE_TO_GROUND) {
        clampedCart.height += cartoPosition.height;
      }
      ellipsoid.cartographicToCartesian(clampedCart, clampedPosition);
    }

    primitive._positions = calculateFovPositions(primitive, clampedPosition);
    primitive._heightChanged = true;
  };
}

/**
 * FovPrimitive get it's Clamping Position
 * @param {*} primitive
 * @return {*}
 * @todo 场景未加载完成时，fovFeature 开启后 会造成 updateClamping 出现报错 (待测试修改)
 */
function updateClamping(primitive) {
  if (defined(primitive._removeUpdateHeightCallback)) {
    primitive._removeUpdateHeightCallback();
    primitive._removeUpdateHeightCallback = undefined;
  }

  const position = primitive._position;

  const scene = primitive._scene;

  if (!defined(scene?.globe)) {
    console.warn('This intersect method of the FovFeature need scene and globe.');
    return;
  }

  if (primitive._heightReference === HeightReference.NONE) {
    primitive._positions = calculateFovPositions(primitive, position, primitive._heightOffset);
    return;
  }

  const globe = scene.globe;
  const ellipsoid = globe.ellipsoid;

  const cartoPosition = ellipsoid.cartesianToCartographic(position);
  const surface = globe._surface;

  primitive._removeUpdateHeightCallback = surface.updateHeight(cartoPosition, getUpdateHeightCallback(primitive, ellipsoid, cartoPosition));

  const height = globe.getHeight(cartoPosition);
  if (defined(height)) {
    const cb = getUpdateHeightCallback(primitive, ellipsoid, cartoPosition);
    Cartographic.clone(cartoPosition, scratchCartographic);
    scratchCartographic.height = height;
    ellipsoid.cartographicToCartesian(scratchCartographic, scratchPosition);
    cb(scratchPosition);
  }
}

function getCartesianArray(sector, height = 0) {
  const coordinates = sector?.geometry?.coordinates[0];
  if (coordinates) {
    return coordinates.reduce((pre, cur) => {
      if (height) {
        pre.push(Cartesian3.fromDegrees(...cur, height));
      } else {
        pre.push(Cartesian3.fromDegrees(...cur));
      }
      return pre;
    }, []);
  }
}

/**
 * @description SectorDetectionPrimitive Positions Calculate Method
 * @param {*} radius
 * @param {*} position
 * @param {*} heading
 * @param {*} halfAngle
 * @param slices
 */
function getSectorDetectionPositions(position, radius, heading, halfAngle, slices) {
  const cartographic = Cartographic.fromCartesian(position);
  const center = turf.point([CesiumMath.toDegrees(cartographic.longitude), CesiumMath.toDegrees(cartographic.latitude)]);
  const bearing1 = heading + halfAngle;
  const bearing2 = heading - halfAngle;
  const sector = turf.sector(center, radius / 1000, Math.min(bearing1, bearing2), Math.max(bearing1, bearing2), { steps: slices });
  return getCartesianArray(sector);
}

export { updateClamping, getSectorDetectionPositions };
