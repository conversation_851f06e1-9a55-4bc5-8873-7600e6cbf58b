import { GanttGraphicAbstract } from './GanttGraphicAbstract';
import type { GanttDataBase } from './GanttType';
import type { GanttRow } from './GanttRow';
import { GanttGraphicBase } from './GanttGraphicBase';

type GanttRangeGraphicType = 'point' | 'range';
export class GanttRangeGraphic<T extends GanttDataBase = GanttDataBase> extends GanttGraphicBase<T> implements GanttGraphicAbstract {
  id: string;
  data: T;
  type: GanttRangeGraphicType;
  color: string = '#18576a';
  selectedColor: string = '#79b4f1';
  onLineColor: string = '#000';
  constructor(data: T, row: GanttRow, type?: GanttRangeGraphicType) {
    super(data, row);
    this.id = data.id;
    this.data = data;
    this.type = type ?? 'range';
  }
  // 当前节点所需展示的颜色
  get currentColor() {
    const isSelected = this.gantt.selectedRowCollection.has(this.id);
    const isOnline = this.gantt.online.isOnlineById(this.id);
    return isOnline ? this.onLineColor : isSelected ? this.selectedColor : this.color;
  }

  rangeRender() {
    const ctx = this.row.gantt.ctx;
    ctx.beginPath();
    ctx.fillStyle = this.currentColor;
    this.calcPos();
    ctx.beginPath();
    ctx.roundRect(this.x, this.y + this.row.rowGap / 2, this.width, this.row.rowHeight, 3);
    ctx.fill();
    ctx.fillStyle = '#fff';
    ctx.font = `${this.gantt.fontSize}px sans-serif`;
    ctx.textBaseline = 'middle';
    const label = this.gantt.rowFormatLabel(this.data);
    ctx.fillText(label, this.x + 5, this.y + this.row.rowHeight / 2 + this.row.rowGap / 2);
  }
  pointRender() {
    const ctx = this.row.gantt.ctx;
    this.calcPos();
    ctx.beginPath();
    ctx.fillStyle = '#fff';
    ctx.font = `${this.gantt.fontSize}px sans-serif`;
    ctx.arc(this.x, this.y + this.row.rowHeight / 2, 3, 0, 2 * Math.PI);
    ctx.fill();
    ctx.closePath();
  }
  rangePick(x: number, y: number) {
    return x >= this.x && x <= this.x + this.width && y >= this.y && y <= this.y + this.row.rowHeight;
  }
  pick(x: number, y: number) {
    if (this.type === 'range') {
      return this.rangePick(x, y);
    }
  }
  render() {
    this.calcPos();
    // 优化显示懒加载
    if (!this.isShow) return;

    if (this.type === 'range') {
      this.rangeRender();
      return;
    }
    this.pointRender();
  }
}
