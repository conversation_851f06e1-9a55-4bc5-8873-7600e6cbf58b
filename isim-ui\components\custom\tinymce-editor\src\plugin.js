import tinymce from 'tinymce/tinymce';
tinymce.PluginManager.add('kityformula-editor', function (editor, url) {
  const baseURL = '/tinymce/kityformula-editor/kityFormula.html';

  editor.on('dblclick', function () {
    console.log(1111);
    const sel = editor.selection.getContent();
    const path = /\<img(.*?)src="data:image\/png;base64,[A-Za-z0-9+/=]*"(.*?)data-latex="(.*?)" \/>/g;
    const path2 = /data-latex="(.*?)"/g;

    // if(sel.search(path)==0){
    sel.replace(path2, function ($0, $1) {
      console.log($1);
      const param = encodeURIComponent($1);
      console.log(param);
      openDialog(param);
      return $0;
    });
    // };
  });

  var openDialog = function (param) {
    return editor.windowManager.openUrl({
      title: '插入公式',
      size: 'large',
      width: 785,
      height: 475,
      url: param ? baseURL + '?c=' + param : baseURL,
      buttons: [
        {
          type: 'cancel',
          text: 'Close'
        },
        {
          type: 'custom',
          text: 'Save',
          name: 'save',
          primary: true
        }
      ],
      onAction: function (api, details) {
        switch (details.name) {
          case 'save':
            api.sendMessage('save');
            break;
          default:
            break;
        }
      }
    });
  };

  editor.ui.registry.addButton('kityformula-editor', {
    text: '公式',
    tooltip: '插入公式',
    onAction: function () {
      openDialog();
    }
  });
  editor.ui.registry.addMenuItem('kityformula-editor', {
    text: '公式',
    onAction: function () {
      openDialog();
    }
  });
  return {
    getMetadata: function () {
      return {
        name: '公式',
        url: 'http://hgcserver.gitee.io'
      };
    }
  };
});
