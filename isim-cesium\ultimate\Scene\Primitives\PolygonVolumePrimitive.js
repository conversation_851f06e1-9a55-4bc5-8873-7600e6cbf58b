/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/03/16 10:34:41
 * @description PolygonPrimitive
 * @version 3.0
 * */
import {
  BoundingSphere,
  Cartesian3,
  ClassificationType,
  Color,
  ColorGeometryInstanceAttribute,
  defaultValue,
  defined,
  Ellipsoid,
  GeometryInstance,
  GeometryPipeline,
  GroundPrimitive,
  PerInstanceColorAppearance,
  PolygonGeometry,
  PolygonOutlineGeometry,
  Primitive
} from 'cesium';

import AbstractPrimitive from './AbstractPrimitive';

export default class PolygonVolumePrimitive extends AbstractPrimitive {
  constructor(options) {
    super(options);
    this._ellipsoid = defaultValue(options.ellipsoid, Ellipsoid.WGS84);
    this._color = Color.clone(defaultValue(options.color, Color.WHITE));
    this._depthFailShow = defaultValue(options.depthFailShow, false);
    this._depthFailColor = Color.clone(defaultValue(options.depthFailColor, this._color));
    this._extrudedHeight = options.extrudedHeight;
    this._height = defaultValue(options.height, 0);
    this._outline = defaultValue(options.outline, false);
    this._outlineColor = Color.clone(defaultValue(options.outlineColor, this._color));
    this._outlineColor.withAlpha(1.0);
    this._positions = defaultValue(options.positions, []);
    this._clampToGround = defaultValue(options.clampToGround, false);
    this._classificationType = defaultValue(options.classificationType, ClassificationType.BOTH);
    this._allowPicking = defaultValue(options.allowPicking, true);
    this._perPositionHeight = defaultValue(options.perPositionHeight, false);
    this._boundingSphere = new BoundingSphere();
    this._outlinePrimitive = undefined;
    this._update = true;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._positions.length < 3) {
      this._primitive = this._primitive && this._primitive.destroy();
      this._outlinePrimitive = this._outlinePrimitive && this._outlinePrimitive.destroy();
    } else {
      if (this._update) {
        this._update = !1;
        this._primitive = this._primitive && this._primitive.destroy();
        this._outlinePrimitive = this._outlinePrimitive && this._outlinePrimitive.destroy();
        this._clampToGround ? this._createGroundPolygon() : this._createPolygon();
        this._boundingSphere = BoundingSphere.fromPoints(this._positions, this._boundingSphere);
      }
      this._primitive.update(frameState);
      if (this._outline && this._outlinePrimitive) {
        this._outlinePrimitive.update(frameState);
      }
    }
  }

  _createPolygon() {
    const positions = this._positions.map((item) => {
      return Cartesian3.clone(item);
    });

    this._primitive = new Primitive({
      geometryInstances: new GeometryInstance({
        geometry: PolygonGeometry.fromPositions({
          positions,
          vertexFormat: PerInstanceColorAppearance.FLAT_VERTEX_FORMAT,
          ellipsoid: this._ellipsoid,
          extrudedHeight: this._extrudedHeight,
          height: this._perPositionHeight ? undefined : this._height,
          perPositionHeight: this._perPositionHeight
        }),
        attributes: {
          color: ColorGeometryInstanceAttribute.fromColor(this._color),
          depthFailColor: this._depthFailShow ? ColorGeometryInstanceAttribute.fromColor(this._depthFailColor) : undefined
        },
        id: this._id
      }),
      appearance: createAppearance(this._color),
      depthFailAppearance: this._depthFailShow ? createAppearance(this._depthFailColor) : undefined,
      allowPicking: this._allowPicking,
      asynchronous: !1
    });

    if (this._outline) {
      this._outlinePrimitive = new Primitive({
        geometryInstances: new GeometryInstance({
          geometry: PolygonOutlineGeometry.fromPositions({
            positions,
            vertexFormat: PerInstanceColorAppearance.FLAT_VERTEX_FORMAT,
            ellipsoid: this._ellipsoid,
            extrudedHeight: this._extrudedHeight,
            height: this._perPositionHeight ? undefined : this._height,
            perPositionHeight: this._perPositionHeight
          }),
          attributes: {
            color: ColorGeometryInstanceAttribute.fromColor(this._outlineColor)
          },
          id: this._id
        }),
        appearance: createAppearance(this._outlineColor),
        asynchronous: !1
      });
    }
  }

  _createGroundPolygon() {
    const positions = this._positions.map((item) => {
      return Cartesian3.clone(item);
    });

    this._primitive = new GroundPrimitive({
      geometryInstances: new GeometryInstance({
        geometry: PolygonGeometry.fromPositions({
          positions,
          vertexFormat: PerInstanceColorAppearance.FLAT_VERTEX_FORMAT,
          ellipsoid: this._ellipsoid
        }),
        attributes: { color: ColorGeometryInstanceAttribute.fromColor(this._color) },
        id: this._id
      }),
      appearance: createAppearance(this._color),
      allowPicking: this._allowPicking,
      asynchronous: !1,
      classificationType: this._classificationType
    });
  }

  destroy() {
    this._outlinePrimitive = this._outlinePrimitive && this._outlinePrimitive.destroy();
    super.destroy();
  }

  set extrudedHeight(value) {
    if (this._extrudedHeight === value) {
      return;
    }
    this._extrudedHeight = value;
    this._update = true;
  }

  set height(value) {
    if (this._height === value) {
      return;
    }
    this._height = value;
    this._update = true;
  }

  set outline(value) {
    if (this._outline === value) {
      return;
    }
    this._outline = value;
    this._update = true;
  }

  set depthFailShow(value) {
    if (this._depthFailShow === value) {
      return;
    }
    this._depthFailShow = value;
    this._update = true;
  }

  get positions() {
    return this._positions;
  }

  set positions(value) {
    this._positions = value;
    this._update = true;
  }

  get color() {
    return this._color;
  }

  set color(value) {
    if (!Color.equals(this._color, value)) {
      this._color = Color.clone(value, this._color);
      if (defined(this._primitive)) {
        let color = this._primitive.getGeometryInstanceAttributes(this._id).color; // TODO test
        color = value.toBytes(color);
        this._primitive.getGeometryInstanceAttributes(this._id).color = color;
      }
    }
  }

  get outlineColor() {
    return this._outlineColor;
  }

  set outlineColor(value) {
    if (!Color.equals(this._outlineColor, value)) {
      this._outlineColor = Color.clone(value, this._outlineColor);
      if (defined(this._outlinePrimitive)) {
        let color = this._outlinePrimitive.getGeometryInstanceAttributes(this._id).color;
        color = value.toBytes(color);
        this._outlinePrimitive.getGeometryInstanceAttributes(this._id).color = color;
      }
    }
  }

  get depthFailColor() {
    return this._depthFailColor;
  }

  set depthFailColor(value) {
    if (!Color.equals(this._depthFailColor, value)) {
      this._depthFailColor = Color.clone(value, this._depthFailColor);
      if (defined(this._primitive) && !this._clampToGround) {
        let color = this._primitive.getGeometryInstanceAttributes(this._id).depthFailColor;
        color = value.toBytes(color);
        this._primitive.getGeometryInstanceAttributes(this._id).depthFailColor = color;
      }
    }
  }

  get boundingVolume() {
    return this._boundingSphere;
  }

  get ellipsoid() {
    return this._ellipsoid;
  }

  get clampToGround() {
    return this._clampToGround;
  }

  get classificationType() {
    return this._classificationType;
  }

  set classificationType(value) {
    this._classificationType = value;
    this._update = !0;
  }

  get allowPicking() {
    return this._allowPicking;
  }
}

/**
 * @private
 */
function createAppearance(color) {
  return new PerInstanceColorAppearance({ flat: !0, closed: !1, translucent: color.alpha < 1 });
}
