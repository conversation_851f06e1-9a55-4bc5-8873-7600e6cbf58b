<!--
* <AUTHOR> zhang<PERSON>e
* @Date :  2023/4/11
* @Version : 1.0
* @Content : 键值对列表展示
-->
<template>
  <div class="sim-key-val" :class="{ border: border }">
    <div v-for="item in filterList" :key="item.title" class="sim-key-val__item">
      <div class="sim-key-val__title">
        <n-ellipsis>{{ item[labelField] }}</n-ellipsis>
      </div>

      <div
        class="sim-key-val__value"
        :class="{
          'sim-key-val__value-slot': item.slotName || slotValue,
          ellipsis: item.readonly || !item.slotName,
          'sim-key-val__value--readonly': item.readonly
        }"
      >
        <template v-if="item.slotName">
          <slot :name="item.slotName" :params="data" :item="item"></slot>
        </template>

        <slot v-else-if="$slots.default" :params="item"></slot>

        <slot v-else>
          <n-ellipsis>{{ valueShow(item) }}</n-ellipsis>
        </slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'SimKeyVal'
};
</script>
<script setup lang="ts">
// 第三方包
import { PropType } from 'vue';

// 组件

// hooks
import { KeyValType } from './simKeyValTypes';

const props = defineProps({
  labelField: {
    type: String as PropType<keyof KeyValType>,
    default: 'title'
  },
  valueField: {
    type: String as PropType<keyof KeyValType>,
    default: 'value'
  },
  slotValue: {
    type: Boolean,
    default: false
  },
  border: {
    type: Boolean,
    default: true
  },
  list: {
    type: Array as PropType<KeyValType[]>,
    default: () => []
  },
  data: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({})
  }
});

// 展示值
const valueShow = (item: KeyValType) => {
  const data = props.data;
  if (typeof item[props.valueField] === 'function') {
    return item[props.valueField](data, item);
  }
  // @ts-ignore
  return data[item.value] ?? item[props.valueField];
};

// 过滤不需要展示的数据
const filterList = computed(
  () =>
    props.list?.filter((it) => {
      if (!it.hasOwnProperty('isShow')) {
        return true;
      }
      if (typeof it.isShow === 'function') {
        const data = props.data;
        return it.isShow(data, it);
      }
      return !!it.isShow;
    })
);
</script>

<style lang="less">
@title-width: 100px;

.sim-key-val {
  color: var(--text-color);

  //&.border {
  //  border-top: 1px solid rgba(var(--primary-color-val), 0.3);
  //}
  //
  //&.border &__item {
  //  border: 1px solid rgba(var(--primary-color-val), 0.3);
  //  border-top: none;
  //}
  //
  //&.border &__title {
  //  border-right: 1px solid rgba(var(--primary-color-val), 0.3);
  //}

  :deep(.n-ellipsis) {
    max-width: 100%;
  }

  &__item {
    margin-bottom: 1px;
    display: flex;
    height: 32px;
    line-height: 32px;
  }

  &__title {
    margin-right: 1px;
    box-sizing: border-box;
    padding: 0 8px;
    width: @title-width;
    background: rgba(var(--primary-color-val), 0.15);
  }

  &__value {
    box-sizing: border-box;
    flex: 1;
    padding: 0 8px;
    max-width: calc(100% - @title-width);
    background: rgba(var(--text-color-val), 0.05);

    &-slot {
      padding: 0;
      background: rgba(var(--primary-color-val), 0.1);
    }

    &--readonly {
      padding: 0 8px;
      line-height: 32px;
      background: transparent;
    }
  }
}
</style>
