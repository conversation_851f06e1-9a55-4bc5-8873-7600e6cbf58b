<template>
  <!--  <div :class="['tinymce-box', theme && 'theme-tinymce']" :style="{ height: '100%' }">-->
  <div :class="['tinymce-box', theme && 'theme-tinymce']" :style="{ height: tinyHeight }">
    <Editor
      api-key="rnqa07mc4nsgetpbmjcm2ocxdzssg4gtxdplbee45u9orwnc"
      :id="props.id"
      v-model="contentValue"
      :init="initOptions"
      :disabled="readonly"
    />
  </div>
</template>
<script>
export default {
  name: 'SimTinymce'
};
</script>
<script setup>
import { computed } from 'vue';
import Editor from '@tinymce/tinymce-vue';

const props = defineProps({
  theme: {
    type: Boolean,
    default: false
  },
  toolbar: {
    type: String,
    default:
      'kityformula-editor undo redo restoredraft | cut copy image | fontsize | forecolor backcolor bold italic underline strikethrough link | alignleft aligncenter alignright alignjustify lineheight outdent indent |  styleselect formatselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript removeformat'
  },
  modelValue: {
    type: String,
    required: true,
    default: ''
  },

  menubar: {
    type: [Boolean, String],
    default: 'file edit insert format table' // tools help
  },

  height: {
    type: String,
    default: '100%'
  },

  id: {
    type: [String, Number],
    default: 'myTinymce'
  },
  color: {
    type: String,
    default: '#fff'
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

const contentValue = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  }
});
watchEffect(() => {
  console.log(contentValue.value)
})

const initOptions = {
  language: 'zh-Hans', //汉化
  skin_url: '/tinymce/ui/oxide-dark', //皮肤
  content_css: '/tinymce/content/default/content.css',
  content_style: `body{overflow:auto !important;font-size:14px;font-family:Microsoft YaHei,微软雅黑,宋体,Arial,Helvetica,sans-serif;line-height:1.5;color:${props.color}}}img {max-width:100%;}`,
  //height: 600,
  menubar: props.menubar,
  menu: {
    file: {
      title: 'File',
      items: 'newdocument preview | export | deleteallconversations' // newdocument |
    },
    edit: {
      title: 'Edit',
      items: 'undo redo restoredraft | cut copy | selectall | searchreplace'
    },
    view: {
      title: 'View',
      items: 'code | visualaid visualchars visualblocks | preview  | showcomments'
    },
    insert: {
      title: 'Insert',
      items: 'link media addcomment pageembed codesample inserttable | charmap | tableofcontents | insertdatetime'
    },
    format: {
      title: 'Format',
      items:
        'bold italic underline strikethrough superscript subscript codeformat | blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat'
    },
    tools: { title: 'Tools', items: 'a11ycheck code wordcount' },
    table: {
      title: 'Table',
      items: 'inserttable | cell row column | advtablesort | tableprops deletetable'
    },
    help: { title: 'Help', items: 'help' }
  },
  /**
   * kityformula-editor  公式
   * code codesample 代码
   */
  plugins:
    'preview searchreplace autolink directionality visualblocks visualchars  image link  template table insertdatetime advlist lists wordcount  help autosave  autoresize kityformula-editor',
  toolbar: props.toolbar,
  toolbar_sticky: false,
  toolbar_mode: 'sliding', // floating  sliding  scrolling
  line_height_formats: '1 1.2 1.4 1.6 2', //行高
  font_size_formats: '12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px 56px 72px', //字体大小
  font_family_formats:
    '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',

  // 上传图片
  images_file_types: 'jpeg,jpg,png,gif,bmp',
  // images_upload_url: 'http://test.ihycc.cn/api/common/upload',
  // images_upload_base_path: 'http://www.baidu.com',
  images_upload_handler: function (blobInfo, success, failure) {
    return new Promise(function (resolve, reject) {
      const reader = new FileReader();
      reader.readAsDataURL(blobInfo.blob());
      reader.onload = function () {
        resolve(this.result);
      };
    });
  },
  placeholder: '在这里输入文字',

  branding: false, //tiny技术支持信息是否显示
  statusbar: false, //最下方的元素路径和字数统计那一栏是否显示
  elementpath: false, //元素路径是否显示
  custom_undo_redo_levels: 10, //撤销和重做的次数
  draggable_modal: true //对话框允许拖拽
};

const tinyHeight = computed(() => props.height || '100%');
</script>

<style lang="less">
iframe {
  border: 1px solid #fff;
}
.tinymce-box {
  width: 100%;
  //height: 100%;
  .tox-tinymce {
    border: 1px solid rgba(0, 179, 255, 0.15);
    background-color: var(--secondary-bg-color);
    &:not(.tox-tinymce-inline) .tox-editor-header {
      background-color: var(--secondary-header-bg-color);
    }
    .tox-mbtn,
    .tox-tbtn,
    .tox-tbtn.tox-tbtn--bespoke:hover,
    .tox-tbtn.tox-tbtn--bespoke:focus:not(.tox-tbtn--disabled) {
      color: var(--text-color);
      svg {
        fill: var(--text-color);
      }
    }
    .tox-tbtn--disabled svg {
      fill: var(--disabled-text);
    }
    .tox-promotion {
      display: none;
    }

    .tox-menubar,
    .tox-toolbar-overlord,
    .tox-toolbar__overflow,
    .tox-toolbar__primary,
    .tox-edit-area__iframe {
      background: transparent;
      html:not(root) {
        background: #ccc;
        body::before {
          color: #fff !important;
        }
      }
    }
  }

  .tox {
    height: 100% !important;
    border-radius: 0;
    //z-index: 9999;

    .tox-menu {
      //background-color: var(--primary-color) !important;
    }

    .tox-textfield,
    .tox-dropzone {
      background: transparent !important;
      border: 1px solid var(--primary-color-dep-val) !important;

      &:focus {
        box-shadow: none !important;
      }
    }

    .tox-dialog__body-nav-item--active {
      color: var(--text-color) !important;
      border-color: var(--primary-color-dep-val) !important;
    }

    .tox-collection--list .tox-collection__item--enabled,
    .tox-collection--list .tox-collection__item--active {
      background-color: var(--primary-color-dep-val) !important;
    }
    .tox-collection--list .tox-collection__item .tox-collection__item-label {
      color: #222f3e !important;
      * {
        color: #222f3e !important;
      }
    }

    .tox-tbtn--bespoke {
      background: transparent !important;
      border: 1px solid var(--primary-color-dep-val) !important;
    }

    .tox-dialog__body-nav {
      background: red;
    }

    .tox-dialog {
      background: var(--primary-color-dep-val);

      .tox-dialog__title {
        font-size: 16px;
      }

      .tox-dialog__header,
      .tox-dialog__body-content,
      .tox-dialog__body-content,
      .tox-dialog__footer {
        background: var(--primary-color-dep-val);
      }
    }

    .tox-dialog__iframe.tox-dialog__iframe--opaque {
      background: transparent;
    }
  }
}

.tox-tinymce-aux {
  z-index: 9999 !important;
}

.theme-tinymce {
  .tox-tinymce {
    background-color: var(--primary-color-3);
  }
}
</style>
