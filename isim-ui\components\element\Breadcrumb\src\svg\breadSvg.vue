<!--
* @Author: wll
* @Date: 2023/4/11 9:17
* @Version: 1.0
* @Content:
-->
<template>
  <div>
    <svg
      width="8.000000"
      height="5.000000"
      viewBox="0 0 8 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <g id="矩形 1207">
        <path d="M4 0L8 0L4 5L0 5L4 0Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.300000" />
      </g>
      <defs />
    </svg>
    <svg
      width="8.000000"
      height="5.000000"
      viewBox="0 0 8 5"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <g id="矩形 1213">
        <path d="M4 0L8 0L4 5L0 5L4 0Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.600000" />
      </g>
      <defs />
    </svg>
    <svg
      width="17.000000"
      height="6.000000"
      viewBox="0 0 17 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <g id="矩形 1214">
        <path d="M4 0L17 0L17 6L0 6L4 0Z" fill-rule="evenodd" fill="var(--primary-color)" />
      </g>
      <defs />
    </svg>
  </div>
</template>

<script lang="ts">
export default {
  name: 'BreadSvg'
};
</script>
<script setup lang="ts"></script>
