<!--
* @Author: 宋计民
* @Date: 2024/1/23
* @Version: 1.0
* @Content: attribute-table.vue
-->
<template>
  <div class="sim-attribute-table">
    <slot />
  </div>
</template>

<script setup lang="ts" generic="T extends Record<string, any>">
import { attributeTableInjectKey } from './attribute-table-data';

defineOptions({
  name: 'SimAttribute'
});

const props = defineProps({
  labelWidth: {
    type: String,
    default: '120px'
  },
  allowEdit: {
    type: Boolean,
    default: false
  }
});
const data = defineModel('data', { default: () => ({}) });
provide(attributeTableInjectKey, { labelWidth: props.labelWidth, allowEdit: props.allowEdit, data: data.value });
</script>

<style scoped lang="less">
.sim-attribute-table {
  width: 100%;
}
</style>
