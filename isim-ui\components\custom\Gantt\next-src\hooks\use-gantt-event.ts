/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { Ref } from 'vue';
import { useEventListener } from '@vueuse/core';
interface UseGanttEventOptions {
  wheelEvent?: (e: WheelEvent) => void;
  mouseEnter?: (e: MouseEvent) => void;
}
export function useGanttEvent(canvasRef: Ref<HTMLElement>, options?: UseGanttEventOptions) {
  const { wheelEvent, mouseEnter } = options ?? {};
  const mouseWheel = (e: WheelEvent) => {
    e.preventDefault();
    wheelEvent?.(e);
  };

  const mouseLeave = () => {
    toValue(canvasRef).removeEventListener('wheel', mouseWheel);
    toValue(canvasRef).removeEventListener('mouseleave', mouseLeave);
  };
  const mouseEnterEvent = (e: MouseEvent) => {
    mouseEnter?.(e);
    toValue(canvasRef).addEventListener('wheel', mouseWheel);
    toValue(canvasRef).addEventListener('mouseleave', mouseLeave);
  };

  onMounted(() => {
    toValue(canvasRef).addEventListener('mouseenter', mouseEnterEvent);
  });

  onBeforeUnmount(() => {
    toValue(canvasRef).removeEventListener('mouseenter', mouseEnterEvent);
  });
}

interface keyDownAfterClickEventType {
  keys: string[];
  click: (e: MouseEvent) => void;
}

export const keyDownAndClick = (
  canvasRef: Ref<HTMLElement | undefined>,
  event: {
    keyDownAndClick: keyDownAfterClickEventType;
    clickEvent?: (e: MouseEvent) => void;
  }
) => {
  let isKeyDown = false;
  const { keyDownAndClick } = event;

  useEventListener(document, 'keydown', (e) => {
    if (keyDownAndClick.keys.includes(e.key)) isKeyDown = true;
  });
  useEventListener(document, 'keyup', (_e) => {
    isKeyDown = false;
  });

  useEventListener(canvasRef, 'click', (e) => {
    if (isKeyDown) return keyDownAndClick.click(e);
    event.clickEvent?.(e);
  });
};
