import { useAnnotationsInject } from '../use-annotations-inject';
import { setAnnotations, refreshAnnotations, AnnotationGraphic, AnnotationOptionsType, AnnotationConfig } from './annotation-graphic';

/**
 * @Author: 宋计民
 * @Date: 2023-08-30 9:43
 * @Version: 1.0
 * @Content: Rectangle.ts
 */

export class RectangleAnnotation extends AnnotationGraphic {
  readonly ctx: CanvasRenderingContext2D;
  readonly color: string;
  readonly startX: number;
  readonly startY: number;
  readonly width: number;
  public stopX: number;
  public stopY: number;
  constructor(ctx: CanvasRenderingContext2D, options: AnnotationOptionsType) {
    super();
    this.ctx = ctx;
    const { color = '#f00', startX, startY, width = 1 } = options;
    this.color = color;
    this.startX = startX;
    this.startY = startY;
    this.width = width;
    this.stopX = startX;
    this.stopY = startY;
    this.draw();
  }
  get minX() {
    return Math.min(this.startX, this.stopX);
  }
  get maxX() {
    return Math.max(this.startX, this.stopX);
  }
  get minY() {
    return Math.min(this.startY, this.stopY);
  }
  get maxY() {
    return Math.max(this.startY, this.stopY);
  }
  draw() {
    const ctx = this.ctx;
    ctx.beginPath();
    ctx.moveTo(this.minX, this.minY);
    ctx.lineTo(this.maxX, this.minY);
    ctx.lineTo(this.maxX, this.maxY);
    ctx.lineTo(this.minX, this.maxY);
    ctx.lineTo(this.minX, this.minY);
    ctx.strokeStyle = this.color;
    ctx.lineCap = 'square';
    ctx.lineWidth = this.width;
    ctx.stroke();
  }
}

export function useRectangleAnnotations(config: AnnotationConfig) {
  const { ctx, drawBoxRef, clearAnnotations, bindMouseUpAndLeave } = useAnnotationsInject();

  const start = () => {
    const canvas = drawBoxRef.value;
    canvas.onmousedown = (e: MouseEvent) => {
      const startX = e.offsetX;
      const startY = e.offsetY;
      const rectangle = new RectangleAnnotation(ctx.value, { ...toRaw(config), startX, startY });
      setAnnotations(rectangle);
      canvas.onmousemove = (e: MouseEvent) => {
        rectangle.stopX = e.offsetX;
        rectangle.stopY = e.offsetY;
        clearAnnotations();
        refreshAnnotations();
      };
      bindMouseUpAndLeave();
    };
  };
  const stop = () => {
    drawBoxRef.value.onmousedown = null;
  };
  return {
    start,
    stop
  };
}
