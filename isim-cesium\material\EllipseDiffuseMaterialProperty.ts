/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: 扩展圆的材质
 */
// @ts-nocheck

import { Color, defaultValue, defined, Material, Property, createPropertyDescriptor, Event, MaterialProperty } from 'cesium';
import { isNumber } from '@utils/data-type.ts';
import EllipseDiffuseShader from './Sharder/EllipseDiffuseMaterial.glsl?raw';

/**
 * @name MaterialType
 */
const MaterialType = 'EllipseDiffuse';

Material._materialCache.addMaterial(MaterialType, {
  fabric: {
    type: MaterialType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 2,
      percent: 0.03,
      gradient: 0.2,
      number: 5,
      // rotation: 90,
      // openAngle: 90,
      maxAngle: 180,
      minAngle: -180
    },
    source: EllipseDiffuseShader
  },
  translucent: function () {
    return true;
  }
});

type EllipseDiffuseOptions = {
  color: Color;
  speed: number;
  percent: number;
  gradient: number;
  number: number;
  rotation?: number;
  openAngle?: number;
  maxAngle?: number;
  minAngle?: number;
};
const EllipseDefaultValue: EllipseDiffuseOptions = {
  color: new Color(1.0, 0.0, 0.0, 0.7),
  speed: 2,
  percent: 0.03,
  gradient: 0.2,
  number: 5,
  maxAngle: 180,
  minAngle: -180
};
// const defaultColor = new Color(1.0, 0.0, 0.0, 0.7);
// const defaultSpeed = 2;
// const defaultPercent = 0.03;
// const defaultGradient = 0.2;
// const defaultNumber = 5;
// const defaultRotation = 0;
// const defaultOpenAngle = 90;
// const defaultMaxAngle = 180;
// const defaultMinAngle = -180;

export interface EllipseDiffuseMaterialPropertyConstructor extends MaterialProperty {
  new (options?: EllipseDiffuseOptions): EllipseDiffuseMaterialPropertyConstructor;
}

/**
 * @extends {MaterialProperty}
 * @param options
 * @constructor
 */
export const EllipseDiffuseMaterialProperty: EllipseDiffuseMaterialPropertyConstructor = function (options?: EllipseDiffuseOptions) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;
  this._percent = undefined;
  this._percentSubscription = undefined;
  this._gradient = undefined;
  this._gradientSubscription = undefined;
  this._number = undefined;
  this._numberSubscription = undefined;

  this._rotation = undefined;
  this._rotationSubscription = undefined;
  this._openAngle = undefined;
  this._openAngleSubscription = undefined;

  this._maxAngle = undefined;
  this._maxAngleSubscription = undefined;
  this._minAngle = undefined;
  this._minAngleSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
  this.percent = options.percent;
  this.gradient = options.gradient;
  this.number = options.number;
  // 优先判断方向角和张开角
  this.rotation = options.rotation;
  this.openAngle = options.openAngle;

  // maxAngle 和 minAngle设置数字应在 -180 ~ 180 之间
  this.maxAngle = options.maxAngle;
  this.minAngle = options.minAngle;
};

Object.defineProperties(EllipseDiffuseMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._color) && Property.isConstant(this._glow);
    }
  },

  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },

  color: createPropertyDescriptor('color'),

  /**
   * Gets or sets the numeric Property specifying the strength of the glow, as a percentage of the total line width (less than 1.0).
   * @memberof PolylineGlowMaterialProperty.prototype
   * @type {Property|undefined}
   */
  speed: createPropertyDescriptor('speed'),

  /**
   * Gets or sets the numeric Property specifying the strength of the tapering effect, as a percentage of the total line length.  If 1.0 or higher, no taper effect is used.
   * @memberof PolylineGlowMaterialProperty.prototype
   * @type {Property|undefined}
   */
  percent: createPropertyDescriptor('percent'),

  gradient: createPropertyDescriptor('gradient'),

  number: createPropertyDescriptor('number'),

  rotation: createPropertyDescriptor('rotation'),

  openAngle: createPropertyDescriptor('openAngle'),

  maxAngle: createPropertyDescriptor('maxAngle'),

  minAngle: createPropertyDescriptor('minAngle')
});

/**
 * Gets the {@link Material} type at the provided time.
 *
 * @param {JulianDate} _time The time for which to retrieve the type.
 * @returns {String} The type of material.
 */
EllipseDiffuseMaterialProperty.prototype.getType = function (_time) {
  return 'EllipseDiffuse';
};

EllipseDiffuseMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  result.color = Property.getValueOrClonedDefault(this._color, time, EllipseDefaultValue.color, result.color);
  result.speed = Property.getValueOrDefault(this._speed, time, EllipseDefaultValue.speed, result.speed);
  result.percent = Property.getValueOrDefault(this._percent, time, EllipseDefaultValue.percent, result.percent);
  result.gradient = Property.getValueOrDefault(this._gradient, time, EllipseDefaultValue.gradient, result.gradient);
  result.number = Property.getValueOrDefault(this._number, time, EllipseDefaultValue.number, result.number);

  // 如果有传方向角和探测角，优先通过方向角和探测角计算最大最小角度

  let rotation = this.rotation?.getValue();
  const openAngle = this.openAngle?.getValue();
  if (isNumber(rotation) && isNumber(openAngle)) {
    rotation %= 360;
    const dis = openAngle / 2;
    let maxAngle = -rotation + dis + 90;
    let minAngle = -rotation - dis + 90;

    if (rotation > 270) {
      maxAngle += 360;
      minAngle += 360;
    }
    if (rotation < -90) {
      maxAngle -= 360;
      minAngle -= 360;
    }

    result.maxAngle = maxAngle;
    result.minAngle = minAngle;
  } else {
    const maxAngle = Property.getValueOrDefault(this._maxAngle, time, EllipseDefaultValue.maxAngle, result.maxAngle);
    const minAngle = Property.getValueOrDefault(this._minAngle, time, EllipseDefaultValue.minAngle, result.minAngle);
    result.maxAngle = maxAngle > minAngle ? maxAngle : minAngle;
    result.minAngle = minAngle < maxAngle ? minAngle : maxAngle;
  }

  // console.log('result', result);
  return result;
};

EllipseDiffuseMaterialProperty.prototype.equals = function (other) {
  return (
    this === other ||
    (other instanceof EllipseDiffuseMaterialProperty &&
      Property.equals(this._color, other._color) &&
      Property.equals(this._glowPower, other._glowPower) &&
      Property.equals(this._taperPower, other._taperPower))
  );
};
