<!--
 * @Author: css
 * @Date: 2023-04-11 10:01:23
 * @Version: 1.0
 * @Content: Steps
-->
<template>
  <el-steps finish-status="success" class="sim-steps" simple :active="stepsActive">
    <el-step
      v-for="(item, index) in stepsList"
      :key="item"
      :title="item.name"
      :tabindex="index"
      :class="{ active: curRoute.path === item.path }"
      @click="handleClick(item)"
    >
    </el-step>
  </el-steps>
</template>
<script lang="ts">
export default {
  name: 'SimSteps'
};
</script>
<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouterHook } from '@hooks/useRouteHook';

interface Props {
  stepsList: any;
  stepsActive: number;
}
const props = withDefaults(defineProps<Props>(), {
  stepsList: [
    { path: '/experiment-design', name: '实验设计' },
    { path: '/experiment-prepare', name: '实验准备' },
    { path: '/experiment-implement', name: '实验实施' },
    { path: '/experiment-evaluate', name: '实验评估' }
  ],
  stepsActive: 2
});
const { routerPush, matchedRef, queryRef } = useRouterHook();
/**
 * 当前路由
 */
const curRoute = computed(() => matchedRef.value[2]);

/**
 * 路由跳转
 * @param item
 */
const handleClick = (item: { path: any; children: { path: any }[] }) => {
  let path = item.path;
  if (item?.children) {
    path = item?.children[0].path;
  }
  routerPush({ path, query: { expId: queryRef.value.expId } });
};
</script>

<style lang="less">
.el-steps--simple {
  background: transparent !important;
  display: flex;
  flex-direction: column;
  // position: absolute;
  // left: 20px;
  // height: 80%;
  cursor: pointer;
  padding: 0;
}
.el-steps {
  --el-text-color-primary: var(--text-color-val);
  --el-text-color-placeholder: var(--text-color-val);
  --el-color-success: rgba(30, 255, 0);

  .el-step__icon.is-text {
    border-radius: 5px;
  }
  .el-step.is-simple {
    padding: 18px 0 5px 0;
    flex-direction: column;
    width: 48px;
    height: 140px;
    background-size: 48px 140px;
    background-image: url('./svg/steps-bg.svg');
    background-repeat: no-repeat;
    &.active {
      background-image: url('./images/experiment-steps-bg-icon-active.png');
      background-repeat: no-repeat;
    }
    .el-step__head {
      padding: 0;
    }
    .el-step__title {
      text-align: center;
      margin-top: 6px;
    }
    .el-step__main {
      flex-direction: column;
      align-items: center;
      flex-grow: 0;
      width: 48px;

      .sim-steps__dec {
        content: '';
        position: absolute;
        top: 50%;
        width: 4px;
        height: 16px;
        @blockWidth: 40%;
        background: linear-gradient(90deg, rgba(255, 255, 255, 1) 50%, rgba(164, 164, 164, 1) 50%, rgba(255, 255, 255, 1) 50%);
        clip-path: polygon(0 0, 1px 0, 4px 3px, 4px 13px, 1px 16px, 0 16px);
        transform: translateY(-50%);
      }
      :before {
        .sim-steps__dec;
        left: 0;
        top: 24%;
      }
      :after {
        .sim-steps__dec;
        right: 0;
        top: 24%;
        transform: translateY(-50%) rotateZ(180deg);
      }
      .is-wait {
        writing-mode: vertical-rl;
        font-weight: 700;
        letter-spacing: 4px;
        ::before {
          content: none;
        }
      }
      .is-wait::before {
        top: 41%;
      }
      .is-wait::after {
        top: 41%;
      }
      :hover {
        color: var(--primary-color);
      }
      ::selection {
        color: red;
        background: radial-gradient(rgba(var(--primary-color-val), 0.4050588235294118) 0.2%, rgba(var(--primary-color-val), 0) 20%),
          linear-gradient(
            180deg,
            rgba(var(--btn-top-color-val), 1) 0%,
            rgba(var(--btn-center-color-val), 1) 73%,
            rgba(var(--btn-bottom-color-val), 1) 100%
          );
        border: 1px solid var(--primary-color);
        box-shadow: inset 0px 0px 6px var(--primary-color);
      }
      :focus {
        color: red;
      }
    }
    .el-step__arrow {
      width: 16px;
      height: 32px;
      background: url('./svg/steps-arrow.svg');
      margin-top: 30px;
    }
  }
  .el-step.is-simple .el-step__arrow::after,
  .el-step.is-simple .el-step__arrow::before {
    content: none;
  }
  .el-step:last-of-type.is-flex {
    max-width: 100% !important;
  }
}
</style>
