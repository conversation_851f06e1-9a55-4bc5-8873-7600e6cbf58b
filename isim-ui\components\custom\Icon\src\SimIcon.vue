<!--
* <AUTHOR> 宋计民
* @Date :  2023/5/4
* @Version : 1.0
* @Content : SimIcon
-->
<template>
  <!--<i :class="name"></i>-->
  <span class="sim-icon">
    <svg aria-hidden="true" class="sim-icon__svg">
      <use :xlink:href="symbolId" :fill="color" />
    </svg>
  </span>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
defineOptions({
  name: 'SimIcon'
});
const props = defineProps({
  name: {
    type: String,
    default: ''
  },
  prefix: {
    type: String,
    default: 'sim-icon'
  },
  color: {
    type: String,
    default: 'var(--text-color)'
  },
  size: {
    type: String as PropType<'small' | 'default' | 'middle' | 'large'>,
    default: 'default'
  }
});

const symbolId = computed(() => `#${props.prefix}-${props.name}`);
const sizeValue = computed(() => {
  return {
    small: '12px',
    default: '16px',
    middle: '18px',
    large: '28px'
  }[props.size];
});
</script>
<style scoped lang="less">
.sim-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  &__svg {
    width: v-bind(sizeValue);
    height: v-bind(sizeValue);
  }
}
</style>
