/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/10/18 18:50:59
 * @description content
 * @version 1.0
 * @CesiumVersion 1.96
 * */
import { Color, HorizontalOrigin, LabelStyle, Material } from 'cesium';

/**
 * MeasuredGraticule
 * Gaus<PERSON>er x axis constant value
 * @type {Number}
 */
const CONSTANT_6_DEGREES = 2.999999;
const LABEL_OPTIONS = {
  font: `bold 1rem Arial`,
  // font: '16px Lucida Console',
  fillColor: Color.WHITE,
  outlineColor: Color.BLACK,
  outlineWidth: 4,
  style: LabelStyle.FILL_AND_OUTLINE,
  disableDepthTestDistance: Number.POSITIVE_INFINITY
};

function createLineGraphics(positions, color) {
  return {
    positions,
    width: 0.5,
    material: Material.fromType('Color', {
      color: color
    })
  };
}

function createLabels(graticule, pos, text, isX = true) {
  return graticule._labels.add({
    ...graticule._labelOptions,
    position: pos,
    text,
    horizontalOrigin: isX ? HorizontalOrigin.CENTER : HorizontalOrigin.CENTER
  });
}

const gridSpacing = [100000, 50000, 10000, 5000, 1000, 500, 100, 50, 10, 5, 1];

/**
 * MeasuredGraticule grid scale
 * @param {Number} y
 * @param {Number} index
 * @returns
 */
function getScale(y, index = 0) {
  if (!gridSpacing[index] || gridSpacing[index] === 1) {
    return 1;
  }

  if (y / gridSpacing[index] > 10) {
    return gridSpacing[index];
  }

  if (y / gridSpacing[index] > 3) {
    return gridSpacing[index + 1];
  }

  return getScale(y, index + 2);
}

function isCenterEqual(pre, cur) {
  return pre[0] === cur[0] && pre[1] === cur[1];
}

export { CONSTANT_6_DEGREES, LABEL_OPTIONS, createLineGraphics, createLabels, getScale, isCenterEqual };
