/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */

import { DropdownDividerOption, DropdownGroupOption, DropdownOption, DropdownRenderOption } from 'naive-ui';
import { PositionedEvent } from 'isim-cesium';

type PositionType = PositionedEvent['position'];
type FilterEvent = (params: { data: RightMenuOptions; position: PositionType }) => boolean;
export type RightMenuOptions = (DropdownOption | DropdownGroupOption | DropdownDividerOption | DropdownRenderOption) & {
  isShow?: FilterEvent;
};

type SetMenuParams = RightMenuOptions & {
  group: string;
  isShow?: FilterEvent;
};
export const useRightMenu = defineStore('cesiumRightMenu', {
  state() {
    return {
      posOffset: {
        x: 0,
        y: 0
      },
      menuMap: {
        baseGroup: {},
        otherGroup: {}
      } as Record<string, Record<string, RightMenuOptions>>,
      eventList: {} as Record<string, Function>,
      rightClickEvent: new Set(),
      cachePosition: null as PositionType | null
    };
  },
  getters: {
    menuOptions(state): RightMenuOptions[] {
      const menuMap = state.menuMap;
      const menuOptions: RightMenuOptions[] = [];
      // const position = state.cachePosition;
      Object.values(menuMap).forEach((item) => {
        const values = Object.values(item);
        if (!values.length) {
          return;
        }
        menuOptions.push(...values);
        menuOptions.push({
          type: 'divider',
          show: values.some((item) => item.show)
        });
      });
      return menuOptions;
    }
  },
  actions: {
    setMenu(data: SetMenuParams, event?: (data: any) => void) {
      if (!this.menuMap[data.group]) {
        this.menuMap[data.group] = {};
      }
      this.menuMap[data.group][data.key!] = data;
      if (event) {
        this.eventList[data.key!] = event;
      }
    },
    setMenus(opt: { data: SetMenuParams; event?: (data: any) => void }[]) {
      opt.forEach((item) => {
        this.setMenu(item.data, item.event);
      });
    },
    clearMenu() {
      Object.keys(this.menuMap).forEach((item) => {
        this.menuMap[item] = {};
      });
      this.eventList = {};
    },
    clearByGroup(groupName: string) {
      this.menuMap[groupName] = {};
    },
    executeFilter(position?: PositionType) {
      if (position) {
        this.cachePosition = position;
      }
      const _position = position ?? this.cachePosition;
      Object.values(this.menuMap).forEach((item) => {
        Object.values(item).forEach((item) => {
          item.show = item.isShow?.({ data: item, position: _position! }) ?? false;
        });
      });
    },
    setRightClick(callback: (params: { position: PositionType }) => void) {
      this.rightClickEvent.add(callback);
    },
    setOffset(x: number, y: number) {
      this.posOffset = { x, y };
    },
    reset() {
      this.$reset();
    }
  }
});
