<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : scale.vue
-->
<template>
  <div v-show="!!scaleList.length" class="sim-scale">
    <div v-for="item in scaleList" :key="item.label">
      <div class="sim-scale__label">
        {{ item.label }}
      </div>
      <div class="sim-scale__bar" :style="{ width: item.barWidth + 'px' }" />
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'SimScale'
});
</script>

<script setup lang="ts">
import { getViewerName, onViewerCreated } from 'isim-cesium';
import { PropType } from 'vue';
import { Cartesian2, defined, EllipsoidGeodesic, Scene } from 'cesium';

const props = defineProps({
  viewerName: {
    type: String,
    default: getViewerName()
  },
  count: {
    type: Number,
    default: 3
  },
  updateType: {
    type: String as PropType<'change' | 'update'>,
    default: 'change'
  }
});

const distances = [
  1, 2, 3, 5, 10, 20, 30, 50, 100, 200, 300, 500, 1000, 2000, 3000, 5000, 10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000,
  2000000, 3000000, 5000000, 10000000, 20000000, 30000000, 50000000
];
const scaleList = ref<{ label: string; barWidth: number }[]>([]);
const geodesic = new EllipsoidGeodesic();

const cesiumScale = (scene: Scene) => {
  const width = scene.canvas.clientWidth;
  const height = scene.canvas.clientHeight;

  const left = scene.camera.getPickRay(new Cartesian2((width / 2) | 0, height - 1));
  const right = scene.camera.getPickRay(new Cartesian2((1 + width / 2) | 0, height - 1));

  const globe = scene.globe;
  const leftPosition = globe.pick(left, scene);
  const rightPosition = globe.pick(right, scene);

  if (!defined(leftPosition) || !defined(rightPosition)) {
    scaleList.value = [];
    return;
  }
  const leftCartographic = globe.ellipsoid.cartesianToCartographic(leftPosition);
  const rightCartographic = globe.ellipsoid.cartesianToCartographic(rightPosition);

  geodesic.setEndPoints(leftCartographic, rightCartographic);
  const pixelDistance = geodesic.surfaceDistance;

  // Find the first distance that makes the scale bar less than 100 pixels.
  const maxBarWidth = 100;
  let distance: number | undefined;
  for (let i = distances.length - 1; !defined(distance) && i >= 0; --i) {
    if (distances[i] / pixelDistance < maxBarWidth) {
      distance = distances[i];
      break;
    }
  }

  if (!defined(distance)) {
    scaleList.value = [];
    return;
  }
  getDistanceLabel(distance!, pixelDistance);
};

const getDistanceLabel = (distance: number, pixelDistance: number) => {
  let labelVal = distance;
  let unit = ' m';
  if (distance >= 1000) {
    labelVal = distance / 1000;
    unit = ' km';
  }
  const arr = [];
  for (let i = 1; i <= props.count; i++) {
    arr.push({
      label: labelVal * i + unit,
      barWidth: (distance / pixelDistance) | 0
    });
  }
  scaleList.value = arr;
};
let removeCallback: Function;
onViewerCreated(
  (viewer) => {
    if (props.updateType === 'change') {
      removeCallback = viewer.camera.changed.addEventListener(() => cesiumScale(viewer.scene));
      return;
    }
    removeCallback = viewer.scene.preUpdate.addEventListener(cesiumScale);
  },
  { viewerName: props.viewerName }
);
onBeforeUnmount(() => {
  removeCallback?.();
});
</script>

<style scoped lang="less">
.sim-scale {
  position: absolute;
  z-index: 100;
  right: 0;
  bottom: 10px;
  width: fit-content;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  box-sizing: border-box;
  &__label {
    font-size: 12px;
    //color: var(--text-color);
    color: #fff;
    text-align: center;
  }
  &__bar {
    position: relative;
    padding-top: 10px;
  }
  &__bar:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 10px;
    //border: 1px solid var(--text-color);
    border: 1px solid #fff;
    border-top: none;
    left: 0;
    bottom: 0;
  }
}
</style>
