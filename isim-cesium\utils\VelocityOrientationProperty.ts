/**
 * @Author: 宋计民
 * @Date: 2023-09-27 10:36
 * @Version: 1.0
 * @Content: index.ts
 */
import {
  Cartesian3,
  defined,
  Ellipsoid,
  Event,
  ExtrapolationType,
  JulianDate,
  Matrix3,
  PositionProperty,
  Quaternion,
  Transforms,
  VelocityVectorProperty as CVelocityOrientationProperty
} from 'cesium';

const velocityScratch = new Cartesian3();
const positionScratch = new Cartesian3();
const rotationScratch = new Matrix3();

export class VelocityOrientationProperty {
  _velocityVectorProperty: CVelocityOrientationProperty;
  _subscription: undefined;
  definitionChanged: Event;
  _forwardExtrapolationType: ExtrapolationType;
  _ellipsoid: Ellipsoid;
  constructor(position?: PositionProperty, ellipsoid = Ellipsoid.WGS84) {
    this._velocityVectorProperty = new CVelocityOrientationProperty(position, true);
    this.definitionChanged = new Event();
    this._forwardExtrapolationType = ExtrapolationType.HOLD;
    this._velocityVectorProperty.forwardExtrapolationType = this._forwardExtrapolationType;
    this._ellipsoid = ellipsoid;
    this._velocityVectorProperty.definitionChanged.addEventListener(() => {
      this.definitionChanged.raiseEvent(this);
    });
  }
  get isConstant() {
    return this._velocityVectorProperty.isConstant;
  }
  get position() {
    return this._velocityVectorProperty.position;
  }
  set position(value) {
    this._velocityVectorProperty.position = value;
  }
  get ellipsoid() {
    return this._ellipsoid;
  }
  set ellipsoid(value) {
    if (this._ellipsoid !== value) {
      this._ellipsoid = value;
      this.definitionChanged.raiseEvent(this);
    }
  }
  get forwardExtrapolationType() {
    return this._forwardExtrapolationType;
  }

  set forwardExtrapolationType(value) {
    this._forwardExtrapolationType = value;
    this._velocityVectorProperty.forwardExtrapolationType = this._forwardExtrapolationType;
  }
  getValue(time: JulianDate, result: Quaternion) {
    // @ts-ignore
    const velocity = this._velocityVectorProperty.getValue(time, positionScratch);

    if (!defined(velocity)) {
      return undefined;
    }

    Transforms.rotationMatrixFromPositionVelocity(positionScratch, velocity, this._ellipsoid, rotationScratch);
    return Quaternion.fromRotationMatrix(rotationScratch, result);
  }
}
