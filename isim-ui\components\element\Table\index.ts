/**
 * @Author: 宋计民
 * @Date: 2023/4/4
 * @Version: 1.0
 * @Content:
 */
import SimTableCom from './src/Table.vue';
import SimTableColumnCom from './src/TableColumn.vue';

import { WithInstallCom } from 'isim-ui';

const SimTable = SimTableCom as WithInstallCom<typeof SimTableCom>;

SimTable.install = function (Vue) {
  Vue.component('SimTable', SimTableCom);
  Vue.component('SimTableColumn', SimTableColumnCom);
};
const SimTableColumn = SimTableColumnCom;

export { SimTable, SimTableColumn };
