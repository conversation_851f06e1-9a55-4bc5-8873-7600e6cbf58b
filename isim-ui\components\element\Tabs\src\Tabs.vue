<!--
* <AUTHOR> 崔晓东
* @Date :  2023/4/7
* @Version : 1.0
* @Content : Tabs
-->
<template>
  <el-tabs v-bind="{ ...$attrs, ...props }" :class="`sim-tabs ${className}`">
    <template v-for="slotName in Object.keys($slots)" #[slotName]>
      <slot :name="slotName"> </slot>
    </template>
  </el-tabs>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { tabsProps } from 'element-plus';
defineOptions({
  name: 'SimTabs'
});
const props = defineProps({
  ...tabsProps,
  panelWidth: {
    type: String,
    default: '200px'
  }
});
const className = computed(() => {
  return props.type === 'border-card' ? 'block-tab' : 'inline-tab';
});
</script>
<style lang="less" scoped>
:deep(.el-tabs__nav-scroll) {
  display: flex;
  justify-content: center;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  width: v-bind('props.panelWidth');
  height: 32px;
  margin-right: 2px;
  color: var(--text-color);
  background: var(--secondary-bg-color);
}
:deep(.el-tabs__item:hover) {
  background: var(--secondary-color-hover);
}
:deep(.el-tabs__item.is-active) {
  background: var(--primary-color-hover-linear);
  color: var(--text-color);
}
</style>
