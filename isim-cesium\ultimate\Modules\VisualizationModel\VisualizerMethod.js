import { Math as CesiumMath, Model, Transforms } from 'cesium';
import Yaw<PERSON>itchRoll from '../../Core/Transform/YawPitchRoll';

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/08/01 14:15:24
 * @description Visualizer Public Method
 * @version 1.0
 * */

function generateGradientColor(color) {
  const fadeOutColor = color.clone();
  fadeOutColor.alpha = 0.2;

  const fadeInColor = color.clone();
  if (color.green === 0) {
    fadeInColor.green = 0.6;
  } else {
    fadeInColor.red = 0.5;
    fadeInColor.blue = 1.0;
  }

  fadeInColor.alpha = 0.3;

  return [fadeInColor, fadeOutColor];
}

function updateMode(primitive, frameState) {
  const update = primitive._update;
  if (!update) {
    primitive._update = frameState.mode !== primitive._mode;
  }
}

function createModel(primitives, url, pos, hpr) {
  primitives.add(
    Model.fromGltf({
      url,
      minimumPixelSize: 10,
      modelMatrix: Transforms.headingPitchRollToFixedFrame(pos, hpr)
    })
  );
}

function getTrans(position, yaw, pitch, roll) {
  const ypr = YawPitchRoll.fromDegrees(yaw, pitch, roll, YawPitchRoll.Direction.TO_NORTH);
  return ypr.toTransforms(position);
}

function getHeading(pitch, yaw) {
  const p = CesiumMath.negativePiToPi(CesiumMath.toRadians(pitch));
  return p > -Math.PI / 2 && p < Math.PI / 2 ? yaw : yaw + 180;
}

export { generateGradientColor, updateMode, createModel, getTrans, getHeading };
