/**
 * <AUTHOR>
 * @email jayjay<PERSON>@yeah.net
 * @date 2021/07/20 16:07:46
 * @description  update and add new Method
 * */

import { defined, PositionProperty, ReferenceFrame, SampledPositionProperty } from 'cesium';

SampledPositionProperty.prototype.getInertialValue = function (time, result) {
  return this.getValueInReferenceFrame(time, ReferenceFrame.INERTIAL, result);
};

SampledPositionProperty.prototype.getOrbitFixedValue = function (time, value, result) {
  if (!defined(value) || this._referenceFrame === ReferenceFrame.FIXED) {
    return undefined;
  }
  return PositionProperty.convertToReferenceFrame(time, value, this._referenceFrame, ReferenceFrame.FIXED, result);
};
