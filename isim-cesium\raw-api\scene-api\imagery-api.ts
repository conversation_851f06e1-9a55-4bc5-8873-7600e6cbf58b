import {
  DefaultProxy,
  GeographicTilingScheme,
  ImageryLayer,
  UrlTemplateImageryProvider,
  WebMapServiceImageryProvider,
  WebMapTileServiceImageryProvider
} from 'cesium';
import { getViewer, getViewerName, onViewerDestroyed, WGS84GridLayer } from 'isim-cesium';

export interface ImageryCacheConfigType {
  isTop?: boolean;
  label?: string;
  code: string;
  url?: string;
  type?: string;
  layer?: string;
  imageLayer?: ImageryLayer;
  value?: Function;
}

export interface ImageryCacheObjType extends ImageryCacheConfigType {
  imagery: ImageryProviderType;
}

export type ImageryProviderType = UrlTemplateImageryProvider | WebMapServiceImageryProvider | WebMapTileServiceImageryProvider | WGS84GridLayer | any;

const imageryWeak = new Map<string, Map<string, ImageryProviderType>>();

/**
 * 设置缓存
 * @param imageryName
 * @param imagery
 * @param viewerName
 */
export function setImageryWeak(
  imageryName: string,
  imagery: {
    imagery: ImageryProviderType;
    imageLayer: ImageryLayer;
  },
  viewerName = getViewerName()
) {
  if (!getImageryWeakByViewer(viewerName)) {
    imageryWeak.set(viewerName, new Map());
    onViewerDestroyed(() => imageryWeak.delete(viewerName), { viewerName });
  }
  getImageryWeakByViewer(viewerName)?.set(imageryName, imagery);
}

/**
 * 清空当前视图的所有图层缓存
 * @param viewerName
 */
export function clearImageryWeak(viewerName = getViewerName()) {
  getImageryWeakByViewer(viewerName)?.clear();
}

/**
 * 删除指定名称的图层
 * @param imageryName
 * @param viewerName
 */
export function removeImageryWeak(imageryName: string, viewerName = getViewerName()) {
  getImageryWeakByViewer(viewerName)?.delete(imageryName);
}

/**
 * 通过viewer获取到imageProvider缓存中的配置
 * @param viewerName
 */
export function getImageryWeakByViewer(viewerName = getViewerName()) {
  return imageryWeak.get(viewerName);
}

/**
 * 添加图层
 * @param imageryName
 * @param imageProvider
 * @param viewerName
 */
export function addImageryProvider(imageryName: string, imageProvider: ImageryProviderType, viewerName = getViewerName()) {
  const viewer = getViewer(viewerName);
  if (hasImageryProviderByViewer(imageryName, viewerName)) {
    visibleImageryProvider(imageryName);
    return false;
  }
  // @ts-ignore
  const imageLayer = viewer.scene.imageryLayers.addImageryProvider(imageProvider);
  setImageryWeak(imageryName, {
    imagery: imageProvider,
    imageLayer: imageLayer
  });
}

/**
 * 设置图层 会清空所有的图层
 * @param imageName
 * @param imageProvider
 * @param viewerName
 */
export function setImageryProvider(imageName: string, imageProvider: ImageryProviderType, viewerName = getViewerName()) {
  const viewer = getViewer(viewerName);
  if (hasImageryProviderByViewer(imageName, viewerName)) {
    return false;
  }
  removeAllImageryProvider(viewerName);
  // @ts-ignore
  const imageLayer = viewer.scene.imageryLayers.addImageryProvider(imageProvider);
  setImageryWeak(imageName, {
    imagery: imageProvider,
    imageLayer
  });
}

/**
 * 清空所有的图层
 * @param viewerName
 */
export function removeAllImageryProvider(viewerName?: string) {
  const viewer = getViewer(viewerName);
  viewer.scene.imageryLayers.removeAll();
  clearImageryWeak(viewerName);
}

/**
 * 创建底圖 provider
 * @param config
 */
export function createUrlTemplateImageryProvider(config: UrlTemplateImageryProvider.ConstructorOptions) {
  const _config = {
    tilingScheme: new GeographicTilingScheme(),
    minimumLevel: 0,
    maximumLevel: 19,
    ...config
  };
  console.log(_config);
  return new UrlTemplateImageryProvider(_config);
}

/**
 * 创建WMS provider
 * @param config
 */
export function createWebMapServiceImageryProvider(config: WebMapServiceImageryProvider.ConstructorOptions) {
  const _config = {
    proxy: new DefaultProxy('/proxy/'),
    parameters: {
      service: 'WMS',
      version: '1.3.0',
      transparent: true,
      format: 'image/png'
    },
    ...config
  };
  return new WebMapServiceImageryProvider(_config);
}

/**
 * 创建 WTMS provider
 * @param config
 */
export function createWebTileMapServiceImageryProvider(config: Omit<WebMapTileServiceImageryProvider.ConstructorOptions, 'tileMatrixSetID'>) {
  return new WebMapTileServiceImageryProvider({
    tileMatrixLabels: [
      'EPSG:4326:0',
      'EPSG:4326:1',
      'EPSG:4326:2',
      'EPSG:4326:3',
      'EPSG:4326:4',
      'EPSG:4326:5',
      'EPSG:4326:6',
      'EPSG:4326:7',
      'EPSG:4326:8',
      'EPSG:4326:9',
      'EPSG:4326:10',
      'EPSG:4326:11',
      'EPSG:4326:12',
      'EPSG:4326:13',
      'EPSG:4326:14',
      'EPSG:4326:15',
      'EPSG:4326:16',
      'EPSG:4326:17',
      'EPSG:4326:18',
      'EPSG:4326:19',
      'EPSG:4326:20',
      'EPSG:4326:21'
    ],
    format: 'image/png',
    tileMatrixSetID: 'EPSG:4326',
    ...config
  });
}

/**
 * 判断图层是否已经存在
 * @param imageryName
 * @param viewerName
 */
export function hasImageryProviderByViewer(imageryName: string, viewerName = getViewerName()) {
  return getImageryWeakByViewer(viewerName)?.has(imageryName);
}

export function getImageryProviderByCode(code: string, viewerName = getViewerName()) {
  return getImageryWeakByViewer(viewerName)?.get(code);
}

/**
 * 隐藏图层
 */
export const hiddenImageryProvider = generateImageryProviderShow(false);

/**
 * 显示图层
 */
export const visibleImageryProvider = generateImageryProviderShow(true);

export function imageryProviderIsShow(imagery: string, viewerName = getViewerName()) {
  const imageryObj = getImageryWeakByViewer(viewerName)?.get(imagery);
  return imageryObj?.imageLayer.show;
}

function generateImageryProviderShow(isShow: boolean) {
  return function (imagery: string, viewerName = getViewerName()) {
    const imageryObj = getImageryWeakByViewer(viewerName)?.get(imagery);
    if (imageryObj?.imageLayer) {
      imageryObj.imageLayer.show = isShow;
      if (imageryObj.imageLayer.imageryProvider) {
        //@ts-ignore
        imageryObj.imageLayer.imageryProvider.show = isShow;
      }
    }
  };
}

/**
 * 获取已添加的所有的图层
 * @param viewerName
 */
export function getImageryProviderList(viewerName = getViewerName()) {
  return getImageryWeakByViewer(viewerName);
}
