import SimBreadcrumbCom from './src/Breadcrumb.vue';
import SimBreadcrumbForPropCom from './src/BreadcrumbForProp.vue';

import { WithInstallCom } from 'isim-ui';
const SimBreadcrumb = SimBreadcrumbCom as WithInstallCom<typeof SimBreadcrumbCom>;
const SimBreadcrumbForProp = SimBreadcrumbForPropCom as WithInstallCom<typeof SimBreadcrumbForPropCom>;

SimBreadcrumb.install = function (Vue) {
  Vue.component('SimBreadcrumb', SimBreadcrumbCom);
};
SimBreadcrumbForProp.install = function (Vue) {
  Vue.component('SimBreadcrumbForProp', SimBreadcrumbForPropCom);
};

export { SimBreadcrumb, SimBreadcrumbForProp };
