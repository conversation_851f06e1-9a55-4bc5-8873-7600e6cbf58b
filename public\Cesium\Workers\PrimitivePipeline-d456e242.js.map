{"version": 3, "file": "PrimitivePipeline-d456e242.js", "sources": ["../../../../Source/Core/OffsetGeometryInstanceAttribute.js", "../../../../Source/Scene/PrimitivePipeline.js"], "sourcesContent": ["import Check from \"./Check.js\";\nimport ComponentDatatype from \"./ComponentDatatype.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\n\n/**\n * Value and type information for per-instance geometry attribute that determines the geometry instance offset\n *\n * @alias OffsetGeometryInstanceAttribute\n * @constructor\n *\n * @param {Number} [x=0] The x translation\n * @param {Number} [y=0] The y translation\n * @param {Number} [z=0] The z translation\n *\n * @private\n *\n * @see GeometryInstance\n * @see GeometryInstanceAttribute\n */\nfunction OffsetGeometryInstanceAttribute(x, y, z) {\n  x = defaultValue(x, 0);\n  y = defaultValue(y, 0);\n  z = defaultValue(z, 0);\n\n  /**\n   * The values for the attributes stored in a typed array.\n   *\n   * @type Float32Array\n   */\n  this.value = new Float32Array([x, y, z]);\n}\n\nObject.defineProperties(OffsetGeometryInstanceAttribute.prototype, {\n  /**\n   * The datatype of each component in the attribute, e.g., individual elements in\n   * {@link OffsetGeometryInstanceAttribute#value}.\n   *\n   * @memberof OffsetGeometryInstanceAttribute.prototype\n   *\n   * @type {ComponentDatatype}\n   * @readonly\n   *\n   * @default {@link ComponentDatatype.FLOAT}\n   */\n  componentDatatype: {\n    get: function () {\n      return ComponentDatatype.FLOAT;\n    },\n  },\n\n  /**\n   * The number of components in the attributes, i.e., {@link OffsetGeometryInstanceAttribute#value}.\n   *\n   * @memberof OffsetGeometryInstanceAttribute.prototype\n   *\n   * @type {Number}\n   * @readonly\n   *\n   * @default 3\n   */\n  componentsPerAttribute: {\n    get: function () {\n      return 3;\n    },\n  },\n\n  /**\n   * When <code>true</code> and <code>componentDatatype</code> is an integer format,\n   * indicate that the components should be mapped to the range [0, 1] (unsigned)\n   * or [-1, 1] (signed) when they are accessed as floating-point for rendering.\n   *\n   * @memberof OffsetGeometryInstanceAttribute.prototype\n   *\n   * @type {Boolean}\n   * @readonly\n   *\n   * @default false\n   */\n  normalize: {\n    get: function () {\n      return false;\n    },\n  },\n});\n\n/**\n * Creates a new {@link OffsetGeometryInstanceAttribute} instance given the provided an enabled flag and {@link DistanceDisplayCondition}.\n *\n * @param {Cartesian3} offset The cartesian offset\n * @returns {OffsetGeometryInstanceAttribute} The new {@link OffsetGeometryInstanceAttribute} instance.\n */\nOffsetGeometryInstanceAttribute.fromCartesian3 = function (offset) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"offset\", offset);\n  //>>includeEnd('debug');\n\n  return new OffsetGeometryInstanceAttribute(offset.x, offset.y, offset.z);\n};\n\n/**\n * Converts a distance display condition to a typed array that can be used to assign a distance display condition attribute.\n *\n * @param {Cartesian3} offset The cartesian offset\n * @param {Float32Array} [result] The array to store the result in, if undefined a new instance will be created.\n * @returns {Float32Array} The modified result parameter or a new instance if result was undefined.\n *\n * @example\n * const attributes = primitive.getGeometryInstanceAttributes('an id');\n * attributes.modelMatrix = Cesium.OffsetGeometryInstanceAttribute.toValue(modelMatrix, attributes.modelMatrix);\n */\nOffsetGeometryInstanceAttribute.toValue = function (offset, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"offset\", offset);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Float32Array([offset.x, offset.y, offset.z]);\n  }\n\n  result[0] = offset.x;\n  result[1] = offset.y;\n  result[2] = offset.z;\n  return result;\n};\nexport default OffsetGeometryInstanceAttribute;\n", "import BoundingSphere from \"../Core/BoundingSphere.js\";\nimport ComponentDatatype from \"../Core/ComponentDatatype.js\";\nimport defaultValue from \"../Core/defaultValue.js\";\nimport defined from \"../Core/defined.js\";\nimport DeveloperError from \"../Core/DeveloperError.js\";\nimport Ellipsoid from \"../Core/Ellipsoid.js\";\nimport GeographicProjection from \"../Core/GeographicProjection.js\";\nimport Geometry from \"../Core/Geometry.js\";\nimport GeometryAttribute from \"../Core/GeometryAttribute.js\";\nimport GeometryAttributes from \"../Core/GeometryAttributes.js\";\nimport GeometryPipeline from \"../Core/GeometryPipeline.js\";\nimport IndexDatatype from \"../Core/IndexDatatype.js\";\nimport Matrix4 from \"../Core/Matrix4.js\";\nimport OffsetGeometryInstanceAttribute from \"../Core/OffsetGeometryInstanceAttribute.js\";\nimport WebMercatorProjection from \"../Core/WebMercatorProjection.js\";\n\nfunction transformToWorldCoordinates(\n  instances,\n  primitiveModelMatrix,\n  scene3DOnly\n) {\n  let toWorld = !scene3DOnly;\n  const length = instances.length;\n  let i;\n\n  if (!toWorld && length > 1) {\n    const modelMatrix = instances[0].modelMatrix;\n\n    for (i = 1; i < length; ++i) {\n      if (!Matrix4.equals(modelMatrix, instances[i].modelMatrix)) {\n        toWorld = true;\n        break;\n      }\n    }\n  }\n\n  if (toWorld) {\n    for (i = 0; i < length; ++i) {\n      if (defined(instances[i].geometry)) {\n        GeometryPipeline.transformToWorldCoordinates(instances[i]);\n      }\n    }\n  } else {\n    // Leave geometry in local coordinate system; auto update model-matrix.\n    Matrix4.multiplyTransformation(\n      primitiveModelMatrix,\n      instances[0].modelMatrix,\n      primitiveModelMatrix\n    );\n  }\n}\n\nfunction addGeometryBatchId(geometry, batchId) {\n  const attributes = geometry.attributes;\n  const positionAttr = attributes.position;\n  const numberOfComponents =\n    positionAttr.values.length / positionAttr.componentsPerAttribute;\n\n  attributes.batchId = new GeometryAttribute({\n    componentDatatype: ComponentDatatype.FLOAT,\n    componentsPerAttribute: 1,\n    values: new Float32Array(numberOfComponents),\n  });\n\n  const values = attributes.batchId.values;\n  for (let j = 0; j < numberOfComponents; ++j) {\n    values[j] = batchId;\n  }\n}\n\nfunction addBatchIds(instances) {\n  const length = instances.length;\n\n  for (let i = 0; i < length; ++i) {\n    const instance = instances[i];\n    if (defined(instance.geometry)) {\n      addGeometryBatchId(instance.geometry, i);\n    } else if (\n      defined(instance.westHemisphereGeometry) &&\n      defined(instance.eastHemisphereGeometry)\n    ) {\n      addGeometryBatchId(instance.westHemisphereGeometry, i);\n      addGeometryBatchId(instance.eastHemisphereGeometry, i);\n    }\n  }\n}\n\nfunction geometryPipeline(parameters) {\n  const instances = parameters.instances;\n  const projection = parameters.projection;\n  const uintIndexSupport = parameters.elementIndexUintSupported;\n  const scene3DOnly = parameters.scene3DOnly;\n  const vertexCacheOptimize = parameters.vertexCacheOptimize;\n  const compressVertices = parameters.compressVertices;\n  const modelMatrix = parameters.modelMatrix;\n\n  let i;\n  let geometry;\n  let primitiveType;\n  let length = instances.length;\n\n  for (i = 0; i < length; ++i) {\n    if (defined(instances[i].geometry)) {\n      primitiveType = instances[i].geometry.primitiveType;\n      break;\n    }\n  }\n\n  //>>includeStart('debug', pragmas.debug);\n  for (i = 1; i < length; ++i) {\n    if (\n      defined(instances[i].geometry) &&\n      instances[i].geometry.primitiveType !== primitiveType\n    ) {\n      throw new DeveloperError(\n        \"All instance geometries must have the same primitiveType.\"\n      );\n    }\n  }\n  //>>includeEnd('debug');\n\n  // Unify to world coordinates before combining.\n  transformToWorldCoordinates(instances, modelMatrix, scene3DOnly);\n\n  // Clip to IDL\n  if (!scene3DOnly) {\n    for (i = 0; i < length; ++i) {\n      if (defined(instances[i].geometry)) {\n        GeometryPipeline.splitLongitude(instances[i]);\n      }\n    }\n  }\n\n  addBatchIds(instances);\n\n  // Optimize for vertex shader caches\n  if (vertexCacheOptimize) {\n    for (i = 0; i < length; ++i) {\n      const instance = instances[i];\n      if (defined(instance.geometry)) {\n        GeometryPipeline.reorderForPostVertexCache(instance.geometry);\n        GeometryPipeline.reorderForPreVertexCache(instance.geometry);\n      } else if (\n        defined(instance.westHemisphereGeometry) &&\n        defined(instance.eastHemisphereGeometry)\n      ) {\n        GeometryPipeline.reorderForPostVertexCache(\n          instance.westHemisphereGeometry\n        );\n        GeometryPipeline.reorderForPreVertexCache(\n          instance.westHemisphereGeometry\n        );\n\n        GeometryPipeline.reorderForPostVertexCache(\n          instance.eastHemisphereGeometry\n        );\n        GeometryPipeline.reorderForPreVertexCache(\n          instance.eastHemisphereGeometry\n        );\n      }\n    }\n  }\n\n  // Combine into single geometry for better rendering performance.\n  let geometries = GeometryPipeline.combineInstances(instances);\n\n  length = geometries.length;\n  for (i = 0; i < length; ++i) {\n    geometry = geometries[i];\n\n    // Split positions for GPU RTE\n    const attributes = geometry.attributes;\n    if (!scene3DOnly) {\n      for (const name in attributes) {\n        if (\n          attributes.hasOwnProperty(name) &&\n          attributes[name].componentDatatype === ComponentDatatype.DOUBLE\n        ) {\n          const name3D = name + \"3D\";\n          const name2D = name + \"2D\";\n\n          // Compute 2D positions\n          GeometryPipeline.projectTo2D(\n            geometry,\n            name,\n            name3D,\n            name2D,\n            projection\n          );\n          if (defined(geometry.boundingSphere) && name === \"position\") {\n            geometry.boundingSphereCV = BoundingSphere.fromVertices(\n              geometry.attributes.position2D.values\n            );\n          }\n\n          GeometryPipeline.encodeAttribute(\n            geometry,\n            name3D,\n            name3D + \"High\",\n            name3D + \"Low\"\n          );\n          GeometryPipeline.encodeAttribute(\n            geometry,\n            name2D,\n            name2D + \"High\",\n            name2D + \"Low\"\n          );\n        }\n      }\n    } else {\n      for (const name in attributes) {\n        if (\n          attributes.hasOwnProperty(name) &&\n          attributes[name].componentDatatype === ComponentDatatype.DOUBLE\n        ) {\n          GeometryPipeline.encodeAttribute(\n            geometry,\n            name,\n            name + \"3DHigh\",\n            name + \"3DLow\"\n          );\n        }\n      }\n    }\n\n    // oct encode and pack normals, compress texture coordinates\n    if (compressVertices) {\n      GeometryPipeline.compressVertices(geometry);\n    }\n  }\n\n  if (!uintIndexSupport) {\n    // Break into multiple geometries to fit within unsigned short indices if needed\n    let splitGeometries = [];\n    length = geometries.length;\n    for (i = 0; i < length; ++i) {\n      geometry = geometries[i];\n      splitGeometries = splitGeometries.concat(\n        GeometryPipeline.fitToUnsignedShortIndices(geometry)\n      );\n    }\n\n    geometries = splitGeometries;\n  }\n\n  return geometries;\n}\n\nfunction createPickOffsets(instances, geometryName, geometries, pickOffsets) {\n  let offset;\n  let indexCount;\n  let geometryIndex;\n\n  const offsetIndex = pickOffsets.length - 1;\n  if (offsetIndex >= 0) {\n    const pickOffset = pickOffsets[offsetIndex];\n    offset = pickOffset.offset + pickOffset.count;\n    geometryIndex = pickOffset.index;\n    indexCount = geometries[geometryIndex].indices.length;\n  } else {\n    offset = 0;\n    geometryIndex = 0;\n    indexCount = geometries[geometryIndex].indices.length;\n  }\n\n  const length = instances.length;\n  for (let i = 0; i < length; ++i) {\n    const instance = instances[i];\n    const geometry = instance[geometryName];\n    if (!defined(geometry)) {\n      continue;\n    }\n\n    const count = geometry.indices.length;\n\n    if (offset + count > indexCount) {\n      offset = 0;\n      indexCount = geometries[++geometryIndex].indices.length;\n    }\n\n    pickOffsets.push({\n      index: geometryIndex,\n      offset: offset,\n      count: count,\n    });\n    offset += count;\n  }\n}\n\nfunction createInstancePickOffsets(instances, geometries) {\n  const pickOffsets = [];\n  createPickOffsets(instances, \"geometry\", geometries, pickOffsets);\n  createPickOffsets(\n    instances,\n    \"westHemisphereGeometry\",\n    geometries,\n    pickOffsets\n  );\n  createPickOffsets(\n    instances,\n    \"eastHemisphereGeometry\",\n    geometries,\n    pickOffsets\n  );\n  return pickOffsets;\n}\n\n/**\n * @private\n */\nconst PrimitivePipeline = {};\n\n/**\n * @private\n */\nPrimitivePipeline.combineGeometry = function (parameters) {\n  let geometries;\n  let attributeLocations;\n  const instances = parameters.instances;\n  const length = instances.length;\n  let pickOffsets;\n\n  let offsetInstanceExtend;\n  let hasOffset = false;\n  if (length > 0) {\n    geometries = geometryPipeline(parameters);\n    if (geometries.length > 0) {\n      attributeLocations = GeometryPipeline.createAttributeLocations(\n        geometries[0]\n      );\n      if (parameters.createPickOffsets) {\n        pickOffsets = createInstancePickOffsets(instances, geometries);\n      }\n    }\n    if (\n      defined(instances[0].attributes) &&\n      defined(instances[0].attributes.offset)\n    ) {\n      offsetInstanceExtend = new Array(length);\n      hasOffset = true;\n    }\n  }\n\n  const boundingSpheres = new Array(length);\n  const boundingSpheresCV = new Array(length);\n  for (let i = 0; i < length; ++i) {\n    const instance = instances[i];\n    const geometry = instance.geometry;\n    if (defined(geometry)) {\n      boundingSpheres[i] = geometry.boundingSphere;\n      boundingSpheresCV[i] = geometry.boundingSphereCV;\n      if (hasOffset) {\n        offsetInstanceExtend[i] = instance.geometry.offsetAttribute;\n      }\n    }\n\n    const eastHemisphereGeometry = instance.eastHemisphereGeometry;\n    const westHemisphereGeometry = instance.westHemisphereGeometry;\n    if (defined(eastHemisphereGeometry) && defined(westHemisphereGeometry)) {\n      if (\n        defined(eastHemisphereGeometry.boundingSphere) &&\n        defined(westHemisphereGeometry.boundingSphere)\n      ) {\n        boundingSpheres[i] = BoundingSphere.union(\n          eastHemisphereGeometry.boundingSphere,\n          westHemisphereGeometry.boundingSphere\n        );\n      }\n      if (\n        defined(eastHemisphereGeometry.boundingSphereCV) &&\n        defined(westHemisphereGeometry.boundingSphereCV)\n      ) {\n        boundingSpheresCV[i] = BoundingSphere.union(\n          eastHemisphereGeometry.boundingSphereCV,\n          westHemisphereGeometry.boundingSphereCV\n        );\n      }\n    }\n  }\n\n  return {\n    geometries: geometries,\n    modelMatrix: parameters.modelMatrix,\n    attributeLocations: attributeLocations,\n    pickOffsets: pickOffsets,\n    offsetInstanceExtend: offsetInstanceExtend,\n    boundingSpheres: boundingSpheres,\n    boundingSpheresCV: boundingSpheresCV,\n  };\n};\n\nfunction transferGeometry(geometry, transferableObjects) {\n  const attributes = geometry.attributes;\n  for (const name in attributes) {\n    if (attributes.hasOwnProperty(name)) {\n      const attribute = attributes[name];\n\n      if (defined(attribute) && defined(attribute.values)) {\n        transferableObjects.push(attribute.values.buffer);\n      }\n    }\n  }\n\n  if (defined(geometry.indices)) {\n    transferableObjects.push(geometry.indices.buffer);\n  }\n}\n\nfunction transferGeometries(geometries, transferableObjects) {\n  const length = geometries.length;\n  for (let i = 0; i < length; ++i) {\n    transferGeometry(geometries[i], transferableObjects);\n  }\n}\n\n// This function was created by simplifying packCreateGeometryResults into a count-only operation.\nfunction countCreateGeometryResults(items) {\n  let count = 1;\n  const length = items.length;\n  for (let i = 0; i < length; i++) {\n    const geometry = items[i];\n    ++count;\n\n    if (!defined(geometry)) {\n      continue;\n    }\n\n    const attributes = geometry.attributes;\n\n    count +=\n      7 +\n      2 * BoundingSphere.packedLength +\n      (defined(geometry.indices) ? geometry.indices.length : 0);\n\n    for (const property in attributes) {\n      if (\n        attributes.hasOwnProperty(property) &&\n        defined(attributes[property])\n      ) {\n        const attribute = attributes[property];\n        count += 5 + attribute.values.length;\n      }\n    }\n  }\n\n  return count;\n}\n\n/**\n * @private\n */\nPrimitivePipeline.packCreateGeometryResults = function (\n  items,\n  transferableObjects\n) {\n  const packedData = new Float64Array(countCreateGeometryResults(items));\n  const stringTable = [];\n  const stringHash = {};\n\n  const length = items.length;\n  let count = 0;\n  packedData[count++] = length;\n  for (let i = 0; i < length; i++) {\n    const geometry = items[i];\n\n    const validGeometry = defined(geometry);\n    packedData[count++] = validGeometry ? 1.0 : 0.0;\n\n    if (!validGeometry) {\n      continue;\n    }\n\n    packedData[count++] = geometry.primitiveType;\n    packedData[count++] = geometry.geometryType;\n    packedData[count++] = defaultValue(geometry.offsetAttribute, -1);\n\n    const validBoundingSphere = defined(geometry.boundingSphere) ? 1.0 : 0.0;\n    packedData[count++] = validBoundingSphere;\n    if (validBoundingSphere) {\n      BoundingSphere.pack(geometry.boundingSphere, packedData, count);\n    }\n\n    count += BoundingSphere.packedLength;\n\n    const validBoundingSphereCV = defined(geometry.boundingSphereCV)\n      ? 1.0\n      : 0.0;\n    packedData[count++] = validBoundingSphereCV;\n    if (validBoundingSphereCV) {\n      BoundingSphere.pack(geometry.boundingSphereCV, packedData, count);\n    }\n\n    count += BoundingSphere.packedLength;\n\n    const attributes = geometry.attributes;\n    const attributesToWrite = [];\n    for (const property in attributes) {\n      if (\n        attributes.hasOwnProperty(property) &&\n        defined(attributes[property])\n      ) {\n        attributesToWrite.push(property);\n        if (!defined(stringHash[property])) {\n          stringHash[property] = stringTable.length;\n          stringTable.push(property);\n        }\n      }\n    }\n\n    packedData[count++] = attributesToWrite.length;\n    for (let q = 0; q < attributesToWrite.length; q++) {\n      const name = attributesToWrite[q];\n      const attribute = attributes[name];\n      packedData[count++] = stringHash[name];\n      packedData[count++] = attribute.componentDatatype;\n      packedData[count++] = attribute.componentsPerAttribute;\n      packedData[count++] = attribute.normalize ? 1 : 0;\n      packedData[count++] = attribute.values.length;\n      packedData.set(attribute.values, count);\n      count += attribute.values.length;\n    }\n\n    const indicesLength = defined(geometry.indices)\n      ? geometry.indices.length\n      : 0;\n    packedData[count++] = indicesLength;\n\n    if (indicesLength > 0) {\n      packedData.set(geometry.indices, count);\n      count += indicesLength;\n    }\n  }\n\n  transferableObjects.push(packedData.buffer);\n\n  return {\n    stringTable: stringTable,\n    packedData: packedData,\n  };\n};\n\n/**\n * @private\n */\nPrimitivePipeline.unpackCreateGeometryResults = function (\n  createGeometryResult\n) {\n  const stringTable = createGeometryResult.stringTable;\n  const packedGeometry = createGeometryResult.packedData;\n\n  let i;\n  const result = new Array(packedGeometry[0]);\n  let resultIndex = 0;\n\n  let packedGeometryIndex = 1;\n  while (packedGeometryIndex < packedGeometry.length) {\n    const valid = packedGeometry[packedGeometryIndex++] === 1.0;\n    if (!valid) {\n      result[resultIndex++] = undefined;\n      continue;\n    }\n\n    const primitiveType = packedGeometry[packedGeometryIndex++];\n    const geometryType = packedGeometry[packedGeometryIndex++];\n    let offsetAttribute = packedGeometry[packedGeometryIndex++];\n    if (offsetAttribute === -1) {\n      offsetAttribute = undefined;\n    }\n\n    let boundingSphere;\n    let boundingSphereCV;\n\n    const validBoundingSphere = packedGeometry[packedGeometryIndex++] === 1.0;\n    if (validBoundingSphere) {\n      boundingSphere = BoundingSphere.unpack(\n        packedGeometry,\n        packedGeometryIndex\n      );\n    }\n\n    packedGeometryIndex += BoundingSphere.packedLength;\n\n    const validBoundingSphereCV = packedGeometry[packedGeometryIndex++] === 1.0;\n    if (validBoundingSphereCV) {\n      boundingSphereCV = BoundingSphere.unpack(\n        packedGeometry,\n        packedGeometryIndex\n      );\n    }\n\n    packedGeometryIndex += BoundingSphere.packedLength;\n\n    let length;\n    let values;\n    let componentsPerAttribute;\n    const attributes = new GeometryAttributes();\n    const numAttributes = packedGeometry[packedGeometryIndex++];\n    for (i = 0; i < numAttributes; i++) {\n      const name = stringTable[packedGeometry[packedGeometryIndex++]];\n      const componentDatatype = packedGeometry[packedGeometryIndex++];\n      componentsPerAttribute = packedGeometry[packedGeometryIndex++];\n      const normalize = packedGeometry[packedGeometryIndex++] !== 0;\n\n      length = packedGeometry[packedGeometryIndex++];\n      values = ComponentDatatype.createTypedArray(componentDatatype, length);\n      for (let valuesIndex = 0; valuesIndex < length; valuesIndex++) {\n        values[valuesIndex] = packedGeometry[packedGeometryIndex++];\n      }\n\n      attributes[name] = new GeometryAttribute({\n        componentDatatype: componentDatatype,\n        componentsPerAttribute: componentsPerAttribute,\n        normalize: normalize,\n        values: values,\n      });\n    }\n\n    let indices;\n    length = packedGeometry[packedGeometryIndex++];\n\n    if (length > 0) {\n      const numberOfVertices = values.length / componentsPerAttribute;\n      indices = IndexDatatype.createTypedArray(numberOfVertices, length);\n      for (i = 0; i < length; i++) {\n        indices[i] = packedGeometry[packedGeometryIndex++];\n      }\n    }\n\n    result[resultIndex++] = new Geometry({\n      primitiveType: primitiveType,\n      geometryType: geometryType,\n      boundingSphere: boundingSphere,\n      boundingSphereCV: boundingSphereCV,\n      indices: indices,\n      attributes: attributes,\n      offsetAttribute: offsetAttribute,\n    });\n  }\n\n  return result;\n};\n\nfunction packInstancesForCombine(instances, transferableObjects) {\n  const length = instances.length;\n  const packedData = new Float64Array(1 + length * 19);\n  let count = 0;\n  packedData[count++] = length;\n  for (let i = 0; i < length; i++) {\n    const instance = instances[i];\n    Matrix4.pack(instance.modelMatrix, packedData, count);\n    count += Matrix4.packedLength;\n    if (defined(instance.attributes) && defined(instance.attributes.offset)) {\n      const values = instance.attributes.offset.value;\n      packedData[count] = values[0];\n      packedData[count + 1] = values[1];\n      packedData[count + 2] = values[2];\n    }\n    count += 3;\n  }\n  transferableObjects.push(packedData.buffer);\n\n  return packedData;\n}\n\nfunction unpackInstancesForCombine(data) {\n  const packedInstances = data;\n  const result = new Array(packedInstances[0]);\n  let count = 0;\n\n  let i = 1;\n  while (i < packedInstances.length) {\n    const modelMatrix = Matrix4.unpack(packedInstances, i);\n    let attributes;\n    i += Matrix4.packedLength;\n    if (defined(packedInstances[i])) {\n      attributes = {\n        offset: new OffsetGeometryInstanceAttribute(\n          packedInstances[i],\n          packedInstances[i + 1],\n          packedInstances[i + 2]\n        ),\n      };\n    }\n    i += 3;\n\n    result[count++] = {\n      modelMatrix: modelMatrix,\n      attributes: attributes,\n    };\n  }\n\n  return result;\n}\n\n/**\n * @private\n */\nPrimitivePipeline.packCombineGeometryParameters = function (\n  parameters,\n  transferableObjects\n) {\n  const createGeometryResults = parameters.createGeometryResults;\n  const length = createGeometryResults.length;\n\n  for (let i = 0; i < length; i++) {\n    transferableObjects.push(createGeometryResults[i].packedData.buffer);\n  }\n\n  return {\n    createGeometryResults: parameters.createGeometryResults,\n    packedInstances: packInstancesForCombine(\n      parameters.instances,\n      transferableObjects\n    ),\n    ellipsoid: parameters.ellipsoid,\n    isGeographic: parameters.projection instanceof GeographicProjection,\n    elementIndexUintSupported: parameters.elementIndexUintSupported,\n    scene3DOnly: parameters.scene3DOnly,\n    vertexCacheOptimize: parameters.vertexCacheOptimize,\n    compressVertices: parameters.compressVertices,\n    modelMatrix: parameters.modelMatrix,\n    createPickOffsets: parameters.createPickOffsets,\n  };\n};\n\n/**\n * @private\n */\nPrimitivePipeline.unpackCombineGeometryParameters = function (\n  packedParameters\n) {\n  const instances = unpackInstancesForCombine(packedParameters.packedInstances);\n  const createGeometryResults = packedParameters.createGeometryResults;\n  const length = createGeometryResults.length;\n  let instanceIndex = 0;\n\n  for (let resultIndex = 0; resultIndex < length; resultIndex++) {\n    const geometries = PrimitivePipeline.unpackCreateGeometryResults(\n      createGeometryResults[resultIndex]\n    );\n    const geometriesLength = geometries.length;\n    for (\n      let geometryIndex = 0;\n      geometryIndex < geometriesLength;\n      geometryIndex++\n    ) {\n      const geometry = geometries[geometryIndex];\n      const instance = instances[instanceIndex];\n      instance.geometry = geometry;\n      ++instanceIndex;\n    }\n  }\n\n  const ellipsoid = Ellipsoid.clone(packedParameters.ellipsoid);\n  const projection = packedParameters.isGeographic\n    ? new GeographicProjection(ellipsoid)\n    : new WebMercatorProjection(ellipsoid);\n\n  return {\n    instances: instances,\n    ellipsoid: ellipsoid,\n    projection: projection,\n    elementIndexUintSupported: packedParameters.elementIndexUintSupported,\n    scene3DOnly: packedParameters.scene3DOnly,\n    vertexCacheOptimize: packedParameters.vertexCacheOptimize,\n    compressVertices: packedParameters.compressVertices,\n    modelMatrix: Matrix4.clone(packedParameters.modelMatrix),\n    createPickOffsets: packedParameters.createPickOffsets,\n  };\n};\n\nfunction packBoundingSpheres(boundingSpheres) {\n  const length = boundingSpheres.length;\n  const bufferLength = 1 + (BoundingSphere.packedLength + 1) * length;\n  const buffer = new Float32Array(bufferLength);\n\n  let bufferIndex = 0;\n  buffer[bufferIndex++] = length;\n\n  for (let i = 0; i < length; ++i) {\n    const bs = boundingSpheres[i];\n    if (!defined(bs)) {\n      buffer[bufferIndex++] = 0.0;\n    } else {\n      buffer[bufferIndex++] = 1.0;\n      BoundingSphere.pack(boundingSpheres[i], buffer, bufferIndex);\n    }\n    bufferIndex += BoundingSphere.packedLength;\n  }\n\n  return buffer;\n}\n\nfunction unpackBoundingSpheres(buffer) {\n  const result = new Array(buffer[0]);\n  let count = 0;\n\n  let i = 1;\n  while (i < buffer.length) {\n    if (buffer[i++] === 1.0) {\n      result[count] = BoundingSphere.unpack(buffer, i);\n    }\n    ++count;\n    i += BoundingSphere.packedLength;\n  }\n\n  return result;\n}\n\n/**\n * @private\n */\nPrimitivePipeline.packCombineGeometryResults = function (\n  results,\n  transferableObjects\n) {\n  if (defined(results.geometries)) {\n    transferGeometries(results.geometries, transferableObjects);\n  }\n\n  const packedBoundingSpheres = packBoundingSpheres(results.boundingSpheres);\n  const packedBoundingSpheresCV = packBoundingSpheres(\n    results.boundingSpheresCV\n  );\n  transferableObjects.push(\n    packedBoundingSpheres.buffer,\n    packedBoundingSpheresCV.buffer\n  );\n\n  return {\n    geometries: results.geometries,\n    attributeLocations: results.attributeLocations,\n    modelMatrix: results.modelMatrix,\n    pickOffsets: results.pickOffsets,\n    offsetInstanceExtend: results.offsetInstanceExtend,\n    boundingSpheres: packedBoundingSpheres,\n    boundingSpheresCV: packedBoundingSpheresCV,\n  };\n};\n\n/**\n * @private\n */\nPrimitivePipeline.unpackCombineGeometryResults = function (packedResult) {\n  return {\n    geometries: packedResult.geometries,\n    attributeLocations: packedResult.attributeLocations,\n    modelMatrix: packedResult.modelMatrix,\n    pickOffsets: packedResult.pickOffsets,\n    offsetInstanceExtend: packedResult.offsetInstanceExtend,\n    boundingSpheres: unpackBoundingSpheres(packedResult.boundingSpheres),\n    boundingSpheresCV: unpackBoundingSpheres(packedResult.boundingSpheresCV),\n  };\n};\nexport default PrimitivePipeline;\n"], "names": ["OffsetGeometryInstanceAttribute", "x", "y", "z", "defaultValue", "this", "value", "Float32Array", "addGeometryBatchId", "geometry", "batchId", "attributes", "positionAttr", "position", "numberOfComponents", "values", "length", "componentsPerAttribute", "GeometryAttribute", "componentDatatype", "ComponentDatatype", "FLOAT", "j", "geometryPipeline", "parameters", "instances", "projection", "uintIndexSupport", "elementIndexUintSupported", "scene3DOnly", "vertexCacheOptimize", "compressVertices", "modelMatrix", "i", "primitiveType", "defined", "DeveloperError", "primitiveModelMatrix", "toWorld", "Matrix4", "equals", "GeometryPipeline", "transformToWorldCoordinates", "multiplyTransformation", "splitLongitude", "instance", "westHemisphereGeometry", "eastHemisphereGeometry", "addBatchIds", "reorderForPostVertexCache", "reorderForPreVertexCache", "geometries", "combineInstances", "name", "hasOwnProperty", "DOUBLE", "encodeAttribute", "name3D", "name2D", "projectTo2D", "boundingSphere", "boundingSphereCV", "BoundingSphere", "fromVertices", "position2D", "splitGeometries", "concat", "fitToUnsignedShortIndices", "createPickOffsets", "geometryName", "pickOffsets", "offset", "indexCount", "geometryIndex", "offsetIndex", "pickOffset", "count", "index", "indices", "push", "Object", "defineProperties", "prototype", "get", "normalize", "fromCartesian3", "Check", "toValue", "result", "PrimitivePipeline", "transferGeometry", "transferableObjects", "attribute", "buffer", "packInstancesForCombine", "packedData", "Float64Array", "pack", "<PERSON><PERSON><PERSON><PERSON>", "packBoundingSpheres", "boundingSpheres", "bufferLength", "bufferIndex", "bs", "unpackBoundingSpheres", "Array", "unpack", "combineGeometry", "attributeLocations", "offsetInstanceExtend", "hasOffset", "createAttributeLocations", "createInstancePickOffsets", "boundingSpheresCV", "offsetAttribute", "union", "packCreateGeometryResults", "items", "property", "countCreateGeometryResults", "stringTable", "stringHash", "validGeometry", "geometryType", "validBoundingSphere", "validBoundingSphereCV", "attributesToWrite", "q", "set", "indicesLength", "unpackCreateGeometryResults", "createGeometryResult", "packedGeometry", "resultIndex", "packedGeometryIndex", "undefined", "GeometryAttributes", "numAttributes", "createTypedArray", "valuesIndex", "numberOfVertices", "IndexDatatype", "Geometry", "packCombineGeometryParameters", "createGeometryResults", "packedInstances", "ellipsoid", "isGeographic", "GeographicProjection", "unpackCombineGeometryParameters", "packedParameters", "data", "unpackInstancesForCombine", "instanceIndex", "geometriesLength", "Ellipsoid", "clone", "WebMercatorProjection", "packCombineGeometryResults", "results", "transferGeometries", "packedBoundingSpheres", "packedBoundingSpheresCV", "unpackCombineGeometryResults", "packedResult"], "mappings": "oVAoBA,SAASA,EAAgCC,EAAGC,EAAGC,GAC7CF,EAAIG,EAAYA,aAACH,EAAG,GACpBC,EAAIE,EAAYA,aAACF,EAAG,GACpBC,EAAIC,EAAYA,aAACD,EAAG,GAOpBE,KAAKC,MAAQ,IAAIC,aAAa,CAACN,EAAGC,EAAGC,GACvC,CCqBA,SAASK,EAAmBC,EAAUC,GACpC,MAAMC,EAAaF,EAASE,WACtBC,EAAeD,EAAWE,SAC1BC,EACJF,EAAaG,OAAOC,OAASJ,EAAaK,uBAE5CN,EAAWD,QAAU,IAAIQ,oBAAkB,CACzCC,kBAAmBC,EAAiBA,kBAACC,MACrCJ,uBAAwB,EACxBF,OAAQ,IAAIR,aAAaO,KAG3B,MAAMC,EAASJ,EAAWD,QAAQK,OAClC,IAAK,IAAIO,EAAI,EAAGA,EAAIR,IAAsBQ,EACxCP,EAAOO,GAAKZ,CAEhB,CAmBA,SAASa,EAAiBC,GACxB,MAAMC,EAAYD,EAAWC,UACvBC,EAAaF,EAAWE,WACxBC,EAAmBH,EAAWI,0BAC9BC,EAAcL,EAAWK,YACzBC,EAAsBN,EAAWM,oBACjCC,EAAmBP,EAAWO,iBAC9BC,EAAcR,EAAWQ,YAE/B,IAAIC,EACAxB,EACAyB,EACAlB,EAASS,EAAUT,OAEvB,IAAKiB,EAAI,EAAGA,EAAIjB,IAAUiB,EACxB,GAAIE,EAAOA,QAACV,EAAUQ,GAAGxB,UAAW,CAClCyB,EAAgBT,EAAUQ,GAAGxB,SAASyB,cACtC,KACD,CAIH,IAAKD,EAAI,EAAGA,EAAIjB,IAAUiB,EACxB,GACEE,EAAAA,QAAQV,EAAUQ,GAAGxB,WACrBgB,EAAUQ,GAAGxB,SAASyB,gBAAkBA,EAExC,MAAM,IAAIE,EAAcA,eACtB,6DAUN,GA7GF,SACEX,EACAY,EACAR,GAEA,IAAIS,GAAWT,EACf,MAAMb,EAASS,EAAUT,OACzB,IAAIiB,EAEJ,IAAKK,GAAWtB,EAAS,EAAG,CAC1B,MAAMgB,EAAcP,EAAU,GAAGO,YAEjC,IAAKC,EAAI,EAAGA,EAAIjB,IAAUiB,EACxB,IAAKM,EAAOA,QAACC,OAAOR,EAAaP,EAAUQ,GAAGD,aAAc,CAC1DM,GAAU,EACV,KACD,CAEJ,CAED,GAAIA,EACF,IAAKL,EAAI,EAAGA,EAAIjB,IAAUiB,EACpBE,EAAOA,QAACV,EAAUQ,GAAGxB,WACvBgC,EAAAA,iBAAiBC,4BAA4BjB,EAAUQ,SAK3DM,EAAAA,QAAQI,uBACNN,EACAZ,EAAU,GAAGO,YACbK,EAGN,CAwEEK,CAA4BjB,EAAWO,EAAaH,IAG/CA,EACH,IAAKI,EAAI,EAAGA,EAAIjB,IAAUiB,EACpBE,EAAOA,QAACV,EAAUQ,GAAGxB,WACvBgC,EAAAA,iBAAiBG,eAAenB,EAAUQ,IAQhD,GAlEF,SAAqBR,GACnB,MAAMT,EAASS,EAAUT,OAEzB,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,IAAUiB,EAAG,CAC/B,MAAMY,EAAWpB,EAAUQ,GACvBE,EAAOA,QAACU,EAASpC,UACnBD,EAAmBqC,EAASpC,SAAUwB,GAEtCE,EAAOA,QAACU,EAASC,yBACjBX,EAAOA,QAACU,EAASE,0BAEjBvC,EAAmBqC,EAASC,uBAAwBb,GACpDzB,EAAmBqC,EAASE,uBAAwBd,GAEvD,CACH,CAgDEe,CAAYvB,GAGRK,EACF,IAAKG,EAAI,EAAGA,EAAIjB,IAAUiB,EAAG,CAC3B,MAAMY,EAAWpB,EAAUQ,GACvBE,EAAOA,QAACU,EAASpC,WACnBgC,EAAAA,iBAAiBQ,0BAA0BJ,EAASpC,UACpDgC,EAAAA,iBAAiBS,yBAAyBL,EAASpC,WAEnD0B,EAAOA,QAACU,EAASC,yBACjBX,EAAOA,QAACU,EAASE,0BAEjBN,EAAAA,iBAAiBQ,0BACfJ,EAASC,wBAEXL,EAAAA,iBAAiBS,yBACfL,EAASC,wBAGXL,EAAAA,iBAAiBQ,0BACfJ,EAASE,wBAEXN,EAAAA,iBAAiBS,yBACfL,EAASE,wBAGd,CAIH,IAAII,EAAaV,EAAAA,iBAAiBW,iBAAiB3B,GAGnD,IADAT,EAASmC,EAAWnC,OACfiB,EAAI,EAAGA,EAAIjB,IAAUiB,EAAG,CAC3BxB,EAAW0C,EAAWlB,GAGtB,MAAMtB,EAAaF,EAASE,WAC5B,GAAKkB,EAsCH,IAAK,MAAMwB,KAAQ1C,EAEfA,EAAW2C,eAAeD,IAC1B1C,EAAW0C,GAAMlC,oBAAsBC,EAAiBA,kBAACmC,QAEzDd,EAAAA,iBAAiBe,gBACf/C,EACA4C,EACAA,EAAO,SACPA,EAAO,cA9Cb,IAAK,MAAMA,KAAQ1C,EACjB,GACEA,EAAW2C,eAAeD,IAC1B1C,EAAW0C,GAAMlC,oBAAsBC,EAAiBA,kBAACmC,OACzD,CACA,MAAME,EAASJ,EAAO,KAChBK,EAASL,EAAO,KAGtBZ,EAAAA,iBAAiBkB,YACflD,EACA4C,EACAI,EACAC,EACAhC,GAEES,EAAOA,QAAC1B,EAASmD,iBAA4B,aAATP,IACtC5C,EAASoD,iBAAmBC,EAAAA,eAAeC,aACzCtD,EAASE,WAAWqD,WAAWjD,SAInC0B,EAAAA,iBAAiBe,gBACf/C,EACAgD,EACAA,EAAS,OACTA,EAAS,OAEXhB,EAAAA,iBAAiBe,gBACf/C,EACAiD,EACAA,EAAS,OACTA,EAAS,MAEZ,CAmBD3B,GACFU,mBAAiBV,iBAAiBtB,EAErC,CAED,IAAKkB,EAAkB,CAErB,IAAIsC,EAAkB,GAEtB,IADAjD,EAASmC,EAAWnC,OACfiB,EAAI,EAAGA,EAAIjB,IAAUiB,EACxBxB,EAAW0C,EAAWlB,GACtBgC,EAAkBA,EAAgBC,OAChCzB,EAAgBA,iBAAC0B,0BAA0B1D,IAI/C0C,EAAac,CACd,CAED,OAAOd,CACT,CAEA,SAASiB,EAAkB3C,EAAW4C,EAAclB,EAAYmB,GAC9D,IAAIC,EACAC,EACAC,EAEJ,MAAMC,EAAcJ,EAAYtD,OAAS,EACzC,GAAI0D,GAAe,EAAG,CACpB,MAAMC,EAAaL,EAAYI,GAC/BH,EAASI,EAAWJ,OAASI,EAAWC,MACxCH,EAAgBE,EAAWE,MAC3BL,EAAarB,EAAWsB,GAAeK,QAAQ9D,MACnD,MACIuD,EAAS,EACTE,EAAgB,EAChBD,EAAarB,EAAWsB,GAAeK,QAAQ9D,OAGjD,MAAMA,EAASS,EAAUT,OACzB,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,IAAUiB,EAAG,CAC/B,MACMxB,EADWgB,EAAUQ,GACDoC,GAC1B,IAAKlC,EAAAA,QAAQ1B,GACX,SAGF,MAAMmE,EAAQnE,EAASqE,QAAQ9D,OAE3BuD,EAASK,EAAQJ,IACnBD,EAAS,EACTC,EAAarB,IAAasB,GAAeK,QAAQ9D,QAGnDsD,EAAYS,KAAK,CACfF,MAAOJ,EACPF,OAAQA,EACRK,MAAOA,IAETL,GAAUK,CACX,CACH,CD9PAI,OAAOC,iBAAiBjF,EAAgCkF,UAAW,CAYjE/D,kBAAmB,CACjBgE,IAAK,WACH,OAAO/D,EAAAA,kBAAkBC,KAC1B,GAaHJ,uBAAwB,CACtBkE,IAAK,WACH,OAAO,CACR,GAeHC,UAAW,CACTD,IAAK,WACH,OAAO,CACR,KAULnF,EAAgCqF,eAAiB,SAAUd,GAKzD,OAHAe,EAAAA,MAAMnD,QAAQ,SAAUoC,GAGjB,IAAIvE,EAAgCuE,EAAOtE,EAAGsE,EAAOrE,EAAGqE,EAAOpE,EACxE,EAaAH,EAAgCuF,QAAU,SAAUhB,EAAQiB,GAY1D,OAVAF,EAAAA,MAAMnD,QAAQ,SAAUoC,GAGnBpC,EAAAA,QAAQqD,KACXA,EAAS,IAAIjF,aAAa,CAACgE,EAAOtE,EAAGsE,EAAOrE,EAAGqE,EAAOpE,KAGxDqF,EAAO,GAAKjB,EAAOtE,EACnBuF,EAAO,GAAKjB,EAAOrE,EACnBsF,EAAO,GAAKjB,EAAOpE,EACZqF,CACT,EC0LM,MAAAC,EAAoB,CAAG,EAiF7B,SAASC,EAAiBjF,EAAUkF,GAClC,MAAMhF,EAAaF,EAASE,WAC5B,IAAK,MAAM0C,KAAQ1C,EACjB,GAAIA,EAAW2C,eAAeD,GAAO,CACnC,MAAMuC,EAAYjF,EAAW0C,GAEzBlB,EAAAA,QAAQyD,IAAczD,EAAOA,QAACyD,EAAU7E,SAC1C4E,EAAoBZ,KAAKa,EAAU7E,OAAO8E,OAE7C,CAGC1D,EAAOA,QAAC1B,EAASqE,UACnBa,EAAoBZ,KAAKtE,EAASqE,QAAQe,OAE9C,CA4OA,SAASC,EAAwBrE,EAAWkE,GAC1C,MAAM3E,EAASS,EAAUT,OACnB+E,EAAa,IAAIC,aAAa,EAAa,GAAThF,GACxC,IAAI4D,EAAQ,EACZmB,EAAWnB,KAAW5D,EACtB,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,EAAQiB,IAAK,CAC/B,MAAMY,EAAWpB,EAAUQ,GAG3B,GAFAM,EAAOA,QAAC0D,KAAKpD,EAASb,YAAa+D,EAAYnB,GAC/CA,GAASrC,EAAOA,QAAC2D,aACb/D,EAAOA,QAACU,EAASlC,aAAewB,EAAOA,QAACU,EAASlC,WAAW4D,QAAS,CACvE,MAAMxD,EAAS8B,EAASlC,WAAW4D,OAAOjE,MAC1CyF,EAAWnB,GAAS7D,EAAO,GAC3BgF,EAAWnB,EAAQ,GAAK7D,EAAO,GAC/BgF,EAAWnB,EAAQ,GAAK7D,EAAO,EAChC,CACD6D,GAAS,CACV,CAGD,OAFAe,EAAoBZ,KAAKgB,EAAWF,QAE7BE,CACT,CA6GA,SAASI,EAAoBC,GAC3B,MAAMpF,EAASoF,EAAgBpF,OACzBqF,EAAe,GAAKvC,EAAcA,eAACoC,aAAe,GAAKlF,EACvD6E,EAAS,IAAItF,aAAa8F,GAEhC,IAAIC,EAAc,EAClBT,EAAOS,KAAiBtF,EAExB,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,IAAUiB,EAAG,CAC/B,MAAMsE,EAAKH,EAAgBnE,GACtBE,EAAAA,QAAQoE,IAGXV,EAAOS,KAAiB,EACxBxC,EAAcA,eAACmC,KAAKG,EAAgBnE,GAAI4D,EAAQS,IAHhDT,EAAOS,KAAiB,EAK1BA,GAAexC,EAAcA,eAACoC,YAC/B,CAED,OAAOL,CACT,CAEA,SAASW,EAAsBX,GAC7B,MAAML,EAAS,IAAIiB,MAAMZ,EAAO,IAChC,IAAIjB,EAAQ,EAER3C,EAAI,EACR,KAAOA,EAAI4D,EAAO7E,QACI,IAAhB6E,EAAO5D,OACTuD,EAAOZ,GAASd,EAAAA,eAAe4C,OAAOb,EAAQ5D,MAE9C2C,EACF3C,GAAK6B,EAAcA,eAACoC,aAGtB,OAAOV,CACT,CA5eAC,EAAkBkB,gBAAkB,SAAUnF,GAC5C,IAAI2B,EACAyD,EACJ,MAAMnF,EAAYD,EAAWC,UACvBT,EAASS,EAAUT,OACzB,IAAIsD,EAEAuC,EACAC,GAAY,EACZ9F,EAAS,IACXmC,EAAa5B,EAAiBC,GAC1B2B,EAAWnC,OAAS,IACtB4F,EAAqBnE,EAAgBA,iBAACsE,yBACpC5D,EAAW,IAET3B,EAAW4C,oBACbE,EA1CR,SAAmC7C,EAAW0B,GAC5C,MAAMmB,EAAc,GAcpB,OAbAF,EAAkB3C,EAAW,WAAY0B,EAAYmB,GACrDF,EACE3C,EACA,yBACA0B,EACAmB,GAEFF,EACE3C,EACA,yBACA0B,EACAmB,GAEKA,CACT,CA0BsB0C,CAA0BvF,EAAW0B,KAIrDhB,EAAAA,QAAQV,EAAU,GAAGd,aACrBwB,EAAAA,QAAQV,EAAU,GAAGd,WAAW4D,UAEhCsC,EAAuB,IAAIJ,MAAMzF,GACjC8F,GAAY,IAIhB,MAAMV,EAAkB,IAAIK,MAAMzF,GAC5BiG,EAAoB,IAAIR,MAAMzF,GACpC,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,IAAUiB,EAAG,CAC/B,MAAMY,EAAWpB,EAAUQ,GACrBxB,EAAWoC,EAASpC,SACtB0B,EAAAA,QAAQ1B,KACV2F,EAAgBnE,GAAKxB,EAASmD,eAC9BqD,EAAkBhF,GAAKxB,EAASoD,iBAC5BiD,IACFD,EAAqB5E,GAAKY,EAASpC,SAASyG,kBAIhD,MAAMnE,EAAyBF,EAASE,uBAClCD,EAAyBD,EAASC,uBACpCX,EAAOA,QAACY,IAA2BZ,EAAOA,QAACW,KAE3CX,EAAOA,QAACY,EAAuBa,iBAC/BzB,EAAOA,QAACW,EAAuBc,kBAE/BwC,EAAgBnE,GAAK6B,EAAAA,eAAeqD,MAClCpE,EAAuBa,eACvBd,EAAuBc,iBAIzBzB,EAAOA,QAACY,EAAuBc,mBAC/B1B,EAAOA,QAACW,EAAuBe,oBAE/BoD,EAAkBhF,GAAK6B,EAAAA,eAAeqD,MACpCpE,EAAuBc,iBACvBf,EAAuBe,mBAI9B,CAED,MAAO,CACLV,WAAYA,EACZnB,YAAaR,EAAWQ,YACxB4E,mBAAoBA,EACpBtC,YAAaA,EACbuC,qBAAsBA,EACtBT,gBAAiBA,EACjBa,kBAAmBA,EAEvB,EA8DAxB,EAAkB2B,0BAA4B,SAC5CC,EACA1B,GAEA,MAAMI,EAAa,IAAIC,aAvCzB,SAAoCqB,GAClC,IAAIzC,EAAQ,EACZ,MAAM5D,EAASqG,EAAMrG,OACrB,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,EAAQiB,IAAK,CAC/B,MAAMxB,EAAW4G,EAAMpF,GAGvB,KAFE2C,GAEGzC,EAAAA,QAAQ1B,GACX,SAGF,MAAME,EAAaF,EAASE,WAE5BiE,GACE,EACA,EAAId,EAAcA,eAACoC,cAClB/D,EAAOA,QAAC1B,EAASqE,SAAWrE,EAASqE,QAAQ9D,OAAS,GAEzD,IAAK,MAAMsG,KAAY3G,EAEnBA,EAAW2C,eAAegE,IAC1BnF,UAAQxB,EAAW2G,MAGnB1C,GAAS,EADSjE,EAAW2G,GACNvG,OAAOC,OAGnC,CAED,OAAO4D,CACT,CASsC2C,CAA2BF,IACzDG,EAAc,GACdC,EAAa,CAAA,EAEbzG,EAASqG,EAAMrG,OACrB,IAAI4D,EAAQ,EACZmB,EAAWnB,KAAW5D,EACtB,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,EAAQiB,IAAK,CAC/B,MAAMxB,EAAW4G,EAAMpF,GAEjByF,EAAgBvF,UAAQ1B,GAG9B,GAFAsF,EAAWnB,KAAW8C,EAAgB,EAAM,GAEvCA,EACH,SAGF3B,EAAWnB,KAAWnE,EAASyB,cAC/B6D,EAAWnB,KAAWnE,EAASkH,aAC/B5B,EAAWnB,KAAWxE,EAAAA,aAAaK,EAASyG,iBAAkB,GAE9D,MAAMU,EAAsBzF,EAAAA,QAAQ1B,EAASmD,gBAAkB,EAAM,EACrEmC,EAAWnB,KAAWgD,EAClBA,GACF9D,EAAcA,eAACmC,KAAKxF,EAASmD,eAAgBmC,EAAYnB,GAG3DA,GAASd,EAAcA,eAACoC,aAExB,MAAM2B,EAAwB1F,EAAAA,QAAQ1B,EAASoD,kBAC3C,EACA,EACJkC,EAAWnB,KAAWiD,EAClBA,GACF/D,EAAcA,eAACmC,KAAKxF,EAASoD,iBAAkBkC,EAAYnB,GAG7DA,GAASd,EAAcA,eAACoC,aAExB,MAAMvF,EAAaF,EAASE,WACtBmH,EAAoB,GAC1B,IAAK,MAAMR,KAAY3G,EAEnBA,EAAW2C,eAAegE,IAC1BnF,UAAQxB,EAAW2G,MAEnBQ,EAAkB/C,KAAKuC,GAClBnF,EAAOA,QAACsF,EAAWH,MACtBG,EAAWH,GAAYE,EAAYxG,OACnCwG,EAAYzC,KAAKuC,KAKvBvB,EAAWnB,KAAWkD,EAAkB9G,OACxC,IAAK,IAAI+G,EAAI,EAAGA,EAAID,EAAkB9G,OAAQ+G,IAAK,CACjD,MAAM1E,EAAOyE,EAAkBC,GACzBnC,EAAYjF,EAAW0C,GAC7B0C,EAAWnB,KAAW6C,EAAWpE,GACjC0C,EAAWnB,KAAWgB,EAAUzE,kBAChC4E,EAAWnB,KAAWgB,EAAU3E,uBAChC8E,EAAWnB,KAAWgB,EAAUR,UAAY,EAAI,EAChDW,EAAWnB,KAAWgB,EAAU7E,OAAOC,OACvC+E,EAAWiC,IAAIpC,EAAU7E,OAAQ6D,GACjCA,GAASgB,EAAU7E,OAAOC,MAC3B,CAED,MAAMiH,EAAgB9F,EAAAA,QAAQ1B,EAASqE,SACnCrE,EAASqE,QAAQ9D,OACjB,EACJ+E,EAAWnB,KAAWqD,EAElBA,EAAgB,IAClBlC,EAAWiC,IAAIvH,EAASqE,QAASF,GACjCA,GAASqD,EAEZ,CAID,OAFAtC,EAAoBZ,KAAKgB,EAAWF,QAE7B,CACL2B,YAAaA,EACbzB,WAAYA,EAEhB,EAKAN,EAAkByC,4BAA8B,SAC9CC,GAEA,MAAMX,EAAcW,EAAqBX,YACnCY,EAAiBD,EAAqBpC,WAE5C,IAAI9D,EACJ,MAAMuD,EAAS,IAAIiB,MAAM2B,EAAe,IACxC,IAAIC,EAAc,EAEdC,EAAsB,EAC1B,KAAOA,EAAsBF,EAAepH,QAAQ,CAElD,KADwD,IAA1CoH,EAAeE,MACjB,CACV9C,EAAO6C,UAAiBE,EACxB,QACD,CAED,MAAMrG,EAAgBkG,EAAeE,KAC/BX,EAAeS,EAAeE,KACpC,IAKI1E,EACAC,EANAqD,EAAkBkB,EAAeE,MACZ,IAArBpB,IACFA,OAAkBqB,GAMkD,IAA1CH,EAAeE,OAEzC1E,EAAiBE,EAAcA,eAAC4C,OAC9B0B,EACAE,IAIJA,GAAuBxE,EAAcA,eAACoC,aAYtC,IAAIlF,EACAD,EACAE,EAZoE,IAA1CmH,EAAeE,OAE3CzE,EAAmBC,EAAcA,eAAC4C,OAChC0B,EACAE,IAIJA,GAAuBxE,EAAcA,eAACoC,aAKtC,MAAMvF,EAAa,IAAI6H,EAAAA,mBACjBC,EAAgBL,EAAeE,KACrC,IAAKrG,EAAI,EAAGA,EAAIwG,EAAexG,IAAK,CAClC,MAAMoB,EAAOmE,EAAYY,EAAeE,MAClCnH,EAAoBiH,EAAeE,KACzCrH,EAAyBmH,EAAeE,KACxC,MAAMlD,EAAsD,IAA1CgD,EAAeE,KAEjCtH,EAASoH,EAAeE,KACxBvH,EAASK,EAAAA,kBAAkBsH,iBAAiBvH,EAAmBH,GAC/D,IAAK,IAAI2H,EAAc,EAAGA,EAAc3H,EAAQ2H,IAC9C5H,EAAO4H,GAAeP,EAAeE,KAGvC3H,EAAW0C,GAAQ,IAAInC,oBAAkB,CACvCC,kBAAmBA,EACnBF,uBAAwBA,EACxBmE,UAAWA,EACXrE,OAAQA,GAEX,CAED,IAAI+D,EAGJ,GAFA9D,EAASoH,EAAeE,KAEpBtH,EAAS,EAAG,CACd,MAAM4H,EAAmB7H,EAAOC,OAASC,EAEzC,IADA6D,EAAU+D,EAAAA,cAAcH,iBAAiBE,EAAkB5H,GACtDiB,EAAI,EAAGA,EAAIjB,EAAQiB,IACtB6C,EAAQ7C,GAAKmG,EAAeE,IAE/B,CAED9C,EAAO6C,KAAiB,IAAIS,WAAS,CACnC5G,cAAeA,EACfyF,aAAcA,EACd/D,eAAgBA,EAChBC,iBAAkBA,EAClBiB,QAASA,EACTnE,WAAYA,EACZuG,gBAAiBA,GAEpB,CAED,OAAO1B,CACT,EAyDAC,EAAkBsD,8BAAgC,SAChDvH,EACAmE,GAEA,MAAMqD,EAAwBxH,EAAWwH,sBACnChI,EAASgI,EAAsBhI,OAErC,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,EAAQiB,IAC1B0D,EAAoBZ,KAAKiE,EAAsB/G,GAAG8D,WAAWF,QAG/D,MAAO,CACLmD,sBAAuBxH,EAAWwH,sBAClCC,gBAAiBnD,EACftE,EAAWC,UACXkE,GAEFuD,UAAW1H,EAAW0H,UACtBC,aAAc3H,EAAWE,sBAAsB0H,EAAoBA,qBACnExH,0BAA2BJ,EAAWI,0BACtCC,YAAaL,EAAWK,YACxBC,oBAAqBN,EAAWM,oBAChCC,iBAAkBP,EAAWO,iBAC7BC,YAAaR,EAAWQ,YACxBoC,kBAAmB5C,EAAW4C,kBAElC,EAKAqB,EAAkB4D,gCAAkC,SAClDC,GAEA,MAAM7H,EAnER,SAAmC8H,GACjC,MAAMN,EAAkBM,EAClB/D,EAAS,IAAIiB,MAAMwC,EAAgB,IACzC,IAAIrE,EAAQ,EAER3C,EAAI,EACR,KAAOA,EAAIgH,EAAgBjI,QAAQ,CACjC,MAAMgB,EAAcO,EAAOA,QAACmE,OAAOuC,EAAiBhH,GACpD,IAAItB,EACJsB,GAAKM,EAAOA,QAAC2D,aACT/D,UAAQ8G,EAAgBhH,MAC1BtB,EAAa,CACX4D,OAAQ,IAAIvE,EACViJ,EAAgBhH,GAChBgH,EAAgBhH,EAAI,GACpBgH,EAAgBhH,EAAI,MAI1BA,GAAK,EAELuD,EAAOZ,KAAW,CAChB5C,YAAaA,EACbrB,WAAYA,EAEf,CAED,OAAO6E,CACT,CAuCoBgE,CAA0BF,EAAiBL,iBACvDD,EAAwBM,EAAiBN,sBACzChI,EAASgI,EAAsBhI,OACrC,IAAIyI,EAAgB,EAEpB,IAAK,IAAIpB,EAAc,EAAGA,EAAcrH,EAAQqH,IAAe,CAC7D,MAAMlF,EAAasC,EAAkByC,4BACnCc,EAAsBX,IAElBqB,EAAmBvG,EAAWnC,OACpC,IACE,IAAIyD,EAAgB,EACpBA,EAAgBiF,EAChBjF,IACA,CACA,MAAMhE,EAAW0C,EAAWsB,GACXhD,EAAUgI,GAClBhJ,SAAWA,IAClBgJ,CACH,CACF,CAED,MAAMP,EAAYS,EAASA,UAACC,MAAMN,EAAiBJ,WAKnD,MAAO,CACLzH,UAAWA,EACXyH,UAAWA,EACXxH,WAPiB4H,EAAiBH,aAChC,IAAIC,EAAAA,qBAAqBF,GACzB,IAAIW,EAAAA,sBAAsBX,GAM5BtH,0BAA2B0H,EAAiB1H,0BAC5CC,YAAayH,EAAiBzH,YAC9BC,oBAAqBwH,EAAiBxH,oBACtCC,iBAAkBuH,EAAiBvH,iBACnCC,YAAaO,EAAOA,QAACqH,MAAMN,EAAiBtH,aAC5CoC,kBAAmBkF,EAAiBlF,kBAExC,EA2CAqB,EAAkBqE,2BAA6B,SAC7CC,EACApE,GAEIxD,EAAOA,QAAC4H,EAAQ5G,aAxZtB,SAA4BA,EAAYwC,GACtC,MAAM3E,EAASmC,EAAWnC,OAC1B,IAAK,IAAIiB,EAAI,EAAGA,EAAIjB,IAAUiB,EAC5ByD,EAAiBvC,EAAWlB,GAAI0D,EAEpC,CAoZIqE,CAAmBD,EAAQ5G,WAAYwC,GAGzC,MAAMsE,EAAwB9D,EAAoB4D,EAAQ3D,iBACpD8D,EAA0B/D,EAC9B4D,EAAQ9C,mBAOV,OALAtB,EAAoBZ,KAClBkF,EAAsBpE,OACtBqE,EAAwBrE,QAGnB,CACL1C,WAAY4G,EAAQ5G,WACpByD,mBAAoBmD,EAAQnD,mBAC5B5E,YAAa+H,EAAQ/H,YACrBsC,YAAayF,EAAQzF,YACrBuC,qBAAsBkD,EAAQlD,qBAC9BT,gBAAiB6D,EACjBhD,kBAAmBiD,EAEvB,EAKAzE,EAAkB0E,6BAA+B,SAAUC,GACzD,MAAO,CACLjH,WAAYiH,EAAajH,WACzByD,mBAAoBwD,EAAaxD,mBACjC5E,YAAaoI,EAAapI,YAC1BsC,YAAa8F,EAAa9F,YAC1BuC,qBAAsBuD,EAAavD,qBACnCT,gBAAiBI,EAAsB4D,EAAahE,iBACpDa,kBAAmBT,EAAsB4D,EAAanD,mBAE1D"}