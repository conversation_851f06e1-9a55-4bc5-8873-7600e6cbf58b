/* eslint-disable */
import GaussKrugerTransform from '../../Core/GaussKrugerTransform';
import { CONSTANT_6_DEGREES, createLabels, createLineGraphics, LABEL_OPTIONS, isCenterEqual } from './GraticuleSettings.js';
import {
  Cartesian2,
  Cartesian3,
  Cartographic,
  Color,
  defined,
  defaultValue,
  destroyObject,
  CustomPolylineCollection,
  PrimitiveCollection,
  OriginLabelCollection,
  Rectangle,
  SceneMode,
  Math as CesiumMath
} from 'cesium';

const { abs } = Math;

export default class CustomMeasuredGraticule {
  /**
   * Custom Measured Graticule
   * @param {object} options
   * @param {Viewer} options.viewer
   * @param {Number[]} options.center [lon,lat]
   * @param {Number | undefined} options.scale grid scale
   * @param {Color | undefined} options.color the color of the graticule
   * @param {Color | undefined} options.color2D SceneMode color
   * @param {LabelGraphics | undefined} options.labelOptions 标签样式
   * @param {Number | undefined} options.gap 标签显示间隔
   * @param {Number | undefined} options.xRange graticule 在x轴方向上的范围
   * @param {Number | undefined} options.yRange graticule 在y轴方向上的范围
   * @param {Number | undefined} options.labelOffset 标签显示偏移值
   */
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    this._viewer = options.viewer;
    this._center = options.center;
    this._scale = defaultValue(options.scale, 1000);
    this._color = defaultValue(options.color, Color.WHITE.withAlpha(0.9));
    this._color2D = defaultValue(options.color2D, Color.GRAY.withAlpha(0.8));
    this._labelOptions = {
      ...LABEL_OPTIONS,
      ...options.labelOptions
    };
    this._gap = defaultValue(options.gap, 1);
    this._xRange = defaultValue(options.xRange, 50);
    this._yRange = defaultValue(options.xRange, 25);
    this._labelOffset = defaultValue(options.labelOffset, 0);

    this._zoneWide = 6;
    this._gaussTransform = new GaussKrugerTransform('CGCS2000');
    this._primitives = this._viewer.scene.primitives.add(new PrimitiveCollection());
    this._lines = this._primitives.add(new CustomPolylineCollection({ disableDepthTest: true }));
    this._labels = this._primitives.add(new OriginLabelCollection());
    this._show = true;

    this._viewer._measuredGraticule = this;
    this.init();
  }

  get show() {
    return this._show;
  }

  set show(value) {
    if (this._show === value) {
      return;
    }

    this._show = value;
    this._primitives.show = value;
  }

  set center(value) {
    if (!defined(value)) {
      return;
    }
    if (isCenterEqual(this._center, value)) {
      return;
    }
    this._center = value;
    this._getExtentView();
    this.create();
  }

  set scale(value) {
    if (this._scale === value) {
      return;
    }
    this._scale = value;
    this._getExtentView();
    this.create();
  }

  changeSceneMode() {
    const lines = this._lines._polylines;
    const color = this._viewer.scene.mode === SceneMode.SCENE3D ? this._color : this._color2D;
    if (lines.length > 0) {
      lines.forEach((item) => {
        item.material.uniforms.color = color;
      });
    }
  }

  init() {
    const scene = this._viewer.scene;
    this._cameraListener = scene.camera.changed.addEventListener(() => {
      // const {west, east, north, south} = this._getScreenViewRange();
      // console.log('west', CesiumMath.toDegrees(west))
      // console.log('east', CesiumMath.toDegrees(east))
      // console.log('north', CesiumMath.toDegrees(north))
      // console.log('south', CesiumMath.toDegrees(south))
      //
      // if (defined(west) && defined(east) && defined(north) && defined(south)) {
      //     const intersect = Rectangle.intersection(new Rectangle(west, south, east, north),
      //         Rectangle.fromDegrees(this._rectangle.west, this._rectangle.south, this._rectangle.east, this._rectangle.north),
      //         new Rectangle());
      //     console.log(intersect)
      // }
      // label show
      // this.update();
      // console.log(this._rectangle)
    });
  }

  update() {
    const lines = this._lines._polylines;
    if (lines.length === 0) {
      return;
    }
    const scene = this._viewer.scene;
    const labels = this._labels._labels;
    let cart0 = scene.cartesianToCanvasCoordinates(labels[0].position);
    let cart1 = scene.cartesianToCanvasCoordinates(labels[1].position);
    if (defined(cart0) && defined(cart1)) {
      this._labels.show = Cartesian2.distance(cart0, cart1) >= 100;
    } else {
      this._labels.show = true;
    }
    cart0 = scene.cartesianToCanvasCoordinates(lines[0].positions[0]);
    cart1 = scene.cartesianToCanvasCoordinates(lines[0].positions[1]);
    if (defined(cart0) && defined(cart1)) {
      this._lines.show = Cartesian2.distance(cart0, cart1) >= 4;
    } else {
      this._lines.show = true;
    }
  }

  create() {
    this._lines.removeAll();
    this._labels.removeAll();
    const zoneWide = this._zoneWide;
    const { west, east, north, south } = this._getExtentView();
    let westCenter = parseInt(abs(west) / zoneWide + 1) * zoneWide - 3;
    westCenter = west > 0 ? westCenter : -westCenter;
    let eastCenter = parseInt(abs(east) / zoneWide + 1) * zoneWide - 3;
    eastCenter = east > 0 ? eastCenter : -eastCenter;

    switch (eastCenter - westCenter) {
      case 24:
        this._drawLines(westCenter, west, westCenter + CONSTANT_6_DEGREES, north, south);
        this._drawLines(westCenter + 6, westCenter + 3, westCenter + 6 + CONSTANT_6_DEGREES, north, south);
        this._drawLines(westCenter + 12, westCenter + 9, westCenter + 12 + CONSTANT_6_DEGREES, north, south);
        this._drawLines(westCenter + 18, westCenter + 15, westCenter + 18 + CONSTANT_6_DEGREES, north, south);
        this._drawLines(eastCenter, eastCenter - 3, east, north, south);
        break;
      case 18:
        this._drawLines(westCenter, west, westCenter + CONSTANT_6_DEGREES, north, south);
        this._drawLines(westCenter + 6, westCenter + 3, westCenter + 6 + CONSTANT_6_DEGREES, north, south);
        this._drawLines(westCenter + 12, westCenter + 9, westCenter + 12 + CONSTANT_6_DEGREES, north, south);
        this._drawLines(eastCenter, eastCenter - 3, east, north, south);
        break;
      case 12:
        this._drawLines(westCenter, west, westCenter + CONSTANT_6_DEGREES, north, south);
        this._drawLines(westCenter + 6, westCenter + 3, westCenter + 6 + CONSTANT_6_DEGREES, north, south);
        this._drawLines(eastCenter, eastCenter - 3, east, north, south);
        break;
      case 6:
        this._drawLines(westCenter, west, westCenter + CONSTANT_6_DEGREES, north, south);
        this._drawLines(eastCenter, westCenter + 3, east, north, south);
        break;
      case 0:
        this._drawLines(westCenter, west, east, north, south);
        break;
    }
    // this.update();
  }

  _drawLines(centerMeridian, west, east, north, south) {
    let westTemp = centerMeridian - 3;
    westTemp = west < westTemp ? westTemp : west;
    let eastTemp = centerMeridian + CONSTANT_6_DEGREES;
    eastTemp = east > eastTemp ? eastTemp : east;

    const { x: xMin, y: yMin } = this._gaussTransform._getXY(westTemp, south, centerMeridian);
    const { x, y } = this._gaussTransform._getXY(eastTemp, north, centerMeridian);

    const scale = this._scale;
    const startX = (parseInt(xMin / scale) - 1) * scale;
    const endX = (parseInt(x / scale) + 1) * scale;
    const startY = (parseInt(yMin / scale) - 1) * scale;
    const endY = (parseInt(y / scale) + 1) * scale;

    const color = this._viewer.scene.mode === SceneMode.SCENE3D ? this._color : this._color2D;

    const offset = this._labelOffset;
    let xyPositions,
      positions,
      labelIndex,
      countX = 0,
      countY = 0;

    for (let i = startX; i <= endX; i += scale) {
      positions = [];
      xyPositions = [];

      for (let j = startY; j <= endY; j += scale) {
        const { longitude, latitude } = this._gaussTransform._getLonLat(i, j, centerMeridian);
        positions.push(Cartesian3.fromDegrees(longitude, latitude));
        xyPositions.push([i, j]);
      }
      this._lines.add(createLineGraphics(positions, color));
      if (countX % (this._gap + 1) === 0) {
        labelIndex = positions.length - 1 - offset;
        if (xyPositions[labelIndex]) {
          createLabels(this, positions[labelIndex], `X: ${xyPositions[labelIndex][0]}`);
        }
      }
      countX++;
    }

    for (let j = startY; j <= endY; j += scale) {
      positions = [];
      xyPositions = [];
      for (let i = startX; i <= endX; i += scale) {
        const { longitude, latitude } = this._gaussTransform._getLonLat(i, j, centerMeridian);
        positions.push(Cartesian3.fromDegrees(longitude, latitude));
        xyPositions.push([i, j]);
      }
      this._lines.add(createLineGraphics(positions, color));
      if (countY % (this._gap + 1) === 0) {
        labelIndex = offset + 1;
        if (xyPositions[labelIndex]) {
          createLabels(this, positions[labelIndex], `Y: ${xyPositions[labelIndex][1]}`, false);
        }
      }
      countY++;
    }
  }

  _getExtentView() {
    const [longitude, latitude] = this._center;
    const point = turf.point([longitude, latitude]);
    const westPoint = turf.destination(point, (this._xRange * this._scale) / 2000, -90);
    const eastPoint = turf.destination(point, (this._xRange * this._scale) / 2000, 90);
    const northPoint = turf.destination(point, (this._yRange * this._scale) / 2000, 0);
    const southPoint = turf.destination(point, (this._yRange * this._scale) / 2000, 180);
    this._rectangle = {
      west: westPoint.geometry.coordinates[0],
      north: northPoint.geometry.coordinates[1],
      east: eastPoint.geometry.coordinates[0],
      south: southPoint.geometry.coordinates[1]
    };
    return this._rectangle;
  }

  _getScreenViewRange() {
    const camera = this._viewer.scene.camera;
    const canvas = this._viewer.scene.canvas;
    const ellipsoid = this._viewer.scene.globe.ellipsoid;
    const offsetX = 40,
      offsetY = 20;
    const corners = {
      north: camera.pickEllipsoid(new Cartesian2(canvas.clientWidth / 2, offsetY), ellipsoid),
      south: camera.pickEllipsoid(new Cartesian2(canvas.clientWidth / 2, canvas.clientHeight - offsetY), ellipsoid),
      west: camera.pickEllipsoid(new Cartesian2(offsetX, canvas.clientWidth / 2), ellipsoid),
      east: camera.pickEllipsoid(new Cartesian2(canvas.clientHeight - offsetX, canvas.clientWidth / 2), ellipsoid)
    };
    return {
      north: corners.north ? Cartographic.fromCartesian(corners.north).latitude : undefined,
      south: corners.south ? Cartographic.fromCartesian(corners.south).latitude : undefined,
      west: corners.west ? Cartographic.fromCartesian(corners.west).longitude : undefined,
      east: corners.east ? Cartographic.fromCartesian(corners.east).longitude : undefined
    };
  }

  isDestroyed() {
    return false;
  }

  destroy() {
    this._cameraListener();
    this._viewer.scene.primitives.remove(this._primitives);
    delete this._viewer._measuredGraticule;
    return destroyObject(this);
  }
}
