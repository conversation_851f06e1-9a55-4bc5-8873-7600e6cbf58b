/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/03/21 14:45:20
 * @description CornicSensor Tests
 * */
import CustomSensorPrimitive from './CustomSensorPrimitive.js';
import removePrimitive from './removePrimitive.js';

const { AssociativeArray, Cartesian3, Color, defined, destroyObject, DeveloperError, Matrix3, Matrix4, Quaternion, Spherical, MaterialProperty, Property } = Cesium;

const CesiumMath = Cesium.Math;

const defaultIntersectionColor = Color.WHITE;
const defaultIntersectionWidth = 1.0;
const defaultRadius = Number.POSITIVE_INFINITY;

const matrix3Scratch = new Matrix3();
const cachedPosition = new Cartesian3();
const cachedOrientation = new Quaternion();

function assignSpherical(index, array, clock, cone) {
  let spherical = array[index];
  if (!defined(spherical)) {
    array[index] = spherical = new Spherical();
  }
  spherical.clock = clock;
  spherical.cone = cone;
  spherical.magnitude = 1.0;
}

function computeDirections(primitive, minimumClockAngle, maximumClockAngle, innerHalfAngle, outerHalfAngle) {
  const directions = primitive.directions;
  let angle;
  let i = 0;
  const angleStep = CesiumMath.toRadians(2.0);
  if (minimumClockAngle === 0.0 && maximumClockAngle === CesiumMath.TWO_PI) {
    // No clock angle limits, so this is just a circle.
    // There might be a hole but we're ignoring it for now.
    for (angle = 0.0; angle < CesiumMath.TWO_PI; angle += angleStep) {
      assignSpherical(i++, directions, angle, outerHalfAngle);
    }
  } else {
    // There are clock angle limits.
    for (angle = minimumClockAngle; angle < maximumClockAngle; angle += angleStep) {
      assignSpherical(i++, directions, angle, outerHalfAngle);
    }
    assignSpherical(i++, directions, maximumClockAngle, outerHalfAngle);
    if (innerHalfAngle) {
      for (angle = maximumClockAngle; angle > minimumClockAngle; angle -= angleStep) {
        assignSpherical(i++, directions, angle, innerHalfAngle);
      }
      assignSpherical(i++, directions, minimumClockAngle, innerHalfAngle);
    } else {
      assignSpherical(i++, directions, maximumClockAngle, 0.0);
    }
  }
  directions.length = i;
  primitive.directions = directions;
}

/**
 * A {@link Visualizer} which maps {@link Entity#conicSensor} to a {@link ConicSensor}.
 * @alias ConicSensorVisualizer
 * @constructor
 *
 * @param {Scene} scene The scene the primitives will be rendered in.
 * @param {EntityCollection} entityCollection The entityCollection to visualize.
 */
export default function ConicSensorVisualizer(scene, entityCollection) {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(scene)) {
    throw new DeveloperError('scene is required.');
  }
  if (!defined(entityCollection)) {
    throw new DeveloperError('entityCollection is required.');
  }
  // >>includeEnd('debug');

  entityCollection.collectionChanged.addEventListener(ConicSensorVisualizer.prototype._onCollectionChanged, this);

  this._scene = scene;
  this._primitives = scene.primitives;
  this._entityCollection = entityCollection;
  this._hash = {};
  this._entitiesToVisualize = new AssociativeArray();

  this._onCollectionChanged(entityCollection, entityCollection.values, [], []);
}

/**
 * Updates the primitives created by this visualizer to match their
 * Entity counterpart at the given time.
 *
 * @param {JulianDate} time The time to update to.
 * @returns {Boolean} This function always returns true.
 */
ConicSensorVisualizer.prototype.update = function (time) {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(time)) {
    throw new DeveloperError('time is required.');
  }
  // >>includeEnd('debug');

  const entities = this._entitiesToVisualize.values;
  const hash = this._hash;
  const primitives = this._primitives;

  for (let i = 0, len = entities.length; i < len; i++) {
    const entity = entities[i];
    const conicSensorGraphics = entity._conicSensor;

    let position;
    let orientation;
    let data = hash[entity.id];
    let show = entity.isAvailable(time) && Property.getValueOrDefault(conicSensorGraphics._show, time, true);

    if (show) {
      position = Property.getValueOrUndefined(entity._position, time, cachedPosition);
      orientation = Property.getValueOrUndefined(entity._orientation, time, cachedOrientation);
      show = defined(position) && defined(orientation); 
    }

    if (!show) {
      // don't bother creating or updating anything else
      if (defined(data)) {
        data.primitive.show = false;
      }
      continue;
    }

    let primitive = defined(data) ? data.primitive : undefined;
    if (!defined(primitive)) {
      primitive = new CustomSensorPrimitive();
      primitive.id = entity;
      primitives.add(primitive);

      data = {
        primitive: primitive,
        position: undefined,
        orientation: undefined,
        minimumClockAngle: undefined,
        maximumClockAngle: undefined,
        innerHalfAngle: undefined,
        outerHalfAngle: undefined,
      };
      hash[entity.id] = data;
    }
    if (!Cartesian3.equals(position, data.position) || !Quaternion.equals(orientation, data.orientation)) {
      Matrix4.fromRotationTranslation(Matrix3.fromQuaternion(orientation, matrix3Scratch), position, primitive.modelMatrix);
      data.position = Cartesian3.clone(position, data.position);
      data.orientation = Quaternion.clone(orientation, data.orientation);
    }

    primitive.show = true;

    const minimumClockAngle = Property.getValueOrDefault(conicSensorGraphics._minimumClockAngle, time, 0);
    const maximumClockAngle = Property.getValueOrDefault(conicSensorGraphics._maximumClockAngle, time, CesiumMath.TWO_PI);
    const innerHalfAngle = Property.getValueOrDefault(conicSensorGraphics._innerHalfAngle, time, 0);
    const outerHalfAngle = Property.getValueOrDefault(conicSensorGraphics._outerHalfAngle, time, Math.PI);

    if (
      minimumClockAngle !== data.minimumClockAngle ||
      maximumClockAngle !== data.maximumClockAngle ||
      innerHalfAngle !== data.innerHalfAngle ||
      outerHalfAngle !== data.outerHalfAngle
    ) {
      computeDirections(primitive, minimumClockAngle, maximumClockAngle, innerHalfAngle, outerHalfAngle);
      data.innerHalfAngle = innerHalfAngle;
      data.maximumClockAngle = maximumClockAngle;
      data.outerHalfAngle = outerHalfAngle;
      data.minimumClockAngle = minimumClockAngle;
    }

    primitive.radius = Property.getValueOrDefault(conicSensorGraphics._radius, time, defaultRadius);
    primitive.lateralSurfaceMaterial = MaterialProperty.getValue(time, conicSensorGraphics._lateralSurfaceMaterial, primitive.lateralSurfaceMaterial);
    primitive.intersectionColor = Property.getValueOrClonedDefault(conicSensorGraphics._intersectionColor, time, defaultIntersectionColor, primitive.intersectionColor);
    primitive.intersectionWidth = Property.getValueOrDefault(conicSensorGraphics._intersectionWidth, time, defaultIntersectionWidth);
  }
  return true;
};

/**
 * Returns true if this object was destroyed; otherwise, false.
 *
 * @returns {Boolean} True if this object was destroyed; otherwise, false.
 */
ConicSensorVisualizer.prototype.isDestroyed = function () {
  return false;
};

/**
 * Removes and destroys all primitives created by this instance.
 */
ConicSensorVisualizer.prototype.destroy = function () {
  const entities = this._entitiesToVisualize.values;
  const hash = this._hash;
  const primitives = this._primitives;
  for (let i = entities.length - 1; i > -1; i--) {
    removePrimitive(entities[i], hash, primitives);
  }
  return destroyObject(this);
};

/**
 * @private
 */
ConicSensorVisualizer.prototype._onCollectionChanged = function (entityCollection, added, removed, changed) {
  let i;
  let entity;
  const entities = this._entitiesToVisualize;
  const hash = this._hash;
  const primitives = this._primitives;

  for (i = added.length - 1; i > -1; i--) {
    entity = added[i];
    if (defined(entity._conicSensor) && defined(entity._position) && defined(entity._orientation)) {
      entities.set(entity.id, entity);
    }
  }

  for (i = changed.length - 1; i > -1; i--) {
    entity = changed[i];
    if (defined(entity._conicSensor) && defined(entity._position) && defined(entity._orientation)) {
      entities.set(entity.id, entity);
    } else {
      removePrimitive(entity, hash, primitives);
      entities.remove(entity.id);
    }
  }

  for (i = removed.length - 1; i > -1; i--) {
    entity = removed[i];
    removePrimitive(entity, hash, primitives);
    entities.remove(entity.id);
  }
};
