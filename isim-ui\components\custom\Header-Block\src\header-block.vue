<!--
* @Author: wll
* @Date: 2023/4/21 13:56
* @Version: 1.0
* @Content: 带有标题的内容块组件
-->
<template>
  <div class="yt-block">
    <div class="yt-block-header">
      <div class="yt-block-header__title">
        <span>{{ title }}</span>
        <span v-show="badgeText" class="rt-badge">{{ badgeText }}</span>
        <slot name="subTitle"></slot>
      </div>
      <div class="yt-block-header__line"></div>
      <div v-if="!$slots.right" class="yt-block-header__modification">
        <!--        <rt-three-points />-->
      </div>
      <slot name="right"></slot>
    </div>
    <div class="yt-block-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimBlock'
};
</script>
<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  badgeText: [String, Number],
  hasFooter: {
    type: Boolean,
    default: true
  }
});
</script>

<style scoped lang="less">
.yt-block {
  width: 100%;
  .square-point {
    width: 6px;
    height: 6px;
    background: rgba(var(--primary-color-val), 0.3);
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &__title {
      position: relative;
      padding-left: 6px;
      color: var(--text-color);
      font-weight: 700;
      font-size: 14px;

      &::before {
        position: absolute;
        z-index: 9;
        left: 0;
        top: 0px;
        bottom: 0;
        content: '';
        margin: auto;
        width: 2px;
        height: 12px;
        background: var(--primary-color);
      }

      /*&-badge {
          margin-left: 8px;
          padding: 0 1px;
          position: relative;
          min-width: 16px;
          height: 16px;
          text-align: center;
          line-height: 16px;
          background: rgba(var(--primary-color-val), 0.2);
          display: inline-block;
          box-sizing: border-box;
          font-size: 12px;
          font-weight: 400;
          color: var(--primary-color);
          &::before {
            position: absolute;
            z-index: 9;
            width: 2px;
            height: 2px;
            top: 0;
            right: 0;
            background: var(--primary-color);
            content: '';
          }
        }*/
    }
    &__line {
      margin-left: 8px;
      height: 1px;
      background: rgba(var(--primary-color-val), 0.3);
      flex: 1;
    }
    &__modification {
      position: absolute;
      right: 0;
      bottom: 1px;
      display: flex;
      width: 22px;
      justify-content: space-between;
    }
  }

  &-content {
    width: 100%;
  }
}
</style>
