<!--
* <AUTHOR> 张乐
* @Date :  2023/4/20
* @Version : 1.0
* @Content : tree-select
-->
<template>
  <sim-block class="sim-tree-select" :class="{ 'sim-readonly': $attrs.readonly }">
    <el-tree-select ref="treeRef" v-bind="$attrs" :suffix-icon="SimSuffix" :disabled="$attrs.disabled || $attrs.readonly" />
  </sim-block>
</template>

<script lang="ts">
export default {
  name: 'SimTreeSelect'
};
</script>
<script lang="ts" setup>
import { SimBlock, SimSuffix } from 'isim-ui';
import { ref } from 'vue';

const treeRef = ref();
defineExpose({
  treeRef
});
</script>
<style scoped lang="less">
.sim-tree-select {
  width: 100%;
  height: 32px;
}

:deep(.el-select) {
  margin-top: -4px;
  width: 100%;
  background: transparent;
}

:deep(.el-input__wrapper) {
  background-color: transparent;
  box-shadow: none;
  border-radius: 8px;
}

:deep(.el-input .el-input__wrapper.is-focus) {
  box-shadow: none !important;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select:hover) {
  --el-select-border-color-hover: none;
}
</style>
