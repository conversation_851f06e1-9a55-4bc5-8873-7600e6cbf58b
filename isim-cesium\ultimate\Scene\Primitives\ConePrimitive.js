/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/08/23 14:05:24
 * @description update ConePrimitive
 * @modify add customDesign ConeGeometry (add topFill and bottomFill params)
 * @modify extract createPrimitive to PrimitiveLibrary Module
 * @version 3.2
 * */
import AbstractPrimitive from './AbstractPrimitive.js';
import CylinderGeometry from '../geometries/CylinderGeometry.js';
import PrimitiveLibrary from './PrimitiveLibrary.js';

const {
  Cartesian3,
  Color,
  //   CylinderGeometry,
  CylinderOutlineGeometry,
  defaultValue,
  defined,
  Material,
  Matrix4
} = Cesium;

// import { Cartesian3, Color, CylinderGeometry, CylinderOutlineGeometry, defaultValue, defined, Material, Matrix4 } from 'cesium';

const matrixScratch = new Matrix4();

const PRIMITIVE_MODE = PrimitiveLibrary.PRIMITIVE_MODE;
export class ConePrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._length = defaultValue(options.length, 10000.0);
    this._radius = defaultValue(options.radius, 5000.0);
    this._color = defaultValue(options.color, Color.YELLOW.withAlpha(0.2));
    this._outline = defaultValue(options.outline, true);
    this._outlineColor = defaultValue(options.outlineColor, this._color.clone().withAlpha(0.8));
    this._numberOfVerticalLines = defaultValue(options.numberOfVerticalLines, 0);
    this._topFill = defaultValue(options.topFill, true);
    this._slices = options.slices;
    this._modelMatrix = Matrix4.clone(defaultValue(options.modelMatrix, Matrix4.IDENTITY));

    this._allowPicking = defaultValue(options.allowPicking, false);
    this._asynchronous = defaultValue(options.asynchronous, false);
    this._materialType = options.materialType;
    this._uniforms = options.uniforms;
    this._update = true;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive = this._primitive && this._primitive.destroy();
      this._outlinePrimitive = this._outlinePrimitive && this._outlinePrimitive.destroy();

      this._createCone(PRIMITIVE_MODE.FILL);
      if (this._outline) {
        this._createCone(PRIMITIVE_MODE.OUTLINE);
      }
    }

    this._primitive?.update(frameState);
    this._outlinePrimitive?.update(frameState);
  }

  _createCone(mode) {
    const length = this._length;
    /**
     * @bug 当 translation = length/2 时，反方向上的移动会报错，当 length = 100 (或 100/3) 报错相同
     * @example const relativeMatrix = Matrix4.fromTranslation(new Cartesian3(0, 0, 50), matrixScratch)
     * @example const relativeMatrix = Matrix4.fromTranslation(new Cartesian3(0, 0, 50.0001), matrixScratch); 报错解决
     */
    //  const relativeMatrix = Matrix4.fromTranslation(new Cartesian3(0, 0, 50.0001), matrixScratch);
    const relativeMatrix = Matrix4.fromTranslation(new Cartesian3(0, 0, length / 2 + 0.0000001), matrixScratch);

    const options = {
      geometryOptions: {
        length,
        topRadius: this._radius,
        bottomRadius: 0,
        slices: this._slices,
        numberOfVerticalLines: this._numberOfVerticalLines,
        topFill: this._topFill
      },
      relativeMatrix,
      color: this._color,
      modelMatrix: this._modelMatrix,
      allowPicking: this._allowPicking,
      asynchronous: this._asynchronous,
      materialType: this._materialType,
      uniforms: this._uniforms
    };

    if (mode === PRIMITIVE_MODE.FILL) {
      this._primitive = PrimitiveLibrary.createPrimitive(CylinderGeometry, options);
    }

    if (mode === PRIMITIVE_MODE.OUTLINE) {
      this._outlinePrimitive = PrimitiveLibrary.createPrimitive(CylinderOutlineGeometry, {
        ...options,
        color: this._outlineColor
      });
    }
  }

  destroy() {
    this._outlinePrimitive = this._outlinePrimitive && this._outlinePrimitive.destroy();
    return super.destroy();
  }

  set radius(value) {
    if (this._radius === value) {
      return;
    }

    this._radius = value;
    this._update = true;
  }

  set length(value) {
    if (this._length === value) {
      return;
    }

    this._length = value;
    this._update = true;
  }

  set slices(value) {
    if (this._slices === value) {
      return;
    }

    this._slices = value;
    this._update = true;
  }

  set numberOfVerticalLines(value) {
    if (this._numberOfVerticalLines === value) {
      return;
    }

    this._numberOfVerticalLines = value;
    this._update = true;
  }

  set modelMatrix(value) {
    if (Matrix4.equals(this._modelMatrix, value)) {
      return;
    }

    this._modelMatrix = value;
    if (this._primitive) {
      this._primitive.modelMatrix = value;
    }

    if (this._outlinePrimitive) {
      this._outlinePrimitive.modelMatrix = value;
    }
  }

  set outline(value) {
    if (this._outline === value) {
      return;
    }

    this._outline = value;
    if (this._outline) {
      this._createCone(PRIMITIVE_MODE.OUTLINE);
    }
  }

  set materialType(value) {
    if (this._materialType === value) {
      return;
    }

    this._materialType = value;
    if (this._primitive) {
      this._primitive.appearance.material = Material.fromType(this._materialType, this._uniforms);
    }
  }

  set uniforms(value) {
    // if (this._uniforms === value) {
    //   return;
    // }
    this._uniforms = value;
    if (defined(value) && defined(this._primitive)) {
      const uniforms = this._primitive.appearance.material.uniforms;
      for (let i in value) {
        if (value.hasOwnProperty(i)) {
          uniforms[i] = value[i];
        }
      }
    }
  }

  set color(value) {
    if (Color.equals(this._color, value)) {
      return;
    }
    this._color = value;
    if (this._materialType && this._primitive) {
      const uniforms = this._primitive.appearance.material.uniforms;
      uniforms.color = value;
    } else {
      this._update = true;
    }
  }
}
