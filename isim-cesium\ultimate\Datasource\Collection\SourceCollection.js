/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/24 10:32:47
 * @description simplify logic
 * @version 3.0
 * */
import { defaultValue, defined, destroyObject, DeveloperError } from 'cesium';

export default class SourceCollection {
  constructor(viewer) {
    this._sources = [];
    this._viewer = viewer;
    viewer.sources = this;
  }

  get length() {
    return this._sources.length;
  }

  add(source) {
    if (!defined(source)) {
      throw new DeveloperError('source is required.');
    }
    this._sources.push(source);
    source.register(this._viewer.scene);
    return source;
  }

  get(index) {
    if (!defined(index)) {
      throw new DeveloperError('index is required.');
    }
    return this._sources[index];
  }

  getByName(name) {
    if (!defined(name)) {
      throw new DeveloperError('name is required.');
    }
    return this._sources.filter(source => {
      return source.name === name;
    });
  }

  contains(source) {
    return this.indexOf(source) !== -1;
  }

  indexOf(source) {
    return this._sources.indexOf(source);
  }

  removeAll(destroy) {
    destroy = defaultValue(destroy, false);
    const primitives = this._viewer.scene.primitives;
    let source;
    for (let i = 0; i < this._sources.length; i++) {
      source = this._sources[i];
      if (destroy && typeof source.destroy === 'function') {
        source.destroy();
      } else {
        primitives.destroyPrimitives = false;
        source.unregister();
        primitives.destroyPrimitives = true;
      }
    }
    this._sources = [];
  }

  remove(source, destroy) {
    destroy = defaultValue(destroy, false);
    let index = this.indexOf(source);
    if (index !== -1) {
      this._sources.splice(index, 1);

      const primitives = this._viewer.scene.primitives;
      if (destroy && typeof source.destroy === 'function') {
        source.destroy();
      } else {
        primitives.destroyPrimitives = false;
        source.unregister();
        primitives.destroyPrimitives = true;
      }
      return true;
    }

    return false;
  }

  isDestroyed() {
    return false;
  }

  destroy() {
    this.removeAll(true);
    let viewer = this._viewer;
    delete viewer.source;
    return destroyObject(this);
  }
}
