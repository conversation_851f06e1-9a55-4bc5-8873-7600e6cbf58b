import { getC<PERSON>ra, getC<PERSON>ra<PERSON>ield, getFieldByInstance, getScene, getViewerName, nextTick, ViewerNameType } from 'isim-cesium';
import { Car<PERSON>ian3, Ellipsoid, Math as CesiumMath, SceneMode } from 'cesium';

export * from './trackAndSelectEntity';
export * from './camera-controller';
export * from './camera-fly';

export function onCameraChange(fn: (...params: any[]) => void, viewerName = getViewerName()) {
  nextTick().then(() => {
    getCamera(viewerName).changed.addEventListener(fn);
  });
  return () => {
    getCamera(viewerName).changed.removeEventListener(fn);
  };
}

const getCameraPositionCartographic = getCameraField('positionCartographic');

export const getCameraHeight = getFieldByInstance(getCameraPositionCartographic, 'height');

export function setCameraHeight(height: number, viewerName?: ViewerNameType) {
  const scene = getScene(viewerName);
  if (scene.mode === SceneMode.SCENE2D) {
    getCamera(viewerName).zoomIn(height);
  }
}

export const getCurrentCameraPosition = getCameraField('position');

export function setCameraScale(height: number, viewerName?: ViewerNameType) {
  const cameraHeight = (Ellipsoid.WGS84.maximumRadius * 10) / height;
  const cameraPos = getCameraPositionCartographic(viewerName);
  const longitude = CesiumMath.toDegrees(cameraPos.longitude);
  const latitude = CesiumMath.toDegrees(cameraPos.latitude);
  getCamera(viewerName).setView({
    destination: Cartesian3.fromDegrees(longitude, latitude, cameraHeight)
  });
}
