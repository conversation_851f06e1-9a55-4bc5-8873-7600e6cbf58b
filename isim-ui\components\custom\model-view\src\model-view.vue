<!--
* @Author: 宋计民
* @Date: 2024/7/2
* @Version: 1.0
* @Content: model-view.vue
-->
<template>
  <div class="model-view" ref="viewRef"></div>
</template>

<script setup lang="ts">
import { CesiumWidget, Model, Color, Math as CesiumMath, HeadingPitchRange, Cartesian3, Matrix3, Matrix4 } from 'cesium';
import { createWarningMessage } from '@/utils';

defineOptions({
  name: 'SimModelView'
});

const props = defineProps({
  url: {
    type: String,
    default: 'http://************/static/zhb.gltf'
  },
  scale: {
    type: Number,
    default: 1
  },
  backgroundColor: {
    type: String,
    default: '#fff'
  },
  isRotation: {
    type: Boolean,
    default: false
  }
});
const viewRef = ref();
const widgetIns = shallowRef<CesiumWidget>();
let timerId: any = null;

// 模型自动旋转
const modelRotation = (modelEntity: Model) => {
  let rotationAngle = 0.0;
  destroyedInterval();
  timerId = setInterval(() => {
    rotationAngle += CesiumMath.toRadians(1);
    let rotationMatrix3 = Matrix3.fromRotationZ(rotationAngle);
    modelEntity.modelMatrix = Matrix4.fromRotationTranslation(rotationMatrix3);
  }, 100);
};

const loadModel = (url: string) => {
  widgetIns.value?.scene.primitives.removeAll();
  if (url.includes('*') || url.includes('undefined') || url === 'static/') {
    createWarningMessage('模型正在开发中...');
    return;
  }
  const modelEntity = widgetIns.value?.scene.primitives.add(
    Model.fromGltf({
      url,
      scale: props.scale
    })
  );
  if (props.isRotation) {
    modelRotation(modelEntity);
  }
};

watch(
  () => props.url,
  (val) => {
    loadModel(val);
  }
);

onMounted(() => {
  nextTick().then(() => {
    const widget = new CesiumWidget(viewRef.value, {
      globe: false,
      skyBox: false
      // skyAtmosphere: false
    });

    widget.scene.backgroundColor = Color.fromCssColorString(props.backgroundColor);
    const heading = CesiumMath.toRadians(50);
    const pitch = CesiumMath.toRadians(-20);
    const range = 20;
    widget.camera.lookAt(Cartesian3.ZERO, new HeadingPitchRange(heading, pitch, range));
    // @ts-ignore
    widget._innerCreditContainer.style.display = 'none';
    widgetIns.value = widget;
    loadModel(props.url);
  });
});

const destroyedInterval = () => {
  if (!timerId) return;
  clearInterval(timerId);
  timerId = null;
};

onUnmounted(() => {
  destroyedInterval();
});
</script>

<style scoped lang="less">
.model-view {
  width: 100%;
  height: 100%;
}
</style>
