define(["exports","./arrayRemoveDuplicates-04f4e20a","./Matrix2-57f130bc","./when-4bbc8319","./ComponentDatatype-17ffa790","./PolylinePipeline-b95ae532"],(function(e,t,i,n,o,r){"use strict";const a={};function s(e,t){return o.CesiumMath.equalsEpsilon(e.latitude,t.latitude,o.CesiumMath.EPSILON10)&&o.CesiumMath.equalsEpsilon(e.longitude,t.longitude,o.CesiumMath.EPSILON10)}const l=new i.Cartographic,h=new i.Cartographic;const c=new Array(2),g=new Array(2),p={positions:void 0,height:void 0,granularity:void 0,ellipsoid:void 0};a.computePositions=function(e,a,u,y,d,f){const m=function(e,o,r,a){const c=(o=t.arrayRemoveDuplicates(o,i.Cartesian3.equalsEpsilon)).length;if(c<2)return;const g=n.defined(a),p=n.defined(r),u=new Array(c),y=new Array(c),d=new Array(c),f=o[0];u[0]=f;const m=e.cartesianToCartographic(f,l);p&&(m.height=r[0]),y[0]=m.height,d[0]=g?a[0]:0;let P=y[0]===d[0],A=1;for(let t=1;t<c;++t){const n=o[t],l=e.cartesianToCartographic(n,h);p&&(l.height=r[t]),P=P&&0===l.height,s(m,l)?m.height<l.height&&(y[A-1]=l.height):(u[A]=n,y[A]=l.height,d[A]=g?a[t]:0,P=P&&y[A]===d[A],i.Cartographic.clone(l,m),++A)}return P||A<2?void 0:(u.length=A,y.length=A,d.length=A,{positions:u,topHeights:y,bottomHeights:d})}(e,a,u,y);if(!n.defined(m))return;a=m.positions,u=m.topHeights,y=m.bottomHeights;const P=a.length,A=P-2;let C,w;const b=o.CesiumMath.chordLength(d,e.maximumRadius),v=p;if(v.minDistance=b,v.ellipsoid=e,f){let e,t=0;for(e=0;e<P-1;e++)t+=r.PolylinePipeline.numberOfPoints(a[e],a[e+1],b)+1;C=new Float64Array(3*t),w=new Float64Array(3*t);const i=c,n=g;v.positions=i,v.height=n;let o=0;for(e=0;e<P-1;e++){i[0]=a[e],i[1]=a[e+1],n[0]=u[e],n[1]=u[e+1];const t=r.PolylinePipeline.generateArc(v);C.set(t,o),n[0]=y[e],n[1]=y[e+1],w.set(r.PolylinePipeline.generateArc(v),o),o+=t.length}}else v.positions=a,v.height=u,C=new Float64Array(r.PolylinePipeline.generateArc(v)),v.height=y,w=new Float64Array(r.PolylinePipeline.generateArc(v));return{bottomPositions:w,topPositions:C,numCorners:A}},e.WallGeometryLibrary=a}));
//# sourceMappingURL=WallGeometryLibrary-1e758b06.js.map
