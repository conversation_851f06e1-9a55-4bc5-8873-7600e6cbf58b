import type { App } from 'vue';

import {
  SimBlock,
  SimDraggable,
  SimDraggable2,
  SimFold,
  SimDrawer,
  SimSlider,
  SimButton,
  SimGantt,
  SimInput,
  SimInputNumber,
  SimIconButton,
  SimKeyVal,
  SimInputFilter,
  SimModel,
  SimTabs,
  SimPagination,
  SimSelect,
  SimTable,
  SimLink,
  SimTinymce,
  SimDragItem,
  SimIcon,
  // SimSuffix,
  SimCard,
  SimLoading,
  SimDrawingBoard,
  SimUploadFile,
  SimGanttV1,
  SimNModal,
  SimPickFile,
  SimAttribute,
  SimMonaco
} from './components';

const install = (Vue: App) => {
  Vue.use(SimMonaco);
  Vue.use(SimNModal);
  Vue.use(SimGanttV1);
  Vue.use(SimUploadFile);
  Vue.use(SimIcon);
  // Vue.use(SimSuffix);
  Vue.use(SimCard);
  Vue.use(SimBlock);
  Vue.use(SimDrawingBoard);
  Vue.use(SimDraggable);
  Vue.use(SimDraggable2);
  Vue.use(SimTinymce);
  Vue.use(SimFold);
  Vue.use(SimDrawer);
  Vue.use(SimButton);
  Vue.use(SimGantt);
  Vue.use(SimInput);
  Vue.use(SimModel);
  Vue.use(SimDragItem);
  Vue.use(SimLoading);
  Vue.use(SimSlider);
  Vue.use(SimPickFile);
  Vue.use(SimAttribute);
  Vue.use(SimTabs);
  Vue.use(SimInputNumber);
  Vue.use(SimIconButton);
  Vue.use(SimKeyVal);
  Vue.use(SimInputFilter);
  Vue.use(SimPagination);
  Vue.use(SimSelect);
  Vue.use(SimTable);
  Vue.use(SimLink);
  // Vue.component('SimCollapse', SimCollapse);
  // Vue.component('SimIconImg', SimIconImg);
  // Vue.component('SimCloseBlock', SimCloseBlock);
  // Vue.component('SimAddTag', SimAddTag);
  // Vue.component('SimDatePicker', SimDatePicker);
  // Vue.component('SimTimePicker', SimTimePicker);
  // Vue.component('SimRadioGroup', SimRadioGroup);
  // Vue.use(SimTimeSelect);
  // Vue.use(SimDropdown);
  // Vue.use(SimBreadcrumb);
  // Vue.use(SimBreadcrumbForProp);
  // Vue.component("SimTreeSelect", SimTreeSelect);
  // Vue.component('SvgIcon', SimSvgIcon);
  // Vue.component("SimDataListV1", SimDataListV1);
  // Vue.component("SimUploadAttachment", SimUploadAttachment);
  // Vue.component("SimHeaderBlock", SimHeaderBlock);
  // Vue.component("SimUpload", SimUpload);
};

export * from './components';
export * from './hooks';
export * from './type';

export default install;
