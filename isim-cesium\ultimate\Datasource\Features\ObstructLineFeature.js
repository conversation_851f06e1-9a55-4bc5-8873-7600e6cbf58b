import { BoundingSphere } from 'cesium';
import StaticFeature from './StaticFeature';
import ObstructLineVisualizer from '../../Modules/VisualizationModel/ObstructLineVisualizer';

export default class ObstructLineFeature extends StaticFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this.create();
  }

  create() {
    const bs = BoundingSphere.fromPoints(this._options.positions);
    this._entity.position = bs.center;
    this.primitive = new ObstructLineVisualizer(this._options);
  }
}
