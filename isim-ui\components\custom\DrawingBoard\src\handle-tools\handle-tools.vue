<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : handle-tools.vue
-->
<template>
  <div class="sim-drawing-board-tool">
    <div
      v-for="item in options"
      :key="item.key"
      v-bind="item.props"
      :class="[
        'sim-drawing__handle',
        {
          'sim-draggable__handle--active': currentSelect === item.key
        }
      ]"
    >
      {{ item.label }}
    </div>
    <div class="sim-drawing__handle" @click="handleClear">清除</div>
    <div class="sim-drawing__handle" @click="handleClose">关闭</div>
  </div>
</template>

<script setup lang="ts">
import {
  AnnotationConfig,
  clearAnnotationGraphic,
  useArrowAnnotation,
  useCircleAnnotation,
  usePenAnnotation,
  useRectangleAnnotations,
  useTextAnnotation
} from '../draw-method';
import { useAnnotationsInject } from '../use-annotations-inject';
defineOptions({
  name: 'HandleTools'
});
const emits = defineEmits(['close']);

const currentSelect = ref('');
const annotationConfig: AnnotationConfig = reactive({
  color: '#FF0000',
  width: 3
});

const { clearAnnotations, clearMouseDown } = useAnnotationsInject();

const { start } = useRectangleAnnotations(annotationConfig);
const { start: penStart } = usePenAnnotation(annotationConfig);
const { start: textStart } = useTextAnnotation(annotationConfig);
const { start: arrowStart } = useArrowAnnotation(annotationConfig);
const { start: circleStart } = useCircleAnnotation(annotationConfig);

const handleClear = () => {
  clearAnnotations();
  clearAnnotationGraphic();
};

const handleClose = () => {
  handleClear();
  clearMouseDown();
  emits('close', false);
};
const options = [
  {
    label: '文字',
    key: 'text',
    props: {
      onClick() {
        textStart();
        currentSelect.value = 'text';
      }
    }
  },
  {
    label: '笔',
    key: 'pen',
    props: {
      onClick() {
        penStart();
        currentSelect.value = 'pen';
      }
    }
  },
  {
    label: '矩形',
    key: 'rectangle',
    props: {
      onClick() {
        start();
        currentSelect.value = 'rectangle';
      }
    }
  },
  {
    label: '箭头',
    key: 'arrow',
    props: {
      onClick() {
        arrowStart();
        currentSelect.value = 'arrow';
      }
    }
  },
  {
    label: '圆',
    key: 'circle',
    props: {
      onClick() {
        circleStart();
        currentSelect.value = 'circle';
      }
    }
  }
];
</script>

<style scoped lang="less">
.sim-drawing-board-tool {
  user-select: none;
  position: absolute;
  left: 50%;
  top: 40px;
  z-index: 50000;
  background: var(--model-bg-color-9);
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  border-radius: 5px;
  .sim-drawing__handle {
    padding: 12px;
    width: 40px;
    cursor: default;
    text-align: center;
    &:hover {
      background-color: var(--primary-color-2);
    }
  }

  .sim-draggable__handle--active {
    background-color: var(--primary-color-5);
  }
}
</style>
