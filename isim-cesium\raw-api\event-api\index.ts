import { createEventHandler } from './event-core';
import { KeyboardEventModifier, ScreenSpaceEventType } from 'cesium';

export * from './clear-event';
export * from './tick-event';

export * from './move-entity';
export { hasPrimitiveByPosition, getPrimitiveByPosition, getEntityByPosition, getEntitiesByPosition } from './event-core';
export type { PositionedEvent, MotionEvent } from './event-core';

export const onLeftClick = createEventHandler(ScreenSpaceEventType.LEFT_CLICK);
export const useLeftClick = createEventHandler(ScreenSpaceEventType.LEFT_CLICK, true);

export const onCtrlLeftClick = createEventHandler(ScreenSpaceEventType.LEFT_CLICK, false, KeyboardEventModifier.CTRL);
export const useCtrlLeftClick = createEventHandler(ScreenSpaceEventType.LEFT_CLICK, true, KeyboardEventModifier.CTRL);

export const onLeftDown = createEventHandler(ScreenSpaceEventType.LEFT_DOWN);
export const useLeftDown = createEventHandler(ScreenSpaceEventType.LEFT_DOWN, true);

export const onCtrlLeftDown = createEventHandler(ScreenSpaceEventType.LEFT_DOWN, false, KeyboardEventModifier.CTRL);
export const useCtrlLeftDown = createEventHandler(ScreenSpaceEventType.LEFT_DOWN, true, KeyboardEventModifier.CTRL);

export const onLeftUp = createEventHandler(ScreenSpaceEventType.LEFT_UP);
export const useLeftUp = createEventHandler(ScreenSpaceEventType.LEFT_UP, true);

export const onRightClick = createEventHandler(ScreenSpaceEventType.RIGHT_CLICK);
export const useRightClick = createEventHandler(ScreenSpaceEventType.RIGHT_CLICK, true);

export const onDoubleClick = createEventHandler(ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
export const useDoubleClick = createEventHandler(ScreenSpaceEventType.LEFT_DOUBLE_CLICK, true);

export const onMouseMove = createEventHandler(ScreenSpaceEventType.MOUSE_MOVE);
export const useMouseMove = createEventHandler(ScreenSpaceEventType.MOUSE_MOVE, true);
