{"version": 3, "file": "PolygonGeometryLibrary-1607997e.js", "sources": ["../../../../Source/Core/Queue.js", "../../../../Source/Core/PolygonGeometryLibrary.js"], "sourcesContent": ["/**\n * A queue that can enqueue items at the end, and dequeue items from the front.\n *\n * @alias Queue\n * @constructor\n */\nfunction Queue() {\n  this._array = [];\n  this._offset = 0;\n  this._length = 0;\n}\n\nObject.defineProperties(Queue.prototype, {\n  /**\n   * The length of the queue.\n   *\n   * @memberof Queue.prototype\n   *\n   * @type {Number}\n   * @readonly\n   */\n  length: {\n    get: function () {\n      return this._length;\n    },\n  },\n});\n\n/**\n * Enqueues the specified item.\n *\n * @param {*} item The item to enqueue.\n */\nQueue.prototype.enqueue = function (item) {\n  this._array.push(item);\n  this._length++;\n};\n\n/**\n * Dequeues an item.  Returns undefined if the queue is empty.\n *\n * @returns {*} The the dequeued item.\n */\nQueue.prototype.dequeue = function () {\n  if (this._length === 0) {\n    return undefined;\n  }\n\n  const array = this._array;\n  let offset = this._offset;\n  const item = array[offset];\n  array[offset] = undefined;\n\n  offset++;\n  if (offset > 10 && offset * 2 > array.length) {\n    //compact array\n    this._array = array.slice(offset);\n    offset = 0;\n  }\n\n  this._offset = offset;\n  this._length--;\n\n  return item;\n};\n\n/**\n * Returns the item at the front of the queue.  Returns undefined if the queue is empty.\n *\n * @returns {*} The item at the front of the queue.\n */\nQueue.prototype.peek = function () {\n  if (this._length === 0) {\n    return undefined;\n  }\n\n  return this._array[this._offset];\n};\n\n/**\n * Check whether this queue contains the specified item.\n *\n * @param {*} item The item to search for.\n */\nQueue.prototype.contains = function (item) {\n  return this._array.indexOf(item) !== -1;\n};\n\n/**\n * Remove all items from the queue.\n */\nQueue.prototype.clear = function () {\n  this._array.length = this._offset = this._length = 0;\n};\n\n/**\n * Sort the items in the queue in-place.\n *\n * @param {Queue.Comparator} compareFunction A function that defines the sort order.\n */\nQueue.prototype.sort = function (compareFunction) {\n  if (this._offset > 0) {\n    //compact array\n    this._array = this._array.slice(this._offset);\n    this._offset = 0;\n  }\n\n  this._array.sort(compareFunction);\n};\n\n/**\n * A function used to compare two items while sorting a queue.\n * @callback Queue.Comparator\n *\n * @param {*} a An item in the array.\n * @param {*} b An item in the array.\n * @returns {Number} Returns a negative value if <code>a</code> is less than <code>b</code>,\n *          a positive value if <code>a</code> is greater than <code>b</code>, or\n *          0 if <code>a</code> is equal to <code>b</code>.\n *\n * @example\n * function compareNumbers(a, b) {\n *     return a - b;\n * }\n */\nexport default Queue;\n", "import ArcType from \"./ArcType.js\";\nimport arrayRemoveDuplicates from \"./arrayRemoveDuplicates.js\";\nimport Cartesian2 from \"./Cartesian2.js\";\nimport Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport ComponentDatatype from \"./ComponentDatatype.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport Ellipsoid from \"./Ellipsoid.js\";\nimport EllipsoidRhumbLine from \"./EllipsoidRhumbLine.js\";\nimport Geometry from \"./Geometry.js\";\nimport GeometryAttribute from \"./GeometryAttribute.js\";\nimport GeometryAttributes from \"./GeometryAttributes.js\";\nimport GeometryPipeline from \"./GeometryPipeline.js\";\nimport IndexDatatype from \"./IndexDatatype.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix3 from \"./Matrix3.js\";\nimport PolygonPipeline from \"./PolygonPipeline.js\";\nimport PrimitiveType from \"./PrimitiveType.js\";\nimport Quaternion from \"./Quaternion.js\";\nimport Queue from \"./Queue.js\";\nimport WindingOrder from \"./WindingOrder.js\";\n\n/**\n * @private\n */\nconst PolygonGeometryLibrary = {};\n\nPolygonGeometryLibrary.computeHierarchyPackedLength = function (\n  polygonHierarchy\n) {\n  let numComponents = 0;\n  const stack = [polygonHierarchy];\n  while (stack.length > 0) {\n    const hierarchy = stack.pop();\n    if (!defined(hierarchy)) {\n      continue;\n    }\n\n    numComponents += 2;\n\n    const positions = hierarchy.positions;\n    const holes = hierarchy.holes;\n\n    if (defined(positions)) {\n      numComponents += positions.length * Cartesian3.packedLength;\n    }\n\n    if (defined(holes)) {\n      const length = holes.length;\n      for (let i = 0; i < length; ++i) {\n        stack.push(holes[i]);\n      }\n    }\n  }\n\n  return numComponents;\n};\n\nPolygonGeometryLibrary.packPolygonHierarchy = function (\n  polygonHierarchy,\n  array,\n  startingIndex\n) {\n  const stack = [polygonHierarchy];\n  while (stack.length > 0) {\n    const hierarchy = stack.pop();\n    if (!defined(hierarchy)) {\n      continue;\n    }\n\n    const positions = hierarchy.positions;\n    const holes = hierarchy.holes;\n\n    array[startingIndex++] = defined(positions) ? positions.length : 0;\n    array[startingIndex++] = defined(holes) ? holes.length : 0;\n\n    if (defined(positions)) {\n      const positionsLength = positions.length;\n      for (let i = 0; i < positionsLength; ++i, startingIndex += 3) {\n        Cartesian3.pack(positions[i], array, startingIndex);\n      }\n    }\n\n    if (defined(holes)) {\n      const holesLength = holes.length;\n      for (let j = 0; j < holesLength; ++j) {\n        stack.push(holes[j]);\n      }\n    }\n  }\n\n  return startingIndex;\n};\n\nPolygonGeometryLibrary.unpackPolygonHierarchy = function (\n  array,\n  startingIndex\n) {\n  const positionsLength = array[startingIndex++];\n  const holesLength = array[startingIndex++];\n\n  const positions = new Array(positionsLength);\n  const holes = holesLength > 0 ? new Array(holesLength) : undefined;\n\n  for (\n    let i = 0;\n    i < positionsLength;\n    ++i, startingIndex += Cartesian3.packedLength\n  ) {\n    positions[i] = Cartesian3.unpack(array, startingIndex);\n  }\n\n  for (let j = 0; j < holesLength; ++j) {\n    holes[j] = PolygonGeometryLibrary.unpackPolygonHierarchy(\n      array,\n      startingIndex\n    );\n    startingIndex = holes[j].startingIndex;\n    delete holes[j].startingIndex;\n  }\n\n  return {\n    positions: positions,\n    holes: holes,\n    startingIndex: startingIndex,\n  };\n};\n\nconst distanceScratch = new Cartesian3();\nfunction getPointAtDistance(p0, p1, distance, length) {\n  Cartesian3.subtract(p1, p0, distanceScratch);\n  Cartesian3.multiplyByScalar(\n    distanceScratch,\n    distance / length,\n    distanceScratch\n  );\n  Cartesian3.add(p0, distanceScratch, distanceScratch);\n  return [distanceScratch.x, distanceScratch.y, distanceScratch.z];\n}\n\nPolygonGeometryLibrary.subdivideLineCount = function (p0, p1, minDistance) {\n  const distance = Cartesian3.distance(p0, p1);\n  const n = distance / minDistance;\n  const countDivide = Math.max(0, Math.ceil(CesiumMath.log2(n)));\n  return Math.pow(2, countDivide);\n};\n\nconst scratchCartographic0 = new Cartographic();\nconst scratchCartographic1 = new Cartographic();\nconst scratchCartographic2 = new Cartographic();\nconst scratchCartesian0 = new Cartesian3();\nPolygonGeometryLibrary.subdivideRhumbLineCount = function (\n  ellipsoid,\n  p0,\n  p1,\n  minDistance\n) {\n  const c0 = ellipsoid.cartesianToCartographic(p0, scratchCartographic0);\n  const c1 = ellipsoid.cartesianToCartographic(p1, scratchCartographic1);\n  const rhumb = new EllipsoidRhumbLine(c0, c1, ellipsoid);\n  const n = rhumb.surfaceDistance / minDistance;\n  const countDivide = Math.max(0, Math.ceil(CesiumMath.log2(n)));\n  return Math.pow(2, countDivide);\n};\n\nPolygonGeometryLibrary.subdivideLine = function (p0, p1, minDistance, result) {\n  const numVertices = PolygonGeometryLibrary.subdivideLineCount(\n    p0,\n    p1,\n    minDistance\n  );\n  const length = Cartesian3.distance(p0, p1);\n  const distanceBetweenVertices = length / numVertices;\n\n  if (!defined(result)) {\n    result = [];\n  }\n\n  const positions = result;\n  positions.length = numVertices * 3;\n\n  let index = 0;\n  for (let i = 0; i < numVertices; i++) {\n    const p = getPointAtDistance(p0, p1, i * distanceBetweenVertices, length);\n    positions[index++] = p[0];\n    positions[index++] = p[1];\n    positions[index++] = p[2];\n  }\n\n  return positions;\n};\n\nPolygonGeometryLibrary.subdivideRhumbLine = function (\n  ellipsoid,\n  p0,\n  p1,\n  minDistance,\n  result\n) {\n  const c0 = ellipsoid.cartesianToCartographic(p0, scratchCartographic0);\n  const c1 = ellipsoid.cartesianToCartographic(p1, scratchCartographic1);\n  const rhumb = new EllipsoidRhumbLine(c0, c1, ellipsoid);\n\n  const n = rhumb.surfaceDistance / minDistance;\n  const countDivide = Math.max(0, Math.ceil(CesiumMath.log2(n)));\n  const numVertices = Math.pow(2, countDivide);\n  const distanceBetweenVertices = rhumb.surfaceDistance / numVertices;\n\n  if (!defined(result)) {\n    result = [];\n  }\n\n  const positions = result;\n  positions.length = numVertices * 3;\n\n  let index = 0;\n  for (let i = 0; i < numVertices; i++) {\n    const c = rhumb.interpolateUsingSurfaceDistance(\n      i * distanceBetweenVertices,\n      scratchCartographic2\n    );\n    const p = ellipsoid.cartographicToCartesian(c, scratchCartesian0);\n    positions[index++] = p.x;\n    positions[index++] = p.y;\n    positions[index++] = p.z;\n  }\n\n  return positions;\n};\n\nconst scaleToGeodeticHeightN1 = new Cartesian3();\nconst scaleToGeodeticHeightN2 = new Cartesian3();\nconst scaleToGeodeticHeightP1 = new Cartesian3();\nconst scaleToGeodeticHeightP2 = new Cartesian3();\n\nPolygonGeometryLibrary.scaleToGeodeticHeightExtruded = function (\n  geometry,\n  maxHeight,\n  minHeight,\n  ellipsoid,\n  perPositionHeight\n) {\n  ellipsoid = defaultValue(ellipsoid, Ellipsoid.WGS84);\n\n  const n1 = scaleToGeodeticHeightN1;\n  let n2 = scaleToGeodeticHeightN2;\n  const p = scaleToGeodeticHeightP1;\n  let p2 = scaleToGeodeticHeightP2;\n\n  if (\n    defined(geometry) &&\n    defined(geometry.attributes) &&\n    defined(geometry.attributes.position)\n  ) {\n    const positions = geometry.attributes.position.values;\n    const length = positions.length / 2;\n\n    for (let i = 0; i < length; i += 3) {\n      Cartesian3.fromArray(positions, i, p);\n\n      ellipsoid.geodeticSurfaceNormal(p, n1);\n      p2 = ellipsoid.scaleToGeodeticSurface(p, p2);\n      n2 = Cartesian3.multiplyByScalar(n1, minHeight, n2);\n      n2 = Cartesian3.add(p2, n2, n2);\n      positions[i + length] = n2.x;\n      positions[i + 1 + length] = n2.y;\n      positions[i + 2 + length] = n2.z;\n\n      if (perPositionHeight) {\n        p2 = Cartesian3.clone(p, p2);\n      }\n      n2 = Cartesian3.multiplyByScalar(n1, maxHeight, n2);\n      n2 = Cartesian3.add(p2, n2, n2);\n      positions[i] = n2.x;\n      positions[i + 1] = n2.y;\n      positions[i + 2] = n2.z;\n    }\n  }\n  return geometry;\n};\n\nPolygonGeometryLibrary.polygonOutlinesFromHierarchy = function (\n  polygonHierarchy,\n  scaleToEllipsoidSurface,\n  ellipsoid\n) {\n  // create from a polygon hierarchy\n  // Algorithm adapted from http://www.geometrictools.com/Documentation/TriangulationByEarClipping.pdf\n  const polygons = [];\n  const queue = new Queue();\n  queue.enqueue(polygonHierarchy);\n  let i;\n  let j;\n  let length;\n  while (queue.length !== 0) {\n    const outerNode = queue.dequeue();\n    let outerRing = outerNode.positions;\n    if (scaleToEllipsoidSurface) {\n      length = outerRing.length;\n      for (i = 0; i < length; i++) {\n        ellipsoid.scaleToGeodeticSurface(outerRing[i], outerRing[i]);\n      }\n    }\n    outerRing = arrayRemoveDuplicates(\n      outerRing,\n      Cartesian3.equalsEpsilon,\n      true\n    );\n    if (outerRing.length < 3) {\n      continue;\n    }\n\n    const numChildren = outerNode.holes ? outerNode.holes.length : 0;\n    // The outer polygon contains inner polygons\n    for (i = 0; i < numChildren; i++) {\n      const hole = outerNode.holes[i];\n      let holePositions = hole.positions;\n      if (scaleToEllipsoidSurface) {\n        length = holePositions.length;\n        for (j = 0; j < length; ++j) {\n          ellipsoid.scaleToGeodeticSurface(holePositions[j], holePositions[j]);\n        }\n      }\n      holePositions = arrayRemoveDuplicates(\n        holePositions,\n        Cartesian3.equalsEpsilon,\n        true\n      );\n      if (holePositions.length < 3) {\n        continue;\n      }\n      polygons.push(holePositions);\n\n      let numGrandchildren = 0;\n      if (defined(hole.holes)) {\n        numGrandchildren = hole.holes.length;\n      }\n\n      for (j = 0; j < numGrandchildren; j++) {\n        queue.enqueue(hole.holes[j]);\n      }\n    }\n\n    polygons.push(outerRing);\n  }\n\n  return polygons;\n};\n\nPolygonGeometryLibrary.polygonsFromHierarchy = function (\n  polygonHierarchy,\n  projectPointsTo2D,\n  scaleToEllipsoidSurface,\n  ellipsoid\n) {\n  // create from a polygon hierarchy\n  // Algorithm adapted from http://www.geometrictools.com/Documentation/TriangulationByEarClipping.pdf\n  const hierarchy = [];\n  const polygons = [];\n\n  const queue = new Queue();\n  queue.enqueue(polygonHierarchy);\n\n  while (queue.length !== 0) {\n    const outerNode = queue.dequeue();\n    let outerRing = outerNode.positions;\n    const holes = outerNode.holes;\n\n    let i;\n    let length;\n    if (scaleToEllipsoidSurface) {\n      length = outerRing.length;\n      for (i = 0; i < length; i++) {\n        ellipsoid.scaleToGeodeticSurface(outerRing[i], outerRing[i]);\n      }\n    }\n\n    outerRing = arrayRemoveDuplicates(\n      outerRing,\n      Cartesian3.equalsEpsilon,\n      true\n    );\n    if (outerRing.length < 3) {\n      continue;\n    }\n\n    let positions2D = projectPointsTo2D(outerRing);\n    if (!defined(positions2D)) {\n      continue;\n    }\n    const holeIndices = [];\n\n    let originalWindingOrder = PolygonPipeline.computeWindingOrder2D(\n      positions2D\n    );\n    if (originalWindingOrder === WindingOrder.CLOCKWISE) {\n      positions2D.reverse();\n      outerRing = outerRing.slice().reverse();\n    }\n\n    let positions = outerRing.slice();\n    const numChildren = defined(holes) ? holes.length : 0;\n    const polygonHoles = [];\n    let j;\n\n    for (i = 0; i < numChildren; i++) {\n      const hole = holes[i];\n      let holePositions = hole.positions;\n      if (scaleToEllipsoidSurface) {\n        length = holePositions.length;\n        for (j = 0; j < length; ++j) {\n          ellipsoid.scaleToGeodeticSurface(holePositions[j], holePositions[j]);\n        }\n      }\n\n      holePositions = arrayRemoveDuplicates(\n        holePositions,\n        Cartesian3.equalsEpsilon,\n        true\n      );\n      if (holePositions.length < 3) {\n        continue;\n      }\n\n      const holePositions2D = projectPointsTo2D(holePositions);\n      if (!defined(holePositions2D)) {\n        continue;\n      }\n\n      originalWindingOrder = PolygonPipeline.computeWindingOrder2D(\n        holePositions2D\n      );\n      if (originalWindingOrder === WindingOrder.CLOCKWISE) {\n        holePositions2D.reverse();\n        holePositions = holePositions.slice().reverse();\n      }\n\n      polygonHoles.push(holePositions);\n      holeIndices.push(positions.length);\n      positions = positions.concat(holePositions);\n      positions2D = positions2D.concat(holePositions2D);\n\n      let numGrandchildren = 0;\n      if (defined(hole.holes)) {\n        numGrandchildren = hole.holes.length;\n      }\n\n      for (j = 0; j < numGrandchildren; j++) {\n        queue.enqueue(hole.holes[j]);\n      }\n    }\n\n    hierarchy.push({\n      outerRing: outerRing,\n      holes: polygonHoles,\n    });\n    polygons.push({\n      positions: positions,\n      positions2D: positions2D,\n      holes: holeIndices,\n    });\n  }\n\n  return {\n    hierarchy: hierarchy,\n    polygons: polygons,\n  };\n};\n\nconst computeBoundingRectangleCartesian2 = new Cartesian2();\nconst computeBoundingRectangleCartesian3 = new Cartesian3();\nconst computeBoundingRectangleQuaternion = new Quaternion();\nconst computeBoundingRectangleMatrix3 = new Matrix3();\nPolygonGeometryLibrary.computeBoundingRectangle = function (\n  planeNormal,\n  projectPointTo2D,\n  positions,\n  angle,\n  result\n) {\n  const rotation = Quaternion.fromAxisAngle(\n    planeNormal,\n    angle,\n    computeBoundingRectangleQuaternion\n  );\n  const textureMatrix = Matrix3.fromQuaternion(\n    rotation,\n    computeBoundingRectangleMatrix3\n  );\n\n  let minX = Number.POSITIVE_INFINITY;\n  let maxX = Number.NEGATIVE_INFINITY;\n  let minY = Number.POSITIVE_INFINITY;\n  let maxY = Number.NEGATIVE_INFINITY;\n\n  const length = positions.length;\n  for (let i = 0; i < length; ++i) {\n    const p = Cartesian3.clone(\n      positions[i],\n      computeBoundingRectangleCartesian3\n    );\n    Matrix3.multiplyByVector(textureMatrix, p, p);\n    const st = projectPointTo2D(p, computeBoundingRectangleCartesian2);\n\n    if (defined(st)) {\n      minX = Math.min(minX, st.x);\n      maxX = Math.max(maxX, st.x);\n\n      minY = Math.min(minY, st.y);\n      maxY = Math.max(maxY, st.y);\n    }\n  }\n\n  result.x = minX;\n  result.y = minY;\n  result.width = maxX - minX;\n  result.height = maxY - minY;\n  return result;\n};\n\nPolygonGeometryLibrary.createGeometryFromPositions = function (\n  ellipsoid,\n  polygon,\n  granularity,\n  perPositionHeight,\n  vertexFormat,\n  arcType\n) {\n  let indices = PolygonPipeline.triangulate(polygon.positions2D, polygon.holes);\n\n  /* If polygon is completely unrenderable, just use the first three vertices */\n  if (indices.length < 3) {\n    indices = [0, 1, 2];\n  }\n\n  const positions = polygon.positions;\n\n  if (perPositionHeight) {\n    const length = positions.length;\n    const flattenedPositions = new Array(length * 3);\n    let index = 0;\n    for (let i = 0; i < length; i++) {\n      const p = positions[i];\n      flattenedPositions[index++] = p.x;\n      flattenedPositions[index++] = p.y;\n      flattenedPositions[index++] = p.z;\n    }\n    const geometry = new Geometry({\n      attributes: {\n        position: new GeometryAttribute({\n          componentDatatype: ComponentDatatype.DOUBLE,\n          componentsPerAttribute: 3,\n          values: flattenedPositions,\n        }),\n      },\n      indices: indices,\n      primitiveType: PrimitiveType.TRIANGLES,\n    });\n\n    if (vertexFormat.normal) {\n      return GeometryPipeline.computeNormal(geometry);\n    }\n\n    return geometry;\n  }\n\n  if (arcType === ArcType.GEODESIC) {\n    return PolygonPipeline.computeSubdivision(\n      ellipsoid,\n      positions,\n      indices,\n      granularity\n    );\n  } else if (arcType === ArcType.RHUMB) {\n    return PolygonPipeline.computeRhumbLineSubdivision(\n      ellipsoid,\n      positions,\n      indices,\n      granularity\n    );\n  }\n};\n\nconst computeWallIndicesSubdivided = [];\nconst p1Scratch = new Cartesian3();\nconst p2Scratch = new Cartesian3();\n\nPolygonGeometryLibrary.computeWallGeometry = function (\n  positions,\n  ellipsoid,\n  granularity,\n  perPositionHeight,\n  arcType\n) {\n  let edgePositions;\n  let topEdgeLength;\n  let i;\n  let p1;\n  let p2;\n\n  let length = positions.length;\n  let index = 0;\n\n  if (!perPositionHeight) {\n    const minDistance = CesiumMath.chordLength(\n      granularity,\n      ellipsoid.maximumRadius\n    );\n\n    let numVertices = 0;\n    if (arcType === ArcType.GEODESIC) {\n      for (i = 0; i < length; i++) {\n        numVertices += PolygonGeometryLibrary.subdivideLineCount(\n          positions[i],\n          positions[(i + 1) % length],\n          minDistance\n        );\n      }\n    } else if (arcType === ArcType.RHUMB) {\n      for (i = 0; i < length; i++) {\n        numVertices += PolygonGeometryLibrary.subdivideRhumbLineCount(\n          ellipsoid,\n          positions[i],\n          positions[(i + 1) % length],\n          minDistance\n        );\n      }\n    }\n\n    topEdgeLength = (numVertices + length) * 3;\n    edgePositions = new Array(topEdgeLength * 2);\n    for (i = 0; i < length; i++) {\n      p1 = positions[i];\n      p2 = positions[(i + 1) % length];\n\n      let tempPositions;\n      if (arcType === ArcType.GEODESIC) {\n        tempPositions = PolygonGeometryLibrary.subdivideLine(\n          p1,\n          p2,\n          minDistance,\n          computeWallIndicesSubdivided\n        );\n      } else if (arcType === ArcType.RHUMB) {\n        tempPositions = PolygonGeometryLibrary.subdivideRhumbLine(\n          ellipsoid,\n          p1,\n          p2,\n          minDistance,\n          computeWallIndicesSubdivided\n        );\n      }\n      const tempPositionsLength = tempPositions.length;\n      for (let j = 0; j < tempPositionsLength; ++j, ++index) {\n        edgePositions[index] = tempPositions[j];\n        edgePositions[index + topEdgeLength] = tempPositions[j];\n      }\n\n      edgePositions[index] = p2.x;\n      edgePositions[index + topEdgeLength] = p2.x;\n      ++index;\n\n      edgePositions[index] = p2.y;\n      edgePositions[index + topEdgeLength] = p2.y;\n      ++index;\n\n      edgePositions[index] = p2.z;\n      edgePositions[index + topEdgeLength] = p2.z;\n      ++index;\n    }\n  } else {\n    topEdgeLength = length * 3 * 2;\n    edgePositions = new Array(topEdgeLength * 2);\n    for (i = 0; i < length; i++) {\n      p1 = positions[i];\n      p2 = positions[(i + 1) % length];\n      edgePositions[index] = edgePositions[index + topEdgeLength] = p1.x;\n      ++index;\n      edgePositions[index] = edgePositions[index + topEdgeLength] = p1.y;\n      ++index;\n      edgePositions[index] = edgePositions[index + topEdgeLength] = p1.z;\n      ++index;\n      edgePositions[index] = edgePositions[index + topEdgeLength] = p2.x;\n      ++index;\n      edgePositions[index] = edgePositions[index + topEdgeLength] = p2.y;\n      ++index;\n      edgePositions[index] = edgePositions[index + topEdgeLength] = p2.z;\n      ++index;\n    }\n  }\n\n  length = edgePositions.length;\n  const indices = IndexDatatype.createTypedArray(\n    length / 3,\n    length - positions.length * 6\n  );\n  let edgeIndex = 0;\n  length /= 6;\n\n  for (i = 0; i < length; i++) {\n    const UL = i;\n    const UR = UL + 1;\n    const LL = UL + length;\n    const LR = LL + 1;\n\n    p1 = Cartesian3.fromArray(edgePositions, UL * 3, p1Scratch);\n    p2 = Cartesian3.fromArray(edgePositions, UR * 3, p2Scratch);\n    if (\n      Cartesian3.equalsEpsilon(\n        p1,\n        p2,\n        CesiumMath.EPSILON10,\n        CesiumMath.EPSILON10\n      )\n    ) {\n      //skip corner\n      continue;\n    }\n\n    indices[edgeIndex++] = UL;\n    indices[edgeIndex++] = LL;\n    indices[edgeIndex++] = UR;\n    indices[edgeIndex++] = UR;\n    indices[edgeIndex++] = LL;\n    indices[edgeIndex++] = LR;\n  }\n\n  return new Geometry({\n    attributes: new GeometryAttributes({\n      position: new GeometryAttribute({\n        componentDatatype: ComponentDatatype.DOUBLE,\n        componentsPerAttribute: 3,\n        values: edgePositions,\n      }),\n    }),\n    indices: indices,\n    primitiveType: PrimitiveType.TRIANGLES,\n  });\n};\nexport default PolygonGeometryLibrary;\n"], "names": ["Queue", "this", "_array", "_offset", "_length", "Object", "defineProperties", "prototype", "length", "get", "enqueue", "item", "push", "dequeue", "array", "offset", "undefined", "slice", "peek", "contains", "indexOf", "clear", "sort", "compareFunction", "PolygonGeometryLibrary", "polygonHierarchy", "numComponents", "stack", "hierarchy", "pop", "defined", "positions", "holes", "Cartesian3", "<PERSON><PERSON><PERSON><PERSON>", "i", "startingIndex", "positionsLength", "pack", "<PERSON><PERSON><PERSON><PERSON>", "j", "Array", "unpack", "unpackPolygonHierarchy", "distanceScratch", "getPointAtDistance", "p0", "p1", "distance", "subtract", "multiplyByScalar", "add", "x", "y", "z", "subdivideLineCount", "minDistance", "n", "countDivide", "Math", "max", "ceil", "CesiumMath", "log2", "pow", "scratchCartographic0", "Cartographic", "scratchCartographic1", "scratchCartographic2", "scratchCartesian0", "subdivideRhumbLineCount", "ellipsoid", "c0", "cartesianToCartographic", "c1", "EllipsoidRhumbLine", "surfaceDistance", "subdivideLine", "result", "numVertices", "distanceBetweenVertices", "index", "p", "subdivideRhumbLine", "rhumb", "c", "interpolateUsingSurfaceDistance", "cartographicToCartesian", "scaleToGeodeticHeightN1", "scaleToGeodeticHeightN2", "scaleToGeodeticHeightP1", "scaleToGeodeticHeightP2", "scaleToGeodeticHeightExtruded", "geometry", "maxHeight", "minHeight", "perPositionHeight", "defaultValue", "Ellipsoid", "WGS84", "n1", "n2", "p2", "attributes", "position", "values", "fromArray", "geodeticSurfaceNormal", "scaleToGeodeticSurface", "clone", "polygonOutlinesFromHierarchy", "scaleToEllipsoidSurface", "polygons", "queue", "outerNode", "outerRing", "arrayRemoveDuplicates", "equalsEpsilon", "numC<PERSON><PERSON>n", "hole", "holePositions", "numGrandchildren", "polygonsFromHierarchy", "projectPointsTo2D", "positions2D", "holeIndices", "originalWindingOrder", "PolygonPipeline", "computeWindingOrder2D", "WindingOrder", "CLOCKWISE", "reverse", "polygonHoles", "holePositions2D", "concat", "computeBoundingRectangleCartesian2", "Cartesian2", "computeBoundingRectangleCartesian3", "computeBoundingRectangleQuaternion", "Quaternion", "computeBoundingRectangleMatrix3", "Matrix3", "computeBoundingRectangle", "planeNormal", "projectPointTo2D", "angle", "rotation", "fromAxisAngle", "textureMatrix", "fromQuaternion", "minX", "Number", "POSITIVE_INFINITY", "maxX", "NEGATIVE_INFINITY", "minY", "maxY", "multiplyByVector", "st", "min", "width", "height", "createGeometryFromPositions", "polygon", "granularity", "vertexFormat", "arcType", "indices", "triangulate", "flattenedPositions", "Geometry", "GeometryAttribute", "componentDatatype", "ComponentDatatype", "DOUBLE", "componentsPerAttribute", "primitiveType", "PrimitiveType", "TRIANGLES", "normal", "GeometryPipeline", "computeNormal", "ArcType", "GEODESIC", "computeSubdivision", "RHUMB", "computeRhumbLineSubdivision", "computeWallIndicesSubdivided", "p1Scratch", "p2Scratch", "computeWallGeometry", "edgePositions", "topEdgeLength", "chord<PERSON>ength", "maximumRadius", "tempPositions", "tempPositionsLength", "IndexDatatype", "createTypedArray", "edgeIndex", "UL", "UR", "LL", "LR", "EPSILON10", "GeometryAttributes"], "mappings": "gZAMA,SAASA,IACPC,KAAKC,OAAS,GACdD,KAAKE,QAAU,EACfF,KAAKG,QAAU,CACjB,CAEAC,OAAOC,iBAAiBN,EAAMO,UAAW,CASvCC,OAAQ,CACNC,IAAK,WACH,OAAOR,KAAKG,OACb,KASLJ,EAAMO,UAAUG,QAAU,SAAUC,GAClCV,KAAKC,OAAOU,KAAKD,GACjBV,KAAKG,SACP,EAOAJ,EAAMO,UAAUM,QAAU,WACxB,GAAqB,IAAjBZ,KAAKG,QACP,OAGF,MAAMU,EAAQb,KAAKC,OACnB,IAAIa,EAASd,KAAKE,QAClB,MAAMQ,EAAOG,EAAMC,GAanB,OAZAD,EAAMC,QAAUC,EAEhBD,IACIA,EAAS,IAAe,EAATA,EAAaD,EAAMN,SAEpCP,KAAKC,OAASY,EAAMG,MAAMF,GAC1BA,EAAS,GAGXd,KAAKE,QAAUY,EACfd,KAAKG,UAEEO,CACT,EAOAX,EAAMO,UAAUW,KAAO,WACrB,GAAqB,IAAjBjB,KAAKG,QAIT,OAAOH,KAAKC,OAAOD,KAAKE,QAC1B,EAOAH,EAAMO,UAAUY,SAAW,SAAUR,GACnC,OAAsC,IAA/BV,KAAKC,OAAOkB,QAAQT,EAC7B,EAKAX,EAAMO,UAAUc,MAAQ,WACtBpB,KAAKC,OAAOM,OAASP,KAAKE,QAAUF,KAAKG,QAAU,CACrD,EAOAJ,EAAMO,UAAUe,KAAO,SAAUC,GAC3BtB,KAAKE,QAAU,IAEjBF,KAAKC,OAASD,KAAKC,OAAOe,MAAMhB,KAAKE,SACrCF,KAAKE,QAAU,GAGjBF,KAAKC,OAAOoB,KAAKC,EACnB,EClFM,MAAAC,EAAyB,CAE/BA,6BAAsD,SACpDC,GAEA,IAAIC,EAAgB,EACpB,MAAMC,EAAQ,CAACF,GACf,KAAOE,EAAMnB,OAAS,GAAG,CACvB,MAAMoB,EAAYD,EAAME,MACxB,IAAKC,EAAAA,QAAQF,GACX,SAGFF,GAAiB,EAEjB,MAAMK,EAAYH,EAAUG,UACtBC,EAAQJ,EAAUI,MAMxB,GAJIF,EAAAA,QAAQC,KACVL,GAAiBK,EAAUvB,OAASyB,EAAAA,WAAWC,cAG7CJ,EAAAA,QAAQE,GAAQ,CAClB,MAAMxB,EAASwB,EAAMxB,OACrB,IAAK,IAAI2B,EAAI,EAAGA,EAAI3B,IAAU2B,EAC5BR,EAAMf,KAAKoB,EAAMG,GAEpB,CACF,CAED,OAAOT,CACT,EAEAF,qBAA8C,SAC5CC,EACAX,EACAsB,GAEA,MAAMT,EAAQ,CAACF,GACf,KAAOE,EAAMnB,OAAS,GAAG,CACvB,MAAMoB,EAAYD,EAAME,MACxB,IAAKC,EAAAA,QAAQF,GACX,SAGF,MAAMG,EAAYH,EAAUG,UACtBC,EAAQJ,EAAUI,MAKxB,GAHAlB,EAAMsB,KAAmBN,EAAOA,QAACC,GAAaA,EAAUvB,OAAS,EACjEM,EAAMsB,KAAmBN,EAAOA,QAACE,GAASA,EAAMxB,OAAS,EAErDsB,EAAAA,QAAQC,GAAY,CACtB,MAAMM,EAAkBN,EAAUvB,OAClC,IAAK,IAAI2B,EAAI,EAAGA,EAAIE,IAAmBF,EAAGC,GAAiB,EACzDH,EAAUA,WAACK,KAAKP,EAAUI,GAAIrB,EAAOsB,EAExC,CAED,GAAIN,EAAAA,QAAQE,GAAQ,CAClB,MAAMO,EAAcP,EAAMxB,OAC1B,IAAK,IAAIgC,EAAI,EAAGA,EAAID,IAAeC,EACjCb,EAAMf,KAAKoB,EAAMQ,GAEpB,CACF,CAED,OAAOJ,CACT,EAEAZ,uBAAgD,SAC9CV,EACAsB,GAEA,MAAMC,EAAkBvB,EAAMsB,KACxBG,EAAczB,EAAMsB,KAEpBL,EAAY,IAAIU,MAAMJ,GACtBL,EAAQO,EAAc,EAAI,IAAIE,MAAMF,QAAevB,EAEzD,IACE,IAAImB,EAAI,EACRA,EAAIE,IACFF,EAAGC,GAAiBH,EAAAA,WAAWC,aAEjCH,EAAUI,GAAKF,EAAAA,WAAWS,OAAO5B,EAAOsB,GAG1C,IAAK,IAAII,EAAI,EAAGA,EAAID,IAAeC,EACjCR,EAAMQ,GAAKhB,EAAuBmB,uBAChC7B,EACAsB,GAEFA,EAAgBJ,EAAMQ,GAAGJ,qBAClBJ,EAAMQ,GAAGJ,cAGlB,MAAO,CACLL,UAAWA,EACXC,MAAOA,EACPI,cAAeA,EAEnB,GAEMQ,EAAkB,IAAIX,EAAAA,WAC5B,SAASY,EAAmBC,EAAIC,EAAIC,EAAUxC,GAQ5C,OAPAyB,EAAAA,WAAWgB,SAASF,EAAID,EAAIF,GAC5BX,EAAAA,WAAWiB,iBACTN,EACAI,EAAWxC,EACXoC,GAEFX,EAAAA,WAAWkB,IAAIL,EAAIF,EAAiBA,GAC7B,CAACA,EAAgBQ,EAAGR,EAAgBS,EAAGT,EAAgBU,EAChE,CAEA9B,EAAuB+B,mBAAqB,SAAUT,EAAIC,EAAIS,GAC5D,MACMC,EADWxB,EAAUA,WAACe,SAASF,EAAIC,GACpBS,EACfE,EAAcC,KAAKC,IAAI,EAAGD,KAAKE,KAAKC,aAAWC,KAAKN,KAC1D,OAAOE,KAAKK,IAAI,EAAGN,EACrB,EAEA,MAAMO,EAAuB,IAAIC,EAAAA,aAC3BC,EAAuB,IAAID,EAAAA,aAC3BE,EAAuB,IAAIF,EAAAA,aAC3BG,EAAoB,IAAIpC,EAAAA,WAC9BT,EAAuB8C,wBAA0B,SAC/CC,EACAzB,EACAC,EACAS,GAEA,MAAMgB,EAAKD,EAAUE,wBAAwB3B,EAAImB,GAC3CS,EAAKH,EAAUE,wBAAwB1B,EAAIoB,GAE3CV,EADQ,IAAIkB,EAAkBA,mBAACH,EAAIE,EAAIH,GAC7BK,gBAAkBpB,EAC5BE,EAAcC,KAAKC,IAAI,EAAGD,KAAKE,KAAKC,aAAWC,KAAKN,KAC1D,OAAOE,KAAKK,IAAI,EAAGN,EACrB,EAEAlC,EAAuBqD,cAAgB,SAAU/B,EAAIC,EAAIS,EAAasB,GACpE,MAAMC,EAAcvD,EAAuB+B,mBACzCT,EACAC,EACAS,GAEIhD,EAASyB,EAAUA,WAACe,SAASF,EAAIC,GACjCiC,EAA0BxE,EAASuE,EAEpCjD,EAAAA,QAAQgD,KACXA,EAAS,IAGX,MAAM/C,EAAY+C,EAClB/C,EAAUvB,OAAuB,EAAduE,EAEnB,IAAIE,EAAQ,EACZ,IAAK,IAAI9C,EAAI,EAAGA,EAAI4C,EAAa5C,IAAK,CACpC,MAAM+C,EAAIrC,EAAmBC,EAAIC,EAAIZ,EAAI6C,EAAyBxE,GAClEuB,EAAUkD,KAAWC,EAAE,GACvBnD,EAAUkD,KAAWC,EAAE,GACvBnD,EAAUkD,KAAWC,EAAE,EACxB,CAED,OAAOnD,CACT,EAEAP,EAAuB2D,mBAAqB,SAC1CZ,EACAzB,EACAC,EACAS,EACAsB,GAEA,MAAMN,EAAKD,EAAUE,wBAAwB3B,EAAImB,GAC3CS,EAAKH,EAAUE,wBAAwB1B,EAAIoB,GAC3CiB,EAAQ,IAAIT,EAAkBA,mBAACH,EAAIE,EAAIH,GAEvCd,EAAI2B,EAAMR,gBAAkBpB,EAC5BE,EAAcC,KAAKC,IAAI,EAAGD,KAAKE,KAAKC,aAAWC,KAAKN,KACpDsB,EAAcpB,KAAKK,IAAI,EAAGN,GAC1BsB,EAA0BI,EAAMR,gBAAkBG,EAEnDjD,EAAAA,QAAQgD,KACXA,EAAS,IAGX,MAAM/C,EAAY+C,EAClB/C,EAAUvB,OAAuB,EAAduE,EAEnB,IAAIE,EAAQ,EACZ,IAAK,IAAI9C,EAAI,EAAGA,EAAI4C,EAAa5C,IAAK,CACpC,MAAMkD,EAAID,EAAME,gCACdnD,EAAI6C,EACJZ,GAEIc,EAAIX,EAAUgB,wBAAwBF,EAAGhB,GAC/CtC,EAAUkD,KAAWC,EAAE9B,EACvBrB,EAAUkD,KAAWC,EAAE7B,EACvBtB,EAAUkD,KAAWC,EAAE5B,CACxB,CAED,OAAOvB,CACT,EAEA,MAAMyD,EAA0B,IAAIvD,EAAAA,WAC9BwD,EAA0B,IAAIxD,EAAAA,WAC9ByD,EAA0B,IAAIzD,EAAAA,WAC9B0D,EAA0B,IAAI1D,EAAAA,WAEpCT,EAAuBoE,8BAAgC,SACrDC,EACAC,EACAC,EACAxB,EACAyB,GAEAzB,EAAY0B,EAAAA,aAAa1B,EAAW2B,EAASA,UAACC,OAE9C,MAAMC,EAAKZ,EACX,IAAIa,EAAKZ,EACT,MAAMP,EAAIQ,EACV,IAAIY,EAAKX,EAET,GACE7D,EAAAA,QAAQ+D,IACR/D,EAAOA,QAAC+D,EAASU,aACjBzE,UAAQ+D,EAASU,WAAWC,UAC5B,CACA,MAAMzE,EAAY8D,EAASU,WAAWC,SAASC,OACzCjG,EAASuB,EAAUvB,OAAS,EAElC,IAAK,IAAI2B,EAAI,EAAGA,EAAI3B,EAAQ2B,GAAK,EAC/BF,EAAAA,WAAWyE,UAAU3E,EAAWI,EAAG+C,GAEnCX,EAAUoC,sBAAsBzB,EAAGkB,GACnCE,EAAK/B,EAAUqC,uBAAuB1B,EAAGoB,GACzCD,EAAKpE,EAAUA,WAACiB,iBAAiBkD,EAAIL,EAAWM,GAChDA,EAAKpE,EAAUA,WAACkB,IAAImD,EAAID,EAAIA,GAC5BtE,EAAUI,EAAI3B,GAAU6F,EAAGjD,EAC3BrB,EAAUI,EAAI,EAAI3B,GAAU6F,EAAGhD,EAC/BtB,EAAUI,EAAI,EAAI3B,GAAU6F,EAAG/C,EAE3B0C,IACFM,EAAKrE,EAAAA,WAAW4E,MAAM3B,EAAGoB,IAE3BD,EAAKpE,EAAUA,WAACiB,iBAAiBkD,EAAIN,EAAWO,GAChDA,EAAKpE,EAAUA,WAACkB,IAAImD,EAAID,EAAIA,GAC5BtE,EAAUI,GAAKkE,EAAGjD,EAClBrB,EAAUI,EAAI,GAAKkE,EAAGhD,EACtBtB,EAAUI,EAAI,GAAKkE,EAAG/C,CAEzB,CACD,OAAOuC,CACT,EAEArE,EAAuBsF,6BAA+B,SACpDrF,EACAsF,EACAxC,GAIA,MAAMyC,EAAW,GACXC,EAAQ,IAAIjH,EAElB,IAAImC,EACAK,EACAhC,EACJ,IAJAyG,EAAMvG,QAAQe,GAIU,IAAjBwF,EAAMzG,QAAc,CACzB,MAAM0G,EAAYD,EAAMpG,UACxB,IAAIsG,EAAYD,EAAUnF,UAC1B,GAAIgF,EAEF,IADAvG,EAAS2G,EAAU3G,OACd2B,EAAI,EAAGA,EAAI3B,EAAQ2B,IACtBoC,EAAUqC,uBAAuBO,EAAUhF,GAAIgF,EAAUhF,IAQ7D,GALAgF,EAAYC,EAAqBA,sBAC/BD,EACAlF,EAAAA,WAAWoF,eACX,GAEEF,EAAU3G,OAAS,EACrB,SAGF,MAAM8G,EAAcJ,EAAUlF,MAAQkF,EAAUlF,MAAMxB,OAAS,EAE/D,IAAK2B,EAAI,EAAGA,EAAImF,EAAanF,IAAK,CAChC,MAAMoF,EAAOL,EAAUlF,MAAMG,GAC7B,IAAIqF,EAAgBD,EAAKxF,UACzB,GAAIgF,EAEF,IADAvG,EAASgH,EAAchH,OAClBgC,EAAI,EAAGA,EAAIhC,IAAUgC,EACxB+B,EAAUqC,uBAAuBY,EAAchF,GAAIgF,EAAchF,IAQrE,GALAgF,EAAgBJ,EAAqBA,sBACnCI,EACAvF,EAAAA,WAAWoF,eACX,GAEEG,EAAchH,OAAS,EACzB,SAEFwG,EAASpG,KAAK4G,GAEd,IAAIC,EAAmB,EAKvB,IAJI3F,EAAOA,QAACyF,EAAKvF,SACfyF,EAAmBF,EAAKvF,MAAMxB,QAG3BgC,EAAI,EAAGA,EAAIiF,EAAkBjF,IAChCyE,EAAMvG,QAAQ6G,EAAKvF,MAAMQ,GAE5B,CAEDwE,EAASpG,KAAKuG,EACf,CAED,OAAOH,CACT,EAEAxF,EAAuBkG,sBAAwB,SAC7CjG,EACAkG,EACAZ,EACAxC,GAIA,MAAM3C,EAAY,GACZoF,EAAW,GAEXC,EAAQ,IAAIjH,EAGlB,IAFAiH,EAAMvG,QAAQe,GAEU,IAAjBwF,EAAMzG,QAAc,CACzB,MAAM0G,EAAYD,EAAMpG,UACxB,IAAIsG,EAAYD,EAAUnF,UAC1B,MAAMC,EAAQkF,EAAUlF,MAExB,IAAIG,EACA3B,EACJ,GAAIuG,EAEF,IADAvG,EAAS2G,EAAU3G,OACd2B,EAAI,EAAGA,EAAI3B,EAAQ2B,IACtBoC,EAAUqC,uBAAuBO,EAAUhF,GAAIgF,EAAUhF,IAS7D,GALAgF,EAAYC,EAAqBA,sBAC/BD,EACAlF,EAAAA,WAAWoF,eACX,GAEEF,EAAU3G,OAAS,EACrB,SAGF,IAAIoH,EAAcD,EAAkBR,GACpC,IAAKrF,EAAAA,QAAQ8F,GACX,SAEF,MAAMC,EAAc,GAEpB,IAAIC,EAAuBC,EAAAA,gBAAgBC,sBACzCJ,GAEEE,IAAyBG,EAAYA,aAACC,YACxCN,EAAYO,UACZhB,EAAYA,EAAUlG,QAAQkH,WAGhC,IAAIpG,EAAYoF,EAAUlG,QAC1B,MAAMqG,EAAcxF,EAAAA,QAAQE,GAASA,EAAMxB,OAAS,EAC9C4H,EAAe,GACrB,IAAI5F,EAEJ,IAAKL,EAAI,EAAGA,EAAImF,EAAanF,IAAK,CAChC,MAAMoF,EAAOvF,EAAMG,GACnB,IAAIqF,EAAgBD,EAAKxF,UACzB,GAAIgF,EAEF,IADAvG,EAASgH,EAAchH,OAClBgC,EAAI,EAAGA,EAAIhC,IAAUgC,EACxB+B,EAAUqC,uBAAuBY,EAAchF,GAAIgF,EAAchF,IASrE,GALAgF,EAAgBJ,EAAqBA,sBACnCI,EACAvF,EAAAA,WAAWoF,eACX,GAEEG,EAAchH,OAAS,EACzB,SAGF,MAAM6H,EAAkBV,EAAkBH,GAC1C,IAAK1F,EAAAA,QAAQuG,GACX,SAGFP,EAAuBC,EAAeA,gBAACC,sBACrCK,GAEEP,IAAyBG,EAAYA,aAACC,YACxCG,EAAgBF,UAChBX,EAAgBA,EAAcvG,QAAQkH,WAGxCC,EAAaxH,KAAK4G,GAClBK,EAAYjH,KAAKmB,EAAUvB,QAC3BuB,EAAYA,EAAUuG,OAAOd,GAC7BI,EAAcA,EAAYU,OAAOD,GAEjC,IAAIZ,EAAmB,EAKvB,IAJI3F,EAAOA,QAACyF,EAAKvF,SACfyF,EAAmBF,EAAKvF,MAAMxB,QAG3BgC,EAAI,EAAGA,EAAIiF,EAAkBjF,IAChCyE,EAAMvG,QAAQ6G,EAAKvF,MAAMQ,GAE5B,CAEDZ,EAAUhB,KAAK,CACbuG,UAAWA,EACXnF,MAAOoG,IAETpB,EAASpG,KAAK,CACZmB,UAAWA,EACX6F,YAAaA,EACb5F,MAAO6F,GAEV,CAED,MAAO,CACLjG,UAAWA,EACXoF,SAAUA,EAEd,EAEA,MAAMuB,EAAqC,IAAIC,EAAAA,WACzCC,EAAqC,IAAIxG,EAAAA,WACzCyG,EAAqC,IAAIC,EAAAA,WACzCC,EAAkC,IAAIC,EAAAA,QAC5CrH,EAAuBsH,yBAA2B,SAChDC,EACAC,EACAjH,EACAkH,EACAnE,GAEA,MAAMoE,EAAWP,EAAAA,WAAWQ,cAC1BJ,EACAE,EACAP,GAEIU,EAAgBP,EAAAA,QAAQQ,eAC5BH,EACAN,GAGF,IAAIU,EAAOC,OAAOC,kBACdC,EAAOF,OAAOG,kBACdC,EAAOJ,OAAOC,kBACdI,EAAOL,OAAOG,kBAElB,MAAMlJ,EAASuB,EAAUvB,OACzB,IAAK,IAAI2B,EAAI,EAAGA,EAAI3B,IAAU2B,EAAG,CAC/B,MAAM+C,EAAIjD,EAAAA,WAAW4E,MACnB9E,EAAUI,GACVsG,GAEFI,EAAAA,QAAQgB,iBAAiBT,EAAelE,EAAGA,GAC3C,MAAM4E,EAAKd,EAAiB9D,EAAGqD,GAE3BzG,EAAAA,QAAQgI,KACVR,EAAO3F,KAAKoG,IAAIT,EAAMQ,EAAG1G,GACzBqG,EAAO9F,KAAKC,IAAI6F,EAAMK,EAAG1G,GAEzBuG,EAAOhG,KAAKoG,IAAIJ,EAAMG,EAAGzG,GACzBuG,EAAOjG,KAAKC,IAAIgG,EAAME,EAAGzG,GAE5B,CAMD,OAJAyB,EAAO1B,EAAIkG,EACXxE,EAAOzB,EAAIsG,EACX7E,EAAOkF,MAAQP,EAAOH,EACtBxE,EAAOmF,OAASL,EAAOD,EAChB7E,CACT,EAEAtD,EAAuB0I,4BAA8B,SACnD3F,EACA4F,EACAC,EACApE,EACAqE,EACAC,GAEA,IAAIC,EAAUxC,EAAAA,gBAAgByC,YAAYL,EAAQvC,YAAauC,EAAQnI,OAGnEuI,EAAQ/J,OAAS,IACnB+J,EAAU,CAAC,EAAG,EAAG,IAGnB,MAAMxI,EAAYoI,EAAQpI,UAE1B,GAAIiE,EAAmB,CACrB,MAAMxF,EAASuB,EAAUvB,OACnBiK,EAAqB,IAAIhI,MAAe,EAATjC,GACrC,IAAIyE,EAAQ,EACZ,IAAK,IAAI9C,EAAI,EAAGA,EAAI3B,EAAQ2B,IAAK,CAC/B,MAAM+C,EAAInD,EAAUI,GACpBsI,EAAmBxF,KAAWC,EAAE9B,EAChCqH,EAAmBxF,KAAWC,EAAE7B,EAChCoH,EAAmBxF,KAAWC,EAAE5B,CACjC,CACD,MAAMuC,EAAW,IAAI6E,WAAS,CAC5BnE,WAAY,CACVC,SAAU,IAAImE,EAAAA,kBAAkB,CAC9BC,kBAAmBC,EAAiBA,kBAACC,OACrCC,uBAAwB,EACxBtE,OAAQgE,KAGZF,QAASA,EACTS,cAAeC,EAAaA,cAACC,YAG/B,OAAIb,EAAac,OACRC,EAAgBA,iBAACC,cAAcxF,GAGjCA,CACR,CAED,OAAIyE,IAAYgB,EAAOA,QAACC,SACfxD,EAAeA,gBAACyD,mBACrBjH,EACAxC,EACAwI,EACAH,GAEOE,IAAYgB,EAAOA,QAACG,MACtB1D,EAAeA,gBAAC2D,4BACrBnH,EACAxC,EACAwI,EACAH,QALG,CAQT,EAEA,MAAMuB,EAA+B,GAC/BC,EAAY,IAAI3J,EAAAA,WAChB4J,EAAY,IAAI5J,EAAAA,WAEtBT,EAAuBsK,oBAAsB,SAC3C/J,EACAwC,EACA6F,EACApE,EACAsE,GAEA,IAAIyB,EACAC,EACA7J,EACAY,EACAuD,EAEA9F,EAASuB,EAAUvB,OACnByE,EAAQ,EAEZ,GAAKe,EAsEH,IAFAgG,EAAyB,EAATxL,EAAa,EAC7BuL,EAAgB,IAAItJ,MAAsB,EAAhBuJ,GACrB7J,EAAI,EAAGA,EAAI3B,EAAQ2B,IACtBY,EAAKhB,EAAUI,GACfmE,EAAKvE,GAAWI,EAAI,GAAK3B,GACzBuL,EAAc9G,GAAS8G,EAAc9G,EAAQ+G,GAAiBjJ,EAAGK,IAC/D6B,EACF8G,EAAc9G,GAAS8G,EAAc9G,EAAQ+G,GAAiBjJ,EAAGM,IAC/D4B,EACF8G,EAAc9G,GAAS8G,EAAc9G,EAAQ+G,GAAiBjJ,EAAGO,IAC/D2B,EACF8G,EAAc9G,GAAS8G,EAAc9G,EAAQ+G,GAAiB1F,EAAGlD,IAC/D6B,EACF8G,EAAc9G,GAAS8G,EAAc9G,EAAQ+G,GAAiB1F,EAAGjD,IAC/D4B,EACF8G,EAAc9G,GAAS8G,EAAc9G,EAAQ+G,GAAiB1F,EAAGhD,IAC/D2B,MApFkB,CACtB,MAAMzB,EAAcM,EAAAA,WAAWmI,YAC7B7B,EACA7F,EAAU2H,eAGZ,IAAInH,EAAc,EAClB,GAAIuF,IAAYgB,EAAOA,QAACC,SACtB,IAAKpJ,EAAI,EAAGA,EAAI3B,EAAQ2B,IACtB4C,GAAevD,EAAuB+B,mBACpCxB,EAAUI,GACVJ,GAAWI,EAAI,GAAK3B,GACpBgD,QAGC,GAAI8G,IAAYgB,EAAOA,QAACG,MAC7B,IAAKtJ,EAAI,EAAGA,EAAI3B,EAAQ2B,IACtB4C,GAAevD,EAAuB8C,wBACpCC,EACAxC,EAAUI,GACVJ,GAAWI,EAAI,GAAK3B,GACpBgD,GAON,IAFAwI,EAAyC,GAAxBjH,EAAcvE,GAC/BuL,EAAgB,IAAItJ,MAAsB,EAAhBuJ,GACrB7J,EAAI,EAAGA,EAAI3B,EAAQ2B,IAAK,CAI3B,IAAIgK,EAHJpJ,EAAKhB,EAAUI,GACfmE,EAAKvE,GAAWI,EAAI,GAAK3B,GAGrB8J,IAAYgB,EAAOA,QAACC,SACtBY,EAAgB3K,EAAuBqD,cACrC9B,EACAuD,EACA9C,EACAmI,GAEOrB,IAAYgB,EAAOA,QAACG,QAC7BU,EAAgB3K,EAAuB2D,mBACrCZ,EACAxB,EACAuD,EACA9C,EACAmI,IAGJ,MAAMS,EAAsBD,EAAc3L,OAC1C,IAAK,IAAIgC,EAAI,EAAGA,EAAI4J,IAAuB5J,IAAKyC,EAC9C8G,EAAc9G,GAASkH,EAAc3J,GACrCuJ,EAAc9G,EAAQ+G,GAAiBG,EAAc3J,GAGvDuJ,EAAc9G,GAASqB,EAAGlD,EAC1B2I,EAAc9G,EAAQ+G,GAAiB1F,EAAGlD,IACxC6B,EAEF8G,EAAc9G,GAASqB,EAAGjD,EAC1B0I,EAAc9G,EAAQ+G,GAAiB1F,EAAGjD,IACxC4B,EAEF8G,EAAc9G,GAASqB,EAAGhD,EAC1ByI,EAAc9G,EAAQ+G,GAAiB1F,EAAGhD,IACxC2B,CACH,CACL,CAqBEzE,EAASuL,EAAcvL,OACvB,MAAM+J,EAAU8B,EAAAA,cAAcC,iBAC5B9L,EAAS,EACTA,EAA4B,EAAnBuB,EAAUvB,QAErB,IAAI+L,EAAY,EAGhB,IAFA/L,GAAU,EAEL2B,EAAI,EAAGA,EAAI3B,EAAQ2B,IAAK,CAC3B,MAAMqK,EAAKrK,EACLsK,EAAKD,EAAK,EACVE,EAAKF,EAAKhM,EACVmM,EAAKD,EAAK,EAEhB3J,EAAKd,EAAUA,WAACyE,UAAUqF,EAAoB,EAALS,EAAQZ,GACjDtF,EAAKrE,EAAUA,WAACyE,UAAUqF,EAAoB,EAALU,EAAQZ,GAE/C5J,EAAAA,WAAWoF,cACTtE,EACAuD,EACAxC,EAAAA,WAAW8I,UACX9I,EAAAA,WAAW8I,aAOfrC,EAAQgC,KAAeC,EACvBjC,EAAQgC,KAAeG,EACvBnC,EAAQgC,KAAeE,EACvBlC,EAAQgC,KAAeE,EACvBlC,EAAQgC,KAAeG,EACvBnC,EAAQgC,KAAeI,EACxB,CAED,OAAO,IAAIjC,EAAAA,SAAS,CAClBnE,WAAY,IAAIsG,EAAAA,mBAAmB,CACjCrG,SAAU,IAAImE,EAAAA,kBAAkB,CAC9BC,kBAAmBC,EAAiBA,kBAACC,OACrCC,uBAAwB,EACxBtE,OAAQsF,MAGZxB,QAASA,EACTS,cAAeC,EAAaA,cAACC,WAEjC"}