interface Window {
  CESIUM_BASE_URL: string;
  turf: {
    polygon(data: any): number;
    area(data: any): number;
    point(data: any): number;
    bearing(point1: any, point2: any): number;
    midpoint(point1: any, point2: any): any;
    distance(from: any, to: any, options?: any): number;
    center(data: any): any;
    featureCollection(data: any): any;
    destination(point: any, distance: number, bearing: number, options?: any): any;
  };
}
