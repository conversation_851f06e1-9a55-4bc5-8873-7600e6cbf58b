<!--
* <AUTHOR> wll
* @Date :  2022/10/14
* @Version : 1.0
* @Content : 删除功能组件
-->
<template>
  <div class="close-block" :class="[{ 'close-block--hover': !notAllowClose && canClosed }]">
    <div class="close-icon" @click="handleClose"></div>
    <slot />
  </div>
</template>

<script>
export default {
  name: 'SimCloseBlock'
};
</script>
<script setup>
import { computed } from 'vue';

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  index: {
    type: Number,
    required: true
  },
  minLength: {
    type: Number,
    required: true
  },
  canClosed: {
    type: Boolean,
    default: true
  }
});
const emits = defineEmits(['close']);

const notAllowClose = computed(() => props.data.length <= props.minLength);

const handleClose = () => {
  if (notAllowClose.value) {
    return;
  }
  // eslint-disable-next-line vue/no-mutating-props
  props.data.splice(props.index, 1);
  emits('close');
};
</script>
<style scoped lang="less">
.close-block {
  position: relative;
  padding: 8px 5px 0;
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid transparent;
  //border-radius: 3px;
  .close-icon {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    cursor: pointer;
    background-color: var(--primary-color);
    transform: translate(20px, -20px) rotateZ(45deg);
    z-index: 10;
    &::before {
      content: 'x';
      position: absolute;
      top: 20px;
      left: 17px;
      transform: rotateZ(-45deg);
    }
  }
}
.close-block--hover {
  &:hover {
    border-color: var(--primary-color);
    .close-icon {
      display: block;
    }
  }
}
</style>
