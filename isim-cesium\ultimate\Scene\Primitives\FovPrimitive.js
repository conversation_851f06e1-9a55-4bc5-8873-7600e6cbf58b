/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/28 09:29:10
 * @description Field Of View (二维视场角)
 * @version 1.0
 * */
import { Cartesian3, Color, defaultValue, HeightReference } from 'cesium';
import AbstractPrimitive from './AbstractPrimitive';
import PolylinePrimitive from './PolylinePrimitive.js';
import { updateClamping } from './PrimitiveMethod.js';
import PolygonVolumePrimitive from './PolygonVolumePrimitive.js';

export default class FovPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._heightReference = defaultValue(options.heightReference, HeightReference.NONE);
    this._lineOptions = {
      depthFailShow: false,
      color: Color.YELLOW,
      width: 1,
      materialType: 'Color',
      loop: true,
      ...options.lineOptions
    };
    this._fillOptions = {
      depthFailShow: false,
      color: Color.YELLOW.withAlpha(0.2),
      perPositionHeight: true,
      ...options.fillOptions
    };
    this._outline = defaultValue(options.outline, true);
    this._fill = defaultValue(options.fill, true);
    this._scene = options.scene;
    this._update = true;
    this._heightOffset = defaultValue(options.heightOffset, 0);
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive = this._primitive && this._primitive.destroy();
      this._linePrimitive = this._linePrimitive && this._linePrimitive.destroy();
      if (!this._positions || this._positions.length === 0) {
        return;
      }

      this._updateFOV2D();
    }

    this._primitive?.update(frameState);
    this._linePrimitive?.update(frameState);
  }

  _updateFOV2D() {
    this._fill &&
      (this._primitive = new PolygonVolumePrimitive({
        ...this._fillOptions,
        positions: this._positions,
        id: this._id
      }));
    this._outline &&
      (this._linePrimitive = new PolylinePrimitive({
        ...this._lineOptions,
        positions: this._positions,
        id: this._id
      }));
  }

  destroy() {
    this._linePrimitive = this._linePrimitive && this._linePrimitive.destroy();
    super.destroy();
  }

  set angle(value) {
    if (this._angle === value) {
      return;
    }
    this._angle = value;
    this._update = true;
  }

  set radius(value) {
    if (this._radius === value) {
      return;
    }
    this._radius = value;
    this._update = true;
  }

  /**
   * FOV 的中心与正北的角
   * @param {number} value 与正北的夹角
   */
  set azimuth(value) {
    if (this._azimuth === value) {
      return;
    }
    this._azimuth = value;
    this._update = true;
  }

  set position(value) {
    if (Cartesian3.equals(this._position, value)) {
      return;
    }
    this._position = value;
    updateClamping(this);
    this._update = true;
  }

  set lineOptions(value) {
    if (this._linePrimitive) {
      Object.keys(value).forEach((k) => {
        this._linePrimitive[k] = value[k];
      });
    }
    this._lineOptions = {
      ...this._lineOptions,
      ...value
    };
  }

  set outline(value) {
    if (this._outline === value) {
      return;
    }
    this._outline = value;
    this._update = true;
  }

  set fill(value) {
    if (this._fill === value) {
      return;
    }
    this._fill = value;
    this._update = true;
  }

  set fillOptions(value) {
    if (this._primitive) {
      Object.keys(value).forEach((k) => {
        this._primitive[k] = value[k];
      });
    }
    this._fillOptions = {
      ...this._fillOptions,
      ...value
    };
  }
}
