<!--
* @Author: 张乐
* @Date: 2023/6/8 20:24
* @Version: 1.0
* @Content: SimTableColumn
-->
<template>
  <el-table-column class="sim-table-column" v-bind="$attrs">
    <template v-if="$attrs.sortable" #header="{ $index }">
      <div class="sim-table-column__header" @click="handleSort($index)">
        <span>{{ $attrs.label }}</span>
        <template v-if="getCurColumnSort($index)">
          <i v-if="curColumnSort.orderType === 'ASC'" class="sort-icon sim-icon-shengxu"></i>
          <i v-if="curColumnSort.orderType === 'DESC'" class="sort-icon sim-icon-jiangxu"></i>
        </template>
      </div>
    </template>

    <template v-if="$slots?.default" #default="columnData">
      <slot v-bind="columnData"></slot>
    </template>
  </el-table-column>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'SimTableColumn'
});
type OrderType = 'ASC' | 'SESC' | null;

interface ColumnSort {
  name: string;
  orderName: number;
  orderType: OrderType;
}

const emits = defineEmits(['update:sort-list']);

// 已参加排序列表
const { sortList, handleOrderType, sortChange } = inject<any>('sortProvide');

// 当前列排序信息
const curColumnSort = ref<ColumnSort>();
// 当前列排序序号
let curColumnSortIndex = sortList?.length;

/**
 * 获取当前列排序信息
 * @param index
 * @param label
 */
const getCurColumnSort = (index: number) => {
  const sortIndex = sortList?.findIndex((it: ColumnSort) => it.orderName === index);
  const info = sortList[sortIndex];
  curColumnSort.value = info;
  curColumnSortIndex = sortIndex;
  return info;
};

/**
 * 排序
 * @param index
 */
const handleSort = (index: number) => {
  const orderType = handleOrderType(curColumnSort.value?.orderType || null);
  const list = sortList;
  // 不参加排序
  if (!orderType) {
    list.splice(curColumnSortIndex, 1);
  }

  if (orderType) {
    const info = {
      orderName: index,
      orderType
    };
    // 新增排序信息
    if (curColumnSortIndex === -1) {
      list.push(info);
    } else {
      // 编辑排序信息
      list.splice(curColumnSortIndex, 1, info);
    }
  }
  emits('update:sort-list', list);
  sortChange();
};
</script>

<style scoped lang="less">
.sim-table-column__header {
  display: inline-block;
  //width: 100%;
  //
  //.sort-icon {
  //  position: relative;
  //  //top: 2px;
  //  left: 4px;
  //  color: var(--primary-color);
  //}
}
</style>
