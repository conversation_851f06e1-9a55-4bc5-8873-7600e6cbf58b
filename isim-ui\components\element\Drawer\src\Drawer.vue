<!--
* <AUTHOR> zhang<PERSON>e
* @Date :  2023/4/10
* @Version : 1.0
* @Content : Drawer  :before-close="handleClose"
-->
<template>
  <el-drawer
    class="sim-drawer"
    :append-to-body="true"
    destroy-on-close
    :show-close="false"
    direction="btt"
    v-bind="$attrs"
    :before-close="handleClose"
  >
    <template #header="{ close }">
      <div class="flex-wrap sim-drawer__header">
        <div class="sim-drawer__header-title">{{ title }}</div>
        <div class="sim-drawer__header-close" @click="close">x</div>
      </div>
    </template>

    <div class="sim-drawer__line-left" />
    <div class="sim-drawer__line-right" />

    <div class="sim-drawer__content">
      <slot></slot>
    </div>

    <div v-if="showFooter" class="sim-drawer__footer">
      <div class="sim-drawer__footer-bg"></div>
      <slot name="footer"></slot>
    </div>
  </el-drawer>
</template>

<script lang="ts">
export default {
  name: 'SimDrawer'
};
</script>
<script setup lang="ts">
// 第三方包

// 组件

// hooks

interface Props {
  title: string;
  showFooter: boolean;
}

withDefaults(defineProps<Props>(), {
  title: '',
  showFooter: true
});

const emit = defineEmits(['update:model-value', 'close']);

const handleClose = () => {
  emit('update:model-value', false);
  emit('close');
};
</script>

<style lang="less">
@header-height: 56px;

:root .el-drawer {
  --el-drawer-padding-primary: 0;
}

.sim-drawer {
  height: calc(100vh - var(--rt-header-height)) !important;
  border-bottom: 1px solid rgba(var(--primary-color-val), 1);
  border-top: 1px solid rgba(var(--primary-color-val), 1);
  box-shadow:
    inset 0 12px 12px -12px var(--primary-color),
    inset 0px -12px 12px -12px var(--primary-color);

  &__side {
    position: absolute;
    top: 50%;
    margin-top: -182px;
    display: block;
    width: 8px;
    height: 365px;

    &-left {
      left: 0;
    }

    &-right {
      right: 0;
    }
  }

  &__line-left {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30px;

    -webkit-mask-image: url('./assets/drawer-left-line.svg');
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
    background: var(--primary-color);
  }

  &__line-right {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 30px;

    -webkit-mask-image: url('./assets/drawer-right-line.svg');
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
    background: var(--primary-color);
    filter: drop-shadow(0 0 116px #fff);
  }

  .el-drawer__header {
    margin-bottom: 16px;
    padding: 0;
  }

  &__header {
    width: 100%;
    flex: none !important;
    box-sizing: border-box;
    height: @header-height;
    text-align: center;
    background: rgba(var(--text-color-val), 0.1);

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      margin-left: -225px;
      display: block;
      width: 450px;
      height: 6px;
      -webkit-mask-image: url('./assets/drawer-header.svg');
      background: var(--primary-color);
    }

    &-title {
      width: 100%;
      font-size: 16px;
    }

    &-close {
      box-sizing: border-box;
      position: absolute;
      top: 0;
      right: 0;
      padding-left: 6px;
      width: 47px;
      height: 29px;
      background: radial-gradient(rgba(var(--primary-color-val), 0.29803922) 0%, rgba(var(--primary-color-val), 0) 169%),
        linear-gradient(
          180deg,
          rgba(var(--btn-top-color-val), 1) 0%,
          rgba(var(--btn-center-color-val), 1) 73%,
          rgba(var(--btn-bottom-color-val), 1) 100%
        );
      clip-path: polygon(0 0, 100% 0, 100% 100%, 20% 100%);
      border: 1px solid var(--primary-color);
      box-shadow: inset 0 0 8px var(--primary-color);
      text-align: center;
      line-height: 26px;
      cursor: pointer;
      font-size: 26px;
      z-index: 9999;
    }
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
  }

  &__content {
    margin-bottom: 16px;
    padding: 0 70px 0;
    flex: 1;
    height: 100px;
    overflow: auto;
  }

  &__footer {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 59px;

    &-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      -webkit-mask-image: url('./assets/drawer-footer.svg');
      -webkit-mask-repeat: no-repeat;
      -webkit-mask-position: center center;
      background: rgba(var(--primary-color-val), 1);
    }

    .sim-button {
      margin: 0 16px;
    }
  }
}
</style>
