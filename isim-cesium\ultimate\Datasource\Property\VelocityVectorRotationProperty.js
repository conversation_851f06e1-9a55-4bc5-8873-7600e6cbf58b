/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/11/01 15:17:51
 * @description solve Billboard Direction problem
 * @version 1.2
 * */

import { Cartesian3, Cartographic, defined, <PERSON><PERSON><PERSON><PERSON><PERSON>r, Ellipsoid, Event, JulianDate, Property } from 'cesium';

function VelocityVectorRotationProperty(position) {
  this._position = undefined;
  this._subscription = undefined;
  this._definitionChanged = new Event();
  this._currentRotation = undefined;
  this.position = position;
}

Object.defineProperties(VelocityVectorRotationProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._position);
    }
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },
  position: {
    get: function () {
      return this._position;
    },
    set: function (value) {
      let oldValue = this._position;
      if (oldValue !== value) {
        if (defined(oldValue)) {
          this._subscription();
        }

        this._position = value;

        if (defined(value)) {
          this._subscription = value._definitionChanged.addEventListener(function () {
            this._definitionChanged.raiseEvent(this);
          }, this);
        }

        this._definitionChanged.raiseEvent(this);
      }
    }
  }
});

const position1Scratch = new Cartesian3();
const position2Scratch = new Cartesian3();
const cartographic1Scratch = new Cartographic();
const cartographic2Scratch = new Cartographic();
const timeScratch = new JulianDate();
const step = 1.0 / 60.0;
let ratio = undefined;
let deltaLon = undefined;

VelocityVectorRotationProperty.prototype.getValue = function (time) {
  const rotation = this._getValue(time);
  if (!rotation) {
    return this._currentRotation;
  }
  this._currentRotation = rotation;
  return this._currentRotation;
};
/**
 * @private
 */
VelocityVectorRotationProperty.prototype._getValue = function (time) {
  //>>includeStart('debug', pragmas.debug);
  if (!defined(time)) {
    throw new DeveloperError('time is required');
  }
  //>>includeEnd('debug');
  const property = this._position;
  if (Property.isConstant(property)) {
    return undefined;
  }

  let position1 = property.getValue(time, position1Scratch);
  let position2 = property.getValue(JulianDate.addSeconds(time, step, timeScratch), position2Scratch);

  //If we don't have a position for now, return undefined.
  if (!defined(position1)) {
    return undefined;
  }

  //If we don't have a position for now + step, see if we have a position for now - step.
  if (!defined(position2)) {
    position2 = position1;
    position1 = property.getValue(JulianDate.addSeconds(time, -step, timeScratch), position2Scratch);

    if (!defined(position1)) {
      return undefined;
    }
  }

  if (Cartesian3.equals(position1, position2)) {
    return undefined;
  }

  Cartographic.fromCartesian(position1, Ellipsoid.WGS84, cartographic1Scratch);
  Cartographic.fromCartesian(position2, Ellipsoid.WGS84, cartographic2Scratch);
  deltaLon = cartographic2Scratch.longitude - cartographic1Scratch.longitude;
  ratio = (cartographic2Scratch.latitude - cartographic1Scratch.latitude) / deltaLon;
  return Math.atan(ratio) - ((deltaLon / Math.abs(deltaLon)) * Math.PI) / 2;
};

VelocityVectorRotationProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof VelocityVectorRotationProperty && Property.equals(this._position, other._position))
  );
};

export default VelocityVectorRotationProperty;
