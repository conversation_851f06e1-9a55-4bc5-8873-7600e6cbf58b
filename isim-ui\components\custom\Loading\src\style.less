/**
 * @Author: 张乐
 * @Date: 2023/5/30 16:25
 * @Version: 1.0
 * @Content: 
 */

.sim-loading {
  position: relative;
  margin: 0 auto;
  width: 120px;
  height: 120px;
  background: url("/assets/imgs/component/loading.png") no-repeat center;
  background-size: 100%;

  &_inner {
    position: absolute;
    top: 0;
    left: 0;
    width: 120px;
    height: 120px;
    -webkit-mask-image: url("/assets/imgs/component/loading-inner.png");
    background:var(--primary-color);
    background-size: 100%;
    animation: round 0.8s linear infinite;
  }
}

@keyframes round {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}