<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/10
* @Version : 1.0
* @Content : demo
-->
<template>
  <div class="sim-ui">
    <el-menu class="sim-ui-menu" @select="handleSelect" default-active="card-nodedemo">
      <el-menu-item v-for="(_item, name) in comMap" :key="name" :index="name">{{ name }}</el-menu-item>
    </el-menu>
    <div class="sim-ui-com">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  name: 'SimDemo'
});
</script>
<script lang="ts" setup>
import { AsyncComponentLoader } from 'vue';

const components = import.meta.glob('../components/**/*Demo.vue');

const comMap: any = {};
Object.entries(components).forEach(([path, com]) => {
  let comName = path.split('/').at(-1) as string;
  comName = comName.replace(/\.vue$/g, '').toLowerCase();
  comMap[comName] = defineAsyncComponent({
    loader: com as AsyncComponentLoader
  });
});
const currentComName = ref('gantt-timelinedemo');
const currentComponent = computed(() => comMap[currentComName.value.toLowerCase()]);
const handleSelect = (value: string) => {
  currentComName.value = value;
};
</script>
<style scoped lang="less">
.sim-ui {
  --el-menu-text-color: white;
  display: flex;
  height: 100vh;
  --menu-width: 300px;
  &-menu {
    width: var(--menu-width);
    height: 100%;
  }
  &-com {
    width: calc(100% - var(--menu-width));
  }
}
</style>
