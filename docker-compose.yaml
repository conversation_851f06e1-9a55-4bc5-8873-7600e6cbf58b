services:
  web:
    image: nginx
    container_name: xian-web
    network_mode: host
    volumes:
      - "./web/default.conf:/etc/nginx/conf.d/default.conf"
      - "./web/dist:/dist"
      - "./web/GoogleEarth:/map"
  function:
    image: eclipse-temurin:17.0.15_6-jdk-jammy
    container_name: xian-function
    volumes:
      - "./function/model-function-domain-1.0.0-SNAPSHOT.jar:/root/app.jar"
      - "./function/gdal-raster:/root/gdal-raster"
    working_dir: /root
    command:
      - "bash"
      - "-c"
      - "exec java -Dfile.encoding=UTF8 -Duser.timezone=GMT+8 -jar app.jar"
    ports:
      - "7001:8080"