/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/26 14:32:34
 * @description BaseRadarFeature update
 * @version 3.0
 * */
import BaseRadarPrimitive from '../../Scene/Primitives/BaseRadarPrimitive';
import AbstractFeature from './AbstractFeature.js';
import { Quaternion } from 'cesium';

export default class BaseRadarFeature extends AbstractFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this.create();
  }

  create() {
    this.primitive = new BaseRadarPrimitive(this._options);
  }

  update(time) {
    const position = this.entity.position.getValue(time);
    const q = this.orientation?.getValue(time);

    this._options.cartesian = position;
    if (q) {
      this._options.sarQuaternion = q;
    } else {
      this._options.sarQuaternion = Quaternion.ZERO;
    }
    if (this.primitive) {
      this._updateOptions(time, this.primitive);
    }
  }
}
