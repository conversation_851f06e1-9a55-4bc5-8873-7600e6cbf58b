/**
 * <AUTHOR>
 * @email jayjay<PERSON>@yeah.net
 * @date 2022/05/26 17:13:14
 * @description Model Feature Test
 * @version 1.0
 * */

import ModelPrimitive from '../../Scene/Primitives/ModelPrimitive.js';
import AbstractFeature from './AbstractFeature';

export default class ModelFeature extends AbstractFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this._orientation = this._options.orientation;
    this.create();
  }

  create() {
    const { heightReference, url, scene } = this._options;
    this.primitive = new ModelPrimitive({ heightReference, url, scene });
  }
}
