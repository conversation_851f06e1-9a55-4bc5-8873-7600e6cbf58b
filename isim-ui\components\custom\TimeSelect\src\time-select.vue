<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : time-select.vue
-->
<template>
  <div class="sim-time-select">
    <el-input v-model="timeValue" maxlength="11" @change="handleChange($event)">
      <template #append>
        <el-select v-model="unitValue" placeholder="单位" @change="handleChange($event)">
          <el-option label="天" :value="TIME_UNIT.day" />
          <el-option label="时" :value="TIME_UNIT.hour" />
        </el-select>
      </template>
    </el-input>
  </div>
</template>

<script setup lang="ts">
import { parseTimeValue, TIME_UNIT } from './time-select-format';
defineOptions({
  name: 'SimTimeSelect'
});
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});

const emits = defineEmits(['update:modelValue', 'change']);

const { time, unit } = parseTimeValue(props.modelValue);

const timeValue = ref(time);
const unitValue = ref(unit);

const handleChange = (event: MouseEvent) => {
  const time = toRaw(timeValue.value);
  const unit = toRaw(unitValue.value);
  emits('update:modelValue', `${time}-${unit}`);
  emits('change', `${time}-${unit}`, event);
};
watch(
  () => props.modelValue,
  (val) => {
    const { time, unit } = parseTimeValue(val);
    timeValue.value = time;
    unitValue.value = unit;
  }
);
</script>

<style scoped lang="less">
.sim-time-select {
  width: 100%;
  --append-width: 60px;
  :deep(.el-input-group__append) {
    width: var(--append-width);
  }
  :deep(.el-select) {
    width: var(--append-width);
  }
}
</style>
