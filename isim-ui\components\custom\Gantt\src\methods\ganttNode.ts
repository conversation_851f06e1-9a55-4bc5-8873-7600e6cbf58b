/**
 * @Author: songjimin
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
 */
import type { GanttDataOptions, GanttProps, LabelField } from '../gantt';

export class GanttNode {
  data: GanttDataOptions[];
  labelField: LabelField;
  startTimeField: string | number;
  endTimeField: string | number;
  childrenField: string | number;
  defaultExpandAll: boolean;
  maxTime: number;
  minTime: number;

  constructor(props: GanttProps) {
    this.data = props.data;
    this.labelField = props.labelField as LabelField;
    this.startTimeField = props.startTimeField;
    this.endTimeField = props.endTimeField;
    this.childrenField = props.childrenField;
    this.defaultExpandAll = props.defaultExpandAll;
    this.maxTime = 0;
    this.minTime = 0;
  }

  handleData(data = this.data): GanttDataOptions[] {
    return data.map((item) => {
      if (item.expand === undefined) {
        item.expand = this.defaultExpandAll;
      }
      item._startTime = this.getStartTime(item);
      item._endTime = this.getEndTime(item);
      // 计算整个数据中的最早时间
      if (this.minTime === 0) {
        this.minTime = item._startTime;
      } else if (this.minTime > item._startTime) {
        this.minTime = item._startTime;
      }

      // 计算数据重的最晚时间
      if (this.maxTime < item._endTime) {
        this.maxTime = item._endTime;
      }
      if (item.children) {
        item.children = this.handleData(item.children);
      }

      return item;
    });
  }

  getStartTime(data: GanttDataOptions): number {
    return this.parseTime(data[this.startTimeField] as string | number);
  }

  getEndTime(data: GanttDataOptions) {
    return this.parseTime(data[this.endTimeField] as string | number);
  }

  getLabel(data: GanttDataOptions) {
    if (typeof this.labelField === 'function') {
      return this.labelField(data);
    }
    return data[this.labelField];
  }

  getChildren(data: GanttDataOptions) {
    return data[this.childrenField];
  }

  private parseTime(time: string | number): number {
    if (typeof time === 'string') {
      return new Date(time).getTime();
    }
    return time;
  }

  static getTime(time: string | number): number {
    if (typeof time === 'string') {
      return new Date(time).getTime();
    }
    return time;
  }
}
