define(["exports","./Transforms-89c8bdbf","./Matrix2-860854d4","./RuntimeError-1349fdaf","./when-4bbc8319","./EllipsoidTangentPlane-fa423dde","./ComponentDatatype-8f55628c","./Plane-0e4269ac"],(function(a,t,e,n,r,i,s,o){"use strict";function c(a,t){this.center=e.Cartesian3.clone(r.defaultValue(a,e.Cartesian3.ZERO)),this.halfAxes=e.Matrix3.clone(r.defaultValue(t,e.Matrix3.ZERO))}c.packedLength=e.Cartesian3.packedLength+e.Matrix3.packedLength,c.pack=function(a,t,n){return n=r.defaultValue(n,0),e.Cartesian3.pack(a.center,t,n),e.Matrix3.pack(a.halfAxes,t,n+e.Cartesian3.packedLength),t},c.unpack=function(a,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new c),e.Cartesian3.unpack(a,t,n.center),e.Matrix3.unpack(a,t+e.Cartesian3.packedLength,n.halfAxes),n};const C=new e.Cartesian3,l=new e.Cartesian3,u=new e.Cartesian3,d=new e.Cartesian3,h=new e.Cartesian3,x=new e.Cartesian3,m=new e.Matrix3,M={unitary:new e.Matrix3,diagonal:new e.Matrix3};c.fromPoints=function(a,t){if(r.defined(t)||(t=new c),!r.defined(a)||0===a.length)return t.halfAxes=e.Matrix3.ZERO,t.center=e.Cartesian3.ZERO,t;let n;const i=a.length,s=e.Cartesian3.clone(a[0],C);for(n=1;n<i;n++)e.Cartesian3.add(s,a[n],s);const o=1/i;e.Cartesian3.multiplyByScalar(s,o,s);let f,p=0,g=0,w=0,y=0,N=0,b=0;for(n=0;n<i;n++)f=e.Cartesian3.subtract(a[n],s,l),p+=f.x*f.x,g+=f.x*f.y,w+=f.x*f.z,y+=f.y*f.y,N+=f.y*f.z,b+=f.z*f.z;p*=o,g*=o,w*=o,y*=o,N*=o,b*=o;const O=m;O[0]=p,O[1]=g,O[2]=w,O[3]=g,O[4]=y,O[5]=N,O[6]=w,O[7]=N,O[8]=b;const P=e.Matrix3.computeEigenDecomposition(O,M),T=e.Matrix3.clone(P.unitary,t.halfAxes);let A=e.Matrix3.getColumn(T,0,d),I=e.Matrix3.getColumn(T,1,h),R=e.Matrix3.getColumn(T,2,x),E=-Number.MAX_VALUE,S=-Number.MAX_VALUE,U=-Number.MAX_VALUE,L=Number.MAX_VALUE,z=Number.MAX_VALUE,B=Number.MAX_VALUE;for(n=0;n<i;n++)f=a[n],E=Math.max(e.Cartesian3.dot(A,f),E),S=Math.max(e.Cartesian3.dot(I,f),S),U=Math.max(e.Cartesian3.dot(R,f),U),L=Math.min(e.Cartesian3.dot(A,f),L),z=Math.min(e.Cartesian3.dot(I,f),z),B=Math.min(e.Cartesian3.dot(R,f),B);A=e.Cartesian3.multiplyByScalar(A,.5*(L+E),A),I=e.Cartesian3.multiplyByScalar(I,.5*(z+S),I),R=e.Cartesian3.multiplyByScalar(R,.5*(B+U),R);const V=e.Cartesian3.add(A,I,t.center);e.Cartesian3.add(V,R,V);const _=u;return _.x=E-L,_.y=S-z,_.z=U-B,e.Cartesian3.multiplyByScalar(_,.5,_),e.Matrix3.multiplyByScale(t.halfAxes,_,t.halfAxes),t};const f=new e.Cartesian3,p=new e.Cartesian3;function g(a,t,n,i,s,o,C,l,u,d,h){r.defined(h)||(h=new c);const x=h.halfAxes;e.Matrix3.setColumn(x,0,t,x),e.Matrix3.setColumn(x,1,n,x),e.Matrix3.setColumn(x,2,i,x);let m=f;m.x=(s+o)/2,m.y=(C+l)/2,m.z=(u+d)/2;const M=p;M.x=(o-s)/2,M.y=(l-C)/2,M.z=(d-u)/2;const g=h.center;return m=e.Matrix3.multiplyByVector(x,m,m),e.Cartesian3.add(a,m,g),e.Matrix3.multiplyByScale(x,M,x),h}const w=new e.Cartographic,y=new e.Cartesian3,N=new e.Cartographic,b=new e.Cartographic,O=new e.Cartographic,P=new e.Cartographic,T=new e.Cartographic,A=new e.Cartesian3,I=new e.Cartesian3,R=new e.Cartesian3,E=new e.Cartesian3,S=new e.Cartesian3,U=new e.Cartesian2,L=new e.Cartesian2,z=new e.Cartesian2,B=new e.Cartesian2,V=new e.Cartesian2,_=new e.Cartesian3,k=new e.Cartesian3,W=new e.Cartesian3,D=new e.Cartesian3,X=new e.Cartesian2,q=new e.Cartesian3,j=new e.Cartesian3,Z=new e.Cartesian3,v=new o.Plane(e.Cartesian3.UNIT_X,0);c.fromRectangle=function(a,t,n,c,C){let l,u,d,h,x,m,M;if(t=r.defaultValue(t,0),n=r.defaultValue(n,0),c=r.defaultValue(c,e.Ellipsoid.WGS84),a.width<=s.CesiumMath.PI){const r=e.Rectangle.center(a,w),s=c.cartographicToCartesian(r,y),f=new i.EllipsoidTangentPlane(s,c);M=f.plane;const p=r.longitude,_=a.south<0&&a.north>0?0:r.latitude,k=e.Cartographic.fromRadians(p,a.north,n,N),W=e.Cartographic.fromRadians(a.west,a.north,n,b),D=e.Cartographic.fromRadians(a.west,_,n,O),X=e.Cartographic.fromRadians(a.west,a.south,n,P),q=e.Cartographic.fromRadians(p,a.south,n,T),j=c.cartographicToCartesian(k,A);let Z=c.cartographicToCartesian(W,I);const v=c.cartographicToCartesian(D,R);let Y=c.cartographicToCartesian(X,E);const G=c.cartographicToCartesian(q,S),F=f.projectPointToNearestOnPlane(j,U),H=f.projectPointToNearestOnPlane(Z,L),J=f.projectPointToNearestOnPlane(v,z),K=f.projectPointToNearestOnPlane(Y,B),Q=f.projectPointToNearestOnPlane(G,V);return l=Math.min(H.x,J.x,K.x),u=-l,h=Math.max(H.y,F.y),d=Math.min(K.y,Q.y),W.height=X.height=t,Z=c.cartographicToCartesian(W,I),Y=c.cartographicToCartesian(X,E),x=Math.min(o.Plane.getPointDistance(M,Z),o.Plane.getPointDistance(M,Y)),m=n,g(f.origin,f.xAxis,f.yAxis,f.zAxis,l,u,d,h,x,m,C)}const f=a.south>0,p=a.north<0,Y=f?a.south:p?a.north:0,G=e.Rectangle.center(a,w).longitude,F=e.Cartesian3.fromRadians(G,Y,n,c,_);F.z=0;const H=Math.abs(F.x)<s.CesiumMath.EPSILON10&&Math.abs(F.y)<s.CesiumMath.EPSILON10?e.Cartesian3.UNIT_X:e.Cartesian3.normalize(F,k),J=e.Cartesian3.UNIT_Z,K=e.Cartesian3.cross(H,J,W);M=o.Plane.fromPointNormal(F,H,v);const Q=e.Cartesian3.fromRadians(G+s.CesiumMath.PI_OVER_TWO,Y,n,c,D);u=e.Cartesian3.dot(o.Plane.projectPointOntoPlane(M,Q,X),K),l=-u,h=e.Cartesian3.fromRadians(0,a.north,p?t:n,c,q).z,d=e.Cartesian3.fromRadians(0,a.south,f?t:n,c,j).z;const $=e.Cartesian3.fromRadians(a.east,Y,n,c,Z);return x=o.Plane.getPointDistance(M,$),m=0,g(F,K,J,H,l,u,d,h,x,m,C)},c.clone=function(a,t){if(r.defined(a))return r.defined(t)?(e.Cartesian3.clone(a.center,t.center),e.Matrix3.clone(a.halfAxes,t.halfAxes),t):new c(a.center,a.halfAxes)},c.intersectPlane=function(a,n){const r=a.center,i=n.normal,s=a.halfAxes,o=i.x,c=i.y,C=i.z,l=Math.abs(o*s[e.Matrix3.COLUMN0ROW0]+c*s[e.Matrix3.COLUMN0ROW1]+C*s[e.Matrix3.COLUMN0ROW2])+Math.abs(o*s[e.Matrix3.COLUMN1ROW0]+c*s[e.Matrix3.COLUMN1ROW1]+C*s[e.Matrix3.COLUMN1ROW2])+Math.abs(o*s[e.Matrix3.COLUMN2ROW0]+c*s[e.Matrix3.COLUMN2ROW1]+C*s[e.Matrix3.COLUMN2ROW2]),u=e.Cartesian3.dot(i,r)+n.distance;return u<=-l?t.Intersect.OUTSIDE:u>=l?t.Intersect.INSIDE:t.Intersect.INTERSECTING};const Y=new e.Cartesian3,G=new e.Cartesian3,F=new e.Cartesian3,H=new e.Cartesian3,J=new e.Cartesian3,K=new e.Cartesian3;c.distanceSquaredTo=function(a,t){const n=e.Cartesian3.subtract(t,a.center,f),r=a.halfAxes;let i=e.Matrix3.getColumn(r,0,Y),o=e.Matrix3.getColumn(r,1,G),c=e.Matrix3.getColumn(r,2,F);const C=e.Cartesian3.magnitude(i),l=e.Cartesian3.magnitude(o),u=e.Cartesian3.magnitude(c);let d=!0,h=!0,x=!0;C>0?e.Cartesian3.divideByScalar(i,C,i):d=!1,l>0?e.Cartesian3.divideByScalar(o,l,o):h=!1,u>0?e.Cartesian3.divideByScalar(c,u,c):x=!1;const m=!d+!h+!x;let M,p,g;if(1===m){let a=i;M=o,p=c,h?x||(a=c,p=i):(a=o,M=i),g=e.Cartesian3.cross(M,p,J),a===i?i=g:a===o?o=g:a===c&&(c=g)}else if(2===m){M=i,h?M=o:x&&(M=c);let a=e.Cartesian3.UNIT_Y;a.equalsEpsilon(M,s.CesiumMath.EPSILON3)&&(a=e.Cartesian3.UNIT_X),p=e.Cartesian3.cross(M,a,H),e.Cartesian3.normalize(p,p),g=e.Cartesian3.cross(M,p,J),e.Cartesian3.normalize(g,g),M===i?(o=p,c=g):M===o?(c=p,i=g):M===c&&(i=p,o=g)}else 3===m&&(i=e.Cartesian3.UNIT_X,o=e.Cartesian3.UNIT_Y,c=e.Cartesian3.UNIT_Z);const w=K;w.x=e.Cartesian3.dot(n,i),w.y=e.Cartesian3.dot(n,o),w.z=e.Cartesian3.dot(n,c);let y,N=0;return w.x<-C?(y=w.x+C,N+=y*y):w.x>C&&(y=w.x-C,N+=y*y),w.y<-l?(y=w.y+l,N+=y*y):w.y>l&&(y=w.y-l,N+=y*y),w.z<-u?(y=w.z+u,N+=y*y):w.z>u&&(y=w.z-u,N+=y*y),N};const Q=new e.Cartesian3,$=new e.Cartesian3;c.computePlaneDistances=function(a,n,i,s){r.defined(s)||(s=new t.Interval);let o=Number.POSITIVE_INFINITY,c=Number.NEGATIVE_INFINITY;const C=a.center,l=a.halfAxes,u=e.Matrix3.getColumn(l,0,Y),d=e.Matrix3.getColumn(l,1,G),h=e.Matrix3.getColumn(l,2,F),x=e.Cartesian3.add(u,d,Q);e.Cartesian3.add(x,h,x),e.Cartesian3.add(x,C,x);const m=e.Cartesian3.subtract(x,n,$);let M=e.Cartesian3.dot(i,m);return o=Math.min(M,o),c=Math.max(M,c),e.Cartesian3.add(C,u,x),e.Cartesian3.add(x,d,x),e.Cartesian3.subtract(x,h,x),e.Cartesian3.subtract(x,n,m),M=e.Cartesian3.dot(i,m),o=Math.min(M,o),c=Math.max(M,c),e.Cartesian3.add(C,u,x),e.Cartesian3.subtract(x,d,x),e.Cartesian3.add(x,h,x),e.Cartesian3.subtract(x,n,m),M=e.Cartesian3.dot(i,m),o=Math.min(M,o),c=Math.max(M,c),e.Cartesian3.add(C,u,x),e.Cartesian3.subtract(x,d,x),e.Cartesian3.subtract(x,h,x),e.Cartesian3.subtract(x,n,m),M=e.Cartesian3.dot(i,m),o=Math.min(M,o),c=Math.max(M,c),e.Cartesian3.subtract(C,u,x),e.Cartesian3.add(x,d,x),e.Cartesian3.add(x,h,x),e.Cartesian3.subtract(x,n,m),M=e.Cartesian3.dot(i,m),o=Math.min(M,o),c=Math.max(M,c),e.Cartesian3.subtract(C,u,x),e.Cartesian3.add(x,d,x),e.Cartesian3.subtract(x,h,x),e.Cartesian3.subtract(x,n,m),M=e.Cartesian3.dot(i,m),o=Math.min(M,o),c=Math.max(M,c),e.Cartesian3.subtract(C,u,x),e.Cartesian3.subtract(x,d,x),e.Cartesian3.add(x,h,x),e.Cartesian3.subtract(x,n,m),M=e.Cartesian3.dot(i,m),o=Math.min(M,o),c=Math.max(M,c),e.Cartesian3.subtract(C,u,x),e.Cartesian3.subtract(x,d,x),e.Cartesian3.subtract(x,h,x),e.Cartesian3.subtract(x,n,m),M=e.Cartesian3.dot(i,m),o=Math.min(M,o),c=Math.max(M,c),s.start=o,s.stop=c,s};const aa=new t.BoundingSphere;c.isOccluded=function(a,e){const n=t.BoundingSphere.fromOrientedBoundingBox(a,aa);return!e.isBoundingSphereVisible(n)},c.prototype.intersectPlane=function(a){return c.intersectPlane(this,a)},c.prototype.distanceSquaredTo=function(a){return c.distanceSquaredTo(this,a)},c.prototype.computePlaneDistances=function(a,t,e){return c.computePlaneDistances(this,a,t,e)},c.prototype.isOccluded=function(a){return c.isOccluded(this,a)},c.equals=function(a,t){return a===t||r.defined(a)&&r.defined(t)&&e.Cartesian3.equals(a.center,t.center)&&e.Matrix3.equals(a.halfAxes,t.halfAxes)},c.prototype.clone=function(a){return c.clone(this,a)},c.prototype.equals=function(a){return c.equals(this,a)},a.OrientedBoundingBox=c}));
