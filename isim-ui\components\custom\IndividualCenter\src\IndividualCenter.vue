<!--
 * @Author: css
 * @Date: 2023-04-12 11:32:00
 * @Version: 1.0
 * @Content: IndividualCenter
-->
<template>
  <div class="sim-center" draggable="true" @mousedown="handleMouseDown" @mouseup="handleMouseUp">
    <div class="sim-center-inner" @mouseleave="activeClick = false">
      <!-- <div class="sim-center-inner"> -->
      <div v-show="activeClick" class="center-border"></div>
      <div class="sim-center-inner__subject" @mouseover="activeClick = true">
        <!-- <div class="sim-center__subject"> -->
        <div class="sim-center-inner__subject-content" @click="handleClick('个人中心')">
          <!-- <y-icon class="y-icon-yonghu1" /> -->
          <div class="center-icon">A</div>
          <div class="center-text">个人中心</div>
        </div>
        <div
          v-for="(item, index) in state.centerList"
          v-show="activeClick"
          :key="index"
          class="center-children"
          :class="[`circle${index + 1}`, { active: state.childClick?.title === item.title, hover: state.childHover?.title === item.title }]"
          @mouseover="centerChildHover(item)"
          @mouseleave="state.childHover = null"
          @click="centerChildrenClick(item)"
        >
          <div v-show="state.childHover?.title === item.title" class="open" :class="`openposition${index + 1}`"></div>
          <div
            class="center-children__content"
            :class="{ active: state.childClick?.title === item.title, hover: state.childHover?.title === item.title }"
          >
            <div class="children-text">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'SimIndividualCenter'
};
</script>
<script setup lang="ts">
import { ref } from 'vue';

const emits = defineEmits(['eleClick']);
const activeClick = ref(false);
interface StateCommonType {
  title: string;
}

interface StateType {
  centerList: StateCommonType[];
  childHover: StateCommonType | null;
  childClick: StateCommonType | null;
  disx: number;
  disy: number;
  box: Record<string, any>;
}
const state: StateType = reactive({
  centerList: [
    {
      title: '帮助中心'
    },
    {
      title: '消息列表'
    },
    {
      title: '收藏列表'
    },
    {
      title: '申请列表'
    },
    {
      title: '我的资源'
    }
  ],
  childHover: {
    title: ''
  },
  childClick: {
    title: ''
  },
  disx: 0,
  disy: 0,
  box: {}
});
// 个人中心点击事件
const handleClick = (str: string) => {
  emits('eleClick', str);
};
// 子级hover触发时间
const centerChildHover = (item: StateCommonType) => {
  state.childHover = item;
};
// 子级click触发时间
const centerChildrenClick = (item: { title: string }) => {
  state.childClick = item;
  emits('eleClick', item.title);
};
// 释放鼠标按钮
const handleMouseUp = () => {
  document.onmousedown = null;
  document.onmousemove = null;
  document.onmouseup = null;
  document.onmouseover = document.onmouseup = null;
};
// 拖拽
const handleMouseDown = (e: { pageX: number; pageY: number }) => {
  const box: any = document.getElementsByClassName('sim-center')[0];
  state.disx = e.pageX - box.offsetLeft;
  state.disy = e.pageY - box.offsetTop;

  document.onmousemove = (e) => {
    let x, y;
    if (e.pageX - state.disx > 0) {
      if (e.pageX - state.disx > document.documentElement.clientWidth - 60) {
        x = document.documentElement.clientWidth - 60;
      } else {
        x = e.pageX - state.disx;
      }
    } else {
      x = 0;
    }

    if (e.pageY - state.disy > 0) {
      if (e.pageY - state.disy > document.documentElement.clientHeight - 60) {
        y = document.documentElement.clientHeight - 60;
      } else {
        y = e.pageY - state.disy;
      }
    } else {
      y = 0;
    }

    box.style.left = x + 'px';
    box.style.top = y + 'px';
  };
  document.onmouseup = () => {
    document.onmousedown = null;
    document.onmousemove = null;
    document.onmouseup = null;
  };
  document.ondragstart = (ev) => {
    ev.preventDefault();
  };
  document.ondragend = (ev) => {
    ev.preventDefault();
  };
};
onMounted(() => {
  document.addEventListener('mouseleave', () => {
    document.onmouseover = document.onmouseup = null;
  });
});
</script>

<style scoped lang="less">
@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
  }
  25% {
    -webkit-transform: rotate(90deg);
  }
  50% {
    -webkit-transform: rotate(180deg);
  }
  75% {
    -webkit-transform: rotate(270deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
.sim-center {
  position: fixed;
  width: 188px;
  height: 188px;
  cursor: pointer;
  //   background: #000;
  &-inner {
    position: relative;
    width: 188px;
    height: 188px;
    .center-border {
      position: absolute;
      width: 106px;
      height: 106px;
      border-radius: 50%;
      bottom: 0;
      right: 0;
      background: linear-gradient(180deg, rgba(0, 154, 168, 0.5) 0%, rgba(0, 82, 89, 0.5) 60%, rgba(0, 181, 197, 0.5) 96%);
      border: 1px solid rgba(var(--primary-color-val), 0.3);
      box-shadow: inset 0px 0px 8px var(--primary-color);
      // transform: rotate(90deg);
      animation: rotate 4s linear infinite;
    }
    &__subject {
      position: absolute;
      bottom: 10px;
      right: 10px;
      width: 76px;
      height: 76px;
      border-radius: 50%;
      border: 6px solid var(--text-color);
      &-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        width: 76px;
        height: 76px;
        border-radius: 50%;
        background: linear-gradient(180deg, rgba(0, 154, 168, 0.5) 0%, rgba(0, 82, 89, 0.5) 60%, rgba(0, 181, 197, 0.5) 100%);
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        backdrop-filter: blur(10px);
        border: 1px solid #000;
        .center-icon {
          display: inline-block;
          margin-top: 14px;
        }
        .center-text {
          font-size: 12px;
          font-weight: 700;
        }
      }
    }
    .center-children {
      position: relative;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 3px solid var(--text-color);
      :hover {
        background: linear-gradient(180deg, rgba(var(--primary-color-val), 1) 0%, rgba(0, 82, 89, 1) 60%, rgba(var(--primary-color-val), 1) 98%);
        border-radius: 50%;
      }

      &__content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: radial-gradient(rgba(0, 154, 168, 0.4) 0%, rgba(0, 82, 89, 0.4) 60%, rgba(0, 181, 197, 0.4) 96%);
        // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        backdrop-filter: blur(10px);
        border: 1px solid #000;
        &.active,
        &.hover {
          background: linear-gradient(180deg, var(--primary-color) 0%, rgba(0, 82, 89, 1) 60%, var(--primary-color) 98%);
          border-radius: 50%;
        }
        .children-text {
          margin-top: 4px;
          color: var(--text-color);
          font-size: 14px;
          font-weight: 700;
          line-height: 15px;
          letter-spacing: 0px;
          text-align: center;
        }
      }
    }
    .circle1 {
      position: absolute;
      top: 0;
      right: 26px;
      transform: rotateZ(200deg) translateY(90px) rotate(158deg);
    }
    .circle2 {
      position: absolute;
      top: 0;
      right: 26px;
      transform: rotateZ(164deg) translateY(80px) rotate(200deg);
    }
    .circle3 {
      position: absolute;
      top: 0;
      right: 26px;
      transform: rotateZ(124deg) translateY(84px) rotate(240deg);
    }
    .circle4 {
      position: absolute;
      top: 0;
      right: 26px;
      transform: rotateZ(88deg) translateY(96px) rotate(272deg);
    }
    .circle5 {
      position: absolute;
      top: 0;
      right: 26px;
      transform: rotateZ(60deg) translateY(115px) rotate(298deg);
    }
    .open {
      background: url('./assests/center-open.png');
      background-repeat: no-repeat;
      background-size: 18px 16px;
      width: 18px;
      height: 16px;
    }
    .openposition1 {
      position: absolute;
      top: 48px;
      right: 21px;
      transform: rotate(58deg);
    }
    .openposition2 {
      position: absolute;
      top: 45px;
      right: 0px;
      transform: rotate(20deg);
    }
    .openposition3 {
      position: absolute;
      top: 35px;
      right: -20px;
      transform: rotate(-10deg);
    }
    .openposition4 {
      position: absolute;
      top: 18px;
      right: -32px;
      transform: rotate(-42deg);
    }
    .openposition5 {
      position: absolute;
      top: -3px;
      right: -42px;
      transform: rotate(-66deg);
    }
  }
}
</style>
