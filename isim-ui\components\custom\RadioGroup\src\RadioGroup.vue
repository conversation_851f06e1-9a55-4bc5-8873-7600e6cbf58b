<!--
* @Author: Geng<PERSON><PERSON><PERSON>
* @Date: 2023/4/11
* @Version: 1.0
* @Content: RadioGroup
-->
<template>
  <div :class="['sim-radio', { 'sim-radio__large': size === 'large' }]">
    <div
      v-for="item in props.data"
      :key="item.key"
      :class="['sim-radio-item', { 'sim-radio-item--active': modelValue === item.key }]"
      @click="handleChange(item)"
    >
      <i :class="item.icon"></i>
      <span>{{ item.label }}</span>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'SimRadioGroup'
};
</script>
<script setup lang="ts">
import { PropType } from 'vue';

interface PropData {
  icon?: string; // 图标
  label?: string; // label
  key: string;
}
const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  size: {
    type: String as PropType<'default' | 'large'>,
    default: 'default'
  },
  data: {
    type: Array<PropData>,
    default: () => [
      {
        icon: 'sim-icon-fenlan',
        key: 'tree' // 平铺
      },
      {
        icon: 'sim-icon-kapian',
        key: 'card' // 级联
      },
      {
        icon: 'sim-icon-liebiao',
        key: 'list' // 表格列表
      }
    ]
  }
});
const emit = defineEmits(['update:modelValue', 'change']);

const handleChange = (item: PropData) => {
  if (item.key === props.modelValue) {
    return;
  } // 值改变才触发
  emit('update:modelValue', item.key);
  emit('change', item.key);
};
</script>

<style scoped lang="less">
.sim-radio {
  width: fit-content;
  display: flex;
  align-items: center;
  border: 1px solid rgba(var(--primary-color-val), 0.5);
  background-color: rgba(var(--primary-color-val), 0.15);
  border-radius: 8px;
  padding: 2px;
  height: 32px;
  box-sizing: border-box;
  .sim-radio-item {
    min-width: 40px;
    padding: 0 8px;
    height: 28px;
    font-size: 16px;
    line-height: 28px;
    text-align: center;
    color: var(--text-color);
    box-sizing: border-box;
    cursor: pointer;
    border-radius: 6px;
    border: 1px solid transparent;
    &--active {
      background: linear-gradient(
        180deg,
        rgba(var(--btn-top-color-val), 1) 0%,
        rgba(var(--btn-center-color-val), 1) 60%,
        rgba(var(--btn-bottom-color-val), 1) 100%
      );
      border: 1px solid rgba(var(--primary-color-val), 1);
      //line-height: 28px;
    }
  }
}
.sim-radio__large {
  width: 100%;
  .sim-radio-item {
    flex: 1;
  }
}
</style>
