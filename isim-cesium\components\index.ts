import type { App } from 'vue';
import { SimViewer } from './ISimViewer';
import { SimNavigation } from './Navigation';
import { SimBreastplate } from './breastplate';
import { SimRightMenu2 } from './right-menu';
import { SimScale, SimScaleSelect } from './scale';
export const SimCesiumComponent = {
  install(Vue: App) {
    Vue.use(SimNavigation);
    Vue.use(SimViewer);
    Vue.use(SimBreastplate);
    Vue.use(SimRightMenu2);
    Vue.use(SimScale);
    Vue.use(SimScaleSelect);
  }
};

export { SimViewer, SimScale, SimRightMenu2 };

export { useRightMenuOptionStore } from './right-menu/v1/use-right-menu-option';

export { useRightMenu } from './right-menu/v2/use-right-menu';
