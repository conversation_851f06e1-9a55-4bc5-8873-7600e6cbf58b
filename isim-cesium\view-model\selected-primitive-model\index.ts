/**
 * @Author: 宋计民
 * @Date: 2023/10/16 16:27
 * @Version: 1.0
 * @Content: 选中Primitive
 */
import { Scene, SelectionIndicator, Viewer } from 'cesium';

export class SelectedPrimitiveModel {
  private scene: Scene;
  private indicator: SelectionIndicator;
  private container: HTMLDivElement;
  constructor(
    private viewer: Viewer,
    private primitive?: any
  ) {
    this.scene = this.viewer.scene;
    const { indicate, container } = this.createIndicator();
    indicate.viewModel.animateAppear();
    indicate.viewModel.showSelection = true;
    this.indicator = indicate;
    this.container = container;
    this.bindUpdate();
  }
  setPrimitive(primitive?: any) {
    if (primitive) {
      this.primitive = primitive;
      this.indicator.viewModel.showSelection = true;
      return;
    }
    this.indicator.viewModel.showSelection = false;
  }
  preUpdate = () => {
    const indicator = this.indicator;
    if (!this.primitive) {
      indicator.viewModel.showSelection = false;
      return;
    }
    //@ts-ignore
    this.indicator.viewModel.position = this.primitive?.position;
    this.indicator.viewModel.update();
  };
  bindUpdate() {
    this.scene.preUpdate.addEventListener(this.preUpdate);
  }
  removeUpdate() {
    this.scene.preUpdate.removeEventListener(this.preUpdate);
  }
  private createIndicator() {
    const viewerContainer = this.viewer.container;
    const selectionIndicatorContainer = document.createElement('div');
    selectionIndicatorContainer.className = 'cesium-viewer-selectionIndicatorContainer';
    viewerContainer.appendChild(selectionIndicatorContainer);
    return {
      indicate: new SelectionIndicator(selectionIndicatorContainer, this.viewer.scene),
      container: selectionIndicatorContainer
    };
  }
  destroy() {
    this.removeUpdate();
    this.indicator.destroy();
    this.container.remove();
  }
}
