<!--
* @Author: 宋计民
* @Date: 2024/5/14
* @Version: 1.0
* @Content: gantt-lef-tree.vue
-->
<template>
  <div class="gantt-lef-tree" ref="ganttTreeRef">
    <div
      class="gantt-lef-tree-item"
      v-for="item in showList"
      :key="item.id"
      :style="{
        top: `${item.top}px`,
        paddingLeft: `calc(${item.level! - 1}em)`,
        height: rowHeight + 'px',
        backgroundColor: selectedRowIdList.includes(item.id) ? '#79b4f1' : ''
      }"
      @click="rowClick(item)"
      @mouseover="handleMouseover($event, item)"
      @mouseout="handleMouseout"
    >
      <div class="pre">
        <sim-icon
          v-if="item?.children?.length"
          name="right-fill"
          color="#fff"
          :class="{ 'pre-icon': !excludeList.has(item.id) }"
          @click.stop="changeItemState(item)"
        />
      </div>
      <span>{{ rowTextFormat(item) }}</span>
    </div>
  </div>
  <el-tooltip placement="top" ref="tooltipRef" :visible="visible" :virtual-ref="targetRef" virtual-triggering>
    <template #content>
      <span> {{ rowTextFormat(currentTask) }}</span>
    </template>
  </el-tooltip>
</template>

<script setup lang="ts" generic="T extends GanttDataBase">
import { PropType } from 'vue';
import { GanttDataBase, GanttDataType } from './graphic';
import { useTooltip } from './hooks/tooltip-hook.ts';

defineOptions({
  name: 'GanttLefTree'
});

const props = defineProps({
  height: {
    type: Number,
    default: 100
  },
  gap: {
    type: Number,
    default: 10
  },
  rowTextFormat: {
    type: Function,
    default: (data: GanttDataType) => data.label
  },
  showList: {
    type: Array as PropType<GanttDataType<T>[]>,
    default: () => []
  }
});
// 收起的子节点列表
const excludeList = defineModel<Set<string>>('excludeList', {
  default: () => new Set<string>()
});
// 选中节点列表
const selectedRowList = defineModel<GanttDataType<T>[]>('selectedRowList', {
  default: () => []
});
const selectedRowIdList = computed(() => {
  return selectedRowList.value.map((v) => v.id);
});
// tooltip
const { targetRef, tooltipRef, visible, currentTask, handleMouseover, handleMouseout } = useTooltip<GanttDataType<T>>();

const ganttTreeRef = ref();
const rowHeight = computed(() => props.height + props.gap);
// 收放子节点事件
const changeItemState = (item: GanttDataType<T>) => {
  excludeList.value.has(item.id) ? excludeList.value.delete(item.id) : excludeList.value.add(item.id);
};
// 点击行
const rowClick = (item: GanttDataType<T>) => {
  selectedRowList.value = [item];
};
</script>

<style scoped lang="less">
.gantt-lef-tree {
  width: 100%;
  height: 100%;
  //border: 1px solid red;
  position: relative;
  box-sizing: border-box;
  color: #fff;
  overflow: hidden;

  .gantt-lef-tree-item {
    box-sizing: border-box;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    border-bottom: 1px solid #ccc;
    display: flex;
    align-items: center;

    -webkit-user-select: none; /* Safari 3.1+ */
    -moz-user-select: none; /* Firefox 2+ */
    -ms-user-select: none; /* IE 10+ */
    user-select: none; /* 标准语法 */

    .pre {
      display: flex;
      align-items: center;
      justify-content: center;
      //padding: 2px;
      width: 18px;
      .pre-icon {
        transform: rotateZ(90deg);
      }
    }
    span {
      width: 0;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  //.gantt-lef-tree-item:hover {
  //  background-color: #79b4f1;
  //}
}
</style>
