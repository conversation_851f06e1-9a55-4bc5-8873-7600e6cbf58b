<?xml version='1.0' encoding='utf-8'?>
<objectModel 
  xmlns="http://standards.ieee.org/IEEE1516-2010" 
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://standards.ieee.org/IEEE1516-2010 http://standards.ieee.org/downloads/1516/1516.2-2010/IEEE1516-DIF-2010.xsd">
  <modelIdentification>
    <name>morpheus_core</name>
    <type>FOM</type>
    <version>1.0</version>
    <securityClassification>Unclassified</securityClassification>
    <applicationDomain>Training</applicationDomain>
    <description>An empty project generated by the FOM Editor.</description>
  </modelIdentification>
  <objects>
    <objectClass>
      <name>HLAobjectRoot</name>
      <sharing>Neither</sharing>
      <attribute>
        <name>HLAprivilegeToDeleteObject</name>
        <dataType>HLAtoken</dataType>
        <updateType>Static</updateType>
        <updateCondition>NA</updateCondition>
        <ownership>DivestAcquire</ownership>
        <sharing>PublishSubscribe</sharing>
        <transportation>HLAreliable</transportation>
        <order>TimeStamp</order>
        <semantics>NA</semantics>
      </attribute>
      <attribute>
        <name>UniqueId</name>
        <dataType>MorpheusString</dataType>
        <updateType>Conditional</updateType>
        <updateCondition>On change</updateCondition>
        <ownership>DivestAcquire</ownership>
        <sharing>PublishSubscribe</sharing>
        <transportation>HLAbestEffort</transportation>
        <order>Receive</order>
        <semantics>唯一标识。
					Required. Unique identifer for all object classes.</semantics>
      </attribute>
      <attribute>
        <name>RTIobjectId</name>
        <dataType>RTIobjectId</dataType>
        <updateType>Conditional</updateType>
        <updateCondition>On change</updateCondition>
        <ownership>DivestAcquire</ownership>
        <sharing>PublishSubscribe</sharing>
        <transportation>HLAbestEffort</transportation>
        <order>Receive</order>
        <semantics>RTI 对象实例标识字符串。
					An RTI object instance identification string.</semantics>
      </attribute>
      <attribute>
        <name>FrameLength</name>
        <dataType>HLAinteger32BE</dataType>
        <updateType>Static</updateType>
        <updateCondition>NA</updateCondition>
        <ownership>DivestAcquire</ownership>
        <sharing>PublishSubscribe</sharing>
        <transportation>HLAreliable</transportation>
        <order>TimeStamp</order>
        <semantics>仿真步长，单位毫秒</semantics>
      </attribute>
      <attribute>
        <name>CreateTime</name>
        <dataType>HLAfloat64BE</dataType>
        <updateType>NA</updateType>
        <updateCondition>On Change</updateCondition>
        <ownership>Divest</ownership>
        <sharing>PublishSubscribe</sharing>
        <transportation>HLAbestEffort</transportation>
        <order>Receive</order>
        <semantics>实体创建的仿真时间（相对时间），单位：秒</semantics>
      </attribute>
      <attribute>
        <name>LastUpdateTime</name>
        <dataType>HLAfloat64BE</dataType>
        <updateType>NA</updateType>
        <updateCondition>On Change</updateCondition>
        <ownership>Divest</ownership>
        <sharing>PublishSubscribe</sharing>
        <transportation>HLAbestEffort</transportation>
        <order>Receive</order>
        <semantics>上次更新时间</semantics>
      </attribute>
      <objectClass>
        <name>HLAmanager</name>
        <sharing>Neither</sharing>
        <semantics>This object class is the root class of all MOM object classes</semantics>
        <objectClass>
          <name>HLAfederate</name>
          <sharing>Publish</sharing>
          <semantics>This object class shall contain RTI state variables relating to a joined federate. The RTI
                  shall publish it and shall register one object instance for each joined federate in a federation.
                  Dynamic attributes that shall be contained in an object instance shall be updated periodically, where
                  the period should be determined by an interaction of the class
                  HLAmanager.HLAfederate.HLAadjust.HLAsetTiming. If this value is never set or is set to zero, no
                  periodic update shall be performed by the RTI.

                  The RTI shall respond to the invocation, by any federate, of the Request Attribute Value Update
                  service for this object class or for any instance attribute of an object instance of this class by
                  supplying values via the normal instance attribute update mechanism, regardless of whether the
                  attribute has a data type of static, periodic, or conditional. In addition to its responsibility to
                  update attributes of object instances of this class when those updates are explicitly requested, the
                  RTI shall automatically update instance attributes of object instances of this class according to the
                  update policy of the attribute, which is determined by the update type of the class attribute in Table
                  6. For those attributes that have an update type of Periodic, the update wall-clock time interval
                  shall be determined by the HLAreportPeriod parameter in an interaction of classHLAmanager.HLAfederate.
                  HLAadjust.HLAsetTiming. If this value is never set or is set to zero, no periodic updates shall be
                  performed by the RTI. Those attributes that have an update type of Conditional shall have update
                  conditions as defined in the Table 6.</semantics>
          <attribute>
            <name>HLAfederateHandle</name>
            <dataType>HLAhandle</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Handle of the joined federate returned by a Join Federation Execution service invocation</semantics>
          </attribute>
          <attribute>
            <name>HLAfederateName</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Name of the joined federate supplied to a successful Join Federation Execution service invocation</semantics>
          </attribute>
          <attribute>
            <name>HLAfederateType</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Type of the joined federate specified by the joined federate when it joined the federation</semantics>
          </attribute>
          <attribute>
            <name>HLAfederateHost</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Host name of the computer on which the joined federate is executing</semantics>
          </attribute>
          <attribute>
            <name>HLARTIversion</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Version of the RTI software being used</semantics>
          </attribute>
          <attribute>
            <name>HLAFOMmoduleDesignatorList</name>
            <dataType>HLAmoduleDesignatorList</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>FOM Module designators as specified by the federate when the Join Federation Execution
                     service was invoked. If several identical FOM modules are provided only the designator of the first
                     of these FOM modules shall be added to the list.</semantics>
          </attribute>
          <attribute>
            <name>HLAtimeConstrained</name>
            <dataType>HLAboolean</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever services Time Constrained Enabled or Disable Time Constrained are successfully invoked
                     (including via the HLAdisableTimeConstrained interaction).</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Whether the time advancement of the joined federate is constrained by other joined federates</semantics>
          </attribute>
          <attribute>
            <name>HLAtimeRegulating</name>
            <dataType>HLAboolean</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever services Time Regulation Enabled or Disable Time Regulation are successfully invoked
                     (including via the HLAdisableTimeRegulation interaction).</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Whether the joined federate influences the time advancement of other joined federates</semantics>
          </attribute>
          <attribute>
            <name>HLAasynchronousDelivery</name>
            <dataType>HLAboolean</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever services Enable Asynchronous Delivery or Disable Asynchronous Delivery are
                     successfully invoked (including via the HLAenableAsynchronousDelivery or
                     HLAdisableAsynchronousDelivery interactions).</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Whether the RTI shall deliver RO messages to the joined federate while the joined
                     federate&#x27;s time manager state is not &#x27;Time Advancing&#x27; (only matters if the joined federate is
                     time-constrained).</semantics>
          </attribute>
          <attribute>
            <name>HLAfederateState</name>
            <dataType>HLAfederateState</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever the services Initiate Federate Save, Federation Saved, Federation Restore
                     Begun, Confirm Federation Restoration Request (success), or Join Federation Execution are
                     successfully invoked. Also, after the Federation Restored service has been invoked at all federates
                     in the federation execution. If a joined federate is in the Federate Save in Progress state, no
                     corresponding reflects shall be invoked at that joined federate.</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>State of the joined federate. The MOM may, but is not required to, update any
                     HLAfederateState instance attribute values during the interval after the last federate in the
                     federation execution invokes the Federate Restore Complete service but before the last Federation
                     Restored † callback is invoked at some federate for a given federation restoration.</semantics>
          </attribute>
          <attribute>
            <name>HLAtimeManagerState</name>
            <dataType>HLAtimeState</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever services Time Advance Request, Time Advance Request Available, Next Message
                     Request, Next Message Request Available, Flush Queue Request, or Time Advance Grant are
                     successfully invoked (including via the HLAtimeAdvanceRequest, HLAtimeAdvanceRequestAvailable,
                     HLAnextMessageRequest, HLAnextMessageRequestAvailable, or HLAflushQueueRequest interactions).</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>State of the joined federate&#x27;s time manager</semantics>
          </attribute>
          <attribute>
            <name>HLAlogicalTime</name>
            <dataType>HLAlogicalTime</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Joined federate&#x27;s logical time. Initial value of this information is initial value of
                     federation time of the Time Representation Abstract Data Type (TRADT).</semantics>
          </attribute>
          <attribute>
            <name>HLAlookahead</name>
            <dataType>HLAtimeInterval</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Minimum duration into the future that a TSO message will be scheduled. The value shall not
                     be defined if the joined federate is not time-regulating)</semantics>
          </attribute>
          <attribute>
            <name>HLAGALT</name>
            <dataType>HLAlogicalTime</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Joined federate&#x27;s Greatest Available Logical Time (GALT). The value shall not be defined if
                     GALT is not defined for the joined federate.</semantics>
          </attribute>
          <attribute>
            <name>HLALITS</name>
            <dataType>HLAlogicalTime</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Joined federate&#x27;s Least Incoming Time Stamp (LITS). The value shall not be defined if LITS
                     is not defined for the joined federate.</semantics>
          </attribute>
          <attribute>
            <name>HLAROlength</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Number of RO messages queued for delivery to the joined federate.</semantics>
          </attribute>
          <attribute>
            <name>HLATSOlength</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Number of TSO messages queued for delivery to the joined federate</semantics>
          </attribute>
          <attribute>
            <name>HLAreflectionsReceived</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of times the Reflect Attribute Values † service has been invoked at the joined
                     federate (as opposed to the number of instance attribute value reflections that have been received
                     at the joined federate).</semantics>
          </attribute>
          <attribute>
            <name>HLAupdatesSent</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of updates sent by the joined federate&#x27; in XML and &#x27;Total number of times the
                     Update Attribute Values † service has successfully been invoked by the joined federate (as opposed
                     to the number of instance attribute values that have been updated by the joined federate).</semantics>
          </attribute>
          <attribute>
            <name>HLAinteractionsReceived</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of interactions received by the joined federate.</semantics>
          </attribute>
          <attribute>
            <name>HLAinteractionsSent</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of interactions sent by the joined federate. This information shall reflect
                     related DDM usage.</semantics>
          </attribute>
          <attribute>
            <name>HLAobjectInstancesThatCanBeDeleted</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of object instances whose HLAprivilegeToDeleteObject attribute is owned by the
                     joined federate</semantics>
          </attribute>
          <attribute>
            <name>HLAobjectInstancesUpdated</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of object instances for which the joined federate has invoked the Update
                     Attribute Values service.</semantics>
          </attribute>
          <attribute>
            <name>HLAobjectInstancesReflected</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of object instances for which the joined federate has had a Reflect Attribute
                     Values service invocation.</semantics>
          </attribute>
          <attribute>
            <name>HLAobjectInstancesDeleted</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of times the Delete Object Instance service was invoked by the joined federate
                     since the federate joined the federation</semantics>
          </attribute>
          <attribute>
            <name>HLAobjectInstancesRemoved</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of times the Remove Object Instance service was invoked for the joined
                     federate since the federate joined the federation.</semantics>
          </attribute>
          <attribute>
            <name>HLAobjectInstancesRegistered</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of times the Register Object Instance or Register Object Instance with Region
                     service were invoked by the joined federate since the federate joined the federation.</semantics>
          </attribute>
          <attribute>
            <name>HLAobjectInstancesDiscovered</name>
            <dataType>HLAcount</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Total number of times the Discover Object Instance † service was invoked for the joined
                     federate since the federate joined the federation.The value of the HLAobjectInstancesDiscovered
                     attribute shall include multiple invocations of the Discover Object Instance † service for a given
                     object instance that may occur as a result of invocation of the Local Delete Object Instance
                     service at a federate.</semantics>
          </attribute>
          <attribute>
            <name>HLAtimeGrantedTime</name>
            <dataType>HLAmsec</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Wall clock time duration that the federate has spent in the Time Granted state since the
                     last update of this attribute. When the HLAtimeGrantedTime and the HLAtimeAdvancingTime attributes
                     are initially updated, their values shall be the wall-clock time duration that the federate has
                     spent in the state since the federate has been joined to the federation execution.</semantics>
          </attribute>
          <attribute>
            <name>HLAtimeAdvancingTime</name>
            <dataType>HLAmsec</dataType>
            <updateType>Periodic</updateType>
            <updateCondition>HLAsetTiming.HLAreportPeriod</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Wall clock time duration that the federate has spent in the Time Advancing state since the
                     last update of this attribute. When the HLAtimeGrantedTime and the HLAtimeAdvancingTime attributes
                     are initially updated, their values shall be the wall-clock time duration that the federate has
                     spent in the state since the federate has been joined to the federation execution.</semantics>
          </attribute>
          <attribute>
            <name>HLAconveyRegionDesignatorSets</name>
            <dataType>HLAswitch</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever the HLAmanager.HLAfederate.HLAfederate.HLAadjust.HLAsetSwitches interaction
                     is sent to successfully change the value of the HLAconveyRegionDesignatorSets parameter.</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Value of joined federate&#x27;s Convey Region Designator Sets Switch. Updated when value of
                     switch changes</semantics>
          </attribute>
          <attribute>
            <name>HLAconveyProducingFederate</name>
            <dataType>HLAswitch</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever the HLAmanager.HLAfederate.HLAfederate.HLAadjust.HLAsetSwitches interaction
                     is sent to successfully change the value of the HLAconveyProducingFederate parameter.</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <dimensions>
              <dimension>HLAfederate</dimension>
            </dimensions>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Value of joined federate&#x27;s Convey Producing Federate Switch. Updated when value of switch
                     changes</semantics>
          </attribute>
        </objectClass>
        <objectClass>
          <name>HLAfederation</name>
          <sharing>Publish</sharing>
          <semantics>This object class shall contain RTI state variables relating to a federation execution. The
                  RTI shall publish it and shall register one object instance for the federation execution. The RTI
                  shall respond to the invocation, by any federate, of the Request Attribute Value Update service for
                  this object class or for any instance attribute of an object instance of this class by supplying
                  values via the normal instance attribute update mechanism, regardless of whether the attribute has a
                  data type of static or conditional. In addition to its responsibility to update attributes of object
                  instances of this class when those updates are explicitly requested, the RTI shall automatically
                  update instance attributes of object instances of this class according to the update policy of the
                  attribute, which is determined by the update type of the class attribute in Table 6. Those attributes
                  that have an update type of Conditional shall have update conditions as defined in the Table 6.</semantics>
          <attribute>
            <name>HLAfederationName</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Name of the federation to which the joined federate belongs</semantics>
          </attribute>
          <attribute>
            <name>HLAfederatesInFederation</name>
            <dataType>HLAhandleList</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Federate joins or resigns</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Identifiers of joined federates that are joined to the federation</semantics>
          </attribute>
          <attribute>
            <name>HLARTIversion</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Version of the RTI software</semantics>
          </attribute>
          <attribute>
            <name>HLAMIMdesignator</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Static</updateType>
            <updateCondition>NA</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Designator associated with the MIM specified in the Create Federation Execution service
                     invocation. In case the RTI has supplied the standard MIM, the designator shall be
                     “HLAstandardMIM”.</semantics>
          </attribute>
          <attribute>
            <name>HLAFOMmoduleDesignatorList</name>
            <dataType>HLAmoduleDesignatorList</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever new FOM modules are added by Create Federation Execution and Join Federation
                     Execution service invocations.</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>FOM Module designators for the federation as specified in the Create Federation Execution
                     service and Join Federation Execution invocations. If several identical FOM modules are provided
                     only the designator for the first of these FOM modules shall be added to the list.</semantics>
          </attribute>
          <attribute>
            <name>HLAcurrentFDD</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever the Current FOM subset is modified by Create Federation Execution and Join
                     Federation Execution service invocations.</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>The Current FDD realized as a result of antecedent successful Create Federation Execution
                     and Join Federation Execution service invocations.</semantics>
          </attribute>
          <attribute>
            <name>HLAtimeImplementationName</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Static</updateType>
            <updateCondition>N/A</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Name of the time implementation as supplied to the Create Federation Execution service when
                     the federation was created.</semantics>
          </attribute>
          <attribute>
            <name>HLAlastSaveName</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever Federation Saved service is successfully invoked with a save-success
                     indicator of successful</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Name associated with the last federation state save (null if no saves have occurred)</semantics>
          </attribute>
          <attribute>
            <name>HLAlastSaveTime</name>
            <dataType>HLAlogicalTime</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever Federation Saved service is successfully invoked with a save-success
                     indicator of successful</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Logical time at which the last federation state save occurred. If the last save was not a
                     timed save, then the HLAlastSaveTime attribute value shall be an empty (zero-length) HLAlogicalTime
                     array to indicate that the value of the HLAlastSaveTime attribute is undefined. If no timed saves
                     have occurred the value shall be an empty (zero-length) HLAlogicalTime array.</semantics>
          </attribute>
          <attribute>
            <name>HLAnextSaveName</name>
            <dataType>HLAunicodeString</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever Request Federation Save service is successfully invoked</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Name associated with the next federation state save (null if no saves are scheduled)</semantics>
          </attribute>
          <attribute>
            <name>HLAnextSaveTime</name>
            <dataType>HLAlogicalTime</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever Request Federation Save service is successfully invoked</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Logical time at which the next federation state timed save is scheduled. If no timed saves
                     are scheduled the value shall be an empty (zero-length) HLAlogicalTime array.</semantics>
          </attribute>
          <attribute>
            <name>HLAautoProvide</name>
            <dataType>HLAswitch</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>Whenever the HLAmanager.HLAfederate.HLAfederation.HLAadjust.HLAsetSwitches
                     interaction is sent to successfully change the value of the HLAautoProvide parameter.</updateCondition>
            <ownership>NoTransfer</ownership>
            <sharing>Publish</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>Value of federation-wide Auto-Provide Switch. Updated when value of switch changes</semantics>
          </attribute>
        </objectClass>
      </objectClass>
      <objectClass>
        <name>MorpheusGuard</name>
        <sharing>Neither</sharing>
        <semantics>守护Model,在引擎启动时默认创建</semantics>
        <attribute>
          <name>FrameLength</name>
          <dataType>HLAASCIIstring</dataType>
          <updateType>NA</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>N/A</semantics>
        </attribute>
        <attribute>
          <name>RecordInstanceHandle</name>
          <dataType>HLAASCIIstring</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>数据回放收集实例的ID</semantics>
        </attribute>
        <attribute>
          <name>Ration</name>
          <dataType>SimulationRation</dataType>
          <semantics>倍率</semantics>
          <updateType>Static</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
        </attribute>
      </objectClass>
      <objectClass>
        <name>MorpheusDataCollector</name>
        <sharing>Neither</sharing>
        <semantics>引擎数据收集Model</semantics>
        <attribute>
          <name>Items</name>
          <dataType>DataCollectItemArray</dataType>
          <updateType>Static</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>NoTransfer</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>采集项配置</semantics>
        </attribute>
        <attribute>
          <name>SampleRate</name>
          <dataType>HLAinteger32BE</dataType>
          <updateType>NA</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>采样频率，ms</semantics>
        </attribute>
        <attribute>
          <name>FilePath</name>
          <dataType>HLAASCIIstring</dataType>
          <updateType>NA</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>保存文件路径</semantics>
        </attribute>
      </objectClass>
      <objectClass>
        <name>MorpheusRecorder</name>
        <sharing>Neither</sharing>
        <semantics>负责引擎中的回放数据的收集</semantics>
        <attribute>
          <name>Switch</name>
          <dataType>HLAswitch</dataType>
          <updateType>Static</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>NoTransfer</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>开关</semantics>
        </attribute>
      </objectClass>
      <objectClass>
        <name>MorpheusACMIRecorder</name>
        <sharing>Neither</sharing>
        <semantics>acmi数据映射采集</semantics>
        <attribute>
          <name>Switch</name>
          <dataType>HLAswitch</dataType>
          <updateType>Static</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>NoTransfer</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>开关</semantics>
        </attribute>
      </objectClass>
      <objectClass>
        <name>MorpheusTimeAdvanceProxy</name>
        <sharing>Neither</sharing>
        <semantics>时间推进代理，用于接收外部的TimeGranted请求来推进时间,在timeAdvancePolicy设置为Constrained时生效</semantics>
        <attribute>
          <name>GrantedTime</name>
          <dataType>HLAinteger64BE</dataType>
          <updateType>Static</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>NoTransfer</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>当前可授权的时间</semantics>
        </attribute>
      </objectClass>
      <objectClass>
        <name>MorpheusEnvironments</name>
        <sharing>PublishSubscribe</sharing>
        <attribute>
          <name>FrameLength</name>
          <dataType>HLAASCIIstring</dataType>
          <updateType>NA</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>N/A</semantics>
        </attribute>
      </objectClass>
      <objectClass>
        <name>MorpheusLRC</name>
        <sharing>Publish</sharing>
        <attribute>
          <name>FrameLength</name>
          <dataType>HLAinteger32BE</dataType>
          <updateType>Static</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>N/A</semantics>
        </attribute>
        <attribute>
          <name>PubInteractionList</name>
          <dataType>HLAunicodeString</dataType>
          <updateType>NA</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>N/A</semantics>
        </attribute>
        <attribute>
          <name>SubInteractionList</name>
          <dataType>HLAunicodeString</dataType>
          <updateType>NA</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>N/A</semantics>
        </attribute>
        <attribute>
          <name>PubAttributeList</name>
          <dataType>HLAunicodeString</dataType>
          <updateType>NA</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>N/A</semantics>
        </attribute>
        <attribute>
          <name>SubAttributeList</name>
          <dataType>HLAunicodeString</dataType>
          <updateType>NA</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>N/A</semantics>
        </attribute>
      </objectClass>
    </objectClass>
  </objects>
  <interactions>
    <interactionClass>
      <name>HLAinteractionRoot</name>
      <sharing>Neither</sharing>
      <transportation>HLAreliable</transportation>
      <order>TimeStamp</order>
      <interactionClass>
        <name>HLAmanager</name>
        <sharing>Neither</sharing>
        <transportation>HLAreliable</transportation>
        <order>Receive</order>
        <semantics>Root class of MOM interactions</semantics>
      </interactionClass>
      <interactionClass>
        <name>MorpheusMessage</name>
        <sharing>Neither</sharing>
        <transportation>HLAbestEffort</transportation>
        <order>Receive</order>
        <interactionClass>
          <name>MorpheusMessageRequest</name>
          <sharing>Neither</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>morpheus引擎中请求的基本类型</semantics>
          <parameter>
            <name>originatingEntity</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>消息发送者的objectId</semantics>
          </parameter>
          <parameter>
            <name>eventTime</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>消息发送时间，为java.utils.duration的tostring值</semantics>
          </parameter>
          <parameter>
            <name>requestIdentifier</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>消息的唯一ID</semantics>
          </parameter>
          <parameter>
            <name>orderType</name>
            <dataType>HLAorderType</dataType>
            <semantics>TSO/RO</semantics>
          </parameter>
          <parameter>
            <name>messageType</name>
            <dataType>MorpheusMessageType</dataType>
            <semantics>REQUEST/TELL/RESPONSE</semantics>
          </parameter>
          <parameter>
            <name>receivingEntity</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>消息接收者ID</semantics>
          </parameter>
          <interactionClass>
            <name>MorpheusCreateObjectRequest</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>在morpheuse引擎中创建一个object</semantics>
            <parameter>
              <name>detail</name>
              <dataType>MorpheusObjectInitDetail</dataType>
              <semantics>请求详情</semantics>
            </parameter>
            <parameter>
              <name>NodeInstanceHandle</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>nodeId</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusInteractionMessage</name>
            <sharing>Neither</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>用于在morpheuse引擎中发起一次交互请求，分为交互的请求头（用于路由）和内容（在fom中定义的业务交互类）</semantics>
            <parameter>
              <name>interactionPayLoad</name>
              <dataType>MorpheusInteractionPayLoad</dataType>
              <semantics>模型业务交互类的序列化内容</semantics>
            </parameter>
            <parameter>
              <name>extraData</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusLoadScnRequest</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <semantics>加载想定</semantics>
            <parameter>
              <name>ModelName</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
            <parameter>
              <name>Version</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
            <parameter>
              <name>ClientId</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>请求的客户端ID</semantics>
            </parameter>
          </interactionClass>
        </interactionClass>
        <interactionClass>
          <name>MorpheusMessageBody</name>
          <sharing>Neither</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <interactionClass>
            <name>MorpheusRemoveObject</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>在morpheus引擎中删除一个实体</semantics>
            <parameter>
              <name>ObjectHandle</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusEnableDisableObject</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>在morpheus引擎中启用/禁用一个object</semantics>
            <parameter>
              <name>status</name>
              <dataType>HLAswitch</dataType>
              <semantics>enable/disable</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusAcknowledgeFromObject</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>morpheus中的ack</semantics>
            <parameter>
              <name>acknowledgeFlag</name>
              <dataType>MorpheusAcknowledgeFlagEnum16</dataType>
              <semantics>N/A</semantics>
            </parameter>
            <parameter>
              <name>responseFlag</name>
              <dataType>MorpheusResponseFlagEnum16</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusDataQueryToObject</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>在morpheus引擎中发起一次对象查询请求</semantics>
            <parameter>
              <name>attributeName</name>
              <dataType>HLAargumentList</dataType>
              <semantics>查询对象的属性名称</semantics>
            </parameter>
            <parameter>
              <name>ObjectInstanceHandle</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>查询对象的唯一id</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusDataQueryToObjectResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>MorpheusDataQueryToObjectRequest的ack</semantics>
            <parameter>
              <name>objectClassName</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>对象类名称</semantics>
            </parameter>
            <parameter>
              <name>dataValue</name>
              <dataType>MorpheusObjectAttributePairList</dataType>
              <semantics>属性名和属性值序列化内容的键值对</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusTreeQueryToObject</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>在morpheus中发起一次对象树查询</semantics>
            <parameter>
              <name>ObjectClassName</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>类名</semantics>
            </parameter>
            <parameter>
              <name>IncludeChildren</name>
              <dataType>HLAboolean</dataType>
              <semantics>是否包含子类</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusTreeQueryToObjectResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>MorpheusTreeQueryToObjectRequest的ack</semantics>
            <parameter>
              <name>data</name>
              <dataType>MorpheusObjectDescList</dataType>
              <semantics>object的基本信息列表</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusSetDataToObject</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>在morpheus发起一次属性修改请求</semantics>
            <parameter>
              <name>dataValue</name>
              <dataType>MorpheusObjectAttributePairList</dataType>
              <semantics>修改的属性详情</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusRemoveModel</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <semantics>发起一个在morpheus中删除一个model的请求</semantics>
          </interactionClass>
          <interactionClass>
            <name>MorpheusTransferObjectResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>传输object的所有属性</semantics>
            <parameter>
              <name>Attributes</name>
              <dataType>MorpheusObjectAttributePairList</dataType>
              <semantics>属性值</semantics>
            </parameter>
            <parameter>
              <name>ObjectClassName</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>对象名</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusSubTargetObjectAttributes</name>
            <sharing>Neither</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>订阅一个特定object的属性</semantics>
            <parameter>
              <name>Attributes</name>
              <dataType>HLAargumentList</dataType>
              <semantics>属性名</semantics>
            </parameter>
            <parameter>
              <name>OptType</name>
              <dataType>SubUnsubOperationEnum</dataType>
              <semantics>操作类型</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusSubTargetObjectAttributeResult</name>
            <sharing>Neither</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>MorphuesSubTargetObjectAttributes的推送结果</semantics>
            <parameter>
              <name>Data</name>
              <dataType>HLAopaqueData</dataType>
              <semantics>数据内容</semantics>
            </parameter>
            <parameter>
              <name>ObjectClassName</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>对象类名称</semantics>
            </parameter>
            <parameter>
              <name>AttributeName</name>
              <dataType>HLAunicodeString</dataType>
              <semantics>属性名称</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusTransferObjectRequest</name>
            <sharing>Neither</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>发起一个传输object请求</semantics>
            <parameter>
              <name>RecieveId</name>
              <dataType>EntityIdentifierStruct</dataType>
              <semantics>接收者ID</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusDataCollectRequest</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <semantics>请求收集数据</semantics>
            <parameter>
              <name>CollectName</name>
              <dataType>HLAunicodeString</dataType>
              <semantics>采集项名称</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusDataUpdateRequest</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <parameter>
              <name>ObjectInstanceHandle</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
            <parameter>
              <name>AttributeContent</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusDataUpdateResult</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <parameter>
              <name>ObjectInstanceHandle</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusRemoveObjectResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <parameter>
              <name>ObjectHandle</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
        </interactionClass>
        <interactionClass>
          <name>MorpheusModuleChanged</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>通知所有模型新的fom模块导入</semantics>
        </interactionClass>
        <interactionClass>
          <name>MorpheusApplicationRequest</name>
          <sharing>Neither</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>Morpheus</semantics>
          <parameter>
            <name>RequestIdentifier</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>请求的唯一id</semantics>
          </parameter>
          <interactionClass>
            <name>MorpheusApplicationConnectRequest</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>morpheus引擎的连接请求</semantics>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationConnectedRequest</name>
            <sharing>Neither</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <semantics>带token的请求</semantics>
            <parameter>
              <name>Token</name>
              <dataType>MorpheusAppUserToken</dataType>
              <semantics>morpheus引擎验证登录状态的token</semantics>
            </parameter>
            <interactionClass>
              <name>MorpheusApplicationDataCollectRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>用于在引擎中发起一项数据采集请求</semantics>
              <parameter>
                <name>Items</name>
                <dataType>DataCollectItemArray</dataType>
                <semantics>采集项</semantics>
              </parameter>
              <parameter>
                <name>SampleRate</name>
                <dataType>HLAinteger32BE</dataType>
                <semantics>采样周期,ms</semantics>
              </parameter>
              <parameter>
                <name>FilePath</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>保存文件路径</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationGetInteractionSchemaRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>Receive</order>
              <semantics>获取interaction的json schema，用于客户端渲染</semantics>
              <parameter>
                <name>InteractionName</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>interaction在fom中的名称</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationStartResumeRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAbestEffort</transportation>
              <order>Receive</order>
              <semantics>A Simulation Management (SiMan) interaction, sent from a Simulation Manager federate to
                    either a) start simulating one or more entities or b) resume simulation of one or more entities
                    after a pause.</semantics>
              <parameter>
                <name>OriginatingEntity</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>The DIS entity ID of the entity or application sending the interaction.</semantics>
              </parameter>
              <parameter>
                <name>ReceivingEntity</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>The DIS entity ID of the entity or application which is the intended recipient(s) of the interaction.</semantics>
              </parameter>
              <parameter>
                <name>RealWorldTime</name>
                <dataType>ClockTimeStruct</dataType>
                <semantics>The real world time (GMT) that the entity or entities should start/resume at.</semantics>
              </parameter>
              <parameter>
                <name>SimulationTime</name>
                <dataType>ClockTimeStruct</dataType>
                <semantics>The simulation time that the entity or entities should use when they start/resume.</semantics>
              </parameter>
              <parameter>
                <name>StopTime</name>
                <dataType>ClockTimeStruct</dataType>
                <semantics>The simulation time that the entity or entities should use when they stop</semantics>
              </parameter>
              <parameter>
                <name>OptType</name>
                <dataType>StartResumeOperationEnum</dataType>
                <semantics>OptType</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationStopFreezeRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAbestEffort</transportation>
              <order>Receive</order>
              <semantics>A Simulation Management (SiMan) interaction, sent from a Simulation Manager federate to
                    request that one or more entities either a) freeze (pause) their simulation or b) stop their
                    simulation.</semantics>
              <parameter>
                <name>OriginatingEntity</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>The DIS entity ID of the entity or application sending the interaction.</semantics>
              </parameter>
              <parameter>
                <name>ReceivingEntity</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>The DIS entity ID of the entity or application which is the intended recipient(s) of the interaction.</semantics>
              </parameter>
              <parameter>
                <name>RealWorldTime</name>
                <dataType>ClockTimeStruct</dataType>
                <semantics>The real world time (GMT) that the entity or entities should stop/freeze at.</semantics>
              </parameter>
              <parameter>
                <name>Reason</name>
                <dataType>StopFreezeReasonEnum8</dataType>
                <semantics>The reason for the stop or freeze.</semantics>
              </parameter>
              <parameter>
                <name>OptType</name>
                <dataType>StopFreezeOperationEnum</dataType>
                <semantics>OptType</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationImportModuleRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>在morpheus引擎中导入一个新的module</semantics>
              <parameter>
                <name>Modules</name>
                <dataType>HLAmoduleList</dataType>
                <semantics>依赖的模块列表</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationTreeQueryRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>Receive</order>
              <semantics>查询morpheus引擎中的对象树</semantics>
              <parameter>
                <name>ParentObjectInstanceHandle</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>父ID</semantics>
              </parameter>
              <parameter>
                <name>ObjectClassName</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>类名</semantics>
              </parameter>
              <parameter>
                <name>IncludeChildren</name>
                <dataType>HLAboolean</dataType>
                <semantics>是否包含子类</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationDataQueryRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>Receive</order>
              <semantics>在客户端中发起一次dataQuery</semantics>
              <parameter>
                <name>AttributeName</name>
                <dataType>HLAargumentList</dataType>
                <semantics>参数</semantics>
              </parameter>
              <parameter>
                <name>TargetObjectInstanceHandle</name>
                <dataType>HLAargumentList</dataType>
                <semantics>消息接收者</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationGetAttributesRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>Receive</order>
              <semantics>获取一个object的属性信息</semantics>
              <parameter>
                <name>TargetObjectInstanceHandle</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>被查询对象</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationCreateObjectRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>app端创建object的接口</semantics>
              <parameter>
                <name>Content</name>
                <dataType>HLAunicodeString</dataType>
                <semantics>待创建object的详情，json格式</semantics>
              </parameter>
              <parameter>
                <name>ParentObjectInstanceHandle</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>parnetId，当该object为rootobject时，parentId的entityNumber等于“-1”</semantics>
              </parameter>
              <parameter>
                <name>NodeInstanceHandle</name>
                <dataType>HLAunicodeString</dataType>
                <semantics>nodeId</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationRemoveObjectRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>在morpheus引擎中删除一个object</semantics>
              <parameter>
                <name>TargetObjectInstanceHandle</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>待删除的object</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationEnableDisableObjectRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>app端启用/禁用一个Object</semantics>
              <parameter>
                <name>TargetObjectInstanceHandle</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>操作对象</semantics>
              </parameter>
              <parameter>
                <name>Status</name>
                <dataType>HLAswitch</dataType>
                <semantics>启用/禁用</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationSchemaListRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>Receive</order>
              <semantics>获取当前的schema列表</semantics>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationSubObjectRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>订阅/取消订阅对象类</semantics>
              <parameter>
                <name>ObjectClasssName</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>对象类名称</semantics>
              </parameter>
              <parameter>
                <name>OptType</name>
                <dataType>SubUnsubOperationEnum</dataType>
                <semantics>订阅/取消订阅</semantics>
              </parameter>
              <parameter>
                <name>ReceiveEntity</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>订阅对象，为空代表订阅全部</semantics>
              </parameter>
              <interactionClass>
                <name>MorpheusApplicationSubObjectAttributesRequest</name>
                <sharing>PublishSubscribe</sharing>
                <transportation>HLAreliable</transportation>
                <order>TimeStamp</order>
                <semantics>订阅对象类的制定属性</semantics>
                <parameter>
                  <name>Attributes</name>
                  <dataType>HLAargumentList</dataType>
                  <semantics>指定的属性名</semantics>
                </parameter>
              </interactionClass>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationSubInteractionRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>订阅/取消订阅指定的交互类</semantics>
              <parameter>
                <name>InteractionClassName</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>交互类名称</semantics>
              </parameter>
              <parameter>
                <name>OptType</name>
                <dataType>SubUnsubOperationEnum</dataType>
                <semantics>订阅/取消订阅</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationSendInteractionRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>发送交互类</semantics>
              <parameter>
                <name>ReceiveEntity</name>
                <dataType>EntityIdentifierStruct</dataType>
                <semantics>接收对象</semantics>
              </parameter>
              <parameter>
                <name>InteractionClassName</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>交互类名称</semantics>
              </parameter>
              <parameter>
                <name>InteractionJSONValue</name>
                <dataType>HLAunicodeString</dataType>
                <semantics>交互的消息体，json字符串</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationPushDataRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>开始(停止)推送数据</semantics>
              <parameter>
                <name>OptType</name>
                <dataType>StartStopPushOperationEnum</dataType>
                <semantics>N/A</semantics>
              </parameter>
              <interactionClass>
                <name>MorpheusApplicationPushObjectAttributesRequest</name>
                <sharing>Neither</sharing>
                <transportation>HLAreliable</transportation>
                <order>TimeStamp</order>
                <semantics>对象类推送</semantics>
              </interactionClass>
              <interactionClass>
                <name>MorpheusApplicationPushInteractionRequest</name>
                <sharing>Neither</sharing>
                <transportation>HLAreliable</transportation>
                <order>TimeStamp</order>
                <semantics>交互类推送</semantics>
              </interactionClass>
              <interactionClass>
                <name>MorpheusApplicationPushClockRequest</name>
                <sharing>Neither</sharing>
                <transportation>HLAreliable</transportation>
                <order>TimeStamp</order>
                <semantics>始终推送</semantics>
              </interactionClass>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationSetSpeedMode</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>修改加速比</semantics>
              <parameter>
                <name>Speed</name>
                <dataType>HLAfloat64BE</dataType>
                <semantics>加速比，小于等于0时，引擎进入as fast as possible 模式</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationJumpRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAreliable</transportation>
              <order>Receive</order>
              <semantics>跳进至某个逻辑时间</semantics>
              <parameter>
                <name>SimulationTime</name>
                <dataType>ClockTimeStruct</dataType>
                <semantics>跳跃到的逻辑时间</semantics>
              </parameter>
              <parameter>
                <name>FreezeFlag</name>
                <dataType>HLAboolean</dataType>
                <semantics>是否暂停</semantics>
              </parameter>
              <parameter>
                <name>RelativeFlag</name>
                <dataType>HLAboolean</dataType>
                <semantics>是否为相对时间</semantics>
              </parameter>
              <parameter>
                <name>InteractionClassName</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>交互类名称</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationSearchObjectRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAreliable</transportation>
              <order>Receive</order>
              <semantics>搜索对象</semantics>
              <parameter>
                <name>ObjectClassName</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>对象类名称</semantics>
              </parameter>
              <parameter>
                <name>IncludeChildren</name>
                <dataType>HLAboolean</dataType>
                <semantics>是否包含子类</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationBreakPointRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>断点</semantics>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationFlashBackRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>开始回溯</semantics>
              <parameter>
                <name>BreakPointId</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>断点id</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationStartRecordRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAbestEffort</transportation>
              <order>Receive</order>
              <semantics>发起一次录制请求</semantics>
              <parameter>
                <name>InteractionNames</name>
                <dataType>HLAargumentList</dataType>
                <semantics>N/A</semantics>
              </parameter>
              <parameter>
                <name>FileName</name>
                <dataType>HLAunicodeString</dataType>
                <semantics>N/A</semantics>
              </parameter>
              <parameter>
                <name>Upload</name>
                <dataType>HLAboolean</dataType>
                <semantics>N/A</semantics>
              </parameter>
              <parameter>
                <name>UploadURL</name>
                <dataType>HLAunicodeString</dataType>
                <semantics>N/A</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationInitRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAreliable</transportation>
              <order>Receive</order>
              <semantics>引擎初始化请求</semantics>
              <parameter>
                <name>Body</name>
                <dataType>ApplicationInitParamRecVariantStruct</dataType>
                <semantics>初始化参数</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationTimeGrantRequest</name>
              <sharing>PublishSubscribe</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>app端时间授权交互,在引擎的timeAdvance为Constrained时生效</semantics>
              <parameter>
                <name>TimeAdvanceMillSecs</name>
                <dataType>HLAinteger64BE</dataType>
                <semantics>仿真时间推进的时间，单位:毫秒</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationLoadScnRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAbestEffort</transportation>
              <order>Receive</order>
              <semantics>加载想定</semantics>
              <parameter>
                <name>StartTime</name>
                <dataType>ClockTimeStruct</dataType>
                <semantics>开始时间</semantics>
              </parameter>
              <parameter>
                <name>StopTime</name>
                <dataType>ClockTimeStruct</dataType>
                <semantics>停止时间</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationCollectStopRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAreliable</transportation>
              <order>TimeStamp</order>
              <semantics>采集结束</semantics>
              <parameter>
                <name>Upload</name>
                <dataType>MorpheusBoolean</dataType>
                <semantics>是否需要上传</semantics>
              </parameter>
              <parameter>
                <name>UploadURL</name>
                <dataType>MorpheusString</dataType>
                <semantics>上传的url</semantics>
              </parameter>
              <parameter>
                <name>CollectId</name>
                <dataType>MorpheusString</dataType>
                <semantics>采集ID</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationStopRecordRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAbestEffort</transportation>
              <order>Receive</order>
              <semantics>停止录像</semantics>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationSetFrameRation</name>
              <sharing>Publish</sharing>
              <transportation>HLAbestEffort</transportation>
              <order>Receive</order>
              <parameter>
                <name>Ration</name>
                <dataType>HLAinteger16BE</dataType>
                <semantics>倍率</semantics>
              </parameter>
              <parameter>
                <name>EndTime</name>
                <dataType>HLAinteger64BE</dataType>
                <semantics>结束时间，逻辑时间</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationSetKeyInteractionRequest</name>
              <sharing>Publish</sharing>
              <transportation>HLAbestEffort</transportation>
              <order>Receive</order>
              <parameter>
                <name>InteractionName</name>
                <dataType>MorpheusString</dataType>
                <semantics>N/A</semantics>
              </parameter>
              <parameter>
                <name>OptType</name>
                <dataType>HLAinteger32BE</dataType>
                <semantics>N/A</semantics>
              </parameter>
            </interactionClass>
            <interactionClass>
              <name>MorpheusApplicationDataQueryByClassNameRequest</name>
              <sharing>Neither</sharing>
              <transportation>HLAbestEffort</transportation>
              <order>Receive</order>
              <semantics>根据类名批量查询</semantics>
              <parameter>
                <name>ObjectClassName</name>
                <dataType>HLAASCIIstring</dataType>
                <semantics>类名</semantics>
              </parameter>
              <parameter>
                <name>AttributeName</name>
                <dataType>HLAargumentList</dataType>
                <semantics>属性名</semantics>
              </parameter>
              <parameter>
                <name>IncludeChild</name>
                <dataType>HLAboolean</dataType>
                <semantics>是否包含子类</semantics>
              </parameter>
            </interactionClass>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationCommonRequest</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <parameter>
              <name>RequestCode</name>
              <dataType>MorpheusString</dataType>
              <semantics>N/A</semantics>
            </parameter>
            <parameter>
              <name>RequestMessage</name>
              <dataType>MorpheusString</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
        </interactionClass>
        <interactionClass>
          <name>MorpheusApplicationResult</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <parameter>
            <name>ResponseCode</name>
            <dataType>MorpheusResponseFlagEnum16</dataType>
            <semantics>操作结果</semantics>
          </parameter>
          <interactionClass>
            <name>MorpheusApplicationGetInteractionSchemaResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>MorpheusApplicationGetInteractionSchemaRequest的response</semantics>
            <parameter>
              <name>Content</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationTreeQueryResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>对象树查询结果</semantics>
            <parameter>
              <name>Data</name>
              <dataType>MorpheusObjectDescList</dataType>
              <semantics>对象树列表</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationDataQueryResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>对象属性查询结果</semantics>
            <parameter>
              <name>ObjectInstanceHandle</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>对象ID</semantics>
            </parameter>
            <parameter>
              <name>ObjectClassName</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>对象类名称</semantics>
            </parameter>
            <parameter>
              <name>Attributes</name>
              <dataType>MorpheusJsonFormatAttributeList</dataType>
              <semantics>查询的属性结果</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationGetAttributesResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>获得一个object的详情</semantics>
            <parameter>
              <name>Data</name>
              <dataType>ObjectAttributeList</dataType>
              <semantics>属性详情</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationCreateObjectResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>创建结果</semantics>
            <parameter>
              <name>ObjectInstanceHandle</name>
              <dataType>EntityIdentifierStruct</dataType>
              <semantics>创建对象ID</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationSchemaListResult</name>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>morpheus中目前已加载的fom的详情</semantics>
            <parameter>
              <name>ObjectSchemas</name>
              <dataType>ObjectSchemaList</dataType>
              <semantics>对象类</semantics>
            </parameter>
            <parameter>
              <name>InteractionSchemas</name>
              <dataType>InteractionSchemaList</dataType>
              <semantics>交互类</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationConnectResult</name>
            <sharing>Neither</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>登录返回</semantics>
            <parameter>
              <name>Token</name>
              <dataType>MorpheusAppUserToken</dataType>
              <semantics>用户token</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationSearchObjectResult</name>
            <sharing>Neither</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>搜索结果</semantics>
            <parameter>
              <name>Data</name>
              <dataType>MorpheusObjectDescList</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationBreakPointResult</name>
            <sharing>TimeStamp</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>断点结果</semantics>
            <parameter>
              <name>BreakPointId</name>
              <dataType>HLAASCIIstring</dataType>
              <semantics>断点id</semantics>
            </parameter>
            <parameter>
              <name>BreakPointLogicTime</name>
              <dataType>HLAinteger64BE</dataType>
              <semantics>断点逻辑时间</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationDataCollectResult</name>
            <sharing>Neither</sharing>
            <transportation>HLAreliable</transportation>
            <order>TimeStamp</order>
            <semantics>数据采集请求的结果</semantics>
            <parameter>
              <name>CollectId</name>
              <dataType>HLAunicodeString</dataType>
              <semantics>采集ID</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationCollectStopResult</name>
            <sharing>Neither</sharing>
            <transportation>HLAreliable</transportation>
            <order>Receive</order>
            <semantics>采集结束请求结果</semantics>
            <parameter>
              <name>FileId</name>
              <dataType>MorpheusString</dataType>
              <semantics>文件ID</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationStopRecordResult</name>
            <sharing>Neither</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <parameter>
              <name>FileId</name>
              <dataType>MorpheusString</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>MorpheusApplicationCommonResult</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <parameter>
              <name>ResultCode</name>
              <dataType>MorpheusString</dataType>
              <semantics>N/A</semantics>
            </parameter>
            <parameter>
              <name>ResultMessage</name>
              <dataType>MorpheusString</dataType>
              <semantics>N/A</semantics>
            </parameter>
          </interactionClass>
        </interactionClass>
        <interactionClass>
          <name>MorpheusApplicationPushObjectAttributesResult</name>
          <sharing>Neither</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>对象类推送数据</semantics>
          <parameter>
            <name>Result</name>
            <dataType>MorpheusApplicationPushObjectAttributeStruct</dataType>
            <semantics>推送内容</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>MorpheusApplicationPushInteractionResult</name>
          <sharing>Neither</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>交互类推送结果</semantics>
          <parameter>
            <name>Result</name>
            <dataType>MorpheusApplicationPushInteractionStruct</dataType>
            <semantics>推送内容</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>MorpheusApplicationPushClockResult</name>
          <sharing>Neither</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>时钟推送数据</semantics>
          <parameter>
            <name>LogicTime</name>
            <dataType>HLAinteger64BE</dataType>
            <semantics>逻辑时间</semantics>
          </parameter>
          <parameter>
            <name>PhysicsTime</name>
            <dataType>HLAinteger64BE</dataType>
            <semantics>物理时间</semantics>
          </parameter>
          <parameter>
            <name>Status</name>
            <dataType>ServerStatus</dataType>
            <semantics>引擎状态</semantics>
          </parameter>
          <parameter>
            <name>StateCode</name>
            <dataType>HLAinteger16BE</dataType>
            <semantics>引擎状态2</semantics>
          </parameter>
          <parameter>
            <name>ExpectedSpeed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>期望的加速比</semantics>
          </parameter>
          <parameter>
            <name>ActualSpeed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>真实加速比</semantics>
          </parameter>
          <parameter>
            <name>FrameRation</name>
            <dataType>HLAinteger16BE</dataType>
            <semantics>步长倍率</semantics>
          </parameter>
          <parameter>
            <name>EventsContent</name>
            <dataType>MorpheusString</dataType>
            <semantics>事件内容</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>MorpheusObjectDataLog</name>
          <sharing>Neither</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>数据采集-对象交互类</semantics>
          <parameter>
            <name>LogicTime</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>逻辑时间</semantics>
          </parameter>
          <parameter>
            <name>ObjectInstanceHandle</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>对象ID</semantics>
          </parameter>
          <parameter>
            <name>ObjectClassName</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>对象类名称</semantics>
          </parameter>
          <parameter>
            <name>ParentObjectInstanceHanle</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>上级对象ID</semantics>
          </parameter>
          <parameter>
            <name>Static</name>
            <dataType>HLAboolean</dataType>
            <semantics>是否静态资源</semantics>
          </parameter>
          <parameter>
            <name>DataBody</name>
            <dataType>HLAunicodeString</dataType>
            <semantics>采集内容</semantics>
          </parameter>
          <parameter>
            <name>CollectName</name>
            <dataType>HLAunicodeString</dataType>
            <semantics>采集项名称</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>MorpheusPushDataRequest</name>
          <sharing>Neither</sharing>
          <transportation>HLAreliable</transportation>
          <order>TimeStamp</order>
          <semantics>通知pushservice推送data</semantics>
          <parameter>
            <name>SimulationTime</name>
            <dataType>HLAASCIIstring</dataType>
            <semantics>推送条件：小于该仿真时间的对象属性和交互类会被推送</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>EnvWeatherData</name>
          <sharing>Publish</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>天气数据</semantics>
          <parameter>
            <name>Visibility</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>可见度</semantics>
          </parameter>
          <parameter>
            <name>Precipitation</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>降水量</semantics>
          </parameter>
          <parameter>
            <name>WindSpeed</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>风速</semantics>
          </parameter>
          <parameter>
            <name>WindDirection</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>风向</semantics>
          </parameter>
          <parameter>
            <name>Temperature</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>温度</semantics>
          </parameter>
          <parameter>
            <name>Humidity</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>湿度</semantics>
          </parameter>
          <parameter>
            <name>Longitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>经度</semantics>
          </parameter>
          <parameter>
            <name>Latitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>纬度</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>EnvTerrainData</name>
          <sharing>Publish</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>地形数据</semantics>
          <parameter>
            <name>Longitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>经度</semantics>
          </parameter>
          <parameter>
            <name>Latitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>纬度</semantics>
          </parameter>
          <parameter>
            <name>Elevation</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高程</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>EvnNotification</name>
          <sharing>Publish</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>环境通知</semantics>
          <parameter>
            <name>Longitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>经度</semantics>
          </parameter>
          <parameter>
            <name>Latitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>纬度</semantics>
          </parameter>
          <interactionClass>
            <name>EvnTerrainChg</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <semantics>地形改变</semantics>
          </interactionClass>
          <interactionClass>
            <name>EnvWeatherChg</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <semantics>天气改变</semantics>
          </interactionClass>
        </interactionClass>
        <interactionClass>
          <name>EnvChangeEvent</name>
          <sharing>Publish</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <parameter>
            <name>Longitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>N/A</semantics>
          </parameter>
          <parameter>
            <name>Latitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>N/A</semantics>
          </parameter>
          <interactionClass>
            <name>EnvCraterEvent</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <parameter>
              <name>Radius</name>
              <dataType>HLAfloat32BE</dataType>
              <semantics>半径</semantics>
            </parameter>
            <parameter>
              <name>Depth</name>
              <dataType>HLAfloat32BE</dataType>
              <semantics>深度</semantics>
            </parameter>
          </interactionClass>
          <interactionClass>
            <name>EnvBuildingDamageEvent</name>
            <sharing>Publish</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
          </interactionClass>
        </interactionClass>
      </interactionClass>
      <interactionClass>
        <name>RecordMessage</name>
        <sharing>Neither</sharing>
        <transportation>HLAreliable</transportation>
        <order>TimeStamp</order>
        <semantics>录像回放相关类</semantics>
      </interactionClass>
      <interactionClass>
        <name>NotFoundMessage</name>
        <sharing>Neither</sharing>
        <transportation>HLAbestEffort</transportation>
        <order>Receive</order>
        <semantics>用于通知guard处理reciever不存在的交互类</semantics>
        <parameter>
          <name>MessageReceiver</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>消息接收者</semantics>
        </parameter>
      </interactionClass>
      <interactionClass>
        <name>ErrorMessage</name>
        <sharing>Neither</sharing>
        <transportation>HLAbestEffort</transportation>
        <order>Receive</order>
        <semantics>错误消息</semantics>
        <parameter>
          <name>MessageReceiver</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>消息接收者</semantics>
        </parameter>
        <parameter>
          <name>ErrorCode</name>
          <dataType>HLAinteger32BE</dataType>
          <semantics>错误码</semantics>
        </parameter>
      </interactionClass>
    </interactionClass>
  </interactions>
  <dimensions>
    <dimension notes="MOM1">
      <name>HLAfederate</name>
      <dataType>HLAnormalizedFederateHandle</dataType>
      <normalization>Normalize Federate Handle service</normalization>
      <value>Excluded</value>
    </dimension>
    <dimension>
      <name>HLAserviceGroup</name>
      <dataType>HLAnormalizedServiceGroup</dataType>
      <upperBound>7</upperBound>
      <normalization>Normalize Service Group service</normalization>
      <value>Excluded</value>
    </dimension>
    <dimension>
      <name>MorpheusLongitude</name>
      <dataType>MorpheusNormalizedLongitude</dataType>
      <upperBound>360</upperBound>
      <normalization>Normalize MorpheusLongitude service</normalization>
      <value>Excluded</value>
    </dimension>
    <dimension>
      <name>MorpheusLatitude</name>
      <dataType>MorpheusNormalizedLatitude</dataType>
      <upperBound>360</upperBound>
      <normalization>Normalize MorpheusLatitude service</normalization>
      <value>Excluded</value>
    </dimension>
    <dimension>
      <name>MorpheusSpectrum</name>
      <dataType>MorpheusNormalizedSpectrum</dataType>
      <upperBound>6</upperBound>
      <normalization>Normalize MorpheusSpectrum service</normalization>
      <value>Excluded</value>
    </dimension>
    <dimension>
      <name>MorpheusEntityType</name>
      <dataType>MorpheusNormalizedEntityType</dataType>
      <upperBound>6</upperBound>
      <normalization>Normalize MorpheusEntityType service</normalization>
      <value>Excluded</value>
    </dimension>
    <dimension>
      <name>MorpheusWorld</name>
      <dataType>MorpheusNormalizedWorld</dataType>
      <upperBound>2</upperBound>
      <normalization>Normalize MorpheusWorld service</normalization>
      <value>Included</value>
    </dimension>
  </dimensions>
  <synchronizations />
  <transportations>
    <transportation>
      <name>HLAreliable</name>
      <reliable>Yes</reliable>
      <semantics>Provide reliable delivery of data in the sense that TCP/IP delivers its data reliably</semantics>
    </transportation>
    <transportation>
      <name>HLAbestEffort</name>
      <reliable>No</reliable>
      <semantics>Make an effort to deliver data in the sense that UDP provides best-effort delivery</semantics>
    </transportation>
  </transportations>
  <updateRates />
  <dataTypes>
    <basicDataRepresentations>
      <basicData>
        <name>HLAinteger16BE</name>
        <size>16</size>
        <interpretation>Integer in the range [-2^15, 2^15 - 1]</interpretation>
        <endian>Big</endian>
        <encoding>16-bit two&#x27;s complement signed integer. The most significant bit contains the sign.</encoding>
      </basicData>
      <basicData>
        <name>HLAinteger32BE</name>
        <size>32</size>
        <interpretation>Integer in the range [-2^31, 2^31 - 1]</interpretation>
        <endian>Big</endian>
        <encoding>32-bit two&#x27;s complement signed integer. The most significant bit contains the sign.</encoding>
      </basicData>
      <basicData>
        <name>HLAinteger64BE</name>
        <size>64</size>
        <interpretation>Integer in the range [-2^63, 2^63 - 1]</interpretation>
        <endian>Big</endian>
        <encoding>64-bit two&#x27;s complement signed integer first. The most significant bit contains the sign.</encoding>
      </basicData>
      <basicData>
        <name>HLAfloat32BE</name>
        <size>32</size>
        <interpretation>Single-precision floating point number</interpretation>
        <endian>Big</endian>
        <encoding>32-bit IEEE normalized single-precision format. See IEEE Std 754-1985</encoding>
      </basicData>
      <basicData>
        <name>HLAfloat64BE</name>
        <size>64</size>
        <interpretation>Double-precision floating point number</interpretation>
        <endian>Big</endian>
        <encoding>64-bit IEEE normalized double-precision format. See IEEE Std 754-1985</encoding>
      </basicData>
      <basicData>
        <name>HLAoctetPairBE</name>
        <size>16</size>
        <interpretation>16-bit value</interpretation>
        <endian>Big</endian>
        <encoding>Assumed to be portable among devices.</encoding>
      </basicData>
      <basicData>
        <name>HLAinteger16LE</name>
        <size>16</size>
        <interpretation>Integer in the range [-2^15, 2^15 - 1]</interpretation>
        <endian>Little</endian>
        <encoding>16-bit two&#x27;s complement signed integer. The most significant bit contains the sign.</encoding>
      </basicData>
      <basicData>
        <name>HLAinteger32LE</name>
        <size>32</size>
        <interpretation>Integer in the range [-2^31, 2^31 - 1]</interpretation>
        <endian>Little</endian>
        <encoding>32-bit two&#x27;s complement signed integer. The most significant bit contains the sign.</encoding>
      </basicData>
      <basicData>
        <name>HLAinteger64LE</name>
        <size>64</size>
        <interpretation>Integer in the range [-2^63, 2^63 - 1]</interpretation>
        <endian>Little</endian>
        <encoding>64-bit two&#x27;s complement signed integer first. The most significant bit contains the sign.</encoding>
      </basicData>
      <basicData>
        <name>HLAfloat32LE</name>
        <size>32</size>
        <interpretation>Single-precision floating point number</interpretation>
        <endian>Little</endian>
        <encoding>32-bit IEEE normalized single-precision format. See IEEE Std 754-1985</encoding>
      </basicData>
      <basicData>
        <name>HLAfloat64LE</name>
        <size>64</size>
        <interpretation>Double-precision floating point number</interpretation>
        <endian>Little</endian>
        <encoding>64-bit IEEE normalized double-precision format. See IEEE Std 754-1985</encoding>
      </basicData>
      <basicData>
        <name>HLAoctetPairLE</name>
        <size>16</size>
        <interpretation>16-bit value</interpretation>
        <endian>Little</endian>
        <encoding>Assumed to be portable among hardware devices.</encoding>
      </basicData>
      <basicData>
        <name>HLAoctet</name>
        <size>8</size>
        <interpretation>8-bit value</interpretation>
        <endian>Big</endian>
        <encoding>Assumed to be portable among hardware devices.</encoding>
      </basicData>
      <basicData>
        <name>RPRunsignedInteger16BE</name>
        <size>16</size>
        <interpretation>Integer in the range [0, 2^16-1]</interpretation>
        <endian>Big</endian>
        <encoding>16-bit unsigned integer.</encoding>
      </basicData>
      <basicData>
        <name>RPRunsignedInteger32BE</name>
        <size>32</size>
        <interpretation>Integer in the range [0, 2^32-1]</interpretation>
        <endian>Big</endian>
        <encoding>32-bit unsigned integer.</encoding>
      </basicData>
      <basicData>
        <name>MorpheusString</name>
        <size>1</size>
        <interpretation>String</interpretation>
        <endian>Big</endian>
        <encoding>变长的String</encoding>
      </basicData>
      <basicData>
        <name>MorpheusBoolean</name>
        <size>1</size>
        <interpretation>String</interpretation>
        <endian>Big</endian>
        <encoding>bool</encoding>
      </basicData>
      <basicData>
        <name>MorpheusByteString</name>
        <size>1</size>
        <endian>BIG</endian>
      </basicData>
    </basicDataRepresentations>
    <simpleDataTypes>
      <simpleData>
        <name>HLAASCIIchar</name>
        <representation>HLAoctet</representation>
        <units>NA</units>
        <resolution>NA</resolution>
        <accuracy>NA</accuracy>
        <semantics>Standard ASCII character (see ANSI Std x3.4-1986)</semantics>
      </simpleData>
      <simpleData>
        <name>HLAunicodeChar</name>
        <representation>HLAoctetPairBE</representation>
        <units>NA</units>
        <resolution>NA</resolution>
        <accuracy>NA</accuracy>
        <semantics>Unicode UTF-16 character (see The Unicode Standard, Version 3.0)</semantics>
      </simpleData>
      <simpleData>
        <name>HLAbyte</name>
        <representation>HLAoctet</representation>
        <units>NA</units>
        <resolution>NA</resolution>
        <accuracy>NA</accuracy>
        <semantics>Uninterpreted 8-bit byte</semantics>
      </simpleData>
      <simpleData>
        <name>HLAcount</name>
        <representation>HLAinteger32BE</representation>
        <units>NA</units>
        <resolution>NA</resolution>
        <accuracy>NA</accuracy>
        <semantics>NA</semantics>
      </simpleData>
      <simpleData>
        <name>HLAseconds</name>
        <representation>HLAinteger32BE</representation>
        <units>s</units>
        <resolution>NA</resolution>
        <accuracy>NA</accuracy>
        <semantics>NA</semantics>
      </simpleData>
      <simpleData>
        <name>HLAmsec</name>
        <representation>HLAinteger32BE</representation>
        <units>ms</units>
        <resolution>NA</resolution>
        <accuracy>NA</accuracy>
        <semantics>NA</semantics>
      </simpleData>
      <simpleData>
        <name>HLAnormalizedFederateHandle</name>
        <representation>HLAinteger32BE</representation>
        <units>NA</units>
        <resolution>NA</resolution>
        <accuracy>NA</accuracy>
        <semantics>The type of the normalized value of a federate handle as returned by the Normalize Federate
               Handle service. The value is appropriate for defining the range of the HLAfederate dimension for regions
               with this dimension.</semantics>
      </simpleData>
      <simpleData>
        <name>HLAindex</name>
        <representation>HLAinteger32BE</representation>
        <units>NA</units>
        <resolution>NA</resolution>
        <accuracy>NA</accuracy>
        <semantics>NA</semantics>
      </simpleData>
      <simpleData>
        <name>HLAinteger64Time</name>
        <representation>HLAinteger64BE</representation>
        <units>NA</units>
        <resolution>1</resolution>
        <accuracy>NA</accuracy>
        <semantics>Standardized 64 bit integer time</semantics>
      </simpleData>
      <simpleData>
        <name>HLAfloat64Time</name>
        <representation>HLAfloat64BE</representation>
        <units>NA</units>
        <resolution>4.9E-308</resolution>
        <accuracy>NA</accuracy>
        <semantics>Standardized 64 bit float time</semantics>
      </simpleData>
      <simpleData>
        <name>UnsignedInteger16</name>
        <representation>RPRunsignedInteger16BE</representation>
        <units>N/A</units>
        <resolution>1</resolution>
        <accuracy>perfect</accuracy>
        <semantics>Integer in the range [0, 2^16].</semantics>
      </simpleData>
      <simpleData>
        <name>ClockTimeHourInteger32</name>
        <representation>HLAinteger32BE</representation>
        <units>hour</units>
        <resolution>1</resolution>
        <accuracy>perfect</accuracy>
        <semantics>Time past on the clock in full hours since a specified point in time.</semantics>
      </simpleData>
      <simpleData>
        <name>TimestampUnsignedInteger32</name>
        <representation>RPRunsignedInteger32BE</representation>
        <units>1.676 microsecond</units>
        <resolution>1</resolution>
        <accuracy>perfect</accuracy>
        <semantics>The scale of the time value contained in the most significant 31 bits of the timestamp shall
					be determined by setting one
					hour equal to (2^31-1), thereby resulting in each time unit representing 3600 s/(2^31-1) = 1.676
					microsecond.</semantics>
      </simpleData>
      <simpleData>
        <name>MorpheusNormalizedLongitude</name>
        <representation>HLAinteger32BE</representation>
        <units>N/A</units>
        <resolution>N/A</resolution>
        <accuracy>N/A</accuracy>
        <semantics>The type of the normalized value of a longitude as returned by the Normalize Longtitude service.The value is appropriate for defining the range of the Longitude dimension for regions with this dimension.</semantics>
      </simpleData>
      <simpleData>
        <name>MorpheusNormalizedLatitude</name>
        <representation>HLAinteger32BE</representation>
        <units>N/A</units>
        <resolution>N/A</resolution>
        <accuracy>N/A</accuracy>
        <semantics>The type of the normalized value of a latitude as returned by the Normalize Latitude service.The value is appropriate for defining the range of the Latitude dimension for regions with this dimension.</semantics>
      </simpleData>
      <simpleData>
        <name>MorpheusNormalizedSpectrum</name>
        <representation>HLAinteger32BE</representation>
        <units>N/A</units>
        <resolution>N/A</resolution>
        <accuracy>N/A</accuracy>
        <semantics>The type of the normalized value of a spectrumas returned by the Normalize Spectrum service.The value is appropriate for defining the range of the Spectrum dimension for regions with this dimension.</semantics>
      </simpleData>
    </simpleDataTypes>
    <enumeratedDataTypes>
      <enumeratedData>
        <name>HLAboolean</name>
        <representation>HLAinteger32BE</representation>
        <semantics>Standard boolean type</semantics>
        <enumerator>
          <name>HLAfalse</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>HLAtrue</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>HLAfederateState</name>
        <representation>HLAinteger32BE</representation>
        <semantics>State of the federate</semantics>
        <enumerator>
          <name>ActiveFederate</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>FederateSaveInProgress</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>FederateRestoreInProgress</name>
          <value>5</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>HLAtimeState</name>
        <representation>HLAinteger32BE</representation>
        <semantics>State of time advancement</semantics>
        <enumerator>
          <name>TimeGranted</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>TimeAdvancing</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>HLAownership</name>
        <representation>HLAinteger32BE</representation>
        <semantics>NA</semantics>
        <enumerator>
          <name>Unowned</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Owned</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>HLAresignAction</name>
        <representation>HLAinteger32BE</representation>
        <semantics>Action to be performed by RTI in conjunction with resignation</semantics>
        <enumerator>
          <name>DivestOwnership</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>DeleteObjectInstances</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>CancelPendingAcquisitions</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>DeleteObjectInstancesThenDivestOwnership</name>
          <value>4</value>
        </enumerator>
        <enumerator>
          <name>CancelPendingAcquisitionsThenDeleteObjectInstancesThenDivestOwnership</name>
          <value>5</value>
        </enumerator>
        <enumerator>
          <name>NoAction</name>
          <value>6</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>HLAorderType</name>
        <representation>HLAinteger32BE</representation>
        <semantics>Order type to be used for sending attributes or interactions</semantics>
        <enumerator>
          <name>Receive</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>TimeStamp</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>HLAswitch</name>
        <representation>HLAinteger32BE</representation>
        <semantics>NA</semantics>
        <enumerator>
          <name>Enabled</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Disabled</name>
          <value>0</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>HLAsynchPointStatus</name>
        <representation>HLAinteger32BE</representation>
        <semantics>Joined federate synchronization point status</semantics>
        <enumerator>
          <name>NoActivity</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>AttemptingToRegisterSynchPoint</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>MovingToSynchPoint</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>WaitingForRestOfFederation</name>
          <value>3</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>HLAnormalizedServiceGroup</name>
        <representation>HLAinteger32BE</representation>
        <semantics>Service group identifier</semantics>
        <enumerator>
          <name>FederationManagement</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>DeclarationManagement</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>ObjectManagement</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>OwnershipManagement</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>TimeManagement</name>
          <value>4</value>
        </enumerator>
        <enumerator>
          <name>DataDistributionManagement</name>
          <value>5</value>
        </enumerator>
        <enumerator>
          <name>SupportServices</name>
          <value>6</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>MorpheusAcknowledgeFlagEnum16</name>
        <representation>HLAinteger16BE</representation>
        <semantics>morpheus Acknowledgment flags</semantics>
        <enumerator>
          <name>CreateEntity</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>RemoveEntity</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>StartResume</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>StopFreeze</name>
          <value>4</value>
        </enumerator>
        <enumerator>
          <name>TransferControlRequest</name>
          <value>5</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>MorpheusMessageType</name>
        <representation>HLAinteger16BE</representation>
        <semantics>请求类型</semantics>
        <enumerator>
          <name>REQUEST</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>TELL</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>RESPONSE</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>MorpheusResponseFlagEnum16</name>
        <representation>HLAinteger16BE</representation>
        <semantics>morpheus Response flag</semantics>
        <enumerator>
          <name>Other</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>AbleToComply</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>UnableToComply</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>ObjectDisabled</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>ObjectNotFound</name>
          <value>4</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>StartResumeOperationEnum</name>
        <representation>HLAinteger16BE</representation>
        <semantics>开始/恢复标识</semantics>
        <enumerator>
          <name>Start</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Resume</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>StopFreezeReasonEnum8</name>
        <representation>HLAoctet</representation>
        <semantics>Reason to stop</semantics>
        <enumerator>
          <name>Other</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Recess</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Termination</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>SystemFailure</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>SecurityViolation</name>
          <value>4</value>
        </enumerator>
        <enumerator>
          <name>EntityReconstitution</name>
          <value>5</value>
        </enumerator>
        <enumerator>
          <name>StopForReset</name>
          <value>6</value>
        </enumerator>
        <enumerator>
          <name>StopForRestart</name>
          <value>7</value>
        </enumerator>
        <enumerator>
          <name>AbortTrainingResumeTacOps</name>
          <value>8</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>StopFreezeOperationEnum</name>
        <representation>HLAinteger16BE</representation>
        <semantics>停止/暂停标志</semantics>
        <enumerator>
          <name>Stop</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Freeze</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>Sharing</name>
        <representation>HLAinteger16BE</representation>
        <semantics>属性/交互类发布类型枚举</semantics>
        <enumerator>
          <name>PUBLISH</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>SUBSCRIBE</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>PUBLISHSUBSCRIBE</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>NEITHER</name>
          <value>4</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>JSONFormatBascieTypeEnum</name>
        <representation>HLAinteger16BE</representation>
        <semantics>JSONFormat后的类型枚举</semantics>
        <enumerator>
          <name>Long</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Integer</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Short</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>Float</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>Double</name>
          <value>4</value>
        </enumerator>
        <enumerator>
          <name>Byte</name>
          <value>5</value>
        </enumerator>
        <enumerator>
          <name>String</name>
          <value>6</value>
        </enumerator>
        <enumerator>
          <name>Json</name>
          <value>7</value>
        </enumerator>
        <enumerator>
          <name>Boolean</name>
          <value>8</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>SubUnsubOperationEnum</name>
        <representation>HLAinteger16BE</representation>
        <semantics>订阅/取消订阅</semantics>
        <enumerator>
          <name>Sub</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Unsub</name>
          <value>0</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>StartStopPushOperationEnum</name>
        <representation>HLAinteger16BE</representation>
        <semantics>开始/停止推送数据</semantics>
        <enumerator>
          <name>Start</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Stop</name>
          <value>0</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>ServerStatus</name>
        <representation>HLAinteger16BE</representation>
        <semantics>服务器状态</semantics>
        <enumerator>
          <name>Running</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Stoped</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Paused</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>MorpheusAppEnum</name>
        <representation>HLAinteger16BE</representation>
        <semantics>app类型枚举</semantics>
        <enumerator>
          <name>SimulationService</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>ReplayService</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>MorpheusNormalizedEntityType</name>
        <representation>HLAinteger32BE</representation>
        <semantics>实体类型</semantics>
        <enumerator>
          <name>Other</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Land</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Air</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>Surface</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>SubSurface</name>
          <value>4</value>
        </enumerator>
        <enumerator>
          <name>Space</name>
          <value>5</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>MorpheusNormalizedWorld</name>
        <representation>HLAinteger32BE</representation>
        <semantics>所属星球</semantics>
        <enumerator>
          <name>Earth</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Moon</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Other</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>PushTypeEnum</name>
        <representation>HLAinteger32BE</representation>
        <semantics>推送类型</semantics>
        <enumerator>
          <name>Data</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Time</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
    </enumeratedDataTypes>
    <arrayDataTypes>
      <arrayData>
        <name>HLAASCIIstring</name>
        <dataType>HLAASCIIchar</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>ASCII string representation</semantics>
      </arrayData>
      <arrayData>
        <name>HLAunicodeString</name>
        <dataType>HLAunicodeChar</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>Unicode string representation</semantics>
      </arrayData>
      <arrayData>
        <name>HLAopaqueData</name>
        <dataType>HLAbyte</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>Uninterpreted sequence of bytes</semantics>
      </arrayData>
      <arrayData>
        <name>HLAtoken</name>
        <dataType>HLAbyte</dataType>
        <cardinality>0</cardinality>
        <encoding>HLAfixedArray</encoding>
      </arrayData>
      <arrayData>
        <name>HLAhandle</name>
        <dataType>HLAbyte</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>Encoded value of a handle. The encoding is based on the type of handle</semantics>
      </arrayData>
      <arrayData>
        <name>HLAtransportationName</name>
        <dataType>HLAunicodeChar</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>String whose legal value shall be a name from any row in the OMT transportation table (IEEE Std
               1516.2-2010)</semantics>
      </arrayData>
      <arrayData>
        <name>HLAupdateRateName</name>
        <dataType>HLAunicodeChar</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>String whose legal value shall be a name from any row in the OMT update rate table (IEEE Std
               1516.2-2010)</semantics>
      </arrayData>
      <arrayData>
        <name>HLAlogicalTime</name>
        <dataType>HLAbyte</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>An encoded logical time. An empty array shall indicate that the values is not defined</semantics>
      </arrayData>
      <arrayData>
        <name>HLAtimeInterval</name>
        <dataType>HLAbyte</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>An encoded logical time interval. An empty array shall indicate that the values is not defined</semantics>
      </arrayData>
      <arrayData>
        <name>HLAhandleList</name>
        <dataType>HLAhandle</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>List of encoded handles</semantics>
      </arrayData>
      <arrayData>
        <name>HLAinteractionSubList</name>
        <dataType>HLAinteractionSubscription</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>List of interaction subscription indicators</semantics>
      </arrayData>
      <arrayData>
        <name>HLAargumentList</name>
        <dataType>HLAunicodeString</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>List of arguments</semantics>
      </arrayData>
      <arrayData>
        <name>HLAobjectClassBasedCounts</name>
        <dataType>HLAobjectClassBasedCount</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>List of counts of various items based on object class. In all MOM interactions that have a
               parameter of datatype HLAobjectClassBased- Counts, if an HLAobjectClassBasedCount element of the
               HLAobjectClassBasedCounts array would have a value (object class, 0), the HLAobjectClassBasedCount
               element shall not be present in the HLAobjectClassBasedCounts array. In other words, only HLAobject-
               ClassBasedCount elements that have positive counts shall be present in an HLAobjectClassBasedCounts
               array. From this, it follows that if all object class counts have a zero value, then the HLAobjectClass-
               BasedCounts array shall not have any elements in it; it shall be an empty HLAobjectClassBasedCounts
               array.</semantics>
      </arrayData>
      <arrayData>
        <name>HLAinteractionCounts</name>
        <dataType>HLAinteractionCount</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>List of interaction counts. In all MOM interactions that have a parameter of datatype
               HLAinteractionCounts, if an HLAinteractionCount element of the HLAinteractionCounts array would have a
               value (interaction class, 0), the HLAinteractionCount element shall not be present in the
               HLAinteractionCounts array. In other words, only HLAinteractionCount elements that have positive counts
               shall be present in an HLAinteractionCounts array. From this, it follows that if all interaction class
               counts have a zero value, then the HLAinteractionCounts array shall not have any elements in it; it shall
               be an empty HLAinteractionCounts array.</semantics>
      </arrayData>
      <arrayData>
        <name>HLAsynchPointList</name>
        <dataType>HLAunicodeString</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>List of names of synchronization points.</semantics>
      </arrayData>
      <arrayData>
        <name>HLAsynchPointFederateList</name>
        <dataType>HLAsynchPointFederate</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>List of joined federates and the synchronization status of each.</semantics>
      </arrayData>
      <arrayData>
        <name>HLAmoduleDesignatorList</name>
        <dataType>HLAunicodeString</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>List of designators of FOM modules.</semantics>
      </arrayData>
      <arrayData>
        <name>MorpheusObjectAttributePairList</name>
        <dataType>MorpheusObjectAttributePair</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>HLAMorpheusObjectAttributePair的list</semantics>
      </arrayData>
      <arrayData>
        <name>MorpheusObjectInitDetailList</name>
        <dataType>MorpheusObjectInitDetail</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>object初始化信息的list</semantics>
      </arrayData>
      <arrayData>
        <name>MorpheusObjectDescList</name>
        <dataType>MorpheusObjectDesc</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>MorpheusObjectDesc的list</semantics>
      </arrayData>
      <arrayData>
        <name>ObjectAttributeList</name>
        <dataType>ObjectAttribute</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>ObjectAttribute的list</semantics>
      </arrayData>
      <arrayData>
        <name>MorpheusJsonFormatAttributeList</name>
        <dataType>MorpheusJsonFormatAttribute</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>MorpheusJsonFormatAttribute的list</semantics>
      </arrayData>
      <arrayData>
        <name>InteractionParameterList</name>
        <dataType>InteractionParameter</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>交互类参数列表</semantics>
      </arrayData>
      <arrayData>
        <name>ObjectSchemaList</name>
        <dataType>ObjectSchema</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>对象类详情列表</semantics>
      </arrayData>
      <arrayData>
        <name>InteractionSchemaList</name>
        <dataType>InteractionSchema</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>交互类详情列表</semantics>
      </arrayData>
      <arrayData>
        <name>HLAmoduleList</name>
        <dataType>HLAmoduleIndex</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>SOM索引数组</semantics>
      </arrayData>
      <arrayData>
        <name>DataCollectItemArray</name>
        <dataType>DataCollectItem</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>数据采集配置项数组</semantics>
      </arrayData>
      <arrayData>
        <name>RTIobjectId</name>
        <dataType>HLAunicodeChar</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>An RTI object instance identification string.</semantics>
      </arrayData>
    </arrayDataTypes>
    <fixedRecordDataTypes>
      <fixedRecordData>
        <name>HLAinteractionSubscription</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>Interaction subscription information</semantics>
        <field>
          <name>HLAinteractionClass</name>
          <dataType>HLAhandle</dataType>
          <semantics>Encoded interaction class handle</semantics>
        </field>
        <field>
          <name>HLAactive</name>
          <dataType>HLAboolean</dataType>
          <semantics>Whether subscription is active (HLAtrue=active)</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>HLAobjectClassBasedCount</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>Object class and count of associated items</semantics>
        <field>
          <name>HLAobjectClass</name>
          <dataType>HLAhandle</dataType>
          <semantics>Encoded object class handle</semantics>
        </field>
        <field>
          <name>HLAcount</name>
          <dataType>HLAcount</dataType>
          <semantics>Number of items</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>HLAinteractionCount</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>Count of interactions of a class</semantics>
        <field>
          <name>HLAinteractionClass</name>
          <dataType>HLAhandle</dataType>
          <semantics>Encoded interaction class handle</semantics>
        </field>
        <field>
          <name>HLAinteractionCount</name>
          <dataType>HLAcount</dataType>
          <semantics>Number of interactions</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>HLAsynchPointFederate</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>A particular joined federate and its synchronization point status</semantics>
        <field>
          <name>HLAfederate</name>
          <dataType>HLAhandle</dataType>
          <semantics>Encoded joined federate handle</semantics>
        </field>
        <field>
          <name>HLAfederateSynchStatus</name>
          <dataType>HLAsynchPointStatus</dataType>
          <semantics>Synchronization status of the particular joined federate</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>MorpheusInteractionPayLoad</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>N/A</semantics>
        <field>
          <name>InteractionClassHandle</name>
          <dataType>HLAinteger32BE</dataType>
          <semantics>交互类对象id</semantics>
        </field>
        <field>
          <name>Context</name>
          <dataType>HLAopaqueData</dataType>
          <semantics>交互类内容的序列化结果</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>MorpheusObjectAttributePair</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>morpheuse中对象的attribute的值</semantics>
        <field>
          <name>Key</name>
          <dataType>HLAunicodeString</dataType>
          <semantics>attribute在fom中的name</semantics>
        </field>
        <field>
          <name>Value</name>
          <dataType>HLAopaqueData</dataType>
          <semantics>attribute的值的序列化值</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>MorpheusObjectInitDetail</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>创建object的参数</semantics>
        <field>
          <name>ObjectInstanceHandle</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>对象的id</semantics>
        </field>
        <field>
          <name>ObjectClassName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>对象在fom中的localname</semantics>
        </field>
        <field>
          <name>ModelName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>对象在fom中的modelName</semantics>
        </field>
        <field>
          <name>ParentObjectInstanceHandle</name>
          <dataType>EntityIdentifierStruct</dataType>
          <semantics>对象的parentid,“-1”代表顶级对象</semantics>
        </field>
        <field>
          <name>Component</name>
          <dataType>MorpheusObjectInitDetailList</dataType>
          <semantics>object的组件</semantics>
        </field>
        <field>
          <name>Attribute</name>
          <dataType>MorpheusObjectAttributePairList</dataType>
          <semantics>object的初始化参数</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>MorpheusObjectDesc</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>N/A</semantics>
        <field>
          <name>ObjectClassName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>对象类名称</semantics>
        </field>
        <field>
          <name>ObjectInstanceHandle</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>对象ID</semantics>
        </field>
        <field>
          <name>Status</name>
          <dataType>HLAswitch</dataType>
          <semantics>是否启用</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>MorpheusAppUserToken</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>morpheus引擎app的token标识</semantics>
        <field>
          <name>ClientId</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>客户端ID</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>EntityIdentifierStruct</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>Unique, exercise-wide identification of the entity, or a symbolic group address referencing multiple entities or a simulation application. Based on the Entity Identifier record as specified in IEEE 1278.1-1995 section 5.2.14.</semantics>
        <field>
          <name>FederateIdentifier</name>
          <dataType>FederateIdentifierStruct</dataType>
          <semantics>Simulation application (federate) identifier.</semantics>
        </field>
        <field>
          <name>EntityNumber</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>Each entity in a given simulation application shall be given an entity identifier number unique to all other entities in that application. This identifier number is valid for the duration of the exercise; however, entity identifier numbers may be reused when all possible numbers have been exhausted. No entity shall have an entity identifier number of NO_ENTITY (0), ALL_ENTITIES (0xFFFF), or RQST_ASSIGN_ID (0xFFFE). The entity identifier number need not be registered or retained for future exercises. An entity identifier number equal to zero with valid site and application identification shall address a simulation application. An entity identifier number equal to ALL_ENTITIES shall mean all entities within the specified site and application. An entity identifier number equal to RQST_ASSIGN_ID allows the receiver of the CreateEntity interaction to define the entity identifier number of the new entity. The new entity will specify its entity identifier number in the Acknowledge interaction.</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>FederateIdentifierStruct</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>Unique identification of the simulation application (federate) in an exercise, or a symbolic group address referencing multiple simulation applications. Based on the Simulation Address record as specified in IEEE 1278.1-1995 section ********.</semantics>
        <field>
          <name>SiteID</name>
          <dataType>UnsignedInteger16</dataType>
          <semantics>Each site shall be assigned a unique site identification. No individual site shall be assigned an identification number containing NO_SITE (0) or ALL_SITES (0xFFFF). An identification number equal to ALL_SITES (0xFFFF) shall mean all sites; this may be used to initialize or start all sites. The mechanism by which Site Identification numbers are assigned is part of federation agreements.</semantics>
        </field>
        <field>
          <name>ApplicationID</name>
          <dataType>UnsignedInteger16</dataType>
          <semantics>Each simulation application (federate) at a site shall be assigned an identification number unique within that site. No simulation application shall be assigned an identification number containing NO_APPLIC (0) or ALL_APPLIC (0xFFFF). An application identification number equal to ALL_APPLIC (0xFFFF) shall mean all applications; this may be used to start all applications within a site. One or more simulation applications may reside in a single host computer. The mechanism by which application identification numbers are assigned is part of federation agreements.</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>ClockTimeStruct</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>Specification of the point in time of an occurrence. Based on the Clock Time record as
                    specified in IEEE 1278.1-1995
                    section 5.2.8.</semantics>
        <field>
          <name>Hours</name>
          <dataType>ClockTimeHourInteger32</dataType>
          <semantics>The number of hours since 0000 hours, January 1, 1970 UTC.</semantics>
        </field>
        <field>
          <name>TimePastTheHour</name>
          <dataType>TimestampUnsignedInteger32</dataType>
          <semantics>The time past the hour indicated in the Hours field.</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>ObjectAttribute</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>object的属性信息</semantics>
        <field>
          <name>AttributeName</name>
          <dataType>HLAunicodeString</dataType>
          <semantics>属性名</semantics>
        </field>
        <field>
          <name>DataType</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>属性类型</semantics>
        </field>
        <field>
          <name>ObjectClassName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>所属对象的localname</semantics>
        </field>
        <field>
          <name>SharingValue</name>
          <dataType>Sharing</dataType>
          <semantics>订阅发布属性</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>MorpheusJsonFormatAttribute</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>json序列化后的attribute对象</semantics>
        <field>
          <name>AttributeName</name>
          <dataType>HLAunicodeString</dataType>
          <semantics>attribuet name</semantics>
        </field>
        <field>
          <name>AttributeType</name>
          <dataType>JSONFormatBascieTypeEnum</dataType>
          <semantics>类型</semantics>
        </field>
        <field>
          <name>AttributeValue</name>
          <dataType>HLAunicodeString</dataType>
          <semantics>attribute value</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>ObjectSchema</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>对象类详情</semantics>
        <field>
          <name>ObjectClassName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>对象类名称</semantics>
        </field>
        <field>
          <name>Attributes</name>
          <dataType>ObjectAttributeList</dataType>
          <semantics>属性列表</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>InteractionSchema</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>交互类详情</semantics>
        <field>
          <name>InteractionClassName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>交互类名称</semantics>
        </field>
        <field>
          <name>Parameters</name>
          <dataType>InteractionParameterList</dataType>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>InteractionParameter</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>交互类参数</semantics>
        <field>
          <name>ParameterName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>参数名</semantics>
        </field>
        <field>
          <name>ParameterDataTypeName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>参数类型</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>DataCollectItem</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>采集项定义</semantics>
        <field>
          <name>Name</name>
          <dataType>HLAunicodeString</dataType>
          <semantics>采集项名称</semantics>
        </field>
        <field>
          <name>Sql</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>采集项sql</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>ReplayServiceInitParamStruct</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>回放服务初始化参数</semantics>
        <field>
          <name>RecordInstanceHandle</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>录像记录id</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>HLAmoduleIndex</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>SOM的索引</semantics>
        <field>
          <name>Name</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>名称</semantics>
        </field>
        <field>
          <name>Version</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>版本</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>ApplicationAttributeSturct</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>交互类结构体，用于推送给外部客户端接口</semantics>
        <field>
          <name>AttributeDataType</name>
          <dataType>JSONFormatBascieTypeEnum</dataType>
          <semantics>属性类型</semantics>
        </field>
        <field>
          <name>AttributeName</name>
          <dataType>HLAunicodeString</dataType>
          <semantics>属性名称</semantics>
        </field>
        <field>
          <name>AttributeValue</name>
          <dataType>HLAunicodeString</dataType>
          <semantics>属性的值</semantics>
        </field>
        <field>
          <name>LogicTime</name>
          <dataType>HLAinteger64BE</dataType>
          <semantics>属性的更新时间</semantics>
        </field>
        <field>
          <name>ObjectClassName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>属性所属的对象类名称</semantics>
        </field>
        <field>
          <name>ObjectInstanceHandle</name>
          <dataType>EntityIdentifierStruct</dataType>
          <semantics>属性所属的对象实例</semantics>
        </field>
        <field>
          <name>SubscribeName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>订阅时使用的名字</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>ApplicationInteractionSturct</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>对象属性结构体，用于推送给外部客户端接口</semantics>
        <field>
          <name>InteractionClassName</name>
          <dataType>HLAASCIIstring</dataType>
          <semantics>交互类名称</semantics>
        </field>
        <field>
          <name>InteractionValue</name>
          <dataType>HLAunicodeString</dataType>
          <semantics>交互类结构化后的值</semantics>
        </field>
        <field>
          <name>LogicTime</name>
          <dataType>HLAinteger64BE</dataType>
          <semantics>交互类发出的仿真时间</semantics>
        </field>
        <field>
          <name>originatingEntity</name>
          <dataType>EntityIdentifierStruct</dataType>
          <semantics>交互类的发送方</semantics>
        </field>
        <field>
          <name>ReceiveEntity</name>
          <dataType>EntityIdentifierStruct</dataType>
          <semantics>交互类的接受方</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>SimulationRation</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>仿真frame倍率</semantics>
        <field>
          <name>Ration</name>
          <dataType>HLAinteger16BE</dataType>
          <semantics>frame倍率</semantics>
        </field>
        <field>
          <name>EndTime</name>
          <dataType>HLAinteger64BE</dataType>
          <semantics>交互类结构化后的值</semantics>
        </field>
        <field>
          <name>LogicTime</name>
          <dataType>HLAinteger64BE</dataType>
          <semantics>交互类发出的仿真时间</semantics>
        </field>
        <field>
          <name>originatingEntity</name>
          <dataType>EntityIdentifierStruct</dataType>
          <semantics>交互类的发送方</semantics>
        </field>
        <field>
          <name>ReceiveEntity</name>
          <dataType>EntityIdentifierStruct</dataType>
          <semantics>交互类的接受方</semantics>
        </field>
      </fixedRecordData>
    </fixedRecordDataTypes>
    <variantRecordDataTypes>
      <variantRecordData>
        <name>ApplicationInitParamRecVariantStruct</name>
        <discriminant>Param</discriminant>
        <dataType>MorpheusAppEnum</dataType>
        <encoding>HLAvariantRecord</encoding>
        <semantics>引擎初始化请求</semantics>
        <alternative>
          <enumerator>ReplayService</enumerator>
          <name>ReplayInitParam</name>
          <dataType>ReplayServiceInitParamStruct</dataType>
          <semantics>回放服务初始化参数</semantics>
        </alternative>
      </variantRecordData>
      <variantRecordData>
        <name>MorpheusApplicationPushObjectAttributeStruct</name>
        <discriminant>Param</discriminant>
        <dataType>PushTypeEnum</dataType>
        <encoding>HLAvariantRecord</encoding>
        <semantics>N/A</semantics>
        <alternative>
          <enumerator>Data</enumerator>
          <name>DataContent</name>
          <dataType>ApplicationAttributeSturct</dataType>
          <semantics>N/A</semantics>
        </alternative>
        <alternative>
          <enumerator>Time</enumerator>
          <name>TimeContent</name>
          <dataType>HLAinteger64BE</dataType>
          <semantics>N/A</semantics>
        </alternative>
      </variantRecordData>
      <variantRecordData>
        <name>MorpheusApplicationPushInteractionStruct</name>
        <discriminant>Param</discriminant>
        <dataType>PushTypeEnum</dataType>
        <encoding>HLAvariantRecord</encoding>
        <semantics>N/A</semantics>
        <alternative>
          <enumerator>Data</enumerator>
          <name>DataContent</name>
          <dataType>ApplicationInteractionSturct</dataType>
          <semantics>N/A</semantics>
        </alternative>
        <alternative>
          <enumerator>Time</enumerator>
          <name>TimeContent</name>
          <dataType>HLAinteger64BE</dataType>
          <semantics>N/A</semantics>
        </alternative>
      </variantRecordData>
    </variantRecordDataTypes>
  </dataTypes>
  <notes>
    <note>
      <label>MOM1</label>
      <semantics>The value of the Dimension Upper Bound entry for the Federate dimension is RTI implementation
            dependent.</semantics>
    </note>
  </notes>
</objectModel>