<!--
* @Author: 宋计民
* @Date: 2024/4/23
* @Version: 1.0
* @Content: split-item.vue
-->
<template>
  <div class="split-item" ref="splitItemRef">
    <slot />
    <div ref="splitHandleRef" class="split-item__handle" />
  </div>
</template>

<script setup lang="ts">
import { useResizableHook, useZIndex } from 'isim-ui';
import { useSplitInjectHook } from './use-split-hook.ts';
const { currentZIndex } = useZIndex();
defineOptions({
  name: 'SimSplitItem'
});
const props = defineProps({
  min: {
    type: String,
    default: '50px'
  },
  handleWidth: {
    type: String,
    default: '4px'
  }
});
const splitHandleRef = ref<HTMLDivElement>();
const splitItemRef = ref<HTMLDivElement>();
const { splitRef } = useSplitInjectHook();
useResizableHook(splitHandleRef, {
  resizeDom: splitItemRef,
  eventDom: splitRef,
  moveCallback({ event, resizeRect }) {
    const { clientX } = event;
    const width = clientX - resizeRect.left;
    splitItemRef.value.style.width = `${width}px`;
  }
});
</script>

<style scoped lang="less">
.split-item {
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 100%;
  min-width: v-bind('props.min');
  --handle-width: v-bind('props.handleWidth');
  &__handle {
    position: absolute;
    user-select: none;
    right: calc(var(--handle-width) / 2 * -1);
    top: 0;
    height: 100%;
    width: var(--handle-width);
    z-index: v-bind(currentZIndex);
    cursor: col-resize;
  }
}
</style>
