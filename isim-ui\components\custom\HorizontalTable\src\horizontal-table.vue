<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-18 10:35
* @Version : 1.0
* @Content : 水平表格
-->
<template>
  <div class="command-info">
    <div v-for="item in data" :key="item.ParamCode" class="command-info__item">
      <div>
        <span>{{ item.ParamDesc }}</span>
      </div>
      <div>
        <span v-if="mode === 'read'">{{ item.ParamValue }}</span>
        <sim-input v-else v-model="item.ParamValue" class="command-info__input" @change="handleChange(item)" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup generic="T">
import { PropType } from 'vue';
defineOptions({
  name: 'HorizontalTable'
});
defineProps({
  data: {
    type: Array as PropType<{ ParamDesc: string; ParamCode: string; ParamValue: string }[]>,
    default: () => []
  },
  mode: {
    type: String as PropType<'edit' | 'read'>,
    default: 'read'
  }
});
const handleChange = (data: any) => {
  console.log(data);
};
</script>

<style scoped lang="less"></style>
