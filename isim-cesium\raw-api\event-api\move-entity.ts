/**
 * @Author: 宋计民
 * @Date: 2023/4/12
 * @Version: 1.0
 * @Content:
 */
import { getEntityConstantPosition, onLeftDown, onLeftUp, onMouseMove, onViewerCreated, PositionedEvent, screenPosToCartesian } from 'isim-cesium';
import { Entity } from 'cesium';

interface MoveEntityCallbackParams {
  entity: Entity;
  position: PositionedEvent;
}
type MoveEntityCallback = (params: MoveEntityCallbackParams) => void;

interface MoveEntityOptions {
  condition: (entity: Entity) => boolean;
}
/**
 *
 * @param callback
 * @param options 可以移动的实体的sourceName集合
 */
export function onMoveEntity(callback: MoveEntityCallback, options?: MoveEntityOptions) {
  const closeEvent = new Set<Function>();
  const _condition = options?.condition;
  onViewerCreated(() => {
    // 绑定LeftDown事件
    const downClose = onLeftDown(
      ({ entity: en }) => {
        const condition = _condition ? _condition(en) : false;
        if (!condition) {
          return;
        }
        // 绑定mouseMove事件 实时获取坐标
        const moveClose = onMouseMove(({ position: pos }) => {
          const cartesian = screenPosToCartesian(pos.endPosition);
          if (cartesian) {
            getEntityConstantPosition(en)?.setValue(cartesian);
          }
        });
        closeEvent.add(moveClose);

        // 绑定LeftUp事件 清楚move事件
        const upClose = onLeftUp(({ position }) => {
          moveClose(); // 清楚mouseMove事件
          callback?.({ entity: en, position }); // 执行拖拽实体结束事件
          upClose(); // 清楚leftUp事件
        });
        closeEvent.add(upClose);
      },
      {
        isEntity: true
      }
    );
    closeEvent.add(downClose);
  });
  return () => {
    closeEvent.forEach((item) => item?.());
    closeEvent.clear();
  };
}
