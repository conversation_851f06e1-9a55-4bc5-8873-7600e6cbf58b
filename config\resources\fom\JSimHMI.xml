<?xml version='1.0' encoding='utf-8'?>
<objectModel 
  xmlns="http://standards.ieee.org/IEEE1516-2010" 
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://standards.ieee.org/IEEE1516-2010 http://standards.ieee.org/downloads/1516/1516.2-2010/IEEE1516-DIF-2010.xsd">
  <modelIdentification>
    <name>JSimHMI</name>
    <type>FOM</type>
    <version>1.0</version>
    <reference>
      <type>Dependency</type>
      <identification>JSimBasic</identification>
    </reference>
  </modelIdentification>
  <objects>
    <objectClass>
      <name>HLAobjectRoot</name>
      <objectClass>
        <name>HMI</name>
        <sharing>Publish</sharing>
        <objectClass>
          <name>ForceHMI</name>
          <sharing>Publish</sharing>
          <attribute>
            <name>HMIObjectList</name>
            <dataType>MorpheusString</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>On Change</updateCondition>
            <ownership>Divest</ownership>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <semantics>N/A</semantics>
          </attribute>
        </objectClass>
      </objectClass>
      <objectClass>
        <name>Assess</name>
        <sharing>Publish</sharing>
        <semantics>评估模型基类</semantics>
        <attribute>
          <name>Name</name>
          <dataType>MorpheusString</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>名称</semantics>
        </attribute>
        <attribute>
          <name>ForceNum</name>
          <dataType>MorpheusString</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>各属方不同平台的数量</semantics>
        </attribute>
        <attribute>
          <name>PlatformDamageInfo</name>
          <dataType>MorpheusString</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>平台战损信息</semantics>
        </attribute>
        <attribute>
          <name>WeaponCountAndDamageInfo</name>
          <dataType>MorpheusString</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>武器发射数量以及战损信息</semantics>
        </attribute>
      </objectClass>
    </objectClass>
  </objects>
  <interactions>
    <interactionClass>
      <name>HLAinteractionRoot</name>
      <interactionClass>
        <name>CommandHMI</name>
        <sharing>PublishSubscribe</sharing>
        <transportation>HLAreliable</transportation>
        <order>Receive</order>
        <interactionClass>
          <name>B10001</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇定速定向机动</semantics>
          <order>Receive</order>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10002</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇计划航线机动</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:返航)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10003</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇区域机动</semantics>
          <order>Receive</order>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位(区域类型为矩形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(0-自定义点位 1-8字型 2-跑道型 3-z字型 4-矩形型)</semantics>
          </parameter>
          <parameter>
            <name>areaType</name>
            <dataType>AreaTypeEnum</dataType>
            <semantics>区域类型(0-矩形 1-圆形)</semantics>
          </parameter>
          <parameter>
            <name>centerPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>中心点位(区域类型为圆形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>radius</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>半径(千米)(区域类型为圆形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(机动方式为自定义点位时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10004</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇伴随机动</semantics>
          <order>Receive</order>
          <parameter>
            <name>followPlatform</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标批号</semantics>
          </parameter>
          <parameter>
            <name>followDistance</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>相对距离（海里）</semantics>
          </parameter>
          <parameter>
            <name>followAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>方位角</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10005</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对空侦察</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10006</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对海侦察</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10007</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对潜搜索</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10008</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对空抗击</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>打击模式</semantics>
          </parameter>
          <parameter>
            <name>pointDefense</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>点防御标识, true为点防御, false为编队协同的面防御(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>salvoes</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>齐射数量, 如1拦1, 2拦1(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>attackAreaAngelStart</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>面防御的开始角度, 正北为0度, 顺时针旋转, 默认0(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>attackAreaAngelEnd</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>面防御的结束角度, 默认360(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id(手动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量(手动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>发射的武器名称（可选参数</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10009</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对海打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetCaptureMode</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(1-自动捕获;2-手动捕获)</semantics>
            <parentLabel>B10009</parentLabel>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
            <parentLabel>B10009</parentLabel>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(指定目标捕获时所需参数)</semantics>
            <parentLabel>B10009</parentLabel>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
            <parentLabel>B10009</parentLabel>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
            <parentLabel>B10009</parentLabel>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
            <parentLabel>B10009</parentLabel>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
            <parentLabel>B10009</parentLabel>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10010</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对陆攻击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10011</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对潜攻击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10012</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇红外防护</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>自动Auto,手动模式Manual</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>发射方位(手动模式参数，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10013</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇箔条防护</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>自动Auto,手动模式Manual</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>发射方位(手动模式参数，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10014</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇雷达干扰</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>机动路径(可选)</semantics>
          </parameter>
          <parameter>
            <name>trackId</name>
            <dataType>MorpheusString</dataType>
            <semantics>情报ID</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
          <parameter>
            <name>techniqueId</name>
            <dataType>TechniqueEnum</dataType>
            <semantics>干扰类型</semantics>
          </parameter>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰设备（可选）</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10016</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇布雷</semantics>
          <order>Receive</order>
          <parameter>
            <name>mineName</name>
            <dataType>MorpheusString</dataType>
            <semantics>水雷名称</semantics>
          </parameter>
          <parameter>
            <name>mineNum</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>水雷数量</semantics>
          </parameter>
          <parameter>
            <name>rowNum</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>排数</semantics>
          </parameter>
          <parameter>
            <name>mineSpacing</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>雷场间距</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10017</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇扫雷</semantics>
          <order>Receive</order>
          <parameter>
            <name>sweepSpeed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>扫雷速度</semantics>
          </parameter>
          <parameter>
            <name>startLocation</name>
            <dataType>LocationLLA</dataType>
            <semantics>扫雷开始点</semantics>
          </parameter>
          <parameter>
            <name>stopLocation</name>
            <dataType>LocationLLA</dataType>
            <semantics>扫雷结束点</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10018</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇装载</semantics>
          <order>Receive</order>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被装载单元ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10019</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇卸载</semantics>
          <order>Receive</order>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被卸载单元ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10020</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇补给</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>被补给者的实体ID</semantics>
          </parameter>
          <parameter>
            <name>materialType</name>
            <dataType>MaterialTypeEnum</dataType>
            <semantics>补给物资类型(1：武器;2:燃料)</semantics>
          </parameter>
          <parameter>
            <name>materialName</name>
            <dataType>MorpheusString</dataType>
            <semantics>补给物资名称(补给武器时需要)</semantics>
          </parameter>
          <parameter>
            <name>supplyCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>补给物资数量</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>机动路线</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10021</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇修理</semantics>
          <order>Receive</order>
          <parameter>
            <name>repairType</name>
            <dataType>RepairTypeEnum</dataType>
            <semantics>修理类型</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10022</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇拖带</semantics>
          <order>Receive</order>
          <parameter>
            <name>tractionId</name>
            <dataType>MorpheusString</dataType>
            <semantics>被拖带平ID</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>机动路线</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10023</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇进港</semantics>
          <order>Receive</order>
          <parameter>
            <name>portId</name>
            <dataType>MorpheusString</dataType>
            <semantics>海港ID</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>机动路线</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10024</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇出港</semantics>
          <order>Receive</order>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>机动路线</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10025</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>水面舰艇指挥权变更</semantics>
          <parameter>
            <name>exchangeRequest</name>
            <dataType>ExchangeRequestEnum</dataType>
            <semantics>请求类型</semantics>
          </parameter>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求指挥权变更平台</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10026</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵定速定向</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10027</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵计划航线机动</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:返航)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10028</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵区域机动</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位(区域类型为矩形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(0-自定义点位 1-8字型 2-跑道型 3-z字型 4-矩形型)</semantics>
          </parameter>
          <parameter>
            <name>areaType</name>
            <dataType>AreaTypeEnum</dataType>
            <semantics>区域类型(0-矩形 1-圆形)</semantics>
          </parameter>
          <parameter>
            <name>centerPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>中心点位(区域类型为圆形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>radius</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>半径(千米)(区域类型为圆形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(机动方式为自定义点位时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10029</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵伴随机动</semantics>
          <parameter>
            <name>followPlatform</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标批号</semantics>
          </parameter>
          <parameter>
            <name>followDistance</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>相对距离(m)</semantics>
          </parameter>
          <parameter>
            <name>followAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>方位角(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10030</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对空侦察</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10031</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对海侦察</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10032</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对潜搜索</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10033</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵空战</semantics>
          <parameter>
            <name>airWarfareTargetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>航空兵空战目标id</semantics>
          </parameter>
          <parameter>
            <name>airWarfareTactics</name>
            <dataType>MorpheusString</dataType>
            <semantics>航空兵空战策略</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10034</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵拦截</semantics>
          <parameter>
            <name>airWarfareTargetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>航空兵拦截目标id</semantics>
          </parameter>
          <parameter>
            <name>airInterceptTactics</name>
            <dataType>MorpheusString</dataType>
            <semantics>航空兵拦截策略</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10035</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对舰攻击</semantics>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标id</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetCaptureMode</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(1-自动 2-手动)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划航线(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10036</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对陆攻击</semantics>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标id</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划航线(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10037</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对潜攻击</semantics>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标id</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划航线(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10042</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵布雷</semantics>
          <parameter>
            <name>mineName</name>
            <dataType>MorpheusString</dataType>
            <semantics>水雷名称</semantics>
          </parameter>
          <parameter>
            <name>mineNum</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>水雷数量</semantics>
          </parameter>
          <parameter>
            <name>rowNum</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>排数</semantics>
          </parameter>
          <parameter>
            <name>mineSpacing</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>雷场间距</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10043</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵起飞</semantics>
          <parameter>
            <name>launchAircraftId</name>
            <dataType>MorpheusString</dataType>
            <semantics>起飞飞机id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10044</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵降落</semantics>
          <parameter>
            <name>landPlatformId</name>
            <dataType>MorpheusString</dataType>
            <semantics>降落的目标平台/机场ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10047</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵转场</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>转场速度</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>转场的目标平台/机场ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10048</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵空中加油</semantics>
          <parameter>
            <name>refuelPlatform</name>
            <dataType>MorpheusString</dataType>
            <semantics>被加油平台ID</semantics>
          </parameter>
          <parameter>
            <name>refuelDistance</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>加油点距离(nmi)</semantics>
          </parameter>
          <parameter>
            <name>followDistance</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>伴飞距离(m)</semantics>
          </parameter>
          <parameter>
            <name>followAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>伴飞角度(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10049</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵装载</semantics>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被装载单元ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10050</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵卸载</semantics>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被卸载单元ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10051</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵空投</semantics>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被空投单元ID</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoInts</name>
            <dataType>Waypoints</dataType>
            <semantics>空投区域点位</semantics>
          </parameter>
          <parameter>
            <name>returnMode</name>
            <dataType>ReturnModeEnum</dataType>
            <semantics>返航方式(1-返航 2-指定点位返航)</semantics>
          </parameter>
          <parameter>
            <name>returnRoute</name>
            <dataType>Waypoints</dataType>
            <semantics>指定点位返航自定义的点位路线(非必填)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10052</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵指挥权变更</semantics>
          <parameter>
            <name>exchangeRequest</name>
            <dataType>ExchangeRequestEnum</dataType>
            <semantics>请求类型</semantics>
          </parameter>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求指挥权变更平台</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10039</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵雷达干扰</semantics>
          <parameter>
            <name>Route</name>
            <dataType>Waypoints</dataType>
            <semantics>机动路径(可选)</semantics>
          </parameter>
          <parameter>
            <name>TrackId</name>
            <dataType>MorpheusString</dataType>
            <semantics>情报ID(干扰对象)</semantics>
          </parameter>
          <parameter>
            <name>TechniqueId</name>
            <dataType>TechniqueEnum</dataType>
            <semantics>干扰类型</semantics>
          </parameter>
          <parameter>
            <name>DeviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰设备(可选)</semantics>
          </parameter>
          <parameter>
            <name>StartTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰开始时间</semantics>
          </parameter>
          <parameter>
            <name>Duration</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰持续时间(分钟)``</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10041</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵发射诱饵弹</semantics>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>攻击模式(自动-Auto 手动-Manual)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>发射方位(手动模式参数)(度，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10038</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵红外防护</semantics>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>攻击模式(自动-Auto 手动-Manual)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>发射方位(手动模式参数)(度，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10110</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵部署箔条走廊</semantics>
          <parameter>
            <name>deploymentLength</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>部署长度(m)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10053</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇定速定向</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10054</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇计划航线机动</semantics>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:返航)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10055</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇区域机动</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位(区域类型为矩形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(0-自定义点位 1-8字型 2-跑道型 3-z字型 4-矩形型)</semantics>
          </parameter>
          <parameter>
            <name>areaType</name>
            <dataType>AreaTypeEnum</dataType>
            <semantics>区域类型(0-矩形 1-圆形)</semantics>
          </parameter>
          <parameter>
            <name>centerPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>中心点位(区域类型为圆形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>radius</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>半径(千米)(区域类型为圆形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(机动方式为自定义点位时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10056</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇对潜搜索</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10057</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇对海侦察</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10058</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇对海打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetCaptureMode</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(1-自动捕获;2-手动捕获)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(指定目标捕获时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10059</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇对地打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10060</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇对潜打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10062</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇充电</semantics>
          <order>Receive</order>
          <parameter>
            <name>duration</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>充电时长(分钟)</semantics>
          </parameter>
          <parameter>
            <name>chargeType</name>
            <dataType>MorpheusString</dataType>
            <semantics>充电方式</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10063</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇投放</semantics>
          <order>Receive</order>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被投放潜艇id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10064</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇进港</semantics>
          <order>Receive</order>
          <parameter>
            <name>portId</name>
            <dataType>MorpheusString</dataType>
            <semantics>海港ID</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>机动路线</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10065</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇出港</semantics>
          <order>Receive</order>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>机动路线</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
        </interactionClass>
      </interactionClass>
    </interactionClass>
  </interactions>
  <dimensions />
  <time>
    <timeStamp>
      <dataType>HLAinteger64Time</dataType>
      <semantics>Number of microseconds since the Epoch. The default Epoch is 00:00 Jan 1, 1970 in the UTC+0 time
                zone, but other Epochs may be agreed by a federation. Leap days are included in the calculation, but
                leap seconds are not.</semantics>
    </timeStamp>
    <lookahead>
      <dataType>HLAinteger64Time</dataType>
      <semantics>Number or microseconds.</semantics>
    </lookahead>
  </time>
  <tags>
    <updateReflectTag>
      <dataType>RPRUserDefinedTag</dataType>
      <semantics>User-supplied tag provided with each update/reflect of object instance attribute values. Contains
                (at least) the time at which the provided data is valid.</semantics>
    </updateReflectTag>
    <sendReceiveTag>
      <dataType>RPRUserDefinedTag</dataType>
      <semantics>User-supplied tag provided with each send/receive of an interaction. Contains (at least) the time
                at which the provided data is valid.</semantics>
    </sendReceiveTag>
    <deleteRemoveTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </deleteRemoveTag>
    <divestitureRequestTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </divestitureRequestTag>
    <divestitureCompletionTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </divestitureCompletionTag>
    <acquisitionRequestTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </acquisitionRequestTag>
    <requestUpdateTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </requestUpdateTag>
  </tags>
  <synchronizations>
    <synchronizationPoint>
      <label>RPRobjectsRegistered</label>
      <dataType>NA</dataType>
      <capability>NA</capability>
      <semantics>Indicates that all object instances have been registered.</semantics>
    </synchronizationPoint>
    <synchronizationPoint>
      <label>RPRattributesUpdated</label>
      <dataType>NA</dataType>
      <capability>NA</capability>
      <semantics>Indicates that all required updates with initial values have been provided for all registered
                instances.</semantics>
    </synchronizationPoint>
  </synchronizations>
  <transportations />
  <updateRates />
  <dataTypes>
    <basicDataRepresentations />
    <simpleDataTypes />
    <enumeratedDataTypes>
      <enumeratedData>
        <name>ArrivedActionEnum</name>
        <representation>HLAoctet</representation>
        <semantics>机动完成后行为枚举</semantics>
        <enumerator>
          <name>KEEP</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>STOP</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>RETURN2BASE</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>ManeuverModeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>机动模式枚举</semantics>
        <enumerator>
          <name>Custom</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>EightShaped</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>RunwayType</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>ZShaped</name>
          <value>3</value>
        </enumerator>
        <enumerator>
          <name>Rectangle</name>
          <value>4</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>AreaTypeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>区域类型枚举</semantics>
        <enumerator>
          <name>Rectangle</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Circular</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>AttackModeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>攻击模式枚举</semantics>
        <enumerator>
          <name>Auto</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Manual</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>TargetCaptureModeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>目标捕获模式枚举</semantics>
        <enumerator>
          <name>AutoCapture</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>ManualCapture</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>MaterialTypeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>补给类型枚举</semantics>
        <enumerator>
          <name>Weapon</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Fuel</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>RepairTypeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>修理类型枚举</semantics>
        <enumerator>
          <name>Fire</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Flooding</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>ReturnModeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>返航方式枚举</semantics>
        <enumerator>
          <name>FH</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>ZDDWFH</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>ExchangeRequestEnum</name>
        <representation>HLAoctet</representation>
        <semantics>变更指挥请求类型枚举</semantics>
        <enumerator>
          <name>BecomeSubordinate</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>BecomeCommander</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>TechniqueEnum</name>
        <representation>HLAoctet</representation>
        <semantics>干扰类型枚举</semantics>
        <enumerator>
          <name>YZGR</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>QPGR</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
    </enumeratedDataTypes>
    <arrayDataTypes>
      <arrayData>
        <name>UnitIds</name>
        <dataType>MorpheusString</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>N/A</semantics>
      </arrayData>
      <arrayData>
        <name>WaypointArray</name>
        <dataType>Waypoint</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>N/A</semantics>
      </arrayData>
    </arrayDataTypes>
    <fixedRecordDataTypes>
      <fixedRecordData>
        <name>HMIObject</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>N/A</semantics>
      </fixedRecordData>
      <fixedRecordData>
        <name>Waypoint</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>N/A</semantics>
        <field>
          <name>Latitude</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>纬度</semantics>
        </field>
        <field>
          <name>Longitude</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>经度</semantics>
        </field>
        <field>
          <name>Altitude</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>高度</semantics>
        </field>
        <field>
          <name>Speed</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>速度</semantics>
        </field>
        <field>
          <name>StayTime</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>停留时间</semantics>
        </field>
      </fixedRecordData>
    </fixedRecordDataTypes>
    <variantRecordDataTypes />
  </dataTypes>
  <notes />
</objectModel>