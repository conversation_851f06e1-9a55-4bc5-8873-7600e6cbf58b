<!--
* @Author: 宋计民
* @Date: 2024/4/24
* @Version: 1.0
* @Content: gantt-timeline.vue
-->
<template>
  <div v-for="(item, i) in timeRange" :key="item.timeSecond" :style="{ backgroundColor: i == currentMoveIndex ? 'red' : '' }">
    第{{ i }}个：{{ item.time }}
  </div>
  <div class="sim-gantt-timeline" ref="timelineRef" @wheel.prevent="mouseWheelEvent">
    <span
      class="sim-gantt-timeline__text"
      v-for="(item, i) in timeRange"
      :key="item.timeSecond"
      :style="{ left: `${getLeftOffset(i)}%`, width: `${interval}%` }"
      @mouseover="mousemove(i)"
    >
      {{ item.time }}
    </span>
    <div
      class="sim-gantt-timeline__tick sim-gantt-timeline__long"
      :style="{ width: `${interval * timeNumber}%`, left: `${getLeftOffset(0)}%` }"
    ></div>
    <div
      class="sim-gantt-timeline__tick sim-gantt-timeline__short"
      :style="{ width: `${interval * timeNumber}%`, left: `${getLeftOffset(0)}%` }"
    ></div>
    <!-- 滑块 -->
    <div class="sim-gantt-timeline__slider" :style="{ left: `${sliderLeft}%` }" @mousedown="mouseDown"></div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { useSlider } from './hooks/slider-hook.ts';

defineOptions({
  name: 'SimGanttTimelineNew'
});

const props = defineProps({
  // 最小时间间隔
  minInterval: {
    type: Number,
    default: 1000
  }
});

const emits = defineEmits(['change']);

const currentTime = defineModel<number>('currentTime', { required: true, default: Date.now() });
const startTime = defineModel<number>('startTime', { required: true, default: Date.now() });
const endTime = defineModel<number>('endTime', { required: true });
let currentStartTime = ref(startTime.value);
let currentEndTime = ref(endTime.value);

const widthRatio = 100; //最大宽度百分比
const timeNumber = 7; //时间段数量
let intervalMaxStep = 1;
let intervalMinStep = 0;
let intervalStep = intervalMinStep; //控制时间段宽度每次新增或减少的步长
const getIntervalStep = (step: number) => widthRatio / (timeNumber - step);
// 时间段最小宽度
const minWidth = getIntervalStep(intervalMinStep);
// 时间段最大宽度
const maxWidth = getIntervalStep(intervalMaxStep);
const interval = ref(minWidth); //时间段宽度

let currentMoveIndex = 0; //当前选中时间段

let step: number; //时间间隔秒数。最小为1秒
let maxStep: number; //最大时间间隔

const timelineRef = ref<HTMLDivElement>();
const { sliderLeft, mouseDown, resetSliderLeft } = useSlider(timelineRef, {
  currentTime: currentTime,
  currentStartTime: currentStartTime,
  currentEndTime: currentEndTime
});

const offsetList = ref([]);

const timeRange: Ref<
  {
    timeSecond: number;
    time: string;
  }[]
> = ref([]);

const getLeftOffset = (index: number) => {
  return offsetList.value[index];
};

const setOffsetList = () => {
  for (let i = currentMoveIndex - 1; i >= 0; i--) {
    offsetList.value[i] = offsetList.value[i + 1] - interval.value;
  }
  for (let i = currentMoveIndex + 1; i <= timeNumber; i++) {
    offsetList.value[i] = offsetList.value[i - 1] + interval.value;
  }
  // 防止溢出
  if (offsetList.value[0] > 0) {
    const rightOffset = offsetList.value[0];
    for (let i = 0; i < timeNumber; i++) {
      offsetList.value[i] = offsetList.value[i] - rightOffset;
    }
  }

  const maxLength = widthRatio;
  if (offsetList.value[timeNumber] < maxLength) {
    offsetList.value[timeNumber] = maxLength;
    for (let i = timeNumber - 1; i >= 0; i--) {
      offsetList.value[i] = offsetList.value[i + 1] - interval.value;
    }
  }
  console.log('offsetList.value', offsetList.value);
};
// 待修改：拿到当前两边的刻度
const setCurrentTimeFrame = () => {
  if (!timeRange.value.length) {
    currentStartTime.value = startTime.value;
    currentEndTime.value = endTime.value;
    return;
  }
  // console.log('timeRange.value', timeRange.value);
  // console.log('offsetList.value', offsetList.value);
  const minTime = timeRange.value[0].timeSecond;
  // const maxTime = timeRange.value[timeNumber - 1].timeSecond;
  const lastTime = timeRange.value[timeNumber].timeSecond;
  const currentTimeInterval = (lastTime - minTime) / timeNumber;
  // const widthInterval = offsetList.value[1] - offsetList.value[0];
  const widthInterval = (offsetList.value[timeNumber] - offsetList.value[0]) / timeNumber;

  currentStartTime.value = minTime + (currentTimeInterval * Math.abs(offsetList.value[0])) / widthInterval;
  currentEndTime.value = lastTime - (currentTimeInterval * (offsetList.value[timeNumber] - 100)) / widthInterval;

  // console.log('----2222222222222222222');
  // console.log('currentStartTime.value', currentStartTime.value);
  // console.log('currentEndTime.value', currentEndTime.value);
  // console.log('currentStartTime.value', dayjs(currentStartTime.value).format('YYYY/MM/DD HH:mm:ss'));
  // console.log('currentEndTime.value', dayjs(currentEndTime.value).format('YYYY/MM/DD HH:mm:ss'));

  emits('change', { currentStartTime: currentStartTime.value, currentEndTime: currentEndTime.value });
  resetSliderLeft();
};
watch(
  () => interval.value,
  () => {
    // setOffsetList();
    // setCurrentTimeFrame();
  },
  { immediate: true }
);
// 初始化时间轴
const initTimeRange = () => {
  const arr = [];
  const offsetArr = [];
  const currentTimeInterval = endTime.value - startTime.value;
  step = Math.floor(currentTimeInterval / timeNumber / 1000);
  maxStep = Math.floor(currentTimeInterval / timeNumber / 1000);
  for (let i = 0; i <= timeNumber; i++) {
    const timeSecond = startTime.value + (currentTimeInterval * i) / timeNumber;
    const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
    const offset = i * interval.value;

    arr.push({ timeSecond, time });
    offsetArr.push(offset);
  }
  timeRange.value = arr;
  offsetList.value = offsetArr;
  setCurrentTimeFrame();
};
watch(
  () => [startTime.value, endTime.value, props.minInterval],
  () => {
    interval.value = minWidth;
    initTimeRange();
  },
  { immediate: true }
);
// 更新时间轴的时间数组的值
// --》修改--将上一次的currentStartTime，currentEndTime作为
// timeRange的最大最小值
// const setTimeRange = (addTime: number) => {
//   const oldTimeList = timeRange.value;
//   const oldStartTime = oldTimeList[0].timeSecond;
//   const OLDendTime = oldTimeList[oldTimeList.length - 1].timeSecond;
//   const currentMoveTime = oldTimeList[currentMoveIndex].timeSecond;
//
//   // let currentTimeInterval = (OLDendTime - oldStartTime) / (oldTimeList.length - 1) + addTime;
//   let currentTimeInterval: number;
//   if (setTimeRange <= 0) {
//     currentTimeInterval = (currentEndTime.value - currentStartTime.value) / timeNumber;
//   } else {
//     currentTimeInterval = (currentEndTime.value - currentStartTime.value) / timeNumber + addTime;
//   }
//
//   step = Math.floor(currentTimeInterval / 1000);
//
//   const minInterval = props.minInterval;
//
//   // if (currentTimeInterval <= 0) {
//   //   step = 1;
//   //   currentTimeInterval = 1000;
//   // }
//   if (currentTimeInterval <= minInterval) {
//     step = 1;
//     currentTimeInterval = minInterval;
//   }
//   const arr = [];
//   // 选中节点之前的节点
//   for (let i = 0; i <= currentMoveIndex; i++) {
//     const timeSecond = currentMoveTime - currentTimeInterval * (currentMoveIndex - i);
//     const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
//
//     arr.push({ timeSecond, time });
//   }
//   // 选中节点之后的节点
//   for (let i = currentMoveIndex + 1; i <= timeNumber; i++) {
//     const timeSecond = currentMoveTime + (i - currentMoveIndex) * currentTimeInterval;
//     const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
//
//     arr.push({ timeSecond, time });
//   }
//   // 时间轴溢出
//   const minTime = arr[0].timeSecond;
//   // const maxTime = timeRange.value[timeNumber - 1].timeSecond;
//   const lastTime = arr[timeNumber].timeSecond;
//   if (minTime < startTime.value && lastTime > endTime.value) return initTimeRange();
//
//   if (minTime < startTime.value) {
//     const offsetTime = startTime.value - minTime;
//     for (let i = 0; i < timeNumber; i++) {
//       const timeSecond: number = arr[i].timeSecond + offsetTime;
//       const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
//       arr[i] = { timeSecond, time };
//     }
//   }
//   if (lastTime > endTime.value) {
//     const offsetTime = lastTime - endTime.value;
//     for (let i = 0; i < timeNumber; i++) {
//       const timeSecond: number = arr[i].timeSecond - offsetTime;
//       const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
//       arr[i] = { timeSecond, time };
//     }
//   }
//   console.log('----111111111111111111111');
//   console.log('currentStartTime.value', currentStartTime.value);
//   console.log('currentEndTime.value', currentEndTime.value);
//   console.log('currentStartTime.value', dayjs(currentStartTime.value).format('YYYY/MM/DD HH:mm:ss'));
//   console.log('currentEndTime.value', dayjs(currentEndTime.value).format('YYYY/MM/DD HH:mm:ss'));
//   console.log('oldTimeList', JSON.parse(JSON.stringify(oldTimeList)));
//   console.log('arr', JSON.parse(JSON.stringify(arr)));
//
//   timeRange.value = arr;
// };

// 缩小
const setTimeRangeZoomOut = () => {
  const oldTimeList = timeRange.value;
  const currentMoveTime = oldTimeList[currentMoveIndex].timeSecond;
  let currentTimeInterval = (currentEndTime.value - currentStartTime.value) / timeNumber;

  step = Math.floor(currentTimeInterval / 1000);

  const minInterval = props.minInterval;

  const arr = new Array(timeNumber + 1);
  // 方案一
  if (currentTimeInterval <= minInterval) {
    step = 1;
    currentTimeInterval = minInterval;
    // 选中节点之前的节点
    for (let i = 0; i <= currentMoveIndex; i++) {
      const timeSecond = currentMoveTime - currentTimeInterval * (currentMoveIndex - i);
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');

      arr[i] = { timeSecond, time };
    }
    // 选中节点之后的节点
    for (let i = currentMoveIndex + 1; i <= timeNumber; i++) {
      const timeSecond = currentMoveTime + (i - currentMoveIndex) * currentTimeInterval;
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');

      arr[i] = { timeSecond, time };
    }
  } else {
    arr[0] = { timeSecond: currentStartTime.value, time: dayjs(currentStartTime.value).format('YYYY/MM/DD HH:mm:ss') };
    arr[timeNumber] = { timeSecond: currentEndTime.value, time: dayjs(currentEndTime.value).format('YYYY/MM/DD HH:mm:ss') };
    arr[currentMoveIndex] = { timeSecond: currentMoveTime, time: dayjs(currentMoveTime).format('YYYY/MM/DD HH:mm:ss') };
    const prevTimeInterval = (arr[currentMoveIndex].timeSecond - arr[0].timeSecond) / currentMoveIndex;
    for (let i = 1; i < currentMoveIndex; i++) {
      // const timeSecond = currentMoveTime - currentTimeInterval * (currentMoveIndex - i);
      const timeSecond = arr[0].timeSecond + (i * (arr[currentMoveIndex].timeSecond - arr[0].timeSecond)) / currentMoveIndex;
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
      arr[i] = { timeSecond, time };
    }
    for (let i = currentMoveIndex + 1; i < timeNumber; i++) {
      // const timeSecond = currentMoveTime + (i - currentMoveIndex) * currentTimeInterval;
      const timeSecond =
        currentMoveTime + ((i - currentMoveIndex) * (arr[timeNumber].timeSecond - currentMoveTime)) / (timeNumber - currentMoveIndex);
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');

      arr[i] = { timeSecond, time };
    }
    // console.log('选中--', currentMoveIndex);
    // for (let i = 0; i < timeNumber; i++) {
    //   const diff = arr[i + 1].timeSecond - arr[i].timeSecond;
    //   console.log(`间隔，  ${i}   --  ${diff}`);
    // }
  }

  // 时间轴溢出
  const minTime = arr[0].timeSecond;
  // const maxTime = timeRange.value[timeNumber - 1].timeSecond;
  const lastTime = arr[timeNumber].timeSecond;
  if (minTime < startTime.value && lastTime > endTime.value) return initTimeRange();

  if (minTime < startTime.value) {
    const offsetTime = startTime.value - minTime;
    for (let i = 0; i < timeNumber; i++) {
      const timeSecond: number = arr[i].timeSecond + offsetTime;
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
      arr[i] = { timeSecond, time };
    }
  }
  if (lastTime > endTime.value) {
    const offsetTime = lastTime - endTime.value;
    for (let i = 0; i < timeNumber; i++) {
      const timeSecond: number = arr[i].timeSecond - offsetTime;
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
      arr[i] = { timeSecond, time };
    }
  }
  timeRange.value = arr;
};
// 放大
const setTimeRangeZoomIn = () => {
  const oldTimeList = timeRange.value;
  const oldStartTime = oldTimeList[0].timeSecond;
  const OLDendTime = oldTimeList[oldTimeList.length - 1].timeSecond;
  const currentMoveTime = oldTimeList[currentMoveIndex].timeSecond;

  // let currentTimeInterval = (OLDendTime - oldStartTime) / (oldTimeList.length - 1) + addTime;
  let currentTimeInterval = (currentEndTime.value - currentStartTime.value) / timeNumber;

  step = Math.floor(currentTimeInterval / 1000);

  const minInterval = props.minInterval;

  const arr = new Array(timeNumber + 1);
  // 方案一
  if (currentTimeInterval <= minInterval) {
    step = 1;
    currentTimeInterval = minInterval;
    // 选中节点之前的节点
    for (let i = 0; i <= currentMoveIndex; i++) {
      const timeSecond = currentMoveTime - currentTimeInterval * (currentMoveIndex - i);
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');

      arr[i] = { timeSecond, time };
    }
    // 选中节点之后的节点
    for (let i = currentMoveIndex + 1; i <= timeNumber; i++) {
      const timeSecond = currentMoveTime + (i - currentMoveIndex) * currentTimeInterval;
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');

      arr[i] = { timeSecond, time };
    }
  } else {
    arr[0] = { timeSecond: currentStartTime.value, time: dayjs(currentStartTime.value).format('YYYY/MM/DD HH:mm:ss') };
    arr[timeNumber] = { timeSecond: currentEndTime.value, time: dayjs(currentEndTime.value).format('YYYY/MM/DD HH:mm:ss') };
    arr[currentMoveIndex] = { timeSecond: currentMoveTime, time: dayjs(currentMoveTime).format('YYYY/MM/DD HH:mm:ss') };
    const prevTimeInterval = (arr[currentMoveIndex].timeSecond - arr[0].timeSecond) / currentMoveIndex;
    for (let i = 1; i < currentMoveIndex; i++) {
      // const timeSecond = currentMoveTime - currentTimeInterval * (currentMoveIndex - i);
      const timeSecond = arr[0].timeSecond + (i * (arr[currentMoveIndex].timeSecond - arr[0].timeSecond)) / currentMoveIndex;
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
      arr[i] = { timeSecond, time };
    }
    for (let i = currentMoveIndex + 1; i < timeNumber; i++) {
      // const timeSecond = currentMoveTime + (i - currentMoveIndex) * currentTimeInterval;
      const timeSecond =
        currentMoveTime + ((i - currentMoveIndex) * (arr[timeNumber].timeSecond - currentMoveTime)) / (timeNumber - currentMoveIndex);
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');

      arr[i] = { timeSecond, time };
    }
    // console.log('选中--', currentMoveIndex);
    // for (let i = 0; i < timeNumber; i++) {
    //   const diff = arr[i + 1].timeSecond - arr[i].timeSecond;
    //   console.log(`间隔，  ${i}   --  ${diff}`);
    // }
  }

  // 时间轴溢出
  const minTime = arr[0].timeSecond;
  // const maxTime = timeRange.value[timeNumber - 1].timeSecond;
  const lastTime = arr[timeNumber].timeSecond;
  if (minTime < startTime.value && lastTime > endTime.value) return initTimeRange();

  if (minTime < startTime.value) {
    const offsetTime = startTime.value - minTime;
    for (let i = 0; i < timeNumber; i++) {
      const timeSecond: number = arr[i].timeSecond + offsetTime;
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
      arr[i] = { timeSecond, time };
    }
  }
  if (lastTime > endTime.value) {
    const offsetTime = lastTime - endTime.value;
    for (let i = 0; i < timeNumber; i++) {
      const timeSecond: number = arr[i].timeSecond - offsetTime;
      const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
      arr[i] = { timeSecond, time };
    }
  }
  // console.log('----111111111111111111111');
  // console.log('currentStartTime.value', currentStartTime.value);
  // console.log('currentEndTime.value', currentEndTime.value);
  // console.log('currentStartTime.value', dayjs(currentStartTime.value).format('YYYY/MM/DD HH:mm:ss'));
  // console.log('currentEndTime.value', dayjs(currentEndTime.value).format('YYYY/MM/DD HH:mm:ss'));
  // console.log('oldTimeList', JSON.parse(JSON.stringify(oldTimeList)));
  // console.log('arr', JSON.parse(JSON.stringify(arr)));

  timeRange.value = arr;
};
const mousemove = (i: number) => {
  currentMoveIndex = i;
  // console.log('currentMoveIndex', currentMoveIndex);
};

// 滚动滚轮，放大缩小时间轴
const mouseWheelEvent = (e: WheelEvent) => {
  const dy = e.deltaY;

  // 缩小
  if (dy >= 0) {
    console.log('缩小');
    if (step <= 1) return (interval.value = minWidth);
    if (interval.value >= maxWidth) {
      // setTimeRange(-10000);
      setTimeRangeZoomOut();
      intervalStep = intervalMinStep;
      interval.value = minWidth;
      setOffsetList();
      // setCurrentTimeFrame();
      return;
    }
    intervalStep += 0.1;
    interval.value = getIntervalStep(intervalStep);
    setOffsetList();
    setCurrentTimeFrame();
    return;
  }
  console.log('放大');
  // 放大
  if (step >= maxStep) return initTimeRange();
  if (interval.value <= minWidth) {
    // setTimeRange(10000);
    intervalStep = intervalMaxStep;
    interval.value = maxWidth;
    setOffsetList();
    setTimeRangeZoomIn();
    // setCurrentTimeFrame();
    return;
  }
  intervalStep -= 0.1;
  interval.value = getIntervalStep(intervalStep);
  setOffsetList();
  setCurrentTimeFrame();

  // // 缩小
  // if (dy <= 0) {
  //   console.log('缩小');
  //   if (step <= 1) return (interval.value = minWidth);
  //   if (interval.value <= minWidth) {
  //     setTimeRange(-100000);
  //     intervalStep = intervalMaxStep;
  //     interval.value = maxWidth;
  //     return;
  //   }
  //   intervalStep -= 0.1;
  //   interval.value = getIntervalStep(intervalStep);
  //   return;
  // }
  // console.log('放大');
  // // 放大
  // if (step >= maxStep) return initTimeRange();
  // if (interval.value >= maxWidth) {
  //   setTimeRange(100000);
  //   intervalStep = intervalMinStep;
  //   return (interval.value = minWidth);
  // }
  // intervalStep += 0.1;
  // interval.value = getIntervalStep(intervalStep);
};
// onMounted(() => {
//   timelineRef.value.addEventListener('wheel', mouseWheelEvent);
// });
// onBeforeUnmount(() => {
//   timelineRef.value.removeEventListener('wheel', mouseWheelEvent);
// });
</script>

<style scoped lang="less">
.sim-gantt-timeline {
  --short-range: 10;
  --long-range: v-bind(minWidth);

  position: relative;
  //overflow-x: hidden;
  height: 30px;
  font-size: 12px;
  background-color: skyblue;
  box-sizing: border-box;
  &__text {
    position: absolute;
    left: 0;
    bottom: 0px;
    height: 100%;
    //border: 1px solid red;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &__tick {
    position: absolute;
    left: 0;
    bottom: 3px;
    width: 100%;
    background-repeat: repeat-x;
  }

  &__long {
    position: absolute;
    bottom: 3px;
    left: 0;
    background-repeat: repeat-x;
    background-image: linear-gradient(90deg, fade(white, 50%) 1px, transparent 1px, transparent 100%);
    //background-image: linear-gradient(90deg, fade(blue, 50%) 1px, transparent 5px, transparent 100%);
    height: 10px;
    //border: 1px solid blue;
    background-size: calc(var(--long-range) * 1%) 100%;
    pointer-events: none;
  }
  &__short {
    position: absolute;
    left: 0;
    bottom: 3px;
    background-image: linear-gradient(90deg, fade(white, 50%) 1px, transparent 1px, transparent 100%);
    height: 5px;
    background-size: calc(var(--long-range) / 10 * 1%) 100%;
    pointer-events: none;
    //border: 1px solid #000;
  }
  &__slider {
    position: absolute;
    left: -10px;
    bottom: 3px;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    background-color: red;
    border-radius: 50%;
    z-index: 999;
  }
}
</style>
