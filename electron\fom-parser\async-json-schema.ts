/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
// @ts-nocheck
import axios from 'axios';
import fs from 'node:fs';
import fsPromise from 'node:fs/promises';
import { getConfig } from '../get-config.ts';

const config = getConfig();

async function requestApi(clazzName: string) {
  const res = await axios.post(
    `${config.httpProxy}:6001/fom/fomType/object/getJSONSchema`,
    { clazzName, interaction: true },
    {
      headers: {
        token:
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjEzMjIzMDA3ODY1LCJ1c2VySWQiOiJVMjAyMzA4MTUxOTAwMDAwMSJ9.Yau3Ake8RbbVr5lUJ7tOUVeqmOGhJTbFvTFTvXEG8O8'
      }
    }
  );
  if (res.data.code === '0000') {
    const data = res.data.data as any[];
    const result: Record<string, any> = {};
    const order: string[] = [];
    data
      .filter((item) => !item.parentProperty) //过滤掉父级的属性
      .forEach((item) => {
        const type = item.jsonSchema.type;
        const schemaObj = {
          title: item.semantic,
          ...item.jsonSchema
        };
        /**
         * 处理时间组件
         */
        if (['startTime', 'endTime', 'SimTime'].includes(item.name)) {
          schemaObj.format = 'date-time';
          schemaObj['ui:options'] = {
            clearable: false
          };
        }
        if (type === 'array') {
          schemaObj.default = [];
        }
        result[item.name] = schemaObj;
        order.push(item.name);
      });

    const resultData = {
      'ui:order': order,
      properties: result
    };
    writeFile(clazzName, resultData);
  }
}
const file = fs.readFileSync('./interaction.json', { encoding: 'utf-8' });
const result = JSON.parse(file);
Object.values(result)
  .filter((item) => item.metaData.parent === 'CommandHMI')
  .forEach((item) => {
    requestApi(item.name);
  });
const dirPath = 'interaction-schema';
function writeFile(clazzName: string, value: any) {
  if (!fs.existsSync(`./${dirPath}`)) {
    fs.mkdirSync(`./${dirPath}`);
  }
  fsPromise.writeFile(`./${dirPath}/${clazzName}.json`, JSON.stringify(value, null, 2));
}
