{"version": 3, "file": "upsampleQuantizedTerrainMesh.js", "sources": ["../../../../Source/Core/Intersections2D.js", "../../../../Source/WorkersES6/upsampleQuantizedTerrainMesh.js"], "sourcesContent": ["import Cartesian2 from \"./Cartesian2.js\";\nimport Cartesian3 from \"./Cartesian3.js\";\nimport Check from \"./Check.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\n\n/**\n * Contains functions for operating on 2D triangles.\n *\n * @namespace Intersections2D\n */\nconst Intersections2D = {};\n\n/**\n * Splits a 2D triangle at given axis-aligned threshold value and returns the resulting\n * polygon on a given side of the threshold.  The resulting polygon may have 0, 1, 2,\n * 3, or 4 vertices.\n *\n * @param {Number} threshold The threshold coordinate value at which to clip the triangle.\n * @param {<PERSON><PERSON>an} keepAbove true to keep the portion of the triangle above the threshold, or false\n *                            to keep the portion below.\n * @param {Number} u0 The coordinate of the first vertex in the triangle, in counter-clockwise order.\n * @param {Number} u1 The coordinate of the second vertex in the triangle, in counter-clockwise order.\n * @param {Number} u2 The coordinate of the third vertex in the triangle, in counter-clockwise order.\n * @param {Number[]} [result] The array into which to copy the result.  If this parameter is not supplied,\n *                            a new array is constructed and returned.\n * @returns {Number[]} The polygon that results after the clip, specified as a list of\n *                     vertices.  The vertices are specified in counter-clockwise order.\n *                     Each vertex is either an index from the existing list (identified as\n *                     a 0, 1, or 2) or -1 indicating a new vertex not in the original triangle.\n *                     For new vertices, the -1 is followed by three additional numbers: the\n *                     index of each of the two original vertices forming the line segment that\n *                     the new vertex lies on, and the fraction of the distance from the first\n *                     vertex to the second one.\n *\n * @example\n * const result = Cesium.Intersections2D.clipTriangleAtAxisAlignedThreshold(0.5, false, 0.2, 0.6, 0.4);\n * // result === [2, 0, -1, 1, 0, 0.25, -1, 1, 2, 0.5]\n */\nIntersections2D.clipTriangleAtAxisAlignedThreshold = function (\n  threshold,\n  keepAbove,\n  u0,\n  u1,\n  u2,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(threshold)) {\n    throw new DeveloperError(\"threshold is required.\");\n  }\n  if (!defined(keepAbove)) {\n    throw new DeveloperError(\"keepAbove is required.\");\n  }\n  if (!defined(u0)) {\n    throw new DeveloperError(\"u0 is required.\");\n  }\n  if (!defined(u1)) {\n    throw new DeveloperError(\"u1 is required.\");\n  }\n  if (!defined(u2)) {\n    throw new DeveloperError(\"u2 is required.\");\n  }\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = [];\n  } else {\n    result.length = 0;\n  }\n\n  let u0Behind;\n  let u1Behind;\n  let u2Behind;\n  if (keepAbove) {\n    u0Behind = u0 < threshold;\n    u1Behind = u1 < threshold;\n    u2Behind = u2 < threshold;\n  } else {\n    u0Behind = u0 > threshold;\n    u1Behind = u1 > threshold;\n    u2Behind = u2 > threshold;\n  }\n\n  const numBehind = u0Behind + u1Behind + u2Behind;\n\n  let u01Ratio;\n  let u02Ratio;\n  let u12Ratio;\n  let u10Ratio;\n  let u20Ratio;\n  let u21Ratio;\n\n  if (numBehind === 1) {\n    if (u0Behind) {\n      u01Ratio = (threshold - u0) / (u1 - u0);\n      u02Ratio = (threshold - u0) / (u2 - u0);\n\n      result.push(1);\n\n      result.push(2);\n\n      if (u02Ratio !== 1.0) {\n        result.push(-1);\n        result.push(0);\n        result.push(2);\n        result.push(u02Ratio);\n      }\n\n      if (u01Ratio !== 1.0) {\n        result.push(-1);\n        result.push(0);\n        result.push(1);\n        result.push(u01Ratio);\n      }\n    } else if (u1Behind) {\n      u12Ratio = (threshold - u1) / (u2 - u1);\n      u10Ratio = (threshold - u1) / (u0 - u1);\n\n      result.push(2);\n\n      result.push(0);\n\n      if (u10Ratio !== 1.0) {\n        result.push(-1);\n        result.push(1);\n        result.push(0);\n        result.push(u10Ratio);\n      }\n\n      if (u12Ratio !== 1.0) {\n        result.push(-1);\n        result.push(1);\n        result.push(2);\n        result.push(u12Ratio);\n      }\n    } else if (u2Behind) {\n      u20Ratio = (threshold - u2) / (u0 - u2);\n      u21Ratio = (threshold - u2) / (u1 - u2);\n\n      result.push(0);\n\n      result.push(1);\n\n      if (u21Ratio !== 1.0) {\n        result.push(-1);\n        result.push(2);\n        result.push(1);\n        result.push(u21Ratio);\n      }\n\n      if (u20Ratio !== 1.0) {\n        result.push(-1);\n        result.push(2);\n        result.push(0);\n        result.push(u20Ratio);\n      }\n    }\n  } else if (numBehind === 2) {\n    if (!u0Behind && u0 !== threshold) {\n      u10Ratio = (threshold - u1) / (u0 - u1);\n      u20Ratio = (threshold - u2) / (u0 - u2);\n\n      result.push(0);\n\n      result.push(-1);\n      result.push(1);\n      result.push(0);\n      result.push(u10Ratio);\n\n      result.push(-1);\n      result.push(2);\n      result.push(0);\n      result.push(u20Ratio);\n    } else if (!u1Behind && u1 !== threshold) {\n      u21Ratio = (threshold - u2) / (u1 - u2);\n      u01Ratio = (threshold - u0) / (u1 - u0);\n\n      result.push(1);\n\n      result.push(-1);\n      result.push(2);\n      result.push(1);\n      result.push(u21Ratio);\n\n      result.push(-1);\n      result.push(0);\n      result.push(1);\n      result.push(u01Ratio);\n    } else if (!u2Behind && u2 !== threshold) {\n      u02Ratio = (threshold - u0) / (u2 - u0);\n      u12Ratio = (threshold - u1) / (u2 - u1);\n\n      result.push(2);\n\n      result.push(-1);\n      result.push(0);\n      result.push(2);\n      result.push(u02Ratio);\n\n      result.push(-1);\n      result.push(1);\n      result.push(2);\n      result.push(u12Ratio);\n    }\n  } else if (numBehind !== 3) {\n    // Completely in front of threshold\n    result.push(0);\n    result.push(1);\n    result.push(2);\n  }\n  // else Completely behind threshold\n\n  return result;\n};\n\n/**\n * Compute the barycentric coordinates of a 2D position within a 2D triangle.\n *\n * @param {Number} x The x coordinate of the position for which to find the barycentric coordinates.\n * @param {Number} y The y coordinate of the position for which to find the barycentric coordinates.\n * @param {Number} x1 The x coordinate of the triangle's first vertex.\n * @param {Number} y1 The y coordinate of the triangle's first vertex.\n * @param {Number} x2 The x coordinate of the triangle's second vertex.\n * @param {Number} y2 The y coordinate of the triangle's second vertex.\n * @param {Number} x3 The x coordinate of the triangle's third vertex.\n * @param {Number} y3 The y coordinate of the triangle's third vertex.\n * @param {Cartesian3} [result] The instance into to which to copy the result.  If this parameter\n *                     is undefined, a new instance is created and returned.\n * @returns {Cartesian3} The barycentric coordinates of the position within the triangle.\n *\n * @example\n * const result = Cesium.Intersections2D.computeBarycentricCoordinates(0.0, 0.0, 0.0, 1.0, -1, -0.5, 1, -0.5);\n * // result === new Cesium.Cartesian3(1.0 / 3.0, 1.0 / 3.0, 1.0 / 3.0);\n */\nIntersections2D.computeBarycentricCoordinates = function (\n  x,\n  y,\n  x1,\n  y1,\n  x2,\n  y2,\n  x3,\n  y3,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(x)) {\n    throw new DeveloperError(\"x is required.\");\n  }\n  if (!defined(y)) {\n    throw new DeveloperError(\"y is required.\");\n  }\n  if (!defined(x1)) {\n    throw new DeveloperError(\"x1 is required.\");\n  }\n  if (!defined(y1)) {\n    throw new DeveloperError(\"y1 is required.\");\n  }\n  if (!defined(x2)) {\n    throw new DeveloperError(\"x2 is required.\");\n  }\n  if (!defined(y2)) {\n    throw new DeveloperError(\"y2 is required.\");\n  }\n  if (!defined(x3)) {\n    throw new DeveloperError(\"x3 is required.\");\n  }\n  if (!defined(y3)) {\n    throw new DeveloperError(\"y3 is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const x1mx3 = x1 - x3;\n  const x3mx2 = x3 - x2;\n  const y2my3 = y2 - y3;\n  const y1my3 = y1 - y3;\n  const inverseDeterminant = 1.0 / (y2my3 * x1mx3 + x3mx2 * y1my3);\n  const ymy3 = y - y3;\n  const xmx3 = x - x3;\n  const l1 = (y2my3 * xmx3 + x3mx2 * ymy3) * inverseDeterminant;\n  const l2 = (-y1my3 * xmx3 + x1mx3 * ymy3) * inverseDeterminant;\n  const l3 = 1.0 - l1 - l2;\n\n  if (defined(result)) {\n    result.x = l1;\n    result.y = l2;\n    result.z = l3;\n    return result;\n  }\n  return new Cartesian3(l1, l2, l3);\n};\n\n/**\n * Compute the intersection between 2 line segments\n *\n * @param {Number} x00 The x coordinate of the first line's first vertex.\n * @param {Number} y00 The y coordinate of the first line's first vertex.\n * @param {Number} x01 The x coordinate of the first line's second vertex.\n * @param {Number} y01 The y coordinate of the first line's second vertex.\n * @param {Number} x10 The x coordinate of the second line's first vertex.\n * @param {Number} y10 The y coordinate of the second line's first vertex.\n * @param {Number} x11 The x coordinate of the second line's second vertex.\n * @param {Number} y11 The y coordinate of the second line's second vertex.\n * @param {Cartesian2} [result] The instance into to which to copy the result. If this parameter\n *                     is undefined, a new instance is created and returned.\n * @returns {Cartesian2} The intersection point, undefined if there is no intersection point or lines are coincident.\n *\n * @example\n * const result = Cesium.Intersections2D.computeLineSegmentLineSegmentIntersection(0.0, 0.0, 0.0, 2.0, -1, 1, 1, 1);\n * // result === new Cesium.Cartesian2(0.0, 1.0);\n */\nIntersections2D.computeLineSegmentLineSegmentIntersection = function (\n  x00,\n  y00,\n  x01,\n  y01,\n  x10,\n  y10,\n  x11,\n  y11,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.number(\"x00\", x00);\n  Check.typeOf.number(\"y00\", y00);\n  Check.typeOf.number(\"x01\", x01);\n  Check.typeOf.number(\"y01\", y01);\n  Check.typeOf.number(\"x10\", x10);\n  Check.typeOf.number(\"y10\", y10);\n  Check.typeOf.number(\"x11\", x11);\n  Check.typeOf.number(\"y11\", y11);\n  //>>includeEnd('debug');\n\n  const numerator1A = (x11 - x10) * (y00 - y10) - (y11 - y10) * (x00 - x10);\n  const numerator1B = (x01 - x00) * (y00 - y10) - (y01 - y00) * (x00 - x10);\n  const denominator1 = (y11 - y10) * (x01 - x00) - (x11 - x10) * (y01 - y00);\n\n  // If denominator = 0, then lines are parallel. If denominator = 0 and both numerators are 0, then coincident\n  if (denominator1 === 0) {\n    return;\n  }\n\n  const ua1 = numerator1A / denominator1;\n  const ub1 = numerator1B / denominator1;\n\n  if (ua1 >= 0 && ua1 <= 1 && ub1 >= 0 && ub1 <= 1) {\n    if (!defined(result)) {\n      result = new Cartesian2();\n    }\n\n    result.x = x00 + ua1 * (x01 - x00);\n    result.y = y00 + ua1 * (y01 - y00);\n\n    return result;\n  }\n};\nexport default Intersections2D;\n", "import AttributeCompression from \"../Core/AttributeCompression.js\";\nimport BoundingSphere from \"../Core/BoundingSphere.js\";\nimport Cartesian2 from \"../Core/Cartesian2.js\";\nimport Cartesian3 from \"../Core/Cartesian3.js\";\nimport Cartographic from \"../Core/Cartographic.js\";\nimport defined from \"../Core/defined.js\";\nimport Ellipsoid from \"../Core/Ellipsoid.js\";\nimport EllipsoidalOccluder from \"../Core/EllipsoidalOccluder.js\";\nimport IndexDatatype from \"../Core/IndexDatatype.js\";\nimport Intersections2D from \"../Core/Intersections2D.js\";\nimport CesiumMath from \"../Core/Math.js\";\nimport OrientedBoundingBox from \"../Core/OrientedBoundingBox.js\";\nimport Rectangle from \"../Core/Rectangle.js\";\nimport TerrainEncoding from \"../Core/TerrainEncoding.js\";\nimport createTaskProcessorWorker from \"./createTaskProcessorWorker.js\";\n\nconst maxShort = 32767;\nconst halfMaxShort = (maxShort / 2) | 0;\n\nconst clipScratch = [];\nconst clipScratch2 = [];\nconst verticesScratch = [];\nconst cartographicScratch = new Cartographic();\nlet cartesian3Scratch = new Cartesian3();\nconst uScratch = [];\nconst vScratch = [];\nconst heightScratch = [];\nconst indicesScratch = [];\nconst normalsScratch = [];\nconst horizonOcclusionPointScratch = new Cartesian3();\nconst boundingSphereScratch = new BoundingSphere();\nconst orientedBoundingBoxScratch = new OrientedBoundingBox();\nconst decodeTexCoordsScratch = new Cartesian2();\nconst octEncodedNormalScratch = new Cartesian3();\n\nfunction upsampleQuantizedTerrainMesh(parameters, transferableObjects) {\n  const isEastChild = parameters.isEastChild;\n  const isNorthChild = parameters.isNorthChild;\n\n  const minU = isEastChild ? halfMaxShort : 0;\n  const maxU = isEastChild ? maxShort : halfMaxShort;\n  const minV = isNorthChild ? halfMaxShort : 0;\n  const maxV = isNorthChild ? maxShort : halfMaxShort;\n\n  const uBuffer = uScratch;\n  const vBuffer = vScratch;\n  const heightBuffer = heightScratch;\n  const normalBuffer = normalsScratch;\n\n  uBuffer.length = 0;\n  vBuffer.length = 0;\n  heightBuffer.length = 0;\n  normalBuffer.length = 0;\n\n  const indices = indicesScratch;\n  indices.length = 0;\n\n  const vertexMap = {};\n\n  const parentVertices = parameters.vertices;\n  let parentIndices = parameters.indices;\n  parentIndices = parentIndices.subarray(0, parameters.indexCountWithoutSkirts);\n\n  const encoding = TerrainEncoding.clone(parameters.encoding);\n  const hasVertexNormals = encoding.hasVertexNormals;\n\n  let vertexCount = 0;\n  const quantizedVertexCount = parameters.vertexCountWithoutSkirts;\n\n  const parentMinimumHeight = parameters.minimumHeight;\n  const parentMaximumHeight = parameters.maximumHeight;\n\n  const parentUBuffer = new Array(quantizedVertexCount);\n  const parentVBuffer = new Array(quantizedVertexCount);\n  const parentHeightBuffer = new Array(quantizedVertexCount);\n  const parentNormalBuffer = hasVertexNormals\n    ? new Array(quantizedVertexCount * 2)\n    : undefined;\n\n  const threshold = 20;\n  let height;\n\n  let i, n;\n  let u, v;\n  for (i = 0, n = 0; i < quantizedVertexCount; ++i, n += 2) {\n    const texCoords = encoding.decodeTextureCoordinates(\n      parentVertices,\n      i,\n      decodeTexCoordsScratch\n    );\n    height = encoding.decodeHeight(parentVertices, i);\n\n    u = CesiumMath.clamp((texCoords.x * maxShort) | 0, 0, maxShort);\n    v = CesiumMath.clamp((texCoords.y * maxShort) | 0, 0, maxShort);\n    parentHeightBuffer[i] = CesiumMath.clamp(\n      (((height - parentMinimumHeight) /\n        (parentMaximumHeight - parentMinimumHeight)) *\n        maxShort) |\n        0,\n      0,\n      maxShort\n    );\n\n    if (u < threshold) {\n      u = 0;\n    }\n\n    if (v < threshold) {\n      v = 0;\n    }\n\n    if (maxShort - u < threshold) {\n      u = maxShort;\n    }\n\n    if (maxShort - v < threshold) {\n      v = maxShort;\n    }\n\n    parentUBuffer[i] = u;\n    parentVBuffer[i] = v;\n\n    if (hasVertexNormals) {\n      const encodedNormal = encoding.getOctEncodedNormal(\n        parentVertices,\n        i,\n        octEncodedNormalScratch\n      );\n      parentNormalBuffer[n] = encodedNormal.x;\n      parentNormalBuffer[n + 1] = encodedNormal.y;\n    }\n\n    if (\n      ((isEastChild && u >= halfMaxShort) ||\n        (!isEastChild && u <= halfMaxShort)) &&\n      ((isNorthChild && v >= halfMaxShort) ||\n        (!isNorthChild && v <= halfMaxShort))\n    ) {\n      vertexMap[i] = vertexCount;\n      uBuffer.push(u);\n      vBuffer.push(v);\n      heightBuffer.push(parentHeightBuffer[i]);\n      if (hasVertexNormals) {\n        normalBuffer.push(parentNormalBuffer[n]);\n        normalBuffer.push(parentNormalBuffer[n + 1]);\n      }\n\n      ++vertexCount;\n    }\n  }\n\n  const triangleVertices = [];\n  triangleVertices.push(new Vertex());\n  triangleVertices.push(new Vertex());\n  triangleVertices.push(new Vertex());\n\n  const clippedTriangleVertices = [];\n  clippedTriangleVertices.push(new Vertex());\n  clippedTriangleVertices.push(new Vertex());\n  clippedTriangleVertices.push(new Vertex());\n\n  let clippedIndex;\n  let clipped2;\n\n  for (i = 0; i < parentIndices.length; i += 3) {\n    const i0 = parentIndices[i];\n    const i1 = parentIndices[i + 1];\n    const i2 = parentIndices[i + 2];\n\n    const u0 = parentUBuffer[i0];\n    const u1 = parentUBuffer[i1];\n    const u2 = parentUBuffer[i2];\n\n    triangleVertices[0].initializeIndexed(\n      parentUBuffer,\n      parentVBuffer,\n      parentHeightBuffer,\n      parentNormalBuffer,\n      i0\n    );\n    triangleVertices[1].initializeIndexed(\n      parentUBuffer,\n      parentVBuffer,\n      parentHeightBuffer,\n      parentNormalBuffer,\n      i1\n    );\n    triangleVertices[2].initializeIndexed(\n      parentUBuffer,\n      parentVBuffer,\n      parentHeightBuffer,\n      parentNormalBuffer,\n      i2\n    );\n\n    // Clip triangle on the east-west boundary.\n    const clipped = Intersections2D.clipTriangleAtAxisAlignedThreshold(\n      halfMaxShort,\n      isEastChild,\n      u0,\n      u1,\n      u2,\n      clipScratch\n    );\n\n    // Get the first clipped triangle, if any.\n    clippedIndex = 0;\n\n    if (clippedIndex >= clipped.length) {\n      continue;\n    }\n    clippedIndex = clippedTriangleVertices[0].initializeFromClipResult(\n      clipped,\n      clippedIndex,\n      triangleVertices\n    );\n\n    if (clippedIndex >= clipped.length) {\n      continue;\n    }\n    clippedIndex = clippedTriangleVertices[1].initializeFromClipResult(\n      clipped,\n      clippedIndex,\n      triangleVertices\n    );\n\n    if (clippedIndex >= clipped.length) {\n      continue;\n    }\n    clippedIndex = clippedTriangleVertices[2].initializeFromClipResult(\n      clipped,\n      clippedIndex,\n      triangleVertices\n    );\n\n    // Clip the triangle against the North-south boundary.\n    clipped2 = Intersections2D.clipTriangleAtAxisAlignedThreshold(\n      halfMaxShort,\n      isNorthChild,\n      clippedTriangleVertices[0].getV(),\n      clippedTriangleVertices[1].getV(),\n      clippedTriangleVertices[2].getV(),\n      clipScratch2\n    );\n    addClippedPolygon(\n      uBuffer,\n      vBuffer,\n      heightBuffer,\n      normalBuffer,\n      indices,\n      vertexMap,\n      clipped2,\n      clippedTriangleVertices,\n      hasVertexNormals\n    );\n\n    // If there's another vertex in the original clipped result,\n    // it forms a second triangle.  Clip it as well.\n    if (clippedIndex < clipped.length) {\n      clippedTriangleVertices[2].clone(clippedTriangleVertices[1]);\n      clippedTriangleVertices[2].initializeFromClipResult(\n        clipped,\n        clippedIndex,\n        triangleVertices\n      );\n\n      clipped2 = Intersections2D.clipTriangleAtAxisAlignedThreshold(\n        halfMaxShort,\n        isNorthChild,\n        clippedTriangleVertices[0].getV(),\n        clippedTriangleVertices[1].getV(),\n        clippedTriangleVertices[2].getV(),\n        clipScratch2\n      );\n      addClippedPolygon(\n        uBuffer,\n        vBuffer,\n        heightBuffer,\n        normalBuffer,\n        indices,\n        vertexMap,\n        clipped2,\n        clippedTriangleVertices,\n        hasVertexNormals\n      );\n    }\n  }\n\n  const uOffset = isEastChild ? -maxShort : 0;\n  const vOffset = isNorthChild ? -maxShort : 0;\n\n  const westIndices = [];\n  const southIndices = [];\n  const eastIndices = [];\n  const northIndices = [];\n\n  let minimumHeight = Number.MAX_VALUE;\n  let maximumHeight = -minimumHeight;\n\n  const cartesianVertices = verticesScratch;\n  cartesianVertices.length = 0;\n\n  const ellipsoid = Ellipsoid.clone(parameters.ellipsoid);\n  const rectangle = Rectangle.clone(parameters.childRectangle);\n\n  const north = rectangle.north;\n  const south = rectangle.south;\n  let east = rectangle.east;\n  const west = rectangle.west;\n\n  if (east < west) {\n    east += CesiumMath.TWO_PI;\n  }\n\n  for (i = 0; i < uBuffer.length; ++i) {\n    u = Math.round(uBuffer[i]);\n    if (u <= minU) {\n      westIndices.push(i);\n      u = 0;\n    } else if (u >= maxU) {\n      eastIndices.push(i);\n      u = maxShort;\n    } else {\n      u = u * 2 + uOffset;\n    }\n\n    uBuffer[i] = u;\n\n    v = Math.round(vBuffer[i]);\n    if (v <= minV) {\n      southIndices.push(i);\n      v = 0;\n    } else if (v >= maxV) {\n      northIndices.push(i);\n      v = maxShort;\n    } else {\n      v = v * 2 + vOffset;\n    }\n\n    vBuffer[i] = v;\n\n    height = CesiumMath.lerp(\n      parentMinimumHeight,\n      parentMaximumHeight,\n      heightBuffer[i] / maxShort\n    );\n    if (height < minimumHeight) {\n      minimumHeight = height;\n    }\n    if (height > maximumHeight) {\n      maximumHeight = height;\n    }\n\n    heightBuffer[i] = height;\n\n    cartographicScratch.longitude = CesiumMath.lerp(west, east, u / maxShort);\n    cartographicScratch.latitude = CesiumMath.lerp(south, north, v / maxShort);\n    cartographicScratch.height = height;\n\n    ellipsoid.cartographicToCartesian(cartographicScratch, cartesian3Scratch);\n\n    cartesianVertices.push(cartesian3Scratch.x);\n    cartesianVertices.push(cartesian3Scratch.y);\n    cartesianVertices.push(cartesian3Scratch.z);\n  }\n\n  const boundingSphere = BoundingSphere.fromVertices(\n    cartesianVertices,\n    Cartesian3.ZERO,\n    3,\n    boundingSphereScratch\n  );\n  const orientedBoundingBox = OrientedBoundingBox.fromRectangle(\n    rectangle,\n    minimumHeight,\n    maximumHeight,\n    ellipsoid,\n    orientedBoundingBoxScratch\n  );\n\n  const occluder = new EllipsoidalOccluder(ellipsoid);\n  const horizonOcclusionPoint = occluder.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid(\n    boundingSphere.center,\n    cartesianVertices,\n    3,\n    boundingSphere.center,\n    minimumHeight,\n    horizonOcclusionPointScratch\n  );\n\n  const heightRange = maximumHeight - minimumHeight;\n\n  const vertices = new Uint16Array(\n    uBuffer.length + vBuffer.length + heightBuffer.length\n  );\n\n  for (i = 0; i < uBuffer.length; ++i) {\n    vertices[i] = uBuffer[i];\n  }\n\n  let start = uBuffer.length;\n\n  for (i = 0; i < vBuffer.length; ++i) {\n    vertices[start + i] = vBuffer[i];\n  }\n\n  start += vBuffer.length;\n\n  for (i = 0; i < heightBuffer.length; ++i) {\n    vertices[start + i] =\n      (maxShort * (heightBuffer[i] - minimumHeight)) / heightRange;\n  }\n\n  const indicesTypedArray = IndexDatatype.createTypedArray(\n    uBuffer.length,\n    indices\n  );\n\n  let encodedNormals;\n  if (hasVertexNormals) {\n    const normalArray = new Uint8Array(normalBuffer);\n    transferableObjects.push(\n      vertices.buffer,\n      indicesTypedArray.buffer,\n      normalArray.buffer\n    );\n    encodedNormals = normalArray.buffer;\n  } else {\n    transferableObjects.push(vertices.buffer, indicesTypedArray.buffer);\n  }\n\n  return {\n    vertices: vertices.buffer,\n    encodedNormals: encodedNormals,\n    indices: indicesTypedArray.buffer,\n    minimumHeight: minimumHeight,\n    maximumHeight: maximumHeight,\n    westIndices: westIndices,\n    southIndices: southIndices,\n    eastIndices: eastIndices,\n    northIndices: northIndices,\n    boundingSphere: boundingSphere,\n    orientedBoundingBox: orientedBoundingBox,\n    horizonOcclusionPoint: horizonOcclusionPoint,\n  };\n}\n\nfunction Vertex() {\n  this.vertexBuffer = undefined;\n  this.index = undefined;\n  this.first = undefined;\n  this.second = undefined;\n  this.ratio = undefined;\n}\n\nVertex.prototype.clone = function (result) {\n  if (!defined(result)) {\n    result = new Vertex();\n  }\n\n  result.uBuffer = this.uBuffer;\n  result.vBuffer = this.vBuffer;\n  result.heightBuffer = this.heightBuffer;\n  result.normalBuffer = this.normalBuffer;\n  result.index = this.index;\n  result.first = this.first;\n  result.second = this.second;\n  result.ratio = this.ratio;\n\n  return result;\n};\n\nVertex.prototype.initializeIndexed = function (\n  uBuffer,\n  vBuffer,\n  heightBuffer,\n  normalBuffer,\n  index\n) {\n  this.uBuffer = uBuffer;\n  this.vBuffer = vBuffer;\n  this.heightBuffer = heightBuffer;\n  this.normalBuffer = normalBuffer;\n  this.index = index;\n  this.first = undefined;\n  this.second = undefined;\n  this.ratio = undefined;\n};\n\nVertex.prototype.initializeFromClipResult = function (\n  clipResult,\n  index,\n  vertices\n) {\n  let nextIndex = index + 1;\n\n  if (clipResult[index] !== -1) {\n    vertices[clipResult[index]].clone(this);\n  } else {\n    this.vertexBuffer = undefined;\n    this.index = undefined;\n    this.first = vertices[clipResult[nextIndex]];\n    ++nextIndex;\n    this.second = vertices[clipResult[nextIndex]];\n    ++nextIndex;\n    this.ratio = clipResult[nextIndex];\n    ++nextIndex;\n  }\n\n  return nextIndex;\n};\n\nVertex.prototype.getKey = function () {\n  if (this.isIndexed()) {\n    return this.index;\n  }\n  return JSON.stringify({\n    first: this.first.getKey(),\n    second: this.second.getKey(),\n    ratio: this.ratio,\n  });\n};\n\nVertex.prototype.isIndexed = function () {\n  return defined(this.index);\n};\n\nVertex.prototype.getH = function () {\n  if (defined(this.index)) {\n    return this.heightBuffer[this.index];\n  }\n  return CesiumMath.lerp(this.first.getH(), this.second.getH(), this.ratio);\n};\n\nVertex.prototype.getU = function () {\n  if (defined(this.index)) {\n    return this.uBuffer[this.index];\n  }\n  return CesiumMath.lerp(this.first.getU(), this.second.getU(), this.ratio);\n};\n\nVertex.prototype.getV = function () {\n  if (defined(this.index)) {\n    return this.vBuffer[this.index];\n  }\n  return CesiumMath.lerp(this.first.getV(), this.second.getV(), this.ratio);\n};\n\nlet encodedScratch = new Cartesian2();\n// An upsampled triangle may be clipped twice before it is assigned an index\n// In this case, we need a buffer to handle the recursion of getNormalX() and getNormalY().\nlet depth = -1;\nconst cartesianScratch1 = [new Cartesian3(), new Cartesian3()];\nconst cartesianScratch2 = [new Cartesian3(), new Cartesian3()];\nfunction lerpOctEncodedNormal(vertex, result) {\n  ++depth;\n\n  let first = cartesianScratch1[depth];\n  let second = cartesianScratch2[depth];\n\n  first = AttributeCompression.octDecode(\n    vertex.first.getNormalX(),\n    vertex.first.getNormalY(),\n    first\n  );\n  second = AttributeCompression.octDecode(\n    vertex.second.getNormalX(),\n    vertex.second.getNormalY(),\n    second\n  );\n  cartesian3Scratch = Cartesian3.lerp(\n    first,\n    second,\n    vertex.ratio,\n    cartesian3Scratch\n  );\n  Cartesian3.normalize(cartesian3Scratch, cartesian3Scratch);\n\n  AttributeCompression.octEncode(cartesian3Scratch, result);\n\n  --depth;\n\n  return result;\n}\n\nVertex.prototype.getNormalX = function () {\n  if (defined(this.index)) {\n    return this.normalBuffer[this.index * 2];\n  }\n\n  encodedScratch = lerpOctEncodedNormal(this, encodedScratch);\n  return encodedScratch.x;\n};\n\nVertex.prototype.getNormalY = function () {\n  if (defined(this.index)) {\n    return this.normalBuffer[this.index * 2 + 1];\n  }\n\n  encodedScratch = lerpOctEncodedNormal(this, encodedScratch);\n  return encodedScratch.y;\n};\n\nconst polygonVertices = [];\npolygonVertices.push(new Vertex());\npolygonVertices.push(new Vertex());\npolygonVertices.push(new Vertex());\npolygonVertices.push(new Vertex());\n\nfunction addClippedPolygon(\n  uBuffer,\n  vBuffer,\n  heightBuffer,\n  normalBuffer,\n  indices,\n  vertexMap,\n  clipped,\n  triangleVertices,\n  hasVertexNormals\n) {\n  if (clipped.length === 0) {\n    return;\n  }\n\n  let numVertices = 0;\n  let clippedIndex = 0;\n  while (clippedIndex < clipped.length) {\n    clippedIndex = polygonVertices[numVertices++].initializeFromClipResult(\n      clipped,\n      clippedIndex,\n      triangleVertices\n    );\n  }\n\n  for (let i = 0; i < numVertices; ++i) {\n    const polygonVertex = polygonVertices[i];\n    if (!polygonVertex.isIndexed()) {\n      const key = polygonVertex.getKey();\n      if (defined(vertexMap[key])) {\n        polygonVertex.newIndex = vertexMap[key];\n      } else {\n        const newIndex = uBuffer.length;\n        uBuffer.push(polygonVertex.getU());\n        vBuffer.push(polygonVertex.getV());\n        heightBuffer.push(polygonVertex.getH());\n        if (hasVertexNormals) {\n          normalBuffer.push(polygonVertex.getNormalX());\n          normalBuffer.push(polygonVertex.getNormalY());\n        }\n        polygonVertex.newIndex = newIndex;\n        vertexMap[key] = newIndex;\n      }\n    } else {\n      polygonVertex.newIndex = vertexMap[polygonVertex.index];\n      polygonVertex.uBuffer = uBuffer;\n      polygonVertex.vBuffer = vBuffer;\n      polygonVertex.heightBuffer = heightBuffer;\n      if (hasVertexNormals) {\n        polygonVertex.normalBuffer = normalBuffer;\n      }\n    }\n  }\n\n  if (numVertices === 3) {\n    // A triangle.\n    indices.push(polygonVertices[0].newIndex);\n    indices.push(polygonVertices[1].newIndex);\n    indices.push(polygonVertices[2].newIndex);\n  } else if (numVertices === 4) {\n    // A quad - two triangles.\n    indices.push(polygonVertices[0].newIndex);\n    indices.push(polygonVertices[1].newIndex);\n    indices.push(polygonVertices[2].newIndex);\n\n    indices.push(polygonVertices[0].newIndex);\n    indices.push(polygonVertices[2].newIndex);\n    indices.push(polygonVertices[3].newIndex);\n  }\n}\nexport default createTaskProcessorWorker(upsampleQuantizedTerrainMesh);\n"], "names": ["Intersections2D", "threshold", "keepAbove", "u0", "u1", "u2", "result", "defined", "DeveloperError", "u0Behind", "u1Behind", "u2Behind", "length", "numBeh<PERSON>", "u01Ratio", "u02Ratio", "u12Ratio", "u10Ratio", "u20Ratio", "u21Ratio", "push", "x", "y", "x1", "y1", "x2", "y2", "x3", "y3", "x1mx3", "x3mx2", "y2my3", "y1my3", "inverseDeterminant", "ymy3", "xmx3", "l1", "l2", "l3", "z", "Cartesian3", "x00", "y00", "x01", "y01", "x10", "y10", "x11", "y11", "Check", "typeOf", "number", "denominator1", "ua1", "ub1", "Cartesian2", "maxShort", "halfMaxShort", "clipScratch", "clipScratch2", "verticesScratch", "cartographicScratch", "Cartographic", "cartesian3Scratch", "uScratch", "vScratch", "heightScratch", "indicesScratch", "normalsScratch", "horizonOcclusionPointScratch", "boundingSphereScratch", "BoundingSphere", "orientedBoundingBoxScratch", "OrientedBoundingBox", "decodeTexCoordsScratch", "octEncodedNormalScratch", "Vertex", "this", "vertexBuffer", "undefined", "index", "first", "second", "ratio", "prototype", "clone", "<PERSON><PERSON><PERSON><PERSON>", "vBuffer", "heightBuffer", "normalBuffer", "initializeIndexed", "initializeFromClipResult", "clipResult", "vertices", "nextIndex", "<PERSON><PERSON><PERSON>", "isIndexed", "JSON", "stringify", "getH", "CesiumMath", "lerp", "getU", "getV", "encodedScratch", "depth", "cartesianScratch1", "cartesianScratch2", "lerpOctEncodedNormal", "vertex", "AttributeCompression", "octDecode", "getNormalX", "getNormalY", "normalize", "octEncode", "polygonVertices", "addClippedPolygon", "indices", "vertexMap", "clipped", "triangleVertices", "hasVertexNormals", "numVertices", "clippedIndex", "i", "polygonVertex", "newIndex", "key", "createTaskProcessorWorker", "parameters", "transferableObjects", "isEastChild", "isNorthChild", "minU", "maxU", "minV", "maxV", "parentVertices", "parentIndices", "subarray", "indexCountWithoutSkirts", "encoding", "TerrainEncoding", "vertexCount", "quantizedVertexCount", "vertexCountWithoutSkirts", "parentMinimumHeight", "minimumHeight", "parentMaximumHeight", "maximumHeight", "parentUBuffer", "Array", "parentVBuffer", "parentHeightBuffer", "parentNormalBuffer", "height", "n", "u", "v", "texCoords", "decodeTextureCoordinates", "decodeHeight", "clamp", "encodedNormal", "getOctEncodedNormal", "clippedTriangleVertices", "clipped2", "i0", "i1", "i2", "clipTriangleAtAxisAlignedThreshold", "uOffset", "vOffset", "westIndices", "southIndices", "eastIndices", "northIndices", "Number", "MAX_VALUE", "cartesianVertices", "ellipsoid", "Ellipsoid", "rectangle", "Rectangle", "childRectangle", "north", "south", "east", "west", "TWO_PI", "Math", "round", "longitude", "latitude", "cartographicToCartesian", "boundingSphere", "fromVertices", "ZERO", "orientedBoundingBox", "fromRectangle", "horizonOcclusionPoint", "EllipsoidalOccluder", "computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid", "center", "heightRange", "Uint16Array", "start", "indicesTypedArray", "IndexDatatype", "createTypedArray", "encodedNormals", "normalArray", "Uint8Array", "buffer"], "mappings": "4fAWA,MAAMA,EAAkB,CA4BxBA,mCAAqD,SACnDC,EACAC,EACAC,EACAC,EACAC,EACAC,GAGA,IAAKC,EAAAA,QAAQN,GACX,MAAM,IAAIO,EAAAA,eAAe,0BAE3B,IAAKD,EAAAA,QAAQL,GACX,MAAM,IAAIM,EAAAA,eAAe,0BAE3B,IAAKD,EAAAA,QAAQJ,GACX,MAAM,IAAIK,EAAAA,eAAe,mBAE3B,IAAKD,EAAAA,QAAQH,GACX,MAAM,IAAII,EAAAA,eAAe,mBAE3B,IAAKD,EAAAA,QAAQF,GACX,MAAM,IAAIG,EAAAA,eAAe,mBAU3B,IAAIC,EACAC,EACAC,EARCJ,EAAAA,QAAQD,GAGXA,EAAOM,OAAS,EAFhBN,EAAS,GAQPJ,GACFO,EAAWN,EAAKF,EAChBS,EAAWN,EAAKH,EAChBU,EAAWN,EAAKJ,IAEhBQ,EAAWN,EAAKF,EAChBS,EAAWN,EAAKH,EAChBU,EAAWN,EAAKJ,GAGlB,MAAMY,EAAYJ,EAAWC,EAAWC,EAExC,IAAIG,EACAC,EACAC,EACAC,EACAC,EACAC,EA0HJ,OAxHkB,IAAdN,EACEJ,GACFK,GAAYb,EAAYE,IAAOC,EAAKD,GACpCY,GAAYd,EAAYE,IAAOE,EAAKF,GAEpCG,EAAOc,KAAK,GAEZd,EAAOc,KAAK,GAEK,IAAbL,IACFT,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKL,IAGG,IAAbD,IACFR,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKN,KAELJ,GACTM,GAAYf,EAAYG,IAAOC,EAAKD,GACpCa,GAAYhB,EAAYG,IAAOD,EAAKC,GAEpCE,EAAOc,KAAK,GAEZd,EAAOc,KAAK,GAEK,IAAbH,IACFX,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKH,IAGG,IAAbD,IACFV,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKJ,KAELL,IACTO,GAAYjB,EAAYI,IAAOF,EAAKE,GACpCc,GAAYlB,EAAYI,IAAOD,EAAKC,GAEpCC,EAAOc,KAAK,GAEZd,EAAOc,KAAK,GAEK,IAAbD,IACFb,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKD,IAGG,IAAbD,IACFZ,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKF,KAGO,IAAdL,EACJJ,GAAYN,IAAOF,EAeZS,GAAYN,IAAOH,EAenBU,GAAYN,IAAOJ,IAC7Bc,GAAYd,EAAYE,IAAOE,EAAKF,GACpCa,GAAYf,EAAYG,IAAOC,EAAKD,GAEpCE,EAAOc,KAAK,GAEZd,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKL,GAEZT,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKJ,KA5BZG,GAAYlB,EAAYI,IAAOD,EAAKC,GACpCS,GAAYb,EAAYE,IAAOC,EAAKD,GAEpCG,EAAOc,KAAK,GAEZd,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKD,GAEZb,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKN,KA5BZG,GAAYhB,EAAYG,IAAOD,EAAKC,GACpCc,GAAYjB,EAAYI,IAAOF,EAAKE,GAEpCC,EAAOc,KAAK,GAEZd,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKH,GAEZX,EAAOc,MAAM,GACbd,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAKF,IAgCS,IAAdL,IAETP,EAAOc,KAAK,GACZd,EAAOc,KAAK,GACZd,EAAOc,KAAK,IAIPd,CACT,EAqBAN,8BAAgD,SAC9CqB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAtB,GAGA,IAAKC,EAAAA,QAAQc,GACX,MAAM,IAAIb,EAAAA,eAAe,kBAE3B,IAAKD,EAAAA,QAAQe,GACX,MAAM,IAAId,EAAAA,eAAe,kBAE3B,IAAKD,EAAAA,QAAQgB,GACX,MAAM,IAAIf,EAAAA,eAAe,mBAE3B,IAAKD,EAAAA,QAAQiB,GACX,MAAM,IAAIhB,EAAAA,eAAe,mBAE3B,IAAKD,EAAAA,QAAQkB,GACX,MAAM,IAAIjB,EAAAA,eAAe,mBAE3B,IAAKD,EAAAA,QAAQmB,GACX,MAAM,IAAIlB,EAAAA,eAAe,mBAE3B,IAAKD,EAAAA,QAAQoB,GACX,MAAM,IAAInB,EAAAA,eAAe,mBAE3B,IAAKD,EAAAA,QAAQqB,GACX,MAAM,IAAIpB,EAAAA,eAAe,mBAI3B,MAAMqB,EAAQN,EAAKI,EACbG,EAAQH,EAAKF,EACbM,EAAQL,EAAKE,EACbI,EAAQR,EAAKI,EACbK,EAAqB,GAAOF,EAAQF,EAAQC,EAAQE,GACpDE,EAAOZ,EAAIM,EACXO,EAAOd,EAAIM,EACXS,GAAML,EAAQI,EAAOL,EAAQI,GAAQD,EACrCI,IAAOL,EAAQG,EAAON,EAAQK,GAAQD,EACtCK,EAAK,EAAMF,EAAKC,EAEtB,OAAI9B,EAAAA,QAAQD,IACVA,EAAOe,EAAIe,EACX9B,EAAOgB,EAAIe,EACX/B,EAAOiC,EAAID,EACJhC,GAEF,IAAIkC,EAAAA,WAAWJ,EAAIC,EAAIC,EAChC,EAqBAtC,0CAA4D,SAC1DyC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACA1C,GAGA2C,EAAAA,MAAMC,OAAOC,OAAO,MAAOV,GAC3BQ,EAAAA,MAAMC,OAAOC,OAAO,MAAOT,GAC3BO,EAAAA,MAAMC,OAAOC,OAAO,MAAOR,GAC3BM,EAAAA,MAAMC,OAAOC,OAAO,MAAOP,GAC3BK,EAAAA,MAAMC,OAAOC,OAAO,MAAON,GAC3BI,EAAAA,MAAMC,OAAOC,OAAO,MAAOL,GAC3BG,EAAAA,MAAMC,OAAOC,OAAO,MAAOJ,GAC3BE,EAAAA,MAAMC,OAAOC,OAAO,MAAOH,GAG3B,MAEMI,GAAgBJ,EAAMF,IAAQH,EAAMF,IAAQM,EAAMF,IAAQD,EAAMF,GAGtE,GAAqB,IAAjBU,EACF,OAGF,MAAMC,IATeN,EAAMF,IAAQH,EAAMI,IAAQE,EAAMF,IAAQL,EAAMI,IAS3CO,EACpBE,IATeX,EAAMF,IAAQC,EAAMI,IAAQF,EAAMF,IAAQD,EAAMI,IAS3CO,EAE1B,OAAIC,GAAO,GAAKA,GAAO,GAAKC,GAAO,GAAKA,GAAO,GACxC/C,EAAAA,QAAQD,KACXA,EAAS,IAAIiD,EAAAA,YAGfjD,EAAOe,EAAIoB,EAAMY,GAAOV,EAAMF,GAC9BnC,EAAOgB,EAAIoB,EAAMW,GAAOT,EAAMF,GAEvBpC,QART,CAUF,GCpVMkD,EAAW,MACXC,EAAe,MAEfC,EAAc,GACdC,EAAe,GACfC,EAAkB,GAClBC,EAAsB,IAAIC,EAAAA,aAChC,IAAIC,EAAoB,IAAIvB,EAAAA,WAC5B,MAAMwB,EAAW,GACXC,EAAW,GACXC,EAAgB,GAChBC,EAAiB,GACjBC,EAAiB,GACjBC,EAA+B,IAAI7B,EAAAA,WACnC8B,EAAwB,IAAIC,EAAAA,eAC5BC,EAA6B,IAAIC,EAAAA,oBACjCC,EAAyB,IAAInB,EAAAA,WAC7BoB,EAA0B,IAAInC,EAAAA,WA8ZpC,SAASoC,IACPC,KAAKC,kBAAeC,EACpBF,KAAKG,WAAQD,EACbF,KAAKI,WAAQF,EACbF,KAAKK,YAASH,EACdF,KAAKM,WAAQJ,CACf,CAEAH,EAAOQ,UAAUC,MAAQ,SAAU/E,GAcjC,OAbKC,EAAAA,QAAQD,KACXA,EAAS,IAAIsE,GAGftE,EAAOgF,QAAUT,KAAKS,QACtBhF,EAAOiF,QAAUV,KAAKU,QACtBjF,EAAOkF,aAAeX,KAAKW,aAC3BlF,EAAOmF,aAAeZ,KAAKY,aAC3BnF,EAAO0E,MAAQH,KAAKG,MACpB1E,EAAO2E,MAAQJ,KAAKI,MACpB3E,EAAO4E,OAASL,KAAKK,OACrB5E,EAAO6E,MAAQN,KAAKM,MAEb7E,CACT,EAEAsE,EAAOQ,UAAUM,kBAAoB,SACnCJ,EACAC,EACAC,EACAC,EACAT,GAEAH,KAAKS,QAAUA,EACfT,KAAKU,QAAUA,EACfV,KAAKW,aAAeA,EACpBX,KAAKY,aAAeA,EACpBZ,KAAKG,MAAQA,EACbH,KAAKI,WAAQF,EACbF,KAAKK,YAASH,EACdF,KAAKM,WAAQJ,CACf,EAEAH,EAAOQ,UAAUO,yBAA2B,SAC1CC,EACAZ,EACAa,GAEA,IAAIC,EAAYd,EAAQ,EAexB,OAb2B,IAAvBY,EAAWZ,GACba,EAASD,EAAWZ,IAAQK,MAAMR,OAElCA,KAAKC,kBAAeC,EACpBF,KAAKG,WAAQD,EACbF,KAAKI,MAAQY,EAASD,EAAWE,MAC/BA,EACFjB,KAAKK,OAASW,EAASD,EAAWE,MAChCA,EACFjB,KAAKM,MAAQS,EAAWE,KACtBA,GAGGA,CACT,EAEAlB,EAAOQ,UAAUW,OAAS,WACxB,OAAIlB,KAAKmB,YACAnB,KAAKG,MAEPiB,KAAKC,UAAU,CACpBjB,MAAOJ,KAAKI,MAAMc,SAClBb,OAAQL,KAAKK,OAAOa,SACpBZ,MAAON,KAAKM,OAEhB,EAEAP,EAAOQ,UAAUY,UAAY,WAC3B,OAAOzF,EAAOA,QAACsE,KAAKG,MACtB,EAEAJ,EAAOQ,UAAUe,KAAO,WACtB,OAAI5F,EAAOA,QAACsE,KAAKG,OACRH,KAAKW,aAAaX,KAAKG,OAEzBoB,aAAWC,KAAKxB,KAAKI,MAAMkB,OAAQtB,KAAKK,OAAOiB,OAAQtB,KAAKM,MACrE,EAEAP,EAAOQ,UAAUkB,KAAO,WACtB,OAAI/F,EAAOA,QAACsE,KAAKG,OACRH,KAAKS,QAAQT,KAAKG,OAEpBoB,aAAWC,KAAKxB,KAAKI,MAAMqB,OAAQzB,KAAKK,OAAOoB,OAAQzB,KAAKM,MACrE,EAEAP,EAAOQ,UAAUmB,KAAO,WACtB,OAAIhG,EAAOA,QAACsE,KAAKG,OACRH,KAAKU,QAAQV,KAAKG,OAEpBoB,aAAWC,KAAKxB,KAAKI,MAAMsB,OAAQ1B,KAAKK,OAAOqB,OAAQ1B,KAAKM,MACrE,EAEA,IAAIqB,EAAiB,IAAIjD,EAAAA,WAGrBkD,GAAS,EACb,MAAMC,EAAoB,CAAC,IAAIlE,EAAAA,WAAc,IAAIA,EAAAA,YAC3CmE,EAAoB,CAAC,IAAInE,EAAAA,WAAc,IAAIA,EAAAA,YACjD,SAASoE,EAAqBC,EAAQvG,KAClCmG,EAEF,IAAIxB,EAAQyB,EAAkBD,GAC1BvB,EAASyB,EAAkBF,GAwB/B,OAtBAxB,EAAQ6B,EAAoBA,qBAACC,UAC3BF,EAAO5B,MAAM+B,aACbH,EAAO5B,MAAMgC,aACbhC,GAEFC,EAAS4B,EAAoBA,qBAACC,UAC5BF,EAAO3B,OAAO8B,aACdH,EAAO3B,OAAO+B,aACd/B,GAEFnB,EAAoBvB,EAAUA,WAAC6D,KAC7BpB,EACAC,EACA2B,EAAO1B,MACPpB,GAEFvB,EAAAA,WAAW0E,UAAUnD,EAAmBA,GAExC+C,EAAAA,qBAAqBK,UAAUpD,EAAmBzD,KAEhDmG,EAEKnG,CACT,CAEAsE,EAAOQ,UAAU4B,WAAa,WAC5B,OAAIzG,EAAOA,QAACsE,KAAKG,OACRH,KAAKY,aAA0B,EAAbZ,KAAKG,QAGhCwB,EAAiBI,EAAqB/B,KAAM2B,GACrCA,EAAenF,EACxB,EAEAuD,EAAOQ,UAAU6B,WAAa,WAC5B,OAAI1G,EAAOA,QAACsE,KAAKG,OACRH,KAAKY,aAA0B,EAAbZ,KAAKG,MAAY,IAG5CwB,EAAiBI,EAAqB/B,KAAM2B,GACrCA,EAAelF,EACxB,EAEA,MAAM8F,EAAkB,GAMxB,SAASC,EACP/B,EACAC,EACAC,EACAC,EACA6B,EACAC,EACAC,EACAC,EACAC,GAEA,GAAuB,IAAnBF,EAAQ5G,OACV,OAGF,IAAI+G,EAAc,EACdC,EAAe,EACnB,KAAOA,EAAeJ,EAAQ5G,QAC5BgH,EAAeR,EAAgBO,KAAehC,yBAC5C6B,EACAI,EACAH,GAIJ,IAAK,IAAII,EAAI,EAAGA,EAAIF,IAAeE,EAAG,CACpC,MAAMC,EAAgBV,EAAgBS,GACtC,GAAKC,EAAc9B,YAiBjB8B,EAAcC,SAAWR,EAAUO,EAAc9C,OACjD8C,EAAcxC,QAAUA,EACxBwC,EAAcvC,QAAUA,EACxBuC,EAActC,aAAeA,EACzBkC,IACFI,EAAcrC,aAAeA,OAtBD,CAC9B,MAAMuC,EAAMF,EAAc/B,SAC1B,GAAIxF,UAAQgH,EAAUS,IACpBF,EAAcC,SAAWR,EAAUS,OAC9B,CACL,MAAMD,EAAWzC,EAAQ1E,OACzB0E,EAAQlE,KAAK0G,EAAcxB,QAC3Bf,EAAQnE,KAAK0G,EAAcvB,QAC3Bf,EAAapE,KAAK0G,EAAc3B,QAC5BuB,IACFjC,EAAarE,KAAK0G,EAAcd,cAChCvB,EAAarE,KAAK0G,EAAcb,eAElCa,EAAcC,SAAWA,EACzBR,EAAUS,GAAOD,CAClB,CACP,CASG,CAEmB,IAAhBJ,GAEFL,EAAQlG,KAAKgG,EAAgB,GAAGW,UAChCT,EAAQlG,KAAKgG,EAAgB,GAAGW,UAChCT,EAAQlG,KAAKgG,EAAgB,GAAGW,WACP,IAAhBJ,IAETL,EAAQlG,KAAKgG,EAAgB,GAAGW,UAChCT,EAAQlG,KAAKgG,EAAgB,GAAGW,UAChCT,EAAQlG,KAAKgG,EAAgB,GAAGW,UAEhCT,EAAQlG,KAAKgG,EAAgB,GAAGW,UAChCT,EAAQlG,KAAKgG,EAAgB,GAAGW,UAChCT,EAAQlG,KAAKgG,EAAgB,GAAGW,UAEpC,QA1EAX,EAAgBhG,KAAK,IAAIwD,GACzBwC,EAAgBhG,KAAK,IAAIwD,GACzBwC,EAAgBhG,KAAK,IAAIwD,GACzBwC,EAAgBhG,KAAK,IAAIwD,GAwEVqD,GApoBf,SAAsCC,EAAYC,GAChD,MAAMC,EAAcF,EAAWE,YACzBC,EAAeH,EAAWG,aAE1BC,EAAOF,EAAc3E,EAAe,EACpC8E,EAAOH,EAAc5E,EAAWC,EAChC+E,EAAOH,EAAe5E,EAAe,EACrCgF,EAAOJ,EAAe7E,EAAWC,EAEjC6B,EAAUtB,EACVuB,EAAUtB,EACVuB,EAAetB,EACfuB,EAAerB,EAErBkB,EAAQ1E,OAAS,EACjB2E,EAAQ3E,OAAS,EACjB4E,EAAa5E,OAAS,EACtB6E,EAAa7E,OAAS,EAEtB,MAAM0G,EAAUnD,EAChBmD,EAAQ1G,OAAS,EAEjB,MAAM2G,EAAY,CAAA,EAEZmB,EAAiBR,EAAWrC,SAClC,IAAI8C,EAAgBT,EAAWZ,QAC/BqB,EAAgBA,EAAcC,SAAS,EAAGV,EAAWW,yBAErD,MAAMC,EAAWC,EAAeA,gBAAC1D,MAAM6C,EAAWY,UAC5CpB,EAAmBoB,EAASpB,iBAElC,IAAIsB,EAAc,EAClB,MAAMC,EAAuBf,EAAWgB,yBAElCC,EAAsBjB,EAAWkB,cACjCC,EAAsBnB,EAAWoB,cAEjCC,EAAgB,IAAIC,MAAMP,GAC1BQ,EAAgB,IAAID,MAAMP,GAC1BS,EAAqB,IAAIF,MAAMP,GAC/BU,EAAqBjC,EACvB,IAAI8B,MAA6B,EAAvBP,QACVlE,EAGJ,IAAI6E,EAEA/B,EAAGgC,GACHC,GAAGC,GACP,IAAKlC,EAAI,EAAGgC,GAAI,EAAGhC,EAAIoB,IAAwBpB,EAAGgC,IAAK,EAAG,CACxD,MAAMG,EAAYlB,EAASmB,yBACzBvB,EACAb,EACAnD,GAkCF,GAhCAkF,EAASd,EAASoB,aAAaxB,EAAgBb,GAE/CiC,GAAI1D,EAAAA,WAAW+D,MAAOH,EAAU3I,EAAImC,EAAY,EAAG,EAAGA,GACtDuG,GAAI3D,EAAAA,WAAW+D,MAAOH,EAAU1I,EAAIkC,EAAY,EAAG,EAAGA,GACtDkG,EAAmB7B,GAAKzB,EAAAA,WAAW+D,OAC9BP,EAAST,IACTE,EAAsBF,GACvB3F,EACA,EACF,EACAA,GAGEsG,GAxBY,KAyBdA,GAAI,GAGFC,GA5BY,KA6BdA,GAAI,GAGFvG,EAAWsG,GAhCC,KAiCdA,GAAItG,GAGFA,EAAWuG,GApCC,KAqCdA,GAAIvG,GAGN+F,EAAc1B,GAAKiC,GACnBL,EAAc5B,GAAKkC,GAEfrC,EAAkB,CACpB,MAAM0C,EAAgBtB,EAASuB,oBAC7B3B,EACAb,EACAlD,GAEFgF,EAAmBE,IAAKO,EAAc/I,EACtCsI,EAAmBE,GAAI,GAAKO,EAAc9I,CAC3C,EAGG8G,GAAe0B,IAAKrG,IAClB2E,GAAe0B,IAAKrG,KACtB4E,GAAgB0B,IAAKtG,IACnB4E,GAAgB0B,IAAKtG,KAEzB8D,EAAUM,GAAKmB,EACf1D,EAAQlE,KAAK0I,IACbvE,EAAQnE,KAAK2I,IACbvE,EAAapE,KAAKsI,EAAmB7B,IACjCH,IACFjC,EAAarE,KAAKuI,EAAmBE,KACrCpE,EAAarE,KAAKuI,EAAmBE,GAAI,OAGzCb,EAEL,CAED,MAAMvB,GAAmB,GACzBA,GAAiBrG,KAAK,IAAIwD,GAC1B6C,GAAiBrG,KAAK,IAAIwD,GAC1B6C,GAAiBrG,KAAK,IAAIwD,GAE1B,MAAM0F,GAA0B,GAKhC,IAAI1C,GACA2C,GAEJ,IAPAD,GAAwBlJ,KAAK,IAAIwD,GACjC0F,GAAwBlJ,KAAK,IAAIwD,GACjC0F,GAAwBlJ,KAAK,IAAIwD,GAK5BiD,EAAI,EAAGA,EAAIc,EAAc/H,OAAQiH,GAAK,EAAG,CAC5C,MAAM2C,EAAK7B,EAAcd,GACnB4C,EAAK9B,EAAcd,EAAI,GACvB6C,EAAK/B,EAAcd,EAAI,GAEvB1H,EAAKoJ,EAAciB,GACnBpK,EAAKmJ,EAAckB,GACnBpK,EAAKkJ,EAAcmB,GAEzBjD,GAAiB,GAAG/B,kBAClB6D,EACAE,EACAC,EACAC,EACAa,GAEF/C,GAAiB,GAAG/B,kBAClB6D,EACAE,EACAC,EACAC,EACAc,GAEFhD,GAAiB,GAAG/B,kBAClB6D,EACAE,EACAC,EACAC,EACAe,GAIF,MAAMlD,EAAUxH,EAAgB2K,mCAC9BlH,EACA2E,EACAjI,EACAC,EACAC,EACAqD,GAIFkE,GAAe,EAEXA,IAAgBJ,EAAQ5G,SAG5BgH,GAAe0C,GAAwB,GAAG3E,yBACxC6B,EACAI,GACAH,IAGEG,IAAgBJ,EAAQ5G,SAG5BgH,GAAe0C,GAAwB,GAAG3E,yBACxC6B,EACAI,GACAH,IAGEG,IAAgBJ,EAAQ5G,SAG5BgH,GAAe0C,GAAwB,GAAG3E,yBACxC6B,EACAI,GACAH,IAIF8C,GAAWvK,EAAgB2K,mCACzBlH,EACA4E,EACAiC,GAAwB,GAAG/D,OAC3B+D,GAAwB,GAAG/D,OAC3B+D,GAAwB,GAAG/D,OAC3B5C,GAEF0D,EACE/B,EACAC,EACAC,EACAC,EACA6B,EACAC,EACAgD,GACAD,GACA5C,GAKEE,GAAeJ,EAAQ5G,SACzB0J,GAAwB,GAAGjF,MAAMiF,GAAwB,IACzDA,GAAwB,GAAG3E,yBACzB6B,EACAI,GACAH,IAGF8C,GAAWvK,EAAgB2K,mCACzBlH,EACA4E,EACAiC,GAAwB,GAAG/D,OAC3B+D,GAAwB,GAAG/D,OAC3B+D,GAAwB,GAAG/D,OAC3B5C,GAEF0D,EACE/B,EACAC,EACAC,EACAC,EACA6B,EACAC,EACAgD,GACAD,GACA5C,MAGL,CAED,MAAMkD,GAAUxC,GAAc,MAAY,EACpCyC,GAAUxC,GAAe,MAAY,EAErCyC,GAAc,GACdC,GAAe,GACfC,GAAc,GACdC,GAAe,GAErB,IAAI7B,GAAgB8B,OAAOC,UACvB7B,IAAiBF,GAErB,MAAMgC,GAAoBxH,EAC1BwH,GAAkBxK,OAAS,EAE3B,MAAMyK,GAAYC,EAASA,UAACjG,MAAM6C,EAAWmD,WACvCE,GAAYC,EAASA,UAACnG,MAAM6C,EAAWuD,gBAEvCC,GAAQH,GAAUG,MAClBC,GAAQJ,GAAUI,MACxB,IAAIC,GAAOL,GAAUK,KACrB,MAAMC,GAAON,GAAUM,KAMvB,IAJID,GAAOC,KACTD,IAAQxF,EAAUA,WAAC0F,QAGhBjE,EAAI,EAAGA,EAAIvC,EAAQ1E,SAAUiH,EAChCiC,GAAIiC,KAAKC,MAAM1G,EAAQuC,IACnBiC,IAAKxB,GACPwC,GAAY1J,KAAKyG,GACjBiC,GAAI,GACKA,IAAKvB,GACdyC,GAAY5J,KAAKyG,GACjBiC,GAAItG,GAEJsG,GAAQ,EAAJA,GAAQc,GAGdtF,EAAQuC,GAAKiC,GAEbC,GAAIgC,KAAKC,MAAMzG,EAAQsC,IACnBkC,IAAKvB,GACPuC,GAAa3J,KAAKyG,GAClBkC,GAAI,GACKA,IAAKtB,GACdwC,GAAa7J,KAAKyG,GAClBkC,GAAIvG,GAEJuG,GAAQ,EAAJA,GAAQc,GAGdtF,EAAQsC,GAAKkC,GAEbH,EAASxD,EAAUA,WAACC,KAClB8C,EACAE,EACA7D,EAAaqC,GAAKrE,GAEhBoG,EAASR,KACXA,GAAgBQ,GAEdA,EAASN,KACXA,GAAgBM,GAGlBpE,EAAaqC,GAAK+B,EAElB/F,EAAoBoI,UAAY7F,EAAAA,WAAWC,KAAKwF,GAAMD,GAAM9B,GAAItG,GAChEK,EAAoBqI,SAAW9F,EAAAA,WAAWC,KAAKsF,GAAOD,GAAO3B,GAAIvG,GACjEK,EAAoB+F,OAASA,EAE7ByB,GAAUc,wBAAwBtI,EAAqBE,GAEvDqH,GAAkBhK,KAAK2C,EAAkB1C,GACzC+J,GAAkBhK,KAAK2C,EAAkBzC,GACzC8J,GAAkBhK,KAAK2C,EAAkBxB,GAG3C,MAAM6J,GAAiB7H,EAAAA,eAAe8H,aACpCjB,GACA5I,EAAAA,WAAW8J,KACX,EACAhI,GAEIiI,GAAsB9H,EAAAA,oBAAoB+H,cAC9CjB,GACAnC,GACAE,GACA+B,GACA7G,GAIIiI,GADW,IAAIC,sBAAoBrB,IACFsB,6DACrCP,GAAeQ,OACfxB,GACA,EACAgB,GAAeQ,OACfxD,GACA/E,GAGIwI,GAAcvD,GAAgBF,GAE9BvD,GAAW,IAAIiH,YACnBxH,EAAQ1E,OAAS2E,EAAQ3E,OAAS4E,EAAa5E,QAGjD,IAAKiH,EAAI,EAAGA,EAAIvC,EAAQ1E,SAAUiH,EAChChC,GAASgC,GAAKvC,EAAQuC,GAGxB,IAAIkF,GAAQzH,EAAQ1E,OAEpB,IAAKiH,EAAI,EAAGA,EAAItC,EAAQ3E,SAAUiH,EAChChC,GAASkH,GAAQlF,GAAKtC,EAAQsC,GAKhC,IAFAkF,IAASxH,EAAQ3E,OAEZiH,EAAI,EAAGA,EAAIrC,EAAa5E,SAAUiH,EACrChC,GAASkH,GAAQlF,GACdrE,GAAYgC,EAAaqC,GAAKuB,IAAkByD,GAGrD,MAAMG,GAAoBC,EAAAA,cAAcC,iBACtC5H,EAAQ1E,OACR0G,GAGF,IAAI6F,GACJ,GAAIzF,EAAkB,CACpB,MAAM0F,EAAc,IAAIC,WAAW5H,GACnC0C,EAAoB/G,KAClByE,GAASyH,OACTN,GAAkBM,OAClBF,EAAYE,QAEdH,GAAiBC,EAAYE,MACjC,MACInF,EAAoB/G,KAAKyE,GAASyH,OAAQN,GAAkBM,QAG9D,MAAO,CACLzH,SAAUA,GAASyH,OACnBH,eAAgBA,GAChB7F,QAAS0F,GAAkBM,OAC3BlE,cAAeA,GACfE,cAAeA,GACfwB,YAAaA,GACbC,aAAcA,GACdC,YAAaA,GACbC,aAAcA,GACdmB,eAAgBA,GAChBG,oBAAqBA,GACrBE,sBAAuBA,GAE3B"}