/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/10/17 19:32:52
 * @description content
 * @version 1.0
 * @CesiumVersion 1.96
 * */
import GaussKrugerTransform from '../../Core/GaussKrugerTransform';
import { CONSTANT_6_DEGREES, LABEL_OPTIONS, createLineGraphics, createLabels, getScale } from './GraticuleSettings.js';

import {
  Cartesian2,
  Cartesian3,
  Cartographic,
  Color,
  defaultValue,
  destroyObject,
  OriginLabelCollection,
  CustomPolylineCollection,
  PrimitiveCollection,
  Rectangle,
  Math as CesiumMath,
  SceneMode
} from 'cesium';

const { abs } = Math;

export default class MeasuredGraticule {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    this._viewer = options.viewer;
    this._color = defaultValue(options.color, Color.WHITE.withAlpha(0.5));
    this._color2D = defaultValue(options.color2D, Color.GRAY.withAlpha(0.8));
    this._zoneWide = defaultValue(options.zoneWide, 6);

    this._xGap = defaultValue(options.xGap, 1);
    this._yGap = defaultValue(options.xGap, 1);

    this._labelOptions = {
      ...LABEL_OPTIONS,
      ...options.labelOptions
    };

    this._screenOffsetX = defaultValue(options.screenOffsetX, 40);
    this._screenOffsetY = defaultValue(options.screenOffsetY, 20);

    /**
     * @description The position offset of the label.
     */
    this._labelOffset = defaultValue(options.labelOffset, 3);
    this._gaussTransform = new GaussKrugerTransform('CGCS2000');
    this._primitives = this._viewer.scene.primitives.add(new PrimitiveCollection());
    this._lines = this._primitives.add(new CustomPolylineCollection({ disableDepthTest: true }));
    this._labels = this._primitives.add(new OriginLabelCollection());
    this._show = true;

    this._centerZone = undefined;
    this.init();
    this._viewer._measuredGraticule = this;
  }

  changeSceneMode() {
    const lines = this._lines._polylines;
    const color = this.is3DMode() ? this._color : this._color2D;
    if (lines.length > 0) {
      lines.forEach((item) => {
        item.material.uniforms.color = color;
      });
    }
  }

  is3DMode() {
    return this._viewer.scene.mode === SceneMode.SCENE3D;
  }

  set color(value) {
    if (this.is3DMode()) {
      this._color = value;
    } else {
      this._color2D = value;
    }
    this.changeSceneMode();
  }

  get color() {
    return this.is3DMode() ? this._color : this._color2D;
  }

  get show() {
    return this._show;
  }

  set show(value) {
    if (this._show === value) {
      return;
    }

    this._show = value;
    this._primitives.show = value;
  }

  init() {
    const scene = this._viewer.scene;
    this._cameraListener = scene.camera.changed.addEventListener(() => {
      if (this._show) {
        this.render();
      }
    });

    this._canvasResizeListener = this._viewer.container.addEventListener('resize', () => {
      if (this._show) {
        this.render();
      }
    });
  }

  render() {
    this._lines.removeAll();
    this._labels.removeAll();

    const zoneWide = this._zoneWide;
    let { west, north, east, south } = this._getExtentView();

    west = CesiumMath.toDegrees(west);
    east = CesiumMath.toDegrees(east);
    north = CesiumMath.toDegrees(north);
    south = CesiumMath.toDegrees(south);

    let westCenter = parseInt(abs(west) / zoneWide + 1) * zoneWide - 3;
    westCenter = west > 0 ? westCenter : -westCenter;
    let eastCenter = parseInt(abs(east) / zoneWide + 1) * zoneWide - 3;
    eastCenter = east > 0 ? eastCenter : -eastCenter;

    // add lines
    switch (eastCenter - westCenter) {
      case 24:
        this._drawLines(westCenter, west, westCenter + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(westCenter + 6, westCenter + 3, westCenter + 6 + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(westCenter + 12, westCenter + 9, westCenter + 12 + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(westCenter + 18, westCenter + 15, westCenter + 18 + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(eastCenter, eastCenter - 3, east, north, south);
        break;
      case 18:
        this._drawLines(westCenter, west, westCenter + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(westCenter + 6, westCenter + 3, westCenter + 6 + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(westCenter + 12, westCenter + 9, westCenter + 12 + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(eastCenter, eastCenter - 3, east, north, south);
        break;
      case 12:
        this._drawLines(westCenter, west, westCenter + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(westCenter + 6, westCenter + 3, westCenter + 6 + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(eastCenter, eastCenter - 3, east, north, south);
        break;
      case 6:
        this._drawLines(westCenter, west, westCenter + CONSTANT_6_DEGREES, north, south, true);
        this._drawLines(eastCenter, westCenter + 3, east, north, south);
        break;
      case 0:
        this._drawLines(westCenter, west, east, north, south);
        break;
    }
  }

  isDestroyed() {
    return false;
  }

  destroy() {
    this._cameraListener();
    this._canvasResizeListener();
    this._viewer.scene.primitives.remove(this._primitives);
    this._show = false;
    return destroyObject(this);
  }

  _drawLines(centerMeridian, west, east, north, south, isRemoveOverlap = false) {
    let westTemp = centerMeridian - 3;
    westTemp = west < westTemp ? westTemp : west;
    let eastTemp = centerMeridian + CONSTANT_6_DEGREES;
    eastTemp = east > eastTemp ? eastTemp : east;

    let zone = abs(parseInt(centerMeridian / 6)) + 1;
    zone = centerMeridian > 0 ? zone : 60 - zone + 1;

    const { x: xMin, y: yMin } = this._gaussTransform._getXY(westTemp, south, centerMeridian);
    const { x, y } = this._gaussTransform._getXY(eastTemp, north, centerMeridian);

    const scale = getScale(y - yMin, 0);
    const startX = (parseInt(xMin / scale) - 1) * scale;
    const endX = (parseInt(x / scale) + 1) * scale;
    const startY = (parseInt(yMin / scale) - 1) * scale;
    const endY = (parseInt(y / scale) + 1) * scale;
    const numX = (endX - startX) / scale;
    const numY = (endY - startY) / scale;

    const offset = this._labelOffset;
    let xyPositions,
      positions,
      labelIndex,
      countX = 0,
      countY = 0;
    const color = this._viewer.scene.mode === SceneMode.SCENE3D ? this._color : this._color2D;

    // add meridian
    for (let i = startX; i <= endX; i += scale) {
      positions = [];
      xyPositions = [];

      for (let j = startY; j <= endY; j += scale) {
        const { longitude, latitude } = this._gaussTransform._getLonLat(i, j, centerMeridian);
        positions.push(Cartesian3.fromDegrees(longitude, latitude));
        xyPositions.push([i, j]);
      }
      if (!(numX - countX < 2 && isRemoveOverlap)) {
        this._lines.add(createLineGraphics(positions, color));
        if (countX % (this._xGap + 1) === 0) {
          labelIndex = positions.length - 1 - offset;
          if (xyPositions[labelIndex]) {
            createLabels(this, positions[labelIndex], `${zone}${xyPositions[labelIndex][0]}`);
          }
        }
      }
      countX++;
    }

    // add parallel
    for (let j = startY; j <= endY; j += scale) {
      positions = [];
      xyPositions = [];
      countX = 0;
      for (let i = startX; i <= endX; i += scale) {
        if (!(numX - countX < 2 && isRemoveOverlap)) {
          const { longitude, latitude } = this._gaussTransform._getLonLat(i, j, centerMeridian);
          positions.push(Cartesian3.fromDegrees(longitude, latitude));
        }
        xyPositions.push([i, j]);
        countX++;
      }
      this._lines.add(createLineGraphics(positions, color));
      if ((numY - countY) % (this._yGap + 1) === 0) {
        labelIndex = offset;
        if (xyPositions[labelIndex]) {
          createLabels(this, positions[labelIndex], `${xyPositions[labelIndex][1]}`, false);
        }
      }
      countY++;
    }
  }

  _getExtentView() {
    const camera = this._viewer.scene.camera;
    const canvas = this._viewer.scene.canvas;
    const ellipsoid = this._viewer.scene.globe.ellipsoid;
    const corners = [
      camera.pickEllipsoid(new Cartesian2(0, 0), ellipsoid),
      camera.pickEllipsoid(new Cartesian2(canvas.clientWidth, 0), ellipsoid),
      camera.pickEllipsoid(new Cartesian2(0, canvas.clientHeight), ellipsoid),
      camera.pickEllipsoid(new Cartesian2(canvas.clientWidth, canvas.clientHeight), ellipsoid)
    ];

    for (let index = 0; index < 4; index++) {
      if (corners[index] === undefined) {
        return Rectangle.MAX_VALUE;
      }
    }

    return Rectangle.fromCartographicArray(ellipsoid.cartesianArrayToCartographicArray(corners));
  }

  _screenCenterPosition() {
    const canvas = this._viewer.scene.canvas;
    const center = new Cartesian2(Math.round(canvas.clientWidth / 2), Math.round(canvas.clientHeight / 2));
    const cartesian = this._viewer.scene.camera.pickEllipsoid(center);
    if (cartesian) {
      return cartesian;
    }
    return Cartesian3.fromDegrees(0, 0, 0);
  }

  _getScreenViewRange() {
    const camera = this._viewer.scene.camera;
    const canvas = this._viewer.scene.canvas;
    const ellipsoid = this._viewer.scene.globe.ellipsoid;
    //const height = camera.positionCartographic.height;

    // let offsetX = 40,
    //   offsetY = 20;
    // if (height < 36000) {
    //   offsetX = 60;
    // }

    // canvas pias offset
    const offsetX = this._screenOffsetX;
    const offsetY = this._screenOffsetY;

    const corners = {
      north: camera.pickEllipsoid(new Cartesian2(canvas.clientWidth / 2, offsetY), ellipsoid),
      south: camera.pickEllipsoid(new Cartesian2(canvas.clientWidth / 2, canvas.clientHeight - offsetY), ellipsoid),
      west: camera.pickEllipsoid(new Cartesian2(offsetX, canvas.clientHeight / 2), ellipsoid),
      east: camera.pickEllipsoid(new Cartesian2(canvas.clientWidth - offsetX, canvas.clientHeight / 2), ellipsoid)
    };

    return {
      north: corners.north ? Cartographic.fromCartesian(corners.north).latitude : undefined,
      south: corners.south ? Cartographic.fromCartesian(corners.south).latitude : undefined,
      west: corners.west ? Cartographic.fromCartesian(corners.west).longitude : undefined,
      east: corners.east ? Cartographic.fromCartesian(corners.east).longitude : undefined
    };
  }
}
