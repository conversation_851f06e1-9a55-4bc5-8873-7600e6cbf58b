import { postRequest } from '@api/instance/systemInstance.ts';

export interface PositionOpt {
  altitude: number;
  latitude: number;
  longitude: number;
}

export interface EntityOpt {
  entityId: string;
  entityName: string;
  side: string;
  locationLLA: PositionOpt;
  heading?: number;
  pitch?: number;
}

export interface TrackOpt {
  entityId: string;
  entityName: string;
  side: string;
  tracks: { entities: DetectedEntity[] }[];
}

export interface DetectedEntity {
  entityId: string;
  entityName: string;
  side: string;
  locationLLA: PositionOpt;
  speed: number;
}

export interface SituationData {
  TimeStamp: number;
  radarStatusList: EntityOpt[];
  shipStatusList: EntityOpt[];
  trackList: TrackOpt[];
}

export function querySituationApi() {
  return postRequest<SituationData>('/situation/ship_rcs/getSituation');
}

export function query3DRadarApi(data: { entityId: string; locationLLA: PositionOpt }) {
  return postRequest<Array<number[]>>('/situation/ship_rcs/3D', data);
}

export function query2DRadarApi(data: { entityId: string; locationLLA: PositionOpt }) {
  return postRequest<Array<number[]>>('/situation/ship_rcs/2D', data);
}
