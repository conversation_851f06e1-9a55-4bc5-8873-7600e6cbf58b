/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/28 10:40:03
 * @description FovFeature
 * @version 1.0
 * */
import AbstractFeature from './AbstractFeature.js';
import { ConePrimitive } from 'isim-cesium';

export default class ConeFeature extends AbstractFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this.create();
    //  高度计算方式
    this.heightReference = this._options?.heightReference ?? this.heightReference;
  }

  create() {
    const { length, radius, color, outline, outlineColor, slices, materialType, uniforms } = this._options;
    this.primitive = new ConePrimitive({
      length,
      radius,
      color,
      outline,
      outlineColor,
      slices,
      materialType,
      uniforms
    });
  }

  update(time) {
    // console.log('this._options', this._options);
    this._options.position = this.entity.position.getValue(time);
    if (this.primitive) {
      this._updateOptions(time, this.primitive);
      this._updateTransform(time);
      this.primitive._update = true;
    }
  }
}
