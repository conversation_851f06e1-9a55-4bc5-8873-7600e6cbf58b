import { Gantt } from './Gantt.ts';
import { GanttScrollBarBase } from './GanttScrollBarBase.ts';

type onScrollFunctionType = (scrollTop: number, percentY: number) => void;

export class GanttScrollBar extends GanttScrollBarBase {
  scrollEvent: onScrollFunctionType[] = [];

  constructor(gantt: Gantt) {
    super(gantt);

    this.registerDragEvent();
  }

  checkStartY() {
    if (Math.abs(this.startY) >= this.maxY) {
      this.startY = -this.maxY;
    }
    if (this.startY > 0) this.startY = 0;
    this.calcBarStartY();
    // this.emitScroll();
  }

  scrollTo(toTop: boolean) {
    if (toTop) {
      this.startY -= this.scrollStep;
      if (Math.abs(this.startY) >= this.maxY) {
        this.startY = -this.maxY;
      }
      this.calcBarStartY();
      // this.emitScroll();
      return;
    }
    this.startY += this.scrollStep;
    if (this.startY > 0) this.startY = 0;
    this.calcBarStartY();
    // this.emitScroll();
  }
  setScrollY(currentY: number) {
    if (currentY < 0) currentY = 0;
    if (currentY > this.scrollBarMaxY) currentY = this.scrollBarMaxY;
    if (this.scrollBarStartY !== currentY) {
      this.scrollBarStartY = currentY;
      this.calcStartY();
    }

    // this.emitScroll();
  }

  scrollToByRowIndex(index: number) {
    const y = (index * this.height) / this.graphic.rowCollection.size;
    this.setScrollY(y);
  }

  onScroll(fn: onScrollFunctionType) {
    this.scrollEvent.push(fn);
  }
  emitScroll() {
    const maxY = this.height - this.scrollBarHeight;
    const percentY = this.startY / maxY;
    this.scrollEvent.forEach((fn) => {
      fn(this.startY, percentY);
    });
  }

  pick(x: number, y: number) {
    x *= window.devicePixelRatio;
    y *= window.devicePixelRatio;
    return x <= this.scrollBarStopX && x >= this.scrollBarStartX && y >= this.scrollBarStartY && y <= this.scrollBarStopY;
  }

  // 注册拖动滚动条事件
  registerDragEvent() {
    const dom = this.graphic.canvas;
    let clientY = 0;
    let currentY = 0;
    // let currentOptionY = 0;

    let flag = false;
    dom.addEventListener('mousedown', (e: MouseEvent) => {
      if (!this.pick(e.offsetX, e.offsetY)) return;
      currentY = this.scrollBarStartY;
      // currentOptionY = this.y;
      clientY = e.clientY;
      flag = true;
      // document.addEventListener('mousemove', mouseMove);
      // document.addEventListener('mouseup', mouseUp);
    });
    const mouseMove = (e: MouseEvent) => {
      if (this.pick(e.offsetX, e.offsetY)) {
        this.isSelected = true;
        this.render();
      } else if (!flag) {
        this.isSelected = false;
        this.render();
      }

      if (!flag) return;
      const offsetY = e.clientY - clientY;
      this.setScrollY(currentY + offsetY * window.devicePixelRatio);
      // console.log(this.startY);
      this.graphic.render();
    };

    const mouseUp = (_e: MouseEvent) => {
      flag = false;
      // document.removeEventListener('mousemove', mouseMove);
      // document.removeEventListener('mouseup', mouseUp);
    };
    document.addEventListener('mousemove', mouseMove);
    document.addEventListener('mouseup', mouseUp);
  }
  // 缩放时，先计算滚动页面高度，再计算滚动条高度
  resize() {
    this.calcStartY();
    this.checkStartY();
  }

  render() {
    const ctx = this.ctx;
    // ctx.fillStyle = '#cbcbcb';
    ctx.fillStyle = this.color;
    ctx.fillRect(this.scrollBarStartX, this.scrollBarStartY, this.width, this.scrollBarHeight);
    this.emitScroll();
    // console.log('**************');
    // console.log('this.scrollBarStartX', this.scrollBarStartX);
    // console.log('this.scrollBarStopX', this.scrollBarStopX);
    // console.log('this.scrollBarStartY', this.scrollBarStartY);
    // console.log('this.scrollBarHeight', this.scrollBarHeight);
    // console.log('this.width', this.width);
    // console.log('this.gantt.width', this.graphic.width);
  }
}
