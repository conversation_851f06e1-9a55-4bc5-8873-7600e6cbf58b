<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : right-menu.vue
-->
<template>
  <n-dropdown :options="menuOptions" :show="menuShow" size="small" placement="bottom-start" trigger="manual" :x="menuX" :y="menuY" />
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'SimRightMenu'
});
</script>

<script setup lang="ts">
import { RightMenuItem, useRightMenuOptionStore } from './use-right-menu-option.ts';
import { getViewerContainer, onLeftDown, onRightClick, screenToDegrees } from 'isim-cesium';
import { Cartesian2, Entity } from 'cesium';
import { Ref, ref } from 'vue';
import { PositionedEvent } from 'isim-cesium/raw-api/event-api/event-core.js';
const rightMenuOption = useRightMenuOptionStore();
const { menuY, menuX, menuShow, rightClickCallback, getterEntityMenuOptions, getterEmptyMenuOptions } = storeToRefs(rightMenuOption);
const currentEntity = shallowRef<Entity | undefined>(undefined);
const currentMousePos = shallowRef<Cartesian2>(new Cartesian2());

const setCurrentMousePos = (data: Cartesian2 = new Cartesian2()) => {
  currentMousePos.value = data;
};

interface FilterParams {
  position: Cartesian2;
  entity?: Entity;
  menuShow: Ref<boolean>;
}
const bindClickEvent = (data: RightMenuItem[], filterParam: FilterParams) => {
  return data
    .map((item) => {
      const { filter: itemFilter, ...others } = item;
      const _opt: RightMenuItem = {
        ...others
      };
      if (item.click) {
        _opt.props = {
          onClick(e: MouseEvent) {
            item.click?.(filterParam, e);
            setDropdownShow(false);
          }
        };
      }
      if (item.children) {
        _opt.children = bindClickEvent(item.children, filterParam);
      }
      const filterVal = itemFilter ? itemFilter(filterParam) : true;
      if (filterVal) {
        return _opt;
      }
    })
    .filter(Boolean) as RightMenuItem[];
};

const menuOptions = ref<RightMenuItem[]>([]);

const setMenuOptions = () => {
  const entity = toRaw(currentEntity.value);
  const _options = currentEntity.value ? rightMenuOption.getterEntityMenuOptions : rightMenuOption.getterEmptyMenuOptions;
  const filterParam: FilterParams = {
    position: currentMousePos.value,
    entity,
    menuShow
  };
  menuOptions.value = bindClickEvent(_options, filterParam) as any[];
};

const setDropdownShow = (show = true) => {
  // 如果菜单项为空 则不弹出菜单
  if (toRaw(menuOptions.value).length === 0) {
    menuShow.value = false;
    return;
  }
  menuShow.value = show;
};
const setDropdownPos = (pos: PositionedEvent) => {
  const degrees = screenToDegrees(pos.position);
  if (!degrees) {
    setDropdownShow(false);
    return;
  }
  const { x, y } = pos.position;
  const container = getViewerContainer();
  const { y: containerY } = container.getBoundingClientRect();
  menuX.value = x;
  menuY.value = y + containerY;
};
const entityClose = onRightClick(
  ({ entity, position: e }) => {
    currentEntity.value = entity;
    setCurrentMousePos(e.position);
    setMenuOptions();
    setDropdownPos(e);
    executeCallback({ entity: entity, position: toRaw(currentMousePos.value) });
    setDropdownShow();
  },
  {
    isEntity: true
  }
);
const executeCallback = (data: { entity?: Entity; position: Cartesian2 }) => {
  rightClickCallback.value.forEach((item) => {
    item(data);
  });
};
const emptyClose = onRightClick(
  ({ position: e }) => {
    currentEntity.value = undefined;
    setCurrentMousePos(e.position);
    setMenuOptions();
    setDropdownPos(e);
    executeCallback({ position: toRaw(currentMousePos.value) });
    setDropdownShow();
  },
  {
    isEmpty: true
  }
);
const leftClose = onLeftDown(() => {
  setDropdownShow(false);
  currentEntity.value = undefined;
  setCurrentMousePos();
});

watch(
  () => [getterEntityMenuOptions.value, getterEmptyMenuOptions.value],
  () => {
    setMenuOptions();
  }
);

onBeforeUnmount(() => {
  entityClose();
  emptyClose();
  leftClose();
});
</script>

<style scoped lang="less"></style>
