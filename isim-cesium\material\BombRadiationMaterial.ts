/**
 * @Author: 宋计民
 * @Date: 2023/10/16 11:22
 * @Version: 1.0
 * @Content: BombRadiationType.ts
 */
import { Color, Material } from 'cesium';
import BOMB_RADIATION_SOURCE from './Sharder/BombRadiationMaterial.glsl?raw';

export const BombRadiationMaterial = new Material({
  fabric: {
    type: 'BombRadiation',
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 3.0,
      count: 2,
      gradient: 0.5
    },
    source: BOMB_RADIATION_SOURCE
  },
  translucent: function (_material) {
    return true;
  }
});
