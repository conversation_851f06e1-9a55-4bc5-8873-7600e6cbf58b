import type { GanttDataType, GanttMode } from './GanttType';

export interface ConnectLineType {
  type: 'bezier' | 'line';
  show: boolean;
}

export type rowFormatLabelType = (data: GanttDataType) => string;

export interface GanttOptionType {
  container: HTMLCanvasElement;
  rowHeight: number;
  rowGap: number;
  rowFormatLabel: rowFormatLabelType;
  fontSize: number;
  // width: number;
  // height: number;
  minTime: number;
  maxTime: number;
  currentTime: number;
  showStartTime: number;
  showEndTime: number;
  _interval: number;
  minInterval: number;
  mode: GanttMode;
  splitLine: boolean;
  connectLine: boolean | ConnectLineType;
  startX: number;
  startY: number;
  scrollStep: number;
}

export class GanttOption {
  options: GanttOptionType;
  constructor(options: GanttOptionType) {
    this.options = options;
    this.options.showStartTime = this.options.showStartTime || this.options.minTime;
    this.options.showEndTime = this.options.showEndTime || this.options.maxTime;
    this.calculateInterval();
  }
  // 甘特图宽度
  get width() {
    return this.options.container.width;
  }
  // 开始时间
  get minTime() {
    return this.options.minTime;
  }
  // 结束时间
  get maxTime() {
    return this.options.maxTime;
  }
  // 当前时间标记的时间
  get currentTime() {
    return this.options.currentTime;
  }
  // 甘特图高度
  get height() {
    return this.options.container.height;
  }
  // 1px所占时间段毫秒数
  get interval() {
    return this.options._interval;
  }
  // 节点高度
  get rowHeight() {
    return this.options.rowHeight * window.devicePixelRatio;
  }
  // 节点间间隔
  get rowGap() {
    return this.options.rowGap * window.devicePixelRatio;
  }
  // 任务名称文字大小
  get fontSize() {
    return this.options.fontSize * window.devicePixelRatio;
  }
  // 开始时间的X轴位置
  get startX() {
    return this.options.startX;
  }
  set startX(value: number) {
    this.options.startX = value;
  }
  // 当前第一个节点的Y轴位置,同样也是Y轴滚动的位置
  get startY() {
    return this.options.startY;
  }

  set startY(value: number) {
    // console.log('startY', value);
    // if (value <= 0) value = 0;
    // const maxY = this.maxTime - this.height;
    // if (value > maxY) value = maxY;
    this.options.startY = value;
  }

  /**
   * 显示的结束时间和开始时间之间的间隔
   */
  get distance() {
    return this.showEndTime - this.showStartTime;
  }
  // 展示区域的开始时间
  get showStartTime() {
    return this.options.showStartTime;
  }
  set showStartTime(time: number) {
    if (time <= this.minTime) time = this.minTime;
    this.options.showStartTime = time;
  }
  // 展示区域的结束时间
  get showEndTime() {
    return this.options.showEndTime;
  }

  set showEndTime(time: number) {
    if (time >= this.maxTime) time = this.maxTime;
    this.options.showEndTime = time;
  }
  // 是否展示分割线
  get showSplitLine() {
    return this.options.splitLine;
  }

  get mode() {
    return this.options.mode;
  }
  // 鼠标滚动轴的滚动步长
  get scrollStep() {
    return this.options.scrollStep;
  }
  // 当前最小时间到最大时间->所占宽度
  get currentWidth() {
    return (this.width * (this.maxTime - this.minTime)) / (this.showEndTime - this.showStartTime);
  }
  // 节点名称格式化函数
  get rowFormatLabel() {
    return this.options.rowFormatLabel;
  }

  // 计算当前minTime所处的X轴坐标
  calcStartX() {
    this.startX = (this.currentWidth * (this.minTime - this.showStartTime)) / (this.maxTime - this.minTime);
    // console.log('startX-11', this.startX);
  }

  calculateInterval() {
    this.options._interval = (this.showEndTime - this.showStartTime) / this.width;
  }

  get connectLine(): ConnectLineType {
    if (typeof this.options.connectLine === 'boolean') {
      return {
        show: this.options.connectLine,
        type: 'bezier'
      };
    }
    return this.options.connectLine;
  }
}
