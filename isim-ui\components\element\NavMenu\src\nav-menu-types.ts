import { PropType } from 'vue';

/**
 * @Author: 任强
 * @Date: 2023/4/10 20:41
 * @Version: 1.0
 * @Content:
 */
export interface MenuOpt {
  menuId: string;
  menuName: string;
  icon: string;
  orderNo: number;
  routePath: string;
  type?: string;
  pid: string;
  children?: MenuOpt[];
}
export function navMenuProps() {
  return {
    menuList: {
      type: Array as PropType<MenuOpt[]>,
      default: () => []
    }
  };
}
