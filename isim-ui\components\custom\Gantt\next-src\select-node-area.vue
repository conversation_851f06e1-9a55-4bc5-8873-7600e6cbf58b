<!--
* @Author: 宋计民
* @Date: 2024/5/27
* @Version: 1.0
* @Content: select-node-area.vue
-->
<template>
  <div
    class="SelectNodeArea"
    :style="{
      top: data.y + 'px',
      left: startX + '%',
      width: width + '%'
    }"
  >
    {{ data.y }}
  </div>
</template>

<script setup lang="ts">
import { SelectNodeAreaType } from './graphic';

defineOptions({
  name: 'SelectNodeArea'
});
const props = defineProps({
  data: {
    type: Object as PropType<SelectNodeAreaType>,
    required: true
  },
  showStartTime: {
    type: Number,
    default: 0
  },
  showEndTime: {
    type: Number,
    default: 0
  }
});
const diffValue = computed(() => {
  return props.showEndTime - props.showStartTime;
});

const startX = computed(() => {
  const left = (100 * (props.data.data.startTime - props.showStartTime)) / diffValue.value;
  return left;
});
const stopX = computed(() => {
  const right = (100 * (props.data.data.endTime - props.showStartTime)) / diffValue.value;
  return right;
});
const width = computed(() => {
  return stopX.value - startX.value;
});
</script>

<style scoped lang="less">
.SelectNodeArea {
  background-color: #000;
}
</style>
