import { GanttOption } from './GanttOption.ts';
import { Gantt } from './Gantt.ts';

/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
export class GanttScrollBarBase extends GanttOption {
  graphic: Gantt;
  ctx: CanvasRenderingContext2D;
  scrollBarWidth: number = 7;
  scrollBarStartY: number = 0;
  scrollBarMinHeight = 30;
  isSelected: boolean = false;
  style: { fillStyle: string; selectedFillStyle: string };

  constructor(gantt: Gantt) {
    super(gantt.options);
    this.graphic = gantt;
    this.ctx = gantt.ctx;
    this.style = {
      fillStyle: '#cbcbcb',
      selectedFillStyle: '#fff'
    };
  }

  get color() {
    return this.isSelected ? this.style.selectedFillStyle : this.style.fillStyle;
  }
  // 滚动条Y轴可移动最大位置
  get scrollBarMaxY() {
    // return this.totalHeight - this.height;
    return this.height - this.scrollBarHeight;
  }
  // 任务节点Y轴可移动的最大位置
  get maxY() {
    return this.totalHeight - this.height;
  }
  // 滚动条顶部的位置
  get scrollBarStartX() {
    return this.width - this.scrollBarWidth;
  }
  // 计算滚动条Y轴位置
  calcBarStartY() {
    let startY = Math.abs(this.startY);
    if (startY > this.maxY) {
      startY = this.maxY;
    }
    // 处理 可渲染任务部分未占满canvas容器的情况
    // Y轴最大值为 节点总高度-容器高度，所以可滚动区域为负数
    // 在这种情况下滚动条和滚动页面应该处于最顶部的位置
    if (this.maxY < 0) startY = 0;

    this.scrollBarStartY = (startY * this.scrollBarMaxY) / this.maxY;
  }

  // 计算任务节点Y轴当前位置
  calcStartY() {
    // if (this.maxY < 0) return (this.startY = 0);
    const startY = -((this.scrollBarStartY * this.maxY) / this.scrollBarMaxY);
    this.startY = startY;
  }

  // 滚动条底部的位置
  get scrollBarStopY() {
    return this.scrollBarStartY + this.scrollBarHeight;
  }

  get scrollBarStopX() {
    return this.width;
  }
  get length() {
    return this.graphic.rowCollection.size;
  }
  // 任务节点最大可移动高度
  get totalHeight() {
    return this.length * (this.rowHeight + this.rowGap);
  }
  // get height() {
  //   return (this.height / this.totalHeight) * this.height;
  // }

  // 滚动条当前高度
  get scrollBarHeight() {
    let height = (this.height / this.totalHeight) * this.height;
    height = height >= this.scrollBarMinHeight ? height : this.scrollBarMinHeight;
    return height;
  }
}
