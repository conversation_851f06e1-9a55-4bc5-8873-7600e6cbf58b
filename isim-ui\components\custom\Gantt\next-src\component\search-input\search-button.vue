<!--
* @Author: 宋计民
* @Date: 2024/5/28
* @Version: 1.0
* @Content: search-button.vue
-->
<template>
  <div @click.prevent.stop="show = true">搜索</div>
  <search-modal v-if="show" v-model="show" :data="data" @change="emits('change', $event)" />
</template>

<script setup lang="ts" generic="T extends GanttDataBase">
import { GanttDataBase, GanttDataType } from '../../graphic';
import SearchModal from './search-modal.vue';

defineOptions({
  name: 'SearchButton'
});
defineProps({
  data: {
    type: Object as PropType<GanttDataType<T>[]>,
    default: () => {
      return [];
    }
  }
});
const emits = defineEmits(['change']);

const show = ref(false);
</script>

<style scoped lang="less"></style>
