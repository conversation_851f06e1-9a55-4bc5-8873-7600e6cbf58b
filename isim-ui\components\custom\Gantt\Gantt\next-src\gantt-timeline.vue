<!--
* @Author: 宋计民
* @Date: 2024/4/24
* @Version: 1.0
* @Content: gantt-timeline.vue
-->
<template>
  <div class="sim-gantt-timeline" ref="timelineRef">
    <span class="sim-gantt-timeline__text" v-for="item in timeRange" :key="item.timeSecond" :style="{ transform: item.transform }">{{
      item.time
    }}</span>
    <div class="sim-gantt-timeline__tick sim-gantt-timeline__short"></div>
    <div class="sim-gantt-timeline__tick sim-gantt-timeline__long"></div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';

defineOptions({
  name: 'SimGanttTimeline'
});

const startTime = defineModel<number>('startTime', { required: true, default: Date.now() });
// const endTime = defineModel<number>('endTime', { required: true });

const DEFAULT_INTERVAL = 200;
const INTERVAL_STEP = 20;
const timelineRef = ref<HTMLDivElement>();
const interval = defineModel<number>('interval', { default: DEFAULT_INTERVAL });
const shortTickRange = computed(() => interval.value / INTERVAL_STEP);
const translateX = ref(0);

const timeRange = computed(() => {
  const arr = [];
  for (let i = 0; i <= 5; i++) {
    const timeSecond = startTime.value + interval.value * i;
    const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
    const transform = `translateX(${interval.value * i}px)`;
    arr.push({ timeSecond, time, transform });
  }
  return arr;
});
const mouseWheelEvent = (e: WheelEvent) => {
  const delta = e.deltaY;
  const intervalVal = toValue(interval);
  // @ts-ignore
  if (e.offsetX < e.currentTarget.clientWidth / 2) {
    translateX.value = translateX.value <= -DEFAULT_INTERVAL ? 0 : translateX.value + delta;
  }

  /**
   * 向上滚动
   */
  if (delta > 0) {
    interval.value = intervalVal <= DEFAULT_INTERVAL ? DEFAULT_INTERVAL : intervalVal - INTERVAL_STEP;
    return;
  }

  // If the interval value is greater than or equal to DEFAULT_INTERVAL * 1.5, set the interval value to DEFAULT_INTERVAL. Otherwise, add INTERVAL_STEP to the interval value.
  interval.value = intervalVal >= DEFAULT_INTERVAL * 1.5 ? DEFAULT_INTERVAL : intervalVal + INTERVAL_STEP;
};
onMounted(() => {
  timelineRef.value.addEventListener('wheel', mouseWheelEvent);
});
onBeforeUnmount(() => {
  timelineRef.value.removeEventListener('wheel', mouseWheelEvent);
});
</script>

<style scoped lang="less">
.sim-gantt-timeline {
  position: relative;
  overflow-x: hidden;
  --translate-x: v-bind(translateX);
  --short-range: v-bind(shortTickRange);
  --long-range: v-bind(interval);
  transform: translateX(calc(var(--translate-x) * -1px));
  height: 30px;
  font-size: 12px;
  width: calc(100% + v-bind(DEFAULT_INTERVAL) * 1px);
  &__text {
    position: absolute;
    left: 0;
    bottom: 3px;
  }
  &__tick {
    position: absolute;
    left: 0;
    bottom: 3px;
    width: 100%;
    background-repeat: repeat-x;
  }
  &__short {
    background-image: linear-gradient(90deg, fade(white, 50%) 1px, transparent 1px, transparent 100%);
    height: 5px;
    background-size: calc(var(--short-range) * 1px) 100%;
  }
  &__long {
    background-image: linear-gradient(90deg, fade(white, 50%) 1px, transparent 1px, transparent 100%);
    height: 10px;
    background-size: calc(var(--long-range) * 1px) 100%;
  }
}
</style>
