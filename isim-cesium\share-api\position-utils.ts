import { isArray } from '@/utils';
import {
  Cartesian2,
  Cartesian3,
  Cartographic,
  defined,
  EllipsoidGeodesic,
  IntersectionTests,
  Math as CesiumMath,
  Plane,
  Ray,
  Scene,
  SceneMode,
  SceneTransforms
} from 'cesium';
import { getScene, getViewer, ViewerNameType } from 'isim-cesium';

export function degreesToCoordinates(data: DegreesPosType | DegreesPosType[]) {
  if (!isArray(data)) {
    data = [data];
  }

  return data.map((item) => [item.longitude, item.latitude]);
}

/**
 * 笛卡尔转经纬高
 * @param cartesian3
 * @param viewerName
 * @constructor
 */
export function cartesian3ToDegrees(cartesian3: Cartesian3, viewerName?: ViewerNameType): DegreesPosType {
  const viewer = getViewer(viewerName);
  const ellipsoid = viewer.scene.globe.ellipsoid;
  const cartographic = ellipsoid.cartesianToCartographic(cartesian3);
  let longitude = +CesiumMath.toDegrees(cartographic.longitude).toFixed(6);
  let latitude = +CesiumMath.toDegrees(cartographic.latitude).toFixed(6);
  let height = +cartographic.height.toFixed(6);

  if (Number.isInteger(longitude)) {
    longitude = +longitude.toFixed(1);
  }
  if (Number.isInteger(latitude)) {
    latitude = +latitude.toFixed(1);
  }
  if (Number.isInteger(height)) {
    height = +height.toFixed(1);
  }
  return {
    longitude: Number(longitude),
    latitude: Number(latitude),
    height: Number(height)
  };
}

/**
 * 屏幕坐标转换笛卡尔坐标
 */
export function screenPosToCartesian(cartesian2: Cartesian2 | MouseEvent, viewerName?: ViewerNameType): Cartesian3 | undefined {
  const viewer = getViewer(viewerName);
  let _pos = new Cartesian2(cartesian2.x, cartesian2.y);
  if (cartesian2 instanceof MouseEvent) {
    _pos = new Cartesian2(cartesian2.offsetX, cartesian2.offsetY);
  }

  // 如果没有渠道屏幕坐标 取屏幕中间作为点的绘制坐标
  return viewer.camera.pickEllipsoid(_pos, viewer.scene.globe.ellipsoid);
}

/**
 * 屏幕坐标转经纬高
 * @param cartesian2
 * @param viewerName
 * @returns
 */
export function screenPosToLBH(cartesian2: Cartesian2 | MouseEvent, viewerName?: ViewerNameType) {
  const viewer = getViewer(viewerName);
  const _cartesian = screenPosToCartesian(cartesian2, viewer);
  if (_cartesian) {
    return cartesian3ToDegrees(_cartesian, viewer);
  }
  return undefined;
}

export const screenToDegrees = screenPosToLBH;

export interface DegreesPosType {
  longitude: number;
  latitude: number;
  height: number;
}

/**
 * 计算两点的地面距离
 * @param start
 * @param stop
 * @param viewerName
 */
export function computedGroundDistanceByDegrees(start: DegreesPosType, stop: DegreesPosType, viewerName?: ViewerNameType) {
  const { longitude: lon1, latitude: lat1, height: alt1 } = start;
  const { longitude: lon2, latitude: lat2, height: alt2 } = stop;
  const p1 = Cartographic.fromDegrees(lon1, lat1, alt1);
  const p2 = Cartographic.fromDegrees(lon2, lat2, alt2);
  const scene = getScene(viewerName);
  const geodesic = new EllipsoidGeodesic(p1, p2, scene.globe.ellipsoid);
  return geodesic.surfaceDistance;
}

/**
 * 计算两点的地面距离
 * @param start
 * @param stop
 * @param viewerName
 */
export function computedGroundDistance(start: Cartesian3, stop: Cartesian3, viewerName?: ViewerNameType) {
  const p1 = Cartographic.fromCartesian(start);
  const p2 = Cartographic.fromCartesian(stop);
  const scene = getScene(viewerName);
  const geodesic = new EllipsoidGeodesic(p1, p2, scene.globe.ellipsoid);
  return Math.floor(geodesic.surfaceDistance) / 1e3;
}

export function WGS84TransformToJ2000(position: DegreesPosType) {
  // @ts-ignore
  proj4.defs('EPSG:4326', '+proj=longlat +datum=WGS84 +no_defs');
  // @ts-ignore
  proj4.defs('EPSG:4610', '+proj=longlat +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +no_defs');
  // @ts-ignore
  const cartesian2000 = proj4('EPSG:4326', 'EPSG:4610', [position.longitude, position.latitude]);
  return cartesian2000;
}

const rayScratch = new Ray();
/**
 * 通过屏幕坐标得到笛卡尔坐标
 * @param scene
 * @param eventPos
 */
export function getWorldPosition(scene: Scene, eventPos: Cartesian2) {
  if (scene.pickPositionSupported && scene.mode !== SceneMode.SCENE2D) {
    return scene.pickPosition(eventPos);
  }
  if (!scene.globe) {
    return undefined;
  }
  const ray = scene.camera.getPickRay(eventPos, rayScratch);
  const position = scene.globe.pick(ray, scene);
  if (position) {
    return position;
  }
  return scene.camera.pickEllipsoid(eventPos, scene.globe.ellipsoid);
}
const scratch = new Cartesian3();
const cart2 = new Cartesian2();
const normalScratch = new Cartesian3();
const rayScratch$3 = new Ray();
const scratchCarto = new Cartographic();
let v1 = new Cartesian3();
const DRAGGING_PLANE = new Plane(Cartesian3.UNIT_X, 0);
const SURFACE_NORMAL = new Cartesian3();
/**
 * @private
 * @param {*} scene
 * @param {*} eventPos
 * @param {*} flightNode
 */
export function translateByHeight(scene: Scene, eventPos: Cartesian2, flightNode: { cartesian: Cartesian3; groundCartesian: Cartesian3 }) {
  // @ts-ignore
  const ellipsoid = scene.frameState.mapProjection.ellipsoid;
  const startPos = flightNode.groundCartesian;
  let endPos = flightNode.cartesian.clone();
  let draggingPlane = DRAGGING_PLANE;
  let surfaceNormal = SURFACE_NORMAL;
  let startPosTemp = startPos;
  let surfaceTemp = surfaceNormal;
  if (scene.mode === SceneMode.COLUMBUS_VIEW) {
    surfaceTemp = Cartesian3.UNIT_X;
    const carto = ellipsoid.cartesianToCartographic(startPos, scratchCarto);
    startPosTemp = scene.mapProjection.project(carto, scratch);
    Cartesian3.fromElements(startPosTemp.z, startPosTemp.x, startPosTemp.y, startPosTemp);
  }

  let crossPlaneNormal = Cartesian3.cross(surfaceTemp, scene.camera.direction, normalScratch);
  crossPlaneNormal = Cartesian3.cross(surfaceTemp, crossPlaneNormal, crossPlaneNormal);
  crossPlaneNormal = Cartesian3.normalize(crossPlaneNormal, crossPlaneNormal);
  draggingPlane = Plane.fromPointNormal(startPosTemp, crossPlaneNormal, draggingPlane);

  const ray = scene.camera.getPickRay(eventPos, rayScratch$3);
  endPos = IntersectionTests.rayPlane(ray, draggingPlane, endPos);

  if (defined(endPos)) {
    if (scene.mode === SceneMode.COLUMBUS_VIEW) {
      endPos = Cartesian3.fromElements(endPos.y, endPos.z, endPos.x, endPos);
      const cartoNew = scene.mapProjection.unproject(endPos, scratchCarto);
      endPos = ellipsoid.cartographicToCartesian(cartoNew, endPos);
    }
    if (SceneTransforms.wgs84ToWindowCoordinates(scene, flightNode.groundCartesian, cart2).y < eventPos.y) {
      surfaceNormal = Cartesian3.negate(surfaceNormal, normalScratch);
    }
    v1 = Cartesian3.subtract(endPos, startPos, v1);
    v1 = Cartesian3.projectVector(v1, surfaceNormal, v1);
    endPos = Cartesian3.add(startPos, v1, endPos);
    return endPos;
  }
}
