{"version": 3, "file": "VertexFormat-14204a1d.js", "sources": ["../../../../Source/Core/VertexFormat.js"], "sourcesContent": ["import defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\n\n/**\n * A vertex format defines what attributes make up a vertex.  A VertexFormat can be provided\n * to a {@link Geometry} to request that certain properties be computed, e.g., just position,\n * position and normal, etc.\n *\n * @param {Object} [options] An object with boolean properties corresponding to VertexFormat properties as shown in the code example.\n *\n * @alias VertexFormat\n * @constructor\n *\n * @example\n * // Create a vertex format with position and 2D texture coordinate attributes.\n * const format = new Cesium.VertexFormat({\n *   position : true,\n *   st : true\n * });\n *\n * @see Geometry#attributes\n * @see Packable\n */\nfunction VertexFormat(options) {\n  options = defaultValue(options, defaultValue.EMPTY_OBJECT);\n\n  /**\n   * When <code>true</code>, the vertex has a 3D position attribute.\n   * <p>\n   * 64-bit floating-point (for precision).  3 components per attribute.\n   * </p>\n   *\n   * @type Boolean\n   *\n   * @default false\n   */\n  this.position = defaultValue(options.position, false);\n\n  /**\n   * When <code>true</code>, the vertex has a normal attribute (normalized), which is commonly used for lighting.\n   * <p>\n   * 32-bit floating-point.  3 components per attribute.\n   * </p>\n   *\n   * @type Boolean\n   *\n   * @default false\n   */\n  this.normal = defaultValue(options.normal, false);\n\n  /**\n   * When <code>true</code>, the vertex has a 2D texture coordinate attribute.\n   * <p>\n   * 32-bit floating-point.  2 components per attribute\n   * </p>\n   *\n   * @type Boolean\n   *\n   * @default false\n   */\n  this.st = defaultValue(options.st, false);\n\n  /**\n   * When <code>true</code>, the vertex has a bitangent attribute (normalized), which is used for tangent-space effects like bump mapping.\n   * <p>\n   * 32-bit floating-point.  3 components per attribute.\n   * </p>\n   *\n   * @type Boolean\n   *\n   * @default false\n   */\n  this.bitangent = defaultValue(options.bitangent, false);\n\n  /**\n   * When <code>true</code>, the vertex has a tangent attribute (normalized), which is used for tangent-space effects like bump mapping.\n   * <p>\n   * 32-bit floating-point.  3 components per attribute.\n   * </p>\n   *\n   * @type Boolean\n   *\n   * @default false\n   */\n  this.tangent = defaultValue(options.tangent, false);\n\n  /**\n   * When <code>true</code>, the vertex has an RGB color attribute.\n   * <p>\n   * 8-bit unsigned byte.  3 components per attribute.\n   * </p>\n   *\n   * @type Boolean\n   *\n   * @default false\n   */\n  this.color = defaultValue(options.color, false);\n}\n\n/**\n * An immutable vertex format with only a position attribute.\n *\n * @type {VertexFormat}\n * @constant\n *\n * @see VertexFormat#position\n */\nVertexFormat.POSITION_ONLY = Object.freeze(\n  new VertexFormat({\n    position: true,\n  })\n);\n\n/**\n * An immutable vertex format with position and normal attributes.\n * This is compatible with per-instance color appearances like {@link PerInstanceColorAppearance}.\n *\n * @type {VertexFormat}\n * @constant\n *\n * @see VertexFormat#position\n * @see VertexFormat#normal\n */\nVertexFormat.POSITION_AND_NORMAL = Object.freeze(\n  new VertexFormat({\n    position: true,\n    normal: true,\n  })\n);\n\n/**\n * An immutable vertex format with position, normal, and st attributes.\n * This is compatible with {@link MaterialAppearance} when {@link MaterialAppearance#materialSupport}\n * is <code>TEXTURED/code>.\n *\n * @type {VertexFormat}\n * @constant\n *\n * @see VertexFormat#position\n * @see VertexFormat#normal\n * @see VertexFormat#st\n */\nVertexFormat.POSITION_NORMAL_AND_ST = Object.freeze(\n  new VertexFormat({\n    position: true,\n    normal: true,\n    st: true,\n  })\n);\n\n/**\n * An immutable vertex format with position and st attributes.\n * This is compatible with {@link EllipsoidSurfaceAppearance}.\n *\n * @type {VertexFormat}\n * @constant\n *\n * @see VertexFormat#position\n * @see VertexFormat#st\n */\nVertexFormat.POSITION_AND_ST = Object.freeze(\n  new VertexFormat({\n    position: true,\n    st: true,\n  })\n);\n\n/**\n * An immutable vertex format with position and color attributes.\n *\n * @type {VertexFormat}\n * @constant\n *\n * @see VertexFormat#position\n * @see VertexFormat#color\n */\nVertexFormat.POSITION_AND_COLOR = Object.freeze(\n  new VertexFormat({\n    position: true,\n    color: true,\n  })\n);\n\n/**\n * An immutable vertex format with well-known attributes: position, normal, st, tangent, and bitangent.\n *\n * @type {VertexFormat}\n * @constant\n *\n * @see VertexFormat#position\n * @see VertexFormat#normal\n * @see VertexFormat#st\n * @see VertexFormat#tangent\n * @see VertexFormat#bitangent\n */\nVertexFormat.ALL = Object.freeze(\n  new VertexFormat({\n    position: true,\n    normal: true,\n    st: true,\n    tangent: true,\n    bitangent: true,\n  })\n);\n\n/**\n * An immutable vertex format with position, normal, and st attributes.\n * This is compatible with most appearances and materials; however\n * normal and st attributes are not always required.  When this is\n * known in advance, another <code>VertexFormat</code> should be used.\n *\n * @type {VertexFormat}\n * @constant\n *\n * @see VertexFormat#position\n * @see VertexFormat#normal\n */\nVertexFormat.DEFAULT = VertexFormat.POSITION_NORMAL_AND_ST;\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nVertexFormat.packedLength = 6;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {VertexFormat} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nVertexFormat.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(value)) {\n    throw new DeveloperError(\"value is required\");\n  }\n  if (!defined(array)) {\n    throw new DeveloperError(\"array is required\");\n  }\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  array[startingIndex++] = value.position ? 1.0 : 0.0;\n  array[startingIndex++] = value.normal ? 1.0 : 0.0;\n  array[startingIndex++] = value.st ? 1.0 : 0.0;\n  array[startingIndex++] = value.tangent ? 1.0 : 0.0;\n  array[startingIndex++] = value.bitangent ? 1.0 : 0.0;\n  array[startingIndex] = value.color ? 1.0 : 0.0;\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {VertexFormat} [result] The object into which to store the result.\n * @returns {VertexFormat} The modified result parameter or a new VertexFormat instance if one was not provided.\n */\nVertexFormat.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(array)) {\n    throw new DeveloperError(\"array is required\");\n  }\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new VertexFormat();\n  }\n\n  result.position = array[startingIndex++] === 1.0;\n  result.normal = array[startingIndex++] === 1.0;\n  result.st = array[startingIndex++] === 1.0;\n  result.tangent = array[startingIndex++] === 1.0;\n  result.bitangent = array[startingIndex++] === 1.0;\n  result.color = array[startingIndex] === 1.0;\n  return result;\n};\n\n/**\n * Duplicates a VertexFormat instance.\n *\n * @param {VertexFormat} vertexFormat The vertex format to duplicate.\n * @param {VertexFormat} [result] The object onto which to store the result.\n * @returns {VertexFormat} The modified result parameter or a new VertexFormat instance if one was not provided. (Returns undefined if vertexFormat is undefined)\n */\nVertexFormat.clone = function (vertexFormat, result) {\n  if (!defined(vertexFormat)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    result = new VertexFormat();\n  }\n\n  result.position = vertexFormat.position;\n  result.normal = vertexFormat.normal;\n  result.st = vertexFormat.st;\n  result.tangent = vertexFormat.tangent;\n  result.bitangent = vertexFormat.bitangent;\n  result.color = vertexFormat.color;\n  return result;\n};\nexport default VertexFormat;\n"], "names": ["VertexFormat", "options", "defaultValue", "EMPTY_OBJECT", "this", "position", "normal", "st", "bitangent", "tangent", "color", "POSITION_ONLY", "Object", "freeze", "POSITION_AND_NORMAL", "POSITION_NORMAL_AND_ST", "POSITION_AND_ST", "POSITION_AND_COLOR", "ALL", "DEFAULT", "<PERSON><PERSON><PERSON><PERSON>", "pack", "value", "array", "startingIndex", "defined", "DeveloperError", "unpack", "result", "clone", "vertexFormat"], "mappings": "6FAwBA,SAASA,EAAaC,GACpBA,EAAUC,EAAAA,aAAaD,EAASC,EAAYA,aAACC,cAY7CC,KAAKC,SAAWH,EAAYA,aAACD,EAAQI,UAAU,GAY/CD,KAAKE,OAASJ,EAAYA,aAACD,EAAQK,QAAQ,GAY3CF,KAAKG,GAAKL,EAAYA,aAACD,EAAQM,IAAI,GAYnCH,KAAKI,UAAYN,EAAYA,aAACD,EAAQO,WAAW,GAYjDJ,KAAKK,QAAUP,EAAYA,aAACD,EAAQQ,SAAS,GAY7CL,KAAKM,MAAQR,EAAYA,aAACD,EAAQS,OAAO,EAC3C,CAUAV,EAAaW,cAAgBC,OAAOC,OAClC,IAAIb,EAAa,CACfK,UAAU,KAcdL,EAAac,oBAAsBF,OAAOC,OACxC,IAAIb,EAAa,CACfK,UAAU,EACVC,QAAQ,KAgBZN,EAAae,uBAAyBH,OAAOC,OAC3C,IAAIb,EAAa,CACfK,UAAU,EACVC,QAAQ,EACRC,IAAI,KAcRP,EAAagB,gBAAkBJ,OAAOC,OACpC,IAAIb,EAAa,CACfK,UAAU,EACVE,IAAI,KAaRP,EAAaiB,mBAAqBL,OAAOC,OACvC,IAAIb,EAAa,CACfK,UAAU,EACVK,OAAO,KAgBXV,EAAakB,IAAMN,OAAOC,OACxB,IAAIb,EAAa,CACfK,UAAU,EACVC,QAAQ,EACRC,IAAI,EACJE,SAAS,EACTD,WAAW,KAgBfR,EAAamB,QAAUnB,EAAae,uBAMpCf,EAAaoB,aAAe,EAW5BpB,EAAaqB,KAAO,SAAUC,EAAOC,EAAOC,GAE1C,IAAKC,EAAAA,QAAQH,GACX,MAAM,IAAII,EAAAA,eAAe,qBAE3B,IAAKD,EAAAA,QAAQF,GACX,MAAM,IAAIG,EAAAA,eAAe,qBAa3B,OATAF,EAAgBtB,EAAYA,aAACsB,EAAe,GAE5CD,EAAMC,KAAmBF,EAAMjB,SAAW,EAAM,EAChDkB,EAAMC,KAAmBF,EAAMhB,OAAS,EAAM,EAC9CiB,EAAMC,KAAmBF,EAAMf,GAAK,EAAM,EAC1CgB,EAAMC,KAAmBF,EAAMb,QAAU,EAAM,EAC/Cc,EAAMC,KAAmBF,EAAMd,UAAY,EAAM,EACjDe,EAAMC,GAAiBF,EAAMZ,MAAQ,EAAM,EAEpCa,CACT,EAUAvB,EAAa2B,OAAS,SAAUJ,EAAOC,EAAeI,GAEpD,IAAKH,EAAAA,QAAQF,GACX,MAAM,IAAIG,EAAAA,eAAe,qBAgB3B,OAZAF,EAAgBtB,EAAYA,aAACsB,EAAe,GAEvCC,EAAAA,QAAQG,KACXA,EAAS,IAAI5B,GAGf4B,EAAOvB,SAAsC,IAA3BkB,EAAMC,KACxBI,EAAOtB,OAAoC,IAA3BiB,EAAMC,KACtBI,EAAOrB,GAAgC,IAA3BgB,EAAMC,KAClBI,EAAOnB,QAAqC,IAA3Bc,EAAMC,KACvBI,EAAOpB,UAAuC,IAA3Be,EAAMC,KACzBI,EAAOlB,MAAiC,IAAzBa,EAAMC,GACdI,CACT,EASA5B,EAAa6B,MAAQ,SAAUC,EAAcF,GAC3C,GAAKH,EAAAA,QAAQK,GAab,OAVKL,EAAAA,QAAQG,KACXA,EAAS,IAAI5B,GAGf4B,EAAOvB,SAAWyB,EAAazB,SAC/BuB,EAAOtB,OAASwB,EAAaxB,OAC7BsB,EAAOrB,GAAKuB,EAAavB,GACzBqB,EAAOnB,QAAUqB,EAAarB,QAC9BmB,EAAOpB,UAAYsB,EAAatB,UAChCoB,EAAOlB,MAAQoB,EAAapB,MACrBkB,CACT"}