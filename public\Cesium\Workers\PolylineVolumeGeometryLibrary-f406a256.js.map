{"version": 3, "file": "PolylineVolumeGeometryLibrary-f406a256.js", "sources": ["../../../../Source/Core/CornerType.js", "../../../../Source/Core/oneTimeWarning.js", "../../../../Source/Core/PolylineVolumeGeometryLibrary.js"], "sourcesContent": ["/**\n * Style options for corners.\n *\n * @demo The {@link https://sandcastle.cesium.com/index.html?src=Corridor.html&label=Geometries|Corridor Demo}\n * demonstrates the three corner types, as used by {@link CorridorGraphics}.\n *\n * @enum {Number}\n */\nconst CornerType = {\n  /**\n   * <img src=\"Images/CornerTypeRounded.png\" style=\"vertical-align: middle;\" width=\"186\" height=\"189\" />\n   *\n   * Corner has a smooth edge.\n   * @type {Number}\n   * @constant\n   */\n  ROUNDED: 0,\n\n  /**\n   * <img src=\"Images/CornerTypeMitered.png\" style=\"vertical-align: middle;\" width=\"186\" height=\"189\" />\n   *\n   * Corner point is the intersection of adjacent edges.\n   * @type {Number}\n   * @constant\n   */\n  MITERED: 1,\n\n  /**\n   * <img src=\"Images/CornerTypeBeveled.png\" style=\"vertical-align: middle;\" width=\"186\" height=\"189\" />\n   *\n   * Corner is clipped.\n   * @type {Number}\n   * @constant\n   */\n  BEVELED: 2,\n};\nexport default Object.freeze(CornerType);\n", "import defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\n\nconst warnings = {};\n\n/**\n * Logs a one time message to the console.  Use this function instead of\n * <code>console.log</code> directly since this does not log duplicate messages\n * unless it is called from multiple workers.\n *\n * @function oneTimeWarning\n *\n * @param {String} identifier The unique identifier for this warning.\n * @param {String} [message=identifier] The message to log to the console.\n *\n * @example\n * for(let i=0;i<foo.length;++i) {\n *    if (!defined(foo[i].bar)) {\n *       // Something that can be recovered from but may happen a lot\n *       oneTimeWarning('foo.bar undefined', 'foo.bar is undefined. Setting to 0.');\n *       foo[i].bar = 0;\n *       // ...\n *    }\n * }\n *\n * @private\n */\nfunction oneTimeWarning(identifier, message) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(identifier)) {\n    throw new DeveloperError(\"identifier is required.\");\n  }\n  //>>includeEnd('debug');\n\n  if (!defined(warnings[identifier])) {\n    warnings[identifier] = true;\n    console.warn(defaultValue(message, identifier));\n  }\n}\n\noneTimeWarning.geometryOutlines =\n  \"Entity geometry outlines are unsupported on terrain. Outlines will be disabled. To enable outlines, disable geometry terrain clamping by explicitly setting height to 0.\";\n\noneTimeWarning.geometryZIndex =\n  \"Entity geometry with zIndex are unsupported when height or extrudedHeight are defined.  zIndex will be ignored\";\n\noneTimeWarning.geometryHeightReference =\n  \"Entity corridor, ellipse, polygon or rectangle with heightReference must also have a defined height.  heightReference will be ignored\";\noneTimeWarning.geometryExtrudedHeightReference =\n  \"Entity corridor, ellipse, polygon or rectangle with extrudedHeightReference must also have a defined extrudedHeight.  extrudedHeightReference will be ignored\";\nexport default oneTimeWarning;\n", "import Cartesian2 from \"./Cartesian2.js\";\nimport Cartesian3 from \"./Cartesian3.js\";\nimport Cartesian4 from \"./Cartesian4.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport CornerType from \"./CornerType.js\";\nimport EllipsoidTangentPlane from \"./EllipsoidTangentPlane.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix3 from \"./Matrix3.js\";\nimport Matrix4 from \"./Matrix4.js\";\nimport PolylinePipeline from \"./PolylinePipeline.js\";\nimport Quaternion from \"./Quaternion.js\";\nimport Transforms from \"./Transforms.js\";\nimport oneTimeWarning from \"../Core/oneTimeWarning.js\";\n\nconst scratch2Array = [new Cartesian3(), new Cartesian3()];\nconst scratchCartesian1 = new Cartesian3();\nconst scratchCartesian2 = new Cartesian3();\nconst scratchCartesian3 = new Cartesian3();\nconst scratchCartesian4 = new Cartesian3();\nconst scratchCartesian5 = new Cartesian3();\nconst scratchCartesian6 = new Cartesian3();\nconst scratchCartesian7 = new Cartesian3();\nconst scratchCartesian8 = new Cartesian3();\nconst scratchCartesian9 = new Cartesian3();\n\nconst scratch1 = new Cartesian3();\nconst scratch2 = new Cartesian3();\n\n/**\n * @private\n */\nconst PolylineVolumeGeometryLibrary = {};\n\nlet cartographic = new Cartographic();\nfunction scaleToSurface(positions, ellipsoid) {\n  const heights = new Array(positions.length);\n  for (let i = 0; i < positions.length; i++) {\n    const pos = positions[i];\n    cartographic = ellipsoid.cartesianToCartographic(pos, cartographic);\n    heights[i] = cartographic.height;\n    positions[i] = ellipsoid.scaleToGeodeticSurface(pos, pos);\n  }\n  return heights;\n}\n\nfunction subdivideHeights(points, h0, h1, granularity) {\n  const p0 = points[0];\n  const p1 = points[1];\n  const angleBetween = Cartesian3.angleBetween(p0, p1);\n  const numPoints = Math.ceil(angleBetween / granularity);\n  const heights = new Array(numPoints);\n  let i;\n  if (h0 === h1) {\n    for (i = 0; i < numPoints; i++) {\n      heights[i] = h0;\n    }\n    heights.push(h1);\n    return heights;\n  }\n\n  const dHeight = h1 - h0;\n  const heightPerVertex = dHeight / numPoints;\n\n  for (i = 1; i < numPoints; i++) {\n    const h = h0 + i * heightPerVertex;\n    heights[i] = h;\n  }\n\n  heights[0] = h0;\n  heights.push(h1);\n  return heights;\n}\n\nconst nextScratch = new Cartesian3();\nconst prevScratch = new Cartesian3();\n\nfunction computeRotationAngle(start, end, position, ellipsoid) {\n  const tangentPlane = new EllipsoidTangentPlane(position, ellipsoid);\n  const next = tangentPlane.projectPointOntoPlane(\n    Cartesian3.add(position, start, nextScratch),\n    nextScratch\n  );\n  const prev = tangentPlane.projectPointOntoPlane(\n    Cartesian3.add(position, end, prevScratch),\n    prevScratch\n  );\n  const angle = Cartesian2.angleBetween(next, prev);\n\n  return prev.x * next.y - prev.y * next.x >= 0.0 ? -angle : angle;\n}\n\nconst negativeX = new Cartesian3(-1, 0, 0);\nlet transform = new Matrix4();\nconst translation = new Matrix4();\nlet rotationZ = new Matrix3();\nconst scaleMatrix = Matrix3.IDENTITY.clone();\nconst westScratch = new Cartesian3();\nconst finalPosScratch = new Cartesian4();\nconst heightCartesian = new Cartesian3();\nfunction addPosition(\n  center,\n  left,\n  shape,\n  finalPositions,\n  ellipsoid,\n  height,\n  xScalar,\n  repeat\n) {\n  let west = westScratch;\n  let finalPosition = finalPosScratch;\n  transform = Transforms.eastNorthUpToFixedFrame(center, ellipsoid, transform);\n\n  west = Matrix4.multiplyByPointAsVector(transform, negativeX, west);\n  west = Cartesian3.normalize(west, west);\n  const angle = computeRotationAngle(west, left, center, ellipsoid);\n  rotationZ = Matrix3.fromRotationZ(angle, rotationZ);\n\n  heightCartesian.z = height;\n  transform = Matrix4.multiplyTransformation(\n    transform,\n    Matrix4.fromRotationTranslation(rotationZ, heightCartesian, translation),\n    transform\n  );\n  const scale = scaleMatrix;\n  scale[0] = xScalar;\n\n  for (let j = 0; j < repeat; j++) {\n    for (let i = 0; i < shape.length; i += 3) {\n      finalPosition = Cartesian3.fromArray(shape, i, finalPosition);\n      finalPosition = Matrix3.multiplyByVector(\n        scale,\n        finalPosition,\n        finalPosition\n      );\n      finalPosition = Matrix4.multiplyByPoint(\n        transform,\n        finalPosition,\n        finalPosition\n      );\n      finalPositions.push(finalPosition.x, finalPosition.y, finalPosition.z);\n    }\n  }\n\n  return finalPositions;\n}\n\nconst centerScratch = new Cartesian3();\nfunction addPositions(\n  centers,\n  left,\n  shape,\n  finalPositions,\n  ellipsoid,\n  heights,\n  xScalar\n) {\n  for (let i = 0; i < centers.length; i += 3) {\n    const center = Cartesian3.fromArray(centers, i, centerScratch);\n    finalPositions = addPosition(\n      center,\n      left,\n      shape,\n      finalPositions,\n      ellipsoid,\n      heights[i / 3],\n      xScalar,\n      1\n    );\n  }\n  return finalPositions;\n}\n\nfunction convertShapeTo3DDuplicate(shape2D, boundingRectangle) {\n  //orientate 2D shape to XZ plane center at (0, 0, 0), duplicate points\n  const length = shape2D.length;\n  const shape = new Array(length * 6);\n  let index = 0;\n  const xOffset = boundingRectangle.x + boundingRectangle.width / 2;\n  const yOffset = boundingRectangle.y + boundingRectangle.height / 2;\n\n  let point = shape2D[0];\n  shape[index++] = point.x - xOffset;\n  shape[index++] = 0.0;\n  shape[index++] = point.y - yOffset;\n  for (let i = 1; i < length; i++) {\n    point = shape2D[i];\n    const x = point.x - xOffset;\n    const z = point.y - yOffset;\n    shape[index++] = x;\n    shape[index++] = 0.0;\n    shape[index++] = z;\n\n    shape[index++] = x;\n    shape[index++] = 0.0;\n    shape[index++] = z;\n  }\n  point = shape2D[0];\n  shape[index++] = point.x - xOffset;\n  shape[index++] = 0.0;\n  shape[index++] = point.y - yOffset;\n\n  return shape;\n}\n\nfunction convertShapeTo3D(shape2D, boundingRectangle) {\n  //orientate 2D shape to XZ plane center at (0, 0, 0)\n  const length = shape2D.length;\n  const shape = new Array(length * 3);\n  let index = 0;\n  const xOffset = boundingRectangle.x + boundingRectangle.width / 2;\n  const yOffset = boundingRectangle.y + boundingRectangle.height / 2;\n\n  for (let i = 0; i < length; i++) {\n    shape[index++] = shape2D[i].x - xOffset;\n    shape[index++] = 0;\n    shape[index++] = shape2D[i].y - yOffset;\n  }\n\n  return shape;\n}\n\nconst quaterion = new Quaternion();\nconst startPointScratch = new Cartesian3();\nconst rotMatrix = new Matrix3();\nfunction computeRoundCorner(\n  pivot,\n  startPoint,\n  endPoint,\n  cornerType,\n  leftIsOutside,\n  ellipsoid,\n  finalPositions,\n  shape,\n  height,\n  duplicatePoints\n) {\n  const angle = Cartesian3.angleBetween(\n    Cartesian3.subtract(startPoint, pivot, scratch1),\n    Cartesian3.subtract(endPoint, pivot, scratch2)\n  );\n  const granularity =\n    cornerType === CornerType.BEVELED\n      ? 0\n      : Math.ceil(angle / CesiumMath.toRadians(5));\n\n  let m;\n  if (leftIsOutside) {\n    m = Matrix3.fromQuaternion(\n      Quaternion.fromAxisAngle(\n        Cartesian3.negate(pivot, scratch1),\n        angle / (granularity + 1),\n        quaterion\n      ),\n      rotMatrix\n    );\n  } else {\n    m = Matrix3.fromQuaternion(\n      Quaternion.fromAxisAngle(pivot, angle / (granularity + 1), quaterion),\n      rotMatrix\n    );\n  }\n\n  let left;\n  let surfacePoint;\n  startPoint = Cartesian3.clone(startPoint, startPointScratch);\n  if (granularity > 0) {\n    const repeat = duplicatePoints ? 2 : 1;\n    for (let i = 0; i < granularity; i++) {\n      startPoint = Matrix3.multiplyByVector(m, startPoint, startPoint);\n      left = Cartesian3.subtract(startPoint, pivot, scratch1);\n      left = Cartesian3.normalize(left, left);\n      if (!leftIsOutside) {\n        left = Cartesian3.negate(left, left);\n      }\n      surfacePoint = ellipsoid.scaleToGeodeticSurface(startPoint, scratch2);\n      finalPositions = addPosition(\n        surfacePoint,\n        left,\n        shape,\n        finalPositions,\n        ellipsoid,\n        height,\n        1,\n        repeat\n      );\n    }\n  } else {\n    left = Cartesian3.subtract(startPoint, pivot, scratch1);\n    left = Cartesian3.normalize(left, left);\n    if (!leftIsOutside) {\n      left = Cartesian3.negate(left, left);\n    }\n    surfacePoint = ellipsoid.scaleToGeodeticSurface(startPoint, scratch2);\n    finalPositions = addPosition(\n      surfacePoint,\n      left,\n      shape,\n      finalPositions,\n      ellipsoid,\n      height,\n      1,\n      1\n    );\n\n    endPoint = Cartesian3.clone(endPoint, startPointScratch);\n    left = Cartesian3.subtract(endPoint, pivot, scratch1);\n    left = Cartesian3.normalize(left, left);\n    if (!leftIsOutside) {\n      left = Cartesian3.negate(left, left);\n    }\n    surfacePoint = ellipsoid.scaleToGeodeticSurface(endPoint, scratch2);\n    finalPositions = addPosition(\n      surfacePoint,\n      left,\n      shape,\n      finalPositions,\n      ellipsoid,\n      height,\n      1,\n      1\n    );\n  }\n\n  return finalPositions;\n}\n\nPolylineVolumeGeometryLibrary.removeDuplicatesFromShape = function (\n  shapePositions\n) {\n  const length = shapePositions.length;\n  const cleanedPositions = [];\n  for (let i0 = length - 1, i1 = 0; i1 < length; i0 = i1++) {\n    const v0 = shapePositions[i0];\n    const v1 = shapePositions[i1];\n\n    if (!Cartesian2.equals(v0, v1)) {\n      cleanedPositions.push(v1); // Shallow copy!\n    }\n  }\n\n  return cleanedPositions;\n};\n\nPolylineVolumeGeometryLibrary.angleIsGreaterThanPi = function (\n  forward,\n  backward,\n  position,\n  ellipsoid\n) {\n  const tangentPlane = new EllipsoidTangentPlane(position, ellipsoid);\n  const next = tangentPlane.projectPointOntoPlane(\n    Cartesian3.add(position, forward, nextScratch),\n    nextScratch\n  );\n  const prev = tangentPlane.projectPointOntoPlane(\n    Cartesian3.add(position, backward, prevScratch),\n    prevScratch\n  );\n\n  return prev.x * next.y - prev.y * next.x >= 0.0;\n};\n\nconst scratchForwardProjection = new Cartesian3();\nconst scratchBackwardProjection = new Cartesian3();\n\nPolylineVolumeGeometryLibrary.computePositions = function (\n  positions,\n  shape2D,\n  boundingRectangle,\n  geometry,\n  duplicatePoints\n) {\n  const ellipsoid = geometry._ellipsoid;\n  const heights = scaleToSurface(positions, ellipsoid);\n  const granularity = geometry._granularity;\n  const cornerType = geometry._cornerType;\n  const shapeForSides = duplicatePoints\n    ? convertShapeTo3DDuplicate(shape2D, boundingRectangle)\n    : convertShapeTo3D(shape2D, boundingRectangle);\n  const shapeForEnds = duplicatePoints\n    ? convertShapeTo3D(shape2D, boundingRectangle)\n    : undefined;\n  const heightOffset = boundingRectangle.height / 2;\n  const width = boundingRectangle.width / 2;\n  let length = positions.length;\n  let finalPositions = [];\n  let ends = duplicatePoints ? [] : undefined;\n\n  let forward = scratchCartesian1;\n  let backward = scratchCartesian2;\n  let cornerDirection = scratchCartesian3;\n  let surfaceNormal = scratchCartesian4;\n  let pivot = scratchCartesian5;\n  let start = scratchCartesian6;\n  let end = scratchCartesian7;\n  let left = scratchCartesian8;\n  let previousPosition = scratchCartesian9;\n\n  let position = positions[0];\n  let nextPosition = positions[1];\n  surfaceNormal = ellipsoid.geodeticSurfaceNormal(position, surfaceNormal);\n  forward = Cartesian3.subtract(nextPosition, position, forward);\n  forward = Cartesian3.normalize(forward, forward);\n  left = Cartesian3.cross(surfaceNormal, forward, left);\n  left = Cartesian3.normalize(left, left);\n  let h0 = heights[0];\n  let h1 = heights[1];\n  if (duplicatePoints) {\n    ends = addPosition(\n      position,\n      left,\n      shapeForEnds,\n      ends,\n      ellipsoid,\n      h0 + heightOffset,\n      1,\n      1\n    );\n  }\n  previousPosition = Cartesian3.clone(position, previousPosition);\n  position = nextPosition;\n  backward = Cartesian3.negate(forward, backward);\n  let subdividedHeights;\n  let subdividedPositions;\n  for (let i = 1; i < length - 1; i++) {\n    const repeat = duplicatePoints ? 2 : 1;\n    nextPosition = positions[i + 1];\n    if (position.equals(nextPosition)) {\n      oneTimeWarning(\n        \"Positions are too close and are considered equivalent with rounding error.\"\n      );\n      continue;\n    }\n    forward = Cartesian3.subtract(nextPosition, position, forward);\n    forward = Cartesian3.normalize(forward, forward);\n    cornerDirection = Cartesian3.add(forward, backward, cornerDirection);\n    cornerDirection = Cartesian3.normalize(cornerDirection, cornerDirection);\n    surfaceNormal = ellipsoid.geodeticSurfaceNormal(position, surfaceNormal);\n\n    const forwardProjection = Cartesian3.multiplyByScalar(\n      surfaceNormal,\n      Cartesian3.dot(forward, surfaceNormal),\n      scratchForwardProjection\n    );\n    Cartesian3.subtract(forward, forwardProjection, forwardProjection);\n    Cartesian3.normalize(forwardProjection, forwardProjection);\n\n    const backwardProjection = Cartesian3.multiplyByScalar(\n      surfaceNormal,\n      Cartesian3.dot(backward, surfaceNormal),\n      scratchBackwardProjection\n    );\n    Cartesian3.subtract(backward, backwardProjection, backwardProjection);\n    Cartesian3.normalize(backwardProjection, backwardProjection);\n\n    const doCorner = !CesiumMath.equalsEpsilon(\n      Math.abs(Cartesian3.dot(forwardProjection, backwardProjection)),\n      1.0,\n      CesiumMath.EPSILON7\n    );\n\n    if (doCorner) {\n      cornerDirection = Cartesian3.cross(\n        cornerDirection,\n        surfaceNormal,\n        cornerDirection\n      );\n      cornerDirection = Cartesian3.cross(\n        surfaceNormal,\n        cornerDirection,\n        cornerDirection\n      );\n      cornerDirection = Cartesian3.normalize(cornerDirection, cornerDirection);\n      const scalar =\n        1 /\n        Math.max(\n          0.25,\n          Cartesian3.magnitude(\n            Cartesian3.cross(cornerDirection, backward, scratch1)\n          )\n        );\n      const leftIsOutside = PolylineVolumeGeometryLibrary.angleIsGreaterThanPi(\n        forward,\n        backward,\n        position,\n        ellipsoid\n      );\n      if (leftIsOutside) {\n        pivot = Cartesian3.add(\n          position,\n          Cartesian3.multiplyByScalar(\n            cornerDirection,\n            scalar * width,\n            cornerDirection\n          ),\n          pivot\n        );\n        start = Cartesian3.add(\n          pivot,\n          Cartesian3.multiplyByScalar(left, width, start),\n          start\n        );\n        scratch2Array[0] = Cartesian3.clone(previousPosition, scratch2Array[0]);\n        scratch2Array[1] = Cartesian3.clone(start, scratch2Array[1]);\n        subdividedHeights = subdivideHeights(\n          scratch2Array,\n          h0 + heightOffset,\n          h1 + heightOffset,\n          granularity\n        );\n        subdividedPositions = PolylinePipeline.generateArc({\n          positions: scratch2Array,\n          granularity: granularity,\n          ellipsoid: ellipsoid,\n        });\n        finalPositions = addPositions(\n          subdividedPositions,\n          left,\n          shapeForSides,\n          finalPositions,\n          ellipsoid,\n          subdividedHeights,\n          1\n        );\n        left = Cartesian3.cross(surfaceNormal, forward, left);\n        left = Cartesian3.normalize(left, left);\n        end = Cartesian3.add(\n          pivot,\n          Cartesian3.multiplyByScalar(left, width, end),\n          end\n        );\n        if (\n          cornerType === CornerType.ROUNDED ||\n          cornerType === CornerType.BEVELED\n        ) {\n          computeRoundCorner(\n            pivot,\n            start,\n            end,\n            cornerType,\n            leftIsOutside,\n            ellipsoid,\n            finalPositions,\n            shapeForSides,\n            h1 + heightOffset,\n            duplicatePoints\n          );\n        } else {\n          cornerDirection = Cartesian3.negate(cornerDirection, cornerDirection);\n          finalPositions = addPosition(\n            position,\n            cornerDirection,\n            shapeForSides,\n            finalPositions,\n            ellipsoid,\n            h1 + heightOffset,\n            scalar,\n            repeat\n          );\n        }\n        previousPosition = Cartesian3.clone(end, previousPosition);\n      } else {\n        pivot = Cartesian3.add(\n          position,\n          Cartesian3.multiplyByScalar(\n            cornerDirection,\n            scalar * width,\n            cornerDirection\n          ),\n          pivot\n        );\n        start = Cartesian3.add(\n          pivot,\n          Cartesian3.multiplyByScalar(left, -width, start),\n          start\n        );\n        scratch2Array[0] = Cartesian3.clone(previousPosition, scratch2Array[0]);\n        scratch2Array[1] = Cartesian3.clone(start, scratch2Array[1]);\n        subdividedHeights = subdivideHeights(\n          scratch2Array,\n          h0 + heightOffset,\n          h1 + heightOffset,\n          granularity\n        );\n        subdividedPositions = PolylinePipeline.generateArc({\n          positions: scratch2Array,\n          granularity: granularity,\n          ellipsoid: ellipsoid,\n        });\n        finalPositions = addPositions(\n          subdividedPositions,\n          left,\n          shapeForSides,\n          finalPositions,\n          ellipsoid,\n          subdividedHeights,\n          1\n        );\n        left = Cartesian3.cross(surfaceNormal, forward, left);\n        left = Cartesian3.normalize(left, left);\n        end = Cartesian3.add(\n          pivot,\n          Cartesian3.multiplyByScalar(left, -width, end),\n          end\n        );\n        if (\n          cornerType === CornerType.ROUNDED ||\n          cornerType === CornerType.BEVELED\n        ) {\n          computeRoundCorner(\n            pivot,\n            start,\n            end,\n            cornerType,\n            leftIsOutside,\n            ellipsoid,\n            finalPositions,\n            shapeForSides,\n            h1 + heightOffset,\n            duplicatePoints\n          );\n        } else {\n          finalPositions = addPosition(\n            position,\n            cornerDirection,\n            shapeForSides,\n            finalPositions,\n            ellipsoid,\n            h1 + heightOffset,\n            scalar,\n            repeat\n          );\n        }\n        previousPosition = Cartesian3.clone(end, previousPosition);\n      }\n      backward = Cartesian3.negate(forward, backward);\n    } else {\n      finalPositions = addPosition(\n        previousPosition,\n        left,\n        shapeForSides,\n        finalPositions,\n        ellipsoid,\n        h0 + heightOffset,\n        1,\n        1\n      );\n      previousPosition = position;\n    }\n    h0 = h1;\n    h1 = heights[i + 1];\n    position = nextPosition;\n  }\n\n  scratch2Array[0] = Cartesian3.clone(previousPosition, scratch2Array[0]);\n  scratch2Array[1] = Cartesian3.clone(position, scratch2Array[1]);\n  subdividedHeights = subdivideHeights(\n    scratch2Array,\n    h0 + heightOffset,\n    h1 + heightOffset,\n    granularity\n  );\n  subdividedPositions = PolylinePipeline.generateArc({\n    positions: scratch2Array,\n    granularity: granularity,\n    ellipsoid: ellipsoid,\n  });\n  finalPositions = addPositions(\n    subdividedPositions,\n    left,\n    shapeForSides,\n    finalPositions,\n    ellipsoid,\n    subdividedHeights,\n    1\n  );\n  if (duplicatePoints) {\n    ends = addPosition(\n      position,\n      left,\n      shapeForEnds,\n      ends,\n      ellipsoid,\n      h1 + heightOffset,\n      1,\n      1\n    );\n  }\n\n  length = finalPositions.length;\n  const posLength = duplicatePoints ? length + ends.length : length;\n  const combinedPositions = new Float64Array(posLength);\n  combinedPositions.set(finalPositions);\n  if (duplicatePoints) {\n    combinedPositions.set(ends, length);\n  }\n\n  return combinedPositions;\n};\nexport default PolylineVolumeGeometryLibrary;\n"], "names": ["CornerType$1", "Object", "freeze", "ROUNDED", "MITERED", "BEVELED", "warnings", "oneTimeWarning", "identifier", "message", "defined", "DeveloperError", "console", "warn", "defaultValue", "geometryOutlines", "geometryZIndex", "geometryHeightReference", "geometryExtrudedHeightReference", "scratch2Array", "Cartesian3", "scratchCartesian1", "scratchCartesian2", "scratchCartesian3", "scratchCartesian4", "scratchCartesian5", "scratchCartesian6", "scratchCartesian7", "scratchCartesian8", "scratchCartesian9", "scratch1", "scratch2", "PolylineVolumeGeometryLibrary", "cartographic", "Cartographic", "subdivideHeights", "points", "h0", "h1", "granularity", "p0", "p1", "angleBetween", "numPoints", "Math", "ceil", "heights", "Array", "i", "push", "heightPerVertex", "h", "<PERSON><PERSON><PERSON><PERSON>", "prevScratch", "negativeX", "transform", "Matrix4", "translation", "rotationZ", "Matrix3", "scaleMatrix", "IDENTITY", "clone", "westScratch", "final<PERSON>os<PERSON><PERSON><PERSON>", "Cartesian4", "heightCartesian", "addPosition", "center", "left", "shape", "finalPositions", "ellipsoid", "height", "xScalar", "repeat", "west", "finalPosition", "Transforms", "eastNorthUpToFixedFrame", "multiplyByPointAsVector", "normalize", "angle", "start", "end", "position", "tangentPlane", "EllipsoidTangentPlane", "next", "projectPointOntoPlane", "add", "prev", "Cartesian2", "x", "y", "computeRotationAngle", "fromRotationZ", "z", "multiplyTransformation", "fromRotationTranslation", "scale", "j", "length", "fromArray", "multiplyByVector", "multiplyByPoint", "centerScratch", "addPositions", "centers", "convertShapeTo3D", "shape2D", "boundingRectangle", "index", "xOffset", "width", "yOffset", "quaterion", "Quaternion", "startPointScratch", "rotMatrix", "computeRoundCorner", "pivot", "startPoint", "endPoint", "cornerType", "leftIsOutside", "duplicatePoints", "subtract", "CornerType", "CesiumMath", "toRadians", "m", "surfacePoint", "fromQuaternion", "fromAxisAngle", "negate", "scaleToGeodeticSurface", "removeDuplicatesFromShape", "shapePositions", "cleanedPositions", "i0", "i1", "v0", "v1", "equals", "angleIsGreaterThanPi", "forward", "backward", "scratchForwardProjection", "scratchBackwardProjection", "computePositions", "positions", "geometry", "_ellipsoid", "pos", "cartesianToCartographic", "scaleToSurface", "_granularity", "_cornerType", "shapeForSides", "point", "convertShapeTo3DDuplicate", "shapeForEnds", "undefined", "heightOffset", "ends", "cornerDirection", "surfaceNormal", "previousPosition", "nextPosition", "geodeticSurfaceNormal", "cross", "subdividedHeights", "subdividedPositions", "forwardProjection", "multiplyByScalar", "dot", "backwardProjection", "equalsEpsilon", "abs", "EPSILON7", "scalar", "max", "magnitude", "PolylinePipeline", "generateArc", "pos<PERSON><PERSON><PERSON>", "combinedPositions", "Float64Array", "set"], "mappings": "oPAoCA,IAAAA,EAAeC,OAAOC,OA5BH,CAQjBC,QAAS,EASTC,QAAS,EASTC,QAAS,IC9BX,MAAMC,EAAW,CAAA,EAwBjB,SAASC,EAAeC,EAAYC,GAElC,IAAKC,EAAAA,QAAQF,GACX,MAAM,IAAIG,EAAAA,eAAe,2BAItBD,EAAOA,QAACJ,EAASE,MACpBF,EAASE,IAAc,EACvBI,QAAQC,KAAKC,EAAAA,aAAaL,EAASD,IAEvC,CAEAD,EAAeQ,iBACb,2KAEFR,EAAeS,eACb,iHAEFT,EAAeU,wBACb,wIACFV,EAAeW,gCACb,gKCpCF,MAAMC,EAAgB,CAAC,IAAIC,EAAAA,WAAc,IAAIA,EAAAA,YACvCC,EAAoB,IAAID,EAAAA,WACxBE,EAAoB,IAAIF,EAAAA,WACxBG,EAAoB,IAAIH,EAAAA,WACxBI,EAAoB,IAAIJ,EAAAA,WACxBK,EAAoB,IAAIL,EAAAA,WACxBM,EAAoB,IAAIN,EAAAA,WACxBO,EAAoB,IAAIP,EAAAA,WACxBQ,EAAoB,IAAIR,EAAAA,WACxBS,EAAoB,IAAIT,EAAAA,WAExBU,EAAW,IAAIV,EAAAA,WACfW,EAAW,IAAIX,EAAAA,WAKfY,EAAgC,CAAG,EAEzC,IAAIC,EAAe,IAAIC,EAAAA,aAYvB,SAASC,EAAiBC,EAAQC,EAAIC,EAAIC,GACxC,MAAMC,EAAKJ,EAAO,GACZK,EAAKL,EAAO,GACZM,EAAetB,EAAUA,WAACsB,aAAaF,EAAIC,GAC3CE,EAAYC,KAAKC,KAAKH,EAAeH,GACrCO,EAAU,IAAIC,MAAMJ,GAC1B,IAAIK,EACJ,GAAIX,IAAOC,EAAI,CACb,IAAKU,EAAI,EAAGA,EAAIL,EAAWK,IACzBF,EAAQE,GAAKX,EAGf,OADAS,EAAQG,KAAKX,GACNQ,CACR,CAED,MACMI,GADUZ,EAAKD,GACaM,EAElC,IAAKK,EAAI,EAAGA,EAAIL,EAAWK,IAAK,CAC9B,MAAMG,EAAId,EAAKW,EAAIE,EACnBJ,EAAQE,GAAKG,CACd,CAID,OAFAL,EAAQ,GAAKT,EACbS,EAAQG,KAAKX,GACNQ,CACT,CAEA,MAAMM,EAAc,IAAIhC,EAAAA,WAClBiC,EAAc,IAAIjC,EAAAA,WAiBxB,MAAMkC,EAAY,IAAIlC,EAAAA,YAAY,EAAG,EAAG,GACxC,IAAImC,EAAY,IAAIC,EAAAA,QACpB,MAAMC,EAAc,IAAID,EAAAA,QACxB,IAAIE,EAAY,IAAIC,EAAAA,QACpB,MAAMC,EAAcD,EAAOA,QAACE,SAASC,QAC/BC,EAAc,IAAI3C,EAAAA,WAClB4C,EAAkB,IAAIC,EAAAA,WACtBC,EAAkB,IAAI9C,EAAAA,WAC5B,SAAS+C,EACPC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAEA,IAAIC,EAAOb,EACPc,EAAgBb,EACpBT,EAAYuB,EAAUA,WAACC,wBAAwBX,EAAQI,EAAWjB,GAElEqB,EAAOpB,EAAOA,QAACwB,wBAAwBzB,EAAWD,EAAWsB,GAC7DA,EAAOxD,EAAAA,WAAW6D,UAAUL,EAAMA,GAClC,MAAMM,EAvCR,SAA8BC,EAAOC,EAAKC,EAAUb,GAClD,MAAMc,EAAe,IAAIC,EAAAA,sBAAsBF,EAAUb,GACnDgB,EAAOF,EAAaG,sBACxBrE,EAAAA,WAAWsE,IAAIL,EAAUF,EAAO/B,GAChCA,GAEIuC,EAAOL,EAAaG,sBACxBrE,EAAAA,WAAWsE,IAAIL,EAAUD,EAAK/B,GAC9BA,GAEI6B,EAAQU,EAAUA,WAAClD,aAAa8C,EAAMG,GAE5C,OAAOA,EAAKE,EAAIL,EAAKM,EAAIH,EAAKG,EAAIN,EAAKK,GAAK,GAAOX,EAAQA,CAC7D,CA0BgBa,CAAqBnB,EAAMP,EAAMD,EAAQI,GACvDd,EAAYC,EAAAA,QAAQqC,cAAcd,EAAOxB,GAEzCQ,EAAgB+B,EAAIxB,EACpBlB,EAAYC,EAAOA,QAAC0C,uBAClB3C,EACAC,EAAAA,QAAQ2C,wBAAwBzC,EAAWQ,EAAiBT,GAC5DF,GAEF,MAAM6C,EAAQxC,EACdwC,EAAM,GAAK1B,EAEX,IAAK,IAAI2B,EAAI,EAAGA,EAAI1B,EAAQ0B,IAC1B,IAAK,IAAIrD,EAAI,EAAGA,EAAIsB,EAAMgC,OAAQtD,GAAK,EACrC6B,EAAgBzD,EAAUA,WAACmF,UAAUjC,EAAOtB,EAAG6B,GAC/CA,EAAgBlB,EAAOA,QAAC6C,iBACtBJ,EACAvB,EACAA,GAEFA,EAAgBrB,EAAOA,QAACiD,gBACtBlD,EACAsB,EACAA,GAEFN,EAAetB,KAAK4B,EAAcgB,EAAGhB,EAAciB,EAAGjB,EAAcoB,GAIxE,OAAO1B,CACT,CAEA,MAAMmC,EAAgB,IAAItF,EAAAA,WAC1B,SAASuF,EACPC,EACAvC,EACAC,EACAC,EACAC,EACA1B,EACA4B,GAEA,IAAK,IAAI1B,EAAI,EAAGA,EAAI4D,EAAQN,OAAQtD,GAAK,EAAG,CAE1CuB,EAAiBJ,EADF/C,EAAAA,WAAWmF,UAAUK,EAAS5D,EAAG0D,GAG9CrC,EACAC,EACAC,EACAC,EACA1B,EAAQE,EAAI,GACZ0B,EACA,EAEH,CACD,OAAOH,CACT,CAkCA,SAASsC,EAAiBC,EAASC,GAEjC,MAAMT,EAASQ,EAAQR,OACjBhC,EAAQ,IAAIvB,MAAe,EAATuD,GACxB,IAAIU,EAAQ,EACZ,MAAMC,EAAUF,EAAkBlB,EAAIkB,EAAkBG,MAAQ,EAC1DC,EAAUJ,EAAkBjB,EAAIiB,EAAkBtC,OAAS,EAEjE,IAAK,IAAIzB,EAAI,EAAGA,EAAIsD,EAAQtD,IAC1BsB,EAAM0C,KAAWF,EAAQ9D,GAAG6C,EAAIoB,EAChC3C,EAAM0C,KAAW,EACjB1C,EAAM0C,KAAWF,EAAQ9D,GAAG8C,EAAIqB,EAGlC,OAAO7C,CACT,CAEA,MAAM8C,EAAY,IAAIC,EAAAA,WAChBC,EAAoB,IAAIlG,EAAAA,WACxBmG,EAAY,IAAI5D,EAAAA,QACtB,SAAS6D,EACPC,EACAC,EACAC,EACAC,EACAC,EACArD,EACAD,EACAD,EACAG,EACAqD,GAEA,MAAM5C,EAAQ9D,EAAAA,WAAWsB,aACvBtB,EAAAA,WAAW2G,SAASL,EAAYD,EAAO3F,GACvCV,EAAAA,WAAW2G,SAASJ,EAAUF,EAAO1F,IAEjCQ,EACJqF,IAAeI,EAAW3H,QACtB,EACAuC,KAAKC,KAAKqC,EAAQ+C,EAAUA,WAACC,UAAU,IAE7C,IAAIC,EAiBA9D,EACA+D,EAEJ,GAlBED,EADEN,EACElE,EAAOA,QAAC0E,eACVhB,EAAAA,WAAWiB,cACTlH,aAAWmH,OAAOd,EAAO3F,GACzBoD,GAAS3C,EAAc,GACvB6E,GAEFG,GAGE5D,EAAOA,QAAC0E,eACVhB,EAAUA,WAACiB,cAAcb,EAAOvC,GAAS3C,EAAc,GAAI6E,GAC3DG,GAMJG,EAAatG,EAAAA,WAAW0C,MAAM4D,EAAYJ,GACtC/E,EAAc,EAAG,CACnB,MAAMoC,EAASmD,EAAkB,EAAI,EACrC,IAAK,IAAI9E,EAAI,EAAGA,EAAIT,EAAaS,IAC/B0E,EAAa/D,EAAOA,QAAC6C,iBAAiB2B,EAAGT,EAAYA,GACrDrD,EAAOjD,EAAUA,WAAC2G,SAASL,EAAYD,EAAO3F,GAC9CuC,EAAOjD,EAAAA,WAAW6D,UAAUZ,EAAMA,GAC7BwD,IACHxD,EAAOjD,EAAAA,WAAWmH,OAAOlE,EAAMA,IAEjC+D,EAAe5D,EAAUgE,uBAAuBd,EAAY3F,GAC5DwC,EAAiBJ,EACfiE,EACA/D,EACAC,EACAC,EACAC,EACAC,EACA,EACAE,EAGR,MACIN,EAAOjD,EAAUA,WAAC2G,SAASL,EAAYD,EAAO3F,GAC9CuC,EAAOjD,EAAAA,WAAW6D,UAAUZ,EAAMA,GAC7BwD,IACHxD,EAAOjD,EAAAA,WAAWmH,OAAOlE,EAAMA,IAEjC+D,EAAe5D,EAAUgE,uBAAuBd,EAAY3F,GAC5DwC,EAAiBJ,EACfiE,EACA/D,EACAC,EACAC,EACAC,EACAC,EACA,EACA,GAGFkD,EAAWvG,EAAAA,WAAW0C,MAAM6D,EAAUL,GACtCjD,EAAOjD,EAAUA,WAAC2G,SAASJ,EAAUF,EAAO3F,GAC5CuC,EAAOjD,EAAAA,WAAW6D,UAAUZ,EAAMA,GAC7BwD,IACHxD,EAAOjD,EAAAA,WAAWmH,OAAOlE,EAAMA,IAEjC+D,EAAe5D,EAAUgE,uBAAuBb,EAAU5F,GAC1DwC,EAAiBJ,EACfiE,EACA/D,EACAC,EACAC,EACAC,EACAC,EACA,EACA,GAIJ,OAAOF,CACT,CAEAvC,EAA8ByG,0BAA4B,SACxDC,GAEA,MAAMpC,EAASoC,EAAepC,OACxBqC,EAAmB,GACzB,IAAK,IAAIC,EAAKtC,EAAS,EAAGuC,EAAK,EAAGA,EAAKvC,EAAQsC,EAAKC,IAAM,CACxD,MAAMC,EAAKJ,EAAeE,GACpBG,EAAKL,EAAeG,GAErBjD,EAAAA,WAAWoD,OAAOF,EAAIC,IACzBJ,EAAiB1F,KAAK8F,EAEzB,CAED,OAAOJ,CACT,EAEA3G,EAA8BiH,qBAAuB,SACnDC,EACAC,EACA9D,EACAb,GAEA,MAAMc,EAAe,IAAIC,EAAAA,sBAAsBF,EAAUb,GACnDgB,EAAOF,EAAaG,sBACxBrE,EAAAA,WAAWsE,IAAIL,EAAU6D,EAAS9F,GAClCA,GAEIuC,EAAOL,EAAaG,sBACxBrE,EAAAA,WAAWsE,IAAIL,EAAU8D,EAAU9F,GACnCA,GAGF,OAAOsC,EAAKE,EAAIL,EAAKM,EAAIH,EAAKG,EAAIN,EAAKK,GAAK,CAC9C,EAEA,MAAMuD,EAA2B,IAAIhI,EAAAA,WAC/BiI,EAA4B,IAAIjI,EAAAA,WAEtCY,EAA8BsH,iBAAmB,SAC/CC,EACAzC,EACAC,EACAyC,EACA1B,GAEA,MAAMtD,EAAYgF,EAASC,WACrB3G,EApVR,SAAwByG,EAAW/E,GACjC,MAAM1B,EAAU,IAAIC,MAAMwG,EAAUjD,QACpC,IAAK,IAAItD,EAAI,EAAGA,EAAIuG,EAAUjD,OAAQtD,IAAK,CACzC,MAAM0G,EAAMH,EAAUvG,GACtBf,EAAeuC,EAAUmF,wBAAwBD,EAAKzH,GACtDa,EAAQE,GAAKf,EAAawC,OAC1B8E,EAAUvG,GAAKwB,EAAUgE,uBAAuBkB,EAAKA,EACtD,CACD,OAAO5G,CACT,CA2UkB8G,CAAeL,EAAW/E,GACpCjC,EAAciH,EAASK,aACvBjC,EAAa4B,EAASM,YACtBC,EAAgBjC,EA5MxB,SAAmChB,EAASC,GAE1C,MAAMT,EAASQ,EAAQR,OACjBhC,EAAQ,IAAIvB,MAAe,EAATuD,GACxB,IAAIU,EAAQ,EACZ,MAAMC,EAAUF,EAAkBlB,EAAIkB,EAAkBG,MAAQ,EAC1DC,EAAUJ,EAAkBjB,EAAIiB,EAAkBtC,OAAS,EAEjE,IAAIuF,EAAQlD,EAAQ,GACpBxC,EAAM0C,KAAWgD,EAAMnE,EAAIoB,EAC3B3C,EAAM0C,KAAW,EACjB1C,EAAM0C,KAAWgD,EAAMlE,EAAIqB,EAC3B,IAAK,IAAInE,EAAI,EAAGA,EAAIsD,EAAQtD,IAAK,CAC/BgH,EAAQlD,EAAQ9D,GAChB,MAAM6C,EAAImE,EAAMnE,EAAIoB,EACdhB,EAAI+D,EAAMlE,EAAIqB,EACpB7C,EAAM0C,KAAWnB,EACjBvB,EAAM0C,KAAW,EACjB1C,EAAM0C,KAAWf,EAEjB3B,EAAM0C,KAAWnB,EACjBvB,EAAM0C,KAAW,EACjB1C,EAAM0C,KAAWf,CAClB,CAMD,OALA+D,EAAQlD,EAAQ,GAChBxC,EAAM0C,KAAWgD,EAAMnE,EAAIoB,EAC3B3C,EAAM0C,KAAW,EACjB1C,EAAM0C,KAAWgD,EAAMlE,EAAIqB,EAEpB7C,CACT,CA+KM2F,CAA0BnD,EAASC,GACnCF,EAAiBC,EAASC,GACxBmD,EAAepC,EACjBjB,EAAiBC,EAASC,QAC1BoD,EACEC,EAAerD,EAAkBtC,OAAS,EAC1CyC,EAAQH,EAAkBG,MAAQ,EACxC,IAAIZ,EAASiD,EAAUjD,OACnB/B,EAAiB,GACjB8F,EAAOvC,EAAkB,QAAKqC,EAE9BjB,EAAU7H,EACV8H,EAAW7H,EACXgJ,EAAkB/I,EAClBgJ,EAAgB/I,EAChBiG,EAAQhG,EACR0D,EAAQzD,EACR0D,EAAMzD,EACN0C,EAAOzC,EACP4I,EAAmB3I,EAEnBwD,EAAWkE,EAAU,GACrBkB,EAAelB,EAAU,GAC7BgB,EAAgB/F,EAAUkG,sBAAsBrF,EAAUkF,GAC1DrB,EAAU9H,EAAUA,WAAC2G,SAAS0C,EAAcpF,EAAU6D,GACtDA,EAAU9H,EAAAA,WAAW6D,UAAUiE,EAASA,GACxC7E,EAAOjD,EAAUA,WAACuJ,MAAMJ,EAAerB,EAAS7E,GAChDA,EAAOjD,EAAAA,WAAW6D,UAAUZ,EAAMA,GAClC,IAiBIuG,EACAC,EAlBAxI,GAAKS,EAAQ,GACbR,GAAKQ,EAAQ,GACbgF,IACFuC,EAAOlG,EACLkB,EACAhB,EACA6F,EACAG,EACA7F,EACAnC,GAAK+H,EACL,EACA,IAGJI,EAAmBpJ,EAAAA,WAAW0C,MAAMuB,EAAUmF,GAC9CnF,EAAWoF,EACXtB,EAAW/H,EAAAA,WAAWmH,OAAOW,EAASC,GAGtC,IAAK,IAAInG,EAAI,EAAGA,EAAIsD,EAAS,EAAGtD,IAAK,CACnC,MAAM2B,EAASmD,EAAkB,EAAI,EAErC,GADA2C,EAAelB,EAAUvG,EAAI,GACzBqC,EAAS2D,OAAOyB,GAAe,CACjClK,EACE,8EAEF,QACD,CACD2I,EAAU9H,EAAUA,WAAC2G,SAAS0C,EAAcpF,EAAU6D,GACtDA,EAAU9H,EAAAA,WAAW6D,UAAUiE,EAASA,GACxCoB,EAAkBlJ,EAAUA,WAACsE,IAAIwD,EAASC,EAAUmB,GACpDA,EAAkBlJ,EAAAA,WAAW6D,UAAUqF,EAAiBA,GACxDC,EAAgB/F,EAAUkG,sBAAsBrF,EAAUkF,GAE1D,MAAMO,EAAoB1J,EAAAA,WAAW2J,iBACnCR,EACAnJ,aAAW4J,IAAI9B,EAASqB,GACxBnB,GAEFhI,EAAAA,WAAW2G,SAASmB,EAAS4B,EAAmBA,GAChD1J,EAAAA,WAAW6D,UAAU6F,EAAmBA,GAExC,MAAMG,EAAqB7J,EAAAA,WAAW2J,iBACpCR,EACAnJ,aAAW4J,IAAI7B,EAAUoB,GACzBlB,GAEFjI,EAAAA,WAAW2G,SAASoB,EAAU8B,EAAoBA,GAClD7J,EAAAA,WAAW6D,UAAUgG,EAAoBA,GAQzC,IANkBhD,EAAAA,WAAWiD,cAC3BtI,KAAKuI,IAAI/J,EAAUA,WAAC4J,IAAIF,EAAmBG,IAC3C,EACAhD,EAAAA,WAAWmD,UAGC,CACZd,EAAkBlJ,EAAUA,WAACuJ,MAC3BL,EACAC,EACAD,GAEFA,EAAkBlJ,EAAUA,WAACuJ,MAC3BJ,EACAD,EACAA,GAEFA,EAAkBlJ,EAAAA,WAAW6D,UAAUqF,EAAiBA,GACxD,MAAMe,EACJ,EACAzI,KAAK0I,IACH,IACAlK,EAAAA,WAAWmK,UACTnK,EAAAA,WAAWuJ,MAAML,EAAiBnB,EAAUrH,KAG5C+F,EAAgB7F,EAA8BiH,qBAClDC,EACAC,EACA9D,EACAb,GAEEqD,GACFJ,EAAQrG,EAAUA,WAACsE,IACjBL,EACAjE,EAAAA,WAAW2J,iBACTT,EACAe,EAASnE,EACToD,GAEF7C,GAEFtC,EAAQ/D,EAAUA,WAACsE,IACjB+B,EACArG,EAAAA,WAAW2J,iBAAiB1G,EAAM6C,EAAO/B,GACzCA,GAEFhE,EAAc,GAAKC,aAAW0C,MAAM0G,EAAkBrJ,EAAc,IACpEA,EAAc,GAAKC,aAAW0C,MAAMqB,EAAOhE,EAAc,IACzDyJ,EAAoBzI,EAClBhB,EACAkB,GAAK+H,EACL9H,GAAK8H,EACL7H,GAEFsI,EAAsBW,EAAgBA,iBAACC,YAAY,CACjDlC,UAAWpI,EACXoB,YAAaA,EACbiC,UAAWA,IAEbD,EAAiBoC,EACfkE,EACAxG,EACA0F,EACAxF,EACAC,EACAoG,EACA,GAEFvG,EAAOjD,EAAUA,WAACuJ,MAAMJ,EAAerB,EAAS7E,GAChDA,EAAOjD,EAAAA,WAAW6D,UAAUZ,EAAMA,GAClCe,EAAMhE,EAAUA,WAACsE,IACf+B,EACArG,EAAAA,WAAW2J,iBAAiB1G,EAAM6C,EAAO9B,GACzCA,GAGAwC,IAAeI,EAAW7H,SAC1ByH,IAAeI,EAAW3H,QAE1BmH,EACEC,EACAtC,EACAC,EACAwC,EACAC,EACArD,EACAD,EACAwF,EACAzH,GAAK8H,EACLtC,IAGFwC,EAAkBlJ,EAAAA,WAAWmH,OAAO+B,EAAiBA,GACrD/F,EAAiBJ,EACfkB,EACAiF,EACAP,EACAxF,EACAC,EACAlC,GAAK8H,EACLiB,EACA1G,IAGJ6F,EAAmBpJ,EAAAA,WAAW0C,MAAMsB,EAAKoF,KAEzC/C,EAAQrG,EAAUA,WAACsE,IACjBL,EACAjE,EAAAA,WAAW2J,iBACTT,EACAe,EAASnE,EACToD,GAEF7C,GAEFtC,EAAQ/D,EAAUA,WAACsE,IACjB+B,EACArG,EAAAA,WAAW2J,iBAAiB1G,GAAO6C,EAAO/B,GAC1CA,GAEFhE,EAAc,GAAKC,aAAW0C,MAAM0G,EAAkBrJ,EAAc,IACpEA,EAAc,GAAKC,aAAW0C,MAAMqB,EAAOhE,EAAc,IACzDyJ,EAAoBzI,EAClBhB,EACAkB,GAAK+H,EACL9H,GAAK8H,EACL7H,GAEFsI,EAAsBW,EAAgBA,iBAACC,YAAY,CACjDlC,UAAWpI,EACXoB,YAAaA,EACbiC,UAAWA,IAEbD,EAAiBoC,EACfkE,EACAxG,EACA0F,EACAxF,EACAC,EACAoG,EACA,GAEFvG,EAAOjD,EAAUA,WAACuJ,MAAMJ,EAAerB,EAAS7E,GAChDA,EAAOjD,EAAAA,WAAW6D,UAAUZ,EAAMA,GAClCe,EAAMhE,EAAUA,WAACsE,IACf+B,EACArG,EAAAA,WAAW2J,iBAAiB1G,GAAO6C,EAAO9B,GAC1CA,GAGAwC,IAAeI,EAAW7H,SAC1ByH,IAAeI,EAAW3H,QAE1BmH,EACEC,EACAtC,EACAC,EACAwC,EACAC,EACArD,EACAD,EACAwF,EACAzH,GAAK8H,EACLtC,GAGFvD,EAAiBJ,EACfkB,EACAiF,EACAP,EACAxF,EACAC,EACAlC,GAAK8H,EACLiB,EACA1G,GAGJ6F,EAAmBpJ,EAAAA,WAAW0C,MAAMsB,EAAKoF,IAE3CrB,EAAW/H,EAAAA,WAAWmH,OAAOW,EAASC,EAC5C,MACM5E,EAAiBJ,EACfqG,EACAnG,EACA0F,EACAxF,EACAC,EACAnC,GAAK+H,EACL,EACA,GAEFI,EAAmBnF,EAErBhD,GAAKC,GACLA,GAAKQ,EAAQE,EAAI,GACjBqC,EAAWoF,CACZ,CAEDtJ,EAAc,GAAKC,aAAW0C,MAAM0G,EAAkBrJ,EAAc,IACpEA,EAAc,GAAKC,aAAW0C,MAAMuB,EAAUlE,EAAc,IAC5DyJ,EAAoBzI,EAClBhB,EACAkB,GAAK+H,EACL9H,GAAK8H,EACL7H,GAEFsI,EAAsBW,EAAgBA,iBAACC,YAAY,CACjDlC,UAAWpI,EACXoB,YAAaA,EACbiC,UAAWA,IAEbD,EAAiBoC,EACfkE,EACAxG,EACA0F,EACAxF,EACAC,EACAoG,EACA,GAEE9C,IACFuC,EAAOlG,EACLkB,EACAhB,EACA6F,EACAG,EACA7F,EACAlC,GAAK8H,EACL,EACA,IAIJ9D,EAAS/B,EAAe+B,OACxB,MAAMoF,GAAY5D,EAAkBxB,EAAS+D,EAAK/D,OAASA,EACrDqF,GAAoB,IAAIC,aAAaF,IAM3C,OALAC,GAAkBE,IAAItH,GAClBuD,GACF6D,GAAkBE,IAAIxB,EAAM/D,GAGvBqF,EACT"}