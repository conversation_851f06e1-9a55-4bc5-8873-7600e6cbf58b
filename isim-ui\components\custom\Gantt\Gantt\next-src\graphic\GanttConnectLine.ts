import { type GanttGraphicAbstract } from './GanttGraphicAbstract';
import type { CoordinatesType } from './GanttType';
import type { ConnectLineType } from './GanttOption';
import { Gantt } from './Gantt.ts';
import { createUUIdV4 } from '@utils/uuid-utils.ts';
import { GanttRangeGraphic } from './GanttRangeGraphic.ts';

interface ConnectLineRenderOpt {
  ctx: CanvasRenderingContext2D;
  start: CoordinatesType;
  stop: CoordinatesType;
  index: number;
}
/**
 * 连线
 */
export class GanttConnectLine implements GanttGraphicAbstract {
  gantt: Gantt;
  ctx: CanvasRenderingContext2D;
  source: GanttRangeGraphic;
  target: GanttRangeGraphic;
  connectLine: ConnectLineType;
  height: number;
  id: string;
  name: string;
  width: number;
  x: number;
  y: number;
  path: { x: number; y: number }[] = []; // 记录路径
  style: { fillStyle: string; selectedFillStyle: string; lineWidth: number };
  constructor(options: {
    gantt: Gantt;
    ctx: CanvasRenderingContext2D;
    source: GanttRangeGraphic;
    target: GanttRangeGraphic;
    connectLine: ConnectLineType;
    id?: string;
  }) {
    const { gantt, ctx, source, target, connectLine } = options;
    this.connectLine = connectLine;
    this.gantt = gantt;
    this.ctx = ctx;
    this.source = source;
    this.target = target;
    this.x = source.x;
    this.y = source.y;
    this.width = source.width;
    this.height = source.height;
    this.name = 'gantt-connect-line';
    this.id = options?.id ?? createUUIdV4();
    this.style = {
      fillStyle: '#62ADFD',
      // selectedFillStyle: '#79b4f1',
      selectedFillStyle: '#fff',
      lineWidth: 2
    };
  }
  get sourceStartX() {
    return this.source.x;
  }
  get sourceStartY() {
    return this.source.y;
  }

  get sourceStopX() {
    if (this.source.type === 'point') {
      return this.source.x;
    }
    return this.source.x + this.source.width;
  }

  get sourceStopY() {
    if (this.source.type === 'point') {
      return this.source.y;
    }
    return this.source.y + this.source.height / 2;
  }

  getTargetX(target: GanttGraphicAbstract) {
    return target.x;
  }
  getTargetY(target: GanttGraphicAbstract) {
    if (target.type === 'point') {
      return target.y;
    }
    return target.y + this.source.height / 2;
  }
  drawArrow(ctx: CanvasRenderingContext2D, item: GanttGraphicAbstract, type: 'fill' | 'border' = 'fill') {
    ctx.save();
    ctx.translate(this.getTargetX(item), this.getTargetY(item));
    ctx.rotate(-(Math.PI / 180) * 45);
    const arrowHeight = 10;
    const arrowWidth = 10;
    ctx.beginPath();
    if (type === 'fill') {
      ctx.fillStyle = this.linColor;
      ctx.moveTo(0, 0);
      ctx.lineTo(0, -arrowHeight);
      ctx.lineTo(-arrowWidth, 0);
      ctx.fill();
    } else {
      ctx.strokeStyle = this.linColor;
      ctx.moveTo(-arrowWidth, 0);
      ctx.lineTo(0, 0);
      ctx.lineTo(0, -arrowHeight);
      ctx.stroke();
    }
    ctx.restore();
  }

  getCenterPoint(start: CoordinatesType, stop: CoordinatesType) {
    return { x: (start.x + stop.x) / 2, y: (start.y + stop.y) / 2 };
  }

  bezierCurveRender(params: ConnectLineRenderOpt) {
    const { ctx, stop, start } = params;
    const center1 = this.getCenterPoint({ x: start.x, y: start.y }, { x: start.x + 80, y: start.y + 10 });
    const center2 = this.getCenterPoint({ x: stop.x - 80, y: stop.y - 5 }, { x: stop.x, y: stop.y });
    ctx.bezierCurveTo(center1.x, center1.y, center2.x, center2.y, stop.x, stop.y);
  }
  // 直线连线
  lineTo(x: number, y: number) {
    this.ctx.lineTo(x, y);
    this.path.push({ x, y });
  }
  lineRender(params: ConnectLineRenderOpt) {
    const { start, stop, index } = params;
    const startX = start.x;
    const startY = start.y;
    const stopX = stop.x;
    const stopY = stop.y;
    const offsetX = 10 + 5 * index;
    this.lineTo(startX + offsetX, startY);
    if (stopX - 2 > startX) {
      this.lineTo(startX + offsetX, stopY);
    } else {
      this.lineTo(startX + offsetX, startY + this.source.height);
      this.lineTo(stopX - offsetX, startY + this.source.height);
      this.lineTo(stopX - offsetX, stopY);
    }
    this.lineTo(stopX, stopY);
  }
  // 曲线的射线检测 -- 未实现
  bezierCurvePick(_x: number, _y: number) {
    return false;
  }
  // 直线的射线检测
  linePick(x: number, y: number) {
    const length = this.path.length - 1;
    for (let i = 0; i < length; i++) {
      const { x: startX, y: startY } = this.path[i];
      const { x: endX, y: endY } = this.path[i + 1];
      const scope = this.style.lineWidth;
      // 竖线
      if (startX == endX) {
        const maxY = Math.max(startY, endY);
        const minY = Math.min(startY, endY);
        if (x <= startX + scope && x >= startX - scope && y <= maxY + scope && y >= minY - scope) {
          return true;
        }
      }
      // 横线
      if (startY == endY) {
        const maxX = Math.max(startX, endX);
        const minX = Math.min(startX, endX);
        if (y <= startY + scope && y >= startY - scope && x <= maxX + scope && x >= minX - scope) {
          return true;
        }
      }
    }
    return false;
  }

  pick(x: number, y: number) {
    if (this.connectLine.type === 'line') {
      return this.linePick(x, y);
    }
    return this.bezierCurvePick(x, y);
  }

  get linColor() {
    if (this.gantt.selectedConnectLineCollection.has(this.id)) return this.style.selectedFillStyle;
    return this.style.fillStyle;
  }

  render() {
    this.path = [];
    const ctx = this.ctx;
    // this.target.forEach((item, index) => {
    const item = this.target;
    const index = 1;

    ctx.lineJoin = 'round';
    ctx.beginPath();
    // ctx.strokeStyle = this.style.fillStyle;
    ctx.strokeStyle = this.linColor;
    ctx.lineWidth = this.style.lineWidth * window.devicePixelRatio;
    const startX = this.sourceStopX;
    const startY = this.sourceStopY;
    const stopX = this.getTargetX(item);
    const stopY = this.getTargetY(item);
    ctx.moveTo(startX, startY);
    this.path.push({ x: startX, y: startY }); //记录开始节点路径
    const renderParams = {
      ctx: this.ctx,
      index,
      start: { x: startX, y: startY },
      stop: { x: stopX, y: stopY }
    };
    if (this.connectLine.type === 'bezier') {
      this.bezierCurveRender(renderParams);
    } else {
      this.lineRender(renderParams);
    }
    ctx.stroke();
    ctx.closePath();
    this.drawArrow(ctx, item, 'fill');
    // });
  }
}
