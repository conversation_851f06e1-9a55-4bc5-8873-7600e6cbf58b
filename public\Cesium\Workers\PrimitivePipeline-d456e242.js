define(["exports","./Transforms-4b8effe1","./ComponentDatatype-17ffa790","./when-4bbc8319","./RuntimeError-1349fdaf","./Matrix2-57f130bc","./GeometryAttribute-2c37d386","./GeometryAttributes-7827a6c2","./GeometryPipeline-e85d5416","./IndexDatatype-4ae6decc","./WebMercatorProjection-3b4197b5"],(function(e,t,n,r,o,i,s,c,a,d,p){"use strict";function f(e,t,n){e=r.defaultValue(e,0),t=r.defaultValue(t,0),n=r.defaultValue(n,0),this.value=new Float32Array([e,t,n])}function u(e,t){const r=e.attributes,o=r.position,i=o.values.length/o.componentsPerAttribute;r.batchId=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:1,values:new Float32Array(i)});const c=r.batchId.values;for(let e=0;e<i;++e)c[e]=t}function l(e){const s=e.instances,c=e.projection,d=e.elementIndexUintSupported,p=e.scene3DOnly,f=e.vertexCacheOptimize,l=e.compressVertices,m=e.modelMatrix;let h,g,y,b=s.length;for(h=0;h<b;++h)if(r.defined(s[h].geometry)){y=s[h].geometry.primitiveType;break}for(h=1;h<b;++h)if(r.defined(s[h].geometry)&&s[h].geometry.primitiveType!==y)throw new o.DeveloperError("All instance geometries must have the same primitiveType.");if(function(e,t,n){let o=!n;const s=e.length;let c;if(!o&&s>1){const t=e[0].modelMatrix;for(c=1;c<s;++c)if(!i.Matrix4.equals(t,e[c].modelMatrix)){o=!0;break}}if(o)for(c=0;c<s;++c)r.defined(e[c].geometry)&&a.GeometryPipeline.transformToWorldCoordinates(e[c]);else i.Matrix4.multiplyTransformation(t,e[0].modelMatrix,t)}(s,m,p),!p)for(h=0;h<b;++h)r.defined(s[h].geometry)&&a.GeometryPipeline.splitLongitude(s[h]);if(function(e){const t=e.length;for(let n=0;n<t;++n){const t=e[n];r.defined(t.geometry)?u(t.geometry,n):r.defined(t.westHemisphereGeometry)&&r.defined(t.eastHemisphereGeometry)&&(u(t.westHemisphereGeometry,n),u(t.eastHemisphereGeometry,n))}}(s),f)for(h=0;h<b;++h){const e=s[h];r.defined(e.geometry)?(a.GeometryPipeline.reorderForPostVertexCache(e.geometry),a.GeometryPipeline.reorderForPreVertexCache(e.geometry)):r.defined(e.westHemisphereGeometry)&&r.defined(e.eastHemisphereGeometry)&&(a.GeometryPipeline.reorderForPostVertexCache(e.westHemisphereGeometry),a.GeometryPipeline.reorderForPreVertexCache(e.westHemisphereGeometry),a.GeometryPipeline.reorderForPostVertexCache(e.eastHemisphereGeometry),a.GeometryPipeline.reorderForPreVertexCache(e.eastHemisphereGeometry))}let x=a.GeometryPipeline.combineInstances(s);for(b=x.length,h=0;h<b;++h){g=x[h];const e=g.attributes;if(p)for(const t in e)e.hasOwnProperty(t)&&e[t].componentDatatype===n.ComponentDatatype.DOUBLE&&a.GeometryPipeline.encodeAttribute(g,t,t+"3DHigh",t+"3DLow");else for(const o in e)if(e.hasOwnProperty(o)&&e[o].componentDatatype===n.ComponentDatatype.DOUBLE){const e=o+"3D",n=o+"2D";a.GeometryPipeline.projectTo2D(g,o,e,n,c),r.defined(g.boundingSphere)&&"position"===o&&(g.boundingSphereCV=t.BoundingSphere.fromVertices(g.attributes.position2D.values)),a.GeometryPipeline.encodeAttribute(g,e,e+"High",e+"Low"),a.GeometryPipeline.encodeAttribute(g,n,n+"High",n+"Low")}l&&a.GeometryPipeline.compressVertices(g)}if(!d){let e=[];for(b=x.length,h=0;h<b;++h)g=x[h],e=e.concat(a.GeometryPipeline.fitToUnsignedShortIndices(g));x=e}return x}function m(e,t,n,o){let i,s,c;const a=o.length-1;if(a>=0){const e=o[a];i=e.offset+e.count,c=e.index,s=n[c].indices.length}else i=0,c=0,s=n[c].indices.length;const d=e.length;for(let a=0;a<d;++a){const d=e[a][t];if(!r.defined(d))continue;const p=d.indices.length;i+p>s&&(i=0,s=n[++c].indices.length),o.push({index:c,offset:i,count:p}),i+=p}}Object.defineProperties(f.prototype,{componentDatatype:{get:function(){return n.ComponentDatatype.FLOAT}},componentsPerAttribute:{get:function(){return 3}},normalize:{get:function(){return!1}}}),f.fromCartesian3=function(e){return o.Check.defined("offset",e),new f(e.x,e.y,e.z)},f.toValue=function(e,t){return o.Check.defined("offset",e),r.defined(t)||(t=new Float32Array([e.x,e.y,e.z])),t[0]=e.x,t[1]=e.y,t[2]=e.z,t};const h={};function g(e,t){const n=e.attributes;for(const e in n)if(n.hasOwnProperty(e)){const o=n[e];r.defined(o)&&r.defined(o.values)&&t.push(o.values.buffer)}r.defined(e.indices)&&t.push(e.indices.buffer)}function y(e,t){const n=e.length,o=new Float64Array(1+19*n);let s=0;o[s++]=n;for(let t=0;t<n;t++){const n=e[t];if(i.Matrix4.pack(n.modelMatrix,o,s),s+=i.Matrix4.packedLength,r.defined(n.attributes)&&r.defined(n.attributes.offset)){const e=n.attributes.offset.value;o[s]=e[0],o[s+1]=e[1],o[s+2]=e[2]}s+=3}return t.push(o.buffer),o}function b(e){const n=e.length,o=1+(t.BoundingSphere.packedLength+1)*n,i=new Float32Array(o);let s=0;i[s++]=n;for(let o=0;o<n;++o){const n=e[o];r.defined(n)?(i[s++]=1,t.BoundingSphere.pack(e[o],i,s)):i[s++]=0,s+=t.BoundingSphere.packedLength}return i}function x(e){const n=new Array(e[0]);let r=0,o=1;for(;o<e.length;)1===e[o++]&&(n[r]=t.BoundingSphere.unpack(e,o)),++r,o+=t.BoundingSphere.packedLength;return n}h.combineGeometry=function(e){let n,o;const i=e.instances,s=i.length;let c,d,p=!1;s>0&&(n=l(e),n.length>0&&(o=a.GeometryPipeline.createAttributeLocations(n[0]),e.createPickOffsets&&(c=function(e,t){const n=[];return m(e,"geometry",t,n),m(e,"westHemisphereGeometry",t,n),m(e,"eastHemisphereGeometry",t,n),n}(i,n))),r.defined(i[0].attributes)&&r.defined(i[0].attributes.offset)&&(d=new Array(s),p=!0));const f=new Array(s),u=new Array(s);for(let e=0;e<s;++e){const n=i[e],o=n.geometry;r.defined(o)&&(f[e]=o.boundingSphere,u[e]=o.boundingSphereCV,p&&(d[e]=n.geometry.offsetAttribute));const s=n.eastHemisphereGeometry,c=n.westHemisphereGeometry;r.defined(s)&&r.defined(c)&&(r.defined(s.boundingSphere)&&r.defined(c.boundingSphere)&&(f[e]=t.BoundingSphere.union(s.boundingSphere,c.boundingSphere)),r.defined(s.boundingSphereCV)&&r.defined(c.boundingSphereCV)&&(u[e]=t.BoundingSphere.union(s.boundingSphereCV,c.boundingSphereCV)))}return{geometries:n,modelMatrix:e.modelMatrix,attributeLocations:o,pickOffsets:c,offsetInstanceExtend:d,boundingSpheres:f,boundingSpheresCV:u}},h.packCreateGeometryResults=function(e,n){const o=new Float64Array(function(e){let n=1;const o=e.length;for(let i=0;i<o;i++){const o=e[i];if(++n,!r.defined(o))continue;const s=o.attributes;n+=7+2*t.BoundingSphere.packedLength+(r.defined(o.indices)?o.indices.length:0);for(const e in s)s.hasOwnProperty(e)&&r.defined(s[e])&&(n+=5+s[e].values.length)}return n}(e)),i=[],s={},c=e.length;let a=0;o[a++]=c;for(let n=0;n<c;n++){const c=e[n],d=r.defined(c);if(o[a++]=d?1:0,!d)continue;o[a++]=c.primitiveType,o[a++]=c.geometryType,o[a++]=r.defaultValue(c.offsetAttribute,-1);const p=r.defined(c.boundingSphere)?1:0;o[a++]=p,p&&t.BoundingSphere.pack(c.boundingSphere,o,a),a+=t.BoundingSphere.packedLength;const f=r.defined(c.boundingSphereCV)?1:0;o[a++]=f,f&&t.BoundingSphere.pack(c.boundingSphereCV,o,a),a+=t.BoundingSphere.packedLength;const u=c.attributes,l=[];for(const e in u)u.hasOwnProperty(e)&&r.defined(u[e])&&(l.push(e),r.defined(s[e])||(s[e]=i.length,i.push(e)));o[a++]=l.length;for(let e=0;e<l.length;e++){const t=l[e],n=u[t];o[a++]=s[t],o[a++]=n.componentDatatype,o[a++]=n.componentsPerAttribute,o[a++]=n.normalize?1:0,o[a++]=n.values.length,o.set(n.values,a),a+=n.values.length}const m=r.defined(c.indices)?c.indices.length:0;o[a++]=m,m>0&&(o.set(c.indices,a),a+=m)}return n.push(o.buffer),{stringTable:i,packedData:o}},h.unpackCreateGeometryResults=function(e){const r=e.stringTable,o=e.packedData;let i;const a=new Array(o[0]);let p=0,f=1;for(;f<o.length;){if(!(1===o[f++])){a[p++]=void 0;continue}const e=o[f++],u=o[f++];let l,m,h=o[f++];-1===h&&(h=void 0);1===o[f++]&&(l=t.BoundingSphere.unpack(o,f)),f+=t.BoundingSphere.packedLength;let g,y,b;1===o[f++]&&(m=t.BoundingSphere.unpack(o,f)),f+=t.BoundingSphere.packedLength;const x=new c.GeometryAttributes,G=o[f++];for(i=0;i<G;i++){const e=r[o[f++]],t=o[f++];b=o[f++];const i=0!==o[f++];g=o[f++],y=n.ComponentDatatype.createTypedArray(t,g);for(let e=0;e<g;e++)y[e]=o[f++];x[e]=new s.GeometryAttribute({componentDatatype:t,componentsPerAttribute:b,normalize:i,values:y})}let S;if(g=o[f++],g>0){const e=y.length/b;for(S=d.IndexDatatype.createTypedArray(e,g),i=0;i<g;i++)S[i]=o[f++]}a[p++]=new s.Geometry({primitiveType:e,geometryType:u,boundingSphere:l,boundingSphereCV:m,indices:S,attributes:x,offsetAttribute:h})}return a},h.packCombineGeometryParameters=function(e,n){const r=e.createGeometryResults,o=r.length;for(let e=0;e<o;e++)n.push(r[e].packedData.buffer);return{createGeometryResults:e.createGeometryResults,packedInstances:y(e.instances,n),ellipsoid:e.ellipsoid,isGeographic:e.projection instanceof t.GeographicProjection,elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:e.modelMatrix,createPickOffsets:e.createPickOffsets}},h.unpackCombineGeometryParameters=function(e){const n=function(e){const t=e,n=new Array(t[0]);let o=0,s=1;for(;s<t.length;){const e=i.Matrix4.unpack(t,s);let c;s+=i.Matrix4.packedLength,r.defined(t[s])&&(c={offset:new f(t[s],t[s+1],t[s+2])}),s+=3,n[o++]={modelMatrix:e,attributes:c}}return n}(e.packedInstances),o=e.createGeometryResults,s=o.length;let c=0;for(let e=0;e<s;e++){const t=h.unpackCreateGeometryResults(o[e]),r=t.length;for(let e=0;e<r;e++){const r=t[e];n[c].geometry=r,++c}}const a=i.Ellipsoid.clone(e.ellipsoid);return{instances:n,ellipsoid:a,projection:e.isGeographic?new t.GeographicProjection(a):new p.WebMercatorProjection(a),elementIndexUintSupported:e.elementIndexUintSupported,scene3DOnly:e.scene3DOnly,vertexCacheOptimize:e.vertexCacheOptimize,compressVertices:e.compressVertices,modelMatrix:i.Matrix4.clone(e.modelMatrix),createPickOffsets:e.createPickOffsets}},h.packCombineGeometryResults=function(e,t){r.defined(e.geometries)&&function(e,t){const n=e.length;for(let r=0;r<n;++r)g(e[r],t)}(e.geometries,t);const n=b(e.boundingSpheres),o=b(e.boundingSpheresCV);return t.push(n.buffer,o.buffer),{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:n,boundingSpheresCV:o}},h.unpackCombineGeometryResults=function(e){return{geometries:e.geometries,attributeLocations:e.attributeLocations,modelMatrix:e.modelMatrix,pickOffsets:e.pickOffsets,offsetInstanceExtend:e.offsetInstanceExtend,boundingSpheres:x(e.boundingSpheres),boundingSpheresCV:x(e.boundingSpheresCV)}},e.PrimitivePipeline=h}));
//# sourceMappingURL=PrimitivePipeline-d456e242.js.map
