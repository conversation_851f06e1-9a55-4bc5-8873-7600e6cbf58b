/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/07/29 15:43:56
 * @description content
 * @version 1.0
 * */
import { Cartesian2, Cartesian3, Color, defaultValue, Material, Math as CesiumMath, SceneMode } from 'cesium';
import SectorDetectionPrimitive from '../../Scene/Primitives/SectorDetectionPrimitive.js';
import AbstractPrimitive from './AbstractVisualizer.js';
import { generateGradientColor, getHeading, getTrans, updateMode } from './VisualizerMethod.js';
import RectangularSensorPrimitive from '../../Modules/Sensor/RectangularSensorPrimitive';

export default class VisibleLightDetectionPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._xHalfAngle = defaultValue(options.xHalfAngle, 0);
    this._yHalfAngle = defaultValue(options.yHalfAngle, 0);

    this._yaw = defaultValue(options.yaw, 0);
    this._pitch = defaultValue(options.pitch, 0);
    this._roll = defaultValue(options.roll, 0);
    this._position = options.position;

    this._range = defaultValue(options.range, 1000);
    this._color = defaultValue(options.color, Color.BLUE);
    this._showScanPlane = defaultValue(options.showScanPlane, false);
    this._slice = defaultValue(options.slice, 8);

    this._modelMatrix = null;
    this._mode = SceneMode.SCENE3D;

    const [fadeInColor, fadeOutColor] = generateGradientColor(this._color);
    this._fadeInColor = fadeInColor;
    this._fadeOutColor = fadeOutColor;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    updateMode(this, frameState);
    if (this._update) {
      this._update = false;
      this._mode = frameState.mode;
      this._primitive3D = this._primitive3D && this._primitive3D.destroy();
      this._primitive2D = this._primitive2D && this._primitive2D.destroy();

      this._mode === SceneMode.SCENE3D && (this._primitive3D = this._createPrimitive3D());
      this._mode === SceneMode.SCENE2D && (this._primitive2D = this._createPrimitive2D());
    }

    this._primitive2D?.update(frameState);
    this._primitive3D?.update(frameState);
  }

  _createPrimitive3D() {
    if (!this._position) {
      return;
    }
    this._modelMatrix = getTrans(this._position, this._yaw, this._pitch, this._roll);

    const color = this._fadeInColor.clone();
    color.alpha = 0.8;

    return new RectangularSensorPrimitive({
      modelMatrix: this._modelMatrix,
      xHalfAngle: CesiumMath.toRadians(this._xHalfAngle),
      yHalfAngle: CesiumMath.toRadians(this._yHalfAngle),
      radius: this._range,
      showDomeLines: false,
      showScanPlane: this._showScanPlane,
      scanPlaneColor: this._fadeInColor,
      lineColor: color,
      slice: this._slice,
      showThroughEllipsoid: false,
      intersectionWidth: 2,
      showIntersection: true,
      material: Material.fromType(Material.FadeType, {
        repeat: false,
        fadeInColor: this._fadeInColor,
        fadeOutColor: this._fadeOutColor,
        time: new Cartesian2(0, 0),
        maximumDistance: 1.5,
        fadeDirection: {
          x: false,
          y: true
        }
      })
    });
  }

  _createPrimitive2D() {
    if (!this._position) {
      return;
    }
    return new SectorDetectionPrimitive({
      radius: this._range,
      halfAngle: this._xHalfAngle,
      heading: getHeading(this._pitch, this._yaw),
      position: this._position,
      lineOptions: {
        color: this._fadeInColor.clone().withAlpha(0.8)
      },
      fillOptions: {
        color: this._fadeInColor
      }
    });
  }

  set color(value) {
    if (Color.equals(this._color, value)) {
      return;
    }

    this._color = value;
    const [fadeInColor, fadeOutColor] = generateGradientColor(this._color);
    this._fadeInColor = fadeInColor;
    this._fadeOutColor = fadeOutColor;

    this._update = true;
  }
  set range(value) {
    if (this._range === value) {
      return;
    }

    this._range = value;
    this._update = true;
  }

  set xHalfAngle(value) {
    if (this._xHalfAngle === value) {
      return;
    }

    this._xHalfAngle = value;
    this._update = true;
  }

  set yHalfAngle(value) {
    if (this._yHalfAngle === value) {
      return;
    }

    this._yHalfAngle = value;
    this._update = true;
  }

  set position(value) {
    if (Cartesian3.equals(this._position, value)) {
      return;
    }

    this._position = value;
    this._update = true;
  }

  set yaw(value) {
    if (this._yaw === value) {
      return;
    }

    this._yaw = value;
    this._update = true;
  }

  set pitch(value) {
    if (this._pitch === value) {
      return;
    }

    this._pitch = value;
    this._update = true;
  }

  set roll(value) {
    if (this._roll === value) {
      return;
    }

    this._roll = value;
    this._update = true;
  }
}
