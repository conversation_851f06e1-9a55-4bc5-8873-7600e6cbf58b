/**
 * @Author: 宋计民
 * @Date: 2023/12/5 13:56
 * @Version: 1.0
 * @Content: ol-viewer.ts
 */
import { Graticule } from 'ol/layer';
import { Stroke } from 'ol/style';
import { transform } from 'ol/proj';
import { View, Map as OlMap } from 'ol';

const olViewerMap = new Map<string, OlMap>();

export function createViewer(name: string) {
  // add graticule
  const graticule = new Graticule({
    // the style to use for the lines, optional.
    strokeStyle: new Stroke({
      color: 'rgba(255,120,0,0.9)',
      width: 2,
      lineDash: [0.5, 4]
    }),
    showLabels: true,
    visible: true,
    wrapX: true
  });
  const map = new OlMap({
    target: name,
    layers: [graticule],
    view: new View({
      //projection: 'EPSG:4326', // 默认 3857
      projection: 'EPSG:3857', // 默认 3857
      //center: [120, 33],
      center: transform([120, 33], 'EPSG:4326', 'EPSG:3857'),
      zoom: 1
    })
  });
}
