<!--
 * @Author: songjimin
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
-->
<template>
  <div :class="ns.b()">
    <div :class="ns.e('current')">
      <slot name="range" :data="props.data" :offset="offsetPixel" :width="widthPixel" :update-range-fn="updateRangeFn">
        <div :class="[ns.e('range')]" :style="rangeStyle" @dblclick.stop="rangeDoubleClick(data)" @contextmenu="rangeContextmenu($event)">
          <span>{{ rangeText }}</span>
        </div>
      </slot>
    </div>
    <sim-gantt-right v-for="(item, dex) in data[childrenField]" v-show="data.expand" :key="dex" :data="item" :deep="deep + 1">
      <template #range="{ data: slotData, offset, width, updateRangeFn: slotUpdateFn }">
        <slot name="range" :data="slotData" :offset="offset" :width="width" :update-range-fn="slotUpdateFn"></slot>
      </template>
    </sim-gantt-right>
  </div>
</template>

<script lang="ts">
export default {
  name: 'SimGanttRight'
};
</script>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import { useNamespace } from 'isim-ui';
import { GanttNode } from './methods/ganttNode';
import { GanttDataOptions, useGanttEvent, useGanttInject } from './gantt';
const props = defineProps({
  data: {
    required: true,
    type: Object as PropType<GanttDataOptions>
  },
  deep: {
    type: Number,
    default: 0
  }
});

const { startTime, endTime, rangeDoubleClick, rightWidth, labelField, childrenField } = useGanttInject();

const rangeText = computed(() => {
  return typeof labelField.value === 'function' ? labelField.value(props.data) : props.data[labelField.value];
});

const ns = useNamespace('gantt-right');

const updateRangeFn = (currentTime: string) => {
  const timeBarSecondsSpan = endTime.value - startTime.value;
  const second = GanttNode.getTime(currentTime) - GanttNode.getTime(startTime.value);
  return Math.round((second * rightWidth.value) / timeBarSecondsSpan);
};
const offsetPixel = computed(() => {
  return updateRangeFn(props.data._startTime);
});
const widthPixel = computed(() => {
  return updateRangeFn(props.data._endTime) - offsetPixel.value;
});
const rangeStyle = computed(() => {
  // TODO 此处有修改
  // const _statTime = props.data._startTime < startTime.value ? startTime.value : props.data._startTime;
  // const tranlateX = getPxFromSecond(props.data._startTime - GanttNode.getTime(startTime.value));

  // const timeBarSecondsSpan = endTime.value - startTime.value
  // const second = props.data._startTime - GanttNode.getTime(startTime.value)
  // const xPos = Math.round(
  //     (second * rightWidth.value) / timeBarSecondsSpan
  // );
  // const xPos = updateRangeFn(props.data._startTime)
  // const _width = updateRangeFn(props.data._endTime) - xPos
  // const _width = getPxFromSecond(props.data._endTime - GanttNode.getTime(_statTime));
  // const _width = Math.round(
  //     ((props.data._endTime - GanttNode.getTime(_statTime)) * rightWidth.value) / timeBarSecondsSpan
  // );
  return {
    transform: `translateX(${offsetPixel.value + 1}px)`,
    width: widthPixel.value + 'px'
  };
});

const { handleContextmenu } = useGanttEvent();

const rangeContextmenu = (event: MouseEvent) => {
  handleContextmenu(event, props.data);
};
</script>

<style scoped lang="less">
.sim-gantt-right__range {
}
</style>
