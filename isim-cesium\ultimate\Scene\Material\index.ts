// @ts-nocheck
import { Material, Color, Cartesian2 } from 'cesium';
/**
 * BombRadiation
 * @type {string}
 */
Material.BombRadiationType = 'BombRadiation';
const BombRadiationMaterial = `
 uniform vec4 color;
 uniform float speed;
 uniform float count;
 uniform float gradient;

 czm_material czm_getMaterial(czm_materialInput materialInput)
 {
   czm_material material = czm_getDefaultMaterial(materialInput);
   material.diffuse = 1.5 * color.rgb;
   vec2 st = materialInput.st;
   float dis = distance(st, vec2(0.5, 0.5));
   float per = fract(czm_frameNumber * speed / 1000.0);
   if(count == 1.0){
     if(dis > per * 0.5){
       discard;
     }else {
       material.alpha = color.a  * dis / per ;
     }
   } else {
     vec3 str = materialInput.str;
     if(abs(str.z)  > 0.001){
       discard;
     }
     if(dis > 0.5){
       discard;
     } else {
       float perDis = 0.5 / count;
       float disNum;
       float bl = 0.0;
       for(int i = 0; i <= 999; i++){
         if(float(i) <= count){
           disNum = perDis * float(i) - dis + per / count;
           if(disNum > 0.0){
             if(disNum < perDis){
               bl = 1.0 - disNum / perDis;
             }
             else if(disNum - perDis < perDis){
               bl = 1.0 - abs(1.0 - disNum / perDis);
             }
             material.alpha = pow(bl,(1.0 + 10.0 * (1.0 - gradient)));
           }
         }
       }
     }
   }
   return material;
}`;

Material._materialCache.addMaterial(Material.BombRadiationType, {
  fabric: {
    type: Material.BombRadiationType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 3.0,
      count: 2,
      gradient: 0.5
    },
    source: BombRadiationMaterial
  },
  translucent: function (material) {
    return true;
  }
});

/**
 * @name SensorWaveType
 */
Material.SensorWaveType = 'SensorWave';
const SensorWaveMaterial = `
      uniform vec4 color;
      uniform float speed;
      uniform float count;
      uniform float gradient;

      const float uvDis = 1.0;
  
      czm_material czm_getMaterial(czm_materialInput materialInput)
      {
        czm_material material = czm_getDefaultMaterial(materialInput);
        material.diffuse = 1.5 * color.rgb;
        vec2 st = materialInput.st;
        float dis = distance(vec2(0.0,st.y), vec2(0.0, 0.0));
        float per = fract(czm_frameNumber * speed / 1000.0);
        if(count == 1.0){
          if(dis > per * uvDis){
            discard;
          }else {
            material.alpha = color.a  * dis / per / 2.0;
          }
        } else {
          vec3 str = materialInput.str;
          if(abs(str.z)  > 0.001){
            discard;
          }
          if(dis > uvDis){
            discard;
          } else {
            float perDis = uvDis / count;
            float disNum;
            float bl = 0.0;
            for(int i = 0; i <= 999; i++){
              if(float(i) <= count){
                disNum = perDis * float(i) - dis + per / count;
                if(disNum > 0.0){
                  if(disNum < perDis){
                    bl = 1.0 - disNum / perDis;
                  }
                  else if(disNum - perDis < perDis){
                    bl = 1.0 - abs(1.0 - disNum / perDis);
                  }
                  material.alpha = pow(bl,(1.0 + 10.0 * (1.0 - gradient)));
                }
              }
            }
          }
        }
        return material;
      }`;
Material._materialCache.addMaterial(Material.SensorWaveType, {
  fabric: {
    type: Material.SensorWaveType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 3.0,
      count: 4,
      gradient: 0.2
    },
    source: SensorWaveMaterial
  },
  translucent: function (material) {
    return true;
  }
});

/**
 * @name CircleDiffuseType
 */
Material.CircleDiffuseType = 'CircleDiffuse';
const CircleDiffuseMaterial = `
    uniform vec4 color;
    uniform float speed;

    vec3 circlePing(float r, float innerTail,  float frontierBorder, float timeResetSeconds,  float radarPingSpeed,  float fadeDistance){
      float t = fract(czm_frameNumber * speed / 1000.0);
      float time = mod(t, timeResetSeconds) * radarPingSpeed;
      float circle;
      circle += smoothstep(time - innerTail, time, r) * smoothstep(time + frontierBorder,time, r);
      circle *= smoothstep(fadeDistance, 0.0, r);
      return vec3(circle);
    }

    czm_material czm_getMaterial(czm_materialInput materialInput){
      czm_material material = czm_getDefaultMaterial(materialInput);
      vec2 st = materialInput.st * 2.0  - 1.0 ;
      vec2 center = vec2(0.);
      float time = fract(czm_frameNumber * speed / 1000.0);
      vec3 flagColor;
      float r = length(st - center) / 4.;
      flagColor += circlePing(r, 0.25, 0.025, 4.0, 0.3, 1.0) * color.rgb;
      // material.alpha = length(flagColor);
      material.alpha = length(flagColor)/2.0;
      material.diffuse = flagColor.rgb;
      return material;
    }`;
Material._materialCache.addMaterial(Material.CircleDiffuseType, {
  fabric: {
    type: Material.CircleDiffuseType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 15.0
    },
    source: CircleDiffuseMaterial
  },
  translucent: function (material) {
    return true;
  }
});

/**
 * @name CommunicationFlowType
 */
Material.CommunicationFlowType = 'CommunicationFlow';
const comFlowMaterial = `
    uniform vec4 color;
    uniform float repeat;
    uniform float speed;
    uniform float thickness;

    czm_material czm_getMaterial(czm_materialInput materialInput)
    {
        czm_material material = czm_getDefaultMaterial(materialInput);
        float offset = fract(czm_frameNumber * 45.0 / 10000.0);
        offset *= -speed;
        float sp = 1.0 / repeat;
        vec2 st = materialInput.st;
        float dis = distance(st, vec2(0.5));
        float m = mod(dis + offset, sp);
        float a = step(sp*(1.0 - thickness), m);
        material.diffuse = color.rgb;
        material.alpha = a * color.a;
        return material;
    }`;
Material._materialCache.addMaterial(Material.CommunicationFlowType, {
  fabric: {
    type: Material.CommunicationFlowType,
    uniforms: {
      color: new Color(1.0, 1.0, 0.0, 0.5),
      repeat: 30.0,
      // offset: 0.0,
      speed: 0.1,
      thickness: 0.3
    },
    source: comFlowMaterial
  },
  translucent: function (material) {
    // return false; // alpha 设置无效
    return true;
  }
});

/**
 * @name PolylineFlowType
 */
Material.PolylineFlowType = 'PolylineFlow';
const lineFlowMaterial = `
        uniform vec4 color;
        uniform float speed;
        uniform float gradient;
        uniform float percent;
        uniform float number;

        czm_material czm_getMaterial(czm_materialInput materialInput){
            czm_material material = czm_getDefaultMaterial(materialInput);
            vec2 st = materialInput.st;
            float t = fract(czm_frameNumber * speed / 1000.0);
            // t *= (1.0 + percent);
            // float alpha = smoothstep(t- percent, t, st.s) * step(-t, -st.s);
            
            float tTemp = t;
            float tAdd = t;
            float tTemp2 = t * (1.0 + percent);
            float tAdd2 = t * (1.0 + percent);
            float alpha = 0.0;
            // 'i' : Loop index cannot be compared with non-constant expression
            for (float i=0.; i < 50.0; i+=1.0){
                if(i >= number){
                    break;
                }
                alpha = smoothstep(tTemp2 - percent, tTemp2, st.s) * step(-tTemp2, -st.s);
                tTemp -= 1.0/number;
                tTemp2 = tTemp * (1.0 + percent);
                // tTemp *= (1.0 - 1.0/number + percent);
                
                if(alpha > 0.0){
                    break;
                }
                alpha = smoothstep(tAdd2 - percent, tAdd2, st.s) * step(-tAdd2, -st.s);
                tAdd += 1.0/number;
                tAdd2 = tAdd * (1.0 + percent);
                // tAdd *= (1.0 + 1.0/number + percent);
                if(alpha > 0.0){
                    break;
                }
            }
            
            alpha += gradient;
            material.diffuse = color.rgb;
            material.alpha = alpha;
            return material;
        }`;
Material._materialCache.addMaterial(Material.PolylineFlowType, {
  fabric: {
    type: Material.PolylineFlowType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 45,
      percent: 0.03,
      gradient: 0.2,
      number: 5
    },
    source: lineFlowMaterial
  },
  translucent: function (material) {
    return true;
  }
});

/**
 * @name PolylineTrailType
 */
Material.PolylineTrailType = 'PolylineTrail';
const LineTrailMaterial = `
        uniform sampler2D image;
        uniform float speed;
        uniform vec4 color;
        uniform vec2 repeat;

        czm_material czm_getMaterial(czm_materialInput materialInput){
            czm_material material = czm_getDefaultMaterial(materialInput);
            vec2 st = repeat * materialInput.st;
            float time = fract(czm_frameNumber * speed / 1000.0);
            vec4 colorImage = texture2D(image, vec2(fract(st.s - time), st.t));
            if(color.a == 0.0){
                material.alpha = colorImage.a;
                material.diffuse = colorImage.rgb;
            }else{
                material.alpha = colorImage.a * color.a;
                material.diffuse = max(color.rgb * material.alpha * 3.0, color.rgb);
            }
            return material;
        }`;

Material._materialCache.addMaterial(Material.PolylineTrailType, {
  fabric: {
    type: Material.PolylineTrailType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      image: Material.DefaultImageId,
      speed: 45,
      repeat: new Cartesian2(1, 1)
    },
    source: LineTrailMaterial
  },
  translucent: function (material) {
    return true;
  }
});

/**
 * @name PolylineMilitaryType
 */
Material.PolylineMilitaryType = 'PolylineMilitary';
const lineMaterialV1 = `
    uniform vec4 color;
    uniform sampler2D imageHead;
    uniform sampler2D image;
    uniform vec2 repeat;

    czm_material czm_getMaterial(czm_materialInput materialInput)
    {
        czm_material material = czm_getDefaultMaterial(materialInput);
        vec2 st = materialInput.st * repeat;
        vec4 colorImageHead = texture2D(imageHead, fract(st));
        vec4 colorImage = texture2D(image, fract(st));
        // support negative value
        if(repeat.x > 0.0){
          if(abs(st.s) < abs(repeat.x) - 1.0){
              material.alpha = colorImage.a * color.a;
          } else {
              material.alpha = colorImageHead.a * color.a;
          }
        } else {
          if(abs(st.s) > abs(repeat.x) - 1.0){
              material.alpha = colorImage.a * color.a;
          } else {
              material.alpha = colorImageHead.a * color.a;
          }
        }
        material.diffuse = color.rgb * material.alpha;
        return material;
    }`;

Material._materialCache.addMaterial(Material.PolylineMilitaryType, {
  fabric: {
    type: Material.PolylineMilitaryType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      imageHead: Material.DefaultImageId,
      image: Material.DefaultImageId,
      repeat: new Cartesian2(1, 1)
    },
    source: lineMaterialV1
  },
  translucent: function (material) {
    return true;
  }
});
