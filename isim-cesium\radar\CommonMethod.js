import { defined, Entity, PrimitiveCollection } from 'cesium';

/**
 * @package utils
 * @param entity {Entity}
 * @param hash {string}
 * @param primitives {PrimitiveCollection}
 */
export function removePrimitive(entity, hash, primitives) {
  const data = hash[entity.id];
  if (defined(data)) {
    const primitive = data.primitive;
    primitives.remove(primitive);
    if (!primitive.isDestroyed()) {
      primitive.destroy();
    }
    delete hash[entity.id];
  }
}
