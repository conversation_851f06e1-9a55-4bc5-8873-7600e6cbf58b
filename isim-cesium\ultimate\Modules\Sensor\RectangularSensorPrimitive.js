/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/03/18 18:50:35
 * */
import RectangularSensorVS from './Shaders/RectangularSensorVS.glsl?raw';
import RectangularSensorFS from './Shaders/RectangularSensorFS.glsl?raw';
import RectangularSensor from './Shaders/RectangularSensor.glsl?raw';
import RectangularSensorScanPlaneFS from './Shaders/RectangularSensorScanPlaneFS.glsl?raw';

const {
  BoundingSphere,
  Cartesian3,
  Color,
  combine,
  ComponentDatatype,
  defaultValue,
  defined,
  destroyObject,
  DeveloperError,
  Matrix4,
  PrimitiveType,
  Buffer,
  Matrix3,
  BufferUsage,
  DrawCommand,
  Pass,
  RenderState,
  ShaderProgram,
  ShaderSource,
  VertexArray,
  BlendingState,
  VertexFormat,
  SceneMode,
  Material,
  CullFace,
  Math: CesiumMath,
  // CesiumMath = Math, // 这个错误导致扫描面无法显示
  JulianDate
} = Cesium;

// let CesiumMath = Cesium.Math;
// console.log('CesiumMath错误', CesiumMath, Math);

// let defineProperties = Object.defineProperties;
const { sin, cos, tan, atan, asin } = Math;

const attributeLocations = {
  position: 0,
  normal: 1
};

export default function RectangularSensorPrimitive(options) {
  const self = this;
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  /**
   * 是否显示
   */
  this.show = defaultValue(options.show, true);

  /**
   * 切分程度
   */
  this.slice = defaultValue(options.slice, 32);

  /**
   * 传感器的模型矩阵
   */
  this.modelMatrix = Matrix4.clone(options.modelMatrix, new Matrix4());
  this._modelMatrix = new Matrix4();
  this._computedModelMatrix = new Matrix4();
  this._computedScanPlaneModelMatrix = new Matrix4();

  /**
   * 传感器的半径
   */
  this.radius = defaultValue(options.radius, Number.POSITIVE_INFINITY);
  this._radius = undefined;

  /**
   * 传感器水平半角
   */
  this.xHalfAngle = defaultValue(options.xHalfAngle, 0);
  this._xHalfAngle = undefined;

  /**
   * 传感器垂直半角
   */
  this.yHalfAngle = defaultValue(options.yHalfAngle, 0);
  this._yHalfAngle = undefined;

  /**
   * 线的颜色
   */
  this.lineColor = defaultValue(options.lineColor, Color.WHITE);

  /**
   * 是否显示扇面的线
   */
  this.showSectorLines = defaultValue(options.showSectorLines, true);

  /**
   * 是否显示扇面和圆顶面连接的线
   */
  this.showSectorSegmentLines = defaultValue(options.showSectorSegmentLines, true);

  /**
   * 是否显示侧面
   */
  this.showLateralSurfaces = defaultValue(options.showLateralSurfaces, true);

  /**
   * 目前用的统一材质
   * @type {Material}
   */
  this.material = defined(options.material) ? options.material : Material.fromType(Material.ColorType);
  this._material = undefined;
  this._translucent = undefined;

  /**
   * 侧面材质
   * @type {Material}
   */
  this.lateralSurfaceMaterial = defined(options.lateralSurfaceMaterial) ? options.lateralSurfaceMaterial : Material.fromType(Material.ColorType);
  this._lateralSurfaceMaterial = undefined;
  this._lateralSurfaceTranslucent = undefined;

  /**
   * 是否显示圆顶表面
   */
  this.showDomeSurfaces = defaultValue(options.showDomeSurfaces, true);

  /**
   * 圆顶表面材质
   * @type {Material}
   */
  this.domeSurfaceMaterial = defined(options.domeSurfaceMaterial) ? options.domeSurfaceMaterial : Material.fromType(Material.ColorType);
  this._domeSurfaceMaterial = undefined;

  /**
   * 是否显示圆顶面线
   */
  this.showDomeLines = defaultValue(options.showDomeLines, true);

  /**
   * 是否显示与地球相交的线
   */
  this.showIntersection = defaultValue(options.showIntersection, true);

  /**
   * 与地球相交的线的颜色
   */
  this.intersectionColor = defaultValue(options.intersectionColor, Color.WHITE);

  /**
   * 与地球相交的线的宽度（像素）
   */
  this.intersectionWidth = defaultValue(options.intersectionWidth, 5.0);

  /**
   * 是否穿过地球
   */
  this.showThroughEllipsoid = defaultValue(options.showThroughEllipsoid, false);
  this._showThroughEllipsoid = undefined;

  /**
   * 是否显示扫描面
   */
  this.showScanPlane = defaultValue(options.showScanPlane, true);

  /**
   * 扫描面颜色
   */
  this.scanPlaneColor = defaultValue(options.scanPlaneColor, Color.WHITE);

  /**
   * 扫描面模式 垂直vertical/水平horizontal
   */
  this.scanPlaneMode = defaultValue(options.scanPlaneMode, 'horizontal');

  /**
   * 扫描速率
   */
  this.scanPlaneRate = defaultValue(options.scanPlaneRate, 10);

  this._scanePlaneXHalfAngle = 0;
  this._scanePlaneYHalfAngle = 0;

  // 时间计算的起点
  this._time = JulianDate.now();

  this._boundingSphere = new BoundingSphere();
  this._boundingSphereWC = new BoundingSphere();

  // 扇面 sector
  this._sectorFrontCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.TRIANGLES,
    boundingVolume: this._boundingSphereWC
  });
  this._sectorBackCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.TRIANGLES,
    boundingVolume: this._boundingSphereWC
  });
  this._sectorVA = undefined;

  // 扇面边线 sectorLine
  this._sectorLineCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.LINES,
    boundingVolume: this._boundingSphereWC
  });
  this._sectorLineVA = undefined;

  // 扇面分割线 sectorSegmentLine
  this._sectorSegmentLineCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.LINES,
    boundingVolume: this._boundingSphereWC
  });
  this._sectorSegmentLineVA = undefined;

  // 弧面 dome
  this._domeFrontCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.TRIANGLES,
    boundingVolume: this._boundingSphereWC
  });
  this._domeBackCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.TRIANGLES,
    boundingVolume: this._boundingSphereWC
  });
  this._domeVA = undefined;

  // 弧面线 domeLine
  this._domeLineCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.LINES,
    boundingVolume: this._boundingSphereWC
  });
  this._domeLineVA = undefined;

  // 扫描面 scanPlane/scanRadial
  this._scanPlaneFrontCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.TRIANGLES,
    boundingVolume: this._boundingSphereWC
  });
  this._scanPlaneBackCommand = new DrawCommand({
    owner: this,
    primitiveType: PrimitiveType.TRIANGLES,
    boundingVolume: this._boundingSphereWC
  });

  this._scanRadialCommand = undefined;

  this._colorCommands = [];

  this._frontFaceRS = undefined;
  this._backFaceRS = undefined;
  this._sp = undefined;

  this._uniforms = {
    u_type: function u_type() {
      return 0; // 面
    },
    u_xHalfAngle: function u_xHalfAngle() {
      return self.xHalfAngle;
    },
    u_yHalfAngle: function u_yHalfAngle() {
      return self.yHalfAngle;
    },
    u_radius: function u_radius() {
      return self.radius;
    },
    u_showThroughEllipsoid: function u_showThroughEllipsoid() {
      return self.showThroughEllipsoid;
    },
    u_showIntersection: function u_showIntersection() {
      return self.showIntersection;
    },
    u_intersectionColor: function u_intersectionColor() {
      return self.intersectionColor;
    },
    u_intersectionWidth: function u_intersectionWidth() {
      return self.intersectionWidth;
    },
    u_normalDirection: function u_normalDirection() {
      return 1.0;
    },
    u_lineColor: function u_lineColor() {
      return self.lineColor;
    }
  };

  this._scanUniforms = {
    u_xHalfAngle: function u_xHalfAngle() {
      return self._scanePlaneXHalfAngle;
    },
    u_yHalfAngle: function u_yHalfAngle() {
      return self._scanePlaneYHalfAngle;
    },
    u_radius: function u_radius() {
      return self.radius;
    },
    u_color: function u_color() {
      return self.scanPlaneColor;
    },
    u_showThroughEllipsoid: function u_showThroughEllipsoid() {
      return self.showThroughEllipsoid;
    },
    u_showIntersection: function u_showIntersection() {
      return self.showIntersection;
    },
    u_intersectionColor: function u_intersectionColor() {
      return self.intersectionColor;
    },
    u_intersectionWidth: function u_intersectionWidth() {
      return self.intersectionWidth;
    },
    u_normalDirection: function u_normalDirection() {
      return 1.0;
    },
    u_lineColor: function u_lineColor() {
      return self.lineColor;
    }
  };
}

RectangularSensorPrimitive.prototype.update = function (frameState) {
  const mode = frameState.mode;
  if (!this.show || mode !== SceneMode.SCENE3D) {
    return;
  }
  let createVS = false; // 画 Vertex
  let createRS = false;
  let createSP = false;

  const xHalfAngle = this.xHalfAngle;
  const yHalfAngle = this.yHalfAngle;

  if (xHalfAngle < 0.0 || yHalfAngle < 0.0) {
    throw new DeveloperError('halfAngle must be greater than or equal to zero.');
  }
  if (xHalfAngle === 0.0 || yHalfAngle === 0.0) {
    return;
  }
  if (this._xHalfAngle !== xHalfAngle || this._yHalfAngle !== yHalfAngle) {
    this._xHalfAngle = xHalfAngle;
    this._yHalfAngle = yHalfAngle;
    createVS = true;
    createRS = true;
  }

  const radius = this.radius;
  if (radius < 0.0) {
    throw new DeveloperError('this.radius must be greater than or equal to zero.');
  }
  let radiusChanged = false;
  if (this._radius !== radius) {
    radiusChanged = true;
    this._radius = radius;
    this._boundingSphere = new BoundingSphere(Cartesian3.ZERO, this.radius);
  }

  const modelMatrixChanged = !Matrix4.equals(this.modelMatrix, this._modelMatrix);
  if (modelMatrixChanged || radiusChanged) {
    Matrix4.clone(this.modelMatrix, this._modelMatrix);
    Matrix4.multiplyByUniformScale(this.modelMatrix, this.radius, this._computedModelMatrix);
    BoundingSphere.transform(this._boundingSphere, this.modelMatrix, this._boundingSphereWC);
  }

  const showThroughEllipsoid = this.showThroughEllipsoid;
  if (this._showThroughEllipsoid !== this.showThroughEllipsoid) {
    this._showThroughEllipsoid = showThroughEllipsoid;
    createRS = true;
  }

  const material = this.material;
  if (this._material !== material) {
    this._material = material;
    createRS = true;
    createSP = true;
  }
  const translucent = material.isTranslucent();

  if (this._translucent !== translucent) {
    this._translucent = translucent;
    createRS = true;
  }

  // 扫描面
  if (this.showScanPlane) {
    const time = frameState.time;
    const timeDiff = JulianDate.secondsDifference(time, this._time);
    if (timeDiff < 0) {
      this._time = JulianDate.clone(time, this._time);
    }
    const percentage = Math.max((timeDiff % this.scanPlaneRate) / this.scanPlaneRate, 0);
    let angle;

    if (this.scanPlaneMode === 'horizontal') {
      angle = 2 * yHalfAngle * percentage - yHalfAngle;
      const cosYHalfAngle = cos(angle);
      const tanXHalfAngle = tan(xHalfAngle);

      this._scanePlaneXHalfAngle = atan(cosYHalfAngle * tanXHalfAngle);
      this._scanePlaneYHalfAngle = angle;
      Matrix3.fromRotationX(this._scanePlaneYHalfAngle, matrix3Scratch);
    } else {
      angle = 2 * xHalfAngle * percentage - xHalfAngle;
      const tanYHalfAngle = tan(yHalfAngle);
      const cosXHalfAngle = cos(angle);

      const maxY = atan(cosXHalfAngle * tanYHalfAngle);
      this._scanePlaneXHalfAngle = angle;
      this._scanePlaneYHalfAngle = maxY;
      Matrix3.fromRotationY(this._scanePlaneXHalfAngle, matrix3Scratch);
    }

    Matrix4.multiplyByMatrix3(this.modelMatrix, matrix3Scratch, this._computedScanPlaneModelMatrix);
    Matrix4.multiplyByUniformScale(this._computedScanPlaneModelMatrix, this.radius, this._computedScanPlaneModelMatrix);
  }

  if (createVS) {
    createVertexArray(this, frameState);
  }
  if (createRS) {
    createRenderState(this, showThroughEllipsoid, translucent);
  }
  if (createSP) {
    createShaderProgram(this, frameState, material);
  }
  if (createRS || createSP) {
    // 重新绘制Sensor，包括Sensor侧面
    createCommands(this, translucent);
  }

  const commandList = frameState.commandList;
  const passes = frameState.passes;
  const colorCommands = this._colorCommands;
  if (passes.render) {
    for (let i = 0, len = colorCommands.length; i < len; i++) {
      const colorCommand = colorCommands[i];
      commandList.push(colorCommand);
    }
  }
};

RectangularSensorPrimitive.prototype.isDestroyed = function () {
  return false;
};

RectangularSensorPrimitive.prototype.destroy = function () {
  // add By Lemonsky
  // console.log('给primitive增加空的destroy()函数，因为该类需要和Cesium.primitive 保持统一,viewer在清除primitive时，需要调用destroy函数');
  return destroyObject(this);
};

let matrix3Scratch = new Matrix3();
const nScratch = new Cartesian3();

// region -- VertexArray --

/**
 * 计算zoy面和zoy面单位扇形位置
 * @param primitive
 * @param xHalfAngle
 * @param yHalfAngle
 * @returns {{zoy: Array, zox: Array}}
 */
function computeUnitPosition(primitive, xHalfAngle, yHalfAngle) {
  const slice = primitive.slice;

  // 以中心为角度
  const cosYHalfAngle = cos(yHalfAngle);
  const tanYHalfAngle = tan(yHalfAngle);
  const cosXHalfAngle = cos(xHalfAngle);
  const tanXHalfAngle = tan(xHalfAngle);

  const maxY = atan(cosXHalfAngle * tanYHalfAngle);
  const maxX = atan(cosYHalfAngle * tanXHalfAngle);

  // ZOY面单位圆
  const zoy = [];
  for (let i = 0; i < slice; i++) {
    const phi = (2 * maxY * i) / (slice - 1) - maxY;
    zoy.push(new Cartesian3(0, sin(phi), cos(phi)));
  }
  // zox面单位圆
  const zox = [];
  for (let i = 0; i < slice; i++) {
    const phi = (2 * maxX * i) / (slice - 1) - maxX;
    zox.push(new Cartesian3(sin(phi), 0, cos(phi)));
  }

  return {
    zoy: zoy,
    zox: zox
  };
}

/**
 * 计算扇面的位置
 * @param primitive
 * @param unitPosition
 * @returns {Array}
 */
function computeSectorPositions(primitive, unitPosition) {
  const xHalfAngle = primitive.xHalfAngle,
    yHalfAngle = primitive.yHalfAngle,
    zoy = unitPosition.zoy,
    zox = unitPosition.zox;
  const positions = [];

  // zoy面沿y轴逆时针转xHalfAngle
  let matrix3 = Matrix3.fromRotationY(xHalfAngle, matrix3Scratch);
  positions.push(
    zoy.map(function (p) {
      return Matrix3.multiplyByVector(matrix3, p, new Cesium.Cartesian3());
    })
  );

  // zox面沿x轴顺时针转yHalfAngle
  matrix3 = Matrix3.fromRotationX(-yHalfAngle, matrix3Scratch);
  positions.push(
    zox
      .map(function (p) {
        return Matrix3.multiplyByVector(matrix3, p, new Cesium.Cartesian3());
      })
      .reverse()
  );

  // zoy面沿y轴顺时针转xHalfAngle
  matrix3 = Matrix3.fromRotationY(-xHalfAngle, matrix3Scratch);
  positions.push(
    zoy
      .map(function (p) {
        return Matrix3.multiplyByVector(matrix3, p, new Cesium.Cartesian3());
      })
      .reverse()
  );

  // zox面沿x轴逆时针转yHalfAngle
  matrix3 = Matrix3.fromRotationX(yHalfAngle, matrix3Scratch);
  positions.push(
    zox.map(function (p) {
      return Matrix3.multiplyByVector(matrix3, p, new Cesium.Cartesian3());
    })
  );
  return positions;
}

/**
 * 创建扇面顶点
 * @param context
 * @param positions
 * @returns {*}
 */
function createSectorVertexArray(context, positions) {
  const planeLength = Array.prototype.concat.apply([], positions).length - positions.length;
  const vertices = new Float32Array(2 * 3 * 3 * planeLength);

  let k = 0;
  for (let i = 0, len = positions.length; i < len; i++) {
    const planePositions = positions[i];
    const n = Cartesian3.normalize(Cartesian3.cross(planePositions[0], planePositions[planePositions.length - 1], nScratch), nScratch);
    for (let j = 0, planeLength = planePositions.length - 1; j < planeLength; j++) {
      vertices[k++] = 0.0;
      vertices[k++] = 0.0;
      vertices[k++] = 0.0;
      vertices[k++] = -n.x;
      vertices[k++] = -n.y;
      vertices[k++] = -n.z;

      vertices[k++] = planePositions[j].x;
      vertices[k++] = planePositions[j].y;
      vertices[k++] = planePositions[j].z;
      vertices[k++] = -n.x;
      vertices[k++] = -n.y;
      vertices[k++] = -n.z;

      vertices[k++] = planePositions[j + 1].x;
      vertices[k++] = planePositions[j + 1].y;
      vertices[k++] = planePositions[j + 1].z;
      vertices[k++] = -n.x;
      vertices[k++] = -n.y;
      vertices[k++] = -n.z;
    }
  }

  const vertexBuffer = Buffer.createVertexBuffer({
    context: context,
    typedArray: vertices,
    usage: BufferUsage.STATIC_DRAW
  });

  const stride = 2 * 3 * Float32Array.BYTES_PER_ELEMENT;

  const attributes = [
    {
      index: attributeLocations.position,
      vertexBuffer: vertexBuffer,
      componentsPerAttribute: 3,
      componentDatatype: ComponentDatatype.FLOAT,
      offsetInBytes: 0,
      strideInBytes: stride
    },
    {
      index: attributeLocations.normal,
      vertexBuffer: vertexBuffer,
      componentsPerAttribute: 3,
      componentDatatype: ComponentDatatype.FLOAT,
      offsetInBytes: 3 * Float32Array.BYTES_PER_ELEMENT,
      strideInBytes: stride
    }
  ];

  return new VertexArray({
    context: context,
    attributes: attributes
  });
}

/**
 * 创建扇面边线顶点
 * @param context
 * @param positions
 * @returns {*}
 */
function createSectorLineVertexArray(context, positions) {
  const planeLength = positions.length;
  const vertices = new Float32Array(3 * 3 * planeLength);

  let k = 0;
  let planePositions;
  for (let i = 0, len = positions.length; i < len; i++) {
    planePositions = positions[i];
    vertices[k++] = 0.0;
    vertices[k++] = 0.0;
    vertices[k++] = 0.0;

    vertices[k++] = planePositions[0].x;
    vertices[k++] = planePositions[0].y;
    vertices[k++] = planePositions[0].z;
  }

  const vertexBuffer = Buffer.createVertexBuffer({
    context: context,
    typedArray: vertices,
    usage: BufferUsage.STATIC_DRAW
  });

  const stride = 3 * Float32Array.BYTES_PER_ELEMENT;

  const attributes = [
    {
      index: attributeLocations.position,
      vertexBuffer: vertexBuffer,
      componentsPerAttribute: 3,
      componentDatatype: ComponentDatatype.FLOAT,
      offsetInBytes: 0,
      strideInBytes: stride
    }
  ];

  return new VertexArray({
    context: context,
    attributes: attributes
  });
}

/**
 * 创建扇面圆顶面连接线顶点
 * @param context
 * @param positions
 * @returns {*}
 */
function createSectorSegmentLineVertexArray(context, positions) {
  const planeLength = Array.prototype.concat.apply([], positions).length - positions.length;
  const vertices = new Float32Array(3 * 3 * planeLength);

  let k = 0;
  for (let i = 0, len = positions.length; i < len; i++) {
    const planePositions = positions[i];

    for (let j = 0, planeLength = planePositions.length - 1; j < planeLength; j++) {
      vertices[k++] = planePositions[j].x;
      vertices[k++] = planePositions[j].y;
      vertices[k++] = planePositions[j].z;

      vertices[k++] = planePositions[j + 1].x;
      vertices[k++] = planePositions[j + 1].y;
      vertices[k++] = planePositions[j + 1].z;
    }
  }

  const vertexBuffer = Buffer.createVertexBuffer({
    context: context,
    typedArray: vertices,
    usage: BufferUsage.STATIC_DRAW
  });

  const stride = 3 * Float32Array.BYTES_PER_ELEMENT;

  const attributes = [
    {
      index: attributeLocations.position,
      vertexBuffer: vertexBuffer,
      componentsPerAttribute: 3,
      componentDatatype: ComponentDatatype.FLOAT,
      offsetInBytes: 0,
      strideInBytes: stride
    }
  ];

  return new VertexArray({
    context: context,
    attributes: attributes
  });
}

/**
 * 创建圆顶面顶点
 * @param context
 */
function createDomeVertexArray(context) {
  const geometry = Cesium.EllipsoidGeometry.createGeometry(
    new Cesium.EllipsoidGeometry({
      vertexFormat: VertexFormat.POSITION_ONLY,
      stackPartitions: 32,
      slicePartitions: 32
    })
  );

  return VertexArray.fromGeometry({
    context: context,
    geometry: geometry,
    attributeLocations: attributeLocations,
    bufferUsage: BufferUsage.STATIC_DRAW,
    interleave: false
  });
}

/**
 * 创建圆顶面连线顶点
 * @param context
 */
function createDomeLineVertexArray(context) {
  const geometry = Cesium.EllipsoidOutlineGeometry.createGeometry(
    new Cesium.EllipsoidOutlineGeometry({
      vertexFormat: VertexFormat.POSITION_ONLY,
      stackPartitions: 32,
      slicePartitions: 32
    })
  );

  return VertexArray.fromGeometry({
    context: context,
    geometry: geometry,
    attributeLocations: attributeLocations,
    bufferUsage: BufferUsage.STATIC_DRAW,
    interleave: false
  });
}

/**
 * 创建扫描面顶点
 * @param context
 * @param positions
 * @returns {*}
 */
function createScanPlaneVertexArray(context, positions) {
  const planeLength = positions.length - 1;
  const vertices = new Float32Array(3 * 3 * planeLength);

  let k = 0;
  for (let i = 0; i < planeLength; i++) {
    vertices[k++] = 0.0;
    vertices[k++] = 0.0;
    vertices[k++] = 0.0;

    vertices[k++] = positions[i].x;
    vertices[k++] = positions[i].y;
    vertices[k++] = positions[i].z;

    vertices[k++] = positions[i + 1].x;
    vertices[k++] = positions[i + 1].y;
    vertices[k++] = positions[i + 1].z;
  }

  const vertexBuffer = Buffer.createVertexBuffer({
    context: context,
    typedArray: vertices,
    usage: BufferUsage.STATIC_DRAW
  });

  const stride = 3 * Float32Array.BYTES_PER_ELEMENT;

  const attributes = [
    {
      index: attributeLocations.position,
      vertexBuffer: vertexBuffer,
      componentsPerAttribute: 3,
      componentDatatype: ComponentDatatype.FLOAT,
      offsetInBytes: 0,
      strideInBytes: stride
    }
  ];

  return new VertexArray({
    context: context,
    attributes: attributes
  });
}
/**
 * 创建顶点Array
 * @param {*} primitive
 * @param {*} frameState
 */
function createVertexArray(primitive, frameState) {
  const context = frameState.context;

  const unitSectorPositions = computeUnitPosition(primitive, primitive.xHalfAngle, primitive.yHalfAngle);
  const positions = computeSectorPositions(primitive, unitSectorPositions);

  // 显示扇面
  if (primitive.showLateralSurfaces) {
    primitive._sectorVA = createSectorVertexArray(context, positions);
  }

  // 显示扇面线
  if (primitive.showSectorLines) {
    primitive._sectorLineVA = createSectorLineVertexArray(context, positions);
  }

  // 显示扇面圆顶面的交线
  if (primitive.showSectorSegmentLines) {
    primitive._sectorSegmentLineVA = createSectorSegmentLineVertexArray(context, positions);
  }

  // 显示弧面
  if (primitive.showDomeSurfaces) {
    primitive._domeVA = createDomeVertexArray(context);
  }

  // 显示弧面线
  if (primitive.showDomeLines) {
    primitive._domeLineVA = createDomeLineVertexArray(context);
  }

  // 显示扫描面
  if (primitive.showScanPlane) {
    if (primitive.scanPlaneMode === 'horizontal') {
      const unitScanPlanePositions = computeUnitPosition(primitive, CesiumMath.PI_OVER_TWO, 0);
      primitive._scanPlaneVA = createScanPlaneVertexArray(context, unitScanPlanePositions.zox);
    } else {
      const unitScanPlanePositions = computeUnitPosition(primitive, 0, CesiumMath.PI_OVER_TWO);
      primitive._scanPlaneVA = createScanPlaneVertexArray(context, unitScanPlanePositions.zoy);
    }
  }
}

function createCommonShaderProgram(primitive, frameState, material) {
  const context = frameState.context;

  const vs = RectangularSensorVS;
  const fs = new ShaderSource({
    sources: [RectangularSensor, material.shaderSource, RectangularSensorFS]
  });

  primitive._sp = ShaderProgram.replaceCache({
    context: context,
    shaderProgram: primitive._sp,
    vertexShaderSource: vs,
    fragmentShaderSource: fs,
    attributeLocations: attributeLocations
  });

  const pickFS = new ShaderSource({
    sources: [RectangularSensor, material.shaderSource, RectangularSensorFS],
    pickColorQualifier: 'uniform'
  });

  primitive._pickSP = ShaderProgram.replaceCache({
    context: context,
    shaderProgram: primitive._pickSP,
    vertexShaderSource: vs,
    fragmentShaderSource: pickFS,
    attributeLocations: attributeLocations
  });
}

function createScanPlaneShaderProgram(primitive, frameState, material) {
  const context = frameState.context;

  const vs = RectangularSensorVS;
  const fs = new ShaderSource({
    sources: [RectangularSensor, material.shaderSource, RectangularSensorScanPlaneFS]
  });

  primitive._scanePlaneSP = ShaderProgram.replaceCache({
    context: context,
    shaderProgram: primitive._scanePlaneSP,
    vertexShaderSource: vs,
    fragmentShaderSource: fs,
    attributeLocations: attributeLocations
  });
}

function createShaderProgram(primitive, frameState, material) {
  createCommonShaderProgram(primitive, frameState, material);

  if (primitive.showScanPlane) {
    createScanPlaneShaderProgram(primitive, frameState, material);
  }
}

function createRenderState(primitive, showThroughEllipsoid, translucent) {
  if (translucent) {
    primitive._frontFaceRS = RenderState.fromCache({
      depthTest: {
        enabled: !showThroughEllipsoid
      },
      depthMask: false,
      blending: BlendingState.ALPHA_BLEND,
      cull: {
        enabled: true,
        face: CullFace.BACK
      }
    });

    primitive._backFaceRS = RenderState.fromCache({
      depthTest: {
        enabled: !showThroughEllipsoid
      },
      depthMask: false,
      blending: BlendingState.ALPHA_BLEND,
      cull: {
        enabled: true,
        face: CullFace.FRONT
      }
    });

    primitive._pickRS = RenderState.fromCache({
      depthTest: {
        enabled: !showThroughEllipsoid
      },
      depthMask: false,
      blending: BlendingState.ALPHA_BLEND
    });
  } else {
    primitive._frontFaceRS = RenderState.fromCache({
      depthTest: {
        enabled: !showThroughEllipsoid
      },
      depthMask: true
    });

    primitive._pickRS = RenderState.fromCache({
      depthTest: {
        enabled: true
      },
      depthMask: true
    });
  }
}
/**
 * 绘制方法
 * @param {*} primitive
 * @param {*} frontCommand
 * @param {*} backCommand
 * @param {*} frontFaceRS
 * @param {*} backFaceRS
 * @param {*} sp
 * @param {*} va
 * @param {*} uniforms
 * @param {*} modelMatrix
 * @param {*} translucent
 * @param {*} pass
 * @param {*} isLine
 */
function createCommand(primitive, frontCommand, backCommand, frontFaceRS, backFaceRS, sp, va, uniforms, modelMatrix, translucent, pass, isLine) {
  if (translucent && backCommand) {
    backCommand.vertexArray = va;
    backCommand.renderState = backFaceRS;
    backCommand.shaderProgram = sp;
    backCommand.uniformMap = combine(uniforms, primitive._material._uniforms);
    backCommand.uniformMap.u_normalDirection = function () {
      return -1.0;
    };
    backCommand.pass = pass;
    backCommand.modelMatrix = modelMatrix;
    primitive._colorCommands.push(backCommand);
  }

  frontCommand.vertexArray = va;
  frontCommand.renderState = frontFaceRS;
  frontCommand.shaderProgram = sp;
  frontCommand.uniformMap = combine(uniforms, primitive._material._uniforms);
  if (isLine) {
    frontCommand.uniformMap.u_type = function () {
      return 1;
    };
  }
  frontCommand.pass = pass;
  frontCommand.modelMatrix = modelMatrix;
  primitive._colorCommands.push(frontCommand);
}
/**
 * Sensor 面的绘制
 * @param {*} primitive
 * @param {*} translucent
 */
function createCommands(primitive, translucent) {
  primitive._colorCommands.length = 0;

  const pass = translucent ? Pass.TRANSLUCENT : Pass.OPAQUE;

  // 显示扇面
  if (primitive.showLateralSurfaces) {
    createCommand(
      primitive,
      primitive._sectorFrontCommand,
      primitive._sectorBackCommand,
      primitive._frontFaceRS,
      primitive._backFaceRS,
      primitive._sp,
      primitive._sectorVA,
      primitive._uniforms,
      primitive._computedModelMatrix,
      translucent,
      pass
    );
  }
  // 显示扇面线
  if (primitive.showSectorLines) {
    createCommand(
      primitive,
      primitive._sectorLineCommand,
      undefined,
      primitive._frontFaceRS,
      primitive._backFaceRS,
      primitive._sp,
      primitive._sectorLineVA,
      primitive._uniforms,
      primitive._computedModelMatrix,
      translucent,
      pass,
      true
    );
  }
  // 显示扇面交接线
  if (primitive.showSectorSegmentLines) {
    createCommand(
      primitive,
      primitive._sectorSegmentLineCommand,
      undefined,
      primitive._frontFaceRS,
      primitive._backFaceRS,
      primitive._sp,
      primitive._sectorSegmentLineVA,
      primitive._uniforms,
      primitive._computedModelMatrix,
      translucent,
      pass,
      true
    );
  }
  // 显示弧面
  if (primitive.showDomeSurfaces) {
    createCommand(
      primitive,
      primitive._domeFrontCommand,
      primitive._domeBackCommand,
      primitive._frontFaceRS,
      primitive._backFaceRS,
      primitive._sp,
      primitive._domeVA,
      primitive._uniforms,
      primitive._computedModelMatrix,
      translucent,
      pass
    );
  }
  // 显示弧面线
  if (primitive.showDomeLines) {
    createCommand(
      primitive,
      primitive._domeLineCommand,
      undefined,
      primitive._frontFaceRS,
      primitive._backFaceRS,
      primitive._sp,
      primitive._domeLineVA,
      primitive._uniforms,
      primitive._computedModelMatrix,
      translucent,
      pass,
      true
    );
  }
  // 显示扫描面
  if (primitive.showScanPlane) {
    createCommand(
      primitive,
      primitive._scanPlaneFrontCommand,
      primitive._scanPlaneBackCommand,
      primitive._frontFaceRS,
      primitive._backFaceRS,
      primitive._scanePlaneSP,
      primitive._scanPlaneVA,
      primitive._scanUniforms,
      primitive._computedScanPlaneModelMatrix,
      translucent,
      pass
    );
  }
}
