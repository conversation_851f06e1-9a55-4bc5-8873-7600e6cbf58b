/**
 * <AUTHOR>
 * @email jayjay<PERSON>@yeah.net
 * @date 2022/03/11 18:25:46
 * @description 自定义 DeveloperError
 * @version 1.0
 * */
import { defined, DeveloperError } from 'cesium';
export default class DeveloperWarn extends DeveloperError {
  name: string;
  author: string;
  constructor(message?: string) {
    super(message);
    this.name = 'DeveloperWarn';
    this.author = '<PERSON><PERSON>';
  }

  toString() {
    let str = `${this.name}: ${this.message}: ${this.author}`;

    if (defined(this.stack)) {
      str += `\n${this.stack.toString()}`;
    }
    return str;
  }
}
