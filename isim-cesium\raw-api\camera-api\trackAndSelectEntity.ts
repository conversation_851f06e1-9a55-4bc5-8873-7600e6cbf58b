import { Entity, JulianDate, Viewer } from 'cesium';
import { getViewer, onPreUpdate, setViewerValueByField } from 'isim-cesium';
import * as Cesium from 'cesium';

/**
 * 选中实体
 */
export const setSelectedEntity = setViewerValueByField('selectedEntity');

/**
 * 锁定实体
 */
export const setTrackedEntity = setViewerValueByField('trackedEntity');

let preUpdateClose: any;
/**
 * 设置第一人称视角
 * @param entity
 */
export function setFirstPerspective(entity?: Entity) {
  const viewer = getViewer();
  firstPerspectiveEvent(viewer, entity);
  preUpdateClose = onPreUpdate(firstPerspectiveEvent(viewer, entity));
}

export function removeFirstPerspective() {
  if (!preUpdateClose) {
    return;
  }
  preUpdateClose?.();
  preUpdateClose = undefined;
}

export function firstPerspectiveEvent(viewer: Viewer, entity?: Entity) {
  return function (_: any, time: JulianDate) {
    if (!entity?.orientation || !entity.position) {
      return;
    }
    const o = entity.orientation?.getValue(time);
    const p = entity.position?.getValue(time);
    const mtx3 = Cesium.Matrix3.fromQuaternion(o);
    const mtx4 = Cesium.Matrix4.fromRotationTranslation(mtx3, p);
    const hpr = Cesium.Transforms.fixedFrameToHeadingPitchRoll(mtx4);
    hpr.heading += Cesium.Math.PI_OVER_TWO;
    entity.show = false;
    viewer.camera.setView({
      destination: p,
      orientation: hpr
    });
  };
}
