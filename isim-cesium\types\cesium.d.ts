import { Cartesian3, Color, ExtrapolationType, ImageryProvider, NearFarScalar, Property, Viewer } from 'cesium';
import { MultipleSelectViewModel, TileCoordinatesImageryProvider } from 'isim-cesium';
import 'cesium';
import { WGS84GridLayer } from 'isim-cesium';
import { NavigationOptions } from 'cesium-navigation-es6';

declare module 'cesium' {
  export interface Viewer {
    bottomContainer: HTMLElement;
    _militarySource: any;
    _plotEntrance?: any;
    multipleSelectedEntities: MultipleSelectViewModel;
    sources: any;
  }

  export class PolylineCanvasMaterialProperty {
    static MilitaryType: {
      DASH_LINE_ARROW: number;
      LINE_ARROW: number;
      CORDON_V1: number;
      CORDON_V2: number;
      TWO_PART_LINE_ARROW: number;
      DEFENSE_LINE: number;
    };
  }

  export class Calculator {
    static calculateSingerArrowTexture(position: PositionProperty, options: {}): any[];
  }

  interface DashLineArrowMaterialPropertyConstructorType {
    color: Color;
    dashLength: number;
    leftSide: number;
  }

  export interface ConstantProperty {
    _value: any;
  }

  export class DashLineArrowMaterialProperty {
    constructor(options: DashLineArrowMaterialPropertyConstructorType);
  }

  export interface BillboardGraphics {
    scaleByDistance: NearFarScalar | Property | undefined;
  }

  export interface ConstantPositionProperty {
    _value: Cartesian3;
  }

  export interface ScreenSpaceEventHandler {
    _inputEvents: Record<string, any> | undefined;
  }

  export interface BillboardGraphics {
    image: ConstantProperty | undefined;
  }

  export interface Entity {
    billboard: BillboardGraphics | undefined;
    position: ConstantPositionProperty | SampledPositionProperty | undefined;
    // orientation: VelocityOrientationProperty | Property | undefined
    _plotType?: string;
  }

  export interface LabelGraphics {
    text: string | Property | undefined;
  }

  export class ImageryLayerCollection {
    addImageryProvider(imageryProvider: ImageryProvider | TileCoordinatesImageryProvider | WGS84GridLayer, index?: number): ImageryLayer;
  }

  export interface VelocityVectorProperty {
    forwardExtrapolationType: ExtrapolationType;
  }

  export interface VelocityOrientationProperty {
    forwardExtrapolationType: ExtrapolationType;
  }

  export interface PositionProperty {
    getInertialValue(time: JulianDate, result?: Cartesian3): Cartesian3;
    getOrbitFixedValue(time: JulianDate, value: any, result?: Cartesian3): any;
  }

  export function when(data: any): Promise<any> | Promise;

  export const VERSION: string;

  export function defaultValue(): void;
}

declare module 'yt-cesium' {
  export class Measure {
    constructor(viewer: Viewer);

    destroy(): void;

    _viewModel: any;

    rightCall(): void;
  }

  export const enum MeasureMode {
    ComponentDistance = 0,
    PointCoordinates = 1,
    SurfacePolylineDistance = 2,
    SurfaceArea = 3,
    SightLine = 4,
    Profiling = 5,
    Bearing = 6,
    SurfaceVolume = 7
  }
}

// 比例尺
declare module 'cesium-navigation-es6-1' {
  export default class CesiumNavigation {
    constructor(viewer: Viewer, options: NavigationOptions);
  }
}
