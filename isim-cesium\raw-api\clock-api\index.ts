import { getViewerField, ViewerNameType } from 'isim-cesium';
import { Clock } from 'cesium';

export * from './synchronizeTime';
export * from './clock-time-api';

export const getClock = getViewerField('clock');

/**
 * 设置速率
 * @param speed
 * @param viewerName
 */
export const setSpeed = setClockValueByField('multiplier');

export const getSpeed = getClockField('multiplier');
export const getMultiplier = getSpeed;

/**
 * 启动动画
 * @param animate
 * @param viewerName
 */

export const setShouldAnimate = setClockValueByField('shouldAnimate');

export const getShouldAnimate = getClockField('shouldAnimate');

/**
 * 获取clock的属性
 * @param field
 */
export function getClockField<T extends keyof Clock>(field: T) {
  return function (viewerName?: ViewerNameType) {
    return getClock(viewerName)[field];
  };
}

/**
 * 设置Clock的属性
 * @param field
 * @param defaultValue
 */
export function setClockValueByField<T extends keyof Clock>(field: T, defaultValue?: Clock[T]) {
  return function (value: Clock[T], viewerName?: ViewerNameType) {
    getClock(viewerName)[field] = value ?? defaultValue;
  };
}
