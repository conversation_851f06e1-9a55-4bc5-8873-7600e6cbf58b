/*
 * @Author: <PERSON>g<PERSON><PERSON>lang
 * @Date: 2023/4/11
 * @Version: 1.0
 * @Content: IndexFilter
 */
import SimInputFilterCom from './src/InputFilter.vue';

import { WithInstallCom } from '../../../type';

const SimInputFilter = SimInputFilterCom as WithInstallCom<typeof SimInputFilterCom>;
SimInputFilter.install = function (Vue) {
  Vue.component('SimInputFilter', SimInputFilterCom);
};

export { SimInputFilter };
