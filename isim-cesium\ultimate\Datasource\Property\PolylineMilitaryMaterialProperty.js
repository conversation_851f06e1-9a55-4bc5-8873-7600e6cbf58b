/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/06 13:39:29
 * @description Military Plot
 * @version 1.0
 * */
import { Cartesian2, Color, createPropertyDescriptor, defaultValue, Event, Material, Property } from 'cesium';
const DEF_REPEAT = new Cartesian2(1, 1);
const DEF_COLOR = Color.fromBytes(0, 255, 255, 255);

export default class PolylineMilitaryMaterialProperty {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    this._definitionChanged = new Event();
    this._color = undefined;
    this._colorSubscription = undefined;
    this._repeat = undefined;
    this._repeatSubscription = undefined;
    this._image = undefined;
    this._imageSubscription = undefined;
    this._imageHead = undefined;
    this._imageHeadSubscription = undefined;
    this.color = defaultValue(options.color, DEF_COLOR);
    this.repeat = defaultValue(options.repeat, DEF_REPEAT);
    this.image = options.image;
    this.imageHead = options.imageHead;
  }

  get isConstant() {
    return false;
  }

  get definitionChanged() {
    return this._definitionChanged;
  }

  getValue(time, result) {
    result = defaultValue(result, defaultValue.EMPTY_OBJECT);
    result.color = Property.getValueOrClonedDefault(this._color, time, DEF_COLOR, result.color);
    result.repeat = Property.getValueOrClonedDefault(this._repeat, time, DEF_REPEAT, result.repeat);
    result.image = Property.getValueOrUndefined(this._image, time);
    result.imageHead = Property.getValueOrUndefined(this._imageHead, time);
    return result;
  }

  getType(time) {
    return Material.PolylineMilitaryType;
  }

  equals(other) {
    return (
      this === other ||
      (other instanceof PolylineMilitaryMaterialProperty &&
        Property.equals(this._color, other._color) &&
        Property.equals(this._repeat, other._repeat) &&
        Property.equals(this._image, other._image) &&
        Property.equals(this._imageHead, other._imageHead))
    );
  }
}

Object.defineProperties(PolylineMilitaryMaterialProperty.prototype, {
  color: createPropertyDescriptor('color'),
  repeat: createPropertyDescriptor('repeat'),
  image: createPropertyDescriptor('image'),
  imageHead: createPropertyDescriptor('imageHead')
});
