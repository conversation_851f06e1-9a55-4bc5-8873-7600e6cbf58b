<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/1
* @Version : 1.0
* @Content : Model
-->
<template>
  <Teleport :to="props.target" :disabled="props.appendToBody">
    <Transition name="el-fade-in">
      <sim-mask v-show="props.visible">
        <div class="sim-model" :class="customClass" :style="modelStyle">
          <div v-if="headerShow" class="sim-model__header">
            <slot name="header">
              <span class="sim-model__title">{{ title }}</span>
            </slot>
            <div v-if="closeShow" class="sim-model__close" @click="handleClose">
              <sim-icon name="guanbi" color="var(--secondary-text-color)" />
            </div>
          </div>
          <div class="sim-model__content" :style="contentStyle">
            <slot />
          </div>
          <div v-if="footerShow" class="sim-model__footer">
            <slot name="footer">
              <sim-button v-if="showConfirmButton" :size="buttonSize" type="primary" :class="confirmButtonClass" @click="handleSure"
                >{{ confirmButtonText }}
              </sim-button>
              <sim-button v-if="showCancelButton" :size="buttonSize" :class="cancelButtonClass" @click="handleCancel">{{
                cancelButtonText
              }}</sim-button>
            </slot>
          </div>
        </div>
      </sim-mask>
    </Transition>
  </Teleport>
</template>

<script lang="ts" setup>
import { SimButton, SimIcon } from 'isim-ui';
import { computed, type PropType } from 'vue';
import { isString } from '@/utils/data-type';
import SimMask from '../../Mask/src/Mask.vue';

defineOptions({
  name: 'SimModel',
  inheritAttrs: false
});

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  customClass: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  },
  footerShow: {
    type: Boolean,
    default: true
  },
  headerShow: {
    type: Boolean,
    default: true
  },
  closeShow: {
    type: Boolean,
    default: true
  },
  width: {
    type: [Number, String],
    default: '500px'
  },
  height: {
    type: [Number, String],
    default: '300px'
  },
  appendToBody: {
    type: Boolean,
    default: false
  },
  target: {
    type: [String, HTMLElement],
    default: 'body'
  },
  //是否显示取消按钮
  showCancelButton: {
    type: Boolean,
    default: true
  },
  // 是否显示确定按钮
  showConfirmButton: {
    type: Boolean,
    default: true
  },
  // 取消按钮的文本内容
  cancelButtonText: {
    type: String,
    default: '取消'
  },
  // 确定按钮的文本内容
  confirmButtonText: {
    type: String,
    default: '确定'
  },
  // 取消按钮的自定义类名
  cancelButtonClass: {
    type: String,
    default: ''
  },
  // 确定按钮的自定义类名
  confirmButtonClass: {
    type: String,
    default: ''
  },
  buttonSize: {
    type: String as PropType<'medium' | 'small'>,
    default: 'small'
  }
});

const emits = defineEmits(['update:visible', 'handle-sure', 'handle-cancel', 'handle-close']);

const updateVisible = (isShow: boolean) => {
  emits('update:visible', isShow);
};

const handleCancel = (e: MouseEvent) => {
  updateVisible(false);
  emits('handle-cancel', e);
};

const handleSure = (e: MouseEvent) => {
  emits('handle-sure', e);
};

const handleClose = (e: MouseEvent) => {
  updateVisible(false);
  emits('handle-close', e);
};

const appendUnit = (data?: number | string) => {
  return `${data}px`;
};

const widthNum = computed(() => (isString(props.width) ? props.width : appendUnit(props.width)));

const heightNum = computed(() => (isString(props.height) ? props.height : appendUnit(props.height)));

const contentHeight = computed(() => {
  if (props.footerShow && props.headerShow) {
    return `calc(100% - var(--sim-model-header-height) - var(--sim-model-footer-height)`;
  }
  if (props.footerShow) {
    return `calc(100% - var(--sim-model-footer-height)`;
  }
  if (props.headerShow) {
    return `calc(100% - var(--sim-model-header-height)`;
  }
  return '100%';
});
const modelStyle = computed(() => ({
  width: widthNum.value,
  height: heightNum.value
}));

const contentStyle = computed(() => ({ height: contentHeight.value }));
</script>

<style lang="less">
.sim-model {
  position: absolute;
  top: 50%;
  left: 50%;
  max-width: 1500px;
  transform: translate(-50%, -50%);
  background: var(--model-bg-color, rgb(0, 45, 49));
  border: 1px solid var(--primary-color-5);
  box-sizing: border-box;
  --sim-model-header-bgc: rgba(255, 255, 255, 0.1);
  --sim-model-footer-bgc: rgba(255, 255, 255, 0.1);
  --sim-model-header-height: 48px;
  --sim-model-footer-height: 48px;

  &__header {
    position: relative;
    width: 100%;
    height: var(--sim-model-header-height);
    display: flex;
    align-items: center;
    overflow-x: clip;
    box-sizing: border-box;
    color: white;
    padding-left: 20px;
    // background: var(--sim-model-header-bgc);
    background-color: var(--secondary-header-bg-color);

    .sim-model__close {
      position: absolute;
      top: 50%;
      right: 0;
      width: 47px;
      height: 29px;
      transform: translateY(-50%);
      text-align: center;
      line-height: 36px;
      cursor: pointer;
      img.close {
        margin-left: 4px;
      }
    }
  }

  &__title {
    font-size: 16px;
    font-weight: 700;
  }

  &__content {
    overflow: auto;
    padding: 16px;
    box-sizing: border-box;
    background-color: var(--primary-bg-color);
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 8px;
    height: var(--sim-model-footer-height);
    // background: var(--sim-model-footer-bgc);
    background: var(--alpha-bg-color);
  }
}
</style>
