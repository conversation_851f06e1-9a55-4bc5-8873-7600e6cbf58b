import {
  defaultConfig,
  getCurrentTimeDayJs,
  getShouldAnimate,
  getViewerName,
  isDefine,
  onViewerDestroyed,
  setCurrentTime,
  setShouldAnimate,
  setSpeed,
  setStartTime,
  setStopTimeInfinity
} from 'isim-cesium';
import dayjs from 'dayjs';

const { synchronizeConfig } = defaultConfig;

export const enum RunStepFn {
  firstStep = 'firstStep',
  secondStep = 'secondStep',
  thirdStep = 'thirdStep'
}

type EngineClockState = {
  actualSpeed: number;
  eventsContent: string;
  expectedSpeed: number;
  frameRatio: number;
  logicTime: number;
  physicsTime: number;
  pushResultType: number;
  stateCode: string;
  status: number;
};

const firstStepWeakMap: Map<string, Set<Function>> = new Map();

export function onFirstStep(fn: Function, viewerName = getViewerName()) {
  nextTick().then(() => {
    if (!firstStepWeakMap.has(viewerName)) {
      firstStepWeakMap.set(viewerName, new Set());
    }
    firstStepWeakMap.get(viewerName)?.add(fn);
  });
}

export function executeFirstStepFn(viewerName = getViewerName()) {
  firstStepWeakMap.get(viewerName)?.forEach((item) => item?.());
  firstStepWeakMap.delete(viewerName);
}

export function synchronizeTime<T extends EngineClockState>(viewerName = getViewerName()) {
  let currentStep: RunStepFn = RunStepFn.firstStep;
  const cacheArray: T[] = [];
  const minSpeed = synchronizeConfig.minSpeed;
  const maxTime = synchronizeConfig.maxTime;
  let animateTimeInterval: number;

  const getActualSpeed = (data: T) => {
    return data.actualSpeed;
  };

  const getSpeed = (): number => {
    if (cacheArray.length === 0) {
      return minSpeed;
    }
    let speed = 1;
    let timeData: T;
    if (cacheArray.length === 1) {
      timeData = cacheArray.slice()[0];
      const _speed = getActualSpeed(timeData);
      if (isDefine(_speed)) {
        return speed;
      }
      return _speed / 2;
    }
    timeData = cacheArray.shift()!;
    speed = getActualSpeed(timeData);
    const diff = getCurrentTimeDayJs().valueOf() - dayjs(timeData.logicTime).valueOf();

    const diffRange = Math.abs(diff / 1000);
    if (diff > maxTime * 1000) {
      setCurrentTime(timeData.logicTime, viewerName);
      speed = minSpeed;
      return speed;
    }
    if (diff < -maxTime * 1000) {
      speed += diffRange;
      return speed;
    }
    if (speed <= 0) {
      speed = minSpeed;
      return speed;
    }
    return speed;
  };

  const setCacheFn = (data: T) => {
    cacheArray.push(data);
  };

  /**
   * 建立动态设置速率的定时器
   */
  const createInterval = () => {
    animateTimeInterval = window.setInterval(() => {
      setSpeed(getSpeed(), viewerName);
    }, synchronizeConfig.interval);
  };

  /**
   * 执行步骤
   */
  const runStepMethod = {
    // 第一步 设置cesium的开始时间 当前时间 和 结束时间
    [RunStepFn.firstStep]: (data: T) => {
      setStartTime(data.logicTime, viewerName);
      setCurrentTime(data.logicTime, viewerName);
      setStopTimeInfinity(viewerName);
      //setStopTime(synchronizeConfig.defaultStopTime, viewerName)
      setCacheFn(data);
      executeFirstStepFn(viewerName);
      currentStep = RunStepFn.secondStep;
    },
    // 第二部 达到缓存数量后 启动定时器
    [RunStepFn.secondStep]: (data: T) => {
      setCacheFn(data);
      if (cacheArray.length > synchronizeConfig.cacheSize) {
        const timeData = cacheArray.splice(0, 1)[0];
        setSpeed(getActualSpeed(timeData), viewerName);
        setShouldAnimate(true);
        clearInterval(animateTimeInterval);
        createInterval();
        currentStep = RunStepFn.thirdStep;
      }
    },
    // 第三步 持续将缓存中添加数据
    [RunStepFn.thirdStep]: (data: T) => {
      setCacheFn(data);
      // 如果动画不动 但是引擎还在走 保证缓存区的数量被控制在设置的缓存数量控制范围之内
      if (!getShouldAnimate() && cacheArray.length > synchronizeConfig.cacheSize) {
        cacheArray.shift();
      }
    }
  };

  const closeIntervalAsync = () => {
    clearInterval(animateTimeInterval);
  };

  const clearCache = () => {
    cacheArray.splice(0, cacheArray.length - 2);
  };
  onViewerDestroyed(closeIntervalAsync, { viewerName });
  return {
    run: (data: T) => {
      runStepMethod[currentStep](data);
    },
    clearCache
  };
}
