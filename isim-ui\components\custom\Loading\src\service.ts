/**
 * @Author: 张乐
 * @Date: 2023/5/30 14:34
 * @Version: 1.0
 * @Content:
 */
import { nextTick } from 'vue';
import type { CSSProperties } from 'vue';
// @ts-ignore
import { isString } from '@/utils/data-type.ts';
import { isClient } from '@vueuse/core';
// @ts-ignore
import { addClass, getStyle, removeClass } from 'element-plus/es/utils/index.mjs';
// @ts-ignore
import { useNamespace, useZIndex } from 'element-plus/es/hooks/index.mjs';
import { createLoadingComponent } from './index';
import type { LoadingInstance } from './index';
// @ts-ignore
import type { LoadingOptionsResolved, LoadingOptions } from './types';

let fullscreenInstance: LoadingInstance | undefined = undefined;
export const Loading = function (options: LoadingOptions = {}): LoadingInstance {
  if (!isClient) {
    return undefined as any;
  }
  const resolved = resolveOptions(options);
  if (resolved.fullscreen && fullscreenInstance) {
    return fullscreenInstance;
  }
  const instance = createLoadingComponent({
    ...resolved,
    closed: () => {
      resolved.closed?.();
      if (resolved.fullscreen) {
        fullscreenInstance = undefined;
      }
    }
  });
  addStyle(resolved, resolved.parent, instance);
  addClassList(resolved, resolved.parent, instance);
  resolved.parent.vLoadingAddClassList = () => addClassList(resolved, resolved.parent, instance);

  let loadingNumber: string | null = resolved.parent.getAttribute('loading-number');
  if (!loadingNumber) {
    loadingNumber = '1';
  } else {
    loadingNumber = `${Number.parseInt(loadingNumber) + 1}`;
  }
  resolved.parent.setAttribute('loading-number', loadingNumber);
  resolved.parent.appendChild(instance.$el);
  // @ts-ignore
  nextTick(() => (instance.visible.value = resolved.visible));
  if (resolved.fullscreen) {
    fullscreenInstance = instance;
  }
  return instance;
};

const resolveOptions = (options: LoadingOptions): LoadingOptionsResolved => {
  let target: HTMLElement;
  if (isString(options.target)) {
    target = document.querySelector<HTMLElement>(options.target) ?? document.body;
  } else {
    target = options.target || document.body;
  }
  return {
    parent: target === document.body || options.body ? document.body : target,
    background: options.background || '',
    svg: options.svg || '',
    svgViewBox: options.svgViewBox || '',
    spinner: options.spinner || false,
    text: options.text || '',
    fullscreen: target === document.body && (options.fullscreen ?? true),
    lock: options.lock ?? false,
    customClass: options.customClass || '',
    visible: options.visible ?? true,
    target
  };
};

const addStyle = async (options: LoadingOptionsResolved, parent: HTMLElement, instance: LoadingInstance) => {
  const { nextZIndex } = useZIndex();
  const maskStyle: CSSProperties = {};
  if (options.fullscreen) {
    // @ts-ignore
    instance.originalPosition.value = getStyle(document.body, 'position');
    // @ts-ignore
    instance.originalOverflow.value = getStyle(document.body, 'overflow');
    maskStyle.zIndex = nextZIndex();
  } else if (options.parent === document.body) {
    // @ts-ignore
    instance.originalPosition.value = getStyle(document.body, 'position');
    await nextTick();
    for (const property of ['top', 'left']) {
      const scroll = property === 'top' ? 'scrollTop' : 'scrollLeft';

      // @ts-ignore
      maskStyle[property] = `${
        // @ts-ignore
        (options.target as HTMLElement).getBoundingClientRect()[property] +
        document.body[scroll] +
        document.documentElement[scroll] -
        //@ts-ignore
        Number.parseInt(getStyle(document.body, `margin-${property}`), 10)
      }px`;
    }
    for (const property of ['height', 'width']) {
      // @ts-ignore
      maskStyle[property] = `${(options.target as HTMLElement).getBoundingClientRect()[property]}px`;
    }
  } else {
    // @ts-ignore
    instance.originalPosition.value = getStyle(parent, 'position');
  }
  for (const [key, value] of Object.entries(maskStyle)) {
    // @ts-ignore
    instance.$el.style[key] = value;
  }
};

const addClassList = (options: LoadingOptions, parent: HTMLElement, instance: LoadingInstance) => {
  const ns = useNamespace('loading');

  // @ts-ignore
  if (!['absolute', 'fixed', 'sticky'].includes(instance.originalPosition.value)) {
    addClass(parent, ns.bm('parent', 'relative'));
  } else {
    removeClass(parent, ns.bm('parent', 'relative'));
  }
  if (options.fullscreen && options.lock) {
    addClass(parent, ns.bm('parent', 'hidden'));
  } else {
    removeClass(parent, ns.bm('parent', 'hidden'));
  }
};
