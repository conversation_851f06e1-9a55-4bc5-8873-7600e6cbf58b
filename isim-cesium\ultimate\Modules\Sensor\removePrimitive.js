/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/03/19 15:07:12
 * @description 添加 Visualizer 删除Primitive的方法
 * */

const { defined } = Cesium;

function removePrimitive(entity, hash, primitives) {
  const data = hash[entity.id];
  if (defined(data)) {
    const primitive = data.primitive;
    primitives.remove(primitive);
    if (!primitive.isDestroyed()) {
      primitive.destroy();
    }
    delete hash[entity.id];
  }
}

export default removePrimitive;
