import { getViewerField, nextTick, ViewerNameType } from 'isim-cesium';
import { Camera, Rectangle } from 'cesium';

export function flyToRectangle(pos: { west: number; south: number; east: number; north: number }, viewerName?: ViewerNameType) {
  const { west, south, east, north } = pos;
  nextTick().then(() => {
    getCamera(viewerName)?.flyTo({
      destination: Rectangle.fromDegrees(west, south, east, north)
    });
  });
}

export function cameraFlyTo(options: Parameters<Camera['flyTo']>[0], viewerName?: ViewerNameType) {
  getCamera(viewerName).flyTo(options);
}

/**
 * 获取相机
 */
export const getCamera = getViewerField('camera');

export function getCameraField<T extends keyof Camera>(field: T) {
  return function (viewerName?: ViewerNameType) {
    return getCamera(viewerName)[field];
  };
}

/**
 * 设置相机的默认视角
 * @param params
 */
export function setDefaultCamera(params: { west: number; south: number; east: number; north: number }) {
  const { west, south, east, north } = params;
  Camera.DEFAULT_VIEW_RECTANGLE = Rectangle.fromDegrees(west, south, east, north);
}

//export function executeCameraMethod<T extends keyof Camera>(field: T) {
//  return function (...args: Array<Camera[T] & [viewerName: ViewerNameType]>){
//    const viewerName = args.at(-1)
//    getCamera(viewerName)
//  }
//}
