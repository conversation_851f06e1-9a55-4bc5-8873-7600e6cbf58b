{"version": 3, "file": "when-4bbc8319.js", "sources": ["../../../../Source/Core/defaultValue.js", "../../../../Source/ThirdParty/_commonjsHelpers-3aae1032.js", "../../../../Source/ThirdParty/when.js", "../../../../Source/Core/defined.js"], "sourcesContent": ["/**\n * Returns the first parameter if not undefined, otherwise the second parameter.\n * Useful for setting a default value for a parameter.\n *\n * @function\n *\n * @param {*} a\n * @param {*} b\n * @returns {*} Returns the first parameter if not undefined, otherwise the second parameter.\n *\n * @example\n * param = Cesium.defaultValue(param, 'default');\n */\nfunction defaultValue(a, b) {\n  if (a !== undefined && a !== null) {\n    return a;\n  }\n  return b;\n}\n\n/**\n * A frozen empty object that can be used as the default value for options passed as\n * an object literal.\n * @type {Object}\n * @memberof defaultValue\n */\ndefaultValue.EMPTY_OBJECT = Object.freeze({});\n\nexport default defaultValue;\n", "/* This file is automatically rebuilt by the Cesium build process. */\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction createCommonjsModule(fn, basedir, module) {\n\treturn module = {\n\t\tpath: basedir,\n\t\texports: {},\n\t\trequire: function (path, base) {\n\t\t\treturn commonjsRequire(path, (base === undefined || base === null) ? module.path : base);\n\t\t}\n\t}, fn(module, module.exports), module.exports;\n}\n\nfunction commonjsRequire () {\n\tthrow new Error('Dynamic requires are not currently supported by @rollup/plugin-commonjs');\n}\n\nexport { commonjsGlobal as a, createCommonjsModule as c };\n", "/* This file is automatically rebuilt by the Cesium build process. */\nimport { c as createCommonjsModule } from './_commonjsHelpers-3aae1032.js';\n\nvar when = createCommonjsModule(function (module, exports) {\n/** @license MIT License (c) copyright B Cavalier & J <PERSON> */\n\n/**\n * A lightweight CommonJS Promises/A and when() implementation\n * when is part of the cujo.js family of libraries (http://cujojs.com/)\n *\n * Licensed under the MIT License at:\n * http://www.opensource.org/licenses/mit-license.php\n *\n * @version 1.7.1\n */\n\n(function(define) {define(function () {\n\tvar reduceArray, slice, undef;\n\n\t//\n\t// Public API\n\t//\n\n\twhen.defer     = defer;     // Create a deferred\n\twhen.resolve   = resolve;   // Create a resolved promise\n\twhen.reject    = reject;    // Create a rejected promise\n\n\twhen.join      = join;      // Join 2 or more promises\n\n\twhen.all       = all;       // Resolve a list of promises\n\twhen.map       = map;       // Array.map() for promises\n\twhen.reduce    = reduce;    // Array.reduce() for promises\n\n\twhen.any       = any;       // One-winner race\n\twhen.some      = some;      // Multi-winner race\n\n\twhen.chain     = chain;     // Make a promise trigger another resolver\n\n\twhen.isPromise = isPromise; // Determine if a thing is a promise\n\n\t/**\n\t * Register an observer for a promise or immediate value.\n\t *\n\t * @param {*} promiseOrValue\n\t * @param {function?} [onFulfilled] callback to be called when promiseOrValue is\n\t *   successfully fulfilled.  If promiseOrValue is an immediate value, callback\n\t *   will be invoked immediately.\n\t * @param {function?} [onRejected] callback to be called when promiseOrValue is\n\t *   rejected.\n\t * @param {function?} [onProgress] callback to be called when progress updates\n\t *   are issued for promiseOrValue.\n\t * @returns {Promise} a new {@link Promise} that will complete with the return\n\t *   value of callback or errback or the completion value of promiseOrValue if\n\t *   callback and/or errback is not supplied.\n\t */\n\tfunction when(promiseOrValue, onFulfilled, onRejected, onProgress) {\n\t\t// Get a trusted promise for the input promiseOrValue, and then\n\t\t// register promise handlers\n\t\treturn resolve(promiseOrValue).then(onFulfilled, onRejected, onProgress);\n\t}\n\n\t/**\n\t * Returns promiseOrValue if promiseOrValue is a {@link Promise}, a new Promise if\n\t * promiseOrValue is a foreign promise, or a new, already-fulfilled {@link Promise}\n\t * whose value is promiseOrValue if promiseOrValue is an immediate value.\n\t *\n\t * @param {*} promiseOrValue\n\t * @returns Guaranteed to return a trusted Promise.  If promiseOrValue is a when.js {@link Promise}\n\t *   returns promiseOrValue, otherwise, returns a new, already-resolved, when.js {@link Promise}\n\t *   whose resolution value is:\n\t *   * the resolution value of promiseOrValue if it's a foreign promise, or\n\t *   * promiseOrValue if it's a value\n\t */\n\tfunction resolve(promiseOrValue) {\n\t\tvar promise, deferred;\n\n\t\tif(promiseOrValue instanceof Promise) {\n\t\t\t// It's a when.js promise, so we trust it\n\t\t\tpromise = promiseOrValue;\n\n\t\t} else {\n\t\t\t// It's not a when.js promise. See if it's a foreign promise or a value.\n\t\t\tif(isPromise(promiseOrValue)) {\n\t\t\t\t// It's a thenable, but we don't know where it came from, so don't trust\n\t\t\t\t// its implementation entirely.  Introduce a trusted middleman when.js promise\n\t\t\t\tdeferred = defer();\n\n\t\t\t\t// IMPORTANT: This is the only place when.js should ever call .then() on an\n\t\t\t\t// untrusted promise. Don't expose the return value to the untrusted promise\n\t\t\t\tpromiseOrValue.then(\n\t\t\t\t\tfunction(value)  { deferred.resolve(value); },\n\t\t\t\t\tfunction(reason) { deferred.reject(reason); },\n\t\t\t\t\tfunction(update) { deferred.progress(update); }\n\t\t\t\t);\n\n\t\t\t\tpromise = deferred.promise;\n\n\t\t\t} else {\n\t\t\t\t// It's a value, not a promise.  Create a resolved promise for it.\n\t\t\t\tpromise = fulfilled(promiseOrValue);\n\t\t\t}\n\t\t}\n\n\t\treturn promise;\n\t}\n\n\t/**\n\t * Returns a rejected promise for the supplied promiseOrValue.  The returned\n\t * promise will be rejected with:\n\t * - promiseOrValue, if it is a value, or\n\t * - if promiseOrValue is a promise\n\t *   - promiseOrValue's value after it is fulfilled\n\t *   - promiseOrValue's reason after it is rejected\n\t * @param {*} promiseOrValue the rejected value of the returned {@link Promise}\n\t * @return {Promise} rejected {@link Promise}\n\t */\n\tfunction reject(promiseOrValue) {\n\t\treturn when(promiseOrValue, rejected);\n\t}\n\n\t/**\n\t * Trusted Promise constructor.  A Promise created from this constructor is\n\t * a trusted when.js promise.  Any other duck-typed promise is considered\n\t * untrusted.\n\t * @constructor\n\t * @name Promise\n\t */\n\tfunction Promise(then) {\n\t\tthis.then = then;\n\t}\n\n\tPromise.prototype = {\n\t\t/**\n\t\t * Register a callback that will be called when a promise is\n\t\t * fulfilled or rejected.  Optionally also register a progress handler.\n\t\t * Shortcut for .then(onFulfilledOrRejected, onFulfilledOrRejected, onProgress)\n\t\t * @param {function?} [onFulfilledOrRejected]\n\t\t * @param {function?} [onProgress]\n\t\t * @return {Promise}\n\t\t */\n\t\talways: function(onFulfilledOrRejected, onProgress) {\n\t\t\treturn this.then(onFulfilledOrRejected, onFulfilledOrRejected, onProgress);\n\t\t},\n\n\t\t/**\n\t\t * Register a rejection handler.  Shortcut for .then(undefined, onRejected)\n\t\t * @param {function?} onRejected\n\t\t * @return {Promise}\n\t\t */\n\t\totherwise: function(onRejected) {\n\t\t\treturn this.then(undef, onRejected);\n\t\t},\n\n\t\t/**\n\t\t * Shortcut for .then(function() { return value; })\n\t\t * @param  {*} value\n\t\t * @return {Promise} a promise that:\n\t\t *  - is fulfilled if value is not a promise, or\n\t\t *  - if value is a promise, will fulfill with its value, or reject\n\t\t *    with its reason.\n\t\t */\n\t\tyield: function(value) {\n\t\t\treturn this.then(function() {\n\t\t\t\treturn value;\n\t\t\t});\n\t\t},\n\n\t\t/**\n\t\t * Assumes that this promise will fulfill with an array, and arranges\n\t\t * for the onFulfilled to be called with the array as its argument list\n\t\t * i.e. onFulfilled.spread(undefined, array).\n\t\t * @param {function} onFulfilled function to receive spread arguments\n\t\t * @return {Promise}\n\t\t */\n\t\tspread: function(onFulfilled) {\n\t\t\treturn this.then(function(array) {\n\t\t\t\t// array may contain promises, so resolve its contents.\n\t\t\t\treturn all(array, function(array) {\n\t\t\t\t\treturn onFulfilled.apply(undef, array);\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t};\n\n\t/**\n\t * Create an already-resolved promise for the supplied value\n\t * @private\n\t *\n\t * @param {*} value\n\t * @return {Promise} fulfilled promise\n\t */\n\tfunction fulfilled(value) {\n\t\tvar p = new Promise(function(onFulfilled) {\n\t\t\t// TODO: Promises/A+ check typeof onFulfilled\n\t\t\ttry {\n\t\t\t\treturn resolve(onFulfilled ? onFulfilled(value) : value);\n\t\t\t} catch(e) {\n\t\t\t\treturn rejected(e);\n\t\t\t}\n\t\t});\n\n\t\treturn p;\n\t}\n\n\t/**\n\t * Create an already-rejected {@link Promise} with the supplied\n\t * rejection reason.\n\t * @private\n\t *\n\t * @param {*} reason\n\t * @return {Promise} rejected promise\n\t */\n\tfunction rejected(reason) {\n\t\tvar p = new Promise(function(_, onRejected) {\n\t\t\t// TODO: Promises/A+ check typeof onRejected\n\t\t\ttry {\n\t\t\t\treturn onRejected ? resolve(onRejected(reason)) : rejected(reason);\n\t\t\t} catch(e) {\n\t\t\t\treturn rejected(e);\n\t\t\t}\n\t\t});\n\n\t\treturn p;\n\t}\n\n\t/**\n\t * Creates a new, Deferred with fully isolated resolver and promise parts,\n\t * either or both of which may be given out safely to consumers.\n\t * The Deferred itself has the full API: resolve, reject, progress, and\n\t * then. The resolver has resolve, reject, and progress.  The promise\n\t * only has then.\n\t *\n\t * @return {Deferred}\n\t */\n\tfunction defer() {\n\t\tvar deferred, promise, handlers, progressHandlers,\n\t\t\t_then, _progress, _resolve;\n\n\t\t/**\n\t\t * The promise for the new deferred\n\t\t * @type {Promise}\n\t\t */\n\t\tpromise = new Promise(then);\n\n\t\t/**\n\t\t * The full Deferred object, with {@link Promise} and {@link Resolver} parts\n\t\t * @class Deferred\n\t\t * @name Deferred\n\t\t */\n\t\tdeferred = {\n\t\t\tthen:     then, // DEPRECATED: use deferred.promise.then\n\t\t\tresolve:  promiseResolve,\n\t\t\treject:   promiseReject,\n\t\t\t// TODO: Consider renaming progress() to notify()\n\t\t\tprogress: promiseProgress,\n\n\t\t\tpromise:  promise,\n\n\t\t\tresolver: {\n\t\t\t\tresolve:  promiseResolve,\n\t\t\t\treject:   promiseReject,\n\t\t\t\tprogress: promiseProgress\n\t\t\t}\n\t\t};\n\n\t\thandlers = [];\n\t\tprogressHandlers = [];\n\n\t\t/**\n\t\t * Pre-resolution then() that adds the supplied callback, errback, and progback\n\t\t * functions to the registered listeners\n\t\t * @private\n\t\t *\n\t\t * @param {function?} [onFulfilled] resolution handler\n\t\t * @param {function?} [onRejected] rejection handler\n\t\t * @param {function?} [onProgress] progress handler\n\t\t */\n\t\t_then = function(onFulfilled, onRejected, onProgress) {\n\t\t\t// TODO: Promises/A+ check typeof onFulfilled, onRejected, onProgress\n\t\t\tvar deferred, progressHandler;\n\n\t\t\tdeferred = defer();\n\n\t\t\tprogressHandler = typeof onProgress === 'function'\n\t\t\t\t? function(update) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// Allow progress handler to transform progress event\n\t\t\t\t\t\tdeferred.progress(onProgress(update));\n\t\t\t\t\t} catch(e) {\n\t\t\t\t\t\t// Use caught value as progress\n\t\t\t\t\t\tdeferred.progress(e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t: function(update) { deferred.progress(update); };\n\n\t\t\thandlers.push(function(promise) {\n\t\t\t\tpromise.then(onFulfilled, onRejected)\n\t\t\t\t\t.then(deferred.resolve, deferred.reject, progressHandler);\n\t\t\t});\n\n\t\t\tprogressHandlers.push(progressHandler);\n\n\t\t\treturn deferred.promise;\n\t\t};\n\n\t\t/**\n\t\t * Issue a progress event, notifying all progress listeners\n\t\t * @private\n\t\t * @param {*} update progress event payload to pass to all listeners\n\t\t */\n\t\t_progress = function(update) {\n\t\t\tprocessQueue(progressHandlers, update);\n\t\t\treturn update;\n\t\t};\n\n\t\t/**\n\t\t * Transition from pre-resolution state to post-resolution state, notifying\n\t\t * all listeners of the resolution or rejection\n\t\t * @private\n\t\t * @param {*} value the value of this deferred\n\t\t */\n\t\t_resolve = function(value) {\n\t\t\tvalue = resolve(value);\n\n\t\t\t// Replace _then with one that directly notifies with the result.\n\t\t\t_then = value.then;\n\t\t\t// Replace _resolve so that this Deferred can only be resolved once\n\t\t\t_resolve = resolve;\n\t\t\t// Make _progress a noop, to disallow progress for the resolved promise.\n\t\t\t_progress = noop;\n\n\t\t\t// Notify handlers\n\t\t\tprocessQueue(handlers, value);\n\n\t\t\t// Free progressHandlers array since we'll never issue progress events\n\t\t\tprogressHandlers = handlers = undef;\n\n\t\t\treturn value;\n\t\t};\n\n\t\treturn deferred;\n\n\t\t/**\n\t\t * Wrapper to allow _then to be replaced safely\n\t\t * @param {function?} [onFulfilled] resolution handler\n\t\t * @param {function?} [onRejected] rejection handler\n\t\t * @param {function?} [onProgress] progress handler\n\t\t * @return {Promise} new promise\n\t\t */\n\t\tfunction then(onFulfilled, onRejected, onProgress) {\n\t\t\t// TODO: Promises/A+ check typeof onFulfilled, onRejected, onProgress\n\t\t\treturn _then(onFulfilled, onRejected, onProgress);\n\t\t}\n\n\t\t/**\n\t\t * Wrapper to allow _resolve to be replaced\n\t\t */\n\t\tfunction promiseResolve(val) {\n\t\t\treturn _resolve(val);\n\t\t}\n\n\t\t/**\n\t\t * Wrapper to allow _reject to be replaced\n\t\t */\n\t\tfunction promiseReject(err) {\n\t\t\treturn _resolve(rejected(err));\n\t\t}\n\n\t\t/**\n\t\t * Wrapper to allow _progress to be replaced\n\t\t */\n\t\tfunction promiseProgress(update) {\n\t\t\treturn _progress(update);\n\t\t}\n\t}\n\n\t/**\n\t * Determines if promiseOrValue is a promise or not.  Uses the feature\n\t * test from http://wiki.commonjs.org/wiki/Promises/A to determine if\n\t * promiseOrValue is a promise.\n\t *\n\t * @param {*} promiseOrValue anything\n\t * @returns {boolean} true if promiseOrValue is a {@link Promise}\n\t */\n\tfunction isPromise(promiseOrValue) {\n\t\treturn promiseOrValue && typeof promiseOrValue.then === 'function';\n\t}\n\n\t/**\n\t * Initiates a competitive race, returning a promise that will resolve when\n\t * howMany of the supplied promisesOrValues have resolved, or will reject when\n\t * it becomes impossible for howMany to resolve, for example, when\n\t * (promisesOrValues.length - howMany) + 1 input promises reject.\n\t *\n\t * @param {Array} promisesOrValues array of anything, may contain a mix\n\t *      of promises and values\n\t * @param howMany {number} number of promisesOrValues to resolve\n\t * @param {function?} [onFulfilled] resolution handler\n\t * @param {function?} [onRejected] rejection handler\n\t * @param {function?} [onProgress] progress handler\n\t * @returns {Promise} promise that will resolve to an array of howMany values that\n\t * resolved first, or will reject with an array of (promisesOrValues.length - howMany) + 1\n\t * rejection reasons.\n\t */\n\tfunction some(promisesOrValues, howMany, onFulfilled, onRejected, onProgress) {\n\n\t\tcheckCallbacks(2, arguments);\n\n\t\treturn when(promisesOrValues, function(promisesOrValues) {\n\n\t\t\tvar toResolve, toReject, values, reasons, deferred, fulfillOne, rejectOne, progress, len, i;\n\n\t\t\tlen = promisesOrValues.length >>> 0;\n\n\t\t\ttoResolve = Math.max(0, Math.min(howMany, len));\n\t\t\tvalues = [];\n\n\t\t\ttoReject = (len - toResolve) + 1;\n\t\t\treasons = [];\n\n\t\t\tdeferred = defer();\n\n\t\t\t// No items in the input, resolve immediately\n\t\t\tif (!toResolve) {\n\t\t\t\tdeferred.resolve(values);\n\n\t\t\t} else {\n\t\t\t\tprogress = deferred.progress;\n\n\t\t\t\trejectOne = function(reason) {\n\t\t\t\t\treasons.push(reason);\n\t\t\t\t\tif(!--toReject) {\n\t\t\t\t\t\tfulfillOne = rejectOne = noop;\n\t\t\t\t\t\tdeferred.reject(reasons);\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tfulfillOne = function(val) {\n\t\t\t\t\t// This orders the values based on promise resolution order\n\t\t\t\t\t// Another strategy would be to use the original position of\n\t\t\t\t\t// the corresponding promise.\n\t\t\t\t\tvalues.push(val);\n\n\t\t\t\t\tif (!--toResolve) {\n\t\t\t\t\t\tfulfillOne = rejectOne = noop;\n\t\t\t\t\t\tdeferred.resolve(values);\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tfor(i = 0; i < len; ++i) {\n\t\t\t\t\tif(i in promisesOrValues) {\n\t\t\t\t\t\twhen(promisesOrValues[i], fulfiller, rejecter, progress);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn deferred.then(onFulfilled, onRejected, onProgress);\n\n\t\t\tfunction rejecter(reason) {\n\t\t\t\trejectOne(reason);\n\t\t\t}\n\n\t\t\tfunction fulfiller(val) {\n\t\t\t\tfulfillOne(val);\n\t\t\t}\n\n\t\t});\n\t}\n\n\t/**\n\t * Initiates a competitive race, returning a promise that will resolve when\n\t * any one of the supplied promisesOrValues has resolved or will reject when\n\t * *all* promisesOrValues have rejected.\n\t *\n\t * @param {Array|Promise} promisesOrValues array of anything, may contain a mix\n\t *      of {@link Promise}s and values\n\t * @param {function?} [onFulfilled] resolution handler\n\t * @param {function?} [onRejected] rejection handler\n\t * @param {function?} [onProgress] progress handler\n\t * @returns {Promise} promise that will resolve to the value that resolved first, or\n\t * will reject with an array of all rejected inputs.\n\t */\n\tfunction any(promisesOrValues, onFulfilled, onRejected, onProgress) {\n\n\t\tfunction unwrapSingleResult(val) {\n\t\t\treturn onFulfilled ? onFulfilled(val[0]) : val[0];\n\t\t}\n\n\t\treturn some(promisesOrValues, 1, unwrapSingleResult, onRejected, onProgress);\n\t}\n\n\t/**\n\t * Return a promise that will resolve only once all the supplied promisesOrValues\n\t * have resolved. The resolution value of the returned promise will be an array\n\t * containing the resolution values of each of the promisesOrValues.\n\t * @memberOf when\n\t *\n\t * @param {Array|Promise} promisesOrValues array of anything, may contain a mix\n\t *      of {@link Promise}s and values\n\t * @param {function?} [onFulfilled] resolution handler\n\t * @param {function?} [onRejected] rejection handler\n\t * @param {function?} [onProgress] progress handler\n\t * @returns {Promise}\n\t */\n\tfunction all(promisesOrValues, onFulfilled, onRejected, onProgress) {\n\t\tcheckCallbacks(1, arguments);\n\t\treturn map(promisesOrValues, identity).then(onFulfilled, onRejected, onProgress);\n\t}\n\n\t/**\n\t * Joins multiple promises into a single returned promise.\n\t * @return {Promise} a promise that will fulfill when *all* the input promises\n\t * have fulfilled, or will reject when *any one* of the input promises rejects.\n\t */\n\tfunction join(/* ...promises */) {\n\t\treturn map(arguments, identity);\n\t}\n\n\t/**\n\t * Traditional map function, similar to `Array.prototype.map()`, but allows\n\t * input to contain {@link Promise}s and/or values, and mapFunc may return\n\t * either a value or a {@link Promise}\n\t *\n\t * @param {Array|Promise} promise array of anything, may contain a mix\n\t *      of {@link Promise}s and values\n\t * @param {function} mapFunc mapping function mapFunc(value) which may return\n\t *      either a {@link Promise} or value\n\t * @returns {Promise} a {@link Promise} that will resolve to an array containing\n\t *      the mapped output values.\n\t */\n\tfunction map(promise, mapFunc) {\n\t\treturn when(promise, function(array) {\n\t\t\tvar results, len, toResolve, resolve, i, d;\n\n\t\t\t// Since we know the resulting length, we can preallocate the results\n\t\t\t// array to avoid array expansions.\n\t\t\ttoResolve = len = array.length >>> 0;\n\t\t\tresults = [];\n\t\t\td = defer();\n\n\t\t\tif(!toResolve) {\n\t\t\t\td.resolve(results);\n\t\t\t} else {\n\n\t\t\t\tresolve = function resolveOne(item, i) {\n\t\t\t\t\twhen(item, mapFunc).then(function(mapped) {\n\t\t\t\t\t\tresults[i] = mapped;\n\n\t\t\t\t\t\tif(!--toResolve) {\n\t\t\t\t\t\t\td.resolve(results);\n\t\t\t\t\t\t}\n\t\t\t\t\t}, d.reject);\n\t\t\t\t};\n\n\t\t\t\t// Since mapFunc may be async, get all invocations of it into flight\n\t\t\t\tfor(i = 0; i < len; i++) {\n\t\t\t\t\tif(i in array) {\n\t\t\t\t\t\tresolve(array[i], i);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t--toResolve;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn d.promise;\n\n\t\t});\n\t}\n\n\t/**\n\t * Traditional reduce function, similar to `Array.prototype.reduce()`, but\n\t * input may contain promises and/or values, and reduceFunc\n\t * may return either a value or a promise, *and* initialValue may\n\t * be a promise for the starting value.\n\t *\n\t * @param {Array|Promise} promise array or promise for an array of anything,\n\t *      may contain a mix of promises and values.\n\t * @param {function} reduceFunc reduce function reduce(currentValue, nextValue, index, total),\n\t *      where total is the total number of items being reduced, and will be the same\n\t *      in each call to reduceFunc.\n\t * @returns {Promise} that will resolve to the final reduced value\n\t */\n\tfunction reduce(promise, reduceFunc /*, initialValue */) {\n\t\tvar args = slice.call(arguments, 1);\n\n\t\treturn when(promise, function(array) {\n\t\t\tvar total;\n\n\t\t\ttotal = array.length;\n\n\t\t\t// Wrap the supplied reduceFunc with one that handles promises and then\n\t\t\t// delegates to the supplied.\n\t\t\targs[0] = function (current, val, i) {\n\t\t\t\treturn when(current, function (c) {\n\t\t\t\t\treturn when(val, function (value) {\n\t\t\t\t\t\treturn reduceFunc(c, value, i, total);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t};\n\n\t\t\treturn reduceArray.apply(array, args);\n\t\t});\n\t}\n\n\t/**\n\t * Ensure that resolution of promiseOrValue will trigger resolver with the\n\t * value or reason of promiseOrValue, or instead with resolveValue if it is provided.\n\t *\n\t * @param promiseOrValue\n\t * @param {Object} resolver\n\t * @param {function} resolver.resolve\n\t * @param {function} resolver.reject\n\t * @param {*} [resolveValue]\n\t * @returns {Promise}\n\t */\n\tfunction chain(promiseOrValue, resolver, resolveValue) {\n\t\tvar useResolveValue = arguments.length > 2;\n\n\t\treturn when(promiseOrValue,\n\t\t\tfunction(val) {\n\t\t\t\tval = useResolveValue ? resolveValue : val;\n\t\t\t\tresolver.resolve(val);\n\t\t\t\treturn val;\n\t\t\t},\n\t\t\tfunction(reason) {\n\t\t\t\tresolver.reject(reason);\n\t\t\t\treturn rejected(reason);\n\t\t\t},\n\t\t\tresolver.progress\n\t\t);\n\t}\n\n\t//\n\t// Utility functions\n\t//\n\n\t/**\n\t * Apply all functions in queue to value\n\t * @param {Array} queue array of functions to execute\n\t * @param {*} value argument passed to each function\n\t */\n\tfunction processQueue(queue, value) {\n\t\tvar handler, i = 0;\n\n\t\twhile (handler = queue[i++]) {\n\t\t\thandler(value);\n\t\t}\n\t}\n\n\t/**\n\t * Helper that checks arrayOfCallbacks to ensure that each element is either\n\t * a function, or null or undefined.\n\t * @private\n\t * @param {number} start index at which to start checking items in arrayOfCallbacks\n\t * @param {Array} arrayOfCallbacks array to check\n\t * @throws {Error} if any element of arrayOfCallbacks is something other than\n\t * a functions, null, or undefined.\n\t */\n\tfunction checkCallbacks(start, arrayOfCallbacks) {\n\t\t// TODO: Promises/A+ update type checking and docs\n\t\tvar arg, i = arrayOfCallbacks.length;\n\n\t\twhile(i > start) {\n\t\t\targ = arrayOfCallbacks[--i];\n\n\t\t\tif (arg != null && typeof arg != 'function') {\n\t\t\t\tthrow new Error('arg '+i+' must be a function');\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * No-Op function used in method replacement\n\t * @private\n\t */\n\tfunction noop() {}\n\n\tslice = [].slice;\n\n\t// ES5 reduce implementation if native not available\n\t// See: http://es5.github.com/#x15.4.4.21 as there are many\n\t// specifics and edge cases.\n\treduceArray = [].reduce ||\n\t\tfunction(reduceFunc /*, initialValue */) {\n\t\t\t/*jshint maxcomplexity: 7*/\n\n\t\t\t// ES5 dictates that reduce.length === 1\n\n\t\t\t// This implementation deviates from ES5 spec in the following ways:\n\t\t\t// 1. It does not check if reduceFunc is a Callable\n\n\t\t\tvar arr, args, reduced, len, i;\n\n\t\t\ti = 0;\n\t\t\t// This generates a jshint warning, despite being valid\n\t\t\t// \"Missing 'new' prefix when invoking a constructor.\"\n\t\t\t// See https://github.com/jshint/jshint/issues/392\n\t\t\tarr = Object(this);\n\t\t\tlen = arr.length >>> 0;\n\t\t\targs = arguments;\n\n\t\t\t// If no initialValue, use first item of array (we know length !== 0 here)\n\t\t\t// and adjust i to start at second item\n\t\t\tif(args.length <= 1) {\n\t\t\t\t// Skip to the first real element in the array\n\t\t\t\tfor(;;) {\n\t\t\t\t\tif(i in arr) {\n\t\t\t\t\t\treduced = arr[i++];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\t// If we reached the end of the array without finding any real\n\t\t\t\t\t// elements, it's a TypeError\n\t\t\t\t\tif(++i >= len) {\n\t\t\t\t\t\tthrow new TypeError();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// If initialValue provided, use it\n\t\t\t\treduced = args[1];\n\t\t\t}\n\n\t\t\t// Do the actual reduce\n\t\t\tfor(;i < len; ++i) {\n\t\t\t\t// Skip holes\n\t\t\t\tif(i in arr) {\n\t\t\t\t\treduced = reduceFunc(reduced, arr[i], i, arr);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn reduced;\n\t\t};\n\n\tfunction identity(x) {\n\t\treturn x;\n\t}\n\n\treturn when;\n});\n})(function (factory) { (module.exports = factory())\n\t\t;\n\t}\n\t// Boilerplate for AMD, Node, and browser global\n);\n});\n\nexport { when as default };\n", "/**\n * @function\n *\n * @param {*} value The object.\n * @returns {Boolean} Returns true if the object is defined, returns false otherwise.\n *\n * @example\n * if (Cesium.defined(positions)) {\n *      doSomething();\n * } else {\n *      doSomethingElse();\n * }\n */\nfunction defined(value) {\n  return value !== undefined && value !== null;\n}\nexport default defined;\n"], "names": ["defaultValue", "a", "b", "EMPTY_OBJECT", "Object", "freeze", "commonjsGlobal", "globalThis", "window", "global", "self", "createCommonjsModule", "fn", "basedir", "module", "path", "exports", "require", "base", "Error", "commonjsRequire", "when", "factory", "reduceArray", "slice", "undef", "promiseOrValue", "onFulfilled", "onRejected", "onProgress", "resolve", "then", "promise", "deferred", "value", "Promise", "isPromise", "defer", "reason", "reject", "update", "progress", "e", "rejected", "this", "_", "handlers", "progressHandlers", "_then", "_progress", "_resolve", "progressHandler", "push", "processQueue", "noop", "promiseResolve", "promiseReject", "promiseProgress", "resolver", "val", "err", "some", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "how<PERSON><PERSON>", "checkCallbacks", "arguments", "toResolve", "toReject", "values", "reasons", "fulfillOne", "rejectOne", "len", "i", "length", "Math", "max", "min", "fulfiller", "rejecter", "all", "map", "identity", "mapFunc", "array", "results", "d", "item", "mapped", "queue", "handler", "start", "arrayOfCallbacks", "arg", "x", "join", "reduce", "reduceFunc", "args", "call", "total", "current", "c", "apply", "any", "chain", "resolveValue", "useResolveValue", "prototype", "always", "onFulfilledOrRejected", "otherwise", "yield", "spread", "arr", "reduced", "TypeError"], "mappings": "6CAaA,SAASA,EAAaC,EAAGC,GACvB,OAAID,QACKA,EAEFC,CACT,CAQAF,EAAaG,aAAeC,OAAOC,OAAO,ICzBvC,IAACC,EAAuC,oBAAfC,WAA6BA,WAA+B,oBAAXC,OAAyBA,OAA2B,oBAAXC,OAAyBA,OAAyB,oBAATC,KAAuBA,KAAO,CAAG,EAEhM,SAASC,EAAqBC,EAAIC,EAASC,GAC1C,OAMGF,EANIE,EAAS,CACfC,KAAMF,EACNG,QAAS,CAAE,EACXC,QAAS,SAAUF,EAAMG,GACxB,OAKH,WACC,MAAM,IAAIC,MAAM,0EACjB,CAPUC,CAAsB,MAACF,GAAuCJ,EAAOC,KAC5E,GACYD,EAAOE,SAAUF,EAAOE,OACvC,CCRI,IAAAK,EAAOV,GAAqB,SAAUG,EAAQE;;AAalD,IAotBaM,IAptBa,WACzB,IAAIC,EAAaC,EAAOC,EAsCxB,SAASJ,EAAKK,EAAgBC,EAAaC,EAAYC,GAGtD,OAAOC,EAAQJ,GAAgBK,KAAKJ,EAAaC,EAAYC,EAC7D,CAcD,SAASC,EAAQJ,GAChB,IAAIM,EAASC,EAqHKC,EAxFlB,OA3BGR,aAA0BS,EAE5BH,EAAUN,EAIPU,EAAUV,IAGZO,EAAWI,IAIXX,EAAeK,MACd,SAASG,GAAUD,EAASH,QAAQI,EAAS,IAC7C,SAASI,GAAUL,EAASM,OAAOD,EAAU,IAC7C,SAASE,GAAUP,EAASQ,SAASD,EAAU,IAGhDR,EAAUC,EAASD,UAgGHE,EA5FIR,EAApBM,EA6FM,IAAIG,GAAQ,SAASR,GAE5B,IACC,OAAOG,EAAQH,EAAcA,EAAYO,GAASA,EAGlD,CAFC,MAAMQ,GACP,OAAOC,EAASD,EAChB,CACJ,KAhGSV,CACP,CAuBD,SAASG,EAAQJ,GAChBa,KAAKb,KAAOA,CACZ,CAmFD,SAASY,EAASL,GAUjB,OATQ,IAAIH,GAAQ,SAASU,EAAGjB,GAE/B,IACC,OAAOA,EAAaE,EAAQF,EAAWU,IAAWK,EAASL,EAG3D,CAFC,MAAMI,GACP,OAAOC,EAASD,EAChB,CACJ,GAGE,CAWD,SAASL,IACR,IAAcL,EAASc,EAAUC,EAChCC,EAAOC,EAAWC,EAwGnB,OAlGAlB,EAAU,IAAIG,EAAQJ,GAuBtBe,EAAW,GACXC,EAAmB,GAWnBC,EAAQ,SAASrB,EAAaC,EAAYC,GAEzC,IAAII,EAAUkB,EAuBd,OArBAlB,EAAWI,IAEXc,EAAwC,mBAAftB,EACtB,SAASW,GACV,IAECP,EAASQ,SAASZ,EAAWW,GAI7B,CAHC,MAAME,GAEPT,EAASQ,SAASC,EAClB,CACD,EACC,SAASF,GAAUP,EAASQ,SAASD,EAAQ,EAEhDM,EAASM,MAAK,SAASpB,GACtBA,EAAQD,KAAKJ,EAAaC,GACxBG,KAAKE,EAASH,QAASG,EAASM,OAAQY,EAC9C,IAEGJ,EAAiBK,KAAKD,GAEflB,EAASD,OACnB,EAOEiB,EAAY,SAAST,GAEpB,OADAa,EAAaN,EAAkBP,GACxBA,CACV,EAQEU,EAAW,SAAShB,GAgBnB,OAfAA,EAAQJ,EAAQI,GAGhBc,EAAQd,EAAMH,KAEdmB,EAAWpB,EAEXmB,EAAYK,EAGZD,EAAaP,EAAUZ,GAGvBa,EAAmBD,EAAWrB,EAEvBS,CACV,EAzFa,CACVH,KAAUA,EACVD,QAAUyB,EACVhB,OAAUiB,EAEVf,SAAUgB,EAEVzB,QAAUA,EAEV0B,SAAU,CACT5B,QAAUyB,EACVhB,OAAUiB,EACVf,SAAUgB,IAwFZ,SAAS1B,EAAKJ,EAAaC,EAAYC,GAEtC,OAAOmB,EAAMrB,EAAaC,EAAYC,EACtC,CAKD,SAAS0B,EAAeI,GACvB,OAAOT,EAASS,EAChB,CAKD,SAASH,EAAcI,GACtB,OAAOV,EAASP,EAASiB,GACzB,CAKD,SAASH,EAAgBjB,GACxB,OAAOS,EAAUT,EACjB,CACD,CAUD,SAASJ,EAAUV,GAClB,OAAOA,GAAiD,mBAAxBA,EAAeK,IAC/C,CAkBD,SAAS8B,EAAKC,EAAkBC,EAASpC,EAAaC,EAAYC,GAIjE,OAFAmC,EAAe,EAAGC,WAEX5C,EAAKyC,GAAkB,SAASA,GAEtC,IAAII,EAAWC,EAAUC,EAAQC,EAASpC,EAAUqC,EAAYC,EAAW9B,EAAU+B,EAAKC,EAa1F,GAXAD,EAAMV,EAAiBY,SAAW,EAElCR,EAAYS,KAAKC,IAAI,EAAGD,KAAKE,IAAId,EAASS,IAC1CJ,EAAS,GAETD,EAAYK,EAAMN,EAAa,EAC/BG,EAAU,GAEVpC,EAAWI,IAGN6B,EA0BJ,IAtBAzB,EAAWR,EAASQ,SAEpB8B,EAAY,SAASjC,GACpB+B,EAAQjB,KAAKd,KACP6B,IACLG,EAAaC,EAAYjB,EACzBrB,EAASM,OAAO8B,GAEtB,EAEIC,EAAa,SAASX,GAIrBS,EAAOhB,KAAKO,KAELO,IACNI,EAAaC,EAAYjB,EACzBrB,EAASH,QAAQsC,GAEvB,EAEQK,EAAI,EAAGA,EAAID,IAAOC,EAClBA,KAAKX,GACPzC,EAAKyC,EAAiBW,GAAIK,EAAWC,EAAUtC,QA3BjDR,EAASH,QAAQsC,GAgClB,OAAOnC,EAASF,KAAKJ,EAAaC,EAAYC,GAE9C,SAASkD,EAASzC,GACjBiC,EAAUjC,EACV,CAED,SAASwC,EAAUnB,GAClBW,EAAWX,EACX,CAEJ,GACE,CAqCD,SAASqB,EAAIlB,EAAkBnC,EAAaC,EAAYC,GAEvD,OADAmC,EAAe,EAAGC,WACXgB,EAAInB,EAAkBoB,GAAUnD,KAAKJ,EAAaC,EAAYC,EACrE,CAuBD,SAASoD,EAAIjD,EAASmD,GACrB,OAAO9D,EAAKW,GAAS,SAASoD,GAC7B,IAAIC,EAASb,EAAKN,EAAWpC,EAAS2C,EAAGa,EAQzC,GAJApB,EAAYM,EAAMY,EAAMV,SAAW,EACnCW,EAAU,GACVC,EAAIjD,IAEA6B,EAeH,IAXApC,EAAU,SAAoByD,EAAMd,GACnCpD,EAAKkE,EAAMJ,GAASpD,MAAK,SAASyD,GACjCH,EAAQZ,GAAKe,IAEPtB,GACLoB,EAAExD,QAAQuD,EAEjB,GAAQC,EAAE/C,OACV,EAGQkC,EAAI,EAAGA,EAAID,EAAKC,IAChBA,KAAKW,EACPtD,EAAQsD,EAAMX,GAAIA,KAEhBP,OAlBJoB,EAAExD,QAAQuD,GAwBX,OAAOC,EAAEtD,OAEZ,GACE,CA0ED,SAASqB,EAAaoC,EAAOvD,GAG5B,IAFA,IAAIwD,EAASjB,EAAI,EAEViB,EAAUD,EAAMhB,MACtBiB,EAAQxD,EAET,CAWD,SAAS8B,EAAe2B,EAAOC,GAI9B,IAFA,IAAIC,EAAKpB,EAAImB,EAAiBlB,OAExBD,EAAIkB,GAGT,GAAW,OAFXE,EAAMD,IAAmBnB,KAEQ,mBAAPoB,EACzB,MAAM,IAAI1E,MAAM,OAAOsD,EAAE,sBAG3B,CAMD,SAASnB,IAAS,CA0DlB,SAAS4B,EAASY,GACjB,OAAOA,CACP,CAED,OA3sBAzE,EAAKgB,MAAYA,EACjBhB,EAAKS,QAAYA,EACjBT,EAAKkB,OA2FL,SAAgBb,GACf,OAAOL,EAAKK,EAAgBiB,EAC5B,EA3FDtB,EAAK0E,KAueL,WACC,OAAOd,EAAIhB,UAAWiB,EACtB,EAveD7D,EAAK2D,IAAYA,EACjB3D,EAAK4D,IAAYA,EACjB5D,EAAK2E,OAwiBL,SAAgBhE,EAASiE,GACxB,IAAIC,EAAO1E,EAAM2E,KAAKlC,UAAW,GAEjC,OAAO5C,EAAKW,GAAS,SAASoD,GAC7B,IAAIgB,EAcJ,OAZAA,EAAQhB,EAAMV,OAIdwB,EAAK,GAAK,SAAUG,EAAS1C,EAAKc,GACjC,OAAOpD,EAAKgF,GAAS,SAAUC,GAC9B,OAAOjF,EAAKsC,GAAK,SAAUzB,GAC1B,OAAO+D,EAAWK,EAAGpE,EAAOuC,EAAG2B,EACrC,GACA,GACA,EAEU7E,EAAYgF,MAAMnB,EAAOc,EACnC,GACE,EA1jBD7E,EAAKmF,IAicL,SAAa1C,EAAkBnC,EAAaC,EAAYC,GAMvD,OAAOgC,EAAKC,EAAkB,GAJ9B,SAA4BH,GAC3B,OAAOhC,EAAcA,EAAYgC,EAAI,IAAMA,EAAI,EAC/C,GAEoD/B,EAAYC,EACjE,EAvcDR,EAAKwC,KAAYA,EAEjBxC,EAAKoF,MAokBL,SAAe/E,EAAgBgC,EAAUgD,GACxC,IAAIC,EAAkB1C,UAAUS,OAAS,EAEzC,OAAOrD,EAAKK,GACX,SAASiC,GAGR,OAFAA,EAAMgD,EAAkBD,EAAe/C,EACvCD,EAAS5B,QAAQ6B,GACVA,CACP,IACD,SAASrB,GAER,OADAoB,EAASnB,OAAOD,GACTK,EAASL,EAChB,GACDoB,EAASjB,SAEV,EAjlBDpB,EAAKe,UAAYA,EA6FjBD,EAAQyE,UAAY,CASnBC,OAAQ,SAASC,EAAuBjF,GACvC,OAAOe,KAAKb,KAAK+E,EAAuBA,EAAuBjF,EAC/D,EAODkF,UAAW,SAASnF,GACnB,OAAOgB,KAAKb,KAAKN,EAAOG,EACxB,EAUDoF,MAAO,SAAS9E,GACf,OAAOU,KAAKb,MAAK,WAChB,OAAOG,CACX,GACG,EASD+E,OAAQ,SAAStF,GAChB,OAAOiB,KAAKb,MAAK,SAASqD,GAEzB,OAAOJ,EAAII,GAAO,SAASA,GAC1B,OAAOzD,EAAY4E,MAAM9E,EAAO2D,EACrC,GACA,GACG,GAifF5D,EAAQ,GAAGA,MAKXD,EAAc,GAAGyE,QAChB,SAASC,GAQR,IAAIiB,EAAKhB,EAAMiB,EAAS3C,EAAKC,EAY7B,GAVAA,EAAI,EAKJD,GADA0C,EAAM9G,OAAOwC,OACH8B,SAAW,GACrBwB,EAAOjC,WAICS,QAAU,EAEjB,OAAQ,CACP,GAAGD,KAAKyC,EAAK,CACZC,EAAUD,EAAIzC,KACd,KACA,CAID,KAAKA,GAAKD,EACT,MAAM,IAAI4C,SAEX,MAGDD,EAAUjB,EAAK,GAIhB,KAAKzB,EAAID,IAAOC,EAEZA,KAAKyC,IACPC,EAAUlB,EAAWkB,EAASD,EAAIzC,GAAIA,EAAGyC,IAI3C,OAAOC,CACV,EAMQ9F,CACR,EACyBP,EAAOE,QAAUM,GAK1C,2EC5tBA,SAAiBY,GACf,OAAOA,OACT"}