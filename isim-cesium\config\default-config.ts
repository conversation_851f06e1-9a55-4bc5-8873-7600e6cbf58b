import * as Cesium from 'cesium';
console.log(Cesium);

export const defaultConfig = {
  DEFAULT_VIEWER_NAME: 'cesium-box',
  // defaultImageryUrl: '/map/twdata/military_style01/{z}/{x}/{y}.png',
  defaultImageryUrl: {
    // url: 'http://20.20.1.190:60000/map/globe/{z}/{x}/{y}.jpg',
    tilingScheme: new Cesium.WebMercatorTilingScheme(),
    // url: '/map/roadmap/{z}/{x}/{y}.png',
    url: '/map/GoogleEarth/{z}/{x}/{reverseY}.jpg',
    // tilingScheme: new Cesium.GeographicTilingScheme(),
    minimumLevel: 0,
    maximumLevel: 19
  },
  defaultTerrainUrl: '/terrain',
  // viewer 的默认配置
  viewerConfig: {
    infoBox: false,
    animation: false,
    homeButton: false,
    geocoder: false,
    shouldAnimate: false,
    baseLayerPicker: false,
    fullscreenButton: false,
    timeline: false,
    sceneMode: Cesium.SceneMode.SCENE3D,
    selectionIndicator: true,
    navigationHelpButton: false,
    sceneModePicker: false,
    terrain: true
  },

  synchronizeConfig: {
    maxTime: 5,
    interval: 1000,
    minSpeed: 0.001,
    cacheSize: 5,
    defaultStopTime: '9999/12/31 23:59:59'
  },

  eventConfig: {
    clickAndDbClickInterval: 100,
    defaultLeftClick: true,
    defaultRightClick: false,
    defaultClearClick: false,
    defaultDoubleClick: false,
    defaultMouseMove: false,
    defaultLeftUp: false,
    defaultLeftDown: false
  },
  navigation: {
    duration: 3,
    enableCompass: true,
    enableDistanceLegend: true,
    enableCompassOuterRing: true,
    enableZoomControls: true
  }
  // defaultVectorImageryProviderConfig: {
  //   country: {}
  // }
};
