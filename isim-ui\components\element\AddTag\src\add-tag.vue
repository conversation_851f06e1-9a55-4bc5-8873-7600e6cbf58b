<!--
* <AUTHOR> wll
* @Date :  2022/10/13
* @Version : 1.0
* @Content : addTag
-->
<template>
  <el-input
    v-if="inputVisible"
    ref="saveTagInput"
    v-model="inputValue"
    class="input-new-tag"
    size="small"
    @keyup.enter="handleInputConfirm"
    @blur="handleInputConfirm"
  >
  </el-input>
  <el-button v-else class="button-new-tag" size="small" @click="showInput"><slot>添加</slot></el-button>
</template>

<script>
export default {
  name: 'SimAddTag'
};
</script>
<script setup>
import { nextTick, ref, shallowRef } from 'vue';

const emits = defineEmits(['handle-add']);

const saveTagInput = shallowRef();

const inputVisible = ref(false);
const inputValue = ref('');
const handleInputConfirm = () => {
  if (inputValue.value !== '') {
    emits('handle-add', inputValue.value);
    inputValue.value = '';
  }
  inputVisible.value = false;
};

const showInput = () => {
  inputVisible.value = true;
  nextTick().then(() => {
    saveTagInput.value.$refs.input.focus();
  });
};
</script>
<style scoped lang="less">
.input-new-tag {
  width: 100px;
  margin-left: 5px;
}
.button-new-tag {
  margin-left: 5px;
}
</style>
