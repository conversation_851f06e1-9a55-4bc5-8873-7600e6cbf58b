<!--
* @Author: 任强
* @Date: 2023/4/7 17:27
* @Version: 1.0
* @Content:
-->
<template>
  <!-- 选中菜单svg颜色模板 -->
  <svg
    width="5.000000"
    height="40.000000"
    viewBox="0 0 5 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <defs>
      <linearGradient id="paint_linear_22_1982_0" x1="2.500000" y1="0.000000" x2="2.500000" y2="40.000000" gradientUnits="userSpaceOnUse">
        <stop stop-color="#fff" />
        <stop offset="0.600976" stop-color="#005259" />
        <stop offset="1.000000" stop-color="#00B5C5" />
      </linearGradient>
      <linearGradient id="paint_linear_22_1983_0" x1="5.000000" y1="20.000000" x2="0.565844" y2="20.000000" gradientUnits="userSpaceOnUse">
        <stop stop-color="#fff" />
        <stop offset="0.265432" stop-color="#F2F2F2" />
        <stop offset="0.664609" stop-color="#A3A3A3" />
        <stop offset="1.000000" stop-color="#D4D4D4" />
      </linearGradient>
    </defs>
  </svg>

  <div class="nav-box" :style="{ width: collapseWidth }">
    <NavTop :collapse="collapse" class="nav-box-top" />
    <NavLeft :collapse="collapse" class="nav-box-left" @change-collapse="handleCollapse" />
    <div class="nav-content">
      <el-menu class="nav-box-menu" :collapse="collapse" :unique-opened="true" :default-active="curActive" @select="handleSelectMenu">
        <template v-for="item in menuList" :key="item.id">
          <el-sub-menu v-if="item.children?.length" :index="item.routePath" :expand-close-icon="ArrowDown" :expand-open-icon="ArrowUp">
            <template #title>
              <i :class="['icon-space', getIcon(item)]" />
              <span class="yt-nav-menu__sub-title">{{ getTitle(item) }}</span>
            </template>
            <template v-for="it in item.children" :key="it.routePath">
              <template v-if="it.component">
                <el-menu-item :index="it.routePath">
                  <i :class="['icon-space', getIcon(it)]" />
                  <template #title>
                    <span class="yt-nav-menu__title">{{ getTitle(it) }}</span>
                  </template>
                </el-menu-item>
              </template>
            </template>
          </el-sub-menu>
          <template v-else>
            <el-menu-item v-if="item.component" :index="item.routePath">
              <i :class="['icon-space', getIcon(item)]" />
              <template #title>
                <span class="yt-nav-menu__title">{{ getTitle(item) }}</span>
              </template>
            </el-menu-item>
          </template>
        </template>
      </el-menu>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'NavMenu'
};
</script>
<script setup lang="ts">
import { ref } from 'vue';

import NavTop from './svg/nav-top.vue';
import NavLeft from './svg/nav-left.vue';
import ArrowUp from './svg/arrow-up.vue';
import ArrowDown from './svg/arrow-down.vue';

import { MenuTableList } from '@/api';

interface PropsOpt {
  menuList: MenuTableList[];
}

withDefaults(defineProps<PropsOpt>(), {});

const emits = defineEmits(['change']);

const getIcon = (item: MenuTableList) => {
  const icon = item.meta?.icon ?? '';
  return item.routePath === curActive.value ? `${icon}-fill` : icon;
};

const getTitle = (item: MenuTableList) => {
  return item.meta?.title ?? item.menuName;
};

const collapse = ref(false);
const handleCollapse = () => {
  collapse.value = !collapse.value;
  emits('change', toRaw(collapse.value));
};

const route = useRoute();
const curActive = computed(() => {
  return route.matched?.[2]?.path || route.path;
});

const router = useRouter();
const handleSelectMenu = (key: string) => {
  if (key.includes('not')) {
    return;
  }
  router.push(key);
};

const collapseWidth = computed(() => (toRaw(collapse.value) ? '76px' : '272px'));
</script>

<style scoped lang="less">
.nav-box {
  position: relative;
  padding: 16px 0 24px 16px;
  height: 100%;
  box-sizing: border-box;
  transition: all 0.3s linear;
  overflow-x: hidden;

  .el-menu--collapse {
    .icon-space {
      margin-right: 0;
    }
  }

  &-top {
    position: absolute;
    top: 0;
    left: 16px;
    transition: all 0.3s linear;
  }

  &-left {
    position: absolute;
    top: 18px;
    left: 0;
  }

  //.is-active {
  //  & > .menu-active-icon {
  //    display: block;
  //  }
  //}

  :deep(.el-sub-menu).is-active .el-sub-menu__title {
    border: none;
  }

  .nav-content {
    padding: 8px;
    border: 2px solid rgba(var(--text-color-val), 0.3);
    border-radius: 0 0 4px 4px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }

  &-menu {
    height: 100%;
    overflow-y: auto;
    border-right: none;
    background: transparent;

    .icon-space {
      font-size: 20px;
      margin-right: 8px;
      font-weight: 100;
    }
  }

  :deep(.el-menu-item) {
    padding-right: 8px;
    color: var(--text-color);
  }
}
</style>
<style lang="less">
.menu-active-icon {
  display: none;
  position: absolute;

  &__left {
    left: 0;
    transform: rotate(180deg);
  }

  &__right {
    right: 0;
  }
}
.el-menu--collapse .el-sub-menu.is-active .el-sub-menu__title,
.el-menu-item.is-active {
  .menu-active-icon {
    display: block;
  }
}
</style>
