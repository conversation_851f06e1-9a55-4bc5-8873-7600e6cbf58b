<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : distance-select.vue
-->
<template>
  <n-dropdown :options="distanceList" :show="show" :x="x" :y="y" />
</template>

<script setup lang="ts">
import { useEventListener } from '@vueuse/core';

defineOptions({
  name: 'DistanceSelect'
});

const emits = defineEmits(['update:show', 'select']);
defineProps({
  show: {
    type: Boolean,
    default: false
  },
  x: {
    type: Number,
    default: 0
  },
  y: {
    type: Number,
    default: 0
  }
});
const distanceConfigList = [
  {
    ratio: 1,
    label: '1:1400万', // 1400公里
    height: 46248.83085151955
  },
  {
    ratio: 4,
    label: '1:100万', // 1000公里
    height: 109404.75765483479
  },
  {
    ratio: 5,
    label: '1:50万' // 500公里
  },
  {
    ratio: 13.5,
    label: '1:25万' // 250公里
  },
  {
    ratio: 60,
    label: '1:5万' // 50公里
  },
  {
    ratio: 200,
    label: '1:1万' // 10公里
  }
];
const handleSelectDropNode = (key: number) => {
  emits('select', key);
};

const distanceList = distanceConfigList.map((it) => {
  return {
    key: it.ratio,
    label: it.label,
    props: {
      onClick: () => handleSelectDropNode(it.ratio)
    }
  };
});
const updateShow = () => {
  emits('update:show', false);
};
const close = useEventListener(document.documentElement, 'click', updateShow);
onBeforeUnmount(() => {
  close();
});
</script>

<style scoped lang="less"></style>
