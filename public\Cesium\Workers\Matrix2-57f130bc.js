define(["exports","./RuntimeError-1349fdaf","./when-4bbc8319","./ComponentDatatype-17ffa790"],(function(e,t,n,r){"use strict";function a(e,t,r){this.x=n.defaultValue(e,0),this.y=n.defaultValue(t,0),this.z=n.defaultValue(r,0)}a.fromSpherical=function(e,r){t.Check.typeOf.object("spherical",e),n.defined(r)||(r=new a);const o=e.clock,c=e.cone,i=n.defaultValue(e.magnitude,1),u=i*Math.sin(c);return r.x=u*Math.cos(o),r.y=u*Math.sin(o),r.z=i*Math.cos(c),r},a.fromElements=function(e,t,r,o){return n.defined(o)?(o.x=e,o.y=t,o.z=r,o):new a(e,t,r)},a.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t):new a(e.x,e.y,e.z)},a.fromCartesian4=a.clone,a.packedLength=3,a.pack=function(e,r,a){return t.Check.typeOf.object("value",e),t.Check.defined("array",r),a=n.defaultValue(a,0),r[a++]=e.x,r[a++]=e.y,r[a]=e.z,r},a.unpack=function(e,r,o){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(o)||(o=new a),o.x=e[r++],o.y=e[r++],o.z=e[r],o},a.packArray=function(e,r){t.Check.defined("array",e);const o=e.length,c=3*o;if(n.defined(r)){if(!Array.isArray(r)&&r.length!==c)throw new t.DeveloperError("If result is a typed array, it must have exactly array.length * 3 elements");r.length!==c&&(r.length=c)}else r=new Array(c);for(let t=0;t<o;++t)a.pack(e[t],r,3*t);return r},a.unpackArray=function(e,r){if(t.Check.defined("array",e),t.Check.typeOf.number.greaterThanOrEquals("array.length",e.length,3),e.length%3!=0)throw new t.DeveloperError("array length must be a multiple of 3.");const o=e.length;n.defined(r)?r.length=o/3:r=new Array(o/3);for(let t=0;t<o;t+=3){const n=t/3;r[n]=a.unpack(e,t,r[n])}return r},a.fromArray=a.unpack,a.maximumComponent=function(e){return t.Check.typeOf.object("cartesian",e),Math.max(e.x,e.y,e.z)},a.minimumComponent=function(e){return t.Check.typeOf.object("cartesian",e),Math.min(e.x,e.y,e.z)},a.minimumByComponent=function(e,n,r){return t.Check.typeOf.object("first",e),t.Check.typeOf.object("second",n),t.Check.typeOf.object("result",r),r.x=Math.min(e.x,n.x),r.y=Math.min(e.y,n.y),r.z=Math.min(e.z,n.z),r},a.maximumByComponent=function(e,n,r){return t.Check.typeOf.object("first",e),t.Check.typeOf.object("second",n),t.Check.typeOf.object("result",r),r.x=Math.max(e.x,n.x),r.y=Math.max(e.y,n.y),r.z=Math.max(e.z,n.z),r},a.magnitudeSquared=function(e){return t.Check.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y+e.z*e.z},a.magnitude=function(e){return Math.sqrt(a.magnitudeSquared(e))};const o=new a;a.distance=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),a.subtract(e,n,o),a.magnitude(o)},a.distanceSquared=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),a.subtract(e,n,o),a.magnitudeSquared(o)},a.normalize=function(e,n){t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n);const r=a.magnitude(e);if(n.x=e.x/r,n.y=e.y/r,n.z=e.z/r,isNaN(n.x)||isNaN(n.y)||isNaN(n.z))throw new t.DeveloperError("normalized result is not a number");return n},a.dot=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),e.x*n.x+e.y*n.y+e.z*n.z},a.multiplyComponents=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x*n.x,r.y=e.y*n.y,r.z=e.z*n.z,r},a.divideComponents=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x/n.x,r.y=e.y/n.y,r.z=e.z/n.z,r},a.add=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x+n.x,r.y=e.y+n.y,r.z=e.z+n.z,r},a.subtract=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x-n.x,r.y=e.y-n.y,r.z=e.z-n.z,r},a.multiplyByScalar=function(e,n,r){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r.x=e.x*n,r.y=e.y*n,r.z=e.z*n,r},a.divideByScalar=function(e,n,r){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r.x=e.x/n,r.y=e.y/n,r.z=e.z/n,r},a.negate=function(e,n){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n},a.abs=function(e,n){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n),n.x=Math.abs(e.x),n.y=Math.abs(e.y),n.z=Math.abs(e.z),n};const c=new a;a.lerp=function(e,n,r,o){return t.Check.typeOf.object("start",e),t.Check.typeOf.object("end",n),t.Check.typeOf.number("t",r),t.Check.typeOf.object("result",o),a.multiplyByScalar(n,r,c),o=a.multiplyByScalar(e,1-r,o),a.add(c,o,o)};const i=new a,u=new a;a.angleBetween=function(e,n){t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),a.normalize(e,i),a.normalize(n,u);const r=a.dot(i,u),o=a.magnitude(a.cross(i,u,i));return Math.atan2(o,r)};const f=new a;a.mostOrthogonalAxis=function(e,n){t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n);const r=a.normalize(e,f);return a.abs(r,r),n=r.x<=r.y?r.x<=r.z?a.clone(a.UNIT_X,n):a.clone(a.UNIT_Z,n):r.y<=r.z?a.clone(a.UNIT_Y,n):a.clone(a.UNIT_Z,n)},a.projectVector=function(e,n,r){t.Check.defined("a",e),t.Check.defined("b",n),t.Check.defined("result",r);const o=a.dot(e,n)/a.dot(n,n);return a.multiplyByScalar(n,o,r)},a.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z},a.equalsArray=function(e,t,n){return e.x===t[n]&&e.y===t[n+1]&&e.z===t[n+2]},a.equalsEpsilon=function(e,t,a,o){return e===t||n.defined(e)&&n.defined(t)&&r.CesiumMath.equalsEpsilon(e.x,t.x,a,o)&&r.CesiumMath.equalsEpsilon(e.y,t.y,a,o)&&r.CesiumMath.equalsEpsilon(e.z,t.z,a,o)},a.cross=function(e,n,r){t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r);const a=e.x,o=e.y,c=e.z,i=n.x,u=n.y,f=n.z,s=o*f-c*u,h=c*i-a*f,l=a*u-o*i;return r.x=s,r.y=h,r.z=l,r},a.midpoint=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=.5*(e.x+n.x),r.y=.5*(e.y+n.y),r.z=.5*(e.z+n.z),r},a.fromDegrees=function(e,n,o,c,i){return t.Check.typeOf.number("longitude",e),t.Check.typeOf.number("latitude",n),e=r.CesiumMath.toRadians(e),n=r.CesiumMath.toRadians(n),a.fromRadians(e,n,o,c,i)};let s=new a,h=new a;const l=new a(40680631590769,40680631590769,40408299984661.445);a.fromRadians=function(e,r,o,c,i){t.Check.typeOf.number("longitude",e),t.Check.typeOf.number("latitude",r),o=n.defaultValue(o,0);const u=n.defined(c)?c.radiiSquared:l,f=Math.cos(r);s.x=f*Math.cos(e),s.y=f*Math.sin(e),s.z=Math.sin(r),s=a.normalize(s,s),a.multiplyComponents(u,s,h);const y=Math.sqrt(a.dot(s,h));return h=a.divideByScalar(h,y,h),s=a.multiplyByScalar(s,o,s),n.defined(i)||(i=new a),a.add(h,s,i)},a.fromDegreesArray=function(e,r,o){if(t.Check.defined("coordinates",e),e.length<2||e.length%2!=0)throw new t.DeveloperError("the number of coordinates must be a multiple of 2 and at least 2");const c=e.length;n.defined(o)?o.length=c/2:o=new Array(c/2);for(let t=0;t<c;t+=2){const n=e[t],c=e[t+1],i=t/2;o[i]=a.fromDegrees(n,c,0,r,o[i])}return o},a.fromRadiansArray=function(e,r,o){if(t.Check.defined("coordinates",e),e.length<2||e.length%2!=0)throw new t.DeveloperError("the number of coordinates must be a multiple of 2 and at least 2");const c=e.length;n.defined(o)?o.length=c/2:o=new Array(c/2);for(let t=0;t<c;t+=2){const n=e[t],c=e[t+1],i=t/2;o[i]=a.fromRadians(n,c,0,r,o[i])}return o},a.fromDegreesArrayHeights=function(e,r,o){if(t.Check.defined("coordinates",e),e.length<3||e.length%3!=0)throw new t.DeveloperError("the number of coordinates must be a multiple of 3 and at least 3");const c=e.length;n.defined(o)?o.length=c/3:o=new Array(c/3);for(let t=0;t<c;t+=3){const n=e[t],c=e[t+1],i=e[t+2],u=t/3;o[u]=a.fromDegrees(n,c,i,r,o[u])}return o},a.fromRadiansArrayHeights=function(e,r,o){if(t.Check.defined("coordinates",e),e.length<3||e.length%3!=0)throw new t.DeveloperError("the number of coordinates must be a multiple of 3 and at least 3");const c=e.length;n.defined(o)?o.length=c/3:o=new Array(c/3);for(let t=0;t<c;t+=3){const n=e[t],c=e[t+1],i=e[t+2],u=t/3;o[u]=a.fromRadians(n,c,i,r,o[u])}return o},a.ZERO=Object.freeze(new a(0,0,0)),a.ONE=Object.freeze(new a(1,1,1)),a.UNIT_X=Object.freeze(new a(1,0,0)),a.UNIT_Y=Object.freeze(new a(0,1,0)),a.UNIT_Z=Object.freeze(new a(0,0,1)),a.prototype.clone=function(e){return a.clone(this,e)},a.prototype.equals=function(e){return a.equals(this,e)},a.prototype.equalsEpsilon=function(e,t,n){return a.equalsEpsilon(this,e,t,n)},a.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+")"};const y=new a,d=new a;function p(e,o,c,i,u){if(!n.defined(e))throw new t.DeveloperError("cartesian is required.");if(!n.defined(o))throw new t.DeveloperError("oneOverRadii is required.");if(!n.defined(c))throw new t.DeveloperError("oneOverRadiiSquared is required.");if(!n.defined(i))throw new t.DeveloperError("centerToleranceSquared is required.");const f=e.x,s=e.y,h=e.z,l=o.x,p=o.y,m=o.z,O=f*f*l*l,C=s*s*p*p,b=h*h*m*m,k=O+C+b,x=Math.sqrt(1/k),g=a.multiplyByScalar(e,x,y);if(k<i)return isFinite(x)?a.clone(g,u):void 0;const j=c.x,w=c.y,M=c.z,z=d;z.x=g.x*j*2,z.y=g.y*w*2,z.z=g.z*M*2;let E,q,T,S,_,R,V,N,I,v,A,U=(1-x)*a.magnitude(e)/(.5*a.magnitude(z)),P=0;do{U-=P,T=1/(1+U*j),S=1/(1+U*w),_=1/(1+U*M),R=T*T,V=S*S,N=_*_,I=R*T,v=V*S,A=N*_,E=O*R+C*V+b*N-1,q=O*I*j+C*v*w+b*A*M;P=E/(-2*q)}while(Math.abs(E)>r.CesiumMath.EPSILON12);return n.defined(u)?(u.x=f*T,u.y=s*S,u.z=h*_,u):new a(f*T,s*S,h*_)}function m(e,t,r){this.longitude=n.defaultValue(e,0),this.latitude=n.defaultValue(t,0),this.height=n.defaultValue(r,0)}m.fromRadians=function(e,r,a,o){return t.Check.typeOf.number("longitude",e),t.Check.typeOf.number("latitude",r),a=n.defaultValue(a,0),n.defined(o)?(o.longitude=e,o.latitude=r,o.height=a,o):new m(e,r,a)},m.fromDegrees=function(e,n,a,o){return t.Check.typeOf.number("longitude",e),t.Check.typeOf.number("latitude",n),e=r.CesiumMath.toRadians(e),n=r.CesiumMath.toRadians(n),m.fromRadians(e,n,a,o)};const O=new a,C=new a,b=new a,k=new a(1/6378137,1/6378137,1/6356752.314245179),x=new a(1/40680631590769,1/40680631590769,1/40408299984661.445),g=r.CesiumMath.EPSILON1;function j(e,o,c,i){o=n.defaultValue(o,0),c=n.defaultValue(c,0),i=n.defaultValue(i,0),t.Check.typeOf.number.greaterThanOrEquals("x",o,0),t.Check.typeOf.number.greaterThanOrEquals("y",c,0),t.Check.typeOf.number.greaterThanOrEquals("z",i,0),e._radii=new a(o,c,i),e._radiiSquared=new a(o*o,c*c,i*i),e._radiiToTheFourth=new a(o*o*o*o,c*c*c*c,i*i*i*i),e._oneOverRadii=new a(0===o?0:1/o,0===c?0:1/c,0===i?0:1/i),e._oneOverRadiiSquared=new a(0===o?0:1/(o*o),0===c?0:1/(c*c),0===i?0:1/(i*i)),e._minimumRadius=Math.min(o,c,i),e._maximumRadius=Math.max(o,c,i),e._centerToleranceSquared=r.CesiumMath.EPSILON1,0!==e._radiiSquared.z&&(e._squaredXOverSquaredZ=e._radiiSquared.x/e._radiiSquared.z)}function w(e,t,n){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,j(this,e,t,n)}m.fromCartesian=function(e,t,o){const c=n.defined(t)?t.oneOverRadii:k,i=n.defined(t)?t.oneOverRadiiSquared:x,u=p(e,c,i,n.defined(t)?t._centerToleranceSquared:g,C);if(!n.defined(u))return;let f=a.multiplyComponents(u,i,O);f=a.normalize(f,f);const s=a.subtract(e,u,b),h=Math.atan2(f.y,f.x),l=Math.asin(f.z),y=r.CesiumMath.sign(a.dot(s,e))*a.magnitude(s);return n.defined(o)?(o.longitude=h,o.latitude=l,o.height=y,o):new m(h,l,y)},m.toCartesian=function(e,n,r){return t.Check.defined("cartographic",e),a.fromRadians(e.longitude,e.latitude,e.height,n,r)},m.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.longitude=e.longitude,t.latitude=e.latitude,t.height=e.height,t):new m(e.longitude,e.latitude,e.height)},m.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.longitude===t.longitude&&e.latitude===t.latitude&&e.height===t.height},m.equalsEpsilon=function(e,t,r){return r=n.defaultValue(r,0),e===t||n.defined(e)&&n.defined(t)&&Math.abs(e.longitude-t.longitude)<=r&&Math.abs(e.latitude-t.latitude)<=r&&Math.abs(e.height-t.height)<=r},m.ZERO=Object.freeze(new m(0,0,0)),m.prototype.clone=function(e){return m.clone(this,e)},m.prototype.equals=function(e){return m.equals(this,e)},m.prototype.equalsEpsilon=function(e,t){return m.equalsEpsilon(this,e,t)},m.prototype.toString=function(){return"("+this.longitude+", "+this.latitude+", "+this.height+")"},Object.defineProperties(w.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}}),w.clone=function(e,t){if(!n.defined(e))return;const r=e._radii;return n.defined(t)?(a.clone(r,t._radii),a.clone(e._radiiSquared,t._radiiSquared),a.clone(e._radiiToTheFourth,t._radiiToTheFourth),a.clone(e._oneOverRadii,t._oneOverRadii),a.clone(e._oneOverRadiiSquared,t._oneOverRadiiSquared),t._minimumRadius=e._minimumRadius,t._maximumRadius=e._maximumRadius,t._centerToleranceSquared=e._centerToleranceSquared,t):new w(r.x,r.y,r.z)},w.fromCartesian3=function(e,t){return n.defined(t)||(t=new w),n.defined(e)?(j(t,e.x,e.y,e.z),t):t},w.WGS84=Object.freeze(new w(6378137,6378137,6356752.314245179)),w.UNIT_SPHERE=Object.freeze(new w(1,1,1)),w.MOON=Object.freeze(new w(r.CesiumMath.LUNAR_RADIUS,r.CesiumMath.LUNAR_RADIUS,r.CesiumMath.LUNAR_RADIUS)),w.prototype.clone=function(e){return w.clone(this,e)},w.packedLength=a.packedLength,w.pack=function(e,r,o){return t.Check.typeOf.object("value",e),t.Check.defined("array",r),o=n.defaultValue(o,0),a.pack(e._radii,r,o),r},w.unpack=function(e,r,o){t.Check.defined("array",e),r=n.defaultValue(r,0);const c=a.unpack(e,r);return w.fromCartesian3(c,o)},w.prototype.geocentricSurfaceNormal=a.normalize,w.prototype.geodeticSurfaceNormalCartographic=function(e,r){t.Check.typeOf.object("cartographic",e);const o=e.longitude,c=e.latitude,i=Math.cos(c),u=i*Math.cos(o),f=i*Math.sin(o),s=Math.sin(c);return n.defined(r)||(r=new a),r.x=u,r.y=f,r.z=s,a.normalize(r,r)},w.prototype.geodeticSurfaceNormal=function(e,t){if(!a.equalsEpsilon(e,a.ZERO,r.CesiumMath.EPSILON14))return n.defined(t)||(t=new a),t=a.multiplyComponents(e,this._oneOverRadiiSquared,t),a.normalize(t,t)};const M=new a,z=new a;w.prototype.cartographicToCartesian=function(e,t){const r=M,o=z;this.geodeticSurfaceNormalCartographic(e,r),a.multiplyComponents(this._radiiSquared,r,o);const c=Math.sqrt(a.dot(r,o));return a.divideByScalar(o,c,o),a.multiplyByScalar(r,e.height,r),n.defined(t)||(t=new a),a.add(o,r,t)},w.prototype.cartographicArrayToCartesianArray=function(e,r){t.Check.defined("cartographics",e);const a=e.length;n.defined(r)?r.length=a:r=new Array(a);for(let t=0;t<a;t++)r[t]=this.cartographicToCartesian(e[t],r[t]);return r};const E=new a,q=new a,T=new a;w.prototype.cartesianToCartographic=function(e,t){const o=this.scaleToGeodeticSurface(e,q);if(!n.defined(o))return;const c=this.geodeticSurfaceNormal(o,E),i=a.subtract(e,o,T),u=Math.atan2(c.y,c.x),f=Math.asin(c.z),s=r.CesiumMath.sign(a.dot(i,e))*a.magnitude(i);return n.defined(t)?(t.longitude=u,t.latitude=f,t.height=s,t):new m(u,f,s)},w.prototype.cartesianArrayToCartographicArray=function(e,r){t.Check.defined("cartesians",e);const a=e.length;n.defined(r)?r.length=a:r=new Array(a);for(let t=0;t<a;++t)r[t]=this.cartesianToCartographic(e[t],r[t]);return r},w.prototype.scaleToGeodeticSurface=function(e,t){return p(e,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,t)},w.prototype.scaleToGeocentricSurface=function(e,r){t.Check.typeOf.object("cartesian",e),n.defined(r)||(r=new a);const o=e.x,c=e.y,i=e.z,u=this._oneOverRadiiSquared,f=1/Math.sqrt(o*o*u.x+c*c*u.y+i*i*u.z);return a.multiplyByScalar(e,f,r)},w.prototype.transformPositionToScaledSpace=function(e,t){return n.defined(t)||(t=new a),a.multiplyComponents(e,this._oneOverRadii,t)},w.prototype.transformPositionFromScaledSpace=function(e,t){return n.defined(t)||(t=new a),a.multiplyComponents(e,this._radii,t)},w.prototype.equals=function(e){return this===e||n.defined(e)&&a.equals(this._radii,e._radii)},w.prototype.toString=function(){return this._radii.toString()},w.prototype.getSurfaceNormalIntersectionWithZAxis=function(e,o,c){if(t.Check.typeOf.object("position",e),!r.CesiumMath.equalsEpsilon(this._radii.x,this._radii.y,r.CesiumMath.EPSILON15))throw new t.DeveloperError("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");t.Check.typeOf.number.greaterThan("Ellipsoid.radii.z",this._radii.z,0),o=n.defaultValue(o,0);const i=this._squaredXOverSquaredZ;if(n.defined(c)||(c=new a),c.x=0,c.y=0,c.z=e.z*(1-i),!(Math.abs(c.z)>=this._radii.z-o))return c};const S=[.14887433898163,.43339539412925,.67940956829902,.86506336668898,.97390652851717,0],_=[.29552422471475,.26926671930999,.21908636251598,.14945134915058,.066671344308684,0];function R(e,n,r){t.Check.typeOf.number("a",e),t.Check.typeOf.number("b",n),t.Check.typeOf.func("func",r);const a=.5*(n+e),o=.5*(n-e);let c=0;for(let e=0;e<5;e++){const t=o*S[e];c+=_[e]*(r(a+t)+r(a-t))}return c*=o,c}function V(e,t,r,a,o,c,i,u,f){this[0]=n.defaultValue(e,0),this[1]=n.defaultValue(a,0),this[2]=n.defaultValue(i,0),this[3]=n.defaultValue(t,0),this[4]=n.defaultValue(o,0),this[5]=n.defaultValue(u,0),this[6]=n.defaultValue(r,0),this[7]=n.defaultValue(c,0),this[8]=n.defaultValue(f,0)}w.prototype.surfaceArea=function(e){t.Check.typeOf.object("rectangle",e);const n=e.west;let a=e.east;const o=e.south,c=e.north;for(;a<n;)a+=r.CesiumMath.TWO_PI;const i=this._radiiSquared,u=i.x,f=i.y,s=i.z,h=u*f;return R(o,c,(function(e){const t=Math.cos(e),r=Math.sin(e);return Math.cos(e)*R(n,a,(function(e){const n=Math.cos(e),a=Math.sin(e);return Math.sqrt(h*r*r+s*(f*n*n+u*a*a)*t*t)}))}))},V.packedLength=9,V.pack=function(e,r,a){return t.Check.typeOf.object("value",e),t.Check.defined("array",r),a=n.defaultValue(a,0),r[a++]=e[0],r[a++]=e[1],r[a++]=e[2],r[a++]=e[3],r[a++]=e[4],r[a++]=e[5],r[a++]=e[6],r[a++]=e[7],r[a++]=e[8],r},V.unpack=function(e,r,a){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(a)||(a=new V),a[0]=e[r++],a[1]=e[r++],a[2]=e[r++],a[3]=e[r++],a[4]=e[r++],a[5]=e[r++],a[6]=e[r++],a[7]=e[r++],a[8]=e[r++],a},V.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t):new V(e[0],e[3],e[6],e[1],e[4],e[7],e[2],e[5],e[8])},V.fromArray=function(e,r,a){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(a)||(a=new V),a[0]=e[r],a[1]=e[r+1],a[2]=e[r+2],a[3]=e[r+3],a[4]=e[r+4],a[5]=e[r+5],a[6]=e[r+6],a[7]=e[r+7],a[8]=e[r+8],a},V.fromColumnMajorArray=function(e,n){return t.Check.defined("values",e),V.clone(e,n)},V.fromRowMajorArray=function(e,r){return t.Check.defined("values",e),n.defined(r)?(r[0]=e[0],r[1]=e[3],r[2]=e[6],r[3]=e[1],r[4]=e[4],r[5]=e[7],r[6]=e[2],r[7]=e[5],r[8]=e[8],r):new V(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8])},V.fromQuaternion=function(e,r){t.Check.typeOf.object("quaternion",e);const a=e.x*e.x,o=e.x*e.y,c=e.x*e.z,i=e.x*e.w,u=e.y*e.y,f=e.y*e.z,s=e.y*e.w,h=e.z*e.z,l=e.z*e.w,y=e.w*e.w,d=a-u-h+y,p=2*(o-l),m=2*(c+s),O=2*(o+l),C=-a+u-h+y,b=2*(f-i),k=2*(c-s),x=2*(f+i),g=-a-u+h+y;return n.defined(r)?(r[0]=d,r[1]=O,r[2]=k,r[3]=p,r[4]=C,r[5]=x,r[6]=m,r[7]=b,r[8]=g,r):new V(d,p,m,O,C,b,k,x,g)},V.fromHeadingPitchRoll=function(e,r){t.Check.typeOf.object("headingPitchRoll",e);const a=Math.cos(-e.pitch),o=Math.cos(-e.heading),c=Math.cos(e.roll),i=Math.sin(-e.pitch),u=Math.sin(-e.heading),f=Math.sin(e.roll),s=a*o,h=-c*u+f*i*o,l=f*u+c*i*o,y=a*u,d=c*o+f*i*u,p=-f*o+c*i*u,m=-i,O=f*a,C=c*a;return n.defined(r)?(r[0]=s,r[1]=y,r[2]=m,r[3]=h,r[4]=d,r[5]=O,r[6]=l,r[7]=p,r[8]=C,r):new V(s,h,l,y,d,p,m,O,C)},V.fromScale=function(e,r){return t.Check.typeOf.object("scale",e),n.defined(r)?(r[0]=e.x,r[1]=0,r[2]=0,r[3]=0,r[4]=e.y,r[5]=0,r[6]=0,r[7]=0,r[8]=e.z,r):new V(e.x,0,0,0,e.y,0,0,0,e.z)},V.fromUniformScale=function(e,r){return t.Check.typeOf.number("scale",e),n.defined(r)?(r[0]=e,r[1]=0,r[2]=0,r[3]=0,r[4]=e,r[5]=0,r[6]=0,r[7]=0,r[8]=e,r):new V(e,0,0,0,e,0,0,0,e)},V.fromCrossProduct=function(e,r){return t.Check.typeOf.object("vector",e),n.defined(r)?(r[0]=0,r[1]=e.z,r[2]=-e.y,r[3]=-e.z,r[4]=0,r[5]=e.x,r[6]=e.y,r[7]=-e.x,r[8]=0,r):new V(0,-e.z,e.y,e.z,0,-e.x,-e.y,e.x,0)},V.fromRotationX=function(e,r){t.Check.typeOf.number("angle",e);const a=Math.cos(e),o=Math.sin(e);return n.defined(r)?(r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=a,r[5]=o,r[6]=0,r[7]=-o,r[8]=a,r):new V(1,0,0,0,a,-o,0,o,a)},V.fromRotationY=function(e,r){t.Check.typeOf.number("angle",e);const a=Math.cos(e),o=Math.sin(e);return n.defined(r)?(r[0]=a,r[1]=0,r[2]=-o,r[3]=0,r[4]=1,r[5]=0,r[6]=o,r[7]=0,r[8]=a,r):new V(a,0,o,0,1,0,-o,0,a)},V.fromRotationZ=function(e,r){t.Check.typeOf.number("angle",e);const a=Math.cos(e),o=Math.sin(e);return n.defined(r)?(r[0]=a,r[1]=o,r[2]=0,r[3]=-o,r[4]=a,r[5]=0,r[6]=0,r[7]=0,r[8]=1,r):new V(a,-o,0,o,a,0,0,0,1)},V.toArray=function(e,r){return t.Check.typeOf.object("matrix",e),n.defined(r)?(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8]]},V.getElementIndex=function(e,n){return t.Check.typeOf.number.greaterThanOrEquals("row",n,0),t.Check.typeOf.number.lessThanOrEquals("row",n,2),t.Check.typeOf.number.greaterThanOrEquals("column",e,0),t.Check.typeOf.number.lessThanOrEquals("column",e,2),3*e+n},V.getColumn=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,2),t.Check.typeOf.object("result",r);const a=3*n,o=e[a],c=e[a+1],i=e[a+2];return r.x=o,r.y=c,r.z=i,r},V.setColumn=function(e,n,r,a){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,2),t.Check.typeOf.object("cartesian",r),t.Check.typeOf.object("result",a);const o=3*n;return(a=V.clone(e,a))[o]=r.x,a[o+1]=r.y,a[o+2]=r.z,a},V.getRow=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,2),t.Check.typeOf.object("result",r);const a=e[n],o=e[n+3],c=e[n+6];return r.x=a,r.y=o,r.z=c,r},V.setRow=function(e,n,r,a){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,2),t.Check.typeOf.object("cartesian",r),t.Check.typeOf.object("result",a),(a=V.clone(e,a))[n]=r.x,a[n+3]=r.y,a[n+6]=r.z,a};const N=new a;V.getScale=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n.x=a.magnitude(a.fromElements(e[0],e[1],e[2],N)),n.y=a.magnitude(a.fromElements(e[3],e[4],e[5],N)),n.z=a.magnitude(a.fromElements(e[6],e[7],e[8],N)),n};const I=new a;V.getMaximumScale=function(e){return V.getScale(e,I),a.maximumComponent(I)},V.multiply=function(e,n,r){t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r);const a=e[0]*n[0]+e[3]*n[1]+e[6]*n[2],o=e[1]*n[0]+e[4]*n[1]+e[7]*n[2],c=e[2]*n[0]+e[5]*n[1]+e[8]*n[2],i=e[0]*n[3]+e[3]*n[4]+e[6]*n[5],u=e[1]*n[3]+e[4]*n[4]+e[7]*n[5],f=e[2]*n[3]+e[5]*n[4]+e[8]*n[5],s=e[0]*n[6]+e[3]*n[7]+e[6]*n[8],h=e[1]*n[6]+e[4]*n[7]+e[7]*n[8],l=e[2]*n[6]+e[5]*n[7]+e[8]*n[8];return r[0]=a,r[1]=o,r[2]=c,r[3]=i,r[4]=u,r[5]=f,r[6]=s,r[7]=h,r[8]=l,r},V.add=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r[4]=e[4]+n[4],r[5]=e[5]+n[5],r[6]=e[6]+n[6],r[7]=e[7]+n[7],r[8]=e[8]+n[8],r},V.subtract=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r[0]=e[0]-n[0],r[1]=e[1]-n[1],r[2]=e[2]-n[2],r[3]=e[3]-n[3],r[4]=e[4]-n[4],r[5]=e[5]-n[5],r[6]=e[6]-n[6],r[7]=e[7]-n[7],r[8]=e[8]-n[8],r},V.multiplyByVector=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("cartesian",n),t.Check.typeOf.object("result",r);const a=n.x,o=n.y,c=n.z,i=e[0]*a+e[3]*o+e[6]*c,u=e[1]*a+e[4]*o+e[7]*c,f=e[2]*a+e[5]*o+e[8]*c;return r.x=i,r.y=u,r.z=f,r},V.multiplyByScalar=function(e,n,r){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r[4]=e[4]*n,r[5]=e[5]*n,r[6]=e[6]*n,r[7]=e[7]*n,r[8]=e[8]*n,r},V.multiplyByScale=function(e,n,r){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("scale",n),t.Check.typeOf.object("result",r),r[0]=e[0]*n.x,r[1]=e[1]*n.x,r[2]=e[2]*n.x,r[3]=e[3]*n.y,r[4]=e[4]*n.y,r[5]=e[5]*n.y,r[6]=e[6]*n.z,r[7]=e[7]*n.z,r[8]=e[8]*n.z,r},V.negate=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n[4]=-e[4],n[5]=-e[5],n[6]=-e[6],n[7]=-e[7],n[8]=-e[8],n},V.transpose=function(e,n){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n);const r=e[0],a=e[3],o=e[6],c=e[1],i=e[4],u=e[7],f=e[2],s=e[5],h=e[8];return n[0]=r,n[1]=a,n[2]=o,n[3]=c,n[4]=i,n[5]=u,n[6]=f,n[7]=s,n[8]=h,n};const v=new a(1,1,1);V.getRotation=function(e,n){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n);const r=a.divideComponents(v,V.getScale(e,I),I);return n=V.multiplyByScale(e,r,n)};const A=[1,0,0],U=[2,2,1];function P(e){let t=0;for(let n=0;n<3;++n){const r=e[V.getElementIndex(U[n],A[n])];t+=2*r*r}return Math.sqrt(t)}function L(e,t){const n=r.CesiumMath.EPSILON15;let a=0,o=1;for(let t=0;t<3;++t){const n=Math.abs(e[V.getElementIndex(U[t],A[t])]);n>a&&(o=t,a=n)}let c=1,i=0;const u=A[o],f=U[o];if(Math.abs(e[V.getElementIndex(f,u)])>n){const t=(e[V.getElementIndex(f,f)]-e[V.getElementIndex(u,u)])/2/e[V.getElementIndex(f,u)];let n;n=t<0?-1/(-t+Math.sqrt(1+t*t)):1/(t+Math.sqrt(1+t*t)),c=1/Math.sqrt(1+n*n),i=n*c}return(t=V.clone(V.IDENTITY,t))[V.getElementIndex(u,u)]=t[V.getElementIndex(f,f)]=c,t[V.getElementIndex(f,u)]=i,t[V.getElementIndex(u,f)]=-i,t}const W=new V,B=new V;V.computeEigenDecomposition=function(e,a){t.Check.typeOf.object("matrix",e);const o=r.CesiumMath.EPSILON20;let c=0,i=0;n.defined(a)||(a={});const u=a.unitary=V.clone(V.IDENTITY,a.unitary),f=a.diagonal=V.clone(e,a.diagonal),s=o*function(e){let t=0;for(let n=0;n<9;++n){const r=e[n];t+=r*r}return Math.sqrt(t)}(f);for(;i<10&&P(f)>s;)L(f,W),V.transpose(W,B),V.multiply(f,W,f),V.multiply(B,f,f),V.multiply(u,W,u),++c>2&&(++i,c=0);return a},V.abs=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n[4]=Math.abs(e[4]),n[5]=Math.abs(e[5]),n[6]=Math.abs(e[6]),n[7]=Math.abs(e[7]),n[8]=Math.abs(e[8]),n},V.determinant=function(e){t.Check.typeOf.object("matrix",e);const n=e[0],r=e[3],a=e[6],o=e[1],c=e[4],i=e[7],u=e[2],f=e[5],s=e[8];return n*(c*s-f*i)+o*(f*a-r*s)+u*(r*i-c*a)},V.inverse=function(e,n){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n);const a=e[0],o=e[1],c=e[2],i=e[3],u=e[4],f=e[5],s=e[6],h=e[7],l=e[8],y=V.determinant(e);if(Math.abs(y)<=r.CesiumMath.EPSILON15)throw new t.DeveloperError("matrix is not invertible");n[0]=u*l-h*f,n[1]=h*c-o*l,n[2]=o*f-u*c,n[3]=s*f-i*l,n[4]=a*l-s*c,n[5]=i*c-a*f,n[6]=i*h-s*u,n[7]=s*o-a*h,n[8]=a*u-i*o;const d=1/y;return V.multiplyByScalar(n,d,n)};const D=new V;function X(e,t,r,a){this.x=n.defaultValue(e,0),this.y=n.defaultValue(t,0),this.z=n.defaultValue(r,0),this.w=n.defaultValue(a,0)}V.inverseTranspose=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),V.inverse(V.transpose(e,D),n)},V.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]&&e[4]===t[4]&&e[5]===t[5]&&e[6]===t[6]&&e[7]===t[7]&&e[8]===t[8]},V.equalsEpsilon=function(e,t,r){return r=n.defaultValue(r,0),e===t||n.defined(e)&&n.defined(t)&&Math.abs(e[0]-t[0])<=r&&Math.abs(e[1]-t[1])<=r&&Math.abs(e[2]-t[2])<=r&&Math.abs(e[3]-t[3])<=r&&Math.abs(e[4]-t[4])<=r&&Math.abs(e[5]-t[5])<=r&&Math.abs(e[6]-t[6])<=r&&Math.abs(e[7]-t[7])<=r&&Math.abs(e[8]-t[8])<=r},V.IDENTITY=Object.freeze(new V(1,0,0,0,1,0,0,0,1)),V.ZERO=Object.freeze(new V(0,0,0,0,0,0,0,0,0)),V.COLUMN0ROW0=0,V.COLUMN0ROW1=1,V.COLUMN0ROW2=2,V.COLUMN1ROW0=3,V.COLUMN1ROW1=4,V.COLUMN1ROW2=5,V.COLUMN2ROW0=6,V.COLUMN2ROW1=7,V.COLUMN2ROW2=8,Object.defineProperties(V.prototype,{length:{get:function(){return V.packedLength}}}),V.prototype.clone=function(e){return V.clone(this,e)},V.prototype.equals=function(e){return V.equals(this,e)},V.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]&&e[4]===t[n+4]&&e[5]===t[n+5]&&e[6]===t[n+6]&&e[7]===t[n+7]&&e[8]===t[n+8]},V.prototype.equalsEpsilon=function(e,t){return V.equalsEpsilon(this,e,t)},V.prototype.toString=function(){return"("+this[0]+", "+this[3]+", "+this[6]+")\n("+this[1]+", "+this[4]+", "+this[7]+")\n("+this[2]+", "+this[5]+", "+this[8]+")"},X.fromElements=function(e,t,r,a,o){return n.defined(o)?(o.x=e,o.y=t,o.z=r,o.w=a,o):new X(e,t,r,a)},X.fromColor=function(e,r){return t.Check.typeOf.object("color",e),n.defined(r)?(r.x=e.red,r.y=e.green,r.z=e.blue,r.w=e.alpha,r):new X(e.red,e.green,e.blue,e.alpha)},X.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new X(e.x,e.y,e.z,e.w)},X.packedLength=4,X.pack=function(e,r,a){return t.Check.typeOf.object("value",e),t.Check.defined("array",r),a=n.defaultValue(a,0),r[a++]=e.x,r[a++]=e.y,r[a++]=e.z,r[a]=e.w,r},X.unpack=function(e,r,a){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(a)||(a=new X),a.x=e[r++],a.y=e[r++],a.z=e[r++],a.w=e[r],a},X.packArray=function(e,r){t.Check.defined("array",e);const a=e.length,o=4*a;if(n.defined(r)){if(!Array.isArray(r)&&r.length!==o)throw new t.DeveloperError("If result is a typed array, it must have exactly array.length * 4 elements");r.length!==o&&(r.length=o)}else r=new Array(o);for(let t=0;t<a;++t)X.pack(e[t],r,4*t);return r},X.unpackArray=function(e,r){if(t.Check.defined("array",e),t.Check.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!=0)throw new t.DeveloperError("array length must be a multiple of 4.");const a=e.length;n.defined(r)?r.length=a/4:r=new Array(a/4);for(let t=0;t<a;t+=4){const n=t/4;r[n]=X.unpack(e,t,r[n])}return r},X.fromArray=X.unpack,X.maximumComponent=function(e){return t.Check.typeOf.object("cartesian",e),Math.max(e.x,e.y,e.z,e.w)},X.minimumComponent=function(e){return t.Check.typeOf.object("cartesian",e),Math.min(e.x,e.y,e.z,e.w)},X.minimumByComponent=function(e,n,r){return t.Check.typeOf.object("first",e),t.Check.typeOf.object("second",n),t.Check.typeOf.object("result",r),r.x=Math.min(e.x,n.x),r.y=Math.min(e.y,n.y),r.z=Math.min(e.z,n.z),r.w=Math.min(e.w,n.w),r},X.maximumByComponent=function(e,n,r){return t.Check.typeOf.object("first",e),t.Check.typeOf.object("second",n),t.Check.typeOf.object("result",r),r.x=Math.max(e.x,n.x),r.y=Math.max(e.y,n.y),r.z=Math.max(e.z,n.z),r.w=Math.max(e.w,n.w),r},X.magnitudeSquared=function(e){return t.Check.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},X.magnitude=function(e){return Math.sqrt(X.magnitudeSquared(e))};const Z=new X;X.distance=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),X.subtract(e,n,Z),X.magnitude(Z)},X.distanceSquared=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),X.subtract(e,n,Z),X.magnitudeSquared(Z)},X.normalize=function(e,n){t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n);const r=X.magnitude(e);if(n.x=e.x/r,n.y=e.y/r,n.z=e.z/r,n.w=e.w/r,isNaN(n.x)||isNaN(n.y)||isNaN(n.z)||isNaN(n.w))throw new t.DeveloperError("normalized result is not a number");return n},X.dot=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),e.x*n.x+e.y*n.y+e.z*n.z+e.w*n.w},X.multiplyComponents=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x*n.x,r.y=e.y*n.y,r.z=e.z*n.z,r.w=e.w*n.w,r},X.divideComponents=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x/n.x,r.y=e.y/n.y,r.z=e.z/n.z,r.w=e.w/n.w,r},X.add=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x+n.x,r.y=e.y+n.y,r.z=e.z+n.z,r.w=e.w+n.w,r},X.subtract=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x-n.x,r.y=e.y-n.y,r.z=e.z-n.z,r.w=e.w-n.w,r},X.multiplyByScalar=function(e,n,r){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r.x=e.x*n,r.y=e.y*n,r.z=e.z*n,r.w=e.w*n,r},X.divideByScalar=function(e,n,r){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r.x=e.x/n,r.y=e.y/n,r.z=e.z/n,r.w=e.w/n,r},X.negate=function(e,n){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=-e.w,n},X.abs=function(e,n){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n),n.x=Math.abs(e.x),n.y=Math.abs(e.y),n.z=Math.abs(e.z),n.w=Math.abs(e.w),n};const Y=new X;X.lerp=function(e,n,r,a){return t.Check.typeOf.object("start",e),t.Check.typeOf.object("end",n),t.Check.typeOf.number("t",r),t.Check.typeOf.object("result",a),X.multiplyByScalar(n,r,Y),a=X.multiplyByScalar(e,1-r,a),X.add(Y,a,a)};const F=new X;X.mostOrthogonalAxis=function(e,n){t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n);const r=X.normalize(e,F);return X.abs(r,r),n=r.x<=r.y?r.x<=r.z?r.x<=r.w?X.clone(X.UNIT_X,n):X.clone(X.UNIT_W,n):r.z<=r.w?X.clone(X.UNIT_Z,n):X.clone(X.UNIT_W,n):r.y<=r.z?r.y<=r.w?X.clone(X.UNIT_Y,n):X.clone(X.UNIT_W,n):r.z<=r.w?X.clone(X.UNIT_Z,n):X.clone(X.UNIT_W,n)},X.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},X.equalsArray=function(e,t,n){return e.x===t[n]&&e.y===t[n+1]&&e.z===t[n+2]&&e.w===t[n+3]},X.equalsEpsilon=function(e,t,a,o){return e===t||n.defined(e)&&n.defined(t)&&r.CesiumMath.equalsEpsilon(e.x,t.x,a,o)&&r.CesiumMath.equalsEpsilon(e.y,t.y,a,o)&&r.CesiumMath.equalsEpsilon(e.z,t.z,a,o)&&r.CesiumMath.equalsEpsilon(e.w,t.w,a,o)},X.ZERO=Object.freeze(new X(0,0,0,0)),X.ONE=Object.freeze(new X(1,1,1,1)),X.UNIT_X=Object.freeze(new X(1,0,0,0)),X.UNIT_Y=Object.freeze(new X(0,1,0,0)),X.UNIT_Z=Object.freeze(new X(0,0,1,0)),X.UNIT_W=Object.freeze(new X(0,0,0,1)),X.prototype.clone=function(e){return X.clone(this,e)},X.prototype.equals=function(e){return X.equals(this,e)},X.prototype.equalsEpsilon=function(e,t,n){return X.equalsEpsilon(this,e,t,n)},X.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};const G=new Float32Array(1),H=new Uint8Array(G.buffer),Q=new Uint32Array([287454020]),J=68===new Uint8Array(Q.buffer)[0];function K(e,t,r,a,o,c,i,u,f,s,h,l,y,d,p,m){this[0]=n.defaultValue(e,0),this[1]=n.defaultValue(o,0),this[2]=n.defaultValue(f,0),this[3]=n.defaultValue(y,0),this[4]=n.defaultValue(t,0),this[5]=n.defaultValue(c,0),this[6]=n.defaultValue(s,0),this[7]=n.defaultValue(d,0),this[8]=n.defaultValue(r,0),this[9]=n.defaultValue(i,0),this[10]=n.defaultValue(h,0),this[11]=n.defaultValue(p,0),this[12]=n.defaultValue(a,0),this[13]=n.defaultValue(u,0),this[14]=n.defaultValue(l,0),this[15]=n.defaultValue(m,0)}X.packFloat=function(e,r){return t.Check.typeOf.number("value",e),n.defined(r)||(r=new X),G[0]=e,J?(r.x=H[0],r.y=H[1],r.z=H[2],r.w=H[3]):(r.x=H[3],r.y=H[2],r.z=H[1],r.w=H[0]),r},X.unpackFloat=function(e){return t.Check.typeOf.object("packedFloat",e),J?(H[0]=e.x,H[1]=e.y,H[2]=e.z,H[3]=e.w):(H[0]=e.w,H[1]=e.z,H[2]=e.y,H[3]=e.x),G[0]},K.packedLength=16,K.pack=function(e,r,a){return t.Check.typeOf.object("value",e),t.Check.defined("array",r),a=n.defaultValue(a,0),r[a++]=e[0],r[a++]=e[1],r[a++]=e[2],r[a++]=e[3],r[a++]=e[4],r[a++]=e[5],r[a++]=e[6],r[a++]=e[7],r[a++]=e[8],r[a++]=e[9],r[a++]=e[10],r[a++]=e[11],r[a++]=e[12],r[a++]=e[13],r[a++]=e[14],r[a]=e[15],r},K.unpack=function(e,r,a){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(a)||(a=new K),a[0]=e[r++],a[1]=e[r++],a[2]=e[r++],a[3]=e[r++],a[4]=e[r++],a[5]=e[r++],a[6]=e[r++],a[7]=e[r++],a[8]=e[r++],a[9]=e[r++],a[10]=e[r++],a[11]=e[r++],a[12]=e[r++],a[13]=e[r++],a[14]=e[r++],a[15]=e[r],a},K.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t):new K(e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15])},K.fromArray=K.unpack,K.fromColumnMajorArray=function(e,n){return t.Check.defined("values",e),K.clone(e,n)},K.fromRowMajorArray=function(e,r){return t.Check.defined("values",e),n.defined(r)?(r[0]=e[0],r[1]=e[4],r[2]=e[8],r[3]=e[12],r[4]=e[1],r[5]=e[5],r[6]=e[9],r[7]=e[13],r[8]=e[2],r[9]=e[6],r[10]=e[10],r[11]=e[14],r[12]=e[3],r[13]=e[7],r[14]=e[11],r[15]=e[15],r):new K(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])},K.fromRotationTranslation=function(e,r,o){return t.Check.typeOf.object("rotation",e),r=n.defaultValue(r,a.ZERO),n.defined(o)?(o[0]=e[0],o[1]=e[1],o[2]=e[2],o[3]=0,o[4]=e[3],o[5]=e[4],o[6]=e[5],o[7]=0,o[8]=e[6],o[9]=e[7],o[10]=e[8],o[11]=0,o[12]=r.x,o[13]=r.y,o[14]=r.z,o[15]=1,o):new K(e[0],e[3],e[6],r.x,e[1],e[4],e[7],r.y,e[2],e[5],e[8],r.z,0,0,0,1)},K.fromTranslationQuaternionRotationScale=function(e,r,a,o){t.Check.typeOf.object("translation",e),t.Check.typeOf.object("rotation",r),t.Check.typeOf.object("scale",a),n.defined(o)||(o=new K);const c=a.x,i=a.y,u=a.z,f=r.x*r.x,s=r.x*r.y,h=r.x*r.z,l=r.x*r.w,y=r.y*r.y,d=r.y*r.z,p=r.y*r.w,m=r.z*r.z,O=r.z*r.w,C=r.w*r.w,b=f-y-m+C,k=2*(s-O),x=2*(h+p),g=2*(s+O),j=-f+y-m+C,w=2*(d-l),M=2*(h-p),z=2*(d+l),E=-f-y+m+C;return o[0]=b*c,o[1]=g*c,o[2]=M*c,o[3]=0,o[4]=k*i,o[5]=j*i,o[6]=z*i,o[7]=0,o[8]=x*u,o[9]=w*u,o[10]=E*u,o[11]=0,o[12]=e.x,o[13]=e.y,o[14]=e.z,o[15]=1,o},K.fromTranslationRotationScale=function(e,n){return t.Check.typeOf.object("translationRotationScale",e),K.fromTranslationQuaternionRotationScale(e.translation,e.rotation,e.scale,n)},K.fromTranslation=function(e,n){return t.Check.typeOf.object("translation",e),K.fromRotationTranslation(V.IDENTITY,e,n)},K.fromScale=function(e,r){return t.Check.typeOf.object("scale",e),n.defined(r)?(r[0]=e.x,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=e.y,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=e.z,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r):new K(e.x,0,0,0,0,e.y,0,0,0,0,e.z,0,0,0,0,1)},K.fromUniformScale=function(e,r){return t.Check.typeOf.number("scale",e),n.defined(r)?(r[0]=e,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=e,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=e,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r):new K(e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1)};const $=new a,ee=new a,te=new a;K.fromCamera=function(e,r){t.Check.typeOf.object("camera",e);const o=e.position,c=e.direction,i=e.up;t.Check.typeOf.object("camera.position",o),t.Check.typeOf.object("camera.direction",c),t.Check.typeOf.object("camera.up",i),a.normalize(c,$),a.normalize(a.cross($,i,ee),ee),a.normalize(a.cross(ee,$,te),te);const u=ee.x,f=ee.y,s=ee.z,h=$.x,l=$.y,y=$.z,d=te.x,p=te.y,m=te.z,O=o.x,C=o.y,b=o.z,k=u*-O+f*-C+s*-b,x=d*-O+p*-C+m*-b,g=h*O+l*C+y*b;return n.defined(r)?(r[0]=u,r[1]=d,r[2]=-h,r[3]=0,r[4]=f,r[5]=p,r[6]=-l,r[7]=0,r[8]=s,r[9]=m,r[10]=-y,r[11]=0,r[12]=k,r[13]=x,r[14]=g,r[15]=1,r):new K(u,f,s,k,d,p,m,x,-h,-l,-y,g,0,0,0,1)},K.computePerspectiveFieldOfView=function(e,n,r,a,o){t.Check.typeOf.number.greaterThan("fovY",e,0),t.Check.typeOf.number.lessThan("fovY",e,Math.PI),t.Check.typeOf.number.greaterThan("near",r,0),t.Check.typeOf.number.greaterThan("far",a,0),t.Check.typeOf.object("result",o);const c=1/Math.tan(.5*e),i=c/n,u=(a+r)/(r-a),f=2*a*r/(r-a);return o[0]=i,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=c,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=u,o[11]=-1,o[12]=0,o[13]=0,o[14]=f,o[15]=0,o},K.computeOrthographicOffCenter=function(e,n,r,a,o,c,i){t.Check.typeOf.number("left",e),t.Check.typeOf.number("right",n),t.Check.typeOf.number("bottom",r),t.Check.typeOf.number("top",a),t.Check.typeOf.number("near",o),t.Check.typeOf.number("far",c),t.Check.typeOf.object("result",i);let u=1/(n-e),f=1/(a-r),s=1/(c-o);const h=-(n+e)*u,l=-(a+r)*f,y=-(c+o)*s;return u*=2,f*=2,s*=-2,i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=f,i[6]=0,i[7]=0,i[8]=0,i[9]=0,i[10]=s,i[11]=0,i[12]=h,i[13]=l,i[14]=y,i[15]=1,i},K.computePerspectiveOffCenter=function(e,n,r,a,o,c,i){t.Check.typeOf.number("left",e),t.Check.typeOf.number("right",n),t.Check.typeOf.number("bottom",r),t.Check.typeOf.number("top",a),t.Check.typeOf.number("near",o),t.Check.typeOf.number("far",c),t.Check.typeOf.object("result",i);const u=2*o/(n-e),f=2*o/(a-r),s=(n+e)/(n-e),h=(a+r)/(a-r),l=-(c+o)/(c-o),y=-2*c*o/(c-o);return i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=f,i[6]=0,i[7]=0,i[8]=s,i[9]=h,i[10]=l,i[11]=-1,i[12]=0,i[13]=0,i[14]=y,i[15]=0,i},K.computeInfinitePerspectiveOffCenter=function(e,n,r,a,o,c){t.Check.typeOf.number("left",e),t.Check.typeOf.number("right",n),t.Check.typeOf.number("bottom",r),t.Check.typeOf.number("top",a),t.Check.typeOf.number("near",o),t.Check.typeOf.object("result",c);const i=2*o/(n-e),u=2*o/(a-r),f=(n+e)/(n-e),s=(a+r)/(a-r),h=-2*o;return c[0]=i,c[1]=0,c[2]=0,c[3]=0,c[4]=0,c[5]=u,c[6]=0,c[7]=0,c[8]=f,c[9]=s,c[10]=-1,c[11]=-1,c[12]=0,c[13]=0,c[14]=h,c[15]=0,c},K.computeViewportTransformation=function(e,t,r,a){n.defined(a)||(a=new K),e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT);const o=n.defaultValue(e.x,0),c=n.defaultValue(e.y,0),i=n.defaultValue(e.width,0),u=n.defaultValue(e.height,0);t=n.defaultValue(t,0);const f=.5*i,s=.5*u,h=.5*((r=n.defaultValue(r,1))-t),l=f,y=s,d=h,p=o+f,m=c+s,O=t+h;return a[0]=l,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=y,a[6]=0,a[7]=0,a[8]=0,a[9]=0,a[10]=d,a[11]=0,a[12]=p,a[13]=m,a[14]=O,a[15]=1,a},K.computeView=function(e,n,r,o,c){return t.Check.typeOf.object("position",e),t.Check.typeOf.object("direction",n),t.Check.typeOf.object("up",r),t.Check.typeOf.object("right",o),t.Check.typeOf.object("result",c),c[0]=o.x,c[1]=r.x,c[2]=-n.x,c[3]=0,c[4]=o.y,c[5]=r.y,c[6]=-n.y,c[7]=0,c[8]=o.z,c[9]=r.z,c[10]=-n.z,c[11]=0,c[12]=-a.dot(o,e),c[13]=-a.dot(r,e),c[14]=a.dot(n,e),c[15]=1,c},K.toArray=function(e,r){return t.Check.typeOf.object("matrix",e),n.defined(r)?(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r[9]=e[9],r[10]=e[10],r[11]=e[11],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15],r):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]]},K.getElementIndex=function(e,n){return t.Check.typeOf.number.greaterThanOrEquals("row",n,0),t.Check.typeOf.number.lessThanOrEquals("row",n,3),t.Check.typeOf.number.greaterThanOrEquals("column",e,0),t.Check.typeOf.number.lessThanOrEquals("column",e,3),4*e+n},K.getColumn=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,3),t.Check.typeOf.object("result",r);const a=4*n,o=e[a],c=e[a+1],i=e[a+2],u=e[a+3];return r.x=o,r.y=c,r.z=i,r.w=u,r},K.setColumn=function(e,n,r,a){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,3),t.Check.typeOf.object("cartesian",r),t.Check.typeOf.object("result",a);const o=4*n;return(a=K.clone(e,a))[o]=r.x,a[o+1]=r.y,a[o+2]=r.z,a[o+3]=r.w,a},K.setTranslation=function(e,n,r){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("translation",n),t.Check.typeOf.object("result",r),r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r[9]=e[9],r[10]=e[10],r[11]=e[11],r[12]=n.x,r[13]=n.y,r[14]=n.z,r[15]=e[15],r};const ne=new a;K.setScale=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("scale",n),t.Check.typeOf.object("result",r);const o=K.getScale(e,ne),c=a.divideComponents(n,o,ne);return K.multiplyByScale(e,c,r)},K.getRow=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,3),t.Check.typeOf.object("result",r);const a=e[n],o=e[n+4],c=e[n+8],i=e[n+12];return r.x=a,r.y=o,r.z=c,r.w=i,r},K.setRow=function(e,n,r,a){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,3),t.Check.typeOf.object("cartesian",r),t.Check.typeOf.object("result",a),(a=K.clone(e,a))[n]=r.x,a[n+4]=r.y,a[n+8]=r.z,a[n+12]=r.w,a};const re=new a;K.getScale=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n.x=a.magnitude(a.fromElements(e[0],e[1],e[2],re)),n.y=a.magnitude(a.fromElements(e[4],e[5],e[6],re)),n.z=a.magnitude(a.fromElements(e[8],e[9],e[10],re)),n};const ae=new a;K.getMaximumScale=function(e){return K.getScale(e,ae),a.maximumComponent(ae)},K.multiply=function(e,n,r){t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r);const a=e[0],o=e[1],c=e[2],i=e[3],u=e[4],f=e[5],s=e[6],h=e[7],l=e[8],y=e[9],d=e[10],p=e[11],m=e[12],O=e[13],C=e[14],b=e[15],k=n[0],x=n[1],g=n[2],j=n[3],w=n[4],M=n[5],z=n[6],E=n[7],q=n[8],T=n[9],S=n[10],_=n[11],R=n[12],V=n[13],N=n[14],I=n[15],v=a*k+u*x+l*g+m*j,A=o*k+f*x+y*g+O*j,U=c*k+s*x+d*g+C*j,P=i*k+h*x+p*g+b*j,L=a*w+u*M+l*z+m*E,W=o*w+f*M+y*z+O*E,B=c*w+s*M+d*z+C*E,D=i*w+h*M+p*z+b*E,X=a*q+u*T+l*S+m*_,Z=o*q+f*T+y*S+O*_,Y=c*q+s*T+d*S+C*_,F=i*q+h*T+p*S+b*_,G=a*R+u*V+l*N+m*I,H=o*R+f*V+y*N+O*I,Q=c*R+s*V+d*N+C*I,J=i*R+h*V+p*N+b*I;return r[0]=v,r[1]=A,r[2]=U,r[3]=P,r[4]=L,r[5]=W,r[6]=B,r[7]=D,r[8]=X,r[9]=Z,r[10]=Y,r[11]=F,r[12]=G,r[13]=H,r[14]=Q,r[15]=J,r},K.add=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r[4]=e[4]+n[4],r[5]=e[5]+n[5],r[6]=e[6]+n[6],r[7]=e[7]+n[7],r[8]=e[8]+n[8],r[9]=e[9]+n[9],r[10]=e[10]+n[10],r[11]=e[11]+n[11],r[12]=e[12]+n[12],r[13]=e[13]+n[13],r[14]=e[14]+n[14],r[15]=e[15]+n[15],r},K.subtract=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r[0]=e[0]-n[0],r[1]=e[1]-n[1],r[2]=e[2]-n[2],r[3]=e[3]-n[3],r[4]=e[4]-n[4],r[5]=e[5]-n[5],r[6]=e[6]-n[6],r[7]=e[7]-n[7],r[8]=e[8]-n[8],r[9]=e[9]-n[9],r[10]=e[10]-n[10],r[11]=e[11]-n[11],r[12]=e[12]-n[12],r[13]=e[13]-n[13],r[14]=e[14]-n[14],r[15]=e[15]-n[15],r},K.multiplyTransformation=function(e,n,r){t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r);const a=e[0],o=e[1],c=e[2],i=e[4],u=e[5],f=e[6],s=e[8],h=e[9],l=e[10],y=e[12],d=e[13],p=e[14],m=n[0],O=n[1],C=n[2],b=n[4],k=n[5],x=n[6],g=n[8],j=n[9],w=n[10],M=n[12],z=n[13],E=n[14],q=a*m+i*O+s*C,T=o*m+u*O+h*C,S=c*m+f*O+l*C,_=a*b+i*k+s*x,R=o*b+u*k+h*x,V=c*b+f*k+l*x,N=a*g+i*j+s*w,I=o*g+u*j+h*w,v=c*g+f*j+l*w,A=a*M+i*z+s*E+y,U=o*M+u*z+h*E+d,P=c*M+f*z+l*E+p;return r[0]=q,r[1]=T,r[2]=S,r[3]=0,r[4]=_,r[5]=R,r[6]=V,r[7]=0,r[8]=N,r[9]=I,r[10]=v,r[11]=0,r[12]=A,r[13]=U,r[14]=P,r[15]=1,r},K.multiplyByMatrix3=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("rotation",n),t.Check.typeOf.object("result",r);const a=e[0],o=e[1],c=e[2],i=e[4],u=e[5],f=e[6],s=e[8],h=e[9],l=e[10],y=n[0],d=n[1],p=n[2],m=n[3],O=n[4],C=n[5],b=n[6],k=n[7],x=n[8],g=a*y+i*d+s*p,j=o*y+u*d+h*p,w=c*y+f*d+l*p,M=a*m+i*O+s*C,z=o*m+u*O+h*C,E=c*m+f*O+l*C,q=a*b+i*k+s*x,T=o*b+u*k+h*x,S=c*b+f*k+l*x;return r[0]=g,r[1]=j,r[2]=w,r[3]=0,r[4]=M,r[5]=z,r[6]=E,r[7]=0,r[8]=q,r[9]=T,r[10]=S,r[11]=0,r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15],r},K.multiplyByTranslation=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("translation",n),t.Check.typeOf.object("result",r);const a=n.x,o=n.y,c=n.z,i=a*e[0]+o*e[4]+c*e[8]+e[12],u=a*e[1]+o*e[5]+c*e[9]+e[13],f=a*e[2]+o*e[6]+c*e[10]+e[14];return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r[9]=e[9],r[10]=e[10],r[11]=e[11],r[12]=i,r[13]=u,r[14]=f,r[15]=e[15],r};const oe=new a;K.multiplyByUniformScale=function(e,n,r){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.number("scale",n),t.Check.typeOf.object("result",r),oe.x=n,oe.y=n,oe.z=n,K.multiplyByScale(e,oe,r)},K.multiplyByScale=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("scale",n),t.Check.typeOf.object("result",r);const a=n.x,o=n.y,c=n.z;return 1===a&&1===o&&1===c?K.clone(e,r):(r[0]=a*e[0],r[1]=a*e[1],r[2]=a*e[2],r[3]=0,r[4]=o*e[4],r[5]=o*e[5],r[6]=o*e[6],r[7]=0,r[8]=c*e[8],r[9]=c*e[9],r[10]=c*e[10],r[11]=0,r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=1,r)},K.multiplyByVector=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("cartesian",n),t.Check.typeOf.object("result",r);const a=n.x,o=n.y,c=n.z,i=n.w,u=e[0]*a+e[4]*o+e[8]*c+e[12]*i,f=e[1]*a+e[5]*o+e[9]*c+e[13]*i,s=e[2]*a+e[6]*o+e[10]*c+e[14]*i,h=e[3]*a+e[7]*o+e[11]*c+e[15]*i;return r.x=u,r.y=f,r.z=s,r.w=h,r},K.multiplyByPointAsVector=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("cartesian",n),t.Check.typeOf.object("result",r);const a=n.x,o=n.y,c=n.z,i=e[0]*a+e[4]*o+e[8]*c,u=e[1]*a+e[5]*o+e[9]*c,f=e[2]*a+e[6]*o+e[10]*c;return r.x=i,r.y=u,r.z=f,r},K.multiplyByPoint=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("cartesian",n),t.Check.typeOf.object("result",r);const a=n.x,o=n.y,c=n.z,i=e[0]*a+e[4]*o+e[8]*c+e[12],u=e[1]*a+e[5]*o+e[9]*c+e[13],f=e[2]*a+e[6]*o+e[10]*c+e[14];return r.x=i,r.y=u,r.z=f,r},K.multiplyByScalar=function(e,n,r){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r[4]=e[4]*n,r[5]=e[5]*n,r[6]=e[6]*n,r[7]=e[7]*n,r[8]=e[8]*n,r[9]=e[9]*n,r[10]=e[10]*n,r[11]=e[11]*n,r[12]=e[12]*n,r[13]=e[13]*n,r[14]=e[14]*n,r[15]=e[15]*n,r},K.negate=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n[4]=-e[4],n[5]=-e[5],n[6]=-e[6],n[7]=-e[7],n[8]=-e[8],n[9]=-e[9],n[10]=-e[10],n[11]=-e[11],n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=-e[15],n},K.transpose=function(e,n){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n);const r=e[1],a=e[2],o=e[3],c=e[6],i=e[7],u=e[11];return n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=r,n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=a,n[9]=c,n[10]=e[10],n[11]=e[14],n[12]=o,n[13]=i,n[14]=u,n[15]=e[15],n},K.abs=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n[4]=Math.abs(e[4]),n[5]=Math.abs(e[5]),n[6]=Math.abs(e[6]),n[7]=Math.abs(e[7]),n[8]=Math.abs(e[8]),n[9]=Math.abs(e[9]),n[10]=Math.abs(e[10]),n[11]=Math.abs(e[11]),n[12]=Math.abs(e[12]),n[13]=Math.abs(e[13]),n[14]=Math.abs(e[14]),n[15]=Math.abs(e[15]),n},K.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e[12]===t[12]&&e[13]===t[13]&&e[14]===t[14]&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[4]===t[4]&&e[5]===t[5]&&e[6]===t[6]&&e[8]===t[8]&&e[9]===t[9]&&e[10]===t[10]&&e[3]===t[3]&&e[7]===t[7]&&e[11]===t[11]&&e[15]===t[15]},K.equalsEpsilon=function(e,t,r){return r=n.defaultValue(r,0),e===t||n.defined(e)&&n.defined(t)&&Math.abs(e[0]-t[0])<=r&&Math.abs(e[1]-t[1])<=r&&Math.abs(e[2]-t[2])<=r&&Math.abs(e[3]-t[3])<=r&&Math.abs(e[4]-t[4])<=r&&Math.abs(e[5]-t[5])<=r&&Math.abs(e[6]-t[6])<=r&&Math.abs(e[7]-t[7])<=r&&Math.abs(e[8]-t[8])<=r&&Math.abs(e[9]-t[9])<=r&&Math.abs(e[10]-t[10])<=r&&Math.abs(e[11]-t[11])<=r&&Math.abs(e[12]-t[12])<=r&&Math.abs(e[13]-t[13])<=r&&Math.abs(e[14]-t[14])<=r&&Math.abs(e[15]-t[15])<=r},K.getTranslation=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n.x=e[12],n.y=e[13],n.z=e[14],n},K.getMatrix3=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[4],n[4]=e[5],n[5]=e[6],n[6]=e[8],n[7]=e[9],n[8]=e[10],n};const ce=new V,ie=new V,ue=new X,fe=new X(0,0,0,1);K.inverse=function(e,n){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n);const a=e[0],o=e[4],c=e[8],i=e[12],u=e[1],f=e[5],s=e[9],h=e[13],l=e[2],y=e[6],d=e[10],p=e[14],m=e[3],O=e[7],C=e[11],b=e[15];let k=d*b,x=p*C,g=y*b,j=p*O,w=y*C,M=d*O,z=l*b,E=p*m,q=l*C,T=d*m,S=l*O,_=y*m;const R=k*f+j*s+w*h-(x*f+g*s+M*h),N=x*u+z*s+T*h-(k*u+E*s+q*h),I=g*u+E*f+S*h-(j*u+z*f+_*h),v=M*u+q*f+_*s-(w*u+T*f+S*s),A=x*o+g*c+M*i-(k*o+j*c+w*i),U=k*a+E*c+q*i-(x*a+z*c+T*i),P=j*a+z*o+_*i-(g*a+E*o+S*i),L=w*a+T*o+S*c-(M*a+q*o+_*c);k=c*h,x=i*s,g=o*h,j=i*f,w=o*s,M=c*f,z=a*h,E=i*u,q=a*s,T=c*u,S=a*f,_=o*u;const W=k*O+j*C+w*b-(x*O+g*C+M*b),B=x*m+z*C+T*b-(k*m+E*C+q*b),D=g*m+E*O+S*b-(j*m+z*O+_*b),Z=M*m+q*O+_*C-(w*m+T*O+S*C),Y=g*d+M*p+x*y-(w*p+k*y+j*d),F=q*p+k*l+E*d-(z*d+T*p+x*l),G=z*y+_*p+j*l-(S*p+g*l+E*y),H=S*d+w*l+T*y-(q*y+_*d+M*l);let Q=a*R+o*N+c*I+i*v;if(Math.abs(Q)<r.CesiumMath.EPSILON21){if(V.equalsEpsilon(K.getMatrix3(e,ce),ie,r.CesiumMath.EPSILON7)&&X.equals(K.getRow(e,3,ue),fe))return n[0]=0,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=0,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=0,n[11]=0,n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=1,n;throw new t.RuntimeError("matrix is not invertible because its determinate is zero.")}return Q=1/Q,n[0]=R*Q,n[1]=N*Q,n[2]=I*Q,n[3]=v*Q,n[4]=A*Q,n[5]=U*Q,n[6]=P*Q,n[7]=L*Q,n[8]=W*Q,n[9]=B*Q,n[10]=D*Q,n[11]=Z*Q,n[12]=Y*Q,n[13]=F*Q,n[14]=G*Q,n[15]=H*Q,n},K.inverseTransformation=function(e,n){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n);const r=e[0],a=e[1],o=e[2],c=e[4],i=e[5],u=e[6],f=e[8],s=e[9],h=e[10],l=e[12],y=e[13],d=e[14],p=-r*l-a*y-o*d,m=-c*l-i*y-u*d,O=-f*l-s*y-h*d;return n[0]=r,n[1]=c,n[2]=f,n[3]=0,n[4]=a,n[5]=i,n[6]=s,n[7]=0,n[8]=o,n[9]=u,n[10]=h,n[11]=0,n[12]=p,n[13]=m,n[14]=O,n[15]=1,n};const se=new K;function he(e,t,r,a){this.west=n.defaultValue(e,0),this.south=n.defaultValue(t,0),this.east=n.defaultValue(r,0),this.north=n.defaultValue(a,0)}K.inverseTranspose=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),K.inverse(K.transpose(e,se),n)},K.IDENTITY=Object.freeze(new K(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)),K.ZERO=Object.freeze(new K(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)),K.COLUMN0ROW0=0,K.COLUMN0ROW1=1,K.COLUMN0ROW2=2,K.COLUMN0ROW3=3,K.COLUMN1ROW0=4,K.COLUMN1ROW1=5,K.COLUMN1ROW2=6,K.COLUMN1ROW3=7,K.COLUMN2ROW0=8,K.COLUMN2ROW1=9,K.COLUMN2ROW2=10,K.COLUMN2ROW3=11,K.COLUMN3ROW0=12,K.COLUMN3ROW1=13,K.COLUMN3ROW2=14,K.COLUMN3ROW3=15,Object.defineProperties(K.prototype,{length:{get:function(){return K.packedLength}}}),K.prototype.clone=function(e){return K.clone(this,e)},K.prototype.equals=function(e){return K.equals(this,e)},K.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]&&e[4]===t[n+4]&&e[5]===t[n+5]&&e[6]===t[n+6]&&e[7]===t[n+7]&&e[8]===t[n+8]&&e[9]===t[n+9]&&e[10]===t[n+10]&&e[11]===t[n+11]&&e[12]===t[n+12]&&e[13]===t[n+13]&&e[14]===t[n+14]&&e[15]===t[n+15]},K.prototype.equalsEpsilon=function(e,t){return K.equalsEpsilon(this,e,t)},K.prototype.toString=function(){return"("+this[0]+", "+this[4]+", "+this[8]+", "+this[12]+")\n("+this[1]+", "+this[5]+", "+this[9]+", "+this[13]+")\n("+this[2]+", "+this[6]+", "+this[10]+", "+this[14]+")\n("+this[3]+", "+this[7]+", "+this[11]+", "+this[15]+")"},Object.defineProperties(he.prototype,{width:{get:function(){return he.computeWidth(this)}},height:{get:function(){return he.computeHeight(this)}}}),he.packedLength=4,he.pack=function(e,r,a){return t.Check.typeOf.object("value",e),t.Check.defined("array",r),a=n.defaultValue(a,0),r[a++]=e.west,r[a++]=e.south,r[a++]=e.east,r[a]=e.north,r},he.unpack=function(e,r,a){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(a)||(a=new he),a.west=e[r++],a.south=e[r++],a.east=e[r++],a.north=e[r],a},he.computeWidth=function(e){t.Check.typeOf.object("rectangle",e);let n=e.east;const a=e.west;return n<a&&(n+=r.CesiumMath.TWO_PI),n-a},he.computeHeight=function(e){return t.Check.typeOf.object("rectangle",e),e.north-e.south},he.fromDegrees=function(e,t,a,o,c){return e=r.CesiumMath.toRadians(n.defaultValue(e,0)),t=r.CesiumMath.toRadians(n.defaultValue(t,0)),a=r.CesiumMath.toRadians(n.defaultValue(a,0)),o=r.CesiumMath.toRadians(n.defaultValue(o,0)),n.defined(c)?(c.west=e,c.south=t,c.east=a,c.north=o,c):new he(e,t,a,o)},he.fromRadians=function(e,t,r,a,o){return n.defined(o)?(o.west=n.defaultValue(e,0),o.south=n.defaultValue(t,0),o.east=n.defaultValue(r,0),o.north=n.defaultValue(a,0),o):new he(e,t,r,a)},he.fromCartographicArray=function(e,a){t.Check.defined("cartographics",e);let o=Number.MAX_VALUE,c=-Number.MAX_VALUE,i=Number.MAX_VALUE,u=-Number.MAX_VALUE,f=Number.MAX_VALUE,s=-Number.MAX_VALUE;for(let t=0,n=e.length;t<n;t++){const n=e[t];o=Math.min(o,n.longitude),c=Math.max(c,n.longitude),f=Math.min(f,n.latitude),s=Math.max(s,n.latitude);const a=n.longitude>=0?n.longitude:n.longitude+r.CesiumMath.TWO_PI;i=Math.min(i,a),u=Math.max(u,a)}return c-o>u-i&&(o=i,c=u,c>r.CesiumMath.PI&&(c-=r.CesiumMath.TWO_PI),o>r.CesiumMath.PI&&(o-=r.CesiumMath.TWO_PI)),n.defined(a)?(a.west=o,a.south=f,a.east=c,a.north=s,a):new he(o,f,c,s)},he.fromCartesianArray=function(e,a,o){t.Check.defined("cartesians",e),a=n.defaultValue(a,w.WGS84);let c=Number.MAX_VALUE,i=-Number.MAX_VALUE,u=Number.MAX_VALUE,f=-Number.MAX_VALUE,s=Number.MAX_VALUE,h=-Number.MAX_VALUE;for(let t=0,n=e.length;t<n;t++){const n=a.cartesianToCartographic(e[t]);c=Math.min(c,n.longitude),i=Math.max(i,n.longitude),s=Math.min(s,n.latitude),h=Math.max(h,n.latitude);const o=n.longitude>=0?n.longitude:n.longitude+r.CesiumMath.TWO_PI;u=Math.min(u,o),f=Math.max(f,o)}return i-c>f-u&&(c=u,i=f,i>r.CesiumMath.PI&&(i-=r.CesiumMath.TWO_PI),c>r.CesiumMath.PI&&(c-=r.CesiumMath.TWO_PI)),n.defined(o)?(o.west=c,o.south=s,o.east=i,o.north=h,o):new he(c,s,i,h)},he.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.west=e.west,t.south=e.south,t.east=e.east,t.north=e.north,t):new he(e.west,e.south,e.east,e.north)},he.equalsEpsilon=function(e,t,r){return r=n.defaultValue(r,0),e===t||n.defined(e)&&n.defined(t)&&Math.abs(e.west-t.west)<=r&&Math.abs(e.south-t.south)<=r&&Math.abs(e.east-t.east)<=r&&Math.abs(e.north-t.north)<=r},he.prototype.clone=function(e){return he.clone(this,e)},he.prototype.equals=function(e){return he.equals(this,e)},he.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.west===t.west&&e.south===t.south&&e.east===t.east&&e.north===t.north},he.prototype.equalsEpsilon=function(e,t){return he.equalsEpsilon(this,e,t)},he.validate=function(e){t.Check.typeOf.object("rectangle",e);const n=e.north;t.Check.typeOf.number.greaterThanOrEquals("north",n,-r.CesiumMath.PI_OVER_TWO),t.Check.typeOf.number.lessThanOrEquals("north",n,r.CesiumMath.PI_OVER_TWO);const a=e.south;t.Check.typeOf.number.greaterThanOrEquals("south",a,-r.CesiumMath.PI_OVER_TWO),t.Check.typeOf.number.lessThanOrEquals("south",a,r.CesiumMath.PI_OVER_TWO);const o=e.west;t.Check.typeOf.number.greaterThanOrEquals("west",o,-Math.PI),t.Check.typeOf.number.lessThanOrEquals("west",o,Math.PI);const c=e.east;t.Check.typeOf.number.greaterThanOrEquals("east",c,-Math.PI),t.Check.typeOf.number.lessThanOrEquals("east",c,Math.PI)},he.southwest=function(e,r){return t.Check.typeOf.object("rectangle",e),n.defined(r)?(r.longitude=e.west,r.latitude=e.south,r.height=0,r):new m(e.west,e.south)},he.northwest=function(e,r){return t.Check.typeOf.object("rectangle",e),n.defined(r)?(r.longitude=e.west,r.latitude=e.north,r.height=0,r):new m(e.west,e.north)},he.northeast=function(e,r){return t.Check.typeOf.object("rectangle",e),n.defined(r)?(r.longitude=e.east,r.latitude=e.north,r.height=0,r):new m(e.east,e.north)},he.southeast=function(e,r){return t.Check.typeOf.object("rectangle",e),n.defined(r)?(r.longitude=e.east,r.latitude=e.south,r.height=0,r):new m(e.east,e.south)},he.center=function(e,a){t.Check.typeOf.object("rectangle",e);let o=e.east;const c=e.west;o<c&&(o+=r.CesiumMath.TWO_PI);const i=r.CesiumMath.negativePiToPi(.5*(c+o)),u=.5*(e.south+e.north);return n.defined(a)?(a.longitude=i,a.latitude=u,a.height=0,a):new m(i,u)},he.intersection=function(e,a,o){t.Check.typeOf.object("rectangle",e),t.Check.typeOf.object("otherRectangle",a);let c=e.east,i=e.west,u=a.east,f=a.west;c<i&&u>0?c+=r.CesiumMath.TWO_PI:u<f&&c>0&&(u+=r.CesiumMath.TWO_PI),c<i&&f<0?f+=r.CesiumMath.TWO_PI:u<f&&i<0&&(i+=r.CesiumMath.TWO_PI);const s=r.CesiumMath.negativePiToPi(Math.max(i,f)),h=r.CesiumMath.negativePiToPi(Math.min(c,u));if((e.west<e.east||a.west<a.east)&&h<=s)return;const l=Math.max(e.south,a.south),y=Math.min(e.north,a.north);return l>=y?void 0:n.defined(o)?(o.west=s,o.south=l,o.east=h,o.north=y,o):new he(s,l,h,y)},he.simpleIntersection=function(e,r,a){t.Check.typeOf.object("rectangle",e),t.Check.typeOf.object("otherRectangle",r);const o=Math.max(e.west,r.west),c=Math.max(e.south,r.south),i=Math.min(e.east,r.east),u=Math.min(e.north,r.north);if(!(c>=u||o>=i))return n.defined(a)?(a.west=o,a.south=c,a.east=i,a.north=u,a):new he(o,c,i,u)},he.union=function(e,a,o){t.Check.typeOf.object("rectangle",e),t.Check.typeOf.object("otherRectangle",a),n.defined(o)||(o=new he);let c=e.east,i=e.west,u=a.east,f=a.west;c<i&&u>0?c+=r.CesiumMath.TWO_PI:u<f&&c>0&&(u+=r.CesiumMath.TWO_PI),c<i&&f<0?f+=r.CesiumMath.TWO_PI:u<f&&i<0&&(i+=r.CesiumMath.TWO_PI);const s=r.CesiumMath.negativePiToPi(Math.min(i,f)),h=r.CesiumMath.negativePiToPi(Math.max(c,u));return o.west=s,o.south=Math.min(e.south,a.south),o.east=h,o.north=Math.max(e.north,a.north),o},he.expand=function(e,r,a){return t.Check.typeOf.object("rectangle",e),t.Check.typeOf.object("cartographic",r),n.defined(a)||(a=new he),a.west=Math.min(e.west,r.longitude),a.south=Math.min(e.south,r.latitude),a.east=Math.max(e.east,r.longitude),a.north=Math.max(e.north,r.latitude),a},he.contains=function(e,n){t.Check.typeOf.object("rectangle",e),t.Check.typeOf.object("cartographic",n);let a=n.longitude;const o=n.latitude,c=e.west;let i=e.east;return i<c&&(i+=r.CesiumMath.TWO_PI,a<0&&(a+=r.CesiumMath.TWO_PI)),(a>c||r.CesiumMath.equalsEpsilon(a,c,r.CesiumMath.EPSILON14))&&(a<i||r.CesiumMath.equalsEpsilon(a,i,r.CesiumMath.EPSILON14))&&o>=e.south&&o<=e.north};const le=new m;function ye(e,t){this.x=n.defaultValue(e,0),this.y=n.defaultValue(t,0)}he.subsample=function(e,a,o,c){t.Check.typeOf.object("rectangle",e),a=n.defaultValue(a,w.WGS84),o=n.defaultValue(o,0),n.defined(c)||(c=[]);let i=0;const u=e.north,f=e.south,s=e.east,h=e.west,l=le;l.height=o,l.longitude=h,l.latitude=u,c[i]=a.cartographicToCartesian(l,c[i]),i++,l.longitude=s,c[i]=a.cartographicToCartesian(l,c[i]),i++,l.latitude=f,c[i]=a.cartographicToCartesian(l,c[i]),i++,l.longitude=h,c[i]=a.cartographicToCartesian(l,c[i]),i++,l.latitude=u<0?u:f>0?f:0;for(let t=1;t<8;++t)l.longitude=-Math.PI+t*r.CesiumMath.PI_OVER_TWO,he.contains(e,l)&&(c[i]=a.cartographicToCartesian(l,c[i]),i++);return 0===l.latitude&&(l.longitude=h,c[i]=a.cartographicToCartesian(l,c[i]),i++,l.longitude=s,c[i]=a.cartographicToCartesian(l,c[i]),i++),c.length=i,c},he.MAX_VALUE=Object.freeze(new he(-Math.PI,-r.CesiumMath.PI_OVER_TWO,Math.PI,r.CesiumMath.PI_OVER_TWO)),ye.fromElements=function(e,t,r){return n.defined(r)?(r.x=e,r.y=t,r):new ye(e,t)},ye.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.x=e.x,t.y=e.y,t):new ye(e.x,e.y)},ye.fromCartesian3=ye.clone,ye.fromCartesian4=ye.clone,ye.packedLength=2,ye.pack=function(e,r,a){return t.Check.typeOf.object("value",e),t.Check.defined("array",r),a=n.defaultValue(a,0),r[a++]=e.x,r[a]=e.y,r},ye.unpack=function(e,r,a){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(a)||(a=new ye),a.x=e[r++],a.y=e[r],a},ye.packArray=function(e,r){t.Check.defined("array",e);const a=e.length,o=2*a;if(n.defined(r)){if(!Array.isArray(r)&&r.length!==o)throw new t.DeveloperError("If result is a typed array, it must have exactly array.length * 2 elements");r.length!==o&&(r.length=o)}else r=new Array(o);for(let t=0;t<a;++t)ye.pack(e[t],r,2*t);return r},ye.unpackArray=function(e,r){if(t.Check.defined("array",e),t.Check.typeOf.number.greaterThanOrEquals("array.length",e.length,2),e.length%2!=0)throw new t.DeveloperError("array length must be a multiple of 2.");const a=e.length;n.defined(r)?r.length=a/2:r=new Array(a/2);for(let t=0;t<a;t+=2){const n=t/2;r[n]=ye.unpack(e,t,r[n])}return r},ye.fromArray=ye.unpack,ye.maximumComponent=function(e){return t.Check.typeOf.object("cartesian",e),Math.max(e.x,e.y)},ye.minimumComponent=function(e){return t.Check.typeOf.object("cartesian",e),Math.min(e.x,e.y)},ye.minimumByComponent=function(e,n,r){return t.Check.typeOf.object("first",e),t.Check.typeOf.object("second",n),t.Check.typeOf.object("result",r),r.x=Math.min(e.x,n.x),r.y=Math.min(e.y,n.y),r},ye.maximumByComponent=function(e,n,r){return t.Check.typeOf.object("first",e),t.Check.typeOf.object("second",n),t.Check.typeOf.object("result",r),r.x=Math.max(e.x,n.x),r.y=Math.max(e.y,n.y),r},ye.magnitudeSquared=function(e){return t.Check.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y},ye.magnitude=function(e){return Math.sqrt(ye.magnitudeSquared(e))};const de=new ye;ye.distance=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),ye.subtract(e,n,de),ye.magnitude(de)},ye.distanceSquared=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),ye.subtract(e,n,de),ye.magnitudeSquared(de)},ye.normalize=function(e,n){t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n);const r=ye.magnitude(e);if(n.x=e.x/r,n.y=e.y/r,isNaN(n.x)||isNaN(n.y))throw new t.DeveloperError("normalized result is not a number");return n},ye.dot=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),e.x*n.x+e.y*n.y},ye.cross=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),e.x*n.y-e.y*n.x},ye.multiplyComponents=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x*n.x,r.y=e.y*n.y,r},ye.divideComponents=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x/n.x,r.y=e.y/n.y,r},ye.add=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x+n.x,r.y=e.y+n.y,r},ye.subtract=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r.x=e.x-n.x,r.y=e.y-n.y,r},ye.multiplyByScalar=function(e,n,r){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r.x=e.x*n,r.y=e.y*n,r},ye.divideByScalar=function(e,n,r){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r.x=e.x/n,r.y=e.y/n,r},ye.negate=function(e,n){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n},ye.abs=function(e,n){return t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n),n.x=Math.abs(e.x),n.y=Math.abs(e.y),n};const pe=new ye;ye.lerp=function(e,n,r,a){return t.Check.typeOf.object("start",e),t.Check.typeOf.object("end",n),t.Check.typeOf.number("t",r),t.Check.typeOf.object("result",a),ye.multiplyByScalar(n,r,pe),a=ye.multiplyByScalar(e,1-r,a),ye.add(pe,a,a)};const me=new ye,Oe=new ye;ye.angleBetween=function(e,n){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),ye.normalize(e,me),ye.normalize(n,Oe),r.CesiumMath.acosClamped(ye.dot(me,Oe))};const Ce=new ye;function be(e,t,r,a){this[0]=n.defaultValue(e,0),this[1]=n.defaultValue(r,0),this[2]=n.defaultValue(t,0),this[3]=n.defaultValue(a,0)}ye.mostOrthogonalAxis=function(e,n){t.Check.typeOf.object("cartesian",e),t.Check.typeOf.object("result",n);const r=ye.normalize(e,Ce);return ye.abs(r,r),n=r.x<=r.y?ye.clone(ye.UNIT_X,n):ye.clone(ye.UNIT_Y,n)},ye.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.x===t.x&&e.y===t.y},ye.equalsArray=function(e,t,n){return e.x===t[n]&&e.y===t[n+1]},ye.equalsEpsilon=function(e,t,a,o){return e===t||n.defined(e)&&n.defined(t)&&r.CesiumMath.equalsEpsilon(e.x,t.x,a,o)&&r.CesiumMath.equalsEpsilon(e.y,t.y,a,o)},ye.ZERO=Object.freeze(new ye(0,0)),ye.ONE=Object.freeze(new ye(1,1)),ye.UNIT_X=Object.freeze(new ye(1,0)),ye.UNIT_Y=Object.freeze(new ye(0,1)),ye.prototype.clone=function(e){return ye.clone(this,e)},ye.prototype.equals=function(e){return ye.equals(this,e)},ye.prototype.equalsEpsilon=function(e,t,n){return ye.equalsEpsilon(this,e,t,n)},ye.prototype.toString=function(){return"("+this.x+", "+this.y+")"},be.packedLength=4,be.pack=function(e,r,a){return t.Check.typeOf.object("value",e),t.Check.defined("array",r),a=n.defaultValue(a,0),r[a++]=e[0],r[a++]=e[1],r[a++]=e[2],r[a++]=e[3],r},be.unpack=function(e,r,a){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(a)||(a=new be),a[0]=e[r++],a[1]=e[r++],a[2]=e[r++],a[3]=e[r++],a},be.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):new be(e[0],e[2],e[1],e[3])},be.fromArray=function(e,r,a){return t.Check.defined("array",e),r=n.defaultValue(r,0),n.defined(a)||(a=new be),a[0]=e[r],a[1]=e[r+1],a[2]=e[r+2],a[3]=e[r+3],a},be.fromColumnMajorArray=function(e,n){return t.Check.defined("values",e),be.clone(e,n)},be.fromRowMajorArray=function(e,r){return t.Check.defined("values",e),n.defined(r)?(r[0]=e[0],r[1]=e[2],r[2]=e[1],r[3]=e[3],r):new be(e[0],e[1],e[2],e[3])},be.fromScale=function(e,r){return t.Check.typeOf.object("scale",e),n.defined(r)?(r[0]=e.x,r[1]=0,r[2]=0,r[3]=e.y,r):new be(e.x,0,0,e.y)},be.fromUniformScale=function(e,r){return t.Check.typeOf.number("scale",e),n.defined(r)?(r[0]=e,r[1]=0,r[2]=0,r[3]=e,r):new be(e,0,0,e)},be.fromRotation=function(e,r){t.Check.typeOf.number("angle",e);const a=Math.cos(e),o=Math.sin(e);return n.defined(r)?(r[0]=a,r[1]=o,r[2]=-o,r[3]=a,r):new be(a,-o,o,a)},be.toArray=function(e,r){return t.Check.typeOf.object("matrix",e),n.defined(r)?(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r):[e[0],e[1],e[2],e[3]]},be.getElementIndex=function(e,n){return t.Check.typeOf.number.greaterThanOrEquals("row",n,0),t.Check.typeOf.number.lessThanOrEquals("row",n,1),t.Check.typeOf.number.greaterThanOrEquals("column",e,0),t.Check.typeOf.number.lessThanOrEquals("column",e,1),2*e+n},be.getColumn=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,1),t.Check.typeOf.object("result",r);const a=2*n,o=e[a],c=e[a+1];return r.x=o,r.y=c,r},be.setColumn=function(e,n,r,a){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,1),t.Check.typeOf.object("cartesian",r),t.Check.typeOf.object("result",a);const o=2*n;return(a=be.clone(e,a))[o]=r.x,a[o+1]=r.y,a},be.getRow=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,1),t.Check.typeOf.object("result",r);const a=e[n],o=e[n+2];return r.x=a,r.y=o,r},be.setRow=function(e,n,r,a){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.number.greaterThanOrEquals("index",n,0),t.Check.typeOf.number.lessThanOrEquals("index",n,1),t.Check.typeOf.object("cartesian",r),t.Check.typeOf.object("result",a),(a=be.clone(e,a))[n]=r.x,a[n+2]=r.y,a};const ke=new ye;be.getScale=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n.x=ye.magnitude(ye.fromElements(e[0],e[1],ke)),n.y=ye.magnitude(ye.fromElements(e[2],e[3],ke)),n};const xe=new ye;be.getMaximumScale=function(e){return be.getScale(e,xe),ye.maximumComponent(xe)},be.multiply=function(e,n,r){t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r);const a=e[0]*n[0]+e[2]*n[1],o=e[0]*n[2]+e[2]*n[3],c=e[1]*n[0]+e[3]*n[1],i=e[1]*n[2]+e[3]*n[3];return r[0]=a,r[1]=c,r[2]=o,r[3]=i,r},be.add=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r},be.subtract=function(e,n,r){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",n),t.Check.typeOf.object("result",r),r[0]=e[0]-n[0],r[1]=e[1]-n[1],r[2]=e[2]-n[2],r[3]=e[3]-n[3],r},be.multiplyByVector=function(e,n,r){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("cartesian",n),t.Check.typeOf.object("result",r);const a=e[0]*n.x+e[2]*n.y,o=e[1]*n.x+e[3]*n.y;return r.x=a,r.y=o,r},be.multiplyByScalar=function(e,n,r){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.number("scalar",n),t.Check.typeOf.object("result",r),r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r},be.multiplyByScale=function(e,n,r){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("scale",n),t.Check.typeOf.object("result",r),r[0]=e[0]*n.x,r[1]=e[1]*n.x,r[2]=e[2]*n.y,r[3]=e[3]*n.y,r},be.negate=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n},be.transpose=function(e,n){t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n);const r=e[0],a=e[2],o=e[1],c=e[3];return n[0]=r,n[1]=a,n[2]=o,n[3]=c,n},be.abs=function(e,n){return t.Check.typeOf.object("matrix",e),t.Check.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n},be.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]},be.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]},be.equalsEpsilon=function(e,t,r){return r=n.defaultValue(r,0),e===t||n.defined(e)&&n.defined(t)&&Math.abs(e[0]-t[0])<=r&&Math.abs(e[1]-t[1])<=r&&Math.abs(e[2]-t[2])<=r&&Math.abs(e[3]-t[3])<=r},be.IDENTITY=Object.freeze(new be(1,0,0,1)),be.ZERO=Object.freeze(new be(0,0,0,0)),be.COLUMN0ROW0=0,be.COLUMN0ROW1=1,be.COLUMN1ROW0=2,be.COLUMN1ROW1=3,Object.defineProperties(be.prototype,{length:{get:function(){return be.packedLength}}}),be.prototype.clone=function(e){return be.clone(this,e)},be.prototype.equals=function(e){return be.equals(this,e)},be.prototype.equalsEpsilon=function(e,t){return be.equalsEpsilon(this,e,t)},be.prototype.toString=function(){return"("+this[0]+", "+this[2]+")\n("+this[1]+", "+this[3]+")"},e.Cartesian2=ye,e.Cartesian3=a,e.Cartesian4=X,e.Cartographic=m,e.Ellipsoid=w,e.Matrix2=be,e.Matrix3=V,e.Matrix4=K,e.Rectangle=he}));
//# sourceMappingURL=Matrix2-57f130bc.js.map
