/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/24 10:44:35
 * @description extract AbstractSource
 * @version 1.0
 * */
import { destroyObject, PrimitiveCollection, RuntimeError } from 'cesium';
import PluginCollection from './PluginCollection.js';

export default class AbstractSource {
  constructor(name) {
    this._name = name;
    this._pluginCollection = new Map();
    this._primitiveCollection = new PrimitiveCollection();
    this._listener = undefined;
  }

  /**
   * 新增添加方法
   * @param {object} plugin
   * @param {string} plugin.id
   * @param {Entity} plugin.entity
   * @param {object | undefined} plugin.coneFeature
   * @param {object | undefined} plugin.baseRadarFeature
   * @param {object | undefined} plugin.bombFeature
   * @param {object | undefined} plugin.modelFeature
   */
  add(pluginOption) {
    const id = pluginOption.id;
    let plugin = this._pluginCollection.get(id);
    if (plugin) throw new RuntimeError(`${this._name} 数据源下面已经拥有id为 ${pluginOption.id} 的 PluginCollection 实例`);

    const sourceMap = {};
    pluginOption.entity[this.name] = sourceMap;

    // 穿入对象为PluginCollection时，无需创建
    if (pluginOption instanceof PluginCollection) {
      if (pluginOption.owner) {
        if (pluginOption.owner != this) throw new RuntimeError('一个PluginCollection实例只能有一个owner父对象引用，该PluginCollection实例已经拥有');
      } else {
        pluginOption.init(this);
      }

      this._pluginCollection.set(pluginOption.id, pluginOption);
      sourceMap[id] = pluginOption;
      return pluginOption;
    }
    // 传入数据为Object，需要进行创建
    plugin = new PluginCollection(pluginOption, this);
    // plugin.add(pluginOption);

    this._pluginCollection.set(id, plugin);
    sourceMap[id] = plugin;
    return plugin;
  }

  remove(plugin) {
    this._pluginCollection.delete(plugin.id);
  }
  removeById(id) {
    this._pluginCollection.delete(id);
  }

  register(scene) {
    this._scene = scene;
    scene.primitives.add(this._primitiveCollection);
    this._listener = scene.preUpdate.addEventListener((scene, time) => {
      this._pluginCollection.forEach((v) => v.update(time));
    });
  }

  unregister() {
    this._listener();
    this._listener = undefined;
    if (this._scene) {
      this._scene.primitives.remove(this._primitiveCollection);
      delete this._scene;
    }
  }

  get show() {
    return this._primitiveCollection.show;
  }

  set show(value) {
    this._primitiveCollection.show = value;
  }

  get name() {
    return this._name;
  }

  get plugins() {
    return this._pluginCollection;
  }

  isDestroyed() {
    return false;
  }

  destroy() {
    this.unregister();
    this._primitiveCollection.destroy();
    this._pluginCollection.forEach((v) => v.removeAll());
    return destroyObject(this);
  }
}
