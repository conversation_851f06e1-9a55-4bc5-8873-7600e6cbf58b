/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/07/29 15:48:02
 * @description AbstractVisualizer copy from AbstractPrimitive
 * @version 1.0
 * */

import { defined, createGuid, defaultValue, destroyObject } from 'cesium';
export default class AbstractPrimitive {
  constructor(options) {
    this.show = defaultValue(options.show, true);
    this._id = defined(options.id) ? options.id : createGuid();
    this._primitive3D = undefined;
    this._primitive2D = undefined;
    this._update = true;
  }

  update(frameState) {}

  isDestroyed() {
    return false;
  }

  destroy() {
    this._primitive3D = this._primitive3D && this._primitive3D.destroy();
    this._primitive2D = this._primitive2D && this._primitive2D.destroy();
    return destroyObject(this);
  }

  get id() {
    return this._id;
  }

  set id(value) {
    this._id = value;
  }
}
