<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : dropdown.vue
-->
<template>
  <n-dropdown v-bind="$attrs" :show="isShow" trigger="click">
    <div ref="handleRef" class="sim-dropdown__handle" @click="changeState">
      <slot />
    </div>
  </n-dropdown>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SimDropdown'
});
import { useDropdownHook } from '@hooks/use-dropdown-hook';

const { isShow, changeState, handleRef } = useDropdownHook();
</script>

<style scoped lang="less">
.sim-dropdown__handle {
  display: inline-block;
}
</style>
