/**
 * @Author: song<PERSON>min
 * @Date: 2022-04-06 12:21:02
 * @Version: 0.0.1
 * @Content:
 */

import type { ToRefs } from 'vue';
import { InitialState, DraggableProps } from './draggable-hook';

export interface UpdatePosTypes {
  pos: number;
  dragState: ToRefs<InitialState>;
  props?: DraggableProps;
  opt?: {
    min: number;
    max: number;
  };
}

// 更新x轴位置
export function updateLeft(options: UpdatePosTypes) {
  const { pos, dragState, opt, props } = options;
  // 允许超出范围
  if (props?.allowOut) {
    dragState.posX.value = pos;
    return;
  }
  if (opt && pos > opt.max) {
    dragState.posX.value = opt.max;
    return;
  }
  if (opt && pos < opt.min) {
    dragState.posX.value = opt.min;
    return;
  }
  // if (props.leftRestrict) {
  //     const _left = props.leftRestrict(pos);
  //     dragState.posX.value = _left;
  //     return;
  // }
  dragState.posX.value = pos;
}

// 更新Y轴位置
export function updateTop(options: UpdatePosTypes) {
  const { pos, dragState, opt, props } = options;
  // 允许超出范围
  if (props?.allowOut) {
    dragState.posY.value = pos;
    return;
  }
  // 切换为全屏模式 修改位置 再切换为初始状态
  if (opt && pos > opt.max) {
    dragState.posY.value = opt.max;
    return;
  }
  if (opt && pos < opt.min) {
    dragState.posY.value = opt.min;
    return;
  }
  // if (props.topRestrict) {
  //     const _top = props.topRestrict(pos);
  //     dragState.posY.value = _top;
  //     return;
  // }
  dragState.posY.value = pos;
}

// 更新宽度
export function updateWidth(options: UpdatePosTypes) {
  const { pos, dragState, props } = options;
  if (props?.minWidth && pos <= props.minWidth) {
    return;
  }
  dragState.width.value = pos;
  // emit("update:w", width);
}

// 更新高度
export function updateHeight(options: UpdatePosTypes) {
  const { pos, dragState, props } = options;
  console.log(pos, props?.minHeight);
  if (props?.minHeight && pos <= props.minHeight) {
    return;
  }
  dragState.height.value = pos;
  // emit("update:h", height);
}

// 根据单位处理数据 考虑 % px
export function handleDataByUnit(data: string | number): number {
  if (typeof data === 'string') {
    if (data.includes('%')) {
      return parseFloat(data.replace('%', '')) / 100;
    }
    if (data.includes('px')) {
      return parseFloat(data.replace('px', ''));
    }
    return parseFloat(data);
  }
  return data;
}

export function joinUnitForNumber(data: string | number) {
  if (typeof data === 'string' && !/[px|%]$/.test(data)) {
    return `${data}px`;
  }
  if (typeof data === 'number') {
    return `${data}px`;
  }
  return data;
}
