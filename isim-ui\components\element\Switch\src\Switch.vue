<!--
* <AUTHOR> 崔晓东
* @Date :  2023/4/7
* @Version : 1.0
* @Content : SimSwitch
-->
<template>
  <n-switch class="sim-switch" v-bind="{ ...props, ...$attrs }">
    <template v-for="(value, name) in $slots" #[name]>
      <slot :name="name"></slot>
    </template>
  </n-switch>
</template>

<script lang="ts" setup>
import { switchProps } from 'element-plus';
defineOptions({
  name: 'SimSwitch'
});

const props = defineProps(switchProps);
</script>
<style lang="less"></style>
