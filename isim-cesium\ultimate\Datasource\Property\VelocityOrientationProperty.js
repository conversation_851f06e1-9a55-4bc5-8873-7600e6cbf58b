/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/07/20 16:11:24
 * @description  update and add Inertial Quaternion calculate Method
 * */
import {
  Cartesian3,
  defaultValue,
  defined,
  Ellipsoid,
  Event,
  JulianDate,
  Matrix3,
  Quaternion,
  Transforms,
  Property,
  ExtrapolationType
} from 'cesium';
import VelocityVectorProperty from './VelocityVectorProperty.js';

function VelocityOrientationProperty(position, ellipsoid) {
  this._velocityVectorProperty = new VelocityVectorProperty(position, true);
  this._subscription = undefined;
  this._ellipsoid = undefined;
  this._definitionChanged = new Event();

  this._forwardExtrapolationType = ExtrapolationType.NONE;
  this._velocityVectorProperty.forwardExtrapolationType = this._forwardExtrapolationType;

  this.ellipsoid = defaultValue(ellipsoid, Ellipsoid.WGS84);

  let that = this;
  this._velocityVectorProperty.definitionChanged.addEventListener(function () {
    that._definitionChanged.raiseEvent(that);
  });
}

Object.defineProperties(VelocityOrientationProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._velocityVectorProperty);
    }
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },
  position: {
    get: function () {
      return this._velocityVectorProperty.position;
    },
    set: function (value) {
      this._velocityVectorProperty.position = value;
    }
  },
  ellipsoid: {
    get: function () {
      return this._ellipsoid;
    },
    set: function (value) {
      let oldValue = this._ellipsoid;
      if (oldValue !== value) {
        this._ellipsoid = value;
        this._definitionChanged.raiseEvent(this);
      }
    }
  },
  /**
   * Gets or sets the type of extrapolation to perform when a value
   * is requested at a time before any available samples.
   * @memberof VelocityOrientationProperty.prototype
   * @type {ExtrapolationType}
   * @default ExtrapolationType.NONE
   */
  forwardExtrapolationType: {
    get: function () {
      return this.forwardExtrapolationType;
    },
    set: function (value) {
      this._forwardExtrapolationType = value;
      this._velocityVectorProperty.forwardExtrapolationType = this._forwardExtrapolationType;
    }
  }
});

let positionScratch = new Cartesian3();
let velocityScratch = new Cartesian3();
let rotationScratch = new Matrix3();

/**
 * modify getValueMethod
 * @returns
 */
VelocityOrientationProperty.prototype.getValue = function (time, result) {
  time = this.getLastTime(time);
  if (this._forwardExtrapolationType === ExtrapolationType.HOLD) {
    velocityScratch = new Cartesian3();
  }
  let velocity = this._velocityVectorProperty._getValue(time, velocityScratch, positionScratch);
  if (!defined(velocity)) {
    return undefined;
  }
  Transforms.rotationMatrixFromPositionVelocity(positionScratch, velocity, this._ellipsoid, rotationScratch);
  return Quaternion.fromRotationMatrix(rotationScratch, result);
};

let timeScratch = new JulianDate();
VelocityOrientationProperty.prototype.getLastTime = function (time) {
  if (!this.position?._property?._times) {
    console.warn('Property is' + this.position?._property);
    return;
  }
  let _times = this.position._property._times;
  if (_times?.length < 2) {
    return time;
  }
  let lastTime = _times[_times.length - 1];
  if (JulianDate.greaterThan(time, lastTime)) {
    return JulianDate.addSeconds(lastTime, -1 / 30, timeScratch);
  }
  return time;
};

VelocityOrientationProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof VelocityOrientationProperty &&
      Property.equals(this._velocityVectorProperty, other._velocityVectorProperty) &&
      (this._ellipsoid === other._ellipsoid || this._ellipsoid.equals(other._ellipsoid)))
  );
};

export default VelocityOrientationProperty;
