import { onMounted, ref } from 'vue';

/**
 * 绑定滚动事件
 * 由于只有右边出现滚动条
 * 所以需要对左边的菜单和右边的任务条进行同步的滚动联动
 */
export function useScrollEventBind() {
  const leftContent = ref();
  const rightContent = ref();
  const leftScrollEvent = () => {
    leftContent.value.onmouseenter = () => {
      rightContent.value.onscroll = null;
      leftContent.value.onscroll = (e: any) => {
        rightContent.value.scrollTop = e.target.scrollTop;
      };
    };
  };
  const rightScrollEvent = () => {
    rightContent.value.onmouseenter = () => {
      leftContent.value.onscroll = null;
      rightContent.value.onscroll = (e: any) => {
        leftContent.value.scrollTop = e.target.scrollTop;
      };
    };
  };
  onMounted(() => {
    leftScrollEvent();
    rightScrollEvent();
  });
  return {
    leftContent,
    rightContent
  };
}
