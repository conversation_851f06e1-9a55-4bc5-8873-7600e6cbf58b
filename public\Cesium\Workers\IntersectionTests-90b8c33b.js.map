{"version": 3, "file": "IntersectionTests-90b8c33b.js", "sources": ["../../../../Source/Core/QuadraticRealPolynomial.js", "../../../../Source/Core/CubicRealPolynomial.js", "../../../../Source/Core/QuarticRealPolynomial.js", "../../../../Source/Core/Ray.js", "../../../../Source/Core/IntersectionTests.js"], "sourcesContent": ["import DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\n\n/**\n * Defines functions for 2nd order polynomial functions of one variable with only real coefficients.\n *\n * @namespace QuadraticRealPolynomial\n */\nconst QuadraticRealPolynomial = {};\n\n/**\n * Provides the discriminant of the quadratic equation from the supplied coefficients.\n *\n * @param {Number} a The coefficient of the 2nd order monomial.\n * @param {Number} b The coefficient of the 1st order monomial.\n * @param {Number} c The coefficient of the 0th order monomial.\n * @returns {Number} The value of the discriminant.\n */\nQuadraticRealPolynomial.computeDiscriminant = function (a, b, c) {\n  //>>includeStart('debug', pragmas.debug);\n  if (typeof a !== \"number\") {\n    throw new DeveloperError(\"a is a required number.\");\n  }\n  if (typeof b !== \"number\") {\n    throw new DeveloperError(\"b is a required number.\");\n  }\n  if (typeof c !== \"number\") {\n    throw new DeveloperError(\"c is a required number.\");\n  }\n  //>>includeEnd('debug');\n\n  const discriminant = b * b - 4.0 * a * c;\n  return discriminant;\n};\n\nfunction addWithCancellationCheck(left, right, tolerance) {\n  const difference = left + right;\n  if (\n    CesiumMath.sign(left) !== CesiumMath.sign(right) &&\n    Math.abs(difference / Math.max(Math.abs(left), Math.abs(right))) < tolerance\n  ) {\n    return 0.0;\n  }\n\n  return difference;\n}\n\n/**\n * Provides the real valued roots of the quadratic polynomial with the provided coefficients.\n *\n * @param {Number} a The coefficient of the 2nd order monomial.\n * @param {Number} b The coefficient of the 1st order monomial.\n * @param {Number} c The coefficient of the 0th order monomial.\n * @returns {Number[]} The real valued roots.\n */\nQuadraticRealPolynomial.computeRealRoots = function (a, b, c) {\n  //>>includeStart('debug', pragmas.debug);\n  if (typeof a !== \"number\") {\n    throw new DeveloperError(\"a is a required number.\");\n  }\n  if (typeof b !== \"number\") {\n    throw new DeveloperError(\"b is a required number.\");\n  }\n  if (typeof c !== \"number\") {\n    throw new DeveloperError(\"c is a required number.\");\n  }\n  //>>includeEnd('debug');\n\n  let ratio;\n  if (a === 0.0) {\n    if (b === 0.0) {\n      // Constant function: c = 0.\n      return [];\n    }\n\n    // Linear function: b * x + c = 0.\n    return [-c / b];\n  } else if (b === 0.0) {\n    if (c === 0.0) {\n      // 2nd order monomial: a * x^2 = 0.\n      return [0.0, 0.0];\n    }\n\n    const cMagnitude = Math.abs(c);\n    const aMagnitude = Math.abs(a);\n\n    if (\n      cMagnitude < aMagnitude &&\n      cMagnitude / aMagnitude < CesiumMath.EPSILON14\n    ) {\n      // c ~= 0.0.\n      // 2nd order monomial: a * x^2 = 0.\n      return [0.0, 0.0];\n    } else if (\n      cMagnitude > aMagnitude &&\n      aMagnitude / cMagnitude < CesiumMath.EPSILON14\n    ) {\n      // a ~= 0.0.\n      // Constant function: c = 0.\n      return [];\n    }\n\n    // a * x^2 + c = 0\n    ratio = -c / a;\n\n    if (ratio < 0.0) {\n      // Both roots are complex.\n      return [];\n    }\n\n    // Both roots are real.\n    const root = Math.sqrt(ratio);\n    return [-root, root];\n  } else if (c === 0.0) {\n    // a * x^2 + b * x = 0\n    ratio = -b / a;\n    if (ratio < 0.0) {\n      return [ratio, 0.0];\n    }\n\n    return [0.0, ratio];\n  }\n\n  // a * x^2 + b * x + c = 0\n  const b2 = b * b;\n  const four_ac = 4.0 * a * c;\n  const radicand = addWithCancellationCheck(b2, -four_ac, CesiumMath.EPSILON14);\n\n  if (radicand < 0.0) {\n    // Both roots are complex.\n    return [];\n  }\n\n  const q =\n    -0.5 *\n    addWithCancellationCheck(\n      b,\n      CesiumMath.sign(b) * Math.sqrt(radicand),\n      CesiumMath.EPSILON14\n    );\n  if (b > 0.0) {\n    return [q / a, c / q];\n  }\n\n  return [c / q, q / a];\n};\nexport default QuadraticRealPolynomial;\n", "import DeveloperError from \"./DeveloperError.js\";\nimport QuadraticRealPolynomial from \"./QuadraticRealPolynomial.js\";\n\n/**\n * Defines functions for 3rd order polynomial functions of one variable with only real coefficients.\n *\n * @namespace CubicRealPolynomial\n */\nconst CubicRealPolynomial = {};\n\n/**\n * Provides the discriminant of the cubic equation from the supplied coefficients.\n *\n * @param {Number} a The coefficient of the 3rd order monomial.\n * @param {Number} b The coefficient of the 2nd order monomial.\n * @param {Number} c The coefficient of the 1st order monomial.\n * @param {Number} d The coefficient of the 0th order monomial.\n * @returns {Number} The value of the discriminant.\n */\nCubicRealPolynomial.computeDiscriminant = function (a, b, c, d) {\n  //>>includeStart('debug', pragmas.debug);\n  if (typeof a !== \"number\") {\n    throw new DeveloperError(\"a is a required number.\");\n  }\n  if (typeof b !== \"number\") {\n    throw new DeveloperError(\"b is a required number.\");\n  }\n  if (typeof c !== \"number\") {\n    throw new DeveloperError(\"c is a required number.\");\n  }\n  if (typeof d !== \"number\") {\n    throw new DeveloperError(\"d is a required number.\");\n  }\n  //>>includeEnd('debug');\n\n  const a2 = a * a;\n  const b2 = b * b;\n  const c2 = c * c;\n  const d2 = d * d;\n\n  const discriminant =\n    18.0 * a * b * c * d +\n    b2 * c2 -\n    27.0 * a2 * d2 -\n    4.0 * (a * c2 * c + b2 * b * d);\n  return discriminant;\n};\n\nfunction computeRealRoots(a, b, c, d) {\n  const A = a;\n  const B = b / 3.0;\n  const C = c / 3.0;\n  const D = d;\n\n  const AC = A * C;\n  const BD = B * D;\n  const B2 = B * B;\n  const C2 = C * C;\n  const delta1 = A * C - B2;\n  const delta2 = A * D - B * C;\n  const delta3 = B * D - C2;\n\n  const discriminant = 4.0 * delta1 * delta3 - delta2 * delta2;\n  let temp;\n  let temp1;\n\n  if (discriminant < 0.0) {\n    let ABar;\n    let CBar;\n    let DBar;\n\n    if (B2 * BD >= AC * C2) {\n      ABar = A;\n      CBar = delta1;\n      DBar = -2.0 * B * delta1 + A * delta2;\n    } else {\n      ABar = D;\n      CBar = delta3;\n      DBar = -D * delta2 + 2.0 * C * delta3;\n    }\n\n    const s = DBar < 0.0 ? -1.0 : 1.0; // This is not Math.Sign()!\n    const temp0 = -s * Math.abs(ABar) * Math.sqrt(-discriminant);\n    temp1 = -DBar + temp0;\n\n    const x = temp1 / 2.0;\n    const p = x < 0.0 ? -Math.pow(-x, 1.0 / 3.0) : Math.pow(x, 1.0 / 3.0);\n    const q = temp1 === temp0 ? -p : -CBar / p;\n\n    temp = CBar <= 0.0 ? p + q : -DBar / (p * p + q * q + CBar);\n\n    if (B2 * BD >= AC * C2) {\n      return [(temp - B) / A];\n    }\n\n    return [-D / (temp + C)];\n  }\n\n  const CBarA = delta1;\n  const DBarA = -2.0 * B * delta1 + A * delta2;\n\n  const CBarD = delta3;\n  const DBarD = -D * delta2 + 2.0 * C * delta3;\n\n  const squareRootOfDiscriminant = Math.sqrt(discriminant);\n  const halfSquareRootOf3 = Math.sqrt(3.0) / 2.0;\n\n  let theta = Math.abs(Math.atan2(A * squareRootOfDiscriminant, -DBarA) / 3.0);\n  temp = 2.0 * Math.sqrt(-CBarA);\n  let cosine = Math.cos(theta);\n  temp1 = temp * cosine;\n  let temp3 = temp * (-cosine / 2.0 - halfSquareRootOf3 * Math.sin(theta));\n\n  const numeratorLarge = temp1 + temp3 > 2.0 * B ? temp1 - B : temp3 - B;\n  const denominatorLarge = A;\n\n  const root1 = numeratorLarge / denominatorLarge;\n\n  theta = Math.abs(Math.atan2(D * squareRootOfDiscriminant, -DBarD) / 3.0);\n  temp = 2.0 * Math.sqrt(-CBarD);\n  cosine = Math.cos(theta);\n  temp1 = temp * cosine;\n  temp3 = temp * (-cosine / 2.0 - halfSquareRootOf3 * Math.sin(theta));\n\n  const numeratorSmall = -D;\n  const denominatorSmall = temp1 + temp3 < 2.0 * C ? temp1 + C : temp3 + C;\n\n  const root3 = numeratorSmall / denominatorSmall;\n\n  const E = denominatorLarge * denominatorSmall;\n  const F =\n    -numeratorLarge * denominatorSmall - denominatorLarge * numeratorSmall;\n  const G = numeratorLarge * numeratorSmall;\n\n  const root2 = (C * F - B * G) / (-B * F + C * E);\n\n  if (root1 <= root2) {\n    if (root1 <= root3) {\n      if (root2 <= root3) {\n        return [root1, root2, root3];\n      }\n      return [root1, root3, root2];\n    }\n    return [root3, root1, root2];\n  }\n  if (root1 <= root3) {\n    return [root2, root1, root3];\n  }\n  if (root2 <= root3) {\n    return [root2, root3, root1];\n  }\n  return [root3, root2, root1];\n}\n\n/**\n * Provides the real valued roots of the cubic polynomial with the provided coefficients.\n *\n * @param {Number} a The coefficient of the 3rd order monomial.\n * @param {Number} b The coefficient of the 2nd order monomial.\n * @param {Number} c The coefficient of the 1st order monomial.\n * @param {Number} d The coefficient of the 0th order monomial.\n * @returns {Number[]} The real valued roots.\n */\nCubicRealPolynomial.computeRealRoots = function (a, b, c, d) {\n  //>>includeStart('debug', pragmas.debug);\n  if (typeof a !== \"number\") {\n    throw new DeveloperError(\"a is a required number.\");\n  }\n  if (typeof b !== \"number\") {\n    throw new DeveloperError(\"b is a required number.\");\n  }\n  if (typeof c !== \"number\") {\n    throw new DeveloperError(\"c is a required number.\");\n  }\n  if (typeof d !== \"number\") {\n    throw new DeveloperError(\"d is a required number.\");\n  }\n  //>>includeEnd('debug');\n\n  let roots;\n  let ratio;\n  if (a === 0.0) {\n    // Quadratic function: b * x^2 + c * x + d = 0.\n    return QuadraticRealPolynomial.computeRealRoots(b, c, d);\n  } else if (b === 0.0) {\n    if (c === 0.0) {\n      if (d === 0.0) {\n        // 3rd order monomial: a * x^3 = 0.\n        return [0.0, 0.0, 0.0];\n      }\n\n      // a * x^3 + d = 0\n      ratio = -d / a;\n      const root =\n        ratio < 0.0 ? -Math.pow(-ratio, 1.0 / 3.0) : Math.pow(ratio, 1.0 / 3.0);\n      return [root, root, root];\n    } else if (d === 0.0) {\n      // x * (a * x^2 + c) = 0.\n      roots = QuadraticRealPolynomial.computeRealRoots(a, 0, c);\n\n      // Return the roots in ascending order.\n      if (roots.Length === 0) {\n        return [0.0];\n      }\n      return [roots[0], 0.0, roots[1]];\n    }\n\n    // Deflated cubic polynomial: a * x^3 + c * x + d= 0.\n    return computeRealRoots(a, 0, c, d);\n  } else if (c === 0.0) {\n    if (d === 0.0) {\n      // x^2 * (a * x + b) = 0.\n      ratio = -b / a;\n      if (ratio < 0.0) {\n        return [ratio, 0.0, 0.0];\n      }\n      return [0.0, 0.0, ratio];\n    }\n    // a * x^3 + b * x^2 + d = 0.\n    return computeRealRoots(a, b, 0, d);\n  } else if (d === 0.0) {\n    // x * (a * x^2 + b * x + c) = 0\n    roots = QuadraticRealPolynomial.computeRealRoots(a, b, c);\n\n    // Return the roots in ascending order.\n    if (roots.length === 0) {\n      return [0.0];\n    } else if (roots[1] <= 0.0) {\n      return [roots[0], roots[1], 0.0];\n    } else if (roots[0] >= 0.0) {\n      return [0.0, roots[0], roots[1]];\n    }\n    return [roots[0], 0.0, roots[1]];\n  }\n\n  return computeRealRoots(a, b, c, d);\n};\nexport default CubicRealPolynomial;\n", "import CubicRealPolynomial from \"./CubicRealPolynomial.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport CesiumMath from \"./Math.js\";\nimport QuadraticRealPolynomial from \"./QuadraticRealPolynomial.js\";\n\n/**\n * Defines functions for 4th order polynomial functions of one variable with only real coefficients.\n *\n * @namespace QuarticRealPolynomial\n */\nconst QuarticRealPolynomial = {};\n\n/**\n * Provides the discriminant of the quartic equation from the supplied coefficients.\n *\n * @param {Number} a The coefficient of the 4th order monomial.\n * @param {Number} b The coefficient of the 3rd order monomial.\n * @param {Number} c The coefficient of the 2nd order monomial.\n * @param {Number} d The coefficient of the 1st order monomial.\n * @param {Number} e The coefficient of the 0th order monomial.\n * @returns {Number} The value of the discriminant.\n */\nQuarticRealPolynomial.computeDiscriminant = function (a, b, c, d, e) {\n  //>>includeStart('debug', pragmas.debug);\n  if (typeof a !== \"number\") {\n    throw new DeveloperError(\"a is a required number.\");\n  }\n  if (typeof b !== \"number\") {\n    throw new DeveloperError(\"b is a required number.\");\n  }\n  if (typeof c !== \"number\") {\n    throw new DeveloperError(\"c is a required number.\");\n  }\n  if (typeof d !== \"number\") {\n    throw new DeveloperError(\"d is a required number.\");\n  }\n  if (typeof e !== \"number\") {\n    throw new DeveloperError(\"e is a required number.\");\n  }\n  //>>includeEnd('debug');\n\n  const a2 = a * a;\n  const a3 = a2 * a;\n  const b2 = b * b;\n  const b3 = b2 * b;\n  const c2 = c * c;\n  const c3 = c2 * c;\n  const d2 = d * d;\n  const d3 = d2 * d;\n  const e2 = e * e;\n  const e3 = e2 * e;\n\n  const discriminant =\n    b2 * c2 * d2 -\n    4.0 * b3 * d3 -\n    4.0 * a * c3 * d2 +\n    18 * a * b * c * d3 -\n    27.0 * a2 * d2 * d2 +\n    256.0 * a3 * e3 +\n    e *\n      (18.0 * b3 * c * d -\n        4.0 * b2 * c3 +\n        16.0 * a * c2 * c2 -\n        80.0 * a * b * c2 * d -\n        6.0 * a * b2 * d2 +\n        144.0 * a2 * c * d2) +\n    e2 *\n      (144.0 * a * b2 * c -\n        27.0 * b2 * b2 -\n        128.0 * a2 * c2 -\n        192.0 * a2 * b * d);\n  return discriminant;\n};\n\nfunction original(a3, a2, a1, a0) {\n  const a3Squared = a3 * a3;\n\n  const p = a2 - (3.0 * a3Squared) / 8.0;\n  const q = a1 - (a2 * a3) / 2.0 + (a3Squared * a3) / 8.0;\n  const r =\n    a0 -\n    (a1 * a3) / 4.0 +\n    (a2 * a3Squared) / 16.0 -\n    (3.0 * a3Squared * a3Squared) / 256.0;\n\n  // Find the roots of the cubic equations:  h^6 + 2 p h^4 + (p^2 - 4 r) h^2 - q^2 = 0.\n  const cubicRoots = CubicRealPolynomial.computeRealRoots(\n    1.0,\n    2.0 * p,\n    p * p - 4.0 * r,\n    -q * q\n  );\n\n  if (cubicRoots.length > 0) {\n    const temp = -a3 / 4.0;\n\n    // Use the largest positive root.\n    const hSquared = cubicRoots[cubicRoots.length - 1];\n\n    if (Math.abs(hSquared) < CesiumMath.EPSILON14) {\n      // y^4 + p y^2 + r = 0.\n      const roots = QuadraticRealPolynomial.computeRealRoots(1.0, p, r);\n\n      if (roots.length === 2) {\n        const root0 = roots[0];\n        const root1 = roots[1];\n\n        let y;\n        if (root0 >= 0.0 && root1 >= 0.0) {\n          const y0 = Math.sqrt(root0);\n          const y1 = Math.sqrt(root1);\n\n          return [temp - y1, temp - y0, temp + y0, temp + y1];\n        } else if (root0 >= 0.0 && root1 < 0.0) {\n          y = Math.sqrt(root0);\n          return [temp - y, temp + y];\n        } else if (root0 < 0.0 && root1 >= 0.0) {\n          y = Math.sqrt(root1);\n          return [temp - y, temp + y];\n        }\n      }\n      return [];\n    } else if (hSquared > 0.0) {\n      const h = Math.sqrt(hSquared);\n\n      const m = (p + hSquared - q / h) / 2.0;\n      const n = (p + hSquared + q / h) / 2.0;\n\n      // Now solve the two quadratic factors:  (y^2 + h y + m)(y^2 - h y + n);\n      const roots1 = QuadraticRealPolynomial.computeRealRoots(1.0, h, m);\n      const roots2 = QuadraticRealPolynomial.computeRealRoots(1.0, -h, n);\n\n      if (roots1.length !== 0) {\n        roots1[0] += temp;\n        roots1[1] += temp;\n\n        if (roots2.length !== 0) {\n          roots2[0] += temp;\n          roots2[1] += temp;\n\n          if (roots1[1] <= roots2[0]) {\n            return [roots1[0], roots1[1], roots2[0], roots2[1]];\n          } else if (roots2[1] <= roots1[0]) {\n            return [roots2[0], roots2[1], roots1[0], roots1[1]];\n          } else if (roots1[0] >= roots2[0] && roots1[1] <= roots2[1]) {\n            return [roots2[0], roots1[0], roots1[1], roots2[1]];\n          } else if (roots2[0] >= roots1[0] && roots2[1] <= roots1[1]) {\n            return [roots1[0], roots2[0], roots2[1], roots1[1]];\n          } else if (roots1[0] > roots2[0] && roots1[0] < roots2[1]) {\n            return [roots2[0], roots1[0], roots2[1], roots1[1]];\n          }\n          return [roots1[0], roots2[0], roots1[1], roots2[1]];\n        }\n        return roots1;\n      }\n\n      if (roots2.length !== 0) {\n        roots2[0] += temp;\n        roots2[1] += temp;\n\n        return roots2;\n      }\n      return [];\n    }\n  }\n  return [];\n}\n\nfunction neumark(a3, a2, a1, a0) {\n  const a1Squared = a1 * a1;\n  const a2Squared = a2 * a2;\n  const a3Squared = a3 * a3;\n\n  const p = -2.0 * a2;\n  const q = a1 * a3 + a2Squared - 4.0 * a0;\n  const r = a3Squared * a0 - a1 * a2 * a3 + a1Squared;\n\n  const cubicRoots = CubicRealPolynomial.computeRealRoots(1.0, p, q, r);\n\n  if (cubicRoots.length > 0) {\n    // Use the most positive root\n    const y = cubicRoots[0];\n\n    const temp = a2 - y;\n    const tempSquared = temp * temp;\n\n    const g1 = a3 / 2.0;\n    const h1 = temp / 2.0;\n\n    const m = tempSquared - 4.0 * a0;\n    const mError = tempSquared + 4.0 * Math.abs(a0);\n\n    const n = a3Squared - 4.0 * y;\n    const nError = a3Squared + 4.0 * Math.abs(y);\n\n    let g2;\n    let h2;\n\n    if (y < 0.0 || m * nError < n * mError) {\n      const squareRootOfN = Math.sqrt(n);\n      g2 = squareRootOfN / 2.0;\n      h2 = squareRootOfN === 0.0 ? 0.0 : (a3 * h1 - a1) / squareRootOfN;\n    } else {\n      const squareRootOfM = Math.sqrt(m);\n      g2 = squareRootOfM === 0.0 ? 0.0 : (a3 * h1 - a1) / squareRootOfM;\n      h2 = squareRootOfM / 2.0;\n    }\n\n    let G;\n    let g;\n    if (g1 === 0.0 && g2 === 0.0) {\n      G = 0.0;\n      g = 0.0;\n    } else if (CesiumMath.sign(g1) === CesiumMath.sign(g2)) {\n      G = g1 + g2;\n      g = y / G;\n    } else {\n      g = g1 - g2;\n      G = y / g;\n    }\n\n    let H;\n    let h;\n    if (h1 === 0.0 && h2 === 0.0) {\n      H = 0.0;\n      h = 0.0;\n    } else if (CesiumMath.sign(h1) === CesiumMath.sign(h2)) {\n      H = h1 + h2;\n      h = a0 / H;\n    } else {\n      h = h1 - h2;\n      H = a0 / h;\n    }\n\n    // Now solve the two quadratic factors:  (y^2 + G y + H)(y^2 + g y + h);\n    const roots1 = QuadraticRealPolynomial.computeRealRoots(1.0, G, H);\n    const roots2 = QuadraticRealPolynomial.computeRealRoots(1.0, g, h);\n\n    if (roots1.length !== 0) {\n      if (roots2.length !== 0) {\n        if (roots1[1] <= roots2[0]) {\n          return [roots1[0], roots1[1], roots2[0], roots2[1]];\n        } else if (roots2[1] <= roots1[0]) {\n          return [roots2[0], roots2[1], roots1[0], roots1[1]];\n        } else if (roots1[0] >= roots2[0] && roots1[1] <= roots2[1]) {\n          return [roots2[0], roots1[0], roots1[1], roots2[1]];\n        } else if (roots2[0] >= roots1[0] && roots2[1] <= roots1[1]) {\n          return [roots1[0], roots2[0], roots2[1], roots1[1]];\n        } else if (roots1[0] > roots2[0] && roots1[0] < roots2[1]) {\n          return [roots2[0], roots1[0], roots2[1], roots1[1]];\n        }\n        return [roots1[0], roots2[0], roots1[1], roots2[1]];\n      }\n      return roots1;\n    }\n    if (roots2.length !== 0) {\n      return roots2;\n    }\n  }\n  return [];\n}\n\n/**\n * Provides the real valued roots of the quartic polynomial with the provided coefficients.\n *\n * @param {Number} a The coefficient of the 4th order monomial.\n * @param {Number} b The coefficient of the 3rd order monomial.\n * @param {Number} c The coefficient of the 2nd order monomial.\n * @param {Number} d The coefficient of the 1st order monomial.\n * @param {Number} e The coefficient of the 0th order monomial.\n * @returns {Number[]} The real valued roots.\n */\nQuarticRealPolynomial.computeRealRoots = function (a, b, c, d, e) {\n  //>>includeStart('debug', pragmas.debug);\n  if (typeof a !== \"number\") {\n    throw new DeveloperError(\"a is a required number.\");\n  }\n  if (typeof b !== \"number\") {\n    throw new DeveloperError(\"b is a required number.\");\n  }\n  if (typeof c !== \"number\") {\n    throw new DeveloperError(\"c is a required number.\");\n  }\n  if (typeof d !== \"number\") {\n    throw new DeveloperError(\"d is a required number.\");\n  }\n  if (typeof e !== \"number\") {\n    throw new DeveloperError(\"e is a required number.\");\n  }\n  //>>includeEnd('debug');\n\n  if (Math.abs(a) < CesiumMath.EPSILON15) {\n    return CubicRealPolynomial.computeRealRoots(b, c, d, e);\n  }\n  const a3 = b / a;\n  const a2 = c / a;\n  const a1 = d / a;\n  const a0 = e / a;\n\n  let k = a3 < 0.0 ? 1 : 0;\n  k += a2 < 0.0 ? k + 1 : k;\n  k += a1 < 0.0 ? k + 1 : k;\n  k += a0 < 0.0 ? k + 1 : k;\n\n  switch (k) {\n    case 0:\n      return original(a3, a2, a1, a0);\n    case 1:\n      return neumark(a3, a2, a1, a0);\n    case 2:\n      return neumark(a3, a2, a1, a0);\n    case 3:\n      return original(a3, a2, a1, a0);\n    case 4:\n      return original(a3, a2, a1, a0);\n    case 5:\n      return neumark(a3, a2, a1, a0);\n    case 6:\n      return original(a3, a2, a1, a0);\n    case 7:\n      return original(a3, a2, a1, a0);\n    case 8:\n      return neumark(a3, a2, a1, a0);\n    case 9:\n      return original(a3, a2, a1, a0);\n    case 10:\n      return original(a3, a2, a1, a0);\n    case 11:\n      return neumark(a3, a2, a1, a0);\n    case 12:\n      return original(a3, a2, a1, a0);\n    case 13:\n      return original(a3, a2, a1, a0);\n    case 14:\n      return original(a3, a2, a1, a0);\n    case 15:\n      return original(a3, a2, a1, a0);\n    default:\n      return undefined;\n  }\n};\nexport default QuarticRealPolynomial;\n", "import Cartesian3 from \"./Cartesian3.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\n\n/**\n * Represents a ray that extends infinitely from the provided origin in the provided direction.\n * @alias Ray\n * @constructor\n *\n * @param {Cartesian3} [origin=Cartesian3.ZERO] The origin of the ray.\n * @param {Cartesian3} [direction=Cartesian3.ZERO] The direction of the ray.\n */\nfunction Ray(origin, direction) {\n  direction = Cartesian3.clone(defaultValue(direction, Cartesian3.ZERO));\n  if (!Cartesian3.equals(direction, Cartesian3.ZERO)) {\n    Cartesian3.normalize(direction, direction);\n  }\n\n  /**\n   * The origin of the ray.\n   * @type {Cartesian3}\n   * @default {@link Cartesian3.ZERO}\n   */\n  this.origin = Cartesian3.clone(defaultValue(origin, Cartesian3.ZERO));\n\n  /**\n   * The direction of the ray.\n   * @type {Cartesian3}\n   */\n  this.direction = direction;\n}\n\n/**\n * Duplicates a Ray instance.\n *\n * @param {Ray} ray The ray to duplicate.\n * @param {Ray} [result] The object onto which to store the result.\n * @returns {Ray} The modified result parameter or a new Ray instance if one was not provided. (Returns undefined if ray is undefined)\n */\nRay.clone = function (ray, result) {\n  if (!defined(ray)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    return new Ray(ray.origin, ray.direction);\n  }\n  result.origin = Cartesian3.clone(ray.origin);\n  result.direction = Cartesian3.clone(ray.direction);\n  return result;\n};\n\n/**\n * Computes the point along the ray given by r(t) = o + t*d,\n * where o is the origin of the ray and d is the direction.\n *\n * @param {Ray} ray The ray.\n * @param {Number} t A scalar value.\n * @param {Cartesian3} [result] The object in which the result will be stored.\n * @returns {Cartesian3} The modified result parameter, or a new instance if none was provided.\n *\n * @example\n * //Get the first intersection point of a ray and an ellipsoid.\n * const intersection = Cesium.IntersectionTests.rayEllipsoid(ray, ellipsoid);\n * const point = Cesium.Ray.getPoint(ray, intersection.start);\n */\nRay.getPoint = function (ray, t, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"ray\", ray);\n  Check.typeOf.number(\"t\", t);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  result = Cartesian3.multiplyByScalar(ray.direction, t, result);\n  return Cartesian3.add(ray.origin, result, result);\n};\nexport default Ray;\n", "import Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport Interval from \"./Interval.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix3 from \"./Matrix3.js\";\nimport QuadraticRealPolynomial from \"./QuadraticRealPolynomial.js\";\nimport QuarticRealPolynomial from \"./QuarticRealPolynomial.js\";\nimport Ray from \"./Ray.js\";\n\n/**\n * Functions for computing the intersection between geometries such as rays, planes, triangles, and ellipsoids.\n *\n * @namespace IntersectionTests\n */\nconst IntersectionTests = {};\n\n/**\n * Computes the intersection of a ray and a plane.\n *\n * @param {Ray} ray The ray.\n * @param {Plane} plane The plane.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The intersection point or undefined if there is no intersections.\n */\nIntersectionTests.rayPlane = function (ray, plane, result) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(ray)) {\n    throw new DeveloperError(\"ray is required.\");\n  }\n  if (!defined(plane)) {\n    throw new DeveloperError(\"plane is required.\");\n  }\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  const origin = ray.origin;\n  const direction = ray.direction;\n  const normal = plane.normal;\n  const denominator = Cartesian3.dot(normal, direction);\n\n  if (Math.abs(denominator) < CesiumMath.EPSILON15) {\n    // Ray is parallel to plane.  The ray may be in the polygon's plane.\n    return undefined;\n  }\n\n  const t = (-plane.distance - Cartesian3.dot(normal, origin)) / denominator;\n\n  if (t < 0) {\n    return undefined;\n  }\n\n  result = Cartesian3.multiplyByScalar(direction, t, result);\n  return Cartesian3.add(origin, result, result);\n};\n\nconst scratchEdge0 = new Cartesian3();\nconst scratchEdge1 = new Cartesian3();\nconst scratchPVec = new Cartesian3();\nconst scratchTVec = new Cartesian3();\nconst scratchQVec = new Cartesian3();\n\n/**\n * Computes the intersection of a ray and a triangle as a parametric distance along the input ray. The result is negative when the triangle is behind the ray.\n *\n * Implements {@link https://cadxfem.org/inf/Fast%20MinimumStorage%20RayTriangle%20Intersection.pdf|\n * Fast Minimum Storage Ray/Triangle Intersection} by Tomas Moller and Ben Trumbore.\n *\n * @memberof IntersectionTests\n *\n * @param {Ray} ray The ray.\n * @param {Cartesian3} p0 The first vertex of the triangle.\n * @param {Cartesian3} p1 The second vertex of the triangle.\n * @param {Cartesian3} p2 The third vertex of the triangle.\n * @param {Boolean} [cullBackFaces=false] If <code>true</code>, will only compute an intersection with the front face of the triangle\n *                  and return undefined for intersections with the back face.\n * @returns {Number} The intersection as a parametric distance along the ray, or undefined if there is no intersection.\n */\nIntersectionTests.rayTriangleParametric = function (\n  ray,\n  p0,\n  p1,\n  p2,\n  cullBackFaces\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(ray)) {\n    throw new DeveloperError(\"ray is required.\");\n  }\n  if (!defined(p0)) {\n    throw new DeveloperError(\"p0 is required.\");\n  }\n  if (!defined(p1)) {\n    throw new DeveloperError(\"p1 is required.\");\n  }\n  if (!defined(p2)) {\n    throw new DeveloperError(\"p2 is required.\");\n  }\n  //>>includeEnd('debug');\n\n  cullBackFaces = defaultValue(cullBackFaces, false);\n\n  const origin = ray.origin;\n  const direction = ray.direction;\n\n  const edge0 = Cartesian3.subtract(p1, p0, scratchEdge0);\n  const edge1 = Cartesian3.subtract(p2, p0, scratchEdge1);\n\n  const p = Cartesian3.cross(direction, edge1, scratchPVec);\n  const det = Cartesian3.dot(edge0, p);\n\n  let tvec;\n  let q;\n\n  let u;\n  let v;\n  let t;\n\n  if (cullBackFaces) {\n    if (det < CesiumMath.EPSILON6) {\n      return undefined;\n    }\n\n    tvec = Cartesian3.subtract(origin, p0, scratchTVec);\n    u = Cartesian3.dot(tvec, p);\n    if (u < 0.0 || u > det) {\n      return undefined;\n    }\n\n    q = Cartesian3.cross(tvec, edge0, scratchQVec);\n\n    v = Cartesian3.dot(direction, q);\n    if (v < 0.0 || u + v > det) {\n      return undefined;\n    }\n\n    t = Cartesian3.dot(edge1, q) / det;\n  } else {\n    if (Math.abs(det) < CesiumMath.EPSILON6) {\n      return undefined;\n    }\n    const invDet = 1.0 / det;\n\n    tvec = Cartesian3.subtract(origin, p0, scratchTVec);\n    u = Cartesian3.dot(tvec, p) * invDet;\n    if (u < 0.0 || u > 1.0) {\n      return undefined;\n    }\n\n    q = Cartesian3.cross(tvec, edge0, scratchQVec);\n\n    v = Cartesian3.dot(direction, q) * invDet;\n    if (v < 0.0 || u + v > 1.0) {\n      return undefined;\n    }\n\n    t = Cartesian3.dot(edge1, q) * invDet;\n  }\n\n  return t;\n};\n\n/**\n * Computes the intersection of a ray and a triangle as a Cartesian3 coordinate.\n *\n * Implements {@link https://cadxfem.org/inf/Fast%20MinimumStorage%20RayTriangle%20Intersection.pdf|\n * Fast Minimum Storage Ray/Triangle Intersection} by Tomas Moller and Ben Trumbore.\n *\n * @memberof IntersectionTests\n *\n * @param {Ray} ray The ray.\n * @param {Cartesian3} p0 The first vertex of the triangle.\n * @param {Cartesian3} p1 The second vertex of the triangle.\n * @param {Cartesian3} p2 The third vertex of the triangle.\n * @param {Boolean} [cullBackFaces=false] If <code>true</code>, will only compute an intersection with the front face of the triangle\n *                  and return undefined for intersections with the back face.\n * @param {Cartesian3} [result] The <code>Cartesian3</code> onto which to store the result.\n * @returns {Cartesian3} The intersection point or undefined if there is no intersections.\n */\nIntersectionTests.rayTriangle = function (\n  ray,\n  p0,\n  p1,\n  p2,\n  cullBackFaces,\n  result\n) {\n  const t = IntersectionTests.rayTriangleParametric(\n    ray,\n    p0,\n    p1,\n    p2,\n    cullBackFaces\n  );\n  if (!defined(t) || t < 0.0) {\n    return undefined;\n  }\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  Cartesian3.multiplyByScalar(ray.direction, t, result);\n  return Cartesian3.add(ray.origin, result, result);\n};\n\nconst scratchLineSegmentTriangleRay = new Ray();\n\n/**\n * Computes the intersection of a line segment and a triangle.\n * @memberof IntersectionTests\n *\n * @param {Cartesian3} v0 The an end point of the line segment.\n * @param {Cartesian3} v1 The other end point of the line segment.\n * @param {Cartesian3} p0 The first vertex of the triangle.\n * @param {Cartesian3} p1 The second vertex of the triangle.\n * @param {Cartesian3} p2 The third vertex of the triangle.\n * @param {Boolean} [cullBackFaces=false] If <code>true</code>, will only compute an intersection with the front face of the triangle\n *                  and return undefined for intersections with the back face.\n * @param {Cartesian3} [result] The <code>Cartesian3</code> onto which to store the result.\n * @returns {Cartesian3} The intersection point or undefined if there is no intersections.\n */\nIntersectionTests.lineSegmentTriangle = function (\n  v0,\n  v1,\n  p0,\n  p1,\n  p2,\n  cullBackFaces,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(v0)) {\n    throw new DeveloperError(\"v0 is required.\");\n  }\n  if (!defined(v1)) {\n    throw new DeveloperError(\"v1 is required.\");\n  }\n  if (!defined(p0)) {\n    throw new DeveloperError(\"p0 is required.\");\n  }\n  if (!defined(p1)) {\n    throw new DeveloperError(\"p1 is required.\");\n  }\n  if (!defined(p2)) {\n    throw new DeveloperError(\"p2 is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const ray = scratchLineSegmentTriangleRay;\n  Cartesian3.clone(v0, ray.origin);\n  Cartesian3.subtract(v1, v0, ray.direction);\n  Cartesian3.normalize(ray.direction, ray.direction);\n\n  const t = IntersectionTests.rayTriangleParametric(\n    ray,\n    p0,\n    p1,\n    p2,\n    cullBackFaces\n  );\n  if (!defined(t) || t < 0.0 || t > Cartesian3.distance(v0, v1)) {\n    return undefined;\n  }\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  Cartesian3.multiplyByScalar(ray.direction, t, result);\n  return Cartesian3.add(ray.origin, result, result);\n};\n\nfunction solveQuadratic(a, b, c, result) {\n  const det = b * b - 4.0 * a * c;\n  if (det < 0.0) {\n    return undefined;\n  } else if (det > 0.0) {\n    const denom = 1.0 / (2.0 * a);\n    const disc = Math.sqrt(det);\n    const root0 = (-b + disc) * denom;\n    const root1 = (-b - disc) * denom;\n\n    if (root0 < root1) {\n      result.root0 = root0;\n      result.root1 = root1;\n    } else {\n      result.root0 = root1;\n      result.root1 = root0;\n    }\n\n    return result;\n  }\n\n  const root = -b / (2.0 * a);\n  if (root === 0.0) {\n    return undefined;\n  }\n\n  result.root0 = result.root1 = root;\n  return result;\n}\n\nconst raySphereRoots = {\n  root0: 0.0,\n  root1: 0.0,\n};\n\nfunction raySphere(ray, sphere, result) {\n  if (!defined(result)) {\n    result = new Interval();\n  }\n\n  const origin = ray.origin;\n  const direction = ray.direction;\n\n  const center = sphere.center;\n  const radiusSquared = sphere.radius * sphere.radius;\n\n  const diff = Cartesian3.subtract(origin, center, scratchPVec);\n\n  const a = Cartesian3.dot(direction, direction);\n  const b = 2.0 * Cartesian3.dot(direction, diff);\n  const c = Cartesian3.magnitudeSquared(diff) - radiusSquared;\n\n  const roots = solveQuadratic(a, b, c, raySphereRoots);\n  if (!defined(roots)) {\n    return undefined;\n  }\n\n  result.start = roots.root0;\n  result.stop = roots.root1;\n  return result;\n}\n\n/**\n * Computes the intersection points of a ray with a sphere.\n * @memberof IntersectionTests\n *\n * @param {Ray} ray The ray.\n * @param {BoundingSphere} sphere The sphere.\n * @param {Interval} [result] The result onto which to store the result.\n * @returns {Interval} The interval containing scalar points along the ray or undefined if there are no intersections.\n */\nIntersectionTests.raySphere = function (ray, sphere, result) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(ray)) {\n    throw new DeveloperError(\"ray is required.\");\n  }\n  if (!defined(sphere)) {\n    throw new DeveloperError(\"sphere is required.\");\n  }\n  //>>includeEnd('debug');\n\n  result = raySphere(ray, sphere, result);\n  if (!defined(result) || result.stop < 0.0) {\n    return undefined;\n  }\n\n  result.start = Math.max(result.start, 0.0);\n  return result;\n};\n\nconst scratchLineSegmentRay = new Ray();\n\n/**\n * Computes the intersection points of a line segment with a sphere.\n * @memberof IntersectionTests\n *\n * @param {Cartesian3} p0 An end point of the line segment.\n * @param {Cartesian3} p1 The other end point of the line segment.\n * @param {BoundingSphere} sphere The sphere.\n * @param {Interval} [result] The result onto which to store the result.\n * @returns {Interval} The interval containing scalar points along the ray or undefined if there are no intersections.\n */\nIntersectionTests.lineSegmentSphere = function (p0, p1, sphere, result) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(p0)) {\n    throw new DeveloperError(\"p0 is required.\");\n  }\n  if (!defined(p1)) {\n    throw new DeveloperError(\"p1 is required.\");\n  }\n  if (!defined(sphere)) {\n    throw new DeveloperError(\"sphere is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const ray = scratchLineSegmentRay;\n  Cartesian3.clone(p0, ray.origin);\n  const direction = Cartesian3.subtract(p1, p0, ray.direction);\n\n  const maxT = Cartesian3.magnitude(direction);\n  Cartesian3.normalize(direction, direction);\n\n  result = raySphere(ray, sphere, result);\n  if (!defined(result) || result.stop < 0.0 || result.start > maxT) {\n    return undefined;\n  }\n\n  result.start = Math.max(result.start, 0.0);\n  result.stop = Math.min(result.stop, maxT);\n  return result;\n};\n\nconst scratchQ = new Cartesian3();\nconst scratchW = new Cartesian3();\n\n/**\n * Computes the intersection points of a ray with an ellipsoid.\n *\n * @param {Ray} ray The ray.\n * @param {Ellipsoid} ellipsoid The ellipsoid.\n * @returns {Interval} The interval containing scalar points along the ray or undefined if there are no intersections.\n */\nIntersectionTests.rayEllipsoid = function (ray, ellipsoid) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(ray)) {\n    throw new DeveloperError(\"ray is required.\");\n  }\n  if (!defined(ellipsoid)) {\n    throw new DeveloperError(\"ellipsoid is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const inverseRadii = ellipsoid.oneOverRadii;\n  const q = Cartesian3.multiplyComponents(inverseRadii, ray.origin, scratchQ);\n  const w = Cartesian3.multiplyComponents(\n    inverseRadii,\n    ray.direction,\n    scratchW\n  );\n\n  const q2 = Cartesian3.magnitudeSquared(q);\n  const qw = Cartesian3.dot(q, w);\n\n  let difference, w2, product, discriminant, temp;\n\n  if (q2 > 1.0) {\n    // Outside ellipsoid.\n    if (qw >= 0.0) {\n      // Looking outward or tangent (0 intersections).\n      return undefined;\n    }\n\n    // qw < 0.0.\n    const qw2 = qw * qw;\n    difference = q2 - 1.0; // Positively valued.\n    w2 = Cartesian3.magnitudeSquared(w);\n    product = w2 * difference;\n\n    if (qw2 < product) {\n      // Imaginary roots (0 intersections).\n      return undefined;\n    } else if (qw2 > product) {\n      // Distinct roots (2 intersections).\n      discriminant = qw * qw - product;\n      temp = -qw + Math.sqrt(discriminant); // Avoid cancellation.\n      const root0 = temp / w2;\n      const root1 = difference / temp;\n      if (root0 < root1) {\n        return new Interval(root0, root1);\n      }\n\n      return {\n        start: root1,\n        stop: root0,\n      };\n    }\n    // qw2 == product.  Repeated roots (2 intersections).\n    const root = Math.sqrt(difference / w2);\n    return new Interval(root, root);\n  } else if (q2 < 1.0) {\n    // Inside ellipsoid (2 intersections).\n    difference = q2 - 1.0; // Negatively valued.\n    w2 = Cartesian3.magnitudeSquared(w);\n    product = w2 * difference; // Negatively valued.\n\n    discriminant = qw * qw - product;\n    temp = -qw + Math.sqrt(discriminant); // Positively valued.\n    return new Interval(0.0, temp / w2);\n  }\n  // q2 == 1.0. On ellipsoid.\n  if (qw < 0.0) {\n    // Looking inward.\n    w2 = Cartesian3.magnitudeSquared(w);\n    return new Interval(0.0, -qw / w2);\n  }\n\n  // qw >= 0.0.  Looking outward or tangent.\n  return undefined;\n};\n\nfunction addWithCancellationCheck(left, right, tolerance) {\n  const difference = left + right;\n  if (\n    CesiumMath.sign(left) !== CesiumMath.sign(right) &&\n    Math.abs(difference / Math.max(Math.abs(left), Math.abs(right))) < tolerance\n  ) {\n    return 0.0;\n  }\n\n  return difference;\n}\n\nfunction quadraticVectorExpression(A, b, c, x, w) {\n  const xSquared = x * x;\n  const wSquared = w * w;\n\n  const l2 = (A[Matrix3.COLUMN1ROW1] - A[Matrix3.COLUMN2ROW2]) * wSquared;\n  const l1 =\n    w *\n    (x *\n      addWithCancellationCheck(\n        A[Matrix3.COLUMN1ROW0],\n        A[Matrix3.COLUMN0ROW1],\n        CesiumMath.EPSILON15\n      ) +\n      b.y);\n  const l0 =\n    A[Matrix3.COLUMN0ROW0] * xSquared +\n    A[Matrix3.COLUMN2ROW2] * wSquared +\n    x * b.x +\n    c;\n\n  const r1 =\n    wSquared *\n    addWithCancellationCheck(\n      A[Matrix3.COLUMN2ROW1],\n      A[Matrix3.COLUMN1ROW2],\n      CesiumMath.EPSILON15\n    );\n  const r0 =\n    w *\n    (x *\n      addWithCancellationCheck(A[Matrix3.COLUMN2ROW0], A[Matrix3.COLUMN0ROW2]) +\n      b.z);\n\n  let cosines;\n  const solutions = [];\n  if (r0 === 0.0 && r1 === 0.0) {\n    cosines = QuadraticRealPolynomial.computeRealRoots(l2, l1, l0);\n    if (cosines.length === 0) {\n      return solutions;\n    }\n\n    const cosine0 = cosines[0];\n    const sine0 = Math.sqrt(Math.max(1.0 - cosine0 * cosine0, 0.0));\n    solutions.push(new Cartesian3(x, w * cosine0, w * -sine0));\n    solutions.push(new Cartesian3(x, w * cosine0, w * sine0));\n\n    if (cosines.length === 2) {\n      const cosine1 = cosines[1];\n      const sine1 = Math.sqrt(Math.max(1.0 - cosine1 * cosine1, 0.0));\n      solutions.push(new Cartesian3(x, w * cosine1, w * -sine1));\n      solutions.push(new Cartesian3(x, w * cosine1, w * sine1));\n    }\n\n    return solutions;\n  }\n\n  const r0Squared = r0 * r0;\n  const r1Squared = r1 * r1;\n  const l2Squared = l2 * l2;\n  const r0r1 = r0 * r1;\n\n  const c4 = l2Squared + r1Squared;\n  const c3 = 2.0 * (l1 * l2 + r0r1);\n  const c2 = 2.0 * l0 * l2 + l1 * l1 - r1Squared + r0Squared;\n  const c1 = 2.0 * (l0 * l1 - r0r1);\n  const c0 = l0 * l0 - r0Squared;\n\n  if (c4 === 0.0 && c3 === 0.0 && c2 === 0.0 && c1 === 0.0) {\n    return solutions;\n  }\n\n  cosines = QuarticRealPolynomial.computeRealRoots(c4, c3, c2, c1, c0);\n  const length = cosines.length;\n  if (length === 0) {\n    return solutions;\n  }\n\n  for (let i = 0; i < length; ++i) {\n    const cosine = cosines[i];\n    const cosineSquared = cosine * cosine;\n    const sineSquared = Math.max(1.0 - cosineSquared, 0.0);\n    const sine = Math.sqrt(sineSquared);\n\n    //const left = l2 * cosineSquared + l1 * cosine + l0;\n    let left;\n    if (CesiumMath.sign(l2) === CesiumMath.sign(l0)) {\n      left = addWithCancellationCheck(\n        l2 * cosineSquared + l0,\n        l1 * cosine,\n        CesiumMath.EPSILON12\n      );\n    } else if (CesiumMath.sign(l0) === CesiumMath.sign(l1 * cosine)) {\n      left = addWithCancellationCheck(\n        l2 * cosineSquared,\n        l1 * cosine + l0,\n        CesiumMath.EPSILON12\n      );\n    } else {\n      left = addWithCancellationCheck(\n        l2 * cosineSquared + l1 * cosine,\n        l0,\n        CesiumMath.EPSILON12\n      );\n    }\n\n    const right = addWithCancellationCheck(\n      r1 * cosine,\n      r0,\n      CesiumMath.EPSILON15\n    );\n    const product = left * right;\n\n    if (product < 0.0) {\n      solutions.push(new Cartesian3(x, w * cosine, w * sine));\n    } else if (product > 0.0) {\n      solutions.push(new Cartesian3(x, w * cosine, w * -sine));\n    } else if (sine !== 0.0) {\n      solutions.push(new Cartesian3(x, w * cosine, w * -sine));\n      solutions.push(new Cartesian3(x, w * cosine, w * sine));\n      ++i;\n    } else {\n      solutions.push(new Cartesian3(x, w * cosine, w * sine));\n    }\n  }\n\n  return solutions;\n}\n\nconst firstAxisScratch = new Cartesian3();\nconst secondAxisScratch = new Cartesian3();\nconst thirdAxisScratch = new Cartesian3();\nconst referenceScratch = new Cartesian3();\nconst bCart = new Cartesian3();\nconst bScratch = new Matrix3();\nconst btScratch = new Matrix3();\nconst diScratch = new Matrix3();\nconst dScratch = new Matrix3();\nconst cScratch = new Matrix3();\nconst tempMatrix = new Matrix3();\nconst aScratch = new Matrix3();\nconst sScratch = new Cartesian3();\nconst closestScratch = new Cartesian3();\nconst surfPointScratch = new Cartographic();\n\n/**\n * Provides the point along the ray which is nearest to the ellipsoid.\n *\n * @param {Ray} ray The ray.\n * @param {Ellipsoid} ellipsoid The ellipsoid.\n * @returns {Cartesian3} The nearest planetodetic point on the ray.\n */\nIntersectionTests.grazingAltitudeLocation = function (ray, ellipsoid) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(ray)) {\n    throw new DeveloperError(\"ray is required.\");\n  }\n  if (!defined(ellipsoid)) {\n    throw new DeveloperError(\"ellipsoid is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const position = ray.origin;\n  const direction = ray.direction;\n\n  if (!Cartesian3.equals(position, Cartesian3.ZERO)) {\n    const normal = ellipsoid.geodeticSurfaceNormal(position, firstAxisScratch);\n    if (Cartesian3.dot(direction, normal) >= 0.0) {\n      // The location provided is the closest point in altitude\n      return position;\n    }\n  }\n\n  const intersects = defined(this.rayEllipsoid(ray, ellipsoid));\n\n  // Compute the scaled direction vector.\n  const f = ellipsoid.transformPositionToScaledSpace(\n    direction,\n    firstAxisScratch\n  );\n\n  // Constructs a basis from the unit scaled direction vector. Construct its rotation and transpose.\n  const firstAxis = Cartesian3.normalize(f, f);\n  const reference = Cartesian3.mostOrthogonalAxis(f, referenceScratch);\n  const secondAxis = Cartesian3.normalize(\n    Cartesian3.cross(reference, firstAxis, secondAxisScratch),\n    secondAxisScratch\n  );\n  const thirdAxis = Cartesian3.normalize(\n    Cartesian3.cross(firstAxis, secondAxis, thirdAxisScratch),\n    thirdAxisScratch\n  );\n  const B = bScratch;\n  B[0] = firstAxis.x;\n  B[1] = firstAxis.y;\n  B[2] = firstAxis.z;\n  B[3] = secondAxis.x;\n  B[4] = secondAxis.y;\n  B[5] = secondAxis.z;\n  B[6] = thirdAxis.x;\n  B[7] = thirdAxis.y;\n  B[8] = thirdAxis.z;\n\n  const B_T = Matrix3.transpose(B, btScratch);\n\n  // Get the scaling matrix and its inverse.\n  const D_I = Matrix3.fromScale(ellipsoid.radii, diScratch);\n  const D = Matrix3.fromScale(ellipsoid.oneOverRadii, dScratch);\n\n  const C = cScratch;\n  C[0] = 0.0;\n  C[1] = -direction.z;\n  C[2] = direction.y;\n  C[3] = direction.z;\n  C[4] = 0.0;\n  C[5] = -direction.x;\n  C[6] = -direction.y;\n  C[7] = direction.x;\n  C[8] = 0.0;\n\n  const temp = Matrix3.multiply(\n    Matrix3.multiply(B_T, D, tempMatrix),\n    C,\n    tempMatrix\n  );\n  const A = Matrix3.multiply(\n    Matrix3.multiply(temp, D_I, aScratch),\n    B,\n    aScratch\n  );\n  const b = Matrix3.multiplyByVector(temp, position, bCart);\n\n  // Solve for the solutions to the expression in standard form:\n  const solutions = quadraticVectorExpression(\n    A,\n    Cartesian3.negate(b, firstAxisScratch),\n    0.0,\n    0.0,\n    1.0\n  );\n\n  let s;\n  let altitude;\n  const length = solutions.length;\n  if (length > 0) {\n    let closest = Cartesian3.clone(Cartesian3.ZERO, closestScratch);\n    let maximumValue = Number.NEGATIVE_INFINITY;\n\n    for (let i = 0; i < length; ++i) {\n      s = Matrix3.multiplyByVector(\n        D_I,\n        Matrix3.multiplyByVector(B, solutions[i], sScratch),\n        sScratch\n      );\n      const v = Cartesian3.normalize(\n        Cartesian3.subtract(s, position, referenceScratch),\n        referenceScratch\n      );\n      const dotProduct = Cartesian3.dot(v, direction);\n\n      if (dotProduct > maximumValue) {\n        maximumValue = dotProduct;\n        closest = Cartesian3.clone(s, closest);\n      }\n    }\n\n    const surfacePoint = ellipsoid.cartesianToCartographic(\n      closest,\n      surfPointScratch\n    );\n    maximumValue = CesiumMath.clamp(maximumValue, 0.0, 1.0);\n    altitude =\n      Cartesian3.magnitude(\n        Cartesian3.subtract(closest, position, referenceScratch)\n      ) * Math.sqrt(1.0 - maximumValue * maximumValue);\n    altitude = intersects ? -altitude : altitude;\n    surfacePoint.height = altitude;\n    return ellipsoid.cartographicToCartesian(surfacePoint, new Cartesian3());\n  }\n\n  return undefined;\n};\n\nconst lineSegmentPlaneDifference = new Cartesian3();\n\n/**\n * Computes the intersection of a line segment and a plane.\n *\n * @param {Cartesian3} endPoint0 An end point of the line segment.\n * @param {Cartesian3} endPoint1 The other end point of the line segment.\n * @param {Plane} plane The plane.\n * @param {Cartesian3} [result] The object onto which to store the result.\n * @returns {Cartesian3} The intersection point or undefined if there is no intersection.\n *\n * @example\n * const origin = Cesium.Cartesian3.fromDegrees(-75.59777, 40.03883);\n * const normal = ellipsoid.geodeticSurfaceNormal(origin);\n * const plane = Cesium.Plane.fromPointNormal(origin, normal);\n *\n * const p0 = new Cesium.Cartesian3(...);\n * const p1 = new Cesium.Cartesian3(...);\n *\n * // find the intersection of the line segment from p0 to p1 and the tangent plane at origin.\n * const intersection = Cesium.IntersectionTests.lineSegmentPlane(p0, p1, plane);\n */\nIntersectionTests.lineSegmentPlane = function (\n  endPoint0,\n  endPoint1,\n  plane,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(endPoint0)) {\n    throw new DeveloperError(\"endPoint0 is required.\");\n  }\n  if (!defined(endPoint1)) {\n    throw new DeveloperError(\"endPoint1 is required.\");\n  }\n  if (!defined(plane)) {\n    throw new DeveloperError(\"plane is required.\");\n  }\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  const difference = Cartesian3.subtract(\n    endPoint1,\n    endPoint0,\n    lineSegmentPlaneDifference\n  );\n  const normal = plane.normal;\n  const nDotDiff = Cartesian3.dot(normal, difference);\n\n  // check if the segment and plane are parallel\n  if (Math.abs(nDotDiff) < CesiumMath.EPSILON6) {\n    return undefined;\n  }\n\n  const nDotP0 = Cartesian3.dot(normal, endPoint0);\n  const t = -(plane.distance + nDotP0) / nDotDiff;\n\n  // intersection only if t is in [0, 1]\n  if (t < 0.0 || t > 1.0) {\n    return undefined;\n  }\n\n  // intersection is endPoint0 + t * (endPoint1 - endPoint0)\n  Cartesian3.multiplyByScalar(difference, t, result);\n  Cartesian3.add(endPoint0, result, result);\n  return result;\n};\n\n/**\n * Computes the intersection of a triangle and a plane\n *\n * @param {Cartesian3} p0 First point of the triangle\n * @param {Cartesian3} p1 Second point of the triangle\n * @param {Cartesian3} p2 Third point of the triangle\n * @param {Plane} plane Intersection plane\n * @returns {Object} An object with properties <code>positions</code> and <code>indices</code>, which are arrays that represent three triangles that do not cross the plane. (Undefined if no intersection exists)\n *\n * @example\n * const origin = Cesium.Cartesian3.fromDegrees(-75.59777, 40.03883);\n * const normal = ellipsoid.geodeticSurfaceNormal(origin);\n * const plane = Cesium.Plane.fromPointNormal(origin, normal);\n *\n * const p0 = new Cesium.Cartesian3(...);\n * const p1 = new Cesium.Cartesian3(...);\n * const p2 = new Cesium.Cartesian3(...);\n *\n * // convert the triangle composed of points (p0, p1, p2) to three triangles that don't cross the plane\n * const triangles = Cesium.IntersectionTests.trianglePlaneIntersection(p0, p1, p2, plane);\n */\nIntersectionTests.trianglePlaneIntersection = function (p0, p1, p2, plane) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(p0) || !defined(p1) || !defined(p2) || !defined(plane)) {\n    throw new DeveloperError(\"p0, p1, p2, and plane are required.\");\n  }\n  //>>includeEnd('debug');\n\n  const planeNormal = plane.normal;\n  const planeD = plane.distance;\n  const p0Behind = Cartesian3.dot(planeNormal, p0) + planeD < 0.0;\n  const p1Behind = Cartesian3.dot(planeNormal, p1) + planeD < 0.0;\n  const p2Behind = Cartesian3.dot(planeNormal, p2) + planeD < 0.0;\n  // Given these dots products, the calls to lineSegmentPlaneIntersection\n  // always have defined results.\n\n  let numBehind = 0;\n  numBehind += p0Behind ? 1 : 0;\n  numBehind += p1Behind ? 1 : 0;\n  numBehind += p2Behind ? 1 : 0;\n\n  let u1, u2;\n  if (numBehind === 1 || numBehind === 2) {\n    u1 = new Cartesian3();\n    u2 = new Cartesian3();\n  }\n\n  if (numBehind === 1) {\n    if (p0Behind) {\n      IntersectionTests.lineSegmentPlane(p0, p1, plane, u1);\n      IntersectionTests.lineSegmentPlane(p0, p2, plane, u2);\n\n      return {\n        positions: [p0, p1, p2, u1, u2],\n        indices: [\n          // Behind\n          0,\n          3,\n          4,\n\n          // In front\n          1,\n          2,\n          4,\n          1,\n          4,\n          3,\n        ],\n      };\n    } else if (p1Behind) {\n      IntersectionTests.lineSegmentPlane(p1, p2, plane, u1);\n      IntersectionTests.lineSegmentPlane(p1, p0, plane, u2);\n\n      return {\n        positions: [p0, p1, p2, u1, u2],\n        indices: [\n          // Behind\n          1,\n          3,\n          4,\n\n          // In front\n          2,\n          0,\n          4,\n          2,\n          4,\n          3,\n        ],\n      };\n    } else if (p2Behind) {\n      IntersectionTests.lineSegmentPlane(p2, p0, plane, u1);\n      IntersectionTests.lineSegmentPlane(p2, p1, plane, u2);\n\n      return {\n        positions: [p0, p1, p2, u1, u2],\n        indices: [\n          // Behind\n          2,\n          3,\n          4,\n\n          // In front\n          0,\n          1,\n          4,\n          0,\n          4,\n          3,\n        ],\n      };\n    }\n  } else if (numBehind === 2) {\n    if (!p0Behind) {\n      IntersectionTests.lineSegmentPlane(p1, p0, plane, u1);\n      IntersectionTests.lineSegmentPlane(p2, p0, plane, u2);\n\n      return {\n        positions: [p0, p1, p2, u1, u2],\n        indices: [\n          // Behind\n          1,\n          2,\n          4,\n          1,\n          4,\n          3,\n\n          // In front\n          0,\n          3,\n          4,\n        ],\n      };\n    } else if (!p1Behind) {\n      IntersectionTests.lineSegmentPlane(p2, p1, plane, u1);\n      IntersectionTests.lineSegmentPlane(p0, p1, plane, u2);\n\n      return {\n        positions: [p0, p1, p2, u1, u2],\n        indices: [\n          // Behind\n          2,\n          0,\n          4,\n          2,\n          4,\n          3,\n\n          // In front\n          1,\n          3,\n          4,\n        ],\n      };\n    } else if (!p2Behind) {\n      IntersectionTests.lineSegmentPlane(p0, p2, plane, u1);\n      IntersectionTests.lineSegmentPlane(p1, p2, plane, u2);\n\n      return {\n        positions: [p0, p1, p2, u1, u2],\n        indices: [\n          // Behind\n          0,\n          1,\n          4,\n          0,\n          4,\n          3,\n\n          // In front\n          2,\n          3,\n          4,\n        ],\n      };\n    }\n  }\n\n  // if numBehind is 3, the triangle is completely behind the plane;\n  // otherwise, it is completely in front (numBehind is 0).\n  return undefined;\n};\nexport default IntersectionTests;\n"], "names": ["QuadraticRealPolynomial", "addWithCancellationCheck", "left", "right", "tolerance", "difference", "CesiumMath", "sign", "Math", "abs", "max", "computeDiscriminant", "a", "b", "c", "DeveloperError", "computeRealRoots", "ratio", "cMagnitude", "aMagnitude", "EPSILON14", "root", "sqrt", "radicand", "q", "CubicRealPolynomial", "d", "A", "B", "C", "D", "AC", "BD", "B2", "C2", "delta1", "delta2", "delta3", "discriminant", "temp", "temp1", "ABar", "CBar", "DBar", "temp0", "x", "p", "pow", "CBarA", "DBarA", "CBarD", "DBarD", "squareRootOfDiscriminant", "halfSquareRootOf3", "theta", "atan2", "cosine", "cos", "temp3", "sin", "numeratorLarge", "denominatorLarge", "root1", "numeratorSmall", "denominatorSmall", "root3", "F", "root2", "b2", "c2", "roots", "Length", "length", "QuarticRealPolynomial", "original", "a3", "a2", "a1", "a0", "a3Squared", "r", "cubicRoots", "hSquared", "root0", "y", "y0", "y1", "h", "m", "n", "roots1", "roots2", "<PERSON><PERSON><PERSON>", "tempSquared", "g1", "h1", "m<PERSON><PERSON><PERSON>", "nError", "g2", "h2", "G", "g", "H", "squareRootOfN", "squareRootOfM", "<PERSON>", "origin", "direction", "Cartesian3", "clone", "defaultValue", "ZERO", "equals", "normalize", "this", "e", "b3", "c3", "d2", "d3", "e2", "EPSILON15", "k", "ray", "result", "defined", "getPoint", "t", "Check", "typeOf", "object", "number", "multiplyByScalar", "add", "IntersectionTests", "plane", "normal", "denominator", "dot", "distance", "scratchEdge0", "scratchEdge1", "scratchPVec", "scratchTVec", "scratchQVec", "rayTriangleParametric", "p0", "p1", "p2", "cullBackFaces", "edge0", "subtract", "edge1", "cross", "det", "tvec", "u", "v", "EPSILON6", "invDet", "<PERSON><PERSON><PERSON><PERSON>", "scratchLineSegmentTriangleRay", "lineSegmentTriangle", "v0", "v1", "raySphereRoots", "raySphere", "sphere", "Interval", "center", "radiusSquared", "radius", "diff", "denom", "disc", "solveQuadratic", "magnitudeSquared", "start", "stop", "scratchLineSegmentRay", "lineSegmentSphere", "maxT", "magnitude", "min", "scratchQ", "scratchW", "rayEllipsoid", "ellipsoid", "inverseRadii", "oneOverRadii", "multiplyComponents", "w", "q2", "qw", "w2", "product", "qw2", "firstAxisScratch", "secondAxisScratch", "thirdAxisScratch", "referenceScratch", "b<PERSON>art", "b<PERSON><PERSON><PERSON>", "Matrix3", "btScratch", "<PERSON><PERSON><PERSON><PERSON>", "d<PERSON><PERSON><PERSON>", "c<PERSON><PERSON><PERSON>", "tempMatrix", "a<PERSON><PERSON><PERSON>", "s<PERSON><PERSON><PERSON>", "closestScratch", "surfPointScratch", "Cartographic", "grazingAltitudeLocation", "position", "geodeticSurfaceNormal", "intersects", "f", "transformPositionToScaledSpace", "firstAxis", "reference", "mostOrthogonalAxis", "secondAxis", "thirdAxis", "z", "B_T", "transpose", "D_I", "fromScale", "radii", "multiply", "multiplyByVector", "solutions", "xSquared", "wSquared", "l2", "COLUMN1ROW1", "COLUMN2ROW2", "l1", "COLUMN1ROW0", "COLUMN0ROW1", "l0", "COLUMN0ROW0", "r1", "COLUMN2ROW1", "COLUMN1ROW2", "r0", "COLUMN2ROW0", "COLUMN0ROW2", "cosines", "cosine0", "sine0", "push", "cosine1", "sine1", "r0Squared", "r1Squared", "r0r1", "c4", "c1", "c0", "i", "cosineSquared", "sineSquared", "sine", "EPSILON12", "quadraticVectorExpression", "negate", "s", "altitude", "closest", "maximumValue", "Number", "NEGATIVE_INFINITY", "dotProduct", "surfacePoint", "cartesianToCartographic", "clamp", "height", "cartographicToCartesian", "lineSegmentPlaneDifference", "lineSegmentPlane", "endPoint0", "endPoint1", "nDotDiff", "nDotP0", "trianglePlaneIntersection", "planeNormal", "planeD", "p0Behind", "p1Beh<PERSON>", "p2Behind", "u1", "u2", "numBeh<PERSON>", "positions", "indices"], "mappings": "+KAQA,MAAMA,EAA0B,CAAA,EA2BhC,SAASC,EAAyBC,EAAMC,EAAOC,GAC7C,MAAMC,EAAaH,EAAOC,EAC1B,OACEG,EAAUA,WAACC,KAAKL,KAAUI,EAAAA,WAAWC,KAAKJ,IAC1CK,KAAKC,IAAIJ,EAAaG,KAAKE,IAAIF,KAAKC,IAAIP,GAAOM,KAAKC,IAAIN,KAAWC,EAE5D,EAGFC,CACT,CA3BAL,EAAwBW,oBAAsB,SAAUC,EAAGC,EAAGC,GAE5D,GAAiB,iBAANF,EACT,MAAM,IAAIG,EAAAA,eAAe,2BAE3B,GAAiB,iBAANF,EACT,MAAM,IAAIE,EAAAA,eAAe,2BAE3B,GAAiB,iBAAND,EACT,MAAM,IAAIC,EAAAA,eAAe,2BAK3B,OADqBF,EAAIA,EAAI,EAAMD,EAAIE,CAEzC,EAsBAd,EAAwBgB,iBAAmB,SAAUJ,EAAGC,EAAGC,GAEzD,GAAiB,iBAANF,EACT,MAAM,IAAIG,EAAAA,eAAe,2BAE3B,GAAiB,iBAANF,EACT,MAAM,IAAIE,EAAAA,eAAe,2BAE3B,GAAiB,iBAAND,EACT,MAAM,IAAIC,EAAAA,eAAe,2BAI3B,IAAIE,EACJ,GAAU,IAANL,EACF,OAAU,IAANC,EAEK,GAIF,EAAEC,EAAID,GACR,GAAU,IAANA,EAAW,CACpB,GAAU,IAANC,EAEF,MAAO,CAAC,EAAK,GAGf,MAAMI,EAAaV,KAAKC,IAAIK,GACtBK,EAAaX,KAAKC,IAAIG,GAE5B,GACEM,EAAaC,GACbD,EAAaC,EAAab,EAAAA,WAAWc,UAIrC,MAAO,CAAC,EAAK,GACR,GACLF,EAAaC,GACbA,EAAaD,EAAaZ,EAAAA,WAAWc,UAIrC,MAAO,GAMT,GAFAH,GAASH,EAAIF,EAETK,EAAQ,EAEV,MAAO,GAIT,MAAMI,EAAOb,KAAKc,KAAKL,GACvB,MAAO,EAAEI,EAAMA,EACnB,CAAS,GAAU,IAANP,EAGT,OADAG,GAASJ,EAAID,EACTK,EAAQ,EACH,CAACA,EAAO,GAGV,CAAC,EAAKA,GAIf,MAEMM,EAAWtB,EAFNY,EAAIA,IACC,EAAMD,EAAIE,GAC8BR,EAAAA,WAAWc,WAEnE,GAAIG,EAAW,EAEb,MAAO,GAGT,MAAMC,GACH,GACDvB,EACEY,EACAP,EAAUA,WAACC,KAAKM,GAAKL,KAAKc,KAAKC,GAC/BjB,EAAAA,WAAWc,WAEf,OAAIP,EAAI,EACC,CAACW,EAAIZ,EAAGE,EAAIU,GAGd,CAACV,EAAIU,EAAGA,EAAIZ,EACrB,ECzIA,MAAMa,EAAsB,CAAA,EAwC5B,SAAST,EAAiBJ,EAAGC,EAAGC,EAAGY,GACjC,MAAMC,EAAIf,EACJgB,EAAIf,EAAI,EACRgB,EAAIf,EAAI,EACRgB,EAAIJ,EAEJK,EAAKJ,EAAIE,EACTG,EAAKJ,EAAIE,EACTG,EAAKL,EAAIA,EACTM,EAAKL,EAAIA,EACTM,EAASR,EAAIE,EAAII,EACjBG,EAAST,EAAIG,EAAIF,EAAIC,EACrBQ,EAAST,EAAIE,EAAII,EAEjBI,EAAe,EAAMH,EAASE,EAASD,EAASA,EACtD,IAAIG,EACAC,EAEJ,GAAIF,EAAe,EAAK,CACtB,IAAIG,EACAC,EACAC,EAEAV,EAAKD,GAAMD,EAAKG,GAClBO,EAAOd,EACPe,EAAOP,EACPQ,GAAQ,EAAMf,EAAIO,EAASR,EAAIS,IAE/BK,EAAOX,EACPY,EAAOL,EACPM,GAAQb,EAAIM,EAAS,EAAMP,EAAIQ,GAGjC,MACMO,IADID,EAAO,GAAO,EAAM,GACXnC,KAAKC,IAAIgC,GAAQjC,KAAKc,MAAMgB,GAC/CE,GAASG,EAAOC,EAEhB,MAAMC,EAAIL,EAAQ,EACZM,EAAID,EAAI,GAAOrC,KAAKuC,KAAKF,EAAG,EAAM,GAAOrC,KAAKuC,IAAIF,EAAG,EAAM,GAC3DrB,EAAIgB,IAAUI,GAASE,GAAKJ,EAAOI,EAIzC,OAFAP,EAAOG,GAAQ,EAAMI,EAAItB,GAAKmB,GAAQG,EAAIA,EAAItB,EAAIA,EAAIkB,GAElDT,EAAKD,GAAMD,EAAKG,EACX,EAAEK,EAAOX,GAAKD,GAGhB,EAAEG,GAAKS,EAAOV,GACtB,CAED,MAAMmB,EAAQb,EACRc,GAAS,EAAMrB,EAAIO,EAASR,EAAIS,EAEhCc,EAAQb,EACRc,GAASrB,EAAIM,EAAS,EAAMP,EAAIQ,EAEhCe,EAA2B5C,KAAKc,KAAKgB,GACrCe,EAAoB7C,KAAKc,KAAK,GAAO,EAE3C,IAAIgC,EAAQ9C,KAAKC,IAAID,KAAK+C,MAAM5B,EAAIyB,GAA2BH,GAAS,GACxEV,EAAO,EAAM/B,KAAKc,MAAM0B,GACxB,IAAIQ,EAAShD,KAAKiD,IAAIH,GACtBd,EAAQD,EAAOiB,EACf,IAAIE,EAAQnB,IAASiB,EAAS,EAAMH,EAAoB7C,KAAKmD,IAAIL,IAEjE,MAAMM,EAAiBpB,EAAQkB,EAAQ,EAAM9B,EAAIY,EAAQZ,EAAI8B,EAAQ9B,EAC/DiC,EAAmBlC,EAEnBmC,EAAQF,EAAiBC,EAE/BP,EAAQ9C,KAAKC,IAAID,KAAK+C,MAAMzB,EAAIsB,GAA2BD,GAAS,GACpEZ,EAAO,EAAM/B,KAAKc,MAAM4B,GACxBM,EAAShD,KAAKiD,IAAIH,GAClBd,EAAQD,EAAOiB,EACfE,EAAQnB,IAASiB,EAAS,EAAMH,EAAoB7C,KAAKmD,IAAIL,IAE7D,MAAMS,GAAkBjC,EAClBkC,EAAmBxB,EAAQkB,EAAQ,EAAM7B,EAAIW,EAAQX,EAAI6B,EAAQ7B,EAEjEoC,EAAQF,EAAiBC,EAGzBE,GACHN,EAAiBI,EAAmBH,EAAmBE,EAGpDI,GAAStC,EAAIqC,EAAItC,GAFbgC,EAAiBG,MAEOnC,EAAIsC,EAAIrC,GALhCgC,EAAmBG,IAO7B,OAAIF,GAASK,EACPL,GAASG,EACPE,GAASF,EACJ,CAACH,EAAOK,EAAOF,GAEjB,CAACH,EAAOG,EAAOE,GAEjB,CAACF,EAAOH,EAAOK,GAEpBL,GAASG,EACJ,CAACE,EAAOL,EAAOG,GAEpBE,GAASF,EACJ,CAACE,EAAOF,EAAOH,GAEjB,CAACG,EAAOE,EAAOL,EACxB,CArIArC,EAAoBd,oBAAsB,SAAUC,EAAGC,EAAGC,EAAGY,GAE3D,GAAiB,iBAANd,EACT,MAAM,IAAIG,EAAAA,eAAe,2BAE3B,GAAiB,iBAANF,EACT,MAAM,IAAIE,EAAAA,eAAe,2BAE3B,GAAiB,iBAAND,EACT,MAAM,IAAIC,EAAAA,eAAe,2BAE3B,GAAiB,iBAANW,EACT,MAAM,IAAIX,EAAAA,eAAe,2BAI3B,MACMqD,EAAKvD,EAAIA,EACTwD,EAAKvD,EAAIA,EAQf,OAJE,GAAOF,EAAIC,EAAIC,EAAIY,EACnB0C,EAAKC,EACL,IARSzD,EAAIA,IAGJc,EAAIA,GAMb,GAAOd,EAAIyD,EAAKvD,EAAIsD,EAAKvD,EAAIa,EAEjC,EAqHAD,EAAoBT,iBAAmB,SAAUJ,EAAGC,EAAGC,EAAGY,GAExD,GAAiB,iBAANd,EACT,MAAM,IAAIG,EAAAA,eAAe,2BAE3B,GAAiB,iBAANF,EACT,MAAM,IAAIE,EAAAA,eAAe,2BAE3B,GAAiB,iBAAND,EACT,MAAM,IAAIC,EAAAA,eAAe,2BAE3B,GAAiB,iBAANW,EACT,MAAM,IAAIX,EAAAA,eAAe,2BAI3B,IAAIuD,EACArD,EACJ,GAAU,IAANL,EAEF,OAAOZ,EAAwBgB,iBAAiBH,EAAGC,EAAGY,GACjD,GAAU,IAANb,EAAW,CACpB,GAAU,IAANC,EAAW,CACb,GAAU,IAANY,EAEF,MAAO,CAAC,EAAK,EAAK,GAIpBT,GAASS,EAAId,EACb,MAAMS,EACJJ,EAAQ,GAAOT,KAAKuC,KAAK9B,EAAO,EAAM,GAAOT,KAAKuC,IAAI9B,EAAO,EAAM,GACrE,MAAO,CAACI,EAAMA,EAAMA,EAC1B,CAAW,OAAU,IAANK,GAET4C,EAAQtE,EAAwBgB,iBAAiBJ,EAAG,EAAGE,GAGlC,IAAjBwD,EAAMC,OACD,CAAC,GAEH,CAACD,EAAM,GAAI,EAAKA,EAAM,KAIxBtD,EAAiBJ,EAAG,EAAGE,EAAGY,EACrC,CAAS,OAAU,IAANZ,EACC,IAANY,GAEFT,GAASJ,EAAID,EACTK,EAAQ,EACH,CAACA,EAAO,EAAK,GAEf,CAAC,EAAK,EAAKA,IAGbD,EAAiBJ,EAAGC,EAAG,EAAGa,GAClB,IAANA,GAET4C,EAAQtE,EAAwBgB,iBAAiBJ,EAAGC,EAAGC,GAGlC,IAAjBwD,EAAME,OACD,CAAC,GACCF,EAAM,IAAM,EACd,CAACA,EAAM,GAAIA,EAAM,GAAI,GACnBA,EAAM,IAAM,EACd,CAAC,EAAKA,EAAM,GAAIA,EAAM,IAExB,CAACA,EAAM,GAAI,EAAKA,EAAM,KAGxBtD,EAAiBJ,EAAGC,EAAGC,EAAGY,EACnC,EClOA,MAAM+C,EAAwB,CAAA,EAgE9B,SAASC,EAASC,EAAIC,EAAIC,EAAIC,GAC5B,MAAMC,EAAYJ,EAAKA,EAEjB7B,EAAI8B,EAAM,EAAMG,EAAa,EAC7BvD,EAAIqD,EAAMD,EAAKD,EAAM,EAAOI,EAAYJ,EAAM,EAC9CK,EACJF,EACCD,EAAKF,EAAM,EACXC,EAAKG,EAAa,GAClB,EAAMA,EAAYA,EAAa,IAG5BE,EAAaxD,EAAoBT,iBACrC,EACA,EAAM8B,EACNA,EAAIA,EAAI,EAAMkC,GACbxD,EAAIA,GAGP,GAAIyD,EAAWT,OAAS,EAAG,CACzB,MAAMjC,GAAQoC,EAAK,EAGbO,EAAWD,EAAWA,EAAWT,OAAS,GAEhD,GAAIhE,KAAKC,IAAIyE,GAAY5E,EAAAA,WAAWc,UAAW,CAE7C,MAAMkD,EAAQtE,EAAwBgB,iBAAiB,EAAK8B,EAAGkC,GAE/D,GAAqB,IAAjBV,EAAME,OAAc,CACtB,MAAMW,EAAQb,EAAM,GACdR,EAAQQ,EAAM,GAEpB,IAAIc,EACJ,GAAID,GAAS,GAAOrB,GAAS,EAAK,CAChC,MAAMuB,EAAK7E,KAAKc,KAAK6D,GACfG,EAAK9E,KAAKc,KAAKwC,GAErB,MAAO,CAACvB,EAAO+C,EAAI/C,EAAO8C,EAAI9C,EAAO8C,EAAI9C,EAAO+C,EACjD,CAAM,GAAIH,GAAS,GAAOrB,EAAQ,EAEjC,OADAsB,EAAI5E,KAAKc,KAAK6D,GACP,CAAC5C,EAAO6C,EAAG7C,EAAO6C,GACpB,GAAID,EAAQ,GAAOrB,GAAS,EAEjC,OADAsB,EAAI5E,KAAKc,KAAKwC,GACP,CAACvB,EAAO6C,EAAG7C,EAAO6C,EAE5B,CACD,MAAO,EACb,CAAW,GAAIF,EAAW,EAAK,CACzB,MAAMK,EAAI/E,KAAKc,KAAK4D,GAEdM,GAAK1C,EAAIoC,EAAW1D,EAAI+D,GAAK,EAC7BE,GAAK3C,EAAIoC,EAAW1D,EAAI+D,GAAK,EAG7BG,EAAS1F,EAAwBgB,iBAAiB,EAAKuE,EAAGC,GAC1DG,EAAS3F,EAAwBgB,iBAAiB,GAAMuE,EAAGE,GAEjE,OAAsB,IAAlBC,EAAOlB,QACTkB,EAAO,IAAMnD,EACbmD,EAAO,IAAMnD,EAES,IAAlBoD,EAAOnB,QACTmB,EAAO,IAAMpD,EACboD,EAAO,IAAMpD,EAETmD,EAAO,IAAMC,EAAO,GACf,CAACD,EAAO,GAAIA,EAAO,GAAIC,EAAO,GAAIA,EAAO,IACvCA,EAAO,IAAMD,EAAO,GACtB,CAACC,EAAO,GAAIA,EAAO,GAAID,EAAO,GAAIA,EAAO,IACvCA,EAAO,IAAMC,EAAO,IAAMD,EAAO,IAAMC,EAAO,GAChD,CAACA,EAAO,GAAID,EAAO,GAAIA,EAAO,GAAIC,EAAO,IACvCA,EAAO,IAAMD,EAAO,IAAMC,EAAO,IAAMD,EAAO,GAChD,CAACA,EAAO,GAAIC,EAAO,GAAIA,EAAO,GAAID,EAAO,IACvCA,EAAO,GAAKC,EAAO,IAAMD,EAAO,GAAKC,EAAO,GAC9C,CAACA,EAAO,GAAID,EAAO,GAAIC,EAAO,GAAID,EAAO,IAE3C,CAACA,EAAO,GAAIC,EAAO,GAAID,EAAO,GAAIC,EAAO,KAE3CD,GAGa,IAAlBC,EAAOnB,QACTmB,EAAO,IAAMpD,EACboD,EAAO,IAAMpD,EAENoD,GAEF,EACR,CACF,CACD,MAAO,EACT,CAEA,SAASC,EAAQjB,EAAIC,EAAIC,EAAIC,GAC3B,MAEMC,EAAYJ,EAAKA,EAEjB7B,GAAK,EAAM8B,EACXpD,EAAIqD,EAAKF,EAJGC,EAAKA,EAIS,EAAME,EAChCE,EAAID,EAAYD,EAAKD,EAAKD,EAAKD,EANnBE,EAAKA,EAQjBI,EAAaxD,EAAoBT,iBAAiB,EAAK8B,EAAGtB,EAAGwD,GAEnE,GAAIC,EAAWT,OAAS,EAAG,CAEzB,MAAMY,EAAIH,EAAW,GAEf1C,EAAOqC,EAAKQ,EACZS,EAActD,EAAOA,EAErBuD,EAAKnB,EAAK,EACVoB,EAAKxD,EAAO,EAEZiD,EAAIK,EAAc,EAAMf,EACxBkB,EAASH,EAAc,EAAMrF,KAAKC,IAAIqE,GAEtCW,EAAIV,EAAY,EAAMK,EACtBa,EAASlB,EAAY,EAAMvE,KAAKC,IAAI2E,GAE1C,IAAIc,EACAC,EAYAC,EACAC,EAYAC,EACAf,EAxBJ,GAAIH,EAAI,GAAOI,EAAIS,EAASR,EAAIO,EAAQ,CACtC,MAAMO,EAAgB/F,KAAKc,KAAKmE,GAChCS,EAAKK,EAAgB,EACrBJ,EAAuB,IAAlBI,EAAwB,GAAO5B,EAAKoB,EAAKlB,GAAM0B,CAC1D,KAAW,CACL,MAAMC,EAAgBhG,KAAKc,KAAKkE,GAChCU,EAAuB,IAAlBM,EAAwB,GAAO7B,EAAKoB,EAAKlB,GAAM2B,EACpDL,EAAKK,EAAgB,CACtB,CAIU,IAAPV,GAAqB,IAAPI,GAChBE,EAAI,EACJC,EAAI,GACK/F,EAAUA,WAACC,KAAKuF,KAAQxF,aAAWC,KAAK2F,IACjDE,EAAIN,EAAKI,EACTG,EAAIjB,EAAIgB,IAERC,EAAIP,EAAKI,EACTE,EAAIhB,EAAIiB,GAKC,IAAPN,GAAqB,IAAPI,GAChBG,EAAI,EACJf,EAAI,GACKjF,EAAUA,WAACC,KAAKwF,KAAQzF,aAAWC,KAAK4F,IACjDG,EAAIP,EAAKI,EACTZ,EAAIT,EAAKwB,IAETf,EAAIQ,EAAKI,EACTG,EAAIxB,EAAKS,GAIX,MAAMG,EAAS1F,EAAwBgB,iBAAiB,EAAKoF,EAAGE,GAC1DX,EAAS3F,EAAwBgB,iBAAiB,EAAKqF,EAAGd,GAEhE,GAAsB,IAAlBG,EAAOlB,OACT,OAAsB,IAAlBmB,EAAOnB,OACLkB,EAAO,IAAMC,EAAO,GACf,CAACD,EAAO,GAAIA,EAAO,GAAIC,EAAO,GAAIA,EAAO,IACvCA,EAAO,IAAMD,EAAO,GACtB,CAACC,EAAO,GAAIA,EAAO,GAAID,EAAO,GAAIA,EAAO,IACvCA,EAAO,IAAMC,EAAO,IAAMD,EAAO,IAAMC,EAAO,GAChD,CAACA,EAAO,GAAID,EAAO,GAAIA,EAAO,GAAIC,EAAO,IACvCA,EAAO,IAAMD,EAAO,IAAMC,EAAO,IAAMD,EAAO,GAChD,CAACA,EAAO,GAAIC,EAAO,GAAIA,EAAO,GAAID,EAAO,IACvCA,EAAO,GAAKC,EAAO,IAAMD,EAAO,GAAKC,EAAO,GAC9C,CAACA,EAAO,GAAID,EAAO,GAAIC,EAAO,GAAID,EAAO,IAE3C,CAACA,EAAO,GAAIC,EAAO,GAAID,EAAO,GAAIC,EAAO,IAE3CD,EAET,GAAsB,IAAlBC,EAAOnB,OACT,OAAOmB,CAEV,CACD,MAAO,EACT,CCvPA,SAASc,EAAIC,EAAQC,GACnBA,EAAYC,EAAUA,WAACC,MAAMC,EAAAA,aAAaH,EAAWC,EAAUA,WAACG,OAC3DH,EAAUA,WAACI,OAAOL,EAAWC,EAAUA,WAACG,OAC3CH,EAAAA,WAAWK,UAAUN,EAAWA,GAQlCO,KAAKR,OAASE,EAAAA,WAAWC,MAAMC,EAAYA,aAACJ,EAAQE,EAAAA,WAAWG,OAM/DG,KAAKP,UAAYA,CACnB,CDTAlC,EAAsB9D,oBAAsB,SAAUC,EAAGC,EAAGC,EAAGY,EAAGyF,GAEhE,GAAiB,iBAANvG,EACT,MAAM,IAAIG,EAAAA,eAAe,2BAE3B,GAAiB,iBAANF,EACT,MAAM,IAAIE,EAAAA,eAAe,2BAE3B,GAAiB,iBAAND,EACT,MAAM,IAAIC,EAAAA,eAAe,2BAE3B,GAAiB,iBAANW,EACT,MAAM,IAAIX,EAAAA,eAAe,2BAE3B,GAAiB,iBAANoG,EACT,MAAM,IAAIpG,EAAAA,eAAe,2BAI3B,MAAM6D,EAAKhE,EAAIA,EAETwD,EAAKvD,EAAIA,EACTuG,EAAKhD,EAAKvD,EACVwD,EAAKvD,EAAIA,EACTuG,EAAKhD,EAAKvD,EACVwG,EAAK5F,EAAIA,EACT6F,EAAKD,EAAK5F,EACV8F,EAAKL,EAAIA,EAsBf,OAlBE/C,EAAKC,EAAKiD,EACV,EAAMF,EAAKG,EACX,EAAM3G,EAAIyG,EAAKC,EACf,GAAK1G,EAAIC,EAAIC,EAAIyG,EACjB,GAAO3C,EAAK0C,EAAKA,EACjB,KAhBS1C,EAAKhE,IAQL4G,EAAKL,GASdA,GACG,GAAOC,EAAKtG,EAAIY,EACf,EAAM0C,EAAKiD,EACX,GAAOzG,EAAIyD,EAAKA,EAChB,GAAOzD,EAAIC,EAAIwD,EAAK3C,EACpB,EAAMd,EAAIwD,EAAKkD,EACf,IAAQ1C,EAAK9D,EAAIwG,GACrBE,GACG,IAAQ5G,EAAIwD,EAAKtD,EAChB,GAAOsD,EAAKA,EACZ,IAAQQ,EAAKP,EACb,IAAQO,EAAK/D,EAAIa,EAEzB,EAwMA+C,EAAsBzD,iBAAmB,SAAUJ,EAAGC,EAAGC,EAAGY,EAAGyF,GAE7D,GAAiB,iBAANvG,EACT,MAAM,IAAIG,EAAAA,eAAe,2BAE3B,GAAiB,iBAANF,EACT,MAAM,IAAIE,EAAAA,eAAe,2BAE3B,GAAiB,iBAAND,EACT,MAAM,IAAIC,EAAAA,eAAe,2BAE3B,GAAiB,iBAANW,EACT,MAAM,IAAIX,EAAAA,eAAe,2BAE3B,GAAiB,iBAANoG,EACT,MAAM,IAAIpG,EAAAA,eAAe,2BAI3B,GAAIP,KAAKC,IAAIG,GAAKN,EAAAA,WAAWmH,UAC3B,OAAOhG,EAAoBT,iBAAiBH,EAAGC,EAAGY,EAAGyF,GAEvD,MAAMxC,EAAK9D,EAAID,EACTgE,EAAK9D,EAAIF,EACTiE,EAAKnD,EAAId,EACTkE,EAAKqC,EAAIvG,EAEf,IAAI8G,EAAI/C,EAAK,EAAM,EAAI,EAKvB,OAJA+C,GAAK9C,EAAK,EAAM8C,EAAI,EAAIA,EACxBA,GAAK7C,EAAK,EAAM6C,EAAI,EAAIA,EACxBA,GAAK5C,EAAK,EAAM4C,EAAI,EAAIA,EAEhBA,GACN,KAAK,EAML,KAAK,EAEL,KAAK,EAIL,KAAK,EAEL,KAAK,EAIL,KAAK,EAEL,KAAK,GAIL,KAAK,GAEL,KAAK,GAEL,KAAK,GAEL,KAAK,GACH,OAAOhD,EAASC,EAAIC,EAAIC,EAAIC,GA7B9B,KAAK,EAEL,KAAK,EAML,KAAK,EAML,KAAK,EAML,KAAK,GACH,OAAOc,EAAQjB,EAAIC,EAAIC,EAAIC,GAS7B,QACE,OAEN,EC5SA2B,EAAII,MAAQ,SAAUc,EAAKC,GACzB,GAAKC,EAAAA,QAAQF,GAGb,OAAKE,EAAAA,QAAQD,IAGbA,EAAOlB,OAASE,EAAUA,WAACC,MAAMc,EAAIjB,QACrCkB,EAAOjB,UAAYC,EAAUA,WAACC,MAAMc,EAAIhB,WACjCiB,GAJE,IAAInB,EAAIkB,EAAIjB,OAAQiB,EAAIhB,UAKnC,EAgBAF,EAAIqB,SAAW,SAAUH,EAAKI,EAAGH,GAW/B,OATAI,EAAAA,MAAMC,OAAOC,OAAO,MAAOP,GAC3BK,EAAAA,MAAMC,OAAOE,OAAO,IAAKJ,GAGpBF,EAAAA,QAAQD,KACXA,EAAS,IAAIhB,EAAAA,YAGfgB,EAAShB,EAAUA,WAACwB,iBAAiBT,EAAIhB,UAAWoB,EAAGH,GAChDhB,EAAAA,WAAWyB,IAAIV,EAAIjB,OAAQkB,EAAQA,EAC5C,EC7DM,MAAAU,EAAoB,CAU1BA,SAA6B,SAAUX,EAAKY,EAAOX,GAEjD,IAAKC,EAAAA,QAAQF,GACX,MAAM,IAAI5G,EAAAA,eAAe,oBAE3B,IAAK8G,EAAAA,QAAQU,GACX,MAAM,IAAIxH,EAAAA,eAAe,sBAItB8G,EAAAA,QAAQD,KACXA,EAAS,IAAIhB,EAAAA,YAGf,MAAMF,EAASiB,EAAIjB,OACbC,EAAYgB,EAAIhB,UAChB6B,EAASD,EAAMC,OACfC,EAAc7B,EAAUA,WAAC8B,IAAIF,EAAQ7B,GAE3C,GAAInG,KAAKC,IAAIgI,GAAenI,EAAAA,WAAWmH,UAErC,OAGF,MAAMM,IAAMQ,EAAMI,SAAW/B,aAAW8B,IAAIF,EAAQ9B,IAAW+B,EAE/D,OAAIV,EAAI,OAAR,GAIAH,EAAShB,EAAUA,WAACwB,iBAAiBzB,EAAWoB,EAAGH,GAC5ChB,EAAUA,WAACyB,IAAI3B,EAAQkB,EAAQA,GACxC,GAEMgB,EAAe,IAAIhC,EAAAA,WACnBiC,EAAe,IAAIjC,EAAAA,WACnBkC,EAAc,IAAIlC,EAAAA,WAClBmC,EAAc,IAAInC,EAAAA,WAClBoC,EAAc,IAAIpC,EAAAA,WAkBxB0B,EAAkBW,sBAAwB,SACxCtB,EACAuB,EACAC,EACAC,EACAC,GAGA,IAAKxB,EAAAA,QAAQF,GACX,MAAM,IAAI5G,EAAAA,eAAe,oBAE3B,IAAK8G,EAAAA,QAAQqB,GACX,MAAM,IAAInI,EAAAA,eAAe,mBAE3B,IAAK8G,EAAAA,QAAQsB,GACX,MAAM,IAAIpI,EAAAA,eAAe,mBAE3B,IAAK8G,EAAAA,QAAQuB,GACX,MAAM,IAAIrI,EAAAA,eAAe,mBAI3BsI,EAAgBvC,EAAYA,aAACuC,GAAe,GAE5C,MAAM3C,EAASiB,EAAIjB,OACbC,EAAYgB,EAAIhB,UAEhB2C,EAAQ1C,EAAAA,WAAW2C,SAASJ,EAAID,EAAIN,GACpCY,EAAQ5C,EAAAA,WAAW2C,SAASH,EAAIF,EAAIL,GAEpC/F,EAAI8D,EAAAA,WAAW6C,MAAM9C,EAAW6C,EAAOV,GACvCY,EAAM9C,EAAUA,WAAC8B,IAAIY,EAAOxG,GAElC,IAAI6G,EACAnI,EAEAoI,EACAC,EACA9B,EAEJ,GAAIsB,EAAe,CACjB,GAAIK,EAAMpJ,EAAUA,WAACwJ,SACnB,OAKF,GAFAH,EAAO/C,EAAUA,WAAC2C,SAAS7C,EAAQwC,EAAIH,GACvCa,EAAIhD,EAAAA,WAAW8B,IAAIiB,EAAM7G,GACrB8G,EAAI,GAAOA,EAAIF,EACjB,OAMF,GAHAlI,EAAIoF,EAAUA,WAAC6C,MAAME,EAAML,EAAON,GAElCa,EAAIjD,EAAAA,WAAW8B,IAAI/B,EAAWnF,GAC1BqI,EAAI,GAAOD,EAAIC,EAAIH,EACrB,OAGF3B,EAAInB,EAAUA,WAAC8B,IAAIc,EAAOhI,GAAKkI,CACnC,KAAS,CACL,GAAIlJ,KAAKC,IAAIiJ,GAAOpJ,EAAAA,WAAWwJ,SAC7B,OAEF,MAAMC,EAAS,EAAML,EAIrB,GAFAC,EAAO/C,EAAUA,WAAC2C,SAAS7C,EAAQwC,EAAIH,GACvCa,EAAIhD,EAAUA,WAAC8B,IAAIiB,EAAM7G,GAAKiH,EAC1BH,EAAI,GAAOA,EAAI,EACjB,OAMF,GAHApI,EAAIoF,EAAUA,WAAC6C,MAAME,EAAML,EAAON,GAElCa,EAAIjD,EAAUA,WAAC8B,IAAI/B,EAAWnF,GAAKuI,EAC/BF,EAAI,GAAOD,EAAIC,EAAI,EACrB,OAGF9B,EAAInB,EAAUA,WAAC8B,IAAIc,EAAOhI,GAAKuI,CAChC,CAED,OAAOhC,CACT,EAmBAO,EAAkB0B,YAAc,SAC9BrC,EACAuB,EACAC,EACAC,EACAC,EACAzB,GAEA,MAAMG,EAAIO,EAAkBW,sBAC1BtB,EACAuB,EACAC,EACAC,EACAC,GAEF,GAAKxB,EAAAA,QAAQE,MAAMA,EAAI,GASvB,OALKF,EAAAA,QAAQD,KACXA,EAAS,IAAIhB,EAAAA,YAGfA,EAAUA,WAACwB,iBAAiBT,EAAIhB,UAAWoB,EAAGH,GACvChB,EAAAA,WAAWyB,IAAIV,EAAIjB,OAAQkB,EAAQA,EAC5C,EAEA,MAAMqC,EAAgC,IAAIxD,EAgB1C6B,EAAkB4B,oBAAsB,SACtCC,EACAC,EACAlB,EACAC,EACAC,EACAC,EACAzB,GAGA,IAAKC,EAAAA,QAAQsC,GACX,MAAM,IAAIpJ,EAAAA,eAAe,mBAE3B,IAAK8G,EAAAA,QAAQuC,GACX,MAAM,IAAIrJ,EAAAA,eAAe,mBAE3B,IAAK8G,EAAAA,QAAQqB,GACX,MAAM,IAAInI,EAAAA,eAAe,mBAE3B,IAAK8G,EAAAA,QAAQsB,GACX,MAAM,IAAIpI,EAAAA,eAAe,mBAE3B,IAAK8G,EAAAA,QAAQuB,GACX,MAAM,IAAIrI,EAAAA,eAAe,mBAI3B,MAAM4G,EAAMsC,EACZrD,EAAAA,WAAWC,MAAMsD,EAAIxC,EAAIjB,QACzBE,EAAUA,WAAC2C,SAASa,EAAID,EAAIxC,EAAIhB,WAChCC,EAAUA,WAACK,UAAUU,EAAIhB,UAAWgB,EAAIhB,WAExC,MAAMoB,EAAIO,EAAkBW,sBAC1BtB,EACAuB,EACAC,EACAC,EACAC,GAEF,MAAKxB,EAAOA,QAACE,IAAMA,EAAI,GAAOA,EAAInB,aAAW+B,SAASwB,EAAIC,IAS1D,OALKvC,EAAAA,QAAQD,KACXA,EAAS,IAAIhB,EAAAA,YAGfA,EAAUA,WAACwB,iBAAiBT,EAAIhB,UAAWoB,EAAGH,GACvChB,EAAAA,WAAWyB,IAAIV,EAAIjB,OAAQkB,EAAQA,EAC5C,EAgCA,MAAMyC,EAAiB,CACrBlF,MAAO,EACPrB,MAAO,GAGT,SAASwG,EAAU3C,EAAK4C,EAAQ3C,GACzBC,EAAAA,QAAQD,KACXA,EAAS,IAAI4C,EAAAA,UAGf,MAAM9D,EAASiB,EAAIjB,OACbC,EAAYgB,EAAIhB,UAEhB8D,EAASF,EAAOE,OAChBC,EAAgBH,EAAOI,OAASJ,EAAOI,OAEvCC,EAAOhE,EAAAA,WAAW2C,SAAS7C,EAAQ+D,EAAQ3B,GAM3CxE,EApDR,SAAwB1D,EAAGC,EAAGC,EAAG8G,GAC/B,MAAM8B,EAAM7I,EAAIA,EAAI,EAAMD,EAAIE,EAC9B,GAAI4I,EAAM,EACR,OACK,GAAIA,EAAM,EAAK,CACpB,MAAMmB,EAAQ,GAAO,EAAMjK,GACrBkK,EAAOtK,KAAKc,KAAKoI,GACjBvE,IAAUtE,EAAIiK,GAAQD,EACtB/G,IAAUjD,EAAIiK,GAAQD,EAU5B,OARI1F,EAAQrB,GACV8D,EAAOzC,MAAQA,EACfyC,EAAO9D,MAAQA,IAEf8D,EAAOzC,MAAQrB,EACf8D,EAAO9D,MAAQqB,GAGVyC,CACR,CAED,MAAMvG,GAAQR,GAAK,EAAMD,GACzB,OAAa,IAATS,GAIJuG,EAAOzC,MAAQyC,EAAO9D,MAAQzC,EACvBuG,QALP,CAMF,CAwBgBmD,CAJJnE,EAAUA,WAAC8B,IAAI/B,EAAWA,GAC1B,EAAMC,EAAAA,WAAW8B,IAAI/B,EAAWiE,GAChChE,EAAUA,WAACoE,iBAAiBJ,GAAQF,EAERL,GACtC,GAAKxC,EAAAA,QAAQvD,GAMb,OAFAsD,EAAOqD,MAAQ3G,EAAMa,MACrByC,EAAOsD,KAAO5G,EAAMR,MACb8D,CACT,CAWAU,EAAkBgC,UAAY,SAAU3C,EAAK4C,EAAQ3C,GAEnD,IAAKC,EAAAA,QAAQF,GACX,MAAM,IAAI5G,EAAAA,eAAe,oBAE3B,IAAK8G,EAAAA,QAAQ0C,GACX,MAAM,IAAIxJ,EAAAA,eAAe,uBAK3B,GADA6G,EAAS0C,EAAU3C,EAAK4C,EAAQ3C,GAC3BC,EAAOA,QAACD,MAAWA,EAAOsD,KAAO,GAKtC,OADAtD,EAAOqD,MAAQzK,KAAKE,IAAIkH,EAAOqD,MAAO,GAC/BrD,CACT,EAEA,MAAMuD,EAAwB,IAAI1E,EAYlC6B,EAAkB8C,kBAAoB,SAAUlC,EAAIC,EAAIoB,EAAQ3C,GAE9D,IAAKC,EAAAA,QAAQqB,GACX,MAAM,IAAInI,EAAAA,eAAe,mBAE3B,IAAK8G,EAAAA,QAAQsB,GACX,MAAM,IAAIpI,EAAAA,eAAe,mBAE3B,IAAK8G,EAAAA,QAAQ0C,GACX,MAAM,IAAIxJ,EAAAA,eAAe,uBAI3B,MAAM4G,EAAMwD,EACZvE,EAAAA,WAAWC,MAAMqC,EAAIvB,EAAIjB,QACzB,MAAMC,EAAYC,EAAAA,WAAW2C,SAASJ,EAAID,EAAIvB,EAAIhB,WAE5C0E,EAAOzE,EAAAA,WAAW0E,UAAU3E,GAIlC,GAHAC,EAAAA,WAAWK,UAAUN,EAAWA,GAEhCiB,EAAS0C,EAAU3C,EAAK4C,EAAQ3C,MAC3BC,EAAAA,QAAQD,IAAWA,EAAOsD,KAAO,GAAOtD,EAAOqD,MAAQI,GAM5D,OAFAzD,EAAOqD,MAAQzK,KAAKE,IAAIkH,EAAOqD,MAAO,GACtCrD,EAAOsD,KAAO1K,KAAK+K,IAAI3D,EAAOsD,KAAMG,GAC7BzD,CACT,EAEA,MAAM4D,EAAW,IAAI5E,EAAAA,WACf6E,EAAW,IAAI7E,EAAAA,WAuFrB,SAAS3G,EAAyBC,EAAMC,EAAOC,GAC7C,MAAMC,EAAaH,EAAOC,EAC1B,OACEG,EAAUA,WAACC,KAAKL,KAAUI,EAAAA,WAAWC,KAAKJ,IAC1CK,KAAKC,IAAIJ,EAAaG,KAAKE,IAAIF,KAAKC,IAAIP,GAAOM,KAAKC,IAAIN,KAAWC,EAE5D,EAGFC,CACT,CAxFAiI,EAAkBoD,aAAe,SAAU/D,EAAKgE,GAE9C,IAAK9D,EAAAA,QAAQF,GACX,MAAM,IAAI5G,EAAAA,eAAe,oBAE3B,IAAK8G,EAAAA,QAAQ8D,GACX,MAAM,IAAI5K,EAAAA,eAAe,0BAI3B,MAAM6K,EAAeD,EAAUE,aACzBrK,EAAIoF,EAAAA,WAAWkF,mBAAmBF,EAAcjE,EAAIjB,OAAQ8E,GAC5DO,EAAInF,EAAAA,WAAWkF,mBACnBF,EACAjE,EAAIhB,UACJ8E,GAGIO,EAAKpF,EAAAA,WAAWoE,iBAAiBxJ,GACjCyK,EAAKrF,EAAUA,WAAC8B,IAAIlH,EAAGuK,GAE7B,IAAI1L,EAAY6L,EAAIC,EAAS7J,EAAcC,EAE3C,GAAIyJ,EAAK,EAAK,CAEZ,GAAIC,GAAM,EAER,OAIF,MAAMG,EAAMH,EAAKA,EAKjB,GAJA5L,EAAa2L,EAAK,EAClBE,EAAKtF,EAAUA,WAACoE,iBAAiBe,GACjCI,EAAUD,EAAK7L,EAEX+L,EAAMD,EAER,OACK,GAAIC,EAAMD,EAAS,CAExB7J,EAAe2J,EAAKA,EAAKE,EACzB5J,GAAQ0J,EAAKzL,KAAKc,KAAKgB,GACvB,MAAM6C,EAAQ5C,EAAO2J,EACfpI,EAAQzD,EAAakC,EAC3B,OAAI4C,EAAQrB,EACH,IAAI0G,EAAQA,SAACrF,EAAOrB,GAGtB,CACLmH,MAAOnH,EACPoH,KAAM/F,EAET,CAED,MAAM9D,EAAOb,KAAKc,KAAKjB,EAAa6L,GACpC,OAAO,IAAI1B,EAAQA,SAACnJ,EAAMA,EAC9B,CAAS,OAAI2K,EAAK,GAEd3L,EAAa2L,EAAK,EAClBE,EAAKtF,EAAUA,WAACoE,iBAAiBe,GACjCI,EAAUD,EAAK7L,EAEfiC,EAAe2J,EAAKA,EAAKE,EACzB5J,GAAQ0J,EAAKzL,KAAKc,KAAKgB,GAChB,IAAIkI,EAAAA,SAAS,EAAKjI,EAAO2J,IAG9BD,EAAK,GAEPC,EAAKtF,EAAUA,WAACoE,iBAAiBe,GAC1B,IAAIvB,EAAAA,SAAS,GAAMyB,EAAKC,SAHjC,CAQF,EA8IA,MAAMG,EAAmB,IAAIzF,EAAAA,WACvB0F,EAAoB,IAAI1F,EAAAA,WACxB2F,EAAmB,IAAI3F,EAAAA,WACvB4F,EAAmB,IAAI5F,EAAAA,WACvB6F,EAAQ,IAAI7F,EAAAA,WACZ8F,EAAW,IAAIC,EAAAA,QACfC,EAAY,IAAID,EAAAA,QAChBE,EAAY,IAAIF,EAAAA,QAChBG,EAAW,IAAIH,EAAAA,QACfI,EAAW,IAAIJ,EAAAA,QACfK,EAAa,IAAIL,EAAAA,QACjBM,EAAW,IAAIN,EAAAA,QACfO,EAAW,IAAItG,EAAAA,WACfuG,EAAiB,IAAIvG,EAAAA,WACrBwG,EAAmB,IAAIC,EAAAA,aAS7B/E,EAAkBgF,wBAA0B,SAAU3F,EAAKgE,GAEzD,IAAK9D,EAAAA,QAAQF,GACX,MAAM,IAAI5G,EAAAA,eAAe,oBAE3B,IAAK8G,EAAAA,QAAQ8D,GACX,MAAM,IAAI5K,EAAAA,eAAe,0BAI3B,MAAMwM,EAAW5F,EAAIjB,OACfC,EAAYgB,EAAIhB,UAEtB,IAAKC,EAAUA,WAACI,OAAOuG,EAAU3G,EAAUA,WAACG,MAAO,CACjD,MAAMyB,EAASmD,EAAU6B,sBAAsBD,EAAUlB,GACzD,GAAIzF,EAAUA,WAAC8B,IAAI/B,EAAW6B,IAAW,EAEvC,OAAO+E,CAEV,CAED,MAAME,EAAa5F,EAAAA,QAAQX,KAAKwE,aAAa/D,EAAKgE,IAG5C+B,EAAI/B,EAAUgC,+BAClBhH,EACA0F,GAIIuB,EAAYhH,EAAUA,WAACK,UAAUyG,EAAGA,GACpCG,EAAYjH,EAAUA,WAACkH,mBAAmBJ,EAAGlB,GAC7CuB,EAAanH,EAAAA,WAAWK,UAC5BL,EAAAA,WAAW6C,MAAMoE,EAAWD,EAAWtB,GACvCA,GAEI0B,EAAYpH,EAAAA,WAAWK,UAC3BL,EAAAA,WAAW6C,MAAMmE,EAAWG,EAAYxB,GACxCA,GAEI3K,EAAI8K,EACV9K,EAAE,GAAKgM,EAAU/K,EACjBjB,EAAE,GAAKgM,EAAUxI,EACjBxD,EAAE,GAAKgM,EAAUK,EACjBrM,EAAE,GAAKmM,EAAWlL,EAClBjB,EAAE,GAAKmM,EAAW3I,EAClBxD,EAAE,GAAKmM,EAAWE,EAClBrM,EAAE,GAAKoM,EAAUnL,EACjBjB,EAAE,GAAKoM,EAAU5I,EACjBxD,EAAE,GAAKoM,EAAUC,EAEjB,MAAMC,EAAMvB,EAAOA,QAACwB,UAAUvM,EAAGgL,GAG3BwB,EAAMzB,EAAAA,QAAQ0B,UAAU1C,EAAU2C,MAAOzB,GACzC/K,EAAI6K,EAAAA,QAAQ0B,UAAU1C,EAAUE,aAAciB,GAE9CjL,EAAIkL,EACVlL,EAAE,GAAK,EACPA,EAAE,IAAM8E,EAAUsH,EAClBpM,EAAE,GAAK8E,EAAUvB,EACjBvD,EAAE,GAAK8E,EAAUsH,EACjBpM,EAAE,GAAK,EACPA,EAAE,IAAM8E,EAAU9D,EAClBhB,EAAE,IAAM8E,EAAUvB,EAClBvD,EAAE,GAAK8E,EAAU9D,EACjBhB,EAAE,GAAK,EAEP,MAAMU,EAAOoK,EAAAA,QAAQ4B,SACnB5B,EAAAA,QAAQ4B,SAASL,EAAKpM,EAAGkL,GACzBnL,EACAmL,GAEIrL,EAAIgL,EAAAA,QAAQ4B,SAChB5B,EAAAA,QAAQ4B,SAAShM,EAAM6L,EAAKnB,GAC5BrL,EACAqL,GAEIpM,EAAI8L,EAAAA,QAAQ6B,iBAAiBjM,EAAMgL,EAAUd,GAG7CgC,EAxOR,SAAmC9M,EAAGd,EAAGC,EAAG+B,EAAGkJ,GAC7C,MAAM2C,EAAW7L,EAAIA,EACf8L,EAAW5C,EAAIA,EAEf6C,GAAMjN,EAAEgL,UAAQkC,aAAelN,EAAEgL,EAAOA,QAACmC,cAAgBH,EACzDI,EACJhD,GACClJ,EACC5C,EACE0B,EAAEgL,EAAOA,QAACqC,aACVrN,EAAEgL,EAAOA,QAACsC,aACV3O,EAAAA,WAAWmH,WAEb5G,EAAEuE,GACA8J,EACJvN,EAAEgL,EAAAA,QAAQwC,aAAeT,EACzB/M,EAAEgL,EAAAA,QAAQmC,aAAeH,EACzB9L,EAAIhC,EAAEgC,EACN/B,EAEIsO,EACJT,EACA1O,EACE0B,EAAEgL,EAAOA,QAAC0C,aACV1N,EAAEgL,EAAOA,QAAC2C,aACVhP,EAAAA,WAAWmH,WAET8H,EACJxD,GACClJ,EACC5C,EAAyB0B,EAAEgL,UAAQ6C,aAAc7N,EAAEgL,EAAAA,QAAQ8C,cAC3D5O,EAAEoN,GAEN,IAAIyB,EACJ,MAAMjB,EAAY,GAClB,GAAW,IAAPc,GAAqB,IAAPH,EAAY,CAE5B,GADAM,EAAU1P,EAAwBgB,iBAAiB4N,EAAIG,EAAIG,GACpC,IAAnBQ,EAAQlL,OACV,OAAOiK,EAGT,MAAMkB,EAAUD,EAAQ,GAClBE,EAAQpP,KAAKc,KAAKd,KAAKE,IAAI,EAAMiP,EAAUA,EAAS,IAI1D,GAHAlB,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAI4D,EAAS5D,GAAK6D,IACnDnB,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAI4D,EAAS5D,EAAI6D,IAE3B,IAAnBF,EAAQlL,OAAc,CACxB,MAAMsL,EAAUJ,EAAQ,GAClBK,EAAQvP,KAAKc,KAAKd,KAAKE,IAAI,EAAMoP,EAAUA,EAAS,IAC1DrB,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAI+D,EAAS/D,GAAKgE,IACnDtB,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAI+D,EAAS/D,EAAIgE,GACnD,CAED,OAAOtB,CACR,CAED,MAAMuB,EAAYT,EAAKA,EACjBU,EAAYb,EAAKA,EAEjBc,EAAOX,EAAKH,EAEZe,EAHYvB,EAAKA,EAGAqB,EACjB5I,EAAK,GAAO0H,EAAKH,EAAKsB,GACtB7L,EAAK,EAAM6K,EAAKN,EAAKG,EAAKA,EAAKkB,EAAYD,EAC3CI,EAAK,GAAOlB,EAAKH,EAAKmB,GACtBG,EAAKnB,EAAKA,EAAKc,EAErB,GAAW,IAAPG,GAAqB,IAAP9I,GAAqB,IAAPhD,GAAqB,IAAP+L,EAC5C,OAAO3B,EAGTiB,EAAUjL,EAAsBzD,iBAAiBmP,EAAI9I,EAAIhD,EAAI+L,EAAIC,GACjE,MAAM7L,EAASkL,EAAQlL,OACvB,GAAe,IAAXA,EACF,OAAOiK,EAGT,IAAK,IAAI6B,EAAI,EAAGA,EAAI9L,IAAU8L,EAAG,CAC/B,MAAM9M,EAASkM,EAAQY,GACjBC,EAAgB/M,EAASA,EACzBgN,EAAchQ,KAAKE,IAAI,EAAM6P,EAAe,GAC5CE,EAAOjQ,KAAKc,KAAKkP,GAGvB,IAAItQ,EAEFA,EADEI,EAAAA,WAAWC,KAAKqO,KAAQtO,EAAAA,WAAWC,KAAK2O,GACnCjP,EACL2O,EAAK2B,EAAgBrB,EACrBH,EAAKvL,EACLlD,EAAAA,WAAWoQ,WAEJpQ,EAAAA,WAAWC,KAAK2O,KAAQ5O,EAAAA,WAAWC,KAAKwO,EAAKvL,GAC/CvD,EACL2O,EAAK2B,EACLxB,EAAKvL,EAAS0L,EACd5O,EAAAA,WAAWoQ,WAGNzQ,EACL2O,EAAK2B,EAAgBxB,EAAKvL,EAC1B0L,EACA5O,EAAAA,WAAWoQ,WAIf,MAKMvE,EAAUjM,EALFD,EACZmP,EAAK5L,EACL+L,EACAjP,EAAAA,WAAWmH,WAIT0E,EAAU,EACZsC,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAIvI,EAAQuI,EAAI0E,IACxCtE,EAAU,EACnBsC,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAIvI,EAAQuI,GAAK0E,IAChC,IAATA,GACThC,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAIvI,EAAQuI,GAAK0E,IAClDhC,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAIvI,EAAQuI,EAAI0E,MAC/CH,GAEF7B,EAAUoB,KAAK,IAAIjJ,EAAAA,WAAW/D,EAAGkJ,EAAIvI,EAAQuI,EAAI0E,GAEpD,CAED,OAAOhC,CACT,CA0GoBkC,CAChBhP,EACAiF,aAAWgK,OAAO/P,EAAGwL,GACrB,EACA,EACA,GAGF,IAAIwE,EACAC,EACJ,MAAMtM,EAASiK,EAAUjK,OACzB,GAAIA,EAAS,EAAG,CACd,IAAIuM,EAAUnK,EAAAA,WAAWC,MAAMD,EAAAA,WAAWG,KAAMoG,GAC5C6D,EAAeC,OAAOC,kBAE1B,IAAK,IAAIZ,EAAI,EAAGA,EAAI9L,IAAU8L,EAAG,CAC/BO,EAAIlE,EAAOA,QAAC6B,iBACVJ,EACAzB,EAAOA,QAAC6B,iBAAiB5M,EAAG6M,EAAU6B,GAAIpD,GAC1CA,GAEF,MAAMrD,EAAIjD,EAAAA,WAAWK,UACnBL,EAAAA,WAAW2C,SAASsH,EAAGtD,EAAUf,GACjCA,GAEI2E,EAAavK,EAAUA,WAAC8B,IAAImB,EAAGlD,GAEjCwK,EAAaH,IACfA,EAAeG,EACfJ,EAAUnK,EAAAA,WAAWC,MAAMgK,EAAGE,GAEjC,CAED,MAAMK,EAAezF,EAAU0F,wBAC7BN,EACA3D,GASF,OAPA4D,EAAe1Q,EAAUA,WAACgR,MAAMN,EAAc,EAAK,GACnDF,EACElK,EAAAA,WAAW0E,UACT1E,EAAAA,WAAW2C,SAASwH,EAASxD,EAAUf,IACrChM,KAAKc,KAAK,EAAM0P,EAAeA,GACrCF,EAAWrD,GAAcqD,EAAWA,EACpCM,EAAaG,OAAST,EACfnF,EAAU6F,wBAAwBJ,EAAc,IAAIxK,EAAAA,WAC5D,CAGH,EAEA,MAAM6K,EAA6B,IAAI7K,EAAAA,WAsBvC0B,EAAkBoJ,iBAAmB,SACnCC,EACAC,EACArJ,EACAX,GAGA,IAAKC,EAAAA,QAAQ8J,GACX,MAAM,IAAI5Q,EAAAA,eAAe,0BAE3B,IAAK8G,EAAAA,QAAQ+J,GACX,MAAM,IAAI7Q,EAAAA,eAAe,0BAE3B,IAAK8G,EAAAA,QAAQU,GACX,MAAM,IAAIxH,EAAAA,eAAe,sBAItB8G,EAAAA,QAAQD,KACXA,EAAS,IAAIhB,EAAAA,YAGf,MAAMvG,EAAauG,EAAAA,WAAW2C,SAC5BqI,EACAD,EACAF,GAEIjJ,EAASD,EAAMC,OACfqJ,EAAWjL,EAAUA,WAAC8B,IAAIF,EAAQnI,GAGxC,GAAIG,KAAKC,IAAIoR,GAAYvR,EAAAA,WAAWwJ,SAClC,OAGF,MAAMgI,EAASlL,EAAUA,WAAC8B,IAAIF,EAAQmJ,GAChC5J,IAAMQ,EAAMI,SAAWmJ,GAAUD,EAGvC,OAAI9J,EAAI,GAAOA,EAAI,OAAnB,GAKAnB,EAAAA,WAAWwB,iBAAiB/H,EAAY0H,EAAGH,GAC3ChB,EAAAA,WAAWyB,IAAIsJ,EAAW/J,EAAQA,GAC3BA,EACT,EAuBAU,EAAkByJ,0BAA4B,SAAU7I,EAAIC,EAAIC,EAAIb,GAElE,KAAKV,EAAAA,QAAQqB,IAAQrB,EAAOA,QAACsB,IAAQtB,EAAAA,QAAQuB,IAAQvB,EAAOA,QAACU,IAC3D,MAAM,IAAIxH,EAAAA,eAAe,uCAI3B,MAAMiR,EAAczJ,EAAMC,OACpByJ,EAAS1J,EAAMI,SACfuJ,EAAWtL,EAAAA,WAAW8B,IAAIsJ,EAAa9I,GAAM+I,EAAS,EACtDE,EAAWvL,EAAAA,WAAW8B,IAAIsJ,EAAa7I,GAAM8I,EAAS,EACtDG,EAAWxL,EAAAA,WAAW8B,IAAIsJ,EAAa5I,GAAM6I,EAAS,EAI5D,IAKII,EAAIC,EALJC,EAAY,EAWhB,GAVAA,GAAaL,EAAW,EAAI,EAC5BK,GAAaJ,EAAW,EAAI,EAC5BI,GAAaH,EAAW,EAAI,EAGV,IAAdG,GAAiC,IAAdA,IACrBF,EAAK,IAAIzL,EAAAA,WACT0L,EAAK,IAAI1L,EAAAA,YAGO,IAAd2L,EAAiB,CACnB,GAAIL,EAIF,OAHA5J,EAAkBoJ,iBAAiBxI,EAAIC,EAAIZ,EAAO8J,GAClD/J,EAAkBoJ,iBAAiBxI,EAAIE,EAAIb,EAAO+J,GAE3C,CACLE,UAAW,CAACtJ,EAAIC,EAAIC,EAAIiJ,EAAIC,GAC5BG,QAAS,CAEP,EACA,EACA,EAGA,EACA,EACA,EACA,EACA,EACA,IAGC,GAAIN,EAIT,OAHA7J,EAAkBoJ,iBAAiBvI,EAAIC,EAAIb,EAAO8J,GAClD/J,EAAkBoJ,iBAAiBvI,EAAID,EAAIX,EAAO+J,GAE3C,CACLE,UAAW,CAACtJ,EAAIC,EAAIC,EAAIiJ,EAAIC,GAC5BG,QAAS,CAEP,EACA,EACA,EAGA,EACA,EACA,EACA,EACA,EACA,IAGC,GAAIL,EAIT,OAHA9J,EAAkBoJ,iBAAiBtI,EAAIF,EAAIX,EAAO8J,GAClD/J,EAAkBoJ,iBAAiBtI,EAAID,EAAIZ,EAAO+J,GAE3C,CACLE,UAAW,CAACtJ,EAAIC,EAAIC,EAAIiJ,EAAIC,GAC5BG,QAAS,CAEP,EACA,EACA,EAGA,EACA,EACA,EACA,EACA,EACA,GAIV,MAAS,GAAkB,IAAdF,EAAiB,CAC1B,IAAKL,EAIH,OAHA5J,EAAkBoJ,iBAAiBvI,EAAID,EAAIX,EAAO8J,GAClD/J,EAAkBoJ,iBAAiBtI,EAAIF,EAAIX,EAAO+J,GAE3C,CACLE,UAAW,CAACtJ,EAAIC,EAAIC,EAAIiJ,EAAIC,GAC5BG,QAAS,CAEP,EACA,EACA,EACA,EACA,EACA,EAGA,EACA,EACA,IAGC,IAAKN,EAIV,OAHA7J,EAAkBoJ,iBAAiBtI,EAAID,EAAIZ,EAAO8J,GAClD/J,EAAkBoJ,iBAAiBxI,EAAIC,EAAIZ,EAAO+J,GAE3C,CACLE,UAAW,CAACtJ,EAAIC,EAAIC,EAAIiJ,EAAIC,GAC5BG,QAAS,CAEP,EACA,EACA,EACA,EACA,EACA,EAGA,EACA,EACA,IAGC,IAAKL,EAIV,OAHA9J,EAAkBoJ,iBAAiBxI,EAAIE,EAAIb,EAAO8J,GAClD/J,EAAkBoJ,iBAAiBvI,EAAIC,EAAIb,EAAO+J,GAE3C,CACLE,UAAW,CAACtJ,EAAIC,EAAIC,EAAIiJ,EAAIC,GAC5BG,QAAS,CAEP,EACA,EACA,EACA,EACA,EACA,EAGA,EACA,EACA,GAIP,CAKH"}