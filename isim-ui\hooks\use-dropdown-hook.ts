/**
 * @Author: 宋计民
 * @Date: 2023-09-12 17:14
 * @Version: 1.0
 * @Content: use-dropdown-hook.ts
 */
import { useBooleanStateHook } from './use-boolean-state';

export function useDropdownHook() {
  const { state: isShow, setState, changeState } = useBooleanStateHook();
  const handleRef = ref();
  const outClick = (e: MouseEvent) => {
    const target = e.target as HTMLDivElement;
    const box = document.querySelector('.n-dropdown-menu');
    if (handleRef.value?.contains(target) || box?.contains(target)) {
      return;
    }
    setState(false);
  };
  document.addEventListener('mousedown', outClick);
  document.addEventListener('dblclick', outClick);
  onScopeDispose(() => {
    document.removeEventListener('click', outClick);
  });
  return {
    isShow,
    handleRef,
    setIsShow: setState,
    changeState
  };
}
