import { defined, destroyObject, KeyboardEventModifier, ScreenSpaceEventHandler, ScreenSpaceEventType, Viewer } from 'cesium';

interface AbstractHandlerOptionType {
  viewer: Viewer;
  handler?: ScreenSpaceEventHandler;
}

export default class AbstractHandler {
  private readonly _viewer: Viewer;
  private _handler?: ScreenSpaceEventHandler;
  private _globalEvents: Record<string, any> | undefined;
  private _depthTestAgainstTerrain: boolean;
  private readonly _signer: boolean;
  private readonly _removalActions: any[];
  private readonly _originalActions: any[];
  constructor(options: AbstractHandlerOptionType) {
    this._viewer = options.viewer;
    this._handler = options.handler;
    this._globalEvents = undefined;
    this._depthTestAgainstTerrain = false;

    this._signer = defined(this._handler);
    this._removalActions = [];
    this._originalActions = [];
  }

  createHandler() {
    const scene = this._viewer.scene;
    if (!scene.pickPositionSupported) {
      window.alert('This browser does not support pickPosition.');
    }
    if (!this._signer) {
      this._handler = new ScreenSpaceEventHandler(scene.canvas);
      this._setGlobalHandler();
    }
  }

  setInputAction(action: any, type: ScreenSpaceEventType, modifier: KeyboardEventModifier) {
    const handler = this._handler;
    const inputEvents = handler?._inputEvents;

    if (defined(modifier)) {
      const id = `${type}+${modifier}`;
      if (inputEvents?.[id]) {
        this._originalActions.push([inputEvents[id], type, modifier]);
      }
      handler?.setInputAction(action, type, modifier);
      this._removalActions.push([action, type, modifier]);
    } else {
      if (inputEvents?.[type]) {
        this._originalActions.push([inputEvents[type], type]);
      }

      handler?.setInputAction(action, type, modifier);
      this._removalActions.push([action, type]);
    }
  }

  removeInputAction() {
    let actions = this._removalActions;
    if (actions.length > 0) {
      actions.forEach((item) => {
        if (item.length === 3) {
          this._handler?.removeInputAction(item[1], item[2]);
        } else {
          this._handler?.removeInputAction(item[1]);
        }
      });
      actions.length = 0;
    }

    actions = this._originalActions;
    if (actions.length > 0) {
      actions.forEach((item) => {
        if (item.length === 3) {
          this._handler?.setInputAction(item[0], item[1], item[2]);
        } else {
          this._handler?.setInputAction(item[0], item[1]);
        }
      });
      actions.length = 0;
    }
  }

  _setGlobalHandler() {
    const globe = this._viewer.scene.globe;
    this._depthTestAgainstTerrain = globe.depthTestAgainstTerrain;
    globe.depthTestAgainstTerrain = true;

    const screenSpaceEventHandler = this._viewer.cesiumWidget.screenSpaceEventHandler;
    this._globalEvents = screenSpaceEventHandler._inputEvents;
    screenSpaceEventHandler._inputEvents = {};
  }

  _resetGlobalHandler() {
    const globe = this._viewer.scene.globe;
    globe.depthTestAgainstTerrain = this._depthTestAgainstTerrain;

    const screenSpaceEventHandler = this._viewer.cesiumWidget.screenSpaceEventHandler;
    screenSpaceEventHandler._inputEvents = this._globalEvents;
    this._globalEvents = undefined;
  }

  destroyHandler() {
    if (!this._signer) {
      this._handler && this._handler.destroy();
      this._resetGlobalHandler();
    } else {
      this.removeInputAction();
    }
  }

  isDestroyed() {
    return false;
  }

  destroy() {
    this.destroyHandler();
    return destroyObject(this);
  }

  get handler() {
    return this._handler;
  }

  get viewer() {
    return this._viewer;
  }
}
