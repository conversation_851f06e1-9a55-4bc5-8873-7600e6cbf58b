<!--
* @Author: 宋计民
* @Date: 2024/1/24
* @Version: 1.0
* @Content: attribute-table-item.vue
-->
<template>
  <div class="attribute-item">
    <span class="attribute-item__label">
      <slot name="label">
        {{ label }}
      </slot>
    </span>
    <span class="attribute-item__value">
      <slot>
        <template v-if="!attributeTableProps.allowEdit">
          {{ value ?? attributeTableProps.data[prop ?? ''] }}
        </template>
        <el-input v-else v-model="attributeTableProps.data[prop ?? '']" />
      </slot>
    </span>
  </div>
</template>

<script setup lang="ts">
import { attributeTableInjectKey, AttributeTablePropsType } from './attribute-table-data.ts';

defineOptions({
  name: 'SimAttributeItem'
});
const attributeTableProps = inject(attributeTableInjectKey) as AttributeTablePropsType;
defineProps<{
  label: string;
  value?: string | number;
  prop?: string;
}>();
</script>

<style scoped lang="less">
.attribute-item {
  display: flex;
  --labelWidth: v-bind('attributeTableProps.labelWidth');
  & + & {
    margin-top: 1px;
  }
  &__label,
  &__value {
    display: inline-block;
    padding: 4px 8px;
    box-sizing: border-box;
  }
  &__label {
    background-color: var(--table-bg-color-odd);
    width: var(--labelWidth);
    margin-right: 1px;
  }
  &__value {
    width: calc(100% - var(--labelWidth) - 1px);
    background-color: var(--alpha-bg-color);
  }
}
</style>
