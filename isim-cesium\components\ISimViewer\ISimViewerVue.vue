<!--
* <AUTHOR> 宋计民
* @Date :  2023/3/18
* @Version : 1.0
* @Content : Viewer
-->
<template>
  <div :id="name" class="sim-viewer">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
import { createViewer, setDefaultCamera, resetCameraController, destroyViewer, defaultConfig, CreateViewerType, getViewerName } from 'isim-cesium';
import { UrlTemplateImageryProvider, Viewer } from 'cesium';

defineOptions({
  name: 'SimViewer'
});

const emits = defineEmits(['viewer-created']);

const props = defineProps({
  name: {
    type: String,
    default: 'cesium-box'
  },
  eventList: {
    type: Array as PropType<Function[]>,
    default: () => []
  },
  defaultViewerConfig: {
    type: Object as PropType<CreateViewerType>,
    default: () => undefined
  },
  defaultCamera: {
    type: Object as PropType<{
      east: number;
      south: number;
      west: number;
      north: number;
    }>,
    default: () => ({
      west: 90,
      south: 10,
      east: 120,
      north: 40
    })
  }
});

// 设置相机默认视角
setDefaultCamera(props.defaultCamera);

const viewerInstance = shallowRef<Viewer | null>();
provide(props.name, viewerInstance);
onMounted(() => {
  const viewer = createViewer(getViewerName(props.name), {
    imageryProvider: new UrlTemplateImageryProvider(defaultConfig.defaultImageryUrl),
    ...props.defaultViewerConfig
  });
  viewerInstance.value = viewer;
  // 重置相机操作
  resetCameraController(viewer);
  props.eventList?.forEach((fn) => fn(viewer));
  emits('viewer-created');
});
onBeforeUnmount(() => {
  viewerInstance.value = null;
  destroyViewer();
});
</script>
<style scoped lang="less">
.sim-viewer {
  position: relative;
  height: calc(100% - var(--header-height, 0));
}
:slotted(div) {
  position: relative;
  z-index: 10;
}

:global(.cesium-viewer) {
  position: absolute;
  top: 0;
  left: 0;
}
</style>
