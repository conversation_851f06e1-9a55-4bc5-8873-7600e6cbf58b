/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/08/09 18:47:32
 * @description MinePrimitive
 * @version 1.0
 * */
import AbstractPrimitive from '../AbstractVisualizer.js';
import {
  BillboardCollection,
  defaultValue,
  DistanceDisplayCondition,
  HeightReference,
  Matrix4,
  Model,
  SceneMode,
  Transforms,
  HeadingPitchRoll
} from 'cesium';

const BILLBOARD_OPTIONS = {
  scale: 1 / 5,
  distanceDisplayCondition: new DistanceDisplayCondition(0, 1000)
};

export default class MinePrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._position = options.position;
    this._heightReference = defaultValue(options.heightReference, HeightReference.NONE);
    this._heading = defaultValue(options.heading, 0);
    this._scene = options.scene;
    this._url = options.url;
    this._image = options.image;
    this._imageOptions = {
      ...BILLBOARD_OPTIONS,
      ...options.imageOptions
    };
    this._modelMatrix = new Matrix4();
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive3D = this._primitive3D && this._primitive3D.destroy();
      this._primitive2D = this._primitive2D && this._primitive2D.destroy();
      this._update3D();
      this._update2D();
    }

    if (frameState.mode === SceneMode.SCENE3D) {
      this._primitive3D?.update(frameState);
    } else if (frameState.mode === SceneMode.SCENE2D) {
      this._primitive2D?.update(frameState);
    }
  }

  _update3D() {
    if (!this._position) {
      return;
    }
    Transforms.headingPitchRollToFixedFrame(
      this._position,
      HeadingPitchRoll.fromDegrees(this._heading, 0, 0),
      undefined,
      undefined,
      this._modelMatrix
    );
    if (this._url) {
      this._primitive3D = Model.fromGltf({
        url: this._url,
        heightReference: this._heightReference,
        scene: this._scene,
        modelMatrix: this._modelMatrix,
        minimumPixelSize: 10
      });
    }
  }

  _update2D() {
    if (!this._position) {
      return;
    }
    this._primitive2D = new BillboardCollection();
    this._primitive2D.add({
      position: this._position,
      image: this._image,
      ...this._imageOptions
    });
  }
}
