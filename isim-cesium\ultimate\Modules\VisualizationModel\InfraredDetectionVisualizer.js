import { Color } from 'cesium';
import VisibleLightDetectionPrimitive from './VisibleLightDetectionVisualizer.js';

export default class InfraredDetectionPrimitive extends VisibleLightDetectionPrimitive {
  constructor(options) {
    super(options);
    this._fadeInColor = Color.fromCssColorString('#b0037e').withAlpha(0.3);
    this._fadeOutColor = Color.fromCssColorString('#ff7c7c').withAlpha(0.8);
    // 红外线成像效果
    // this._fadeInColor = Color.fromCssColorString('#1a4a0f').withAlpha(0.3);
    // this._fadeOutColor = Color.fromCssColorString('#77de54').withAlpha(0.8);
  }
}
