/**
 * @Author: 宋计民
 * @Date: 2023/6/5 16:40
 * @Version: 1.0
 * @Content: 全局页面配置文件
 */
import { ServerOptions, ProxyOptions } from 'vite';

function getProxyObj(address: string, url: string, redirect = false) {
  const conf: ProxyOptions = {
    target: url,
    ws: true,
    changeOrigin: true
    //rewrite: (path: string) => path.replace(new RegExp(`^${address}`), '')
  };
  if (!redirect) {
    conf.rewrite = (path: string) => path.replace(new RegExp(`^${address}`), '');
  }
  return {
    [address]: conf
  };
}

export const clientDevProxyApi: ServerOptions['proxy'] = {
  ...getProxyObj('/api', 'http://***********:60000', true),

  // 影像
  ...getProxyObj('/map', 'http://127.0.0.1:7000/map/'),
  ...getProxyObj('/terrain', 'http://***********:4173/terrain'),
  ...getProxyObj('/situation', 'http://localhost:8080')
};
