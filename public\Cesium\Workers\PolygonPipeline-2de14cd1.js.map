{"version": 3, "file": "PolygonPipeline-2de14cd1.js", "sources": ["../../../../Source/ThirdParty/earcut.js", "../../../../Source/Core/WindingOrder.js", "../../../../Source/Core/PolygonPipeline.js"], "sourcesContent": ["/* This file is automatically rebuilt by the Cesium build process. */\nvar earcut_1 = earcut;\nvar _default = earcut;\n\nfunction earcut(data, holeIndices, dim) {\n\n    dim = dim || 2;\n\n    var hasHoles = holeIndices && holeIndices.length,\n        outerLen = hasHoles ? holeIndices[0] * dim : data.length,\n        outerNode = linkedList(data, 0, outerLen, dim, true),\n        triangles = [];\n\n    if (!outerNode || outerNode.next === outerNode.prev) return triangles;\n\n    var minX, minY, maxX, maxY, x, y, invSize;\n\n    if (hasHoles) outerNode = eliminateHoles(data, holeIndices, outerNode, dim);\n\n    // if the shape is not too simple, we'll use z-order curve hash later; calculate polygon bbox\n    if (data.length > 80 * dim) {\n        minX = maxX = data[0];\n        minY = maxY = data[1];\n\n        for (var i = dim; i < outerLen; i += dim) {\n            x = data[i];\n            y = data[i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n        }\n\n        // minX, minY and invSize are later used to transform coords into integers for z-order calculation\n        invSize = Math.max(maxX - minX, maxY - minY);\n        invSize = invSize !== 0 ? 32767 / invSize : 0;\n    }\n\n    earcutLinked(outerNode, triangles, dim, minX, minY, invSize, 0);\n\n    return triangles;\n}\n\n// create a circular doubly linked list from polygon points in the specified winding order\nfunction linkedList(data, start, end, dim, clockwise) {\n    var i, last;\n\n    if (clockwise === (signedArea(data, start, end, dim) > 0)) {\n        for (i = start; i < end; i += dim) last = insertNode(i, data[i], data[i + 1], last);\n    } else {\n        for (i = end - dim; i >= start; i -= dim) last = insertNode(i, data[i], data[i + 1], last);\n    }\n\n    if (last && equals(last, last.next)) {\n        removeNode(last);\n        last = last.next;\n    }\n\n    return last;\n}\n\n// eliminate colinear or duplicate points\nfunction filterPoints(start, end) {\n    if (!start) return start;\n    if (!end) end = start;\n\n    var p = start,\n        again;\n    do {\n        again = false;\n\n        if (!p.steiner && (equals(p, p.next) || area(p.prev, p, p.next) === 0)) {\n            removeNode(p);\n            p = end = p.prev;\n            if (p === p.next) break;\n            again = true;\n\n        } else {\n            p = p.next;\n        }\n    } while (again || p !== end);\n\n    return end;\n}\n\n// main ear slicing loop which triangulates a polygon (given as a linked list)\nfunction earcutLinked(ear, triangles, dim, minX, minY, invSize, pass) {\n    if (!ear) return;\n\n    // interlink polygon nodes in z-order\n    if (!pass && invSize) indexCurve(ear, minX, minY, invSize);\n\n    var stop = ear,\n        prev, next;\n\n    // iterate through ears, slicing them one by one\n    while (ear.prev !== ear.next) {\n        prev = ear.prev;\n        next = ear.next;\n\n        if (invSize ? isEarHashed(ear, minX, minY, invSize) : isEar(ear)) {\n            // cut off the triangle\n            triangles.push(prev.i / dim | 0);\n            triangles.push(ear.i / dim | 0);\n            triangles.push(next.i / dim | 0);\n\n            removeNode(ear);\n\n            // skipping the next vertex leads to less sliver triangles\n            ear = next.next;\n            stop = next.next;\n\n            continue;\n        }\n\n        ear = next;\n\n        // if we looped through the whole remaining polygon and can't find any more ears\n        if (ear === stop) {\n            // try filtering points and slicing again\n            if (!pass) {\n                earcutLinked(filterPoints(ear), triangles, dim, minX, minY, invSize, 1);\n\n            // if this didn't work, try curing all small self-intersections locally\n            } else if (pass === 1) {\n                ear = cureLocalIntersections(filterPoints(ear), triangles, dim);\n                earcutLinked(ear, triangles, dim, minX, minY, invSize, 2);\n\n            // as a last resort, try splitting the remaining polygon into two\n            } else if (pass === 2) {\n                splitEarcut(ear, triangles, dim, minX, minY, invSize);\n            }\n\n            break;\n        }\n    }\n}\n\n// check whether a polygon node forms a valid ear with adjacent nodes\nfunction isEar(ear) {\n    var a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    // now make sure we don't have other points inside the potential ear\n    var ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox; min & max are calculated like this for speed\n    var x0 = ax < bx ? (ax < cx ? ax : cx) : (bx < cx ? bx : cx),\n        y0 = ay < by ? (ay < cy ? ay : cy) : (by < cy ? by : cy),\n        x1 = ax > bx ? (ax > cx ? ax : cx) : (bx > cx ? bx : cx),\n        y1 = ay > by ? (ay > cy ? ay : cy) : (by > cy ? by : cy);\n\n    var p = c.next;\n    while (p !== a) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) &&\n            area(p.prev, p, p.next) >= 0) return false;\n        p = p.next;\n    }\n\n    return true;\n}\n\nfunction isEarHashed(ear, minX, minY, invSize) {\n    var a = ear.prev,\n        b = ear,\n        c = ear.next;\n\n    if (area(a, b, c) >= 0) return false; // reflex, can't be an ear\n\n    var ax = a.x, bx = b.x, cx = c.x, ay = a.y, by = b.y, cy = c.y;\n\n    // triangle bbox; min & max are calculated like this for speed\n    var x0 = ax < bx ? (ax < cx ? ax : cx) : (bx < cx ? bx : cx),\n        y0 = ay < by ? (ay < cy ? ay : cy) : (by < cy ? by : cy),\n        x1 = ax > bx ? (ax > cx ? ax : cx) : (bx > cx ? bx : cx),\n        y1 = ay > by ? (ay > cy ? ay : cy) : (by > cy ? by : cy);\n\n    // z-order range for the current triangle bbox;\n    var minZ = zOrder(x0, y0, minX, minY, invSize),\n        maxZ = zOrder(x1, y1, minX, minY, invSize);\n\n    var p = ear.prevZ,\n        n = ear.nextZ;\n\n    // look for points inside the triangle in both directions\n    while (p && p.z >= minZ && n && n.z <= maxZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    // look for remaining points in decreasing z-order\n    while (p && p.z >= minZ) {\n        if (p.x >= x0 && p.x <= x1 && p.y >= y0 && p.y <= y1 && p !== a && p !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, p.x, p.y) && area(p.prev, p, p.next) >= 0) return false;\n        p = p.prevZ;\n    }\n\n    // look for remaining points in increasing z-order\n    while (n && n.z <= maxZ) {\n        if (n.x >= x0 && n.x <= x1 && n.y >= y0 && n.y <= y1 && n !== a && n !== c &&\n            pointInTriangle(ax, ay, bx, by, cx, cy, n.x, n.y) && area(n.prev, n, n.next) >= 0) return false;\n        n = n.nextZ;\n    }\n\n    return true;\n}\n\n// go through all polygon nodes and cure small local self-intersections\nfunction cureLocalIntersections(start, triangles, dim) {\n    var p = start;\n    do {\n        var a = p.prev,\n            b = p.next.next;\n\n        if (!equals(a, b) && intersects(a, p, p.next, b) && locallyInside(a, b) && locallyInside(b, a)) {\n\n            triangles.push(a.i / dim | 0);\n            triangles.push(p.i / dim | 0);\n            triangles.push(b.i / dim | 0);\n\n            // remove two nodes involved\n            removeNode(p);\n            removeNode(p.next);\n\n            p = start = b;\n        }\n        p = p.next;\n    } while (p !== start);\n\n    return filterPoints(p);\n}\n\n// try splitting polygon into two and triangulate them independently\nfunction splitEarcut(start, triangles, dim, minX, minY, invSize) {\n    // look for a valid diagonal that divides the polygon into two\n    var a = start;\n    do {\n        var b = a.next.next;\n        while (b !== a.prev) {\n            if (a.i !== b.i && isValidDiagonal(a, b)) {\n                // split the polygon in two by the diagonal\n                var c = splitPolygon(a, b);\n\n                // filter colinear points around the cuts\n                a = filterPoints(a, a.next);\n                c = filterPoints(c, c.next);\n\n                // run earcut on each half\n                earcutLinked(a, triangles, dim, minX, minY, invSize, 0);\n                earcutLinked(c, triangles, dim, minX, minY, invSize, 0);\n                return;\n            }\n            b = b.next;\n        }\n        a = a.next;\n    } while (a !== start);\n}\n\n// link every hole into the outer loop, producing a single-ring polygon without holes\nfunction eliminateHoles(data, holeIndices, outerNode, dim) {\n    var queue = [],\n        i, len, start, end, list;\n\n    for (i = 0, len = holeIndices.length; i < len; i++) {\n        start = holeIndices[i] * dim;\n        end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n        list = linkedList(data, start, end, dim, false);\n        if (list === list.next) list.steiner = true;\n        queue.push(getLeftmost(list));\n    }\n\n    queue.sort(compareX);\n\n    // process holes from left to right\n    for (i = 0; i < queue.length; i++) {\n        outerNode = eliminateHole(queue[i], outerNode);\n    }\n\n    return outerNode;\n}\n\nfunction compareX(a, b) {\n    return a.x - b.x;\n}\n\n// find a bridge between vertices that connects hole with an outer ring and and link it\nfunction eliminateHole(hole, outerNode) {\n    var bridge = findHoleBridge(hole, outerNode);\n    if (!bridge) {\n        return outerNode;\n    }\n\n    var bridgeReverse = splitPolygon(bridge, hole);\n\n    // filter collinear points around the cuts\n    filterPoints(bridgeReverse, bridgeReverse.next);\n    return filterPoints(bridge, bridge.next);\n}\n\n// David Eberly's algorithm for finding a bridge between hole and outer polygon\nfunction findHoleBridge(hole, outerNode) {\n    var p = outerNode,\n        hx = hole.x,\n        hy = hole.y,\n        qx = -Infinity,\n        m;\n\n    // find a segment intersected by a ray from the hole's leftmost point to the left;\n    // segment's endpoint with lesser x will be potential connection point\n    do {\n        if (hy <= p.y && hy >= p.next.y && p.next.y !== p.y) {\n            var x = p.x + (hy - p.y) * (p.next.x - p.x) / (p.next.y - p.y);\n            if (x <= hx && x > qx) {\n                qx = x;\n                m = p.x < p.next.x ? p : p.next;\n                if (x === hx) return m; // hole touches outer segment; pick leftmost endpoint\n            }\n        }\n        p = p.next;\n    } while (p !== outerNode);\n\n    if (!m) return null;\n\n    // look for points inside the triangle of hole point, segment intersection and endpoint;\n    // if there are no points found, we have a valid connection;\n    // otherwise choose the point of the minimum angle with the ray as connection point\n\n    var stop = m,\n        mx = m.x,\n        my = m.y,\n        tanMin = Infinity,\n        tan;\n\n    p = m;\n\n    do {\n        if (hx >= p.x && p.x >= mx && hx !== p.x &&\n                pointInTriangle(hy < my ? hx : qx, hy, mx, my, hy < my ? qx : hx, hy, p.x, p.y)) {\n\n            tan = Math.abs(hy - p.y) / (hx - p.x); // tangential\n\n            if (locallyInside(p, hole) &&\n                (tan < tanMin || (tan === tanMin && (p.x > m.x || (p.x === m.x && sectorContainsSector(m, p)))))) {\n                m = p;\n                tanMin = tan;\n            }\n        }\n\n        p = p.next;\n    } while (p !== stop);\n\n    return m;\n}\n\n// whether sector in vertex m contains sector in vertex p in the same coordinates\nfunction sectorContainsSector(m, p) {\n    return area(m.prev, m, p.prev) < 0 && area(p.next, m, m.next) < 0;\n}\n\n// interlink polygon nodes in z-order\nfunction indexCurve(start, minX, minY, invSize) {\n    var p = start;\n    do {\n        if (p.z === 0) p.z = zOrder(p.x, p.y, minX, minY, invSize);\n        p.prevZ = p.prev;\n        p.nextZ = p.next;\n        p = p.next;\n    } while (p !== start);\n\n    p.prevZ.nextZ = null;\n    p.prevZ = null;\n\n    sortLinked(p);\n}\n\n// Simon Tatham's linked list merge sort algorithm\n// http://www.chiark.greenend.org.uk/~sgtatham/algorithms/listsort.html\nfunction sortLinked(list) {\n    var i, p, q, e, tail, numMerges, pSize, qSize,\n        inSize = 1;\n\n    do {\n        p = list;\n        list = null;\n        tail = null;\n        numMerges = 0;\n\n        while (p) {\n            numMerges++;\n            q = p;\n            pSize = 0;\n            for (i = 0; i < inSize; i++) {\n                pSize++;\n                q = q.nextZ;\n                if (!q) break;\n            }\n            qSize = inSize;\n\n            while (pSize > 0 || (qSize > 0 && q)) {\n\n                if (pSize !== 0 && (qSize === 0 || !q || p.z <= q.z)) {\n                    e = p;\n                    p = p.nextZ;\n                    pSize--;\n                } else {\n                    e = q;\n                    q = q.nextZ;\n                    qSize--;\n                }\n\n                if (tail) tail.nextZ = e;\n                else list = e;\n\n                e.prevZ = tail;\n                tail = e;\n            }\n\n            p = q;\n        }\n\n        tail.nextZ = null;\n        inSize *= 2;\n\n    } while (numMerges > 1);\n\n    return list;\n}\n\n// z-order of a point given coords and inverse of the longer side of data bbox\nfunction zOrder(x, y, minX, minY, invSize) {\n    // coords are transformed into non-negative 15-bit integer range\n    x = (x - minX) * invSize | 0;\n    y = (y - minY) * invSize | 0;\n\n    x = (x | (x << 8)) & 0x00FF00FF;\n    x = (x | (x << 4)) & 0x0F0F0F0F;\n    x = (x | (x << 2)) & 0x33333333;\n    x = (x | (x << 1)) & 0x55555555;\n\n    y = (y | (y << 8)) & 0x00FF00FF;\n    y = (y | (y << 4)) & 0x0F0F0F0F;\n    y = (y | (y << 2)) & 0x33333333;\n    y = (y | (y << 1)) & 0x55555555;\n\n    return x | (y << 1);\n}\n\n// find the leftmost node of a polygon ring\nfunction getLeftmost(start) {\n    var p = start,\n        leftmost = start;\n    do {\n        if (p.x < leftmost.x || (p.x === leftmost.x && p.y < leftmost.y)) leftmost = p;\n        p = p.next;\n    } while (p !== start);\n\n    return leftmost;\n}\n\n// check if a point lies within a convex triangle\nfunction pointInTriangle(ax, ay, bx, by, cx, cy, px, py) {\n    return (cx - px) * (ay - py) >= (ax - px) * (cy - py) &&\n           (ax - px) * (by - py) >= (bx - px) * (ay - py) &&\n           (bx - px) * (cy - py) >= (cx - px) * (by - py);\n}\n\n// check if a diagonal between two polygon nodes is valid (lies in polygon interior)\nfunction isValidDiagonal(a, b) {\n    return a.next.i !== b.i && a.prev.i !== b.i && !intersectsPolygon(a, b) && // dones't intersect other edges\n           (locallyInside(a, b) && locallyInside(b, a) && middleInside(a, b) && // locally visible\n            (area(a.prev, a, b.prev) || area(a, b.prev, b)) || // does not create opposite-facing sectors\n            equals(a, b) && area(a.prev, a, a.next) > 0 && area(b.prev, b, b.next) > 0); // special zero-length case\n}\n\n// signed area of a triangle\nfunction area(p, q, r) {\n    return (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);\n}\n\n// check if two points are equal\nfunction equals(p1, p2) {\n    return p1.x === p2.x && p1.y === p2.y;\n}\n\n// check if two segments intersect\nfunction intersects(p1, q1, p2, q2) {\n    var o1 = sign(area(p1, q1, p2));\n    var o2 = sign(area(p1, q1, q2));\n    var o3 = sign(area(p2, q2, p1));\n    var o4 = sign(area(p2, q2, q1));\n\n    if (o1 !== o2 && o3 !== o4) return true; // general case\n\n    if (o1 === 0 && onSegment(p1, p2, q1)) return true; // p1, q1 and p2 are collinear and p2 lies on p1q1\n    if (o2 === 0 && onSegment(p1, q2, q1)) return true; // p1, q1 and q2 are collinear and q2 lies on p1q1\n    if (o3 === 0 && onSegment(p2, p1, q2)) return true; // p2, q2 and p1 are collinear and p1 lies on p2q2\n    if (o4 === 0 && onSegment(p2, q1, q2)) return true; // p2, q2 and q1 are collinear and q1 lies on p2q2\n\n    return false;\n}\n\n// for collinear points p, q, r, check if point q lies on segment pr\nfunction onSegment(p, q, r) {\n    return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) && q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y);\n}\n\nfunction sign(num) {\n    return num > 0 ? 1 : num < 0 ? -1 : 0;\n}\n\n// check if a polygon diagonal intersects any polygon segments\nfunction intersectsPolygon(a, b) {\n    var p = a;\n    do {\n        if (p.i !== a.i && p.next.i !== a.i && p.i !== b.i && p.next.i !== b.i &&\n                intersects(p, p.next, a, b)) return true;\n        p = p.next;\n    } while (p !== a);\n\n    return false;\n}\n\n// check if a polygon diagonal is locally inside the polygon\nfunction locallyInside(a, b) {\n    return area(a.prev, a, a.next) < 0 ?\n        area(a, b, a.next) >= 0 && area(a, a.prev, b) >= 0 :\n        area(a, b, a.prev) < 0 || area(a, a.next, b) < 0;\n}\n\n// check if the middle point of a polygon diagonal is inside the polygon\nfunction middleInside(a, b) {\n    var p = a,\n        inside = false,\n        px = (a.x + b.x) / 2,\n        py = (a.y + b.y) / 2;\n    do {\n        if (((p.y > py) !== (p.next.y > py)) && p.next.y !== p.y &&\n                (px < (p.next.x - p.x) * (py - p.y) / (p.next.y - p.y) + p.x))\n            inside = !inside;\n        p = p.next;\n    } while (p !== a);\n\n    return inside;\n}\n\n// link two polygon vertices with a bridge; if the vertices belong to the same ring, it splits polygon into two;\n// if one belongs to the outer ring and another to a hole, it merges it into a single ring\nfunction splitPolygon(a, b) {\n    var a2 = new Node(a.i, a.x, a.y),\n        b2 = new Node(b.i, b.x, b.y),\n        an = a.next,\n        bp = b.prev;\n\n    a.next = b;\n    b.prev = a;\n\n    a2.next = an;\n    an.prev = a2;\n\n    b2.next = a2;\n    a2.prev = b2;\n\n    bp.next = b2;\n    b2.prev = bp;\n\n    return b2;\n}\n\n// create a node and optionally link it with previous one (in a circular doubly linked list)\nfunction insertNode(i, x, y, last) {\n    var p = new Node(i, x, y);\n\n    if (!last) {\n        p.prev = p;\n        p.next = p;\n\n    } else {\n        p.next = last.next;\n        p.prev = last;\n        last.next.prev = p;\n        last.next = p;\n    }\n    return p;\n}\n\nfunction removeNode(p) {\n    p.next.prev = p.prev;\n    p.prev.next = p.next;\n\n    if (p.prevZ) p.prevZ.nextZ = p.nextZ;\n    if (p.nextZ) p.nextZ.prevZ = p.prevZ;\n}\n\nfunction Node(i, x, y) {\n    // vertex index in coordinates array\n    this.i = i;\n\n    // vertex coordinates\n    this.x = x;\n    this.y = y;\n\n    // previous and next vertex nodes in a polygon ring\n    this.prev = null;\n    this.next = null;\n\n    // z-order curve value\n    this.z = 0;\n\n    // previous and next nodes in z-order\n    this.prevZ = null;\n    this.nextZ = null;\n\n    // indicates whether this is a steiner point\n    this.steiner = false;\n}\n\n// return a percentage difference between the polygon area and its triangulation area;\n// used to verify correctness of triangulation\nearcut.deviation = function (data, holeIndices, dim, triangles) {\n    var hasHoles = holeIndices && holeIndices.length;\n    var outerLen = hasHoles ? holeIndices[0] * dim : data.length;\n\n    var polygonArea = Math.abs(signedArea(data, 0, outerLen, dim));\n    if (hasHoles) {\n        for (var i = 0, len = holeIndices.length; i < len; i++) {\n            var start = holeIndices[i] * dim;\n            var end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;\n            polygonArea -= Math.abs(signedArea(data, start, end, dim));\n        }\n    }\n\n    var trianglesArea = 0;\n    for (i = 0; i < triangles.length; i += 3) {\n        var a = triangles[i] * dim;\n        var b = triangles[i + 1] * dim;\n        var c = triangles[i + 2] * dim;\n        trianglesArea += Math.abs(\n            (data[a] - data[c]) * (data[b + 1] - data[a + 1]) -\n            (data[a] - data[b]) * (data[c + 1] - data[a + 1]));\n    }\n\n    return polygonArea === 0 && trianglesArea === 0 ? 0 :\n        Math.abs((trianglesArea - polygonArea) / polygonArea);\n};\n\nfunction signedArea(data, start, end, dim) {\n    var sum = 0;\n    for (var i = start, j = end - dim; i < end; i += dim) {\n        sum += (data[j] - data[i]) * (data[i + 1] + data[j + 1]);\n        j = i;\n    }\n    return sum;\n}\n\n// turn a polygon in a multi-dimensional array form (e.g. as in GeoJSON) into a form Earcut accepts\nearcut.flatten = function (data) {\n    var dim = data[0][0].length,\n        result = {vertices: [], holes: [], dimensions: dim},\n        holeIndex = 0;\n\n    for (var i = 0; i < data.length; i++) {\n        for (var j = 0; j < data[i].length; j++) {\n            for (var d = 0; d < dim; d++) result.vertices.push(data[i][j][d]);\n        }\n        if (i > 0) {\n            holeIndex += data[i - 1].length;\n            result.holes.push(holeIndex);\n        }\n    }\n    return result;\n};\nearcut_1.default = _default;\n\nexport { earcut_1 as default };\n", "import WebGLConstants from \"./WebGLConstants.js\";\n\n/**\n * Winding order defines the order of vertices for a triangle to be considered front-facing.\n *\n * @enum {Number}\n */\nconst WindingOrder = {\n  /**\n   * Vertices are in clockwise order.\n   *\n   * @type {Number}\n   * @constant\n   */\n  CLOCKWISE: WebGLConstants.CW,\n\n  /**\n   * Vertices are in counter-clockwise order.\n   *\n   * @type {Number}\n   * @constant\n   */\n  COUNTER_CLOCKWISE: WebGLConstants.CCW,\n};\n\n/**\n * @private\n */\nWindingOrder.validate = function (windingOrder) {\n  return (\n    windingOrder === WindingOrder.CLOCKWISE ||\n    windingOrder === WindingOrder.COUNTER_CLOCKWISE\n  );\n};\n\nexport default Object.freeze(WindingOrder);\n", "import earcut from \"../ThirdParty/earcut.js\";\nimport Cartesian2 from \"./Cartesian2.js\";\nimport Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport Check from \"./Check.js\";\nimport ComponentDatatype from \"./ComponentDatatype.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport Ellipsoid from \"./Ellipsoid.js\";\nimport EllipsoidRhumbLine from \"./EllipsoidRhumbLine.js\";\nimport Geometry from \"./Geometry.js\";\nimport GeometryAttribute from \"./GeometryAttribute.js\";\nimport CesiumMath from \"./Math.js\";\nimport PrimitiveType from \"./PrimitiveType.js\";\nimport WindingOrder from \"./WindingOrder.js\";\n\nconst scaleToGeodeticHeightN = new Cartesian3();\nconst scaleToGeodeticHeightP = new Cartesian3();\n\n/**\n * @private\n */\nconst PolygonPipeline = {};\n\n/**\n * @exception {DeveloperError} At least three positions are required.\n */\nPolygonPipeline.computeArea2D = function (positions) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"positions\", positions);\n  Check.typeOf.number.greaterThanOrEquals(\n    \"positions.length\",\n    positions.length,\n    3\n  );\n  //>>includeEnd('debug');\n\n  const length = positions.length;\n  let area = 0.0;\n\n  for (let i0 = length - 1, i1 = 0; i1 < length; i0 = i1++) {\n    const v0 = positions[i0];\n    const v1 = positions[i1];\n\n    area += v0.x * v1.y - v1.x * v0.y;\n  }\n\n  return area * 0.5;\n};\n\n/**\n * @returns {WindingOrder} The winding order.\n *\n * @exception {DeveloperError} At least three positions are required.\n */\nPolygonPipeline.computeWindingOrder2D = function (positions) {\n  const area = PolygonPipeline.computeArea2D(positions);\n  return area > 0.0 ? WindingOrder.COUNTER_CLOCKWISE : WindingOrder.CLOCKWISE;\n};\n\n/**\n * Triangulate a polygon.\n *\n * @param {Cartesian2[]} positions Cartesian2 array containing the vertices of the polygon\n * @param {Number[]} [holes] An array of the staring indices of the holes.\n * @returns {Number[]} Index array representing triangles that fill the polygon\n */\nPolygonPipeline.triangulate = function (positions, holes) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"positions\", positions);\n  //>>includeEnd('debug');\n\n  const flattenedPositions = Cartesian2.packArray(positions);\n  return earcut(flattenedPositions, holes, 2);\n};\n\nconst subdivisionV0Scratch = new Cartesian3();\nconst subdivisionV1Scratch = new Cartesian3();\nconst subdivisionV2Scratch = new Cartesian3();\nconst subdivisionS0Scratch = new Cartesian3();\nconst subdivisionS1Scratch = new Cartesian3();\nconst subdivisionS2Scratch = new Cartesian3();\nconst subdivisionMidScratch = new Cartesian3();\n\n/**\n * Subdivides positions and raises points to the surface of the ellipsoid.\n *\n * @param {Ellipsoid} ellipsoid The ellipsoid the polygon in on.\n * @param {Cartesian3[]} positions An array of {@link Cartesian3} positions of the polygon.\n * @param {Number[]} indices An array of indices that determines the triangles in the polygon.\n * @param {Number} [granularity=CesiumMath.RADIANS_PER_DEGREE] The distance, in radians, between each latitude and longitude. Determines the number of positions in the buffer.\n *\n * @exception {DeveloperError} At least three indices are required.\n * @exception {DeveloperError} The number of indices must be divisable by three.\n * @exception {DeveloperError} Granularity must be greater than zero.\n */\nPolygonPipeline.computeSubdivision = function (\n  ellipsoid,\n  positions,\n  indices,\n  granularity\n) {\n  granularity = defaultValue(granularity, CesiumMath.RADIANS_PER_DEGREE);\n\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"ellipsoid\", ellipsoid);\n  Check.defined(\"positions\", positions);\n  Check.defined(\"indices\", indices);\n  Check.typeOf.number.greaterThanOrEquals(\"indices.length\", indices.length, 3);\n  Check.typeOf.number.equals(\"indices.length % 3\", \"0\", indices.length % 3, 0);\n  Check.typeOf.number.greaterThan(\"granularity\", granularity, 0.0);\n  //>>includeEnd('debug');\n\n  // triangles that need (or might need) to be subdivided.\n  const triangles = indices.slice(0);\n\n  // New positions due to edge splits are appended to the positions list.\n  let i;\n  const length = positions.length;\n  const subdividedPositions = new Array(length * 3);\n  let q = 0;\n  for (i = 0; i < length; i++) {\n    const item = positions[i];\n    subdividedPositions[q++] = item.x;\n    subdividedPositions[q++] = item.y;\n    subdividedPositions[q++] = item.z;\n  }\n\n  const subdividedIndices = [];\n\n  // Used to make sure shared edges are not split more than once.\n  const edges = {};\n\n  const radius = ellipsoid.maximumRadius;\n  const minDistance = CesiumMath.chordLength(granularity, radius);\n  const minDistanceSqrd = minDistance * minDistance;\n\n  while (triangles.length > 0) {\n    const i2 = triangles.pop();\n    const i1 = triangles.pop();\n    const i0 = triangles.pop();\n\n    const v0 = Cartesian3.fromArray(\n      subdividedPositions,\n      i0 * 3,\n      subdivisionV0Scratch\n    );\n    const v1 = Cartesian3.fromArray(\n      subdividedPositions,\n      i1 * 3,\n      subdivisionV1Scratch\n    );\n    const v2 = Cartesian3.fromArray(\n      subdividedPositions,\n      i2 * 3,\n      subdivisionV2Scratch\n    );\n\n    const s0 = Cartesian3.multiplyByScalar(\n      Cartesian3.normalize(v0, subdivisionS0Scratch),\n      radius,\n      subdivisionS0Scratch\n    );\n    const s1 = Cartesian3.multiplyByScalar(\n      Cartesian3.normalize(v1, subdivisionS1Scratch),\n      radius,\n      subdivisionS1Scratch\n    );\n    const s2 = Cartesian3.multiplyByScalar(\n      Cartesian3.normalize(v2, subdivisionS2Scratch),\n      radius,\n      subdivisionS2Scratch\n    );\n\n    const g0 = Cartesian3.magnitudeSquared(\n      Cartesian3.subtract(s0, s1, subdivisionMidScratch)\n    );\n    const g1 = Cartesian3.magnitudeSquared(\n      Cartesian3.subtract(s1, s2, subdivisionMidScratch)\n    );\n    const g2 = Cartesian3.magnitudeSquared(\n      Cartesian3.subtract(s2, s0, subdivisionMidScratch)\n    );\n\n    const max = Math.max(g0, g1, g2);\n    let edge;\n    let mid;\n\n    // if the max length squared of a triangle edge is greater than the chord length of squared\n    // of the granularity, subdivide the triangle\n    if (max > minDistanceSqrd) {\n      if (g0 === max) {\n        edge = Math.min(i0, i1) + \" \" + Math.max(i0, i1);\n\n        i = edges[edge];\n        if (!defined(i)) {\n          mid = Cartesian3.add(v0, v1, subdivisionMidScratch);\n          Cartesian3.multiplyByScalar(mid, 0.5, mid);\n          subdividedPositions.push(mid.x, mid.y, mid.z);\n          i = subdividedPositions.length / 3 - 1;\n          edges[edge] = i;\n        }\n\n        triangles.push(i0, i, i2);\n        triangles.push(i, i1, i2);\n      } else if (g1 === max) {\n        edge = Math.min(i1, i2) + \" \" + Math.max(i1, i2);\n\n        i = edges[edge];\n        if (!defined(i)) {\n          mid = Cartesian3.add(v1, v2, subdivisionMidScratch);\n          Cartesian3.multiplyByScalar(mid, 0.5, mid);\n          subdividedPositions.push(mid.x, mid.y, mid.z);\n          i = subdividedPositions.length / 3 - 1;\n          edges[edge] = i;\n        }\n\n        triangles.push(i1, i, i0);\n        triangles.push(i, i2, i0);\n      } else if (g2 === max) {\n        edge = Math.min(i2, i0) + \" \" + Math.max(i2, i0);\n\n        i = edges[edge];\n        if (!defined(i)) {\n          mid = Cartesian3.add(v2, v0, subdivisionMidScratch);\n          Cartesian3.multiplyByScalar(mid, 0.5, mid);\n          subdividedPositions.push(mid.x, mid.y, mid.z);\n          i = subdividedPositions.length / 3 - 1;\n          edges[edge] = i;\n        }\n\n        triangles.push(i2, i, i1);\n        triangles.push(i, i0, i1);\n      }\n    } else {\n      subdividedIndices.push(i0);\n      subdividedIndices.push(i1);\n      subdividedIndices.push(i2);\n    }\n  }\n\n  return new Geometry({\n    attributes: {\n      position: new GeometryAttribute({\n        componentDatatype: ComponentDatatype.DOUBLE,\n        componentsPerAttribute: 3,\n        values: subdividedPositions,\n      }),\n    },\n    indices: subdividedIndices,\n    primitiveType: PrimitiveType.TRIANGLES,\n  });\n};\n\nconst subdivisionC0Scratch = new Cartographic();\nconst subdivisionC1Scratch = new Cartographic();\nconst subdivisionC2Scratch = new Cartographic();\nconst subdivisionCartographicScratch = new Cartographic();\n\n/**\n * Subdivides positions on rhumb lines and raises points to the surface of the ellipsoid.\n *\n * @param {Ellipsoid} ellipsoid The ellipsoid the polygon in on.\n * @param {Cartesian3[]} positions An array of {@link Cartesian3} positions of the polygon.\n * @param {Number[]} indices An array of indices that determines the triangles in the polygon.\n * @param {Number} [granularity=CesiumMath.RADIANS_PER_DEGREE] The distance, in radians, between each latitude and longitude. Determines the number of positions in the buffer.\n *\n * @exception {DeveloperError} At least three indices are required.\n * @exception {DeveloperError} The number of indices must be divisable by three.\n * @exception {DeveloperError} Granularity must be greater than zero.\n */\nPolygonPipeline.computeRhumbLineSubdivision = function (\n  ellipsoid,\n  positions,\n  indices,\n  granularity\n) {\n  granularity = defaultValue(granularity, CesiumMath.RADIANS_PER_DEGREE);\n\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"ellipsoid\", ellipsoid);\n  Check.defined(\"positions\", positions);\n  Check.defined(\"indices\", indices);\n  Check.typeOf.number.greaterThanOrEquals(\"indices.length\", indices.length, 3);\n  Check.typeOf.number.equals(\"indices.length % 3\", \"0\", indices.length % 3, 0);\n  Check.typeOf.number.greaterThan(\"granularity\", granularity, 0.0);\n  //>>includeEnd('debug');\n\n  // triangles that need (or might need) to be subdivided.\n  const triangles = indices.slice(0);\n\n  // New positions due to edge splits are appended to the positions list.\n  let i;\n  const length = positions.length;\n  const subdividedPositions = new Array(length * 3);\n  let q = 0;\n  for (i = 0; i < length; i++) {\n    const item = positions[i];\n    subdividedPositions[q++] = item.x;\n    subdividedPositions[q++] = item.y;\n    subdividedPositions[q++] = item.z;\n  }\n\n  const subdividedIndices = [];\n\n  // Used to make sure shared edges are not split more than once.\n  const edges = {};\n\n  const radius = ellipsoid.maximumRadius;\n  const minDistance = CesiumMath.chordLength(granularity, radius);\n\n  const rhumb0 = new EllipsoidRhumbLine(undefined, undefined, ellipsoid);\n  const rhumb1 = new EllipsoidRhumbLine(undefined, undefined, ellipsoid);\n  const rhumb2 = new EllipsoidRhumbLine(undefined, undefined, ellipsoid);\n\n  while (triangles.length > 0) {\n    const i2 = triangles.pop();\n    const i1 = triangles.pop();\n    const i0 = triangles.pop();\n\n    const v0 = Cartesian3.fromArray(\n      subdividedPositions,\n      i0 * 3,\n      subdivisionV0Scratch\n    );\n    const v1 = Cartesian3.fromArray(\n      subdividedPositions,\n      i1 * 3,\n      subdivisionV1Scratch\n    );\n    const v2 = Cartesian3.fromArray(\n      subdividedPositions,\n      i2 * 3,\n      subdivisionV2Scratch\n    );\n\n    const c0 = ellipsoid.cartesianToCartographic(v0, subdivisionC0Scratch);\n    const c1 = ellipsoid.cartesianToCartographic(v1, subdivisionC1Scratch);\n    const c2 = ellipsoid.cartesianToCartographic(v2, subdivisionC2Scratch);\n\n    rhumb0.setEndPoints(c0, c1);\n    const g0 = rhumb0.surfaceDistance;\n    rhumb1.setEndPoints(c1, c2);\n    const g1 = rhumb1.surfaceDistance;\n    rhumb2.setEndPoints(c2, c0);\n    const g2 = rhumb2.surfaceDistance;\n\n    const max = Math.max(g0, g1, g2);\n    let edge;\n    let mid;\n    let midHeight;\n    let midCartesian3;\n\n    // if the max length squared of a triangle edge is greater than granularity, subdivide the triangle\n    if (max > minDistance) {\n      if (g0 === max) {\n        edge = Math.min(i0, i1) + \" \" + Math.max(i0, i1);\n\n        i = edges[edge];\n        if (!defined(i)) {\n          mid = rhumb0.interpolateUsingFraction(\n            0.5,\n            subdivisionCartographicScratch\n          );\n          midHeight = (c0.height + c1.height) * 0.5;\n          midCartesian3 = Cartesian3.fromRadians(\n            mid.longitude,\n            mid.latitude,\n            midHeight,\n            ellipsoid,\n            subdivisionMidScratch\n          );\n          subdividedPositions.push(\n            midCartesian3.x,\n            midCartesian3.y,\n            midCartesian3.z\n          );\n          i = subdividedPositions.length / 3 - 1;\n          edges[edge] = i;\n        }\n\n        triangles.push(i0, i, i2);\n        triangles.push(i, i1, i2);\n      } else if (g1 === max) {\n        edge = Math.min(i1, i2) + \" \" + Math.max(i1, i2);\n\n        i = edges[edge];\n        if (!defined(i)) {\n          mid = rhumb1.interpolateUsingFraction(\n            0.5,\n            subdivisionCartographicScratch\n          );\n          midHeight = (c1.height + c2.height) * 0.5;\n          midCartesian3 = Cartesian3.fromRadians(\n            mid.longitude,\n            mid.latitude,\n            midHeight,\n            ellipsoid,\n            subdivisionMidScratch\n          );\n          subdividedPositions.push(\n            midCartesian3.x,\n            midCartesian3.y,\n            midCartesian3.z\n          );\n          i = subdividedPositions.length / 3 - 1;\n          edges[edge] = i;\n        }\n\n        triangles.push(i1, i, i0);\n        triangles.push(i, i2, i0);\n      } else if (g2 === max) {\n        edge = Math.min(i2, i0) + \" \" + Math.max(i2, i0);\n\n        i = edges[edge];\n        if (!defined(i)) {\n          mid = rhumb2.interpolateUsingFraction(\n            0.5,\n            subdivisionCartographicScratch\n          );\n          midHeight = (c2.height + c0.height) * 0.5;\n          midCartesian3 = Cartesian3.fromRadians(\n            mid.longitude,\n            mid.latitude,\n            midHeight,\n            ellipsoid,\n            subdivisionMidScratch\n          );\n          subdividedPositions.push(\n            midCartesian3.x,\n            midCartesian3.y,\n            midCartesian3.z\n          );\n          i = subdividedPositions.length / 3 - 1;\n          edges[edge] = i;\n        }\n\n        triangles.push(i2, i, i1);\n        triangles.push(i, i0, i1);\n      }\n    } else {\n      subdividedIndices.push(i0);\n      subdividedIndices.push(i1);\n      subdividedIndices.push(i2);\n    }\n  }\n\n  return new Geometry({\n    attributes: {\n      position: new GeometryAttribute({\n        componentDatatype: ComponentDatatype.DOUBLE,\n        componentsPerAttribute: 3,\n        values: subdividedPositions,\n      }),\n    },\n    indices: subdividedIndices,\n    primitiveType: PrimitiveType.TRIANGLES,\n  });\n};\n\n/**\n * Scales each position of a geometry's position attribute to a height, in place.\n *\n * @param {Number[]} positions The array of numbers representing the positions to be scaled\n * @param {Number} [height=0.0] The desired height to add to the positions\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the positions lie.\n * @param {Boolean} [scaleToSurface=true] <code>true</code> if the positions need to be scaled to the surface before the height is added.\n * @returns {Number[]} The input array of positions, scaled to height\n */\nPolygonPipeline.scaleToGeodeticHeight = function (\n  positions,\n  height,\n  ellipsoid,\n  scaleToSurface\n) {\n  ellipsoid = defaultValue(ellipsoid, Ellipsoid.WGS84);\n\n  let n = scaleToGeodeticHeightN;\n  let p = scaleToGeodeticHeightP;\n\n  height = defaultValue(height, 0.0);\n  scaleToSurface = defaultValue(scaleToSurface, true);\n\n  if (defined(positions)) {\n    const length = positions.length;\n\n    for (let i = 0; i < length; i += 3) {\n      Cartesian3.fromArray(positions, i, p);\n\n      if (scaleToSurface) {\n        p = ellipsoid.scaleToGeodeticSurface(p, p);\n      }\n\n      if (height !== 0) {\n        n = ellipsoid.geodeticSurfaceNormal(p, n);\n\n        Cartesian3.multiplyByScalar(n, height, n);\n        Cartesian3.add(p, n, p);\n      }\n\n      positions[i] = p.x;\n      positions[i + 1] = p.y;\n      positions[i + 2] = p.z;\n    }\n  }\n\n  return positions;\n};\nexport default PolygonPipeline;\n"], "names": ["earcut_1", "earcut", "_default", "data", "holeIndices", "dim", "minX", "minY", "maxX", "maxY", "x", "y", "invSize", "hasHoles", "length", "outerLen", "outerNode", "linkedList", "triangles", "next", "prev", "i", "len", "list", "queue", "steiner", "push", "getLeftmost", "sort", "compareX", "eliminateHole", "eliminateHoles", "Math", "max", "earcutLinked", "start", "end", "clockwise", "last", "signedArea", "insertNode", "equals", "removeNode", "filterPoints", "again", "p", "area", "ear", "pass", "z", "zOrder", "prevZ", "nextZ", "q", "e", "tail", "numMerges", "pSize", "qSize", "inSize", "sortLinked", "indexCurve", "stop", "isEarHashed", "isEar", "cureLocalIntersections", "splitEarcut", "a", "b", "c", "ax", "bx", "cx", "ay", "by", "cy", "x0", "y0", "x1", "y1", "pointInTriangle", "minZ", "maxZ", "n", "intersects", "locallyInside", "isValidDiagonal", "splitPolygon", "hole", "bridge", "m", "hx", "hy", "qx", "Infinity", "tan", "mx", "my", "tanMin", "abs", "sectorContainsSector", "findHoleBridge", "bridgeReverse", "leftmost", "px", "py", "intersectsPolygon", "inside", "middleInside", "r", "p1", "p2", "q1", "q2", "o1", "sign", "o2", "o3", "o4", "onSegment", "min", "num", "a2", "Node", "b2", "an", "bp", "this", "sum", "j", "deviation", "polygonArea", "trianglesArea", "flatten", "result", "vertices", "holes", "dimensions", "holeIndex", "d", "default", "WindingOrder", "CLOCKWISE", "WebGLConstants", "CW", "COUNTER_CLOCKWISE", "CCW", "windingOrder", "WindingOrder$1", "Object", "freeze", "scaleToGeodeticHeightN", "Cartesian3", "scaleToGeodeticHeightP", "PolygonPipeline", "positions", "Check", "defined", "typeOf", "number", "greaterThanOrEquals", "i0", "i1", "v0", "v1", "computeArea2D", "flattenedPositions", "Cartesian2", "packArray", "subdivisionV0Scratch", "subdivisionV1Scratch", "subdivisionV2Scratch", "subdivisionS0Scratch", "subdivisionS1Scratch", "subdivisionS2Scratch", "subdivisionMidScratch", "computeSubdivision", "ellipsoid", "indices", "granularity", "defaultValue", "CesiumMath", "RADIANS_PER_DEGREE", "object", "greaterThan", "slice", "subdividedPositions", "Array", "item", "subdividedIndices", "edges", "radius", "maximumRadius", "minDistance", "chord<PERSON>ength", "minDistanceSqrd", "i2", "pop", "fromArray", "v2", "s0", "multiplyByScalar", "normalize", "s1", "s2", "g0", "magnitudeSquared", "subtract", "g1", "g2", "edge", "mid", "add", "Geometry", "attributes", "position", "GeometryAttribute", "componentDatatype", "ComponentDatatype", "DOUBLE", "componentsPerAttribute", "values", "primitiveType", "PrimitiveType", "TRIANGLES", "subdivisionC0Scratch", "Cartographic", "subdivisionC1Scratch", "subdivisionC2Scratch", "subdivisionCartographicScratch", "computeRhumbLineSubdivision", "rhumb0", "EllipsoidRhumbLine", "undefined", "rhumb1", "rhumb2", "c0", "cartesianToCartographic", "c1", "c2", "setEndPoints", "surfaceDistance", "midHeight", "midCartesian3", "interpolateUsingFraction", "height", "fromRadians", "longitude", "latitude", "scaleToGeodeticHeight", "scaleToSurface", "Ellipsoid", "WGS84", "scaleToGeodeticSurface", "geodeticSurfaceNormal"], "mappings": "sPACA,IAAIA,EAAWC,EACXC,EAAWD,EAEf,SAASA,EAAOE,EAAMC,EAAaC,GAE/BA,EAAMA,GAAO,EAEb,IAOIC,EAAMC,EAAMC,EAAMC,EAAMC,EAAGC,EAAGC,EAP9BC,EAAWT,GAAeA,EAAYU,OACtCC,EAAWF,EAAWT,EAAY,GAAKC,EAAMF,EAAKW,OAClDE,EAAYC,EAAWd,EAAM,EAAGY,EAAUV,GAAK,GAC/Ca,EAAY,GAEhB,IAAKF,GAAaA,EAAUG,OAASH,EAAUI,KAAM,OAAOF,EAO5D,GAHIL,IAAUG,EA2PlB,SAAwBb,EAAMC,EAAaY,EAAWX,GAClD,IACIgB,EAAGC,EAAiBC,EADpBC,EAAQ,GAGZ,IAAKH,EAAI,EAAGC,EAAMlB,EAAYU,OAAQO,EAAIC,EAAKD,KAG3CE,EAAON,EAAWd,EAFVC,EAAYiB,GAAKhB,EACnBgB,EAAIC,EAAM,EAAIlB,EAAYiB,EAAI,GAAKhB,EAAMF,EAAKW,OAChBT,GAAK,MAC5BkB,EAAKJ,OAAMI,EAAKE,SAAU,GACvCD,EAAME,KAAKC,EAAYJ,IAM3B,IAHAC,EAAMI,KAAKC,GAGNR,EAAI,EAAGA,EAAIG,EAAMV,OAAQO,IAC1BL,EAAYc,EAAcN,EAAMH,GAAIL,GAGxC,OAAOA,CACX,CA/Q8Be,CAAe5B,EAAMC,EAAaY,EAAWX,IAGnEF,EAAKW,OAAS,GAAKT,EAAK,CACxBC,EAAOE,EAAOL,EAAK,GACnBI,EAAOE,EAAON,EAAK,GAEnB,IAAK,IAAIkB,EAAIhB,EAAKgB,EAAIN,EAAUM,GAAKhB,GACjCK,EAAIP,EAAKkB,IAEDf,IAAMA,EAAOI,IADrBC,EAAIR,EAAKkB,EAAI,IAELd,IAAMA,EAAOI,GACjBD,EAAIF,IAAMA,EAAOE,GACjBC,EAAIF,IAAMA,EAAOE,GAKzBC,EAAsB,KADtBA,EAAUoB,KAAKC,IAAIzB,EAAOF,EAAMG,EAAOF,IACb,MAAQK,EAAU,CAC/C,CAID,OAFAsB,EAAalB,EAAWE,EAAWb,EAAKC,EAAMC,EAAMK,EAAS,GAEtDM,CACX,CAGA,SAASD,EAAWd,EAAMgC,EAAOC,EAAK/B,EAAKgC,GACvC,IAAIhB,EAAGiB,EAEP,GAAID,IAAeE,EAAWpC,EAAMgC,EAAOC,EAAK/B,GAAO,EACnD,IAAKgB,EAAIc,EAAOd,EAAIe,EAAKf,GAAKhB,EAAKiC,EAAOE,EAAWnB,EAAGlB,EAAKkB,GAAIlB,EAAKkB,EAAI,GAAIiB,QAE9E,IAAKjB,EAAIe,EAAM/B,EAAKgB,GAAKc,EAAOd,GAAKhB,EAAKiC,EAAOE,EAAWnB,EAAGlB,EAAKkB,GAAIlB,EAAKkB,EAAI,GAAIiB,GAQzF,OALIA,GAAQG,EAAOH,EAAMA,EAAKnB,QAC1BuB,EAAWJ,GACXA,EAAOA,EAAKnB,MAGTmB,CACX,CAGA,SAASK,EAAaR,EAAOC,GACzB,IAAKD,EAAO,OAAOA,EACdC,IAAKA,EAAMD,GAEhB,IACIS,EADAC,EAAIV,EAER,GAGI,GAFAS,GAAQ,EAEHC,EAAEpB,UAAYgB,EAAOI,EAAGA,EAAE1B,OAAqC,IAA5B2B,EAAKD,EAAEzB,KAAMyB,EAAGA,EAAE1B,MAOtD0B,EAAIA,EAAE1B,SAP8D,CAGpE,GAFAuB,EAAWG,IACXA,EAAIT,EAAMS,EAAEzB,QACFyB,EAAE1B,KAAM,MAClByB,GAAQ,CAEpB,QAGaA,GAASC,IAAMT,GAExB,OAAOA,CACX,CAGA,SAASF,EAAaa,EAAK7B,EAAWb,EAAKC,EAAMC,EAAMK,EAASoC,GAC5D,GAAKD,EAAL,EAGKC,GAAQpC,GAuRjB,SAAoBuB,EAAO7B,EAAMC,EAAMK,GACnC,IAAIiC,EAAIV,EACR,GACgB,IAARU,EAAEI,IAASJ,EAAEI,EAAIC,EAAOL,EAAEnC,EAAGmC,EAAElC,EAAGL,EAAMC,EAAMK,IAClDiC,EAAEM,MAAQN,EAAEzB,KACZyB,EAAEO,MAAQP,EAAE1B,KACZ0B,EAAIA,EAAE1B,WACD0B,IAAMV,GAEfU,EAAEM,MAAMC,MAAQ,KAChBP,EAAEM,MAAQ,KAOd,SAAoB5B,GAChB,IAAIF,EAAGwB,EAAGQ,EAAGC,EAAGC,EAAMC,EAAWC,EAAOC,EACpCC,EAAS,EAEb,EAAG,CAMC,IALAd,EAAItB,EACJA,EAAO,KACPgC,EAAO,KACPC,EAAY,EAELX,GAAG,CAIN,IAHAW,IACAH,EAAIR,EACJY,EAAQ,EACHpC,EAAI,EAAGA,EAAIsC,IACZF,IACAJ,EAAIA,EAAED,OAFc/B,KAOxB,IAFAqC,EAAQC,EAEDF,EAAQ,GAAMC,EAAQ,GAAKL,GAEhB,IAAVI,IAA0B,IAAVC,IAAgBL,GAAKR,EAAEI,GAAKI,EAAEJ,IAC9CK,EAAIT,EACJA,EAAIA,EAAEO,MACNK,MAEAH,EAAID,EACJA,EAAIA,EAAED,MACNM,KAGAH,EAAMA,EAAKH,MAAQE,EAClB/B,EAAO+B,EAEZA,EAAEH,MAAQI,EACVA,EAAOD,EAGXT,EAAIQ,CACP,CAEDE,EAAKH,MAAQ,KACbO,GAAU,CAElB,OAAaH,EAAY,EAGzB,CAtDII,CAAWf,EACf,CApS0BgB,CAAWd,EAAKzC,EAAMC,EAAMK,GAMlD,IAJA,IACIQ,EAAMD,EADN2C,EAAOf,EAIJA,EAAI3B,OAAS2B,EAAI5B,MAIpB,GAHAC,EAAO2B,EAAI3B,KACXD,EAAO4B,EAAI5B,KAEPP,EAAUmD,EAAYhB,EAAKzC,EAAMC,EAAMK,GAAWoD,EAAMjB,GAExD7B,EAAUQ,KAAKN,EAAKC,EAAIhB,EAAM,GAC9Ba,EAAUQ,KAAKqB,EAAI1B,EAAIhB,EAAM,GAC7Ba,EAAUQ,KAAKP,EAAKE,EAAIhB,EAAM,GAE9BqC,EAAWK,GAGXA,EAAM5B,EAAKA,KACX2C,EAAO3C,EAAKA,UAQhB,IAHA4B,EAAM5B,KAGM2C,EAAM,CAETd,EAIe,IAATA,EAEPd,EADAa,EAAMkB,EAAuBtB,EAAaI,GAAM7B,EAAWb,GACzCa,EAAWb,EAAKC,EAAMC,EAAMK,EAAS,GAGvC,IAAToC,GACPkB,EAAYnB,EAAK7B,EAAWb,EAAKC,EAAMC,EAAMK,GAT7CsB,EAAaS,EAAaI,GAAM7B,EAAWb,EAAKC,EAAMC,EAAMK,EAAS,GAYzE,KACH,CA/CY,CAiDrB,CAGA,SAASoD,EAAMjB,GACX,IAAIoB,EAAIpB,EAAI3B,KACRgD,EAAIrB,EACJsB,EAAItB,EAAI5B,KAEZ,GAAI2B,EAAKqB,EAAGC,EAAGC,IAAM,EAAG,OAAO,EAY/B,IATA,IAAIC,EAAKH,EAAEzD,EAAG6D,EAAKH,EAAE1D,EAAG8D,EAAKH,EAAE3D,EAAG+D,EAAKN,EAAExD,EAAG+D,EAAKN,EAAEzD,EAAGgE,EAAKN,EAAE1D,EAGzDiE,EAAKN,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDK,EAAKJ,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDG,EAAKR,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDO,EAAKN,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EAErD9B,EAAIwB,EAAElD,KACH0B,IAAMsB,GAAG,CACZ,GAAItB,EAAEnC,GAAKkE,GAAM/B,EAAEnC,GAAKoE,GAAMjC,EAAElC,GAAKkE,GAAMhC,EAAElC,GAAKoE,GAC9CC,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAI9B,EAAEnC,EAAGmC,EAAElC,IAC/CmC,EAAKD,EAAEzB,KAAMyB,EAAGA,EAAE1B,OAAS,EAAG,OAAO,EACzC0B,EAAIA,EAAE1B,IACT,CAED,OAAO,CACX,CAEA,SAAS4C,EAAYhB,EAAKzC,EAAMC,EAAMK,GAClC,IAAIuD,EAAIpB,EAAI3B,KACRgD,EAAIrB,EACJsB,EAAItB,EAAI5B,KAEZ,GAAI2B,EAAKqB,EAAGC,EAAGC,IAAM,EAAG,OAAO,EAkB/B,IAhBA,IAAIC,EAAKH,EAAEzD,EAAG6D,EAAKH,EAAE1D,EAAG8D,EAAKH,EAAE3D,EAAG+D,EAAKN,EAAExD,EAAG+D,EAAKN,EAAEzD,EAAGgE,EAAKN,EAAE1D,EAGzDiE,EAAKN,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDK,EAAKJ,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDG,EAAKR,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EACrDO,EAAKN,EAAKC,EAAMD,EAAKE,EAAKF,EAAKE,EAAOD,EAAKC,EAAKD,EAAKC,EAGrDM,EAAO/B,EAAO0B,EAAIC,EAAIvE,EAAMC,EAAMK,GAClCsE,EAAOhC,EAAO4B,EAAIC,EAAIzE,EAAMC,EAAMK,GAElCiC,EAAIE,EAAII,MACRgC,EAAIpC,EAAIK,MAGLP,GAAKA,EAAEI,GAAKgC,GAAQE,GAAKA,EAAElC,GAAKiC,GAAM,CACzC,GAAIrC,EAAEnC,GAAKkE,GAAM/B,EAAEnC,GAAKoE,GAAMjC,EAAElC,GAAKkE,GAAMhC,EAAElC,GAAKoE,GAAMlC,IAAMsB,GAAKtB,IAAMwB,GACrEW,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAI9B,EAAEnC,EAAGmC,EAAElC,IAAMmC,EAAKD,EAAEzB,KAAMyB,EAAGA,EAAE1B,OAAS,EAAG,OAAO,EAG9F,GAFA0B,EAAIA,EAAEM,MAEFgC,EAAEzE,GAAKkE,GAAMO,EAAEzE,GAAKoE,GAAMK,EAAExE,GAAKkE,GAAMM,EAAExE,GAAKoE,GAAMI,IAAMhB,GAAKgB,IAAMd,GACrEW,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAIQ,EAAEzE,EAAGyE,EAAExE,IAAMmC,EAAKqC,EAAE/D,KAAM+D,EAAGA,EAAEhE,OAAS,EAAG,OAAO,EAC9FgE,EAAIA,EAAE/B,KACT,CAGD,KAAOP,GAAKA,EAAEI,GAAKgC,GAAM,CACrB,GAAIpC,EAAEnC,GAAKkE,GAAM/B,EAAEnC,GAAKoE,GAAMjC,EAAElC,GAAKkE,GAAMhC,EAAElC,GAAKoE,GAAMlC,IAAMsB,GAAKtB,IAAMwB,GACrEW,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAI9B,EAAEnC,EAAGmC,EAAElC,IAAMmC,EAAKD,EAAEzB,KAAMyB,EAAGA,EAAE1B,OAAS,EAAG,OAAO,EAC9F0B,EAAIA,EAAEM,KACT,CAGD,KAAOgC,GAAKA,EAAElC,GAAKiC,GAAM,CACrB,GAAIC,EAAEzE,GAAKkE,GAAMO,EAAEzE,GAAKoE,GAAMK,EAAExE,GAAKkE,GAAMM,EAAExE,GAAKoE,GAAMI,IAAMhB,GAAKgB,IAAMd,GACrEW,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAIQ,EAAEzE,EAAGyE,EAAExE,IAAMmC,EAAKqC,EAAE/D,KAAM+D,EAAGA,EAAEhE,OAAS,EAAG,OAAO,EAC9FgE,EAAIA,EAAE/B,KACT,CAED,OAAO,CACX,CAGA,SAASa,EAAuB9B,EAAOjB,EAAWb,GAC9C,IAAIwC,EAAIV,EACR,EAAG,CACC,IAAIgC,EAAItB,EAAEzB,KACNgD,EAAIvB,EAAE1B,KAAKA,MAEVsB,EAAO0B,EAAGC,IAAMgB,EAAWjB,EAAGtB,EAAGA,EAAE1B,KAAMiD,IAAMiB,EAAclB,EAAGC,IAAMiB,EAAcjB,EAAGD,KAExFjD,EAAUQ,KAAKyC,EAAE9C,EAAIhB,EAAM,GAC3Ba,EAAUQ,KAAKmB,EAAExB,EAAIhB,EAAM,GAC3Ba,EAAUQ,KAAK0C,EAAE/C,EAAIhB,EAAM,GAG3BqC,EAAWG,GACXH,EAAWG,EAAE1B,MAEb0B,EAAIV,EAAQiC,GAEhBvB,EAAIA,EAAE1B,IACd,OAAa0B,IAAMV,GAEf,OAAOQ,EAAaE,EACxB,CAGA,SAASqB,EAAY/B,EAAOjB,EAAWb,EAAKC,EAAMC,EAAMK,GAEpD,IAAIuD,EAAIhC,EACR,EAAG,CAEC,IADA,IAAIiC,EAAID,EAAEhD,KAAKA,KACRiD,IAAMD,EAAE/C,MAAM,CACjB,GAAI+C,EAAE9C,IAAM+C,EAAE/C,GAAKiE,EAAgBnB,EAAGC,GAAI,CAEtC,IAAIC,EAAIkB,EAAapB,EAAGC,GASxB,OANAD,EAAIxB,EAAawB,EAAGA,EAAEhD,MACtBkD,EAAI1B,EAAa0B,EAAGA,EAAElD,MAGtBe,EAAaiC,EAAGjD,EAAWb,EAAKC,EAAMC,EAAMK,EAAS,QACrDsB,EAAamC,EAAGnD,EAAWb,EAAKC,EAAMC,EAAMK,EAAS,EAExD,CACDwD,EAAIA,EAAEjD,IACT,CACDgD,EAAIA,EAAEhD,IACd,OAAagD,IAAMhC,EACnB,CAyBA,SAASN,EAASsC,EAAGC,GACjB,OAAOD,EAAEzD,EAAI0D,EAAE1D,CACnB,CAGA,SAASoB,EAAc0D,EAAMxE,GACzB,IAAIyE,EAaR,SAAwBD,EAAMxE,GAC1B,IAII0E,EAJA7C,EAAI7B,EACJ2E,EAAKH,EAAK9E,EACVkF,EAAKJ,EAAK7E,EACVkF,GAAMC,IAKV,EAAG,CACC,GAAIF,GAAM/C,EAAElC,GAAKiF,GAAM/C,EAAE1B,KAAKR,GAAKkC,EAAE1B,KAAKR,IAAMkC,EAAElC,EAAG,CACjD,IAAID,EAAImC,EAAEnC,GAAKkF,EAAK/C,EAAElC,IAAMkC,EAAE1B,KAAKT,EAAImC,EAAEnC,IAAMmC,EAAE1B,KAAKR,EAAIkC,EAAElC,GAC5D,GAAID,GAAKiF,GAAMjF,EAAImF,IACfA,EAAKnF,EACLgF,EAAI7C,EAAEnC,EAAImC,EAAE1B,KAAKT,EAAImC,EAAIA,EAAE1B,KACvBT,IAAMiF,GAAI,OAAOD,CAE5B,CACD7C,EAAIA,EAAE1B,IACd,OAAa0B,IAAM7B,GAEf,IAAK0E,EAAG,OAAO,KAMf,IAIIK,EAJAjC,EAAO4B,EACPM,EAAKN,EAAEhF,EACPuF,EAAKP,EAAE/E,EACPuF,EAASJ,IAGbjD,EAAI6C,EAEJ,GACQC,GAAM9C,EAAEnC,GAAKmC,EAAEnC,GAAKsF,GAAML,IAAO9C,EAAEnC,GAC/BsE,EAAgBY,EAAKK,EAAKN,EAAKE,EAAID,EAAII,EAAIC,EAAIL,EAAKK,EAAKJ,EAAKF,EAAIC,EAAI/C,EAAEnC,EAAGmC,EAAElC,KAEjFoF,EAAM/D,KAAKmE,IAAIP,EAAK/C,EAAElC,IAAMgF,EAAK9C,EAAEnC,GAE/B2E,EAAcxC,EAAG2C,KAChBO,EAAMG,GAAWH,IAAQG,IAAWrD,EAAEnC,EAAIgF,EAAEhF,GAAMmC,EAAEnC,IAAMgF,EAAEhF,GAAK0F,EAAqBV,EAAG7C,OAC1F6C,EAAI7C,EACJqD,EAASH,IAIjBlD,EAAIA,EAAE1B,WACD0B,IAAMiB,GAEf,OAAO4B,CACX,CAjEiBW,CAAeb,EAAMxE,GAClC,IAAKyE,EACD,OAAOzE,EAGX,IAAIsF,EAAgBf,EAAaE,EAAQD,GAIzC,OADA7C,EAAa2D,EAAeA,EAAcnF,MACnCwB,EAAa8C,EAAQA,EAAOtE,KACvC,CA0DA,SAASiF,EAAqBV,EAAG7C,GAC7B,OAAOC,EAAK4C,EAAEtE,KAAMsE,EAAG7C,EAAEzB,MAAQ,GAAK0B,EAAKD,EAAE1B,KAAMuE,EAAGA,EAAEvE,MAAQ,CACpE,CAwEA,SAAS+B,EAAOxC,EAAGC,EAAGL,EAAMC,EAAMK,GAe9B,OAPAF,EAAqB,aADrBA,EAAqB,YADrBA,EAAqB,YADrBA,EAAqB,WAHrBA,GAAKA,EAAIJ,GAAQM,EAAU,GAGjBF,GAAK,IACLA,GAAK,IACLA,GAAK,IACLA,GAAK,KAKfC,EAAqB,aADrBA,EAAqB,YADrBA,EAAqB,YADrBA,EAAqB,WAPrBA,GAAKA,EAAIJ,GAAQK,EAAU,GAOjBD,GAAK,IACLA,GAAK,IACLA,GAAK,IACLA,GAAK,KAEE,CACrB,CAGA,SAASgB,EAAYQ,GACjB,IAAIU,EAAIV,EACJoE,EAAWpE,EACf,IACQU,EAAEnC,EAAI6F,EAAS7F,GAAMmC,EAAEnC,IAAM6F,EAAS7F,GAAKmC,EAAElC,EAAI4F,EAAS5F,KAAI4F,EAAW1D,GAC7EA,EAAIA,EAAE1B,WACD0B,IAAMV,GAEf,OAAOoE,CACX,CAGA,SAASvB,EAAgBV,EAAIG,EAAIF,EAAIG,EAAIF,EAAIG,EAAI6B,EAAIC,GACjD,OAAQjC,EAAKgC,IAAO/B,EAAKgC,KAAQnC,EAAKkC,IAAO7B,EAAK8B,KAC1CnC,EAAKkC,IAAO9B,EAAK+B,KAAQlC,EAAKiC,IAAO/B,EAAKgC,KAC1ClC,EAAKiC,IAAO7B,EAAK8B,KAAQjC,EAAKgC,IAAO9B,EAAK+B,EACtD,CAGA,SAASnB,EAAgBnB,EAAGC,GACxB,OAAOD,EAAEhD,KAAKE,IAAM+C,EAAE/C,GAAK8C,EAAE/C,KAAKC,IAAM+C,EAAE/C,IA2C9C,SAA2B8C,EAAGC,GAC1B,IAAIvB,EAAIsB,EACR,EAAG,CACC,GAAItB,EAAExB,IAAM8C,EAAE9C,GAAKwB,EAAE1B,KAAKE,IAAM8C,EAAE9C,GAAKwB,EAAExB,IAAM+C,EAAE/C,GAAKwB,EAAE1B,KAAKE,IAAM+C,EAAE/C,GAC7D+D,EAAWvC,EAAGA,EAAE1B,KAAMgD,EAAGC,GAAI,OAAO,EAC5CvB,EAAIA,EAAE1B,IACd,OAAa0B,IAAMsB,GAEf,OAAO,CACX,CApDoDuC,CAAkBvC,EAAGC,KAC7DiB,EAAclB,EAAGC,IAAMiB,EAAcjB,EAAGD,IA6DpD,SAAsBA,EAAGC,GACrB,IAAIvB,EAAIsB,EACJwC,GAAS,EACTH,GAAMrC,EAAEzD,EAAI0D,EAAE1D,GAAK,EACnB+F,GAAMtC,EAAExD,EAAIyD,EAAEzD,GAAK,EACvB,GACUkC,EAAElC,EAAI8F,GAAS5D,EAAE1B,KAAKR,EAAI8F,GAAQ5D,EAAE1B,KAAKR,IAAMkC,EAAElC,GAC9C6F,GAAM3D,EAAE1B,KAAKT,EAAImC,EAAEnC,IAAM+F,EAAK5D,EAAElC,IAAMkC,EAAE1B,KAAKR,EAAIkC,EAAElC,GAAKkC,EAAEnC,IAC/DiG,GAAUA,GACd9D,EAAIA,EAAE1B,WACD0B,IAAMsB,GAEf,OAAOwC,CACX,CA1E0DC,CAAazC,EAAGC,KAC7DtB,EAAKqB,EAAE/C,KAAM+C,EAAGC,EAAEhD,OAAS0B,EAAKqB,EAAGC,EAAEhD,KAAMgD,KAC5C3B,EAAO0B,EAAGC,IAAMtB,EAAKqB,EAAE/C,KAAM+C,EAAGA,EAAEhD,MAAQ,GAAK2B,EAAKsB,EAAEhD,KAAMgD,EAAGA,EAAEjD,MAAQ,EACrF,CAGA,SAAS2B,EAAKD,EAAGQ,EAAGwD,GAChB,OAAQxD,EAAE1C,EAAIkC,EAAElC,IAAMkG,EAAEnG,EAAI2C,EAAE3C,IAAM2C,EAAE3C,EAAImC,EAAEnC,IAAMmG,EAAElG,EAAI0C,EAAE1C,EAC9D,CAGA,SAAS8B,EAAOqE,EAAIC,GAChB,OAAOD,EAAGpG,IAAMqG,EAAGrG,GAAKoG,EAAGnG,IAAMoG,EAAGpG,CACxC,CAGA,SAASyE,EAAW0B,EAAIE,EAAID,EAAIE,GAC5B,IAAIC,EAAKC,EAAKrE,EAAKgE,EAAIE,EAAID,IACvBK,EAAKD,EAAKrE,EAAKgE,EAAIE,EAAIC,IACvBI,EAAKF,EAAKrE,EAAKiE,EAAIE,EAAIH,IACvBQ,EAAKH,EAAKrE,EAAKiE,EAAIE,EAAID,IAE3B,OAAIE,IAAOE,GAAMC,IAAOC,MAEb,IAAPJ,IAAYK,EAAUT,EAAIC,EAAIC,QACvB,IAAPI,IAAYG,EAAUT,EAAIG,EAAID,QACvB,IAAPK,IAAYE,EAAUR,EAAID,EAAIG,OACvB,IAAPK,IAAYC,EAAUR,EAAIC,EAAIC,MAGtC,CAGA,SAASM,EAAU1E,EAAGQ,EAAGwD,GACrB,OAAOxD,EAAE3C,GAAKsB,KAAKC,IAAIY,EAAEnC,EAAGmG,EAAEnG,IAAM2C,EAAE3C,GAAKsB,KAAKwF,IAAI3E,EAAEnC,EAAGmG,EAAEnG,IAAM2C,EAAE1C,GAAKqB,KAAKC,IAAIY,EAAElC,EAAGkG,EAAElG,IAAM0C,EAAE1C,GAAKqB,KAAKwF,IAAI3E,EAAElC,EAAGkG,EAAElG,EACzH,CAEA,SAASwG,EAAKM,GACV,OAAOA,EAAM,EAAI,EAAIA,EAAM,GAAK,EAAI,CACxC,CAeA,SAASpC,EAAclB,EAAGC,GACtB,OAAOtB,EAAKqB,EAAE/C,KAAM+C,EAAGA,EAAEhD,MAAQ,EAC7B2B,EAAKqB,EAAGC,EAAGD,EAAEhD,OAAS,GAAK2B,EAAKqB,EAAGA,EAAE/C,KAAMgD,IAAM,EACjDtB,EAAKqB,EAAGC,EAAGD,EAAE/C,MAAQ,GAAK0B,EAAKqB,EAAGA,EAAEhD,KAAMiD,GAAK,CACvD,CAoBA,SAASmB,EAAapB,EAAGC,GACrB,IAAIsD,EAAK,IAAIC,EAAKxD,EAAE9C,EAAG8C,EAAEzD,EAAGyD,EAAExD,GAC1BiH,EAAK,IAAID,EAAKvD,EAAE/C,EAAG+C,EAAE1D,EAAG0D,EAAEzD,GAC1BkH,EAAK1D,EAAEhD,KACP2G,EAAK1D,EAAEhD,KAcX,OAZA+C,EAAEhD,KAAOiD,EACTA,EAAEhD,KAAO+C,EAETuD,EAAGvG,KAAO0G,EACVA,EAAGzG,KAAOsG,EAEVE,EAAGzG,KAAOuG,EACVA,EAAGtG,KAAOwG,EAEVE,EAAG3G,KAAOyG,EACVA,EAAGxG,KAAO0G,EAEHF,CACX,CAGA,SAASpF,EAAWnB,EAAGX,EAAGC,EAAG2B,GACzB,IAAIO,EAAI,IAAI8E,EAAKtG,EAAGX,EAAGC,GAYvB,OAVK2B,GAKDO,EAAE1B,KAAOmB,EAAKnB,KACd0B,EAAEzB,KAAOkB,EACTA,EAAKnB,KAAKC,KAAOyB,EACjBP,EAAKnB,KAAO0B,IAPZA,EAAEzB,KAAOyB,EACTA,EAAE1B,KAAO0B,GAQNA,CACX,CAEA,SAASH,EAAWG,GAChBA,EAAE1B,KAAKC,KAAOyB,EAAEzB,KAChByB,EAAEzB,KAAKD,KAAO0B,EAAE1B,KAEZ0B,EAAEM,QAAON,EAAEM,MAAMC,MAAQP,EAAEO,OAC3BP,EAAEO,QAAOP,EAAEO,MAAMD,MAAQN,EAAEM,MACnC,CAEA,SAASwE,EAAKtG,EAAGX,EAAGC,GAEhBoH,KAAK1G,EAAIA,EAGT0G,KAAKrH,EAAIA,EACTqH,KAAKpH,EAAIA,EAGToH,KAAK3G,KAAO,KACZ2G,KAAK5G,KAAO,KAGZ4G,KAAK9E,EAAI,EAGT8E,KAAK5E,MAAQ,KACb4E,KAAK3E,MAAQ,KAGb2E,KAAKtG,SAAU,CACnB,CA+BA,SAASc,EAAWpC,EAAMgC,EAAOC,EAAK/B,GAElC,IADA,IAAI2H,EAAM,EACD3G,EAAIc,EAAO8F,EAAI7F,EAAM/B,EAAKgB,EAAIe,EAAKf,GAAKhB,EAC7C2H,IAAQ7H,EAAK8H,GAAK9H,EAAKkB,KAAOlB,EAAKkB,EAAI,GAAKlB,EAAK8H,EAAI,IACrDA,EAAI5G,EAER,OAAO2G,CACX,CAlCA/H,EAAOiI,UAAY,SAAU/H,EAAMC,EAAaC,EAAKa,GACjD,IAAIL,EAAWT,GAAeA,EAAYU,OACtCC,EAAWF,EAAWT,EAAY,GAAKC,EAAMF,EAAKW,OAElDqH,EAAcnG,KAAKmE,IAAI5D,EAAWpC,EAAM,EAAGY,EAAUV,IACzD,GAAIQ,EACA,IAAK,IAAIQ,EAAI,EAAGC,EAAMlB,EAAYU,OAAQO,EAAIC,EAAKD,IAAK,CACpD,IAAIc,EAAQ/B,EAAYiB,GAAKhB,EACzB+B,EAAMf,EAAIC,EAAM,EAAIlB,EAAYiB,EAAI,GAAKhB,EAAMF,EAAKW,OACxDqH,GAAenG,KAAKmE,IAAI5D,EAAWpC,EAAMgC,EAAOC,EAAK/B,GACxD,CAGL,IAAI+H,EAAgB,EACpB,IAAK/G,EAAI,EAAGA,EAAIH,EAAUJ,OAAQO,GAAK,EAAG,CACtC,IAAI8C,EAAIjD,EAAUG,GAAKhB,EACnB+D,EAAIlD,EAAUG,EAAI,GAAKhB,EACvBgE,EAAInD,EAAUG,EAAI,GAAKhB,EAC3B+H,GAAiBpG,KAAKmE,KACjBhG,EAAKgE,GAAKhE,EAAKkE,KAAOlE,EAAKiE,EAAI,GAAKjE,EAAKgE,EAAI,KAC7ChE,EAAKgE,GAAKhE,EAAKiE,KAAOjE,EAAKkE,EAAI,GAAKlE,EAAKgE,EAAI,IACrD,CAED,OAAuB,IAAhBgE,GAAuC,IAAlBC,EAAsB,EAC9CpG,KAAKmE,KAAKiC,EAAgBD,GAAeA,EACjD,EAYAlI,EAAOoI,QAAU,SAAUlI,GAKvB,IAJA,IAAIE,EAAMF,EAAK,GAAG,GAAGW,OACjBwH,EAAS,CAACC,SAAU,GAAIC,MAAO,GAAIC,WAAYpI,GAC/CqI,EAAY,EAEPrH,EAAI,EAAGA,EAAIlB,EAAKW,OAAQO,IAAK,CAClC,IAAK,IAAI4G,EAAI,EAAGA,EAAI9H,EAAKkB,GAAGP,OAAQmH,IAChC,IAAK,IAAIU,EAAI,EAAGA,EAAItI,EAAKsI,IAAKL,EAAOC,SAAS7G,KAAKvB,EAAKkB,GAAG4G,GAAGU,IAE9DtH,EAAI,IACJqH,GAAavI,EAAKkB,EAAI,GAAGP,OACzBwH,EAAOE,MAAM9G,KAAKgH,GAEzB,CACD,OAAOJ,CACX,EACAtI,EAAS4I,QAAU1I,ECjqBnB,MAAM2I,EAAe,CAOnBC,UAAWC,EAAcA,eAACC,GAQ1BC,kBAAmBF,EAAcA,eAACG,IAMpCL,SAAwB,SAAUM,GAChC,OACEA,IAAiBN,EAAaC,WAC9BK,IAAiBN,EAAaI,iBAElC,GAEA,IAAAG,EAAeC,OAAOC,OAAOT,GCnB7B,MAAMU,EAAyB,IAAIC,EAAAA,WAC7BC,EAAyB,IAAID,EAAAA,WAK7BE,EAAkB,CAKxBA,cAAgC,SAAUC,GAExCC,EAAAA,MAAMC,QAAQ,YAAaF,GAC3BC,QAAME,OAAOC,OAAOC,oBAClB,mBACAL,EAAU7I,OACV,GAIF,MAAMA,EAAS6I,EAAU7I,OACzB,IAAIgC,EAAO,EAEX,IAAK,IAAImH,EAAKnJ,EAAS,EAAGoJ,EAAK,EAAGA,EAAKpJ,EAAQmJ,EAAKC,IAAM,CACxD,MAAMC,EAAKR,EAAUM,GACfG,EAAKT,EAAUO,GAErBpH,GAAQqH,EAAGzJ,EAAI0J,EAAGzJ,EAAIyJ,EAAG1J,EAAIyJ,EAAGxJ,CACjC,CAED,MAAc,GAAPmC,CACT,EAOA4G,sBAAwC,SAAUC,GAEhD,OADaD,EAAgBW,cAAcV,GAC7B,EAAMd,EAAaI,kBAAoBJ,EAAaC,SACpE,EASAY,YAA8B,SAAUC,EAAWnB,GAEjDoB,EAAAA,MAAMC,QAAQ,YAAaF,GAG3B,MAAMW,EAAqBC,EAAAA,WAAWC,UAAUb,GAChD,OAAO1J,EAAOqK,EAAoB9B,EAAO,EAC3C,GAEMiC,EAAuB,IAAIjB,EAAAA,WAC3BkB,EAAuB,IAAIlB,EAAAA,WAC3BmB,EAAuB,IAAInB,EAAAA,WAC3BoB,EAAuB,IAAIpB,EAAAA,WAC3BqB,EAAuB,IAAIrB,EAAAA,WAC3BsB,EAAuB,IAAItB,EAAAA,WAC3BuB,EAAwB,IAAIvB,EAAAA,WAclCE,EAAgBsB,mBAAqB,SACnCC,EACAtB,EACAuB,EACAC,GAEAA,EAAcC,EAAAA,aAAaD,EAAaE,EAAUA,WAACC,oBAGnD1B,EAAAA,MAAME,OAAOyB,OAAO,YAAaN,GACjCrB,EAAAA,MAAMC,QAAQ,YAAaF,GAC3BC,EAAAA,MAAMC,QAAQ,UAAWqB,GACzBtB,QAAME,OAAOC,OAAOC,oBAAoB,iBAAkBkB,EAAQpK,OAAQ,GAC1E8I,EAAAA,MAAME,OAAOC,OAAOtH,OAAO,qBAAsB,IAAKyI,EAAQpK,OAAS,EAAG,GAC1E8I,EAAKA,MAACE,OAAOC,OAAOyB,YAAY,cAAeL,EAAa,GAI5D,MAAMjK,EAAYgK,EAAQO,MAAM,GAGhC,IAAIpK,EACJ,MAAMP,EAAS6I,EAAU7I,OACnB4K,EAAsB,IAAIC,MAAe,EAAT7K,GACtC,IAAIuC,EAAI,EACR,IAAKhC,EAAI,EAAGA,EAAIP,EAAQO,IAAK,CAC3B,MAAMuK,EAAOjC,EAAUtI,GACvBqK,EAAoBrI,KAAOuI,EAAKlL,EAChCgL,EAAoBrI,KAAOuI,EAAKjL,EAChC+K,EAAoBrI,KAAOuI,EAAK3I,CACjC,CAED,MAAM4I,EAAoB,GAGpBC,EAAQ,CAAA,EAERC,EAASd,EAAUe,cACnBC,EAAcZ,EAAUA,WAACa,YAAYf,EAAaY,GAClDI,EAAkBF,EAAcA,EAEtC,KAAO/K,EAAUJ,OAAS,GAAG,CAC3B,MAAMsL,EAAKlL,EAAUmL,MACfnC,EAAKhJ,EAAUmL,MACfpC,EAAK/I,EAAUmL,MAEflC,EAAKX,EAAAA,WAAW8C,UACpBZ,EACK,EAALzB,EACAQ,GAEIL,EAAKZ,EAAAA,WAAW8C,UACpBZ,EACK,EAALxB,EACAQ,GAEI6B,EAAK/C,EAAAA,WAAW8C,UACpBZ,EACK,EAALU,EACAzB,GAGI6B,EAAKhD,EAAAA,WAAWiD,iBACpBjD,aAAWkD,UAAUvC,EAAIS,GACzBmB,EACAnB,GAEI+B,EAAKnD,EAAAA,WAAWiD,iBACpBjD,aAAWkD,UAAUtC,EAAIS,GACzBkB,EACAlB,GAEI+B,EAAKpD,EAAAA,WAAWiD,iBACpBjD,aAAWkD,UAAUH,EAAIzB,GACzBiB,EACAjB,GAGI+B,EAAKrD,EAAAA,WAAWsD,iBACpBtD,EAAAA,WAAWuD,SAASP,EAAIG,EAAI5B,IAExBiC,EAAKxD,EAAAA,WAAWsD,iBACpBtD,EAAAA,WAAWuD,SAASJ,EAAIC,EAAI7B,IAExBkC,EAAKzD,EAAAA,WAAWsD,iBACpBtD,EAAAA,WAAWuD,SAASH,EAAIJ,EAAIzB,IAGxB9I,EAAMD,KAAKC,IAAI4K,EAAIG,EAAIC,GAC7B,IAAIC,EACAC,EAIAlL,EAAMkK,EACJU,IAAO5K,GACTiL,EAAOlL,KAAKwF,IAAIyC,EAAIC,GAAM,IAAMlI,KAAKC,IAAIgI,EAAIC,GAE7C7I,EAAIyK,EAAMoB,GACLrD,EAAAA,QAAQxI,KACX8L,EAAM3D,EAAUA,WAAC4D,IAAIjD,EAAIC,EAAIW,GAC7BvB,EAAAA,WAAWiD,iBAAiBU,EAAK,GAAKA,GACtCzB,EAAoBhK,KAAKyL,EAAIzM,EAAGyM,EAAIxM,EAAGwM,EAAIlK,GAC3C5B,EAAIqK,EAAoB5K,OAAS,EAAI,EACrCgL,EAAMoB,GAAQ7L,GAGhBH,EAAUQ,KAAKuI,EAAI5I,EAAG+K,GACtBlL,EAAUQ,KAAKL,EAAG6I,EAAIkC,IACbY,IAAO/K,GAChBiL,EAAOlL,KAAKwF,IAAI0C,EAAIkC,GAAM,IAAMpK,KAAKC,IAAIiI,EAAIkC,GAE7C/K,EAAIyK,EAAMoB,GACLrD,EAAAA,QAAQxI,KACX8L,EAAM3D,EAAUA,WAAC4D,IAAIhD,EAAImC,EAAIxB,GAC7BvB,EAAAA,WAAWiD,iBAAiBU,EAAK,GAAKA,GACtCzB,EAAoBhK,KAAKyL,EAAIzM,EAAGyM,EAAIxM,EAAGwM,EAAIlK,GAC3C5B,EAAIqK,EAAoB5K,OAAS,EAAI,EACrCgL,EAAMoB,GAAQ7L,GAGhBH,EAAUQ,KAAKwI,EAAI7I,EAAG4I,GACtB/I,EAAUQ,KAAKL,EAAG+K,EAAInC,IACbgD,IAAOhL,IAChBiL,EAAOlL,KAAKwF,IAAI4E,EAAInC,GAAM,IAAMjI,KAAKC,IAAImK,EAAInC,GAE7C5I,EAAIyK,EAAMoB,GACLrD,EAAAA,QAAQxI,KACX8L,EAAM3D,EAAUA,WAAC4D,IAAIb,EAAIpC,EAAIY,GAC7BvB,EAAAA,WAAWiD,iBAAiBU,EAAK,GAAKA,GACtCzB,EAAoBhK,KAAKyL,EAAIzM,EAAGyM,EAAIxM,EAAGwM,EAAIlK,GAC3C5B,EAAIqK,EAAoB5K,OAAS,EAAI,EACrCgL,EAAMoB,GAAQ7L,GAGhBH,EAAUQ,KAAK0K,EAAI/K,EAAG6I,GACtBhJ,EAAUQ,KAAKL,EAAG4I,EAAIC,KAGxB2B,EAAkBnK,KAAKuI,GACvB4B,EAAkBnK,KAAKwI,GACvB2B,EAAkBnK,KAAK0K,GAE1B,CAED,OAAO,IAAIiB,EAAAA,SAAS,CAClBC,WAAY,CACVC,SAAU,IAAIC,EAAAA,kBAAkB,CAC9BC,kBAAmBC,EAAiBA,kBAACC,OACrCC,uBAAwB,EACxBC,OAAQnC,KAGZR,QAASW,EACTiC,cAAeC,EAAaA,cAACC,WAEjC,EAEA,MAAMC,EAAuB,IAAIC,EAAAA,aAC3BC,EAAuB,IAAID,EAAAA,aAC3BE,EAAuB,IAAIF,EAAAA,aAC3BG,EAAiC,IAAIH,EAAAA,aAc3CxE,EAAgB4E,4BAA8B,SAC5CrD,EACAtB,EACAuB,EACAC,GAEAA,EAAcC,EAAAA,aAAaD,EAAaE,EAAUA,WAACC,oBAGnD1B,EAAAA,MAAME,OAAOyB,OAAO,YAAaN,GACjCrB,EAAAA,MAAMC,QAAQ,YAAaF,GAC3BC,EAAAA,MAAMC,QAAQ,UAAWqB,GACzBtB,QAAME,OAAOC,OAAOC,oBAAoB,iBAAkBkB,EAAQpK,OAAQ,GAC1E8I,EAAAA,MAAME,OAAOC,OAAOtH,OAAO,qBAAsB,IAAKyI,EAAQpK,OAAS,EAAG,GAC1E8I,EAAKA,MAACE,OAAOC,OAAOyB,YAAY,cAAeL,EAAa,GAI5D,MAAMjK,EAAYgK,EAAQO,MAAM,GAGhC,IAAIpK,EACJ,MAAMP,EAAS6I,EAAU7I,OACnB4K,EAAsB,IAAIC,MAAe,EAAT7K,GACtC,IAAIuC,EAAI,EACR,IAAKhC,EAAI,EAAGA,EAAIP,EAAQO,IAAK,CAC3B,MAAMuK,EAAOjC,EAAUtI,GACvBqK,EAAoBrI,KAAOuI,EAAKlL,EAChCgL,EAAoBrI,KAAOuI,EAAKjL,EAChC+K,EAAoBrI,KAAOuI,EAAK3I,CACjC,CAED,MAAM4I,EAAoB,GAGpBC,EAAQ,CAAA,EAERC,EAASd,EAAUe,cACnBC,EAAcZ,EAAUA,WAACa,YAAYf,EAAaY,GAElDwC,EAAS,IAAIC,EAAkBA,wBAACC,OAAWA,EAAWxD,GACtDyD,EAAS,IAAIF,EAAkBA,wBAACC,OAAWA,EAAWxD,GACtD0D,EAAS,IAAIH,EAAkBA,wBAACC,OAAWA,EAAWxD,GAE5D,KAAO/J,EAAUJ,OAAS,GAAG,CAC3B,MAAMsL,EAAKlL,EAAUmL,MACfnC,EAAKhJ,EAAUmL,MACfpC,EAAK/I,EAAUmL,MAEflC,EAAKX,EAAAA,WAAW8C,UACpBZ,EACK,EAALzB,EACAQ,GAEIL,EAAKZ,EAAAA,WAAW8C,UACpBZ,EACK,EAALxB,EACAQ,GAEI6B,EAAK/C,EAAAA,WAAW8C,UACpBZ,EACK,EAALU,EACAzB,GAGIiE,EAAK3D,EAAU4D,wBAAwB1E,EAAI8D,GAC3Ca,EAAK7D,EAAU4D,wBAAwBzE,EAAI+D,GAC3CY,EAAK9D,EAAU4D,wBAAwBtC,EAAI6B,GAEjDG,EAAOS,aAAaJ,EAAIE,GACxB,MAAMjC,EAAK0B,EAAOU,gBAClBP,EAAOM,aAAaF,EAAIC,GACxB,MAAM/B,EAAK0B,EAAOO,gBAClBN,EAAOK,aAAaD,EAAIH,GACxB,MAAM3B,EAAK0B,EAAOM,gBAEZhN,EAAMD,KAAKC,IAAI4K,EAAIG,EAAIC,GAC7B,IAAIC,EACAC,EACA+B,EACAC,EAGAlN,EAAMgK,EACJY,IAAO5K,GACTiL,EAAOlL,KAAKwF,IAAIyC,EAAIC,GAAM,IAAMlI,KAAKC,IAAIgI,EAAIC,GAE7C7I,EAAIyK,EAAMoB,GACLrD,EAAAA,QAAQxI,KACX8L,EAAMoB,EAAOa,yBACX,GACAf,GAEFa,EAAsC,IAAzBN,EAAGS,OAASP,EAAGO,QAC5BF,EAAgB3F,EAAUA,WAAC8F,YACzBnC,EAAIoC,UACJpC,EAAIqC,SACJN,EACAjE,EACAF,GAEFW,EAAoBhK,KAClByN,EAAczO,EACdyO,EAAcxO,EACdwO,EAAclM,GAEhB5B,EAAIqK,EAAoB5K,OAAS,EAAI,EACrCgL,EAAMoB,GAAQ7L,GAGhBH,EAAUQ,KAAKuI,EAAI5I,EAAG+K,GACtBlL,EAAUQ,KAAKL,EAAG6I,EAAIkC,IACbY,IAAO/K,GAChBiL,EAAOlL,KAAKwF,IAAI0C,EAAIkC,GAAM,IAAMpK,KAAKC,IAAIiI,EAAIkC,GAE7C/K,EAAIyK,EAAMoB,GACLrD,EAAAA,QAAQxI,KACX8L,EAAMuB,EAAOU,yBACX,GACAf,GAEFa,EAAsC,IAAzBJ,EAAGO,OAASN,EAAGM,QAC5BF,EAAgB3F,EAAUA,WAAC8F,YACzBnC,EAAIoC,UACJpC,EAAIqC,SACJN,EACAjE,EACAF,GAEFW,EAAoBhK,KAClByN,EAAczO,EACdyO,EAAcxO,EACdwO,EAAclM,GAEhB5B,EAAIqK,EAAoB5K,OAAS,EAAI,EACrCgL,EAAMoB,GAAQ7L,GAGhBH,EAAUQ,KAAKwI,EAAI7I,EAAG4I,GACtB/I,EAAUQ,KAAKL,EAAG+K,EAAInC,IACbgD,IAAOhL,IAChBiL,EAAOlL,KAAKwF,IAAI4E,EAAInC,GAAM,IAAMjI,KAAKC,IAAImK,EAAInC,GAE7C5I,EAAIyK,EAAMoB,GACLrD,EAAAA,QAAQxI,KACX8L,EAAMwB,EAAOS,yBACX,GACAf,GAEFa,EAAsC,IAAzBH,EAAGM,OAAST,EAAGS,QAC5BF,EAAgB3F,EAAUA,WAAC8F,YACzBnC,EAAIoC,UACJpC,EAAIqC,SACJN,EACAjE,EACAF,GAEFW,EAAoBhK,KAClByN,EAAczO,EACdyO,EAAcxO,EACdwO,EAAclM,GAEhB5B,EAAIqK,EAAoB5K,OAAS,EAAI,EACrCgL,EAAMoB,GAAQ7L,GAGhBH,EAAUQ,KAAK0K,EAAI/K,EAAG6I,GACtBhJ,EAAUQ,KAAKL,EAAG4I,EAAIC,KAGxB2B,EAAkBnK,KAAKuI,GACvB4B,EAAkBnK,KAAKwI,GACvB2B,EAAkBnK,KAAK0K,GAE1B,CAED,OAAO,IAAIiB,EAAAA,SAAS,CAClBC,WAAY,CACVC,SAAU,IAAIC,EAAAA,kBAAkB,CAC9BC,kBAAmBC,EAAiBA,kBAACC,OACrCC,uBAAwB,EACxBC,OAAQnC,KAGZR,QAASW,EACTiC,cAAeC,EAAaA,cAACC,WAEjC,EAWAtE,EAAgB+F,sBAAwB,SACtC9F,EACA0F,EACApE,EACAyE,GAEAzE,EAAYG,EAAAA,aAAaH,EAAW0E,EAASA,UAACC,OAE9C,IAAIzK,EAAIoE,EACJ1G,EAAI4G,EAKR,GAHA4F,EAASjE,EAAYA,aAACiE,EAAQ,GAC9BK,EAAiBtE,EAAYA,aAACsE,GAAgB,GAE1C7F,EAAAA,QAAQF,GAAY,CACtB,MAAM7I,EAAS6I,EAAU7I,OAEzB,IAAK,IAAIO,EAAI,EAAGA,EAAIP,EAAQO,GAAK,EAC/BmI,EAAAA,WAAW8C,UAAU3C,EAAWtI,EAAGwB,GAE/B6M,IACF7M,EAAIoI,EAAU4E,uBAAuBhN,EAAGA,IAG3B,IAAXwM,IACFlK,EAAI8F,EAAU6E,sBAAsBjN,EAAGsC,GAEvCqE,EAAAA,WAAWiD,iBAAiBtH,EAAGkK,EAAQlK,GACvCqE,EAAAA,WAAW4D,IAAIvK,EAAGsC,EAAGtC,IAGvB8G,EAAUtI,GAAKwB,EAAEnC,EACjBiJ,EAAUtI,EAAI,GAAKwB,EAAElC,EACrBgJ,EAAUtI,EAAI,GAAKwB,EAAEI,CAExB,CAED,OAAO0G,CACT"}