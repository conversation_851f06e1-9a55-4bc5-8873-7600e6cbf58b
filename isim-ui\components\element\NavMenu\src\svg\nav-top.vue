<!--
* @Author: 任强
* @Date: 2023/4/10 9:17
* @Version: 1.0
* @Content: 
-->
<template>
  <svg
    v-if="collapse"
    width="60.000000"
    height="18.000000"
    viewBox="0 0 60 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <path d="M0 18L60 18L60 10L52 2L42 2L38 6L22 6L18 2L8 2L0 10L0 18Z" fill-rule="evenodd" fill="url(#paint_linear_15_2704_0)" />
    <path
      d="M8 2L0 10L0 18L60 18L60 10L52 2L42 2L38 6L22 6L18 2L8 2ZM58.9995 16.9995L58.9995 10.4144L51.5856 3.00049L42.4144 3.00049L38.4144 7.00049L21.5856 7.00049L17.5856 3.00049L8.41443 3.00049L1.0005 10.4144L1.0005 16.9995L58.9995 16.9995Z"
      fill-rule="evenodd"
      fill="#FFFFFF"
    />
    <path
      d="M10 0L50 0L51 1L41 1L37 5L23 5L19 1L9 1L10 0Z"
      clip-rule="evenodd"
      fill-rule="evenodd"
      fill="var(--primary-color)"
      fill-opacity="1.000000"
    />
    <defs>
      <linearGradient id="paint_linear_15_2704_0" x1="29.999992" y1="17.999998" x2="29.999992" y2="2.000977" gradientUnits="userSpaceOnUse">
        <stop stop-color="#F2F2F2" />
        <stop offset="0.361544" stop-color="#C2C2C2" />
        <stop offset="0.500374" stop-color="#999999" />
        <stop offset="0.501206" stop-color="#F2F2F2" />
        <stop offset="0.575500" stop-color="#FFFFFF" />
        <stop offset="1.000000" stop-color="#D4D4D4" />
      </linearGradient>
    </defs>
  </svg>
  <svg
    v-else
    width="256.000000"
    height="18.000000"
    viewBox="0 0 256 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <g id="组合 2955">
      <g id="矩形 1799">
        <path
          d="M0 18L256 18L256 10L248 2L232 2L235 5L228 5L225 2L219 2L222 5L215 5L212 2L140 2L136 6L120 6L116 2L47 2L44 2L41 5L34 5L37 2L31 2L28 5L21 5L24 2L8 2L0 10L0 18Z"
          fill-rule="nonzero"
          fill="url(#paint_linear_15_2621_0)"
        />
        <path
          d="M8 2L0 10L0 18L256 18L256 10L248 2L232 2L235 5L228 5L225 2L219 2L222 5L215 5L212 2L140 2L136 6L120 6L116 2L44 2L41 5L34 5L37 2L31 2L28 5L21 5L24 2L8 2ZM255 16.9995L255 10.4144L247.586 3.00049L234.415 3.00049L237.415 6.00049L227.586 6.00049L224.586 3.00049L221.415 3.00049L224.415 6.00049L214.586 6.00049L211.586 3.00049L140.414 3.00049L136.414 7.00049L119.586 7.00049L115.586 3.00049L44.4144 3.00049L41.4144 6.00049L31.5846 6.00049L34.5846 3.00049L31.4144 3.00049L28.4144 6.00049L18.5846 6.00049L21.5846 3.00049L8.41442 3.00049L1.0005 10.4144L1.0005 16.9995L255 16.9995Z"
          fill-rule="evenodd"
          fill="#FFFFFF"
        />
      </g>
      <g id="合并">
        <path
          d="M24 4L28 0L228 0L232 4L229 4L226 1L216 1L219 4L216 4L213 1L139 1L135 5L121 5L117 1L43 1L40 4L37 4L40 1L30 1L27 4L24 4Z"
          clip-rule="evenodd"
          fill-rule="evenodd"
          fill="var(--primary-color)"
          fill-opacity="1.000000"
        />
      </g>
    </g>
    <defs>
      <linearGradient id="paint_linear_15_2621_0" x1="127.999969" y1="17.999998" x2="127.999969" y2="2.000977" gradientUnits="userSpaceOnUse">
        <stop stop-color="#F2F2F2" />
        <stop offset="0.361544" stop-color="#C2C2C2" />
        <stop offset="0.500374" stop-color="#999999" />
        <stop offset="0.501206" stop-color="#F2F2F2" />
        <stop offset="0.575500" stop-color="#FFFFFF" />
        <stop offset="1.000000" stop-color="#D4D4D4" />
      </linearGradient>
    </defs>
  </svg>
</template>

<script lang="ts">
export default {
  name: 'NavTop'
};
</script>
<script setup lang="ts">
defineProps({
  collapse: {
    type: Boolean,
    default: false
  }
});
</script>
