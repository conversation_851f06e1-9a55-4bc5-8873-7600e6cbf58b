/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/08/02 18:50:55
 * @description SectorDetectionPrimitive 用于 Scene2D 场景下的探测范围
 * @version 1.0
 * */

import AbstractPrimitive from './AbstractPrimitive.js';
import PolylinePrimitive from './PolylinePrimitive.js';
import PolygonVolumePrimitive from './PolygonVolumePrimitive.js';
import { getSectorDetectionPositions } from './PrimitiveMethod.js';
import { Cartesian3, Color, defaultValue, HeightReference } from 'cesium';

export default class SectorDetectionPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._lineOptions = {
      depthFailShow: false,
      color: Color.YELLOW,
      width: 1,
      materialType: 'Color',
      loop: true,
      ...options.lineOptions
    };

    this._fillOptions = { depthFailShow: false, color: Color.YELLOW.withAlpha(0.2), perPositionHeight: true, ...options.fillOptions };
    this._outline = defaultValue(options.outline, true);
    this._fill = defaultValue(options.fill, true);
    this._radius = defaultValue(options.radius, 100);
    this._heading = defaultValue(options.heading, 0);
    this._halfAngle = defaultValue(options.halfAngle, 30);
    this._slice = defaultValue(options.slice, 64);
    this._position = options.position;
    if (this._position) {
      this._updatePositions();
    }
    this._update = true;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive = this._primitive && this._primitive.destroy();
      this._linePrimitive = this._linePrimitive && this._linePrimitive.destroy();
      if (!this._positions || this._positions.length === 0) {
        return;
      }
      this._fill && (this._primitive = new PolygonVolumePrimitive({ ...this._fillOptions, id: this._id, positions: this._positions }));
      this._outline && (this._linePrimitive = new PolylinePrimitive({ ...this._lineOptions, id: this._id, positions: this._positions }));
    }
    this._primitive?.update(frameState);
    this._linePrimitive?.update(frameState);
  }

  _updatePositions() {
    this._positions = getSectorDetectionPositions(this._position, this._radius, this._heading, this._halfAngle, this._slice);
    if (this._primitive) {
      this._primitive.positions = this._positions;
    }
    if (this._linePrimitive) {
      this._linePrimitive.positions = this._positions;
    }
  }

  destroy() {
    this._linePrimitive = this._linePrimitive && this._linePrimitive.destroy();
    super.destroy();
  }

  set slice(value) {
    if (this._slice === value) {
      return;
    }
    this._slice = value;
    this._updatePositions();
  }

  set heading(value) {
    if (this._heading === value) {
      return;
    }
    this._heading = value;
    this._updatePositions();
  }

  set halfAngle(value) {
    if (this._halfAngle === value) {
      return;
    }
    this._halfAngle = value;
    this._updatePositions();
  }

  set radius(value) {
    if (this._radius === value) {
      return;
    }
    this._radius = value;
    this._updatePositions();
  }

  set position(value) {
    if (Cartesian3.equals(this._position, value)) {
      return;
    }
    this._position = value;
    this._updatePositions();
  }

  set lineOptions(value) {
    if (this._linePrimitive) {
      Object.keys(value).forEach((k) => {
        this._linePrimitive[k] = value[k];
      });
    }
    this._lineOptions = {
      ...this._lineOptions,
      ...value
    };
  }

  set outline(value) {
    if (this._outline === value) {
      return;
    }
    this._outline = value;
    this._update = true;
  }

  set fill(value) {
    if (this._fill === value) {
      return;
    }
    this._fill = value;
    this._update = true;
  }

  set fillOptions(value) {
    if (this._primitive) {
      Object.keys(value).forEach((k) => {
        this._primitive[k] = value[k];
      });
    }
    this._fillOptions = {
      ...this._fillOptions,
      ...value
    };
  }
}
