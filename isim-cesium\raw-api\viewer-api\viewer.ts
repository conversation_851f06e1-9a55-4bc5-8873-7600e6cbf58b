import { Viewer } from 'cesium';
import { assign, defaultConfig, emitViewerCreated, executeDestroyed } from 'isim-cesium';

export const viewerMap: Map<string, Viewer> = new Map();

export const DEFAULT_NAME = defaultConfig.DEFAULT_VIEWER_NAME;

const { DEFAULT_VIEWER_NAME, viewerConfig } = defaultConfig;

export type ViewerNameType = string | Viewer;

export type CreateViewerType = Viewer.ConstructorOptions;

/**
 * 创建Viewer视图
 * @param name
 * @param opt
 */
export function createViewer(name: string, opt?: CreateViewerType) {
  if (viewerMap.has(name as string)) {
    console.warn(`名称为 ${name} 的视图已经存在;`);
    return viewerMap.get(name)!;
  }
  viewerMap.set(name, new Viewer(name, assign({}, viewerConfig, opt ?? {})));
  const viewer = viewerMap.get(name)!;
  emitViewerCreated(name, viewer);
  hiddenAdvertise(viewer);
  toggleTimeline(false);
  toggleAnimation(false);
  return viewer;
}

/**
 * 获取视图
 * @param viewerName
 */
export function getViewer(viewerName: ViewerNameType = DEFAULT_VIEWER_NAME): Viewer {
  if (isViewer(viewerName)) {
    return viewerName;
  }
  return viewerMap.get(viewerName)!;
}

export function getViewerName(viewerName?: string) {
  return viewerName ?? DEFAULT_VIEWER_NAME;
}

/**
 * 删除Viewer
 * @param viewerName
 */
export function deleteViewerFromMap(viewerName = DEFAULT_NAME) {
  viewerMap.delete(viewerName);
}

/**
 * 摧毁Viewer
 * @param viewerName
 */
export function destroyViewer(viewerName = DEFAULT_NAME) {
  const _viewer = getViewer(viewerName);
  executeDestroyed(viewerName);
  getViewer(_viewer)?.destroy();
  deleteViewerFromMap(viewerName);
}

/**
 * 清空Viewer视图
 * @constructor
 */
export function ClearViewer() {}

/**
 * 判断是否存在Viewer
 * @param viewerName
 */
export function hasViewer(viewerName: string = defaultConfig.DEFAULT_VIEWER_NAME) {
  return viewerMap.has(viewerName);
}

/**
 * 判断是否存在Viewer 不存在则抛出警告
 * @param viewerName
 */
export function hasViewerWithWarn(viewerName: ViewerNameType) {
  if (!viewerName || !isViewer(viewerName)) {
    console.warn('请先创建Viewer');
    return false;
  }
  return true;
}

/**
 * 判断是不是viewer
 * @param viewerName
 */
export function isViewer(viewerName: any): viewerName is Viewer {
  return viewerName instanceof Viewer;
}

/**
 * 获取viewer的属性
 * @param field
 */
export function getViewerField<T extends keyof Viewer>(field: T) {
  return function (viewerName?: ViewerNameType) {
    return getViewer(viewerName)[field];
  };
}

/**
 * 隐藏广告
 * @param viewerName
 */
export function hiddenAdvertise(viewerName?: ViewerNameType): void {
  const bottomCon = getViewer(viewerName).bottomContainer as unknown as HTMLElement;
  bottomCon.style.display = 'none';
}

export function toggleAnimation(state: boolean, viewerName?: ViewerNameType): void {
  const animationContainer = getViewer(viewerName)?.animation?.container as unknown as HTMLElement;
  if (animationContainer) {
    animationContainer.style.visibility = state ? 'visible' : 'hidden';
  }
}

export function toggleTimeline(state: boolean, viewerName?: ViewerNameType): void {
  const timeLineContainer = getViewer(viewerName)?.timeline?.container as unknown as HTMLElement;
  if (timeLineContainer) {
    timeLineContainer.style.visibility = state ? 'visible' : 'hidden';
  }
}

/**
 * flyTo
 * @param target
 * @param options
 * @param viewerName
 */
export function viewerFlyTo(
  target: Parameters<InstanceType<typeof Viewer>['flyTo']>[0],
  options?: Parameters<InstanceType<typeof Viewer>['flyTo']>[1],
  viewerName?: string
) {
  getViewer(viewerName).flyTo(target, options);
}

export const getViewerContainer = getViewerField('container');

export const getViewerCanvas = getViewerField('canvas');

type CursorType = 'crosshair' | 'pointer' | 'default' | 'move' | '';

/**
 * 设置鼠标样式
 * @param cursorStyle
 * @param viewerName
 */
export function setViewerCursor(cursorStyle: CursorType = '', viewerName?: ViewerNameType) {
  const container = getViewerCanvas(viewerName);
  container.style.cursor = cursorStyle;
}

/**
 * 设置Viewer的属性
 * @param field
 */
export function setViewerValueByField<T extends keyof Viewer>(field: T) {
  return function (value: Viewer[T], viewerName?: ViewerNameType) {
    getViewer(viewerName)[field] = value;
  };
}
