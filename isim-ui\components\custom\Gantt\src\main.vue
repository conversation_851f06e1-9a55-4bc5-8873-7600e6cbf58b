<template>
  <div ref="ganttRef" :class="ns.b()">
    <div :class="ns.e('left')">
      <div :class="ns.b('header')">
        <slot name="leftHeaderText" :label="leftProp.label">
          <span>{{ leftProp.label }}</span>
        </slot>
        <div v-if="$slots.leftTool" :class="ns.be('header', 'lefttool')">
          <slot name="leftTool"></slot>
        </div>
      </div>
      <div ref="leftContent" :class="ns.b('content')">
        <div :class="ns.b('scroll')">
          <left-node v-for="(item, dex) in ganttData" :key="dex" :data="item">
            <template #leftIcon>
              <slot name="leftIcon" :data="item"></slot>
            </template>

            <template #left="{ data: _data }">
              <slot name="left" :data="_data"></slot>
            </template>
          </left-node>
        </div>
      </div>
    </div>
    <div ref="rightTag" :class="ns.e('right')">
      <div :class="ns.b('header')">
        <slot name="timeLine" :start-time="startTime" :end-time="endTime">
          <time-line v-bind="props" />
        </slot>
      </div>
      <div ref="rightContent" :class="ns.b('content')">
        <div :class="ns.b('scroll')">
          <right-node v-for="(item, dex) in ganttData" :key="dex" :data="item">
            <template #range="{ data: _data, offset, width, updateRangeFn }">
              <slot name="range" :data="_data" :offset="offset" :width="width" :update-range-fn="updateRangeFn"></slot>
            </template>
          </right-node>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ISimGantt'
};
</script>
<script lang="ts" setup>
import LeftNode from './leftNode.vue';
import RightNode from './rightNode.vue';
import { computed, onMounted, provide, ref, toRef, watch } from 'vue';
import { useNamespace } from 'isim-ui';
import { GanttDataOptions, ganttEventProvideKey, ganttProvideKey, generateGanttProps } from './gantt';
import { GanttNode } from './methods/ganttNode';
import TimeLine from './time-line.vue';

import { useRightContentSize } from './methods/contentSize';
import { useScrollEventBind } from './methods/scrollEventBind';

const props = defineProps({
  ...generateGanttProps()
});
const emit = defineEmits<{
  'update:startTime': [time: number];
  'update:endTime': [time: number];
  'update:currentTime': [time: number];
  contextmenu: [event: MouseEvent, data: GanttDataOptions];
  'left-click': [event: MouseEvent, data: GanttDataOptions];
  'update:mousewheel': [event: MouseEvent];
}>();
const ns = useNamespace('gantt');

const ganttRef = ref<HTMLElement>();

const { leftContent, rightContent } = useScrollEventBind();

const _ganttHandle = new GanttNode(props);
const ganttData = ref<GanttDataOptions[]>(_ganttHandle.handleData());

watch(
  () => props.data,
  (val) => {
    const _ganttHandle = new GanttNode(props);
    // @ts-ignore
    ganttData.value = _ganttHandle.handleData();
  }
);

// 获取整个甘特图区域能容纳多少个时间刻度
const { rightTag, timeRange, rightWidth, updateTimeRangeUp, updateTimeRangeDown } = useRightContentSize();

// 根据容纳时间的刻度计算出每个时间刻度需要显示多少的时间
const interval = computed(() => {
  const start = GanttNode.getTime(props.startTime);
  const end = GanttNode.getTime(props.endTime);
  return (end - start) / timeRange.value;
});

const rangeDoubleClick = (data: GanttDataOptions) => {
  emit('update:startTime', data._startTime);
  emit('update:endTime', data._endTime);
};

const updateCurrentTime = (time: Number) => {
  emit('update:currentTime', time as number);
};
onMounted(() => {
  // TODO
  // document.documentElement.addEventListener('dblclick', (e: MouseEvent) => {
  //   if (!ganttRef.value?.contains(e.target as HTMLElement)) {
  //     emit('update:startTime', props.startTime as number)
  //     emit('update:endTime', props.endTime as number)
  //   }
  // })
});

const updateMouseWheel = (event: MouseEvent) => {
  emit('update:mousewheel', event);
};

const ganttHandle = _ganttHandle;
provide(ganttProvideKey, {
  leftProp: toRef(props, 'leftProp'),
  labelField: toRef(props, 'labelField'),
  childrenField: toRef(props, 'childrenField'),
  ganttHandle,
  ganttData,
  startTime: toRef(props, 'startTime'),
  endTime: toRef(props, 'endTime'),
  interval: interval.value,
  rightWidth,
  timeRange,

  // getPxFromSecond,
  rangeDoubleClick,
  updateCurrentTime
});
const handleContextmenu = (event: MouseEvent, data: GanttDataOptions) => {
  emit('contextmenu', event, data);
};
const handleClick = (event: MouseEvent, data: GanttDataOptions) => {
  emit('left-click', event, data);
};
provide(ganttEventProvideKey, {
  handleContextmenu,
  handleClick,
  updateMouseWheel,
  updateTimeRangeUp,
  updateTimeRangeDown
});
</script>
<style lang="less">
@node-height: 30px;
@left-width: 300px;
@header-height: 30px;
.i-gantt {
  --border-color-6: var(--model-bg-color);
  --range-bgc: rgba(var(--isim-border-color-val), 0.7);
  @red-color: #f00;
  @black-color: #000;
  display: flex;

  .i-gantt-header {
    position: relative;
    height: @header-height;
    line-height: @header-height;
    border-bottom: 1px solid var(--border-color-6);
    box-sizing: border-box;
  }

  .i-gantt-content {
    height: calc(100% - @header-height);
    overflow-y: auto;
  }

  .i-gantt-scroll {
    width: 100%;
    background: linear-gradient(0, var(--border-color-6) 1px, transparent 1px, transparent 100%);
    background-size: 100% @node-height;
  }

  // left
  .i-gantt__left {
    width: @left-width;
    border-right: 1px solid var(--border-color-6);

    .i-gantt-header {
      display: flex;
      justify-content: space-between;
      padding: 0 4px;
    }

    .i-gantt-content {
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .i-gantt-left {
      &__current {
        height: @node-height;
        display: flex;
        align-items: center;
      }

      &__icon {
        display: flex;
        align-items: center;
        transform: rotate(0);
        transition: all 200ms linear;

        & > svg {
          width: 20px;
          height: 20px;
        }
      }

      &__text {
      }
    }
  }

  // right
  .i-gantt__right {
    position: relative;
    flex-grow: 1;
    width: calc(100% - @left-width);

    .i-gantt-time__current {
      position: absolute;
      top: 0;
      display: flex;
      align-items: flex-start;
      width: 1px;
      height: 100%;
      background-color: fade(@red-color, 100);
      z-index: 10;

      .i-gantt-time__text {
        display: flex;
        white-space: nowrap;
        background-color: @black-color;
        padding: 0 3px;
        // box-shadow: 5px 5px 5px 5px fade(@white-color, 20%);
      }
    }

    .is__end {
      justify-content: flex-end;
    }

    .i-gantt-header {
      overflow: hidden;

      &__time {
        position: absolute;
        top: -3px;
        white-space: nowrap;
        font-size: 13px;
      }
    }

    .i-gantt-content {
      overflow-x: hidden;
    }

    .i-gantt-scroll {
      // background-image: url('./logo.png');
      min-width: 100%;
    }

    .i-gantt-header__scale {
      position: absolute;
      left: 0;
      bottom: 3px;
      width: 100%;
      background-image: linear-gradient(90deg, fadevar(--white-color-5) 1px, transparent 1px, transparent 100%);
      background-repeat: repeat-x;
    }

    .i-gantt-header__scale--long {
      height: 10px;
      background-size: 200px 100%;
    }

    .i-gantt-header__scale--short {
      height: 5px;
      background-size: 5px 100%;
    }

    .i-gantt-right {
      position: relative;

      &__current {
        height: @node-height;
        display: flex;
        align-items: center;
      }

      &__range {
        background-color: var(--range-bgc);
        height: 20px;
        border-radius: 2px;
        padding: 0 2px;
        box-sizing: border-box;
        white-space: nowrap;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }

  .is-expand {
    transform: rotate(90deg) !important;
  }
}
</style>
