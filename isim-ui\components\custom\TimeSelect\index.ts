/**
 * @Author: 宋计民
 * @Date: 2023-08-11 17:17
 * @Version: 1.0
 * @Content: index.ts
 */
import TimeSelectCom from './src/time-select.vue';
import { WithInstallCom } from 'isim-ui';
import { timeSelectShowFormat } from './src/time-select-format';
const SimTimeSelect = TimeSelectCom as WithInstallCom<typeof TimeSelectCom>;

SimTimeSelect.install = function (Vue) {
  Vue.component('SimTimeSelect', TimeSelectCom);
};

export { SimTimeSelect, timeSelectShowFormat };
