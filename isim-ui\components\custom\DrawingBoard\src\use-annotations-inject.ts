/**
 * @Author: 宋计民
 * @Date: 2023-08-29 19:24
 * @Version: 1.0
 * @Content: use-draw-inject.ts
 */

import { Ref } from 'vue';

export const DRAW_CTX_KEY = Symbol('drawBoardCtx');
export const DRAW_BOX_KEY = Symbol('drawBoxKey');

export function useAnnotationsInject() {
  const ctx = inject(DRAW_CTX_KEY) as Ref<CanvasRenderingContext2D>;

  const drawBoxRef = inject(DRAW_BOX_KEY) as Ref<HTMLDivElement>;

  const clearAnnotations = () => {
    ctx.value.clearRect(0, 0, drawBoxRef.value.clientWidth, drawBoxRef.value.clientHeight);
  };

  const clearMouseDown = () => {
    drawBoxRef.value.onmousedown = null;
  };

  const clearALlEvent = () => {
    const canvas = drawBoxRef.value;
    canvas.onmousemove = null;
    canvas.onmouseup = null;
    canvas.onmouseleave = null;
    canvas.ondblclick = null;
  };

  const bindMouseUpAndLeave = () => {
    const canvas = drawBoxRef.value;
    canvas.onmouseup = clearALlEvent;
    canvas.onmouseleave = clearALlEvent;
  };

  return {
    ctx,
    drawBoxRef,
    clearMouseDown,
    bindMouseUpAndLeave,
    clearALlEvent,
    clearAnnotations
  };
}
