import { Gantt } from './Gantt.ts';
import { GanttRow } from './GanttRow.ts';

/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
export class GanttOnLine {
  gantt: Gantt;
  canvas: HTMLCanvasElement;

  onlineRowList: GanttRow[] = [];
  eventList: Function[] = [];

  constructor(gantt: Gantt) {
    this.gantt = gantt;
    this.canvas = gantt.canvas;

    this.registerEvent();
  }

  //   注册事件
  registerEvent() {
    let flag = false;
    document.addEventListener('keydown', (e) => {
      if (e.key === 'A' || e.key === 'a') flag = true;
    });
    document.addEventListener('keyup', (_e) => {
      flag = false;
    });
    this.canvas.addEventListener('click', (_e) => {
      if (!flag) return;
      console.log(112);
      console.log('this.canvas.onclick', this.canvas.onclick);
    });
  }

  push(row: GanttRow) {
    // 如果选中节点已经存在，则判断是取消操作
    if (this.isOnlineById(row.id)) {
      this.onlineRowList = this.onlineRowList.filter((v) => v.id !== row.id);
      this.gantt.render();
      return;
    }
    // 添加节点
    if (this.onlineRowList.length == 0) {
      this.onlineRowList.push(row);
      this.gantt.render();
      return;
    }
    // 创建连线
    this.onlineRowList.push(row);
    this.emitCreateEdge();
    this.gantt.render();
  }

  //   注册创建连线的回调
  onCreateEdge(fn: Function) {
    this.eventList.push(fn);
  }
  //   创建连线的回调
  emitCreateEdge() {
    this.eventList.forEach((fn) => fn(this.onlineRowList));
    this.clear();
  }

  isOnlineById(id: string) {
    return this.onlineRowList.some((row) => row.id === id);
  }

  clear() {
    this.onlineRowList = [];
  }
}
