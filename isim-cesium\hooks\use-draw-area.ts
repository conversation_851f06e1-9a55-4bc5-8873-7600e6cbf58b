/**
 * @Author: 宋计民
 * @Date: 2023-08-21 20:33
 * @Version: 1.0
 * @Content: 绘制区域
 */
import {
  cartesian3ToDegrees,
  clearEntitySourceBySourceName,
  createEntitySource,
  DegreesPosType,
  getCoordinatesFromFeature,
  getCurrentTimeJulian,
  getTwoPointsCenter,
  getTwoPointsDistance,
  labelDefaultConfig,
  onLeftDown,
  onLeftUp,
  onMouseMove,
  onRightClick,
  screenPosToCartesian,
  screenPosToLBH,
  screenToDegrees,
  setViewerCursor
} from 'isim-cesium';
import Cesium, {
  Cartesian3,
  Color,
  ConstantPositionProperty,
  ConstantProperty,
  CustomDataSource,
  Entity,
  LabelStyle,
  PolygonHierarchy
} from 'cesium';
import {
  BandingArealDataType,
  CamberArealDataType,
  CircularCircleArealDataType,
  EllipseArealDataType,
  FanRingArealDataType,
  LocationType
} from '@sim-scenario/editor-components/left-components/sce-area/draw-area.ts';
//@ts-ignore
import * as turf from '@turf/turf';

export const enum GraphicType {
  circle,
  rectangle,
  polygon,
  ellipsis,
  point,
  polyline
}

export interface CallbackParamType<T = DegreesPosType[]> {
  timing: 'start' | 'stop';
  type: GraphicType;
  degrees: T;
  cartesian: Cartesian3[];
  majorAxis?: number;
  minorAxis?: number;
}

export type CallbackType<T = CallbackParamType> = (params: T) => void;

/**
 * 绘制点
 */
export function useDrawPointArea() {
  const DRAW_POINT_SOURCE_NAME = 'web-drawPointAreaSourceName';

  let leftClose: Function;
  let rightClose: Function;
  const start = (callback?: CallbackType<CallbackParamType<LocationType[]>>) => {
    const points: LocationType[] = [];
    const { source } = createEntitySource(DRAW_POINT_SOURCE_NAME, true);
    setViewerCursor('crosshair');
    leftClose = onLeftDown(({ position: pos }) => {
      const posData = screenPosToLBH(pos.position)!;
      if (!posData) {
        return;
      }
      const { longitude, latitude, height } = posData;
      const point = { X: latitude, Y: longitude, Z: height };
      points.push(point);
      const cartesian = screenPosToCartesian(pos.position)!;
      source.entities.add({
        position: cartesian,
        point: {
          pixelSize: 5,
          color: Color.DODGERBLUE
        }
      });
      callback?.({
        timing: 'start',
        type: GraphicType.point,
        degrees: [point],
        cartesian: [cartesian]
      });
    });
    rightClose = onRightClick(() => {
      callback?.({
        timing: 'stop',
        type: GraphicType.polyline,
        degrees: [...points],
        cartesian: points.map((item) => Cartesian3.fromDegrees(item.Y, item.X, item.Z))
      });
    });
  };

  const stop = () => {
    setViewerCursor('');
    leftClose?.();
    rightClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_POINT_SOURCE_NAME);
  };

  const stopAndClear = () => {
    stop();
    clear();
  };

  return {
    start,
    stop,
    clear,
    stopAndClear
  };
}

/**
 * 绘制线
 */
export function useDrawPolylineArea() {
  const DRAW_POLYLINE_SOURCE_NAME = 'web-drawPolylineAreaSourceName';
  let leftClickClose: Function;
  let rightClose: Function;
  let currentEntity: Entity | null;

  const start = (callback: CallbackType) => {
    setViewerCursor('crosshair');

    const { source } = createEntitySource(DRAW_POLYLINE_SOURCE_NAME, true);
    const points: any[] = [];
    leftClickClose = onLeftDown(({ position: pos }) => {
      const degrees = screenToDegrees(pos.position);
      if (!degrees) {
        return;
      }
      points.push(degrees);
      const startCartesian = Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height);
      callback({
        timing: 'start',
        type: GraphicType.polyline,
        degrees: [degrees],
        cartesian: [startCartesian]
      });
      const entityOption: Entity.ConstructorOptions = {
        position: startCartesian,
        point: {
          pixelSize: 5,
          color: Color.RED
        }
      };
      if (currentEntity) {
        const polylinePositions = currentEntity.polyline?.positions as ConstantProperty;
        const cartesian3 = points.map((item) => Cartesian3.fromDegrees(item.longitude, item.latitude, item.height));
        polylinePositions.setValue(cartesian3);
        source.entities.add(entityOption);
        return;
      }
      currentEntity = source.entities.add({
        ...entityOption,
        polyline: {
          positions: new ConstantProperty([startCartesian]),
          clampToGround: true,
          material: Color.RED
        }
      });
    });
    rightClose = onRightClick(() => {
      callback({
        timing: 'stop',
        type: GraphicType.polyline,
        degrees: [...points],
        cartesian: points.map((item) => Cartesian3.fromDegrees(item.longitude, item.latitude, item.height))
      });
    });
  };

  onScopeDispose(() => {
    rightClose?.();
  });
  const stop = () => {
    currentEntity = null;
    setViewerCursor('');
    leftClickClose?.();
    rightClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_POLYLINE_SOURCE_NAME);
  };

  const stopAndClear = () => {
    stop();
    clear();
  };
  return {
    start,
    clear,
    stop,
    stopAndClear
  };
}

/**
 * 绘制矩形
 * @param viewerName
 */
export function useDrawRectangleArea(viewerName?: string) {
  const DRAW_AREA_SOURCE_NAME = 'web-drawAreaSourceName';

  const calcPoints = (p1: DegreesPosType, p2: DegreesPosType) => {
    const { longitude: lon1, latitude: lat1 } = p1;
    const { longitude, latitude } = p2;
    const p3 = Cartesian3.fromDegrees(longitude, lat1, 0);
    const p4 = Cartesian3.fromDegrees(lon1, latitude, 0);
    return { p3, p4 };
  };

  let startClose: Function;
  const start = (stopCallback?: CallbackType) => {
    const { source } = createEntitySource(DRAW_AREA_SOURCE_NAME, true);
    setViewerCursor('crosshair');
    let points: Cartesian3[] = [];
    startClose = onLeftDown(
      ({ position: pos }) => {
        const startPos = screenPosToLBH(pos.position)!;
        if (!startPos) {
          return;
        }
        const { longitude, latitude, height } = startPos;
        const startCartesian = Cartesian3.fromDegrees(longitude, latitude, height);
        stopCallback?.({
          timing: 'start',
          type: GraphicType.rectangle,
          degrees: [{ longitude, latitude, height }],
          cartesian: [startCartesian]
        });
        const hierarchyProperty = new ConstantProperty(new PolygonHierarchy([startCartesian]));
        const posProperty = new ConstantPositionProperty(startCartesian);
        source.entities.add({
          // @ts-ignore
          position: posProperty,
          polygon: {
            hierarchy: hierarchyProperty,
            fill: false,
            outline: true,
            outlineWidth: 5,
            outlineColor: Color.DODGERBLUE
          }
        });
        const moveClose = onMouseMove(
          ({ position: pos }) => {
            const stopPos = screenPosToLBH(pos.endPosition)!;
            if (!stopPos) {
              return;
            }
            const { p3, p4 } = calcPoints(startPos, stopPos);
            const stopCartesian = Cartesian3.fromDegrees(stopPos.longitude, stopPos.latitude, stopPos.height);
            points = [startCartesian, p3, stopCartesian, p4];
            const polygonHierarchy = new PolygonHierarchy(points);
            posProperty.setValue(Cartesian3.midpoint(startCartesian, stopCartesian, new Cartesian3()));
            hierarchyProperty.setValue(polygonHierarchy);
          },
          {
            viewerName
          }
        );
        const upClose = onLeftUp(
          () => {
            stopCallback?.({
              timing: 'stop',
              type: GraphicType.rectangle,
              degrees: points.map((item) => {
                return cartesian3ToDegrees(item);
              }),
              cartesian: [...points]
            });
            points.length = 0;
            moveClose();
            upClose();
          },
          {
            viewerName
          }
        );
      },
      {
        viewerName
      }
    );
  };

  const stop = () => {
    setViewerCursor('');
    startClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_AREA_SOURCE_NAME);
  };

  const stopAndClear = () => {
    stop();
    clear();
  };

  return {
    start,
    stop,
    clear,
    stopAndClear
  };
}

/**
 * 绘制弧形
 */
export function useDrawCamberAreal() {
  const DRAW_CAMBER_SOURCE_NAME = 'web-drawCamberAreaSourceName';
  let leftClickClose: Function;
  let moveClose: Function;
  // 临时实体集合,结束时需清除
  let drawTemporaryEntity: Entity[] = [];
  enum DrawStateEnum {
    DrawCenter, // 圆心
    DrawStart, // 起点
    DrawEnd // 结束点
  }
  const { source } = createEntitySource(DRAW_CAMBER_SOURCE_NAME, true);

  const start = (callback: CallbackType<CamberArealDataType>) => {
    let currentDrawState: DrawStateEnum | null = null;
    // 鼠标移动时的经纬高
    let pos: DegreesPosType | undefined;
    // 鼠标移动时的坐标Cartesian3
    let cartesian3: Cartesian3 = new Cartesian3();
    // 中心点坐标(笛卡尔)
    let cartesian3CenterPoint: Cartesian3 = new Cartesian3();
    // 存放turfPoints集合,用于计算角度、距离(中心点 起点 终点)
    const turfPoints: turf.Feature[] = [];

    const drawData: CamberArealDataType = {
      AzimuthPoints: [],
      StartAngle: 0,
      EndAngle: 0,
      Radius: 0
    };
    // 绘制时提示线positions
    let temporaryPolylinePositions: Cartesian3[] = [];
    setViewerCursor('crosshair');

    leftClickClose = onLeftDown(({ position: pos }) => {
      const degrees = screenToDegrees(pos.position);
      if (!degrees) {
        return;
      }

      const turfCenterPoint = turf.point([degrees.longitude, degrees.latitude]);
      turfPoints.push(turfCenterPoint);

      const pointEntity = source.entities.add({
        position: Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height),
        point: {
          pixelSize: 2,
          color: Color.RED
        }
      });
      drawTemporaryEntity.push(pointEntity);

      if (currentDrawState === DrawStateEnum.DrawStart) {
        currentDrawState = DrawStateEnum.DrawEnd;
        // 终点
        drawData.EndAngle = Number(turf.bearing(turfPoints[0], turfPoints[2]).toFixed(2));
        const startDistance = turf.distance(turfPoints[0], turfPoints[1]).toFixed(5);
        const endDistance = turf.distance(turfPoints[0], turfPoints[2]).toFixed(5);
        drawData.Radius = Number((startDistance > endDistance ? startDistance * 1000 : endDistance * 1000).toFixed(2));
        // 绘制完成返回数据
        callback(drawData);
      }

      if (currentDrawState === DrawStateEnum.DrawCenter) {
        currentDrawState = DrawStateEnum.DrawStart;
        // 起点
        drawData.StartAngle = Number(turf.bearing(turfPoints[0], turfPoints[1]).toFixed(2));
      }

      if (!currentDrawState) {
        currentDrawState = DrawStateEnum.DrawCenter;
        // 中心点
        drawData.AzimuthPoints = [{ X: degrees.latitude, Y: degrees.longitude, Z: degrees.height }];
        cartesian3CenterPoint = Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height);

        const temporaryPolylineEntity = source.entities.add({
          position: cartesian3CenterPoint,
          polyline: {
            positions: new Cesium.CallbackProperty(() => temporaryPolylinePositions, false),
            clampToGround: true,
            material: Color.RED
          }
        });

        drawTemporaryEntity.push(temporaryPolylineEntity);
      }
    });

    moveClose = onMouseMove(({ position: { endPosition } }) => {
      pos = screenPosToLBH(endPosition);
      cartesian3 = Cartesian3.fromDegrees(pos!.longitude, pos!.latitude, pos!.height);
      // 已经绘制了第一个点(中心点),鼠标移动开始选择第二点(起点)
      if (currentDrawState === DrawStateEnum.DrawCenter) {
        temporaryPolylinePositions = [cartesian3CenterPoint, cartesian3];
      }

      // 已经绘制了第二个点(起点),鼠标移动开始选择第三点(终点)
      if (currentDrawState === DrawStateEnum.DrawStart) {
        const {
          geometry: { coordinates: pointCoordinates }
        } = turfPoints[1];
        const startPoint = Cartesian3.fromDegrees(pointCoordinates[0], pointCoordinates[1]);

        const temporaryPoint = turf.point([pos!.longitude, pos!.latitude]);
        const temporaryEndAngle = turf.bearing(turfPoints[0], temporaryPoint);
        const startDistance = turf.distance(turfPoints[0], turfPoints[1]);
        const endDistance = turf.distance(turfPoints[0], temporaryPoint);
        const radius = startDistance > endDistance ? startDistance : endDistance;

        const {
          geometry: { coordinates }
        } = turf.lineArc(turfPoints[0], radius, drawData.StartAngle, temporaryEndAngle);
        const camberPositions = coordinates.map((item: number[]) => Cartesian3.fromDegrees(item[0], item[1]));
        temporaryPolylinePositions = [startPoint, cartesian3CenterPoint, cartesian3, ...camberPositions.reverse()];
      }
    });
  };

  onScopeDispose(() => {});
  const stop = () => {
    setViewerCursor('');
    leftClickClose?.();
    moveClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_CAMBER_SOURCE_NAME);
    // 绘制完成后清空临时entity
    drawTemporaryEntity.forEach((item) => source.entities.removeById(item.id));
    drawTemporaryEntity = [];
  };

  const stopAndClear = () => {
    stop();
    clear();
  };
  return {
    start,
    clear,
    stop,
    stopAndClear
  };
}

/**
 * 绘制扇形扇环
 */
export function useDrawFanRingAreal() {
  const DRAW_FANRING_SOURCE_NAME = 'web-drawFanRingAreaSourceName';
  let leftClickClose: Function;
  let moveClose: Function;
  // 临时实体集合,结束时需清除
  let drawTemporaryEntity: Entity[] = [];
  enum DrawStateEnum {
    DrawCenter, // 圆心
    DrawStartAngle, // 起始角
    DrawEndAngle, // 终止角
    DrawFinish // 绘制完成
  }
  const { source } = createEntitySource(DRAW_FANRING_SOURCE_NAME, true);

  const start = (callback: CallbackType<FanRingArealDataType>) => {
    let currentDrawState: DrawStateEnum | null = null;
    // 鼠标移动时的经纬高
    let pos: DegreesPosType | undefined;
    // 鼠标移动时的坐标Cartesian3
    let cartesian3: Cartesian3 = new Cartesian3();
    // 中心点坐标(笛卡尔)
    let cartesian3CenterPoint: Cartesian3 = new Cartesian3();
    // 存放turfPoints集合,用于计算角度、距离(中心点 起点 终点)
    const turfPoints: turf.Feature[] = [];
    let innerRadius: number;
    let innterPositions: Cartesian3[];
    let outerRadius: number;
    let outerPositions: Cartesian3[];

    const drawData: FanRingArealDataType = {
      CenterPoints: [],
      StartAngle: 0,
      EndAngle: 0,
      InnerRadius: 0,
      OuterRadius: 0
    };
    // 绘制时提示线positions
    let temporaryPolylinePositions: Cartesian3[] = [];
    setViewerCursor('crosshair');

    leftClickClose = onLeftDown(({ position: pos }) => {
      const degrees = screenToDegrees(pos.position);
      if (!degrees) {
        return;
      }

      const turfCenterPoint = turf.point([degrees.longitude, degrees.latitude]);
      turfPoints.push(turfCenterPoint);

      const pointEntity = source.entities.add({
        position: Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height),
        point: {
          pixelSize: 2,
          color: Color.RED
        }
      });
      drawTemporaryEntity.push(pointEntity);

      if (currentDrawState === DrawStateEnum.DrawEndAngle) {
        currentDrawState = DrawStateEnum.DrawFinish;

        drawData.InnerRadius = innerRadius < outerRadius ? innerRadius * 1000 : outerRadius * 1000;
        drawData.OuterRadius = innerRadius < outerRadius ? outerRadius * 1000 : innerRadius * 1000;
        // 绘制完成返回数据
        callback(drawData);
      }

      if (currentDrawState === DrawStateEnum.DrawStartAngle) {
        currentDrawState = DrawStateEnum.DrawEndAngle;
        // 终止角(确定扇形角度)
        drawData.EndAngle = turf.bearing(turfPoints[0], turfPoints[2]);
      }

      if (currentDrawState === DrawStateEnum.DrawCenter) {
        currentDrawState = DrawStateEnum.DrawStartAngle;
        // 起始角
        drawData.StartAngle = turf.bearing(turfPoints[0], turfPoints[1]);
      }

      if (!currentDrawState) {
        currentDrawState = DrawStateEnum.DrawCenter;
        // 中心点
        drawData.CenterPoints = [{ X: degrees.latitude, Y: degrees.longitude, Z: degrees.height }];
        cartesian3CenterPoint = Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height);

        const temporaryPolylineEntity = source.entities.add({
          position: cartesian3CenterPoint,
          polyline: {
            positions: new Cesium.CallbackProperty(() => temporaryPolylinePositions, false),
            clampToGround: true,
            material: Color.RED
          }
        });

        drawTemporaryEntity.push(temporaryPolylineEntity);
      }
    });

    moveClose = onMouseMove(({ position: { endPosition } }) => {
      pos = screenPosToLBH(endPosition);
      cartesian3 = Cartesian3.fromDegrees(pos!.longitude, pos!.latitude, pos!.height);
      // 已经绘制了第一个点(中心点),鼠标移动开始选择第二点(起点)
      if (currentDrawState === DrawStateEnum.DrawCenter) {
        temporaryPolylinePositions = [cartesian3CenterPoint, cartesian3];
      }

      // 已经绘制了第二个点(起始角点),鼠标移动开始选择第三点(终止角)
      if (currentDrawState === DrawStateEnum.DrawStartAngle) {
        const {
          geometry: { coordinates: pointCoordinates }
        } = turfPoints[1];
        const startPoint = Cartesian3.fromDegrees(pointCoordinates[0], pointCoordinates[1]);

        // 鼠标移动时的turf Point
        const temporaryPoint = turf.point([pos!.longitude, pos!.latitude]);
        // 鼠标移动时的终止角方位角
        const temporaryEndAngle = turf.bearing(turfPoints[0], temporaryPoint);
        const startDistance = turf.distance(turfPoints[0], turfPoints[1]);
        const endDistance = turf.distance(turfPoints[0], temporaryPoint);
        innerRadius = startDistance > endDistance ? startDistance : endDistance;

        const {
          geometry: { coordinates }
        } = turf.lineArc(turfPoints[0], innerRadius, drawData.StartAngle, temporaryEndAngle);
        innterPositions = coordinates.map((item: number[]) => Cartesian3.fromDegrees(item[0], item[1]));
        temporaryPolylinePositions = [innterPositions[0], startPoint, cartesian3CenterPoint, cartesian3, ...innterPositions.reverse()];
      }

      if (currentDrawState === DrawStateEnum.DrawEndAngle) {
        // 鼠标移动时的turf Point
        const temporaryPoint = turf.point([pos!.longitude, pos!.latitude]);
        outerRadius = turf.distance(turfPoints[0], temporaryPoint);

        const {
          geometry: { coordinates }
        } = turf.lineArc(turfPoints[0], outerRadius, drawData.StartAngle, drawData.EndAngle);
        outerPositions = coordinates.map((item: number[]) => Cartesian3.fromDegrees(item[0], item[1]));

        temporaryPolylinePositions = [...outerPositions, ...innterPositions, outerPositions[0]];
      }
    });
  };

  onScopeDispose(() => {});
  const stop = () => {
    setViewerCursor('');
    leftClickClose?.();
    moveClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_FANRING_SOURCE_NAME);
    // 绘制完成后清空临时entity
    drawTemporaryEntity.forEach((item) => source.entities.removeById(item.id));
    drawTemporaryEntity = [];
  };

  const stopAndClear = () => {
    stop();
    clear();
  };
  return {
    start,
    clear,
    stop,
    stopAndClear
  };
}

export function useDrawEllipseAreal() {
  const DRAW_ELLIPSE_SOURCE_NAME = 'web-drawEllipseAreaSourceName';
  let leftClickClose: Function;
  let moveClose: Function;
  // 临时实体集合,结束时需清除
  let drawTemporaryEntity: Entity[] = [];

  enum DrawStateEnum {
    DrawCenter, // 圆心
    DrawStart, // 起点
    DrawEnd // 结束点
  }
  const { source } = createEntitySource(DRAW_ELLIPSE_SOURCE_NAME, true);

  const start = (callback: CallbackType<EllipseArealDataType>) => {
    let currentDrawState: DrawStateEnum | null = null;
    // 鼠标移动时的经纬高
    let pos: DegreesPosType | undefined;
    // 中心点坐标(笛卡尔)
    let cartesian3CenterPoint: Cartesian3 = new Cartesian3();
    // 存放turfPoints集合,用于计算角度、距离(中心点 起点 终点)
    const turfPoints: turf.Feature[] = [];
    // 短轴
    let shortAxis: number;
    // 长轴
    let longAxis: number;

    const drawData: EllipseArealDataType = {
      ShortAxis: 0,
      LongAxis: 0,
      RotateAngle: 0,
      CenterPoints: []
    };

    setViewerCursor('crosshair');

    leftClickClose = onLeftDown(({ position: pos }) => {
      const degrees = screenToDegrees(pos.position);
      if (!degrees) {
        return;
      }

      const turfCenterPoint = turf.point([degrees.longitude, degrees.latitude]);
      turfPoints.push(turfCenterPoint);

      if (currentDrawState === DrawStateEnum.DrawStart) {
        currentDrawState = DrawStateEnum.DrawEnd;
        longAxis = turf.distance(turfPoints[0], turfPoints[2], { units: 'meters' });
        drawData.ShortAxis = shortAxis < longAxis ? shortAxis : longAxis;
        drawData.LongAxis = longAxis > shortAxis ? longAxis : shortAxis;
        callback(drawData);
      }

      if (currentDrawState === DrawStateEnum.DrawCenter) {
        currentDrawState = DrawStateEnum.DrawStart;
        shortAxis = turf.distance(turfPoints[0], turfPoints[1], { units: 'meters' });
      }

      if (!currentDrawState) {
        currentDrawState = DrawStateEnum.DrawCenter;
        // 中心点
        drawData.CenterPoints = [{ X: degrees.latitude, Y: degrees.longitude, Z: degrees.height }];
        cartesian3CenterPoint = Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height);

        const pointEntity = source.entities.add({
          position: cartesian3CenterPoint,
          point: {
            pixelSize: 2,
            color: Color.RED
          }
        });
        drawTemporaryEntity.push(pointEntity);

        const ellipseEntity = source.entities.add({
          position: cartesian3CenterPoint,
          ellipse: {
            semiMinorAxis: new Cesium.CallbackProperty(() => shortAxis, false),
            semiMajorAxis: new Cesium.CallbackProperty(() => longAxis, false),
            fill: false,
            outline: true,
            outlineWidth: 2,
            outlineColor: Color.RED,
            rotation: (drawData.RotateAngle! / 180) * Math.PI
          }
        });
        drawTemporaryEntity.push(ellipseEntity);
      }
    });

    moveClose = onMouseMove(({ position: { endPosition } }) => {
      pos = screenPosToLBH(endPosition);
      const turfPoint = turf.point([pos!.longitude, pos!.latitude]);
      // 已经绘制了第一个点(中心点),鼠标移动开始选择第二点(起点)
      if (currentDrawState === DrawStateEnum.DrawCenter) {
        longAxis = shortAxis = turf.distance(turfPoints[0], turfPoint, { units: 'meters' });
        // longAxis = turf.distance(turfPoints[0], turfPoint, { units: 'meters' });
      }
      // 已经绘制了第二个点(起点),鼠标移动开始选择第三点(终点)
      if (currentDrawState === DrawStateEnum.DrawStart) {
        // const distance = turf.distance(turfPoints[0], turfPoint, { units: 'meters' });
        // shortAxis = distance > shortAxis ? shortAxis : distance;
        longAxis = turf.distance(turfPoints[0], turfPoint, { units: 'meters' });
      }
    });
  };

  const stop = () => {
    setViewerCursor('');
    leftClickClose?.();
    moveClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_ELLIPSE_SOURCE_NAME);
    // 绘制完成后清空临时entity
    drawTemporaryEntity.forEach((item) => source.entities.removeById(item.id));
    drawTemporaryEntity = [];
  };

  const stopAndClear = () => {
    stop();
    clear();
  };
  return {
    start,
    clear,
    stop,
    stopAndClear
  };
}

/**
 * 绘制圆形圆环
 */
export function useDrawCircularCircleAreal() {
  const DRAW_CIRCULARCIRCLE_SOURCE_NAME = 'web-drawCircularCircleAreaSourceName';
  let leftClickClose: Function;
  let moveClose: Function;
  // 临时实体集合,结束时需清除
  let drawTemporaryEntity: Entity[] = [];

  enum DrawStateEnum {
    DrawCenter, // 圆心
    DrawStart, // 起点
    DrawEnd // 结束点
  }
  const { source } = createEntitySource(DRAW_CIRCULARCIRCLE_SOURCE_NAME, true);

  const start = (callback: CallbackType<CircularCircleArealDataType>) => {
    let currentDrawState: DrawStateEnum | null = null;
    // 鼠标移动时的经纬高
    let pos: DegreesPosType | undefined;
    // 中心点坐标(笛卡尔)
    let cartesian3CenterPoint: Cartesian3 = new Cartesian3();
    // 存放turfPoints集合,用于计算角度、距离(中心点 起点 终点)
    let centerTurfPoint: turf.Feature;
    // 内径
    let innerRadius: number;
    // 外径
    let outerRadius: number;

    const drawData: CircularCircleArealDataType = {
      InnerRadius: 0,
      OuterRadius: 0,
      CenterPoints: []
    };

    setViewerCursor('crosshair');

    leftClickClose = onLeftDown(({ position: pos }) => {
      const degrees = screenToDegrees(pos.position);
      if (!degrees) {
        return;
      }

      if (currentDrawState === DrawStateEnum.DrawStart) {
        currentDrawState = DrawStateEnum.DrawEnd;
        drawData.InnerRadius = innerRadius < outerRadius ? innerRadius : outerRadius;
        drawData.OuterRadius = innerRadius < outerRadius ? outerRadius : innerRadius;
        callback(drawData);
      }

      if (currentDrawState === DrawStateEnum.DrawCenter) {
        currentDrawState = DrawStateEnum.DrawStart;

        const ellipseEntity = source.entities.add({
          position: cartesian3CenterPoint,
          ellipse: {
            semiMinorAxis: new Cesium.CallbackProperty(() => outerRadius, false),
            semiMajorAxis: new Cesium.CallbackProperty(() => outerRadius, false),
            fill: false,
            outline: true,
            outlineWidth: 10,
            outlineColor: Color.RED
          }
        });
        drawTemporaryEntity.push(ellipseEntity);
      }

      if (!currentDrawState) {
        currentDrawState = DrawStateEnum.DrawCenter;
        // 中心点
        drawData.CenterPoints = [{ X: degrees.latitude, Y: degrees.longitude, Z: degrees.height }];
        cartesian3CenterPoint = Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height);
        centerTurfPoint = turf.point([degrees.longitude, degrees.latitude]);

        const pointEntity = source.entities.add({
          position: cartesian3CenterPoint,
          point: {
            pixelSize: 2,
            color: Color.RED
          }
        });
        drawTemporaryEntity.push(pointEntity);

        const ellipseEntity = source.entities.add({
          position: cartesian3CenterPoint,
          ellipse: {
            semiMinorAxis: new Cesium.CallbackProperty(() => innerRadius, false),
            semiMajorAxis: new Cesium.CallbackProperty(() => innerRadius, false),
            fill: false,
            outline: true,
            outlineWidth: 10,
            outlineColor: Color.RED
          }
        });
        drawTemporaryEntity.push(ellipseEntity);
      }
    });

    moveClose = onMouseMove(({ position: { endPosition } }) => {
      pos = screenPosToLBH(endPosition);
      const turfPoint = turf.point([pos!.longitude, pos!.latitude]);
      // 已经绘制了第一个点(中心点),鼠标移动开始选择第二点(起点)
      if (currentDrawState === DrawStateEnum.DrawCenter) {
        innerRadius = turf.distance(centerTurfPoint, turfPoint, { units: 'meters' });
      }
      // 已经绘制了第二个点(起点),鼠标移动开始选择第三点(终点)
      if (currentDrawState === DrawStateEnum.DrawStart) {
        outerRadius = turf.distance(centerTurfPoint, turfPoint, { units: 'meters' });
      }
    });
  };

  const stop = () => {
    setViewerCursor('');
    leftClickClose?.();
    moveClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_CIRCULARCIRCLE_SOURCE_NAME);
    // 绘制完成后清空临时entity
    drawTemporaryEntity.forEach((item) => source.entities.removeById(item.id));
    drawTemporaryEntity = [];
  };

  const stopAndClear = () => {
    stop();
    clear();
  };
  return {
    start,
    clear,
    stop,
    stopAndClear
  };
}

/**
 * 绘制带状
 */
export function useDrawBandingAreal() {
  const DRAW_BANDING_SOURCE_NAME = 'web-drawBandingAreaSourceName';
  let leftClickClose: Function;
  let rightClickClose: Function;
  let moveClose: Function;
  // 临时实体集合,结束时需清除
  let drawTemporaryEntity: Entity[] = [];

  enum DrawStateEnum {
    DrawStart, // 起点
    DrawWidth, // 宽度
    Draw
  }
  const { source } = createEntitySource(DRAW_BANDING_SOURCE_NAME, true);

  const start = (callback: CallbackType<BandingArealDataType>) => {
    let currentDrawState: DrawStateEnum | null = null;
    // 鼠标移动时的经纬高
    let pos: DegreesPosType | undefined;
    // 起点坐标(笛卡尔)
    let cartesian3StartPoint: Cartesian3 = new Cartesian3();
    // 宽度结束点坐标(笛卡尔)
    let cartesian3EndPoint: Cartesian3 = new Cartesian3();
    // 存放turfPoints集合,用于计算角度、距离(中心点 起点 终点)
    let startTurfPoint: turf.Feature;
    // 带状宽度
    let widthDistance: number = 0;
    // polyline positions
    const linePositions: LocationType[] = [];

    let polylinePositionsLeft: Cartesian3[] = [];
    let polylinePositionsRight: Cartesian3[] = [];

    const drawData: BandingArealDataType = {
      AreaLength: 0,
      PointsData: []
    };

    setViewerCursor('crosshair');

    leftClickClose = onLeftDown(({ position: pos }) => {
      const degrees = screenToDegrees(pos.position);
      if (!degrees) {
        return;
      }

      const pointEntity = source.entities.add({
        position: Cartesian3.fromDegrees(degrees.longitude, degrees.latitude),
        point: {
          pixelSize: 3,
          color: Color.RED
        }
      });
      drawTemporaryEntity.push(pointEntity);

      if (currentDrawState === DrawStateEnum.Draw) {
        linePositions.push({
          X: degrees.latitude,
          Y: degrees.longitude,
          Z: degrees.height
        });
      }

      if (currentDrawState === DrawStateEnum.DrawWidth) {
        currentDrawState = DrawStateEnum.Draw;

        linePositions.push({
          X: degrees.latitude,
          Y: degrees.longitude,
          Z: degrees.height
        });
      }

      if (currentDrawState === DrawStateEnum.DrawStart) {
        currentDrawState = DrawStateEnum.DrawWidth;
        drawData.AreaLength = widthDistance;

        // 计算两点之间的中心点（宽度一半的点位）
        const {
          geometry: { coordinates }
        } = turf.midpoint(startTurfPoint, turf.point([degrees.longitude, degrees.latitude]));
        linePositions.push({
          X: coordinates[1],
          Y: coordinates[0],
          Z: 0
        });

        source.entities.removeAll();

        const polylineEntityLeft = source.entities.add({
          position: cartesian3StartPoint,
          polyline: {
            positions: new Cesium.CallbackProperty(() => polylinePositionsLeft, false),
            clampToGround: true,
            material: Color.RED
          }
        });
        drawTemporaryEntity.push(polylineEntityLeft);

        const polylineEntityRight = source.entities.add({
          position: cartesian3EndPoint,
          polyline: {
            positions: new Cesium.CallbackProperty(() => polylinePositionsRight, false),
            clampToGround: true,
            material: Color.RED
          }
        });
        drawTemporaryEntity.push(polylineEntityRight);
      }

      if (!currentDrawState) {
        currentDrawState = DrawStateEnum.DrawStart;
        // 起点
        cartesian3StartPoint = cartesian3EndPoint = Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height);
        startTurfPoint = turf.point([degrees.longitude, degrees.latitude]);

        const pointEntity = source.entities.add({
          position: cartesian3StartPoint,
          point: {
            pixelSize: 3,
            color: Color.RED
          }
        });
        drawTemporaryEntity.push(pointEntity);

        const widthEntity = source.entities.add({
          position: cartesian3StartPoint,
          label: {
            font: '14px sans-serif',
            fillColor: Color.WHITE,
            style: LabelStyle.FILL_AND_OUTLINE,
            text: new Cesium.CallbackProperty(() => `${widthDistance.toFixed(2)}m`, false)
          },
          polyline: {
            positions: new Cesium.CallbackProperty(() => [cartesian3StartPoint, cartesian3EndPoint], false),
            clampToGround: true,
            material: Color.RED
          }
        });
        drawTemporaryEntity.push(widthEntity);
      }
    });

    moveClose = onMouseMove(({ position: { endPosition } }) => {
      pos = screenPosToLBH(endPosition);
      if (!pos) {
        return;
      }
      const turfPoint = turf.point([pos.longitude, pos.latitude]);
      // 已经绘制了第一个点(中心点),鼠标移动开始选择第二点(起点)
      if (currentDrawState === DrawStateEnum.DrawStart) {
        widthDistance = turf.distance(startTurfPoint, turfPoint, { units: 'meters' });
        cartesian3EndPoint = Cartesian3.fromDegrees(turfPoint.geometry.coordinates[0], turfPoint.geometry.coordinates[1]);
      }
      // 宽度确定，开始绘制
      if (currentDrawState === DrawStateEnum.Draw || currentDrawState === DrawStateEnum.DrawWidth) {
        const line = turf.lineString([...linePositions.map((position) => [position.Y, position.X]), [pos.longitude, pos.latitude]]);
        polylinePositionsLeft = turf
          .lineOffset(line, drawData.AreaLength! / 1000 / 2)
          .geometry.coordinates.map((item: number[]) => Cartesian3.fromDegrees(item[0], item[1]));
        polylinePositionsRight = turf
          .lineOffset(line, -(drawData.AreaLength! / 1000 / 2))
          .geometry.coordinates.map((item: number[]) => Cartesian3.fromDegrees(item[0], item[1]));
      }
    });

    rightClickClose = onRightClick(() => {
      drawData.PointsData = linePositions;
      callback(drawData);
    });
  };

  const stop = () => {
    setViewerCursor('');
    leftClickClose?.();
    moveClose?.();
    rightClickClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_BANDING_SOURCE_NAME);
    // 绘制完成后清空临时entity
    drawTemporaryEntity.forEach((item) => source.entities.removeById(item.id));
    drawTemporaryEntity = [];
  };

  const stopAndClear = () => {
    stop();
    clear();
  };
  return {
    start,
    clear,
    stop,
    stopAndClear
  };
}

/**
 * 绘制多边形
 */
export function useDrawPolygonArea() {
  const DRAW_POLYGON_SOURCE_NAME = 'web-drawPolygonAreaSourceName';
  let leftClickClose: Function;
  let rightClose: Function;
  let currentEntity: Entity | null;
  const start = (callback: CallbackType) => {
    setViewerCursor('crosshair');

    const { source } = createEntitySource(DRAW_POLYGON_SOURCE_NAME, true);
    const points: DegreesPosType[] = [];
    leftClickClose = onLeftDown(({ position: pos }) => {
      const degrees = screenToDegrees(pos.position);
      if (!degrees) {
        return;
      }
      points.push(degrees);
      const startCartesian = Cartesian3.fromDegrees(degrees.longitude, degrees.latitude, degrees.height);
      callback({
        timing: 'start',
        type: GraphicType.polygon,
        degrees: [degrees],
        cartesian: [startCartesian]
      });
      const entityOption: Entity.ConstructorOptions = {
        position: startCartesian,
        point: {
          pixelSize: 5,
          color: Color.RED
        }
      };
      if (currentEntity) {
        const polygonHierarchy = currentEntity.polygon?.hierarchy as ConstantProperty;
        const cartesian3 = points.map((item) => Cartesian3.fromDegrees(item.longitude, item.latitude, item.height));
        polygonHierarchy.setValue(new PolygonHierarchy(cartesian3));
        source.entities.add(entityOption);
        return;
      }
      currentEntity = source.entities.add({
        ...entityOption,
        polygon: {
          hierarchy: new ConstantProperty(new PolygonHierarchy([startCartesian])),
          fill: false,
          outline: true,
          outlineWidth: 5,
          outlineColor: Color.RED
        }
      });
    });
    rightClose = onRightClick(() => {
      callback({
        timing: 'stop',
        type: GraphicType.polygon,
        degrees: [...points],
        cartesian: points.map((item) => Cartesian3.fromDegrees(item.longitude, item.latitude, item.height))
      });
      points.length = 0;
      currentEntity = null;
    });
  };

  onScopeDispose(() => {
    rightClose?.();
  });
  const stop = () => {
    currentEntity = null;
    setViewerCursor('');
    leftClickClose?.();
    rightClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_POLYGON_SOURCE_NAME);
  };

  const stopAndClear = () => {
    stop();
    clear();
  };
  return {
    start,
    clear,
    stop,
    stopAndClear
  };
}

interface CalculateFunctionOptType {
  startPos: DegreesPosType;
  stopPos: DegreesPosType;
  centerPos: DegreesPosType;
}
export function useDrawEllipsisArea() {
  const DRAW_Ellipsis_SOURCE_NAME = 'web-drawEllipsisAreaSourceName';

  let startClose: Function;

  const getCircleAxisValue = ({ startPos, centerPos }: CalculateFunctionOptType) => {
    const horizonDistance = getTwoPointsDistance(startPos, [centerPos.longitude, centerPos.latitude]);
    return {
      majorAxisValue: horizonDistance,
      minorAxisValue: horizonDistance
    };
  };

  const getEllipsisAxisValue = ({ startPos, centerPos }: CalculateFunctionOptType) => {
    const horizonDistance = getTwoPointsDistance(startPos, [centerPos.longitude, startPos.latitude]);
    const verticalDistance = getTwoPointsDistance(startPos, [startPos.longitude, centerPos.latitude]);
    return {
      majorAxisValue: horizonDistance,
      minorAxisValue: verticalDistance
    };
  };

  const calculateFunction = (callback: CallbackType, { isCircle = false, source }: { isCircle: boolean; source: CustomDataSource }) => {
    startClose = onLeftDown(({ position: pos }) => {
      const startPos = screenPosToLBH(pos.position)!;
      if (!startPos) {
        return;
      }
      const { longitude, latitude, height } = startPos;
      const startCartesian = Cartesian3.fromDegrees(longitude, latitude, height);
      callback({ timing: 'start', type: GraphicType.circle, degrees: [{ longitude, latitude, height }], cartesian: [startCartesian] });
      const posProperty = new ConstantPositionProperty(startCartesian);
      const majorAxis = new ConstantProperty(0);
      const minorAxis = new ConstantProperty(0);
      source.entities.add({
        // @ts-ignore
        position: posProperty,
        ellipse: {
          semiMajorAxis: majorAxis,
          semiMinorAxis: minorAxis,
          fill: false,
          outline: true,
          outlineWidth: 10,
          outlineColor: Color.DODGERBLUE
        }
      });
      const moveClose = onMouseMove(({ position: movePos }) => {
        const stopPos = screenPosToLBH(movePos.endPosition);
        if (!stopPos) {
          return;
        }
        const turfFeature = getTwoPointsCenter([startPos.longitude, startPos.latitude], [stopPos.longitude, stopPos.latitude]);
        const [longitude, latitude] = getCoordinatesFromFeature(turfFeature);
        posProperty.setValue(Cartesian3.fromDegrees(longitude, latitude));
        const params = { startPos, stopPos, centerPos: { longitude, latitude, height: 0 } };
        if (isCircle) {
          const { majorAxisValue, minorAxisValue } = getCircleAxisValue(params);
          majorAxis.setValue(majorAxisValue * 1000);
          minorAxis.setValue(minorAxisValue * 1000);
          return;
        }
        const { majorAxisValue, minorAxisValue } = getEllipsisAxisValue(params);
        majorAxis.setValue(majorAxisValue * 1000);
        minorAxis.setValue(minorAxisValue * 1000);
      });
      const upClose = onLeftUp(({ position: pos }) => {
        moveClose();
        upClose();
        const stopPos = screenPosToLBH(pos.position);
        if (!stopPos) {
          return;
        }
        const turfFeature = getTwoPointsCenter([startPos.longitude, startPos.latitude], [stopPos.longitude, stopPos.latitude]);
        const [longitude, latitude] = getCoordinatesFromFeature(turfFeature);
        const cartesian = [
          Cartesian3.fromDegrees(longitude, latitude, 0),
          Cartesian3.fromDegrees(stopPos.longitude, stopPos.latitude, stopPos.height)
        ];
        callback({
          timing: 'stop',
          type: GraphicType.circle,
          cartesian,
          degrees: [{ longitude, latitude, height: 0 }, stopPos],
          majorAxis: majorAxis.getValue(getCurrentTimeJulian()),
          minorAxis: minorAxis.getValue(getCurrentTimeJulian())
        });
      });
    });
  };

  const circleStart = (callback: CallbackType, source: CustomDataSource) => {
    calculateFunction(callback, { isCircle: true, source });
  };

  const start = (callback: CallbackType) => {
    setViewerCursor('crosshair');
    const { source } = createEntitySource(DRAW_Ellipsis_SOURCE_NAME, true);
    circleStart(callback, source);
  };

  const stop = () => {
    setViewerCursor('');
    startClose?.();
  };
  const clear = () => {
    clearEntitySourceBySourceName(DRAW_Ellipsis_SOURCE_NAME);
  };
  const stopAndClear = () => {
    stop();
    clear();
  };
  return {
    start,
    stop,
    clear,
    stopAndClear
  };
}
