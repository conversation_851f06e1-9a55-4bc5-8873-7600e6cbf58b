/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/26 17:27:56
 * @description content
 * @version 1.0
 * */
import {
  defaultValue,
  Cartesian3,
  Model,
  GeometryInstance,
  Primitive,
  HeightReference,
  JulianDate,
  Color,
  MaterialAppearance,
  Material,
  CylinderGeometry,
  Matrix4,
  PrimitiveCollection
} from 'cesium';
import AbstractPrimitive from './AbstractPrimitive';
import defer from '../../Core/defer';
import '../Material/index';
import DeveloperWarn from '../../Core/DeveloperWarn';

const MODEL_MODE = {
  NORMAL: 0,
  DAMAGED: 1
};
export default class ModelPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._update = true;
    this._mode = defaultValue(options.mode, MODEL_MODE.NORMAL);
    this._heightReference = defaultValue(options.heightReference, HeightReference.NONE);
    this._scene = options.scene;
    // bomb
    this._bombColor = defaultValue(options.bombColor, Color.ORANGE.withAlpha(0.2));
    this._bombRadius = defaultValue(options.bombRadius, 50);
    this._duration = defaultValue(options.duration, 1);
    this._bombEndTime = null;
    this._bombUpdate = false;
    this._radius = 1.05;
    this._readyPromise = defer();

    this._childrenShow = defaultValue(options.childrenShow, false);
    this._childrenPrimitive = undefined;

    this._effectShow = false;
    this._effectPrimitive = undefined;
    this._modelMatrix = Matrix4.IDENTITY;
  }

  /**
   * add child model
   * ModelInstanceCollection test
   * @param {Primitive|Model} model
   */
  addChild(model) {
    !this._childrenPrimitive && (this._childrenPrimitive = new PrimitiveCollection());
    if (this._childrenPrimitive.contains(model)) {
      return;
    }
    this._childrenPrimitive.add(model);
  }

  /**
   * add Effect
   * @param prim
   */
  addEffect(prim) {
    !this._effectPrimitive && (this._effectPrimitive = new PrimitiveCollection());
    if (this._effectPrimitive.contains(prim)) {
      return;
    }
    this._effectPrimitive.add(prim);
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive = this._primitive && this._primitive.destroy();
      this._updateMode(frameState);
      if (this._mode === MODEL_MODE.DAMAGED) {
        this._bombEndTime = JulianDate.addSeconds(frameState.time, this._duration, new JulianDate());
      }
    }

    this._primitive?.update(frameState);
    this._mode === MODEL_MODE.DAMAGED && this._updateBombSphere(frameState);

    this._updateChildren(frameState);
    this._updateEffect(frameState);
  }

  _updateMode() {
    if (this._url && this._mode === MODEL_MODE.NORMAL) {
      this._readyPromise = defer();
      if (this._scene && this._heightReference !== HeightReference.NONE) {
        this._primitive = Model.fromGltf({
          url: this._url,
          heightReference: this._heightReference,
          scene: this._scene,
          modelMatrix: this._modelMatrix
        });
      } else {
        this._primitive = Model.fromGltf({ url: this._url, modelMatrix: this._modelMatrix });
      }

      this._readyPromise.resolve(this._primitive);
    }

    if (this._damagedUrl && this._mode === MODEL_MODE.DAMAGED) {
      this._readyPromise = defer();
      if (this._scene && this._heightReference !== HeightReference.NONE) {
        this._primitive = Model.fromGltf({
          url: this._damagedUrl,
          heightReference: this._heightReference,
          scene: this._scene,
          modelMatrix: this._modelMatrix
        });
      } else {
        this._primitive = Model.fromGltf({ url: this._damagedUrl, modelMatrix: this._modelMatrix });
      }
      this._readyPromise.resolve(this._primitive);
    }
  }

  _updateChildren(frameState) {
    if (!this._childrenShow) {
      return;
    }

    const primitives = this._childrenPrimitive._primitives;
    const m = this._modelMatrix.clone();
    let start;

    primitives.length > 0 &&
      primitives.forEach((item) => {
        if (item._animateEndTime && JulianDate.lessThanOrEquals(frameState.time, item._animateEndTime)) {
          start = item._relativePosition;
          if (start && item._relativeVelocity) start.x = start.x + item._relativeVelocity;
          item.modelMatrix = Matrix4.multiplyByTranslation(m, start, new Matrix4());
        } else {
          start = item._relativePosition;
          if (start) item.modelMatrix = Matrix4.multiplyByTranslation(m, start, new Matrix4());
        }
      });
    this._childrenPrimitive?.update(frameState);
  }

  _updateEffect(frameState) {
    if (!this._effectShow) {
      return;
    }

    const primitives = this._effectPrimitive._primitives;
    const m = this._modelMatrix.clone();

    primitives.length > 0 &&
      primitives.forEach((item) => {
        if (item._animateEndTime && JulianDate.lessThanOrEquals(frameState.time, item._animateEndTime)) {
          item.modelMatrix = m;
        } else {
          this._effectPrimitive.remove(item);
        }
      });
    this._effectPrimitive?.update(frameState);
  }

  /**
   * CylinderGeometry top and bottom
   * @param {*} frameState
   */
  _updateBombSphere(frameState) {
    if (JulianDate.lessThanOrEquals(frameState.time, this._bombEndTime)) {
      this._bombSpherePrimitive = this._bombSpherePrimitive && this._bombSpherePrimitive.destroy();
      this._bombSpherePrimitive = createSphere(this, 50);
      this._bombSpherePrimitive?.update(frameState);
    }
  }

  destroy() {
    this._bombSpherePrimitive = this._bombSpherePrimitive && this._bombSpherePrimitive.destroy();
    this._childrenPrimitive = this._childrenPrimitive && this._childrenPrimitive.destroy();
    this._effectPrimitive = this._effectPrimitive && this._effectPrimitive.destroy();
    super.destroy();
  }

  set mode(value) {
    if (this._mode === value) {
      return;
    }
    this._mode = value;
    this._update = true;
  }

  set scale(value) {
    this._scale = value;
    if (this._primitive) {
      this._primitive.scale = value;
    }
  }

  set url(value) {
    if (this._url === value) {
      return;
    }
    this._url = value;
    this._mode === MODEL_MODE.NORMAL && (this._update = true);
  }

  set damagedUrl(value) {
    if (this._damagedUrl === value) {
      return;
    }
    this._damagedUrl = value;
    this._mode === MODEL_MODE.DAMAGED && (this._update = true);
  }

  set modelMatrix(value) {
    if (this._primitive) {
      this._primitive.modelMatrix = value;
    }
    this._modelMatrix = value;
  }

  set bombRadius(value) {
    if (this._bombRadius === value) {
      return;
    }
    this._bombRadius === value;
  }

  get readyPromise() {
    return this._readyPromise.promise;
  }

  set childrenShow(value) {
    this._childrenShow = value;
  }

  set heightReference(value) {
    if (!this._scene) {
      throw new DeveloperWarn('The Support of Height reference need a scene and globe.');
    }

    if (this._heightReference === value) {
      return;
    }

    this._heightReference = value;
    if (this._primitive) {
      if (!this._primitive._scene) {
        this._primitive._scene = this._scene;
      }
      this._primitive.heightReference = value;
    }
  }
}

/**
 *
 * @param {*} modelPrimitive
 * @param {*} radius
 * @return {*}
 */
function createSphere(modelPrimitive, radius) {
  const geometry = new CylinderGeometry({
    length: 0.3,
    topRadius: 0,
    bottomRadius: radius
  });
  const m = modelPrimitive._modelMatrix.clone();
  const modelMatrix = Matrix4.multiplyByTranslation(m, new Cartesian3(0.0, 0.0, 1), new Matrix4());
  const instance = new GeometryInstance({
    geometry
  });

  return new Primitive({
    asynchronous: false,
    geometryInstances: instance,
    modelMatrix,
    appearance: new MaterialAppearance({
      material: Material.fromType('BombRadiation', {
        color: modelPrimitive._bombColor,
        speed: 10,
        count: 2,
        gradient: 0.2
      }),
      translucent: true
    }),
    allowPicking: false
  });
}
