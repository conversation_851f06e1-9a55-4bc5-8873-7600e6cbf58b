<!--
* @Author: 宋计民
* @Date: 2024/4/23
* @Version: 1.0
* @Content: resizable.vue
-->
<template>
  <div class="sim-resizable" ref="resizableRef">
    <slot />
    <sim-resize-handle />
    <sim-resize-handle pos="bottom" />
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import SimResizeHandle from './resizable-handle.vue';
import { useResizableHook } from './use-resizable-hook.ts';

defineOptions({
  name: 'SimResizable'
});
type HandlesTypes = 'tl' | 'tm' | 'tr' | 'mr' | 'br' | 'bm' | 'bl' | 'ml';
const { resizableRef } = useResizableHook();
defineProps({
  handles: {
    type: Array as PropType<HandlesTypes[]>,
    default: () => ['mr', 'ml']
  }
});
onMounted(() => {
  console.log(resizableRef);
});
</script>

<style scoped lang="less">
.sim-resizable {
  position: relative;
  border: 1px solid var(--border-color);
}
</style>
