/**
 * @Author: 宋计民
 * @Date: 2023/10/9 13:46
 * @Version: 1.0
 * @Content: solve Billboard Direction problem
 */
import { PositionProperty, Event, defined, JulianDate, Dev<PERSON>perError, Cartesian3, Cartographic, Ellipsoid, Property } from 'cesium';

const position1Scratch = new Cartesian3();
const position2Scratch = new Cartesian3();
const cartographic1Scratch = new Cartographic();
const cartographic2Scratch = new Cartographic();
const timeScratch = new JulianDate();
const STEP = 1.0 / 60.0;
let ratio = undefined;
let deltaLon = undefined;
export class VelocityVectorRotationProperty {
  private _position: PositionProperty;
  private definitionChanged: Event;
  private _currentRotation: undefined | number;
  private _subscription: undefined | Function;
  constructor(position: PositionProperty) {
    this._position = position;
    this.definitionChanged = new Event();
    this._subscription = undefined;
    this._currentRotation = undefined;
  }
  get isConstant() {
    return this._position?.isConstant;
  }
  get position() {
    return this._position;
  }
  set position(value: PositionProperty) {
    const oldValue = this._position;
    if (oldValue === value) {
      return;
    }
    if (defined(oldValue)) {
      this._subscription?.();
    }

    this._position = value;

    if (defined(value)) {
      this._subscription = value.definitionChanged.addEventListener(() => {
        this.definitionChanged.raiseEvent(this);
      }, this);
    }

    this.definitionChanged.raiseEvent(this);
  }
  getValue(time: JulianDate) {
    const rotation = this._getValue(time);
    if (!rotation) {
      return this._currentRotation;
    }
    this._currentRotation = rotation;
    return this._currentRotation;
  }
  equals(other: VelocityVectorRotationProperty) {
    return this === other || this._position.equals(other._position);
  }
  _getValue(time: JulianDate) {
    //>>includeStart('debug', pragmas.debug);
    if (!defined(time)) {
      throw new DeveloperError('time is required');
    }
    //>>includeEnd('debug');
    const property = this._position;
    if (property.isConstant) {
      return undefined;
    }

    let position1 = property.getValue(time, position1Scratch);
    let position2 = property.getValue(JulianDate.addSeconds(time, STEP, timeScratch), position2Scratch);

    //If we don't have a position for now, return undefined.
    if (!defined(position1)) {
      return undefined;
    }

    //If we don't have a position for now + STEP, see if we have a position for now - STEP.
    if (!defined(position2)) {
      position2 = position1;
      position1 = property.getValue(JulianDate.addSeconds(time, -STEP, timeScratch), position2Scratch);

      if (!defined(position1)) {
        return undefined;
      }
    }

    if (Cartesian3.equals(position1, position2)) {
      return undefined;
    }

    Cartographic.fromCartesian(position1, Ellipsoid.WGS84, cartographic1Scratch);
    Cartographic.fromCartesian(position2, Ellipsoid.WGS84, cartographic2Scratch);
    deltaLon = cartographic2Scratch.longitude - cartographic1Scratch.longitude;
    ratio = (cartographic2Scratch.latitude - cartographic1Scratch.latitude) / deltaLon;
    return Math.atan(ratio) - ((deltaLon / Math.abs(deltaLon)) * Math.PI) / 2;
  }
}
