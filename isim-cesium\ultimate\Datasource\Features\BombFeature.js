/**
 * <AUTHOR>
 * @email jayjay<PERSON>@yeah.net
 * @date 2022/06/18 13:44:50
 * @description BombFeature (just for test demo)
 * @version 1.0
 * */

import AbstractFeature from './AbstractFeature.js';
import BombEffectPrimitive from '../../Scene/Primitives/BombEffectPrimitive.js';

export default class BombFeature extends AbstractFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this.create();
  }

  create() {
    this.primitive = new BombEffectPrimitive(this._options);
  }
}
