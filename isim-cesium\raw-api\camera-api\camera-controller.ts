import { CameraEventType, KeyboardEventModifier, Viewer } from 'cesium';

/**
 * 重置相机的操作方式
 * @param viewer
 */
export function resetCameraController(viewer: Viewer) {
  const controller = viewer.scene.screenSpaceCameraController;
  controller.rotateEventTypes = CameraEventType.RIGHT_DRAG;
  controller.zoomEventTypes = [CameraEventType.WHEEL, CameraEventType.PINCH];
  controller.translateEventTypes = CameraEventType.RIGHT_DRAG;
  controller.tiltEventTypes = [{ eventType: CameraEventType.RIGHT_DRAG, modifier: KeyboardEventModifier.CTRL }];
}
