/**
 * 闪烁的斑点材质
 */
// @ts-nocheck
import { Color, createPropertyDescriptor, defaultValue, defined, Event, Material, MaterialProperty, Property } from 'cesium';
import ShaderSource from './Sharder/SpecklesMaterial.glsl?raw';

const MaterialType = 'SpecklesFlashing';

Material._materialCache.addMaterial(MaterialType, {
  fabric: {
    type: MaterialType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      spacing: 0.1,
      size: 0.02
    },
    source: ShaderSource
  },
  translucent: function () {
    return true;
  }
});

export interface SpecklesMaterialOptions {
  color?: Color;
  spacing?: number;
  size?: number;
}

export interface SpecklesMaterialPropertyConstructor extends MaterialProperty {
  new (options?: SpecklesMaterialOptions): SpecklesMaterialPropertyConstructor;
}

export const SpecklesMaterialProperty: SpecklesMaterialPropertyConstructor = function (options?: SpecklesMaterialOptions) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._spacing = undefined;
  this._spacingSubscription = undefined;
  this._size = undefined;
  this._sizeSubscription = undefined;

  this.color = options.color;
  this.spacing = options.spacing;
  this.size = options.size;
};

Object.defineProperties(SpecklesMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._color) && Property.isConstant(this._glow);
    }
  },

  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },

  color: createPropertyDescriptor('color'),

  /**
   * Gets or sets the numeric Property specifying the strength of the glow, as a percentage of the total line width (less than 1.0).
   * @memberof PolylineGlowMaterialProperty.prototype
   * @type {Property|undefined}
   */
  spacing: createPropertyDescriptor('spacing'),
  size: createPropertyDescriptor('size')
});

/**
 * Gets the {@link Material} type at the provided time.
 *
 * @param {JulianDate} _time The time for which to retrieve the type.
 * @returns {String} The type of material.
 */
SpecklesMaterialProperty.prototype.getType = function (_time) {
  return MaterialType;
};
const defaultColor = new Color(1.0, 0.0, 0.0, 0.7);
const defaultSpacing = 2;
const defaultSize = 2;

SpecklesMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  result.color = Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  result.spacing = Property.getValueOrDefault(this._spacing, time, defaultSpacing, result.spacing);
  result.size = Property.getValueOrDefault(this._size, time, defaultSize, result.size);
  return result;
};

SpecklesMaterialProperty.prototype.equals = function (other) {
  return this === other || (other instanceof SpecklesMaterialProperty && Property.equals(this._color, other._color));
};
