import AbstractFeature from './AbstractFeature';
import { defaultValue } from 'cesium';

const EXCLUDE_PARAMETERS = ['orientation', 'scene'];

export default class StaticFeature extends AbstractFeature {
  constructor(options) {
    super(options);
  }

  create() {}

  update(time) {
    if (this.primitive) {
      this._updateOptions(time, this.primitive);
    }
  }

  _updateOptions(time, options) {
    options = defaultValue(options, {});
    let property, value;
    Object.keys(this._options).forEach((key) => {
      if (!EXCLUDE_PARAMETERS.includes(key)) {
        property = this._options[key];
        if (property?.getValue) {
          value = property.getValue(time);
          if (key === 'show') {
            options[key] = this._setShow(time, value);
          } else {
            options[key] = value;
          }
        } else {
          if (key === 'show') {
            options[key] = this._setShow(time, property);
          } else {
            options[key] = property;
          }
        }
      }
    });
    return options;
  }
}
