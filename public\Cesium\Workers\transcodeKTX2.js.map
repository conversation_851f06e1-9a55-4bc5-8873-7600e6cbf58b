{"version": 3, "file": "transcodeKTX2.js", "sources": ["../../../../Source/Renderer/PixelDatatype.js", "../../../../Source/Core/PixelFormat.js", "../../../../Source/Core/VulkanConstants.js", "../../../../Source/ThirdParty/ktx-parse.js", "../../../../Source/WorkersES6/transcodeKTX2.js"], "sourcesContent": ["import WebGLConstants from \"../Core/WebGLConstants.js\";\n\n/**\n * The data type of a pixel.\n *\n * @enum {Number}\n * @see PostProcessStage\n */\nconst PixelDatatype = {\n  UNSIGNED_BYTE: WebGLConstants.UNSIGNED_BYTE,\n  UNSIGNED_SHORT: WebGLConstants.UNSIGNED_SHORT,\n  UNSIGNED_INT: WebGLConstants.UNSIGNED_INT,\n  FLOAT: WebGLConstants.FLOAT,\n  HALF_FLOAT: WebGLConstants.HALF_FLOAT_OES,\n  UNSIGNED_INT_24_8: WebGLConstants.UNSIGNED_INT_24_8,\n  UNSIGNED_SHORT_4_4_4_4: WebGLConstants.UNSIGNED_SHORT_4_4_4_4,\n  UNSIGNED_SHORT_5_5_5_1: WebGLConstants.UNSIGNED_SHORT_5_5_5_1,\n  UNSIGNED_SHORT_5_6_5: WebGLConstants.UNSIGNED_SHORT_5_6_5,\n};\n\n/**\n  @private\n*/\nPixelDatatype.toWebGLConstant = function (pixelDatatype, context) {\n  switch (pixelDatatype) {\n    case PixelDatatype.UNSIGNED_BYTE:\n      return WebGLConstants.UNSIGNED_BYTE;\n    case PixelDatatype.UNSIGNED_SHORT:\n      return WebGLConstants.UNSIGNED_SHORT;\n    case PixelDatatype.UNSIGNED_INT:\n      return WebGLConstants.UNSIGNED_INT;\n    case PixelDatatype.FLOAT:\n      return WebGLConstants.FLOAT;\n    case PixelDatatype.HALF_FLOAT:\n      return context.webgl2\n        ? WebGLConstants.HALF_FLOAT\n        : WebGLConstants.HALF_FLOAT_OES;\n    case PixelDatatype.UNSIGNED_INT_24_8:\n      return WebGLConstants.UNSIGNED_INT_24_8;\n    case PixelDatatype.UNSIGNED_SHORT_4_4_4_4:\n      return WebGLConstants.UNSIGNED_SHORT_4_4_4_4;\n    case PixelDatatype.UNSIGNED_SHORT_5_5_5_1:\n      return WebGLConstants.UNSIGNED_SHORT_5_5_5_1;\n    case PixelDatatype.UNSIGNED_SHORT_5_6_5:\n      return PixelDatatype.UNSIGNED_SHORT_5_6_5;\n  }\n};\n\n/**\n  @private\n*/\nPixelDatatype.isPacked = function (pixelDatatype) {\n  return (\n    pixelDatatype === PixelDatatype.UNSIGNED_INT_24_8 ||\n    pixelDatatype === PixelDatatype.UNSIGNED_SHORT_4_4_4_4 ||\n    pixelDatatype === PixelDatatype.UNSIGNED_SHORT_5_5_5_1 ||\n    pixelDatatype === PixelDatatype.UNSIGNED_SHORT_5_6_5\n  );\n};\n\n/**\n  @private\n*/\nPixelDatatype.sizeInBytes = function (pixelDatatype) {\n  switch (pixelDatatype) {\n    case PixelDatatype.UNSIGNED_BYTE:\n      return 1;\n    case PixelDatatype.UNSIGNED_SHORT:\n    case PixelDatatype.UNSIGNED_SHORT_4_4_4_4:\n    case PixelDatatype.UNSIGNED_SHORT_5_5_5_1:\n    case PixelDatatype.UNSIGNED_SHORT_5_6_5:\n    case PixelDatatype.HALF_FLOAT:\n      return 2;\n    case PixelDatatype.UNSIGNED_INT:\n    case PixelDatatype.FLOAT:\n    case PixelDatatype.UNSIGNED_INT_24_8:\n      return 4;\n  }\n};\n\n/**\n  @private\n*/\nPixelDatatype.validate = function (pixelDatatype) {\n  return (\n    pixelDatatype === PixelDatatype.UNSIGNED_BYTE ||\n    pixelDatatype === PixelDatatype.UNSIGNED_SHORT ||\n    pixelDatatype === PixelDatatype.UNSIGNED_INT ||\n    pixelDatatype === PixelDatatype.FLOAT ||\n    pixelDatatype === PixelDatatype.HALF_FLOAT ||\n    pixelDatatype === PixelDatatype.UNSIGNED_INT_24_8 ||\n    pixelDatatype === PixelDatatype.UNSIGNED_SHORT_4_4_4_4 ||\n    pixelDatatype === PixelDatatype.UNSIGNED_SHORT_5_5_5_1 ||\n    pixelDatatype === PixelDatatype.UNSIGNED_SHORT_5_6_5\n  );\n};\n\nexport default Object.freeze(PixelDatatype);\n", "import PixelDatatype from \"../Renderer/PixelDatatype.js\";\nimport WebGLConstants from \"./WebGLConstants.js\";\n\n/**\n * The format of a pixel, i.e., the number of components it has and what they represent.\n *\n * @enum {Number}\n */\nconst PixelFormat = {\n  /**\n   * A pixel format containing a depth value.\n   *\n   * @type {Number}\n   * @constant\n   */\n  DEPTH_COMPONENT: WebGLConstants.DEPTH_COMPONENT,\n\n  /**\n   * A pixel format containing a depth and stencil value, most often used with {@link PixelDatatype.UNSIGNED_INT_24_8}.\n   *\n   * @type {Number}\n   * @constant\n   */\n  DEPTH_STENCIL: WebGLConstants.DEPTH_STENCIL,\n\n  /**\n   * A pixel format containing an alpha channel.\n   *\n   * @type {Number}\n   * @constant\n   */\n  ALPHA: WebGLConstants.ALPHA,\n\n  /**\n   * A pixel format containing red, green, and blue channels.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGB: WebGLConstants.RGB,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA: WebGLConstants.RGBA,\n\n  /**\n   * A pixel format containing a luminance (intensity) channel.\n   *\n   * @type {Number}\n   * @constant\n   */\n  LUMINANCE: WebGLConstants.LUMINANCE,\n\n  /**\n   * A pixel format containing luminance (intensity) and alpha channels.\n   *\n   * @type {Number}\n   * @constant\n   */\n  LUMINANCE_ALPHA: WebGLConstants.LUMINANCE_ALPHA,\n\n  /**\n   * A pixel format containing red, green, and blue channels that is DXT1 compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGB_DXT1: WebGLConstants.COMPRESSED_RGB_S3TC_DXT1_EXT,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels that is DXT1 compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA_DXT1: WebGLConstants.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels that is DXT3 compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA_DXT3: WebGLConstants.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels that is DXT5 compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA_DXT5: WebGLConstants.COMPRESSED_RGBA_S3TC_DXT5_EXT,\n\n  /**\n   * A pixel format containing red, green, and blue channels that is PVR 4bpp compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGB_PVRTC_4BPPV1: WebGLConstants.COMPRESSED_RGB_PVRTC_4BPPV1_IMG,\n\n  /**\n   * A pixel format containing red, green, and blue channels that is PVR 2bpp compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGB_PVRTC_2BPPV1: WebGLConstants.COMPRESSED_RGB_PVRTC_2BPPV1_IMG,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels that is PVR 4bpp compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA_PVRTC_4BPPV1: WebGLConstants.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels that is PVR 2bpp compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA_PVRTC_2BPPV1: WebGLConstants.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels that is ASTC compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA_ASTC: WebGLConstants.COMPRESSED_RGBA_ASTC_4x4_WEBGL,\n\n  /**\n   * A pixel format containing red, green, and blue channels that is ETC1 compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGB_ETC1: WebGLConstants.COMPRESSED_RGB_ETC1_WEBGL,\n\n  /**\n   * A pixel format containing red, green, and blue channels that is ETC2 compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGB8_ETC2: WebGLConstants.COMPRESSED_RGB8_ETC2,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels that is ETC2 compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA8_ETC2_EAC: WebGLConstants.COMPRESSED_RGBA8_ETC2_EAC,\n\n  /**\n   * A pixel format containing red, green, blue, and alpha channels that is BC7 compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  RGBA_BC7: WebGLConstants.COMPRESSED_RGBA_BPTC_UNORM,\n};\n\n/**\n * @private\n */\nPixelFormat.componentsLength = function (pixelFormat) {\n  switch (pixelFormat) {\n    case PixelFormat.RGB:\n      return 3;\n    case PixelFormat.RGBA:\n      return 4;\n    case PixelFormat.LUMINANCE_ALPHA:\n      return 2;\n    case PixelFormat.ALPHA:\n    case PixelFormat.LUMINANCE:\n      return 1;\n    default:\n      return 1;\n  }\n};\n\n/**\n * @private\n */\nPixelFormat.validate = function (pixelFormat) {\n  return (\n    pixelFormat === PixelFormat.DEPTH_COMPONENT ||\n    pixelFormat === PixelFormat.DEPTH_STENCIL ||\n    pixelFormat === PixelFormat.ALPHA ||\n    pixelFormat === PixelFormat.RGB ||\n    pixelFormat === PixelFormat.RGBA ||\n    pixelFormat === PixelFormat.LUMINANCE ||\n    pixelFormat === PixelFormat.LUMINANCE_ALPHA ||\n    pixelFormat === PixelFormat.RGB_DXT1 ||\n    pixelFormat === PixelFormat.RGBA_DXT1 ||\n    pixelFormat === PixelFormat.RGBA_DXT3 ||\n    pixelFormat === PixelFormat.RGBA_DXT5 ||\n    pixelFormat === PixelFormat.RGB_PVRTC_4BPPV1 ||\n    pixelFormat === PixelFormat.RGB_PVRTC_2BPPV1 ||\n    pixelFormat === PixelFormat.RGBA_PVRTC_4BPPV1 ||\n    pixelFormat === PixelFormat.RGBA_PVRTC_2BPPV1 ||\n    pixelFormat === PixelFormat.RGBA_ASTC ||\n    pixelFormat === PixelFormat.RGB_ETC1 ||\n    pixelFormat === PixelFormat.RGB8_ETC2 ||\n    pixelFormat === PixelFormat.RGBA8_ETC2_EAC ||\n    pixelFormat === PixelFormat.RGBA_BC7\n  );\n};\n\n/**\n * @private\n */\nPixelFormat.isColorFormat = function (pixelFormat) {\n  return (\n    pixelFormat === PixelFormat.ALPHA ||\n    pixelFormat === PixelFormat.RGB ||\n    pixelFormat === PixelFormat.RGBA ||\n    pixelFormat === PixelFormat.LUMINANCE ||\n    pixelFormat === PixelFormat.LUMINANCE_ALPHA\n  );\n};\n\n/**\n * @private\n */\nPixelFormat.isDepthFormat = function (pixelFormat) {\n  return (\n    pixelFormat === PixelFormat.DEPTH_COMPONENT ||\n    pixelFormat === PixelFormat.DEPTH_STENCIL\n  );\n};\n\n/**\n * @private\n */\nPixelFormat.isCompressedFormat = function (pixelFormat) {\n  return (\n    pixelFormat === PixelFormat.RGB_DXT1 ||\n    pixelFormat === PixelFormat.RGBA_DXT1 ||\n    pixelFormat === PixelFormat.RGBA_DXT3 ||\n    pixelFormat === PixelFormat.RGBA_DXT5 ||\n    pixelFormat === PixelFormat.RGB_PVRTC_4BPPV1 ||\n    pixelFormat === PixelFormat.RGB_PVRTC_2BPPV1 ||\n    pixelFormat === PixelFormat.RGBA_PVRTC_4BPPV1 ||\n    pixelFormat === PixelFormat.RGBA_PVRTC_2BPPV1 ||\n    pixelFormat === PixelFormat.RGBA_ASTC ||\n    pixelFormat === PixelFormat.RGB_ETC1 ||\n    pixelFormat === PixelFormat.RGB8_ETC2 ||\n    pixelFormat === PixelFormat.RGBA8_ETC2_EAC ||\n    pixelFormat === PixelFormat.RGBA_BC7\n  );\n};\n\n/**\n * @private\n */\nPixelFormat.isDXTFormat = function (pixelFormat) {\n  return (\n    pixelFormat === PixelFormat.RGB_DXT1 ||\n    pixelFormat === PixelFormat.RGBA_DXT1 ||\n    pixelFormat === PixelFormat.RGBA_DXT3 ||\n    pixelFormat === PixelFormat.RGBA_DXT5\n  );\n};\n\n/**\n * @private\n */\nPixelFormat.isPVRTCFormat = function (pixelFormat) {\n  return (\n    pixelFormat === PixelFormat.RGB_PVRTC_4BPPV1 ||\n    pixelFormat === PixelFormat.RGB_PVRTC_2BPPV1 ||\n    pixelFormat === PixelFormat.RGBA_PVRTC_4BPPV1 ||\n    pixelFormat === PixelFormat.RGBA_PVRTC_2BPPV1\n  );\n};\n\n/**\n * @private\n */\nPixelFormat.isASTCFormat = function (pixelFormat) {\n  return pixelFormat === PixelFormat.RGBA_ASTC;\n};\n\n/**\n * @private\n */\nPixelFormat.isETC1Format = function (pixelFormat) {\n  return pixelFormat === PixelFormat.RGB_ETC1;\n};\n\n/**\n * @private\n */\nPixelFormat.isETC2Format = function (pixelFormat) {\n  return (\n    pixelFormat === PixelFormat.RGB8_ETC2 ||\n    pixelFormat === PixelFormat.RGBA8_ETC2_EAC\n  );\n};\n\n/**\n * @private\n */\nPixelFormat.isBC7Format = function (pixelFormat) {\n  return pixelFormat === PixelFormat.RGBA_BC7;\n};\n\n/**\n * @private\n */\nPixelFormat.compressedTextureSizeInBytes = function (\n  pixelFormat,\n  width,\n  height\n) {\n  switch (pixelFormat) {\n    case PixelFormat.RGB_DXT1:\n    case PixelFormat.RGBA_DXT1:\n    case PixelFormat.RGB_ETC1:\n    case PixelFormat.RGB8_ETC2:\n      return Math.floor((width + 3) / 4) * Math.floor((height + 3) / 4) * 8;\n\n    case PixelFormat.RGBA_DXT3:\n    case PixelFormat.RGBA_DXT5:\n    case PixelFormat.RGBA_ASTC:\n    case PixelFormat.RGBA8_ETC2_EAC:\n      return Math.floor((width + 3) / 4) * Math.floor((height + 3) / 4) * 16;\n\n    case PixelFormat.RGB_PVRTC_4BPPV1:\n    case PixelFormat.RGBA_PVRTC_4BPPV1:\n      return Math.floor((Math.max(width, 8) * Math.max(height, 8) * 4 + 7) / 8);\n\n    case PixelFormat.RGB_PVRTC_2BPPV1:\n    case PixelFormat.RGBA_PVRTC_2BPPV1:\n      return Math.floor(\n        (Math.max(width, 16) * Math.max(height, 8) * 2 + 7) / 8\n      );\n\n    case PixelFormat.RGBA_BC7:\n      return Math.ceil(width / 4) * Math.ceil(height / 4) * 16;\n\n    default:\n      return 0;\n  }\n};\n\n/**\n * @private\n */\nPixelFormat.textureSizeInBytes = function (\n  pixelFormat,\n  pixelDatatype,\n  width,\n  height\n) {\n  let componentsLength = PixelFormat.componentsLength(pixelFormat);\n  if (PixelDatatype.isPacked(pixelDatatype)) {\n    componentsLength = 1;\n  }\n  return (\n    componentsLength * PixelDatatype.sizeInBytes(pixelDatatype) * width * height\n  );\n};\n\n/**\n * @private\n */\nPixelFormat.alignmentInBytes = function (pixelFormat, pixelDatatype, width) {\n  const mod =\n    PixelFormat.textureSizeInBytes(pixelFormat, pixelDatatype, width, 1) % 4;\n  return mod === 0 ? 4 : mod === 2 ? 2 : 1;\n};\n\n/**\n * @private\n */\nPixelFormat.createTypedArray = function (\n  pixelFormat,\n  pixelDatatype,\n  width,\n  height\n) {\n  let constructor;\n  const sizeInBytes = PixelDatatype.sizeInBytes(pixelDatatype);\n  if (sizeInBytes === Uint8Array.BYTES_PER_ELEMENT) {\n    constructor = Uint8Array;\n  } else if (sizeInBytes === Uint16Array.BYTES_PER_ELEMENT) {\n    constructor = Uint16Array;\n  } else if (\n    sizeInBytes === Float32Array.BYTES_PER_ELEMENT &&\n    pixelDatatype === PixelDatatype.FLOAT\n  ) {\n    constructor = Float32Array;\n  } else {\n    constructor = Uint32Array;\n  }\n\n  const size = PixelFormat.componentsLength(pixelFormat) * width * height;\n  return new constructor(size);\n};\n\n/**\n * @private\n */\nPixelFormat.flipY = function (\n  bufferView,\n  pixelFormat,\n  pixelDatatype,\n  width,\n  height\n) {\n  if (height === 1) {\n    return bufferView;\n  }\n  const flipped = PixelFormat.createTypedArray(\n    pixelFormat,\n    pixelDatatype,\n    width,\n    height\n  );\n  const numberOfComponents = PixelFormat.componentsLength(pixelFormat);\n  const textureWidth = width * numberOfComponents;\n  for (let i = 0; i < height; ++i) {\n    const row = i * width * numberOfComponents;\n    const flippedRow = (height - i - 1) * width * numberOfComponents;\n    for (let j = 0; j < textureWidth; ++j) {\n      flipped[flippedRow + j] = bufferView[row + j];\n    }\n  }\n  return flipped;\n};\n\n/**\n * @private\n */\nPixelFormat.toInternalFormat = function (pixelFormat, pixelDatatype, context) {\n  // WebGL 1 require internalFormat to be the same as PixelFormat\n  if (!context.webgl2) {\n    return pixelFormat;\n  }\n\n  // Convert pixelFormat to correct internalFormat for WebGL 2\n  if (pixelFormat === PixelFormat.DEPTH_STENCIL) {\n    return WebGLConstants.DEPTH24_STENCIL8;\n  }\n\n  if (pixelFormat === PixelFormat.DEPTH_COMPONENT) {\n    if (pixelDatatype === PixelDatatype.UNSIGNED_SHORT) {\n      return WebGLConstants.DEPTH_COMPONENT16;\n    } else if (pixelDatatype === PixelDatatype.UNSIGNED_INT) {\n      return WebGLConstants.DEPTH_COMPONENT24;\n    }\n  }\n\n  if (pixelDatatype === PixelDatatype.FLOAT) {\n    switch (pixelFormat) {\n      case PixelFormat.RGBA:\n        return WebGLConstants.RGBA32F;\n      case PixelFormat.RGB:\n        return WebGLConstants.RGB32F;\n      case PixelFormat.RG:\n        return WebGLConstants.RG32F;\n      case PixelFormat.R:\n        return WebGLConstants.R32F;\n    }\n  }\n\n  if (pixelDatatype === PixelDatatype.HALF_FLOAT) {\n    switch (pixelFormat) {\n      case PixelFormat.RGBA:\n        return WebGLConstants.RGBA16F;\n      case PixelFormat.RGB:\n        return WebGLConstants.RGB16F;\n      case PixelFormat.RG:\n        return WebGLConstants.RG16F;\n      case PixelFormat.R:\n        return WebGLConstants.R16F;\n    }\n  }\n\n  return pixelFormat;\n};\n\nexport default Object.freeze(PixelFormat);\n", "/**\n * Enum containing Vulkan Constant values by name.\n *\n * These match the constants from the {@link https://www.khronos.org/registry/vulkan/specs/1.2-extensions/html/vkspec.html#formats-definition|Vulkan 1.2 specification}.\n *\n * @enum {Number}\n * @private\n */\nconst VulkanConstants = {\n  VK_FORMAT_UNDEFINED: 0,\n  VK_FORMAT_R4G4_UNORM_PACK8: 1,\n  VK_FORMAT_R4G4B4A4_UNORM_PACK16: 2,\n  VK_FORMAT_B4G4R4A4_UNORM_PACK16: 3,\n  VK_FORMAT_R5G6B5_UNORM_PACK16: 4,\n  VK_FORMAT_B5G6R5_UNORM_PACK16: 5,\n  VK_FORMAT_R5G5B5A1_UNORM_PACK16: 6,\n  VK_FORMAT_B5G5R5A1_UNORM_PACK16: 7,\n  VK_FORMAT_A1R5G5B5_UNORM_PACK16: 8,\n  VK_FORMAT_R8_UNORM: 9,\n  VK_FORMAT_R8_SNORM: 10,\n  VK_FORMAT_R8_USCALED: 11,\n  VK_FORMAT_R8_SSCALED: 12,\n  VK_FORMAT_R8_UINT: 13,\n  VK_FORMAT_R8_SINT: 14,\n  VK_FORMAT_R8_SRGB: 15,\n  VK_FORMAT_R8G8_UNORM: 16,\n  VK_FORMAT_R8G8_SNORM: 17,\n  VK_FORMAT_R8G8_USCALED: 18,\n  VK_FORMAT_R8G8_SSCALED: 19,\n  VK_FORMAT_R8G8_UINT: 20,\n  VK_FORMAT_R8G8_SINT: 21,\n  VK_FORMAT_R8G8_SRGB: 22,\n  VK_FORMAT_R8G8B8_UNORM: 23,\n  VK_FORMAT_R8G8B8_SNORM: 24,\n  VK_FORMAT_R8G8B8_USCALED: 25,\n  VK_FORMAT_R8G8B8_SSCALED: 26,\n  VK_FORMAT_R8G8B8_UINT: 27,\n  VK_FORMAT_R8G8B8_SINT: 28,\n  VK_FORMAT_R8G8B8_SRGB: 29,\n  VK_FORMAT_B8G8R8_UNORM: 30,\n  VK_FORMAT_B8G8R8_SNORM: 31,\n  VK_FORMAT_B8G8R8_USCALED: 32,\n  VK_FORMAT_B8G8R8_SSCALED: 33,\n  VK_FORMAT_B8G8R8_UINT: 34,\n  VK_FORMAT_B8G8R8_SINT: 35,\n  VK_FORMAT_B8G8R8_SRGB: 36,\n  VK_FORMAT_R8G8B8A8_UNORM: 37,\n  VK_FORMAT_R8G8B8A8_SNORM: 38,\n  VK_FORMAT_R8G8B8A8_USCALED: 39,\n  VK_FORMAT_R8G8B8A8_SSCALED: 40,\n  VK_FORMAT_R8G8B8A8_UINT: 41,\n  VK_FORMAT_R8G8B8A8_SINT: 42,\n  VK_FORMAT_R8G8B8A8_SRGB: 43,\n  VK_FORMAT_B8G8R8A8_UNORM: 44,\n  VK_FORMAT_B8G8R8A8_SNORM: 45,\n  VK_FORMAT_B8G8R8A8_USCALED: 46,\n  VK_FORMAT_B8G8R8A8_SSCALED: 47,\n  VK_FORMAT_B8G8R8A8_UINT: 48,\n  VK_FORMAT_B8G8R8A8_SINT: 49,\n  VK_FORMAT_B8G8R8A8_SRGB: 50,\n  VK_FORMAT_A8B8G8R8_UNORM_PACK32: 51,\n  VK_FORMAT_A8B8G8R8_SNORM_PACK32: 52,\n  VK_FORMAT_A8B8G8R8_USCALED_PACK32: 53,\n  VK_FORMAT_A8B8G8R8_SSCALED_PACK32: 54,\n  VK_FORMAT_A8B8G8R8_UINT_PACK32: 55,\n  VK_FORMAT_A8B8G8R8_SINT_PACK32: 56,\n  VK_FORMAT_A8B8G8R8_SRGB_PACK32: 57,\n  VK_FORMAT_A2R10G10B10_UNORM_PACK32: 58,\n  VK_FORMAT_A2R10G10B10_SNORM_PACK32: 59,\n  VK_FORMAT_A2R10G10B10_USCALED_PACK32: 60,\n  VK_FORMAT_A2R10G10B10_SSCALED_PACK32: 61,\n  VK_FORMAT_A2R10G10B10_UINT_PACK32: 62,\n  VK_FORMAT_A2R10G10B10_SINT_PACK32: 63,\n  VK_FORMAT_A2B10G10R10_UNORM_PACK32: 64,\n  VK_FORMAT_A2B10G10R10_SNORM_PACK32: 65,\n  VK_FORMAT_A2B10G10R10_USCALED_PACK32: 66,\n  VK_FORMAT_A2B10G10R10_SSCALED_PACK32: 67,\n  VK_FORMAT_A2B10G10R10_UINT_PACK32: 68,\n  VK_FORMAT_A2B10G10R10_SINT_PACK32: 69,\n  VK_FORMAT_R16_UNORM: 70,\n  VK_FORMAT_R16_SNORM: 71,\n  VK_FORMAT_R16_USCALED: 72,\n  VK_FORMAT_R16_SSCALED: 73,\n  VK_FORMAT_R16_UINT: 74,\n  VK_FORMAT_R16_SINT: 75,\n  VK_FORMAT_R16_SFLOAT: 76,\n  VK_FORMAT_R16G16_UNORM: 77,\n  VK_FORMAT_R16G16_SNORM: 78,\n  VK_FORMAT_R16G16_USCALED: 79,\n  VK_FORMAT_R16G16_SSCALED: 80,\n  VK_FORMAT_R16G16_UINT: 81,\n  VK_FORMAT_R16G16_SINT: 82,\n  VK_FORMAT_R16G16_SFLOAT: 83,\n  VK_FORMAT_R16G16B16_UNORM: 84,\n  VK_FORMAT_R16G16B16_SNORM: 85,\n  VK_FORMAT_R16G16B16_USCALED: 86,\n  VK_FORMAT_R16G16B16_SSCALED: 87,\n  VK_FORMAT_R16G16B16_UINT: 88,\n  VK_FORMAT_R16G16B16_SINT: 89,\n  VK_FORMAT_R16G16B16_SFLOAT: 90,\n  VK_FORMAT_R16G16B16A16_UNORM: 91,\n  VK_FORMAT_R16G16B16A16_SNORM: 92,\n  VK_FORMAT_R16G16B16A16_USCALED: 93,\n  VK_FORMAT_R16G16B16A16_SSCALED: 94,\n  VK_FORMAT_R16G16B16A16_UINT: 95,\n  VK_FORMAT_R16G16B16A16_SINT: 96,\n  VK_FORMAT_R16G16B16A16_SFLOAT: 97,\n  VK_FORMAT_R32_UINT: 98,\n  VK_FORMAT_R32_SINT: 99,\n  VK_FORMAT_R32_SFLOAT: 100,\n  VK_FORMAT_R32G32_UINT: 101,\n  VK_FORMAT_R32G32_SINT: 102,\n  VK_FORMAT_R32G32_SFLOAT: 103,\n  VK_FORMAT_R32G32B32_UINT: 104,\n  VK_FORMAT_R32G32B32_SINT: 105,\n  VK_FORMAT_R32G32B32_SFLOAT: 106,\n  VK_FORMAT_R32G32B32A32_UINT: 107,\n  VK_FORMAT_R32G32B32A32_SINT: 108,\n  VK_FORMAT_R32G32B32A32_SFLOAT: 109,\n  VK_FORMAT_R64_UINT: 110,\n  VK_FORMAT_R64_SINT: 111,\n  VK_FORMAT_R64_SFLOAT: 112,\n  VK_FORMAT_R64G64_UINT: 113,\n  VK_FORMAT_R64G64_SINT: 114,\n  VK_FORMAT_R64G64_SFLOAT: 115,\n  VK_FORMAT_R64G64B64_UINT: 116,\n  VK_FORMAT_R64G64B64_SINT: 117,\n  VK_FORMAT_R64G64B64_SFLOAT: 118,\n  VK_FORMAT_R64G64B64A64_UINT: 119,\n  VK_FORMAT_R64G64B64A64_SINT: 120,\n  VK_FORMAT_R64G64B64A64_SFLOAT: 121,\n  VK_FORMAT_B10G11R11_UFLOAT_PACK32: 122,\n  VK_FORMAT_E5B9G9R9_UFLOAT_PACK32: 123,\n  VK_FORMAT_D16_UNORM: 124,\n  VK_FORMAT_X8_D24_UNORM_PACK32: 125,\n  VK_FORMAT_D32_SFLOAT: 126,\n  VK_FORMAT_S8_UINT: 127,\n  VK_FORMAT_D16_UNORM_S8_UINT: 128,\n  VK_FORMAT_D24_UNORM_S8_UINT: 129,\n  VK_FORMAT_D32_SFLOAT_S8_UINT: 130,\n  VK_FORMAT_BC1_RGB_UNORM_BLOCK: 131,\n  VK_FORMAT_BC1_RGB_SRGB_BLOCK: 132,\n  VK_FORMAT_BC1_RGBA_UNORM_BLOCK: 133,\n  VK_FORMAT_BC1_RGBA_SRGB_BLOCK: 134,\n  VK_FORMAT_BC2_UNORM_BLOCK: 135,\n  VK_FORMAT_BC2_SRGB_BLOCK: 136,\n  VK_FORMAT_BC3_UNORM_BLOCK: 137,\n  VK_FORMAT_BC3_SRGB_BLOCK: 138,\n  VK_FORMAT_BC4_UNORM_BLOCK: 139,\n  VK_FORMAT_BC4_SNORM_BLOCK: 140,\n  VK_FORMAT_BC5_UNORM_BLOCK: 141,\n  VK_FORMAT_BC5_SNORM_BLOCK: 142,\n  VK_FORMAT_BC6H_UFLOAT_BLOCK: 143,\n  VK_FORMAT_BC6H_SFLOAT_BLOCK: 144,\n  VK_FORMAT_BC7_UNORM_BLOCK: 145,\n  VK_FORMAT_BC7_SRGB_BLOCK: 146,\n  VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK: 147,\n  VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK: 148,\n  VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK: 149,\n  VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK: 150,\n  VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK: 151,\n  VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK: 152,\n  VK_FORMAT_EAC_R11_UNORM_BLOCK: 153,\n  VK_FORMAT_EAC_R11_SNORM_BLOCK: 154,\n  VK_FORMAT_EAC_R11G11_UNORM_BLOCK: 155,\n  VK_FORMAT_EAC_R11G11_SNORM_BLOCK: 156,\n  VK_FORMAT_ASTC_4x4_UNORM_BLOCK: 157,\n  VK_FORMAT_ASTC_4x4_SRGB_BLOCK: 158,\n  VK_FORMAT_ASTC_5x4_UNORM_BLOCK: 159,\n  VK_FORMAT_ASTC_5x4_SRGB_BLOCK: 160,\n  VK_FORMAT_ASTC_5x5_UNORM_BLOCK: 161,\n  VK_FORMAT_ASTC_5x5_SRGB_BLOCK: 162,\n  VK_FORMAT_ASTC_6x5_UNORM_BLOCK: 163,\n  VK_FORMAT_ASTC_6x5_SRGB_BLOCK: 164,\n  VK_FORMAT_ASTC_6x6_UNORM_BLOCK: 165,\n  VK_FORMAT_ASTC_6x6_SRGB_BLOCK: 166,\n  VK_FORMAT_ASTC_8x5_UNORM_BLOCK: 167,\n  VK_FORMAT_ASTC_8x5_SRGB_BLOCK: 168,\n  VK_FORMAT_ASTC_8x6_UNORM_BLOCK: 169,\n  VK_FORMAT_ASTC_8x6_SRGB_BLOCK: 170,\n  VK_FORMAT_ASTC_8x8_UNORM_BLOCK: 171,\n  VK_FORMAT_ASTC_8x8_SRGB_BLOCK: 172,\n  VK_FORMAT_ASTC_10x5_UNORM_BLOCK: 173,\n  VK_FORMAT_ASTC_10x5_SRGB_BLOCK: 174,\n  VK_FORMAT_ASTC_10x6_UNORM_BLOCK: 175,\n  VK_FORMAT_ASTC_10x6_SRGB_BLOCK: 176,\n  VK_FORMAT_ASTC_10x8_UNORM_BLOCK: 177,\n  VK_FORMAT_ASTC_10x8_SRGB_BLOCK: 178,\n  VK_FORMAT_ASTC_10x10_UNORM_BLOCK: 179,\n  VK_FORMAT_ASTC_10x10_SRGB_BLOCK: 180,\n  VK_FORMAT_ASTC_12x10_UNORM_BLOCK: 181,\n  VK_FORMAT_ASTC_12x10_SRGB_BLOCK: 182,\n  VK_FORMAT_ASTC_12x12_UNORM_BLOCK: 183,\n  VK_FORMAT_ASTC_12x12_SRGB_BLOCK: 184,\n  VK_FORMAT_G8B8G8R8_422_UNORM: 1000156000,\n  VK_FORMAT_B8G8R8G8_422_UNORM: 1000156001,\n  VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM: 1000156002,\n  VK_FORMAT_G8_B8R8_2PLANE_420_UNORM: 1000156003,\n  VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM: 1000156004,\n  VK_FORMAT_G8_B8R8_2PLANE_422_UNORM: 1000156005,\n  VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM: 1000156006,\n  VK_FORMAT_R10X6_UNORM_PACK16: 1000156007,\n  VK_FORMAT_R10X6G10X6_UNORM_2PACK16: 1000156008,\n  VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16: 1000156009,\n  VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16: 1000156010,\n  VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16: 1000156011,\n  VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16: 1000156012,\n  VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16: 1000156013,\n  VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16: 1000156014,\n  VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16: 1000156015,\n  VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16: 1000156016,\n  VK_FORMAT_R12X4_UNORM_PACK16: 1000156017,\n  VK_FORMAT_R12X4G12X4_UNORM_2PACK16: 1000156018,\n  VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16: 1000156019,\n  VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16: 1000156020,\n  VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16: 1000156021,\n  VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16: 1000156022,\n  VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16: 1000156023,\n  VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16: 1000156024,\n  VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16: 1000156025,\n  VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16: 1000156026,\n  VK_FORMAT_G16B16G16R16_422_UNORM: 1000156027,\n  VK_FORMAT_B16G16R16G16_422_UNORM: 1000156028,\n  VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM: 1000156029,\n  VK_FORMAT_G16_B16R16_2PLANE_420_UNORM: 1000156030,\n  VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM: 1000156031,\n  VK_FORMAT_G16_B16R16_2PLANE_422_UNORM: 1000156032,\n  VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM: 1000156033,\n  VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG: 1000054000,\n  VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG: 1000054001,\n  VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG: 1000054002,\n  VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG: 1000054003,\n  VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG: 1000054004,\n  VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG: 1000054005,\n  VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG: 1000054006,\n  VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG: 1000054007,\n  VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT: 1000066000,\n  VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT: 1000066001,\n  VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT: 1000066002,\n  VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT: 1000066003,\n  VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT: 1000066004,\n  VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT: 1000066005,\n  VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT: 1000066006,\n  VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT: 1000066007,\n  VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT: 1000066008,\n  VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT: 1000066009,\n  VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT: 1000066010,\n  VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT: 1000066011,\n  VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT: 1000066012,\n  VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT: 1000066013,\n  VK_FORMAT_G8B8G8R8_422_UNORM_KHR: 1000156000,\n  VK_FORMAT_B8G8R8G8_422_UNORM_KHR: 1000156001,\n  VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM_KHR: 1000156002,\n  VK_FORMAT_G8_B8R8_2PLANE_420_UNORM_KHR: 1000156003,\n  VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM_KHR: 1000156004,\n  VK_FORMAT_G8_B8R8_2PLANE_422_UNORM_KHR: 1000156005,\n  VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM_KHR: 1000156006,\n  VK_FORMAT_R10X6_UNORM_PACK16_KHR: 1000156007,\n  VK_FORMAT_R10X6G10X6_UNORM_2PACK16_KHR: 1000156008,\n  VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16_KHR: 1000156009,\n  VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16_KHR: 1000156010,\n  VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16_KHR: 1000156011,\n  VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16_KHR: 1000156012,\n  VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16_KHR: 1000156013,\n  VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16_KHR: 1000156014,\n  VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16_KHR: 1000156015,\n  VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16_KHR: 1000156016,\n  VK_FORMAT_R12X4_UNORM_PACK16_KHR: 1000156017,\n  VK_FORMAT_R12X4G12X4_UNORM_2PACK16_KHR: 1000156018,\n  VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16_KHR: 1000156019,\n  VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16_KHR: 1000156020,\n  VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16_KHR: 1000156021,\n  VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16_KHR: 1000156022,\n  VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16_KHR: 1000156023,\n  VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16_KHR: 1000156024,\n  VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16_KHR: 1000156025,\n  VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16_KHR: 1000156026,\n  VK_FORMAT_G16B16G16R16_422_UNORM_KHR: 1000156027,\n  VK_FORMAT_B16G16R16G16_422_UNORM_KHR: 1000156028,\n  VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM_KHR: 1000156029,\n  VK_FORMAT_G16_B16R16_2PLANE_420_UNORM_KHR: 1000156030,\n  VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM_KHR: 1000156031,\n  VK_FORMAT_G16_B16R16_2PLANE_422_UNORM_KHR: 1000156032,\n  VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM_KHR: 1000156033,\n};\nexport default Object.freeze(VulkanConstants);\n", "/* This file is automatically rebuilt by the Cesium build process. */\nconst e=[171,75,84,88,32,50,48,187,13,10,26,10];var n,i,s,a,r,o,l,f;!function(t){t[t.NONE=0]=\"NONE\",t[t.BASISLZ=1]=\"BASISLZ\",t[t.ZSTD=2]=\"ZSTD\",t[t.ZLIB=3]=\"ZLIB\";}(n||(n={})),function(t){t[t.BASICFORMAT=0]=\"BASICFORMAT\";}(i||(i={})),function(t){t[t.UNSPECIFIED=0]=\"UNSPECIFIED\",t[t.ETC1S=163]=\"ETC1S\",t[t.UASTC=166]=\"UASTC\";}(s||(s={})),function(t){t[t.UNSPECIFIED=0]=\"UNSPECIFIED\",t[t.SRGB=1]=\"SRGB\";}(a||(a={})),function(t){t[t.UNSPECIFIED=0]=\"UNSPECIFIED\",t[t.LINEAR=1]=\"LINEAR\",t[t.SRGB=2]=\"SRGB\",t[t.ITU=3]=\"ITU\",t[t.NTSC=4]=\"NTSC\",t[t.SLOG=5]=\"SLOG\",t[t.SLOG2=6]=\"SLOG2\";}(r||(r={})),function(t){t[t.ALPHA_STRAIGHT=0]=\"ALPHA_STRAIGHT\",t[t.ALPHA_PREMULTIPLIED=1]=\"ALPHA_PREMULTIPLIED\";}(o||(o={})),function(t){t[t.RGB=0]=\"RGB\",t[t.RRR=3]=\"RRR\",t[t.GGG=4]=\"GGG\",t[t.AAA=15]=\"AAA\";}(l||(l={})),function(t){t[t.RGB=0]=\"RGB\",t[t.RGBA=3]=\"RGBA\",t[t.RRR=4]=\"RRR\",t[t.RRRG=5]=\"RRRG\";}(f||(f={}));class U{constructor(){this.vkFormat=0,this.typeSize=1,this.pixelWidth=0,this.pixelHeight=0,this.pixelDepth=0,this.layerCount=0,this.faceCount=1,this.supercompressionScheme=n.NONE,this.levels=[],this.dataFormatDescriptor=[{vendorId:0,descriptorType:i.BASICFORMAT,versionNumber:2,descriptorBlockSize:40,colorModel:s.UNSPECIFIED,colorPrimaries:a.SRGB,transferFunction:a.SRGB,flags:o.ALPHA_STRAIGHT,texelBlockDimension:{x:4,y:4,z:1,w:1},bytesPlane:[],samples:[]}],this.keyValue={},this.globalData=null;}}class c{constructor(t,e,n,i){this._dataView=new DataView(t.buffer,t.byteOffset+e,n),this._littleEndian=i,this._offset=0;}_nextUint8(){const t=this._dataView.getUint8(this._offset);return this._offset+=1,t}_nextUint16(){const t=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,t}_nextUint32(){const t=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,t}_nextUint64(){const t=this._dataView.getUint32(this._offset,this._littleEndian)+2**32*this._dataView.getUint32(this._offset+4,this._littleEndian);return this._offset+=8,t}_skip(t){return this._offset+=t,this}_scan(t,e=0){const n=this._offset;let i=0;for(;this._dataView.getUint8(this._offset)!==e&&i<t;)i++,this._offset++;return i<t&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+n,i)}}function _(t){return \"undefined\"!=typeof TextDecoder?(new TextDecoder).decode(t):Buffer.from(t).toString(\"utf8\")}function p(t){const n=new Uint8Array(t.buffer,t.byteOffset,e.length);if(n[0]!==e[0]||n[1]!==e[1]||n[2]!==e[2]||n[3]!==e[3]||n[4]!==e[4]||n[5]!==e[5]||n[6]!==e[6]||n[7]!==e[7]||n[8]!==e[8]||n[9]!==e[9]||n[10]!==e[10]||n[11]!==e[11])throw new Error(\"Missing KTX 2.0 identifier.\");const i=new U,s=17*Uint32Array.BYTES_PER_ELEMENT,a=new c(t,e.length,s,!0);i.vkFormat=a._nextUint32(),i.typeSize=a._nextUint32(),i.pixelWidth=a._nextUint32(),i.pixelHeight=a._nextUint32(),i.pixelDepth=a._nextUint32(),i.layerCount=a._nextUint32(),i.faceCount=a._nextUint32();const r=a._nextUint32();i.supercompressionScheme=a._nextUint32();const o=a._nextUint32(),l=a._nextUint32(),f=a._nextUint32(),h=a._nextUint32(),g=a._nextUint64(),p=a._nextUint64(),x=new c(t,e.length+s,3*r*8,!0);for(let e=0;e<r;e++)i.levels.push({levelData:new Uint8Array(t.buffer,t.byteOffset+x._nextUint64(),x._nextUint64()),uncompressedByteLength:x._nextUint64()});const u=new c(t,o,l,!0),y={vendorId:u._skip(4)._nextUint16(),descriptorType:u._nextUint16(),versionNumber:u._nextUint16(),descriptorBlockSize:u._nextUint16(),colorModel:u._nextUint8(),colorPrimaries:u._nextUint8(),transferFunction:u._nextUint8(),flags:u._nextUint8(),texelBlockDimension:{x:u._nextUint8()+1,y:u._nextUint8()+1,z:u._nextUint8()+1,w:u._nextUint8()+1},bytesPlane:[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],samples:[]},D=(y.descriptorBlockSize/4-6)/4;for(let t=0;t<D;t++)y.samples[t]={bitOffset:u._nextUint16(),bitLength:u._nextUint8(),channelID:u._nextUint8(),samplePosition:[u._nextUint8(),u._nextUint8(),u._nextUint8(),u._nextUint8()],sampleLower:u._nextUint32(),sampleUpper:u._nextUint32()};i.dataFormatDescriptor.length=0,i.dataFormatDescriptor.push(y);const b=new c(t,f,h,!0);for(;b._offset<h;){const t=b._nextUint32(),e=b._scan(t),n=_(e),s=b._scan(t-e.byteLength);i.keyValue[n]=n.match(/^ktx/i)?_(s):s,b._offset%4&&b._skip(4-b._offset%4);}if(p<=0)return i;const d=new c(t,g,p,!0),B=d._nextUint16(),w=d._nextUint16(),A=d._nextUint32(),S=d._nextUint32(),m=d._nextUint32(),L=d._nextUint32(),I=[];for(let t=0;t<r;t++)I.push({imageFlags:d._nextUint32(),rgbSliceByteOffset:d._nextUint32(),rgbSliceByteLength:d._nextUint32(),alphaSliceByteOffset:d._nextUint32(),alphaSliceByteLength:d._nextUint32()});const R=g+d._offset,E=R+A,T=E+S,O=T+m,P=new Uint8Array(t.buffer,t.byteOffset+R,A),C=new Uint8Array(t.buffer,t.byteOffset+E,S),F=new Uint8Array(t.buffer,t.byteOffset+T,m),G=new Uint8Array(t.buffer,t.byteOffset+O,L);return i.globalData={endpointCount:B,selectorCount:w,imageDescs:I,endpointsData:P,selectorsData:C,tablesData:F,extendedData:G},i}\n\nexport { p as default };\n", "/* global require */\nimport defined from \"../Core/defined.js\";\nimport Check from \"../Core/Check.js\";\nimport PixelFormat from \"../Core/PixelFormat.js\";\nimport RuntimeError from \"../Core/RuntimeError.js\";\nimport VulkanConstants from \"../Core//VulkanConstants.js\";\nimport PixelDatatype from \"../Renderer/PixelDatatype.js\";\nimport createTaskProcessorWorker from \"./createTaskProcessorWorker.js\";\nimport ktx_parse from \"../ThirdParty/ktx-parse.js\";\n\nconst faceOrder = [\n  \"positiveX\",\n  \"negativeX\",\n  \"positiveY\",\n  \"negativeY\",\n  \"positiveZ\",\n  \"negativeZ\",\n];\n\n// Flags\nconst colorModelETC1S = 163;\nconst colorModelUASTC = 166;\n\nlet transcoderModule;\nfunction transcode(parameters, transferableObjects) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"transcoderModule\", transcoderModule);\n  //>>includeEnd('debug');\n\n  const data = parameters.ktx2Buffer;\n  const supportedTargetFormats = parameters.supportedTargetFormats;\n  let header;\n  try {\n    header = ktx_parse(data);\n  } catch (e) {\n    throw new RuntimeError(\"Invalid KTX2 file.\");\n  }\n\n  if (header.layerCount !== 0) {\n    throw new RuntimeError(\"KTX2 texture arrays are not supported.\");\n  }\n\n  if (header.pixelDepth !== 0) {\n    throw new RuntimeError(\"KTX2 3D textures are unsupported.\");\n  }\n\n  const dfd = header.dataFormatDescriptor[0];\n  const result = new Array(header.levelCount);\n\n  if (\n    header.vkFormat === 0x0 &&\n    (dfd.colorModel === colorModelETC1S || dfd.colorModel === colorModelUASTC)\n  ) {\n    // Compressed, initialize transcoder module\n    transcodeCompressed(\n      data,\n      header,\n      supportedTargetFormats,\n      transcoderModule,\n      transferableObjects,\n      result\n    );\n  } else {\n    transferableObjects.push(data.buffer);\n    parseUncompressed(header, result);\n  }\n\n  return result;\n}\n\n// Parser for uncompressed\nfunction parseUncompressed(header, result) {\n  const internalFormat =\n    header.vkFormat === VulkanConstants.VK_FORMAT_R8G8B8_SRGB\n      ? PixelFormat.RGB\n      : PixelFormat.RGBA;\n  let datatype;\n  if (header.vkFormat === VulkanConstants.VK_FORMAT_R8G8B8A8_UNORM) {\n    datatype = PixelDatatype.UNSIGNED_BYTE;\n  } else if (\n    header.vkFormat === VulkanConstants.VK_FORMAT_R16G16B16A16_SFLOAT\n  ) {\n    datatype = PixelDatatype.HALF_FLOAT;\n  } else if (\n    header.vkFormat === VulkanConstants.VK_FORMAT_R32G32B32A32_SFLOAT\n  ) {\n    datatype = PixelDatatype.FLOAT;\n  }\n\n  for (let i = 0; i < header.levels.length; ++i) {\n    const level = {};\n    result[i] = level;\n    const levelBuffer = header.levels[i].levelData;\n\n    const width = header.pixelWidth >> i;\n    const height = header.pixelHeight >> i;\n    const faceLength =\n      width * height * PixelFormat.componentsLength(internalFormat);\n\n    for (let j = 0; j < header.faceCount; ++j) {\n      // multiply levelBuffer.byteOffset by the size in bytes of the pixel data type\n      const faceByteOffset =\n        levelBuffer.byteOffset + faceLength * header.typeSize * j;\n      let faceView;\n      if (!defined(datatype) || PixelDatatype.sizeInBytes(datatype) === 1) {\n        faceView = new Uint8Array(\n          levelBuffer.buffer,\n          faceByteOffset,\n          faceLength\n        );\n      } else if (PixelDatatype.sizeInBytes(datatype) === 2) {\n        faceView = new Uint16Array(\n          levelBuffer.buffer,\n          faceByteOffset,\n          faceLength\n        );\n      } else {\n        faceView = new Float32Array(\n          levelBuffer.buffer,\n          faceByteOffset,\n          faceLength\n        );\n      }\n\n      level[faceOrder[j]] = {\n        internalFormat: internalFormat,\n        datatype: datatype,\n        width: width,\n        height: height,\n        levelBuffer: faceView,\n      };\n    }\n  }\n}\n\nfunction transcodeCompressed(\n  data,\n  header,\n  supportedTargetFormats,\n  transcoderModule,\n  transferableObjects,\n  result\n) {\n  const ktx2File = new transcoderModule.KTX2File(data);\n  let width = ktx2File.getWidth();\n  let height = ktx2File.getHeight();\n  const levels = ktx2File.getLevels();\n  const hasAlpha = ktx2File.getHasAlpha();\n\n  if (!(width > 0) || !(height > 0) || !(levels > 0)) {\n    ktx2File.close();\n    ktx2File.delete();\n    throw new RuntimeError(\"Invalid KTX2 file\");\n  }\n\n  let internalFormat, transcoderFormat;\n  const dfd = header.dataFormatDescriptor[0];\n  const BasisFormat = transcoderModule.transcoder_texture_format;\n\n  // Determine target format based on platform support\n  if (dfd.colorModel === colorModelETC1S) {\n    if (supportedTargetFormats.etc) {\n      internalFormat = hasAlpha\n        ? PixelFormat.RGBA8_ETC2_EAC\n        : PixelFormat.RGB8_ETC2;\n      transcoderFormat = hasAlpha\n        ? BasisFormat.cTFETC2_RGBA\n        : BasisFormat.cTFETC1_RGB;\n    } else if (supportedTargetFormats.etc1 && !hasAlpha) {\n      internalFormat = PixelFormat.RGB_ETC1;\n      transcoderFormat = BasisFormat.cTFETC1_RGB;\n    } else if (supportedTargetFormats.s3tc) {\n      internalFormat = hasAlpha ? PixelFormat.RGBA_DXT5 : PixelFormat.RGB_DXT1;\n      transcoderFormat = hasAlpha\n        ? BasisFormat.cTFBC3_RGBA\n        : BasisFormat.cTFBC1_RGB;\n    } else if (supportedTargetFormats.pvrtc) {\n      internalFormat = hasAlpha\n        ? PixelFormat.RGBA_PVRTC_4BPPV1\n        : PixelFormat.RGB_PVRTC_4BPPV1;\n      transcoderFormat = hasAlpha\n        ? BasisFormat.cTFPVRTC1_4_RGBA\n        : BasisFormat.cTFPVRTC1_4_RGB;\n    } else if (supportedTargetFormats.astc) {\n      internalFormat = PixelFormat.RGBA_ASTC;\n      transcoderFormat = BasisFormat.cTFASTC_4x4_RGBA;\n    } else if (supportedTargetFormats.bc7) {\n      internalFormat = PixelFormat.RGBA_BC7;\n      transcoderFormat = BasisFormat.cTFBC7_RGBA;\n    } else {\n      throw new RuntimeError(\n        \"No transcoding format target available for ETC1S compressed ktx2.\"\n      );\n    }\n  } else if (dfd.colorModel === colorModelUASTC) {\n    if (supportedTargetFormats.astc) {\n      internalFormat = PixelFormat.RGBA_ASTC;\n      transcoderFormat = BasisFormat.cTFASTC_4x4_RGBA;\n    } else if (supportedTargetFormats.bc7) {\n      internalFormat = PixelFormat.RGBA_BC7;\n      transcoderFormat = BasisFormat.cTFBC7_RGBA;\n    } else if (supportedTargetFormats.s3tc) {\n      internalFormat = hasAlpha ? PixelFormat.RGBA_DXT5 : PixelFormat.RGB_DXT1;\n      transcoderFormat = hasAlpha\n        ? BasisFormat.cTFBC3_RGBA\n        : BasisFormat.cTFBC1_RGB;\n    } else if (supportedTargetFormats.etc) {\n      internalFormat = hasAlpha\n        ? PixelFormat.RGBA8_ETC2_EAC\n        : PixelFormat.RGB8_ETC2;\n      transcoderFormat = hasAlpha\n        ? BasisFormat.cTFETC2_RGBA\n        : BasisFormat.cTFETC1_RGB;\n    } else if (supportedTargetFormats.etc1 && !hasAlpha) {\n      internalFormat = PixelFormat.RGB_ETC1;\n      transcoderFormat = BasisFormat.cTFETC1_RGB;\n    } else if (supportedTargetFormats.pvrtc) {\n      internalFormat = hasAlpha\n        ? PixelFormat.RGBA_PVRTC_4BPPV1\n        : PixelFormat.RGB_PVRTC_4BPPV1;\n      transcoderFormat = hasAlpha\n        ? BasisFormat.cTFPVRTC1_4_RGBA\n        : BasisFormat.cTFPVRTC1_4_RGB;\n    } else {\n      throw new RuntimeError(\n        \"No transcoding format target available for UASTC compressed ktx2.\"\n      );\n    }\n  }\n\n  if (!ktx2File.startTranscoding()) {\n    ktx2File.close();\n    ktx2File.delete();\n    throw new RuntimeError(\"startTranscoding() failed\");\n  }\n\n  for (let i = 0; i < header.levels.length; ++i) {\n    const level = {};\n    result[i] = level;\n    width = header.pixelWidth >> i;\n    height = header.pixelHeight >> i;\n\n    // Since supercompressed cubemaps are unsupported, this function\n    // does not iterate over KTX2 faces and assumes faceCount = 1.\n\n    const dstSize = ktx2File.getImageTranscodedSizeInBytes(\n      i, // level index\n      0, // layer index\n      0, // face index\n      transcoderFormat.value\n    );\n    const dst = new Uint8Array(dstSize);\n\n    const transcoded = ktx2File.transcodeImage(\n      dst,\n      i, // level index\n      0, // layer index\n      0, // face index\n      transcoderFormat.value,\n      0, // get_alpha_for_opaque_formats\n      -1, // channel0\n      -1 // channel1\n    );\n\n    if (!defined(transcoded)) {\n      throw new RuntimeError(\"transcodeImage() failed.\");\n    }\n\n    transferableObjects.push(dst.buffer);\n\n    level[faceOrder[0]] = {\n      internalFormat: internalFormat,\n      width: width,\n      height: height,\n      levelBuffer: dst,\n    };\n  }\n\n  ktx2File.close();\n  ktx2File.delete();\n  return result;\n}\n\nfunction initWorker(compiledModule) {\n  transcoderModule = compiledModule;\n  transcoderModule.initializeBasis();\n\n  self.onmessage = createTaskProcessorWorker(transcode);\n  self.postMessage(true);\n}\n\nfunction transcodeKTX2(event) {\n  const data = event.data;\n\n  // Expect the first message to be to load a web assembly module\n  const wasmConfig = data.webAssemblyConfig;\n  if (defined(wasmConfig)) {\n    // Require and compile WebAssembly module, or use fallback if not supported\n    return require([wasmConfig.modulePath], function (mscBasisTranscoder) {\n      if (defined(wasmConfig.wasmBinaryFile)) {\n        if (!defined(mscBasisTranscoder)) {\n          mscBasisTranscoder = self.MSC_TRANSCODER;\n        }\n\n        mscBasisTranscoder(wasmConfig).then(function (compiledModule) {\n          initWorker(compiledModule);\n        });\n      } else {\n        return mscBasisTranscoder().then(function (transcoder) {\n          initWorker(transcoder);\n        });\n      }\n    });\n  }\n}\nexport default transcodeKTX2;\n"], "names": ["PixelDatatype", "UNSIGNED_BYTE", "WebGLConstants", "UNSIGNED_SHORT", "UNSIGNED_INT", "FLOAT", "HALF_FLOAT", "HALF_FLOAT_OES", "UNSIGNED_INT_24_8", "UNSIGNED_SHORT_4_4_4_4", "UNSIGNED_SHORT_5_5_5_1", "UNSIGNED_SHORT_5_6_5", "pixelDatatype", "context", "webgl2", "PixelDatatype$1", "Object", "freeze", "PixelFormat", "DEPTH_COMPONENT", "DEPTH_STENCIL", "ALPHA", "RGB", "RGBA", "LUMINANCE", "LUMINANCE_ALPHA", "RGB_DXT1", "COMPRESSED_RGB_S3TC_DXT1_EXT", "RGBA_DXT1", "COMPRESSED_RGBA_S3TC_DXT1_EXT", "RGBA_DXT3", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "RGBA_DXT5", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "RGB_PVRTC_4BPPV1", "COMPRESSED_RGB_PVRTC_4BPPV1_IMG", "RGB_PVRTC_2BPPV1", "COMPRESSED_RGB_PVRTC_2BPPV1_IMG", "RGBA_PVRTC_4BPPV1", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "RGBA_PVRTC_2BPPV1", "COMPRESSED_RGBA_PVRTC_2BPPV1_IMG", "RGBA_ASTC", "COMPRESSED_RGBA_ASTC_4x4_WEBGL", "RGB_ETC1", "COMPRESSED_RGB_ETC1_WEBGL", "RGB8_ETC2", "COMPRESSED_RGB8_ETC2", "RGBA8_ETC2_EAC", "COMPRESSED_RGBA8_ETC2_EAC", "RGBA_BC7", "COMPRESSED_RGBA_BPTC_UNORM", "pixelFormat", "width", "height", "Math", "floor", "max", "ceil", "componentsLength", "isPacked", "sizeInBytes", "mod", "textureSizeInBytes", "constructor", "Uint8Array", "BYTES_PER_ELEMENT", "Uint16Array", "Float32Array", "Uint32Array", "bufferView", "flipped", "createTypedArray", "numberOfComponents", "textureWidth", "i", "row", "flippedRow", "j", "DEPTH24_STENCIL8", "DEPTH_COMPONENT16", "DEPTH_COMPONENT24", "RGBA32F", "RGB32F", "RG", "RG32F", "R", "R32F", "RGBA16F", "RGB16F", "RG16F", "R16F", "PixelFormat$1", "VulkanConstants$1", "VK_FORMAT_UNDEFINED", "VK_FORMAT_R4G4_UNORM_PACK8", "VK_FORMAT_R4G4B4A4_UNORM_PACK16", "VK_FORMAT_B4G4R4A4_UNORM_PACK16", "VK_FORMAT_R5G6B5_UNORM_PACK16", "VK_FORMAT_B5G6R5_UNORM_PACK16", "VK_FORMAT_R5G5B5A1_UNORM_PACK16", "VK_FORMAT_B5G5R5A1_UNORM_PACK16", "VK_FORMAT_A1R5G5B5_UNORM_PACK16", "VK_FORMAT_R8_UNORM", "VK_FORMAT_R8_SNORM", "VK_FORMAT_R8_USCALED", "VK_FORMAT_R8_SSCALED", "VK_FORMAT_R8_UINT", "VK_FORMAT_R8_SINT", "VK_FORMAT_R8_SRGB", "VK_FORMAT_R8G8_UNORM", "VK_FORMAT_R8G8_SNORM", "VK_FORMAT_R8G8_USCALED", "VK_FORMAT_R8G8_SSCALED", "VK_FORMAT_R8G8_UINT", "VK_FORMAT_R8G8_SINT", "VK_FORMAT_R8G8_SRGB", "VK_FORMAT_R8G8B8_UNORM", "VK_FORMAT_R8G8B8_SNORM", "VK_FORMAT_R8G8B8_USCALED", "VK_FORMAT_R8G8B8_SSCALED", "VK_FORMAT_R8G8B8_UINT", "VK_FORMAT_R8G8B8_SINT", "VK_FORMAT_R8G8B8_SRGB", "VK_FORMAT_B8G8R8_UNORM", "VK_FORMAT_B8G8R8_SNORM", "VK_FORMAT_B8G8R8_USCALED", "VK_FORMAT_B8G8R8_SSCALED", "VK_FORMAT_B8G8R8_UINT", "VK_FORMAT_B8G8R8_SINT", "VK_FORMAT_B8G8R8_SRGB", "VK_FORMAT_R8G8B8A8_UNORM", "VK_FORMAT_R8G8B8A8_SNORM", "VK_FORMAT_R8G8B8A8_USCALED", "VK_FORMAT_R8G8B8A8_SSCALED", "VK_FORMAT_R8G8B8A8_UINT", "VK_FORMAT_R8G8B8A8_SINT", "VK_FORMAT_R8G8B8A8_SRGB", "VK_FORMAT_B8G8R8A8_UNORM", "VK_FORMAT_B8G8R8A8_SNORM", "VK_FORMAT_B8G8R8A8_USCALED", "VK_FORMAT_B8G8R8A8_SSCALED", "VK_FORMAT_B8G8R8A8_UINT", "VK_FORMAT_B8G8R8A8_SINT", "VK_FORMAT_B8G8R8A8_SRGB", "VK_FORMAT_A8B8G8R8_UNORM_PACK32", "VK_FORMAT_A8B8G8R8_SNORM_PACK32", "VK_FORMAT_A8B8G8R8_USCALED_PACK32", "VK_FORMAT_A8B8G8R8_SSCALED_PACK32", "VK_FORMAT_A8B8G8R8_UINT_PACK32", "VK_FORMAT_A8B8G8R8_SINT_PACK32", "VK_FORMAT_A8B8G8R8_SRGB_PACK32", "VK_FORMAT_A2R10G10B10_UNORM_PACK32", "VK_FORMAT_A2R10G10B10_SNORM_PACK32", "VK_FORMAT_A2R10G10B10_USCALED_PACK32", "VK_FORMAT_A2R10G10B10_SSCALED_PACK32", "VK_FORMAT_A2R10G10B10_UINT_PACK32", "VK_FORMAT_A2R10G10B10_SINT_PACK32", "VK_FORMAT_A2B10G10R10_UNORM_PACK32", "VK_FORMAT_A2B10G10R10_SNORM_PACK32", "VK_FORMAT_A2B10G10R10_USCALED_PACK32", "VK_FORMAT_A2B10G10R10_SSCALED_PACK32", "VK_FORMAT_A2B10G10R10_UINT_PACK32", "VK_FORMAT_A2B10G10R10_SINT_PACK32", "VK_FORMAT_R16_UNORM", "VK_FORMAT_R16_SNORM", "VK_FORMAT_R16_USCALED", "VK_FORMAT_R16_SSCALED", "VK_FORMAT_R16_UINT", "VK_FORMAT_R16_SINT", "VK_FORMAT_R16_SFLOAT", "VK_FORMAT_R16G16_UNORM", "VK_FORMAT_R16G16_SNORM", "VK_FORMAT_R16G16_USCALED", "VK_FORMAT_R16G16_SSCALED", "VK_FORMAT_R16G16_UINT", "VK_FORMAT_R16G16_SINT", "VK_FORMAT_R16G16_SFLOAT", "VK_FORMAT_R16G16B16_UNORM", "VK_FORMAT_R16G16B16_SNORM", "VK_FORMAT_R16G16B16_USCALED", "VK_FORMAT_R16G16B16_SSCALED", "VK_FORMAT_R16G16B16_UINT", "VK_FORMAT_R16G16B16_SINT", "VK_FORMAT_R16G16B16_SFLOAT", "VK_FORMAT_R16G16B16A16_UNORM", "VK_FORMAT_R16G16B16A16_SNORM", "VK_FORMAT_R16G16B16A16_USCALED", "VK_FORMAT_R16G16B16A16_SSCALED", "VK_FORMAT_R16G16B16A16_UINT", "VK_FORMAT_R16G16B16A16_SINT", "VK_FORMAT_R16G16B16A16_SFLOAT", "VK_FORMAT_R32_UINT", "VK_FORMAT_R32_SINT", "VK_FORMAT_R32_SFLOAT", "VK_FORMAT_R32G32_UINT", "VK_FORMAT_R32G32_SINT", "VK_FORMAT_R32G32_SFLOAT", "VK_FORMAT_R32G32B32_UINT", "VK_FORMAT_R32G32B32_SINT", "VK_FORMAT_R32G32B32_SFLOAT", "VK_FORMAT_R32G32B32A32_UINT", "VK_FORMAT_R32G32B32A32_SINT", "VK_FORMAT_R32G32B32A32_SFLOAT", "VK_FORMAT_R64_UINT", "VK_FORMAT_R64_SINT", "VK_FORMAT_R64_SFLOAT", "VK_FORMAT_R64G64_UINT", "VK_FORMAT_R64G64_SINT", "VK_FORMAT_R64G64_SFLOAT", "VK_FORMAT_R64G64B64_UINT", "VK_FORMAT_R64G64B64_SINT", "VK_FORMAT_R64G64B64_SFLOAT", "VK_FORMAT_R64G64B64A64_UINT", "VK_FORMAT_R64G64B64A64_SINT", "VK_FORMAT_R64G64B64A64_SFLOAT", "VK_FORMAT_B10G11R11_UFLOAT_PACK32", "VK_FORMAT_E5B9G9R9_UFLOAT_PACK32", "VK_FORMAT_D16_UNORM", "VK_FORMAT_X8_D24_UNORM_PACK32", "VK_FORMAT_D32_SFLOAT", "VK_FORMAT_S8_UINT", "VK_FORMAT_D16_UNORM_S8_UINT", "VK_FORMAT_D24_UNORM_S8_UINT", "VK_FORMAT_D32_SFLOAT_S8_UINT", "VK_FORMAT_BC1_RGB_UNORM_BLOCK", "VK_FORMAT_BC1_RGB_SRGB_BLOCK", "VK_FORMAT_BC1_RGBA_UNORM_BLOCK", "VK_FORMAT_BC1_RGBA_SRGB_BLOCK", "VK_FORMAT_BC2_UNORM_BLOCK", "VK_FORMAT_BC2_SRGB_BLOCK", "VK_FORMAT_BC3_UNORM_BLOCK", "VK_FORMAT_BC3_SRGB_BLOCK", "VK_FORMAT_BC4_UNORM_BLOCK", "VK_FORMAT_BC4_SNORM_BLOCK", "VK_FORMAT_BC5_UNORM_BLOCK", "VK_FORMAT_BC5_SNORM_BLOCK", "VK_FORMAT_BC6H_UFLOAT_BLOCK", "VK_FORMAT_BC6H_SFLOAT_BLOCK", "VK_FORMAT_BC7_UNORM_BLOCK", "VK_FORMAT_BC7_SRGB_BLOCK", "VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK", "VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK", "VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK", "VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK", "VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK", "VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK", "VK_FORMAT_EAC_R11_UNORM_BLOCK", "VK_FORMAT_EAC_R11_SNORM_BLOCK", "VK_FORMAT_EAC_R11G11_UNORM_BLOCK", "VK_FORMAT_EAC_R11G11_SNORM_BLOCK", "VK_FORMAT_ASTC_4x4_UNORM_BLOCK", "VK_FORMAT_ASTC_4x4_SRGB_BLOCK", "VK_FORMAT_ASTC_5x4_UNORM_BLOCK", "VK_FORMAT_ASTC_5x4_SRGB_BLOCK", "VK_FORMAT_ASTC_5x5_UNORM_BLOCK", "VK_FORMAT_ASTC_5x5_SRGB_BLOCK", "VK_FORMAT_ASTC_6x5_UNORM_BLOCK", "VK_FORMAT_ASTC_6x5_SRGB_BLOCK", "VK_FORMAT_ASTC_6x6_UNORM_BLOCK", "VK_FORMAT_ASTC_6x6_SRGB_BLOCK", "VK_FORMAT_ASTC_8x5_UNORM_BLOCK", "VK_FORMAT_ASTC_8x5_SRGB_BLOCK", "VK_FORMAT_ASTC_8x6_UNORM_BLOCK", "VK_FORMAT_ASTC_8x6_SRGB_BLOCK", "VK_FORMAT_ASTC_8x8_UNORM_BLOCK", "VK_FORMAT_ASTC_8x8_SRGB_BLOCK", "VK_FORMAT_ASTC_10x5_UNORM_BLOCK", "VK_FORMAT_ASTC_10x5_SRGB_BLOCK", "VK_FORMAT_ASTC_10x6_UNORM_BLOCK", "VK_FORMAT_ASTC_10x6_SRGB_BLOCK", "VK_FORMAT_ASTC_10x8_UNORM_BLOCK", "VK_FORMAT_ASTC_10x8_SRGB_BLOCK", "VK_FORMAT_ASTC_10x10_UNORM_BLOCK", "VK_FORMAT_ASTC_10x10_SRGB_BLOCK", "VK_FORMAT_ASTC_12x10_UNORM_BLOCK", "VK_FORMAT_ASTC_12x10_SRGB_BLOCK", "VK_FORMAT_ASTC_12x12_UNORM_BLOCK", "VK_FORMAT_ASTC_12x12_SRGB_BLOCK", "VK_FORMAT_G8B8G8R8_422_UNORM", "VK_FORMAT_B8G8R8G8_422_UNORM", "VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM", "VK_FORMAT_G8_B8R8_2PLANE_420_UNORM", "VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM", "VK_FORMAT_G8_B8R8_2PLANE_422_UNORM", "VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM", "VK_FORMAT_R10X6_UNORM_PACK16", "VK_FORMAT_R10X6G10X6_UNORM_2PACK16", "VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16", "VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16", "VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16", "VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16", "VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16", "VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16", "VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16", "VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16", "VK_FORMAT_R12X4_UNORM_PACK16", "VK_FORMAT_R12X4G12X4_UNORM_2PACK16", "VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16", "VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16", "VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16", "VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16", "VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16", "VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16", "VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16", "VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16", "VK_FORMAT_G16B16G16R16_422_UNORM", "VK_FORMAT_B16G16R16G16_422_UNORM", "VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM", "VK_FORMAT_G16_B16R16_2PLANE_420_UNORM", "VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM", "VK_FORMAT_G16_B16R16_2PLANE_422_UNORM", "VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM", "VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG", "VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG", "VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG", "VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG", "VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG", "VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG", "VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG", "VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG", "VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT", "VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT", "VK_FORMAT_G8B8G8R8_422_UNORM_KHR", "VK_FORMAT_B8G8R8G8_422_UNORM_KHR", "VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM_KHR", "VK_FORMAT_G8_B8R8_2PLANE_420_UNORM_KHR", "VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM_KHR", "VK_FORMAT_G8_B8R8_2PLANE_422_UNORM_KHR", "VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM_KHR", "VK_FORMAT_R10X6_UNORM_PACK16_KHR", "VK_FORMAT_R10X6G10X6_UNORM_2PACK16_KHR", "VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16_KHR", "VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16_KHR", "VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16_KHR", "VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16_KHR", "VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16_KHR", "VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16_KHR", "VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16_KHR", "VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16_KHR", "VK_FORMAT_R12X4_UNORM_PACK16_KHR", "VK_FORMAT_R12X4G12X4_UNORM_2PACK16_KHR", "VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16_KHR", "VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16_KHR", "VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16_KHR", "VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16_KHR", "VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16_KHR", "VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16_KHR", "VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16_KHR", "VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16_KHR", "VK_FORMAT_G16B16G16R16_422_UNORM_KHR", "VK_FORMAT_B16G16R16G16_422_UNORM_KHR", "VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM_KHR", "VK_FORMAT_G16_B16R16_2PLANE_420_UNORM_KHR", "VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM_KHR", "VK_FORMAT_G16_B16R16_2PLANE_422_UNORM_KHR", "VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM_KHR", "e", "n", "s", "a", "r", "o", "l", "f", "t", "NONE", "BASISLZ", "ZSTD", "ZLIB", "BASICFORMAT", "UNSPECIFIED", "ETC1S", "UASTC", "SRGB", "LINEAR", "ITU", "NTSC", "SLOG", "SLOG2", "ALPHA_STRAIGHT", "ALPHA_PREMULTIPLIED", "RRR", "GGG", "AAA", "RRRG", "U", "this", "vkFormat", "typeSize", "pixelWidth", "pixelHeight", "pixelDepth", "layerCount", "faceCount", "supercompressionScheme", "levels", "dataFormatDescriptor", "vendorId", "descriptorType", "versionNumber", "descriptorBlockSize", "colorModel", "colorPrimaries", "transferFunction", "flags", "texelBlockDimension", "x", "y", "z", "w", "bytesPlane", "samples", "keyValue", "globalData", "c", "_dataView", "DataView", "buffer", "byteOffset", "_<PERSON><PERSON><PERSON><PERSON>", "_offset", "_nextUint8", "getUint8", "_nextUint16", "getUint16", "_nextUint32", "getUint32", "_nextUint64", "_skip", "_scan", "_", "TextDecoder", "decode", "<PERSON><PERSON><PERSON>", "from", "toString", "faceOrder", "transcoderModule", "transcode", "parameters", "transferableObjects", "Check", "typeOf", "object", "data", "ktx2Buffer", "supportedTargetFormats", "header", "length", "Error", "h", "g", "p", "push", "levelData", "uncompressedByteLength", "u", "D", "bitOffset", "bitLength", "channelID", "samplePosition", "sampleLower", "sampleUpper", "b", "byteLength", "match", "d", "B", "A", "S", "m", "L", "I", "imageFlags", "rgbSliceByteOffset", "rgbSliceByteLength", "alphaSliceByteOffset", "alphaSliceByteLength", "E", "T", "O", "P", "C", "F", "G", "endpointCount", "selectorCount", "imageDescs", "endpointsData", "selectorsData", "tablesData", "extendedData", "ktx_parse", "RuntimeError", "dfd", "result", "Array", "levelCount", "internalFormat", "VulkanConstants", "datatype", "level", "<PERSON><PERSON><PERSON><PERSON>", "face<PERSON><PERSON><PERSON>", "faceByteOffset", "faceView", "defined", "parseUncompressed", "ktx2File", "KTX2File", "getWidth", "getHeight", "getLevels", "has<PERSON><PERSON><PERSON>", "getHasAlpha", "close", "delete", "transcoderFormat", "BasisFormat", "transcoder_texture_format", "etc", "cTFETC2_RGBA", "cTFETC1_RGB", "etc1", "s3tc", "cTFBC3_RGBA", "cTFBC1_RGB", "pvrtc", "cTFPVRTC1_4_RGBA", "cTFPVRTC1_4_RGB", "astc", "cTFASTC_4x4_RGBA", "bc7", "cTFBC7_RGBA", "startTranscoding", "dstSize", "getImageTranscodedSizeInBytes", "value", "dst", "transcoded", "transcodeImage", "transcodeCompressed", "initWorker", "compiledModule", "initializeBasis", "self", "onmessage", "createTaskProcessorWorker", "postMessage", "event", "wasmConfig", "webAssemblyConfig", "require", "modulePath", "mscBasisTranscoder", "wasmBinaryFile", "then", "transcoder", "MSC_TRANSCODER"], "mappings": "+IAQA,MAAMA,EAAgB,CACpBC,cAAeC,EAAcA,eAACD,cAC9BE,eAAgBD,EAAcA,eAACC,eAC/BC,aAAcF,EAAcA,eAACE,aAC7BC,MAAOH,EAAcA,eAACG,MACtBC,WAAYJ,EAAcA,eAACK,eAC3BC,kBAAmBN,EAAcA,eAACM,kBAClCC,uBAAwBP,EAAcA,eAACO,uBACvCC,uBAAwBR,EAAcA,eAACQ,uBACvCC,qBAAsBT,EAAcA,eAACS,qBAMvCX,gBAAgC,SAAUY,EAAeC,GACvD,OAAQD,GACN,KAAKZ,EAAcC,cACjB,OAAOC,EAAAA,eAAeD,cACxB,KAAKD,EAAcG,eACjB,OAAOD,EAAAA,eAAeC,eACxB,KAAKH,EAAcI,aACjB,OAAOF,EAAAA,eAAeE,aACxB,KAAKJ,EAAcK,MACjB,OAAOH,EAAAA,eAAeG,MACxB,KAAKL,EAAcM,WACjB,OAAOO,EAAQC,OACXZ,EAAAA,eAAeI,WACfJ,EAAAA,eAAeK,eACrB,KAAKP,EAAcQ,kBACjB,OAAON,EAAAA,eAAeM,kBACxB,KAAKR,EAAcS,uBACjB,OAAOP,EAAAA,eAAeO,uBACxB,KAAKT,EAAcU,uBACjB,OAAOR,EAAAA,eAAeQ,uBACxB,KAAKV,EAAcW,qBACjB,OAAOX,EAAcW,qBAE3B,EAKAX,SAAyB,SAAUY,GACjC,OACEA,IAAkBZ,EAAcQ,mBAChCI,IAAkBZ,EAAcS,wBAChCG,IAAkBZ,EAAcU,wBAChCE,IAAkBZ,EAAcW,oBAEpC,EAKAX,YAA4B,SAAUY,GACpC,OAAQA,GACN,KAAKZ,EAAcC,cACjB,OAAO,EACT,KAAKD,EAAcG,eACnB,KAAKH,EAAcS,uBACnB,KAAKT,EAAcU,uBACnB,KAAKV,EAAcW,qBACnB,KAAKX,EAAcM,WACjB,OAAO,EACT,KAAKN,EAAcI,aACnB,KAAKJ,EAAcK,MACnB,KAAKL,EAAcQ,kBACjB,OAAO,EAEb,EAKAR,SAAyB,SAAUY,GACjC,OACEA,IAAkBZ,EAAcC,eAChCW,IAAkBZ,EAAcG,gBAChCS,IAAkBZ,EAAcI,cAChCQ,IAAkBZ,EAAcK,OAChCO,IAAkBZ,EAAcM,YAChCM,IAAkBZ,EAAcQ,mBAChCI,IAAkBZ,EAAcS,wBAChCG,IAAkBZ,EAAcU,wBAChCE,IAAkBZ,EAAcW,oBAEpC,GAEA,IAAAI,EAAeC,OAAOC,OAAOjB,GCzF7B,MAAMkB,EAAc,CAOlBC,gBAAiBjB,EAAcA,eAACiB,gBAQhCC,cAAelB,EAAcA,eAACkB,cAQ9BC,MAAOnB,EAAcA,eAACmB,MAQtBC,IAAKpB,EAAcA,eAACoB,IAQpBC,KAAMrB,EAAcA,eAACqB,KAQrBC,UAAWtB,EAAcA,eAACsB,UAQ1BC,gBAAiBvB,EAAcA,eAACuB,gBAQhCC,SAAUxB,EAAcA,eAACyB,6BAQzBC,UAAW1B,EAAcA,eAAC2B,8BAQ1BC,UAAW5B,EAAcA,eAAC6B,8BAQ1BC,UAAW9B,EAAcA,eAAC+B,8BAQ1BC,iBAAkBhC,EAAcA,eAACiC,gCAQjCC,iBAAkBlC,EAAcA,eAACmC,gCAQjCC,kBAAmBpC,EAAcA,eAACqC,iCAQlCC,kBAAmBtC,EAAcA,eAACuC,iCAQlCC,UAAWxC,EAAcA,eAACyC,+BAQ1BC,SAAU1C,EAAcA,eAAC2C,0BAQzBC,UAAW5C,EAAcA,eAAC6C,qBAQ1BC,eAAgB9C,EAAcA,eAAC+C,0BAQ/BC,SAAUhD,EAAcA,eAACiD,2BAM3BjC,iBAA+B,SAAUkC,GACvC,OAAQA,GACN,KAAKlC,EAAYI,IACf,OAAO,EACT,KAAKJ,EAAYK,KACf,OAAO,EACT,KAAKL,EAAYO,gBACf,OAAO,EACT,KAAKP,EAAYG,MACjB,KAAKH,EAAYM,UAEjB,QACE,OAAO,EAEb,EAKAN,SAAuB,SAAUkC,GAC/B,OACEA,IAAgBlC,EAAYC,iBAC5BiC,IAAgBlC,EAAYE,eAC5BgC,IAAgBlC,EAAYG,OAC5B+B,IAAgBlC,EAAYI,KAC5B8B,IAAgBlC,EAAYK,MAC5B6B,IAAgBlC,EAAYM,WAC5B4B,IAAgBlC,EAAYO,iBAC5B2B,IAAgBlC,EAAYQ,UAC5B0B,IAAgBlC,EAAYU,WAC5BwB,IAAgBlC,EAAYY,WAC5BsB,IAAgBlC,EAAYc,WAC5BoB,IAAgBlC,EAAYgB,kBAC5BkB,IAAgBlC,EAAYkB,kBAC5BgB,IAAgBlC,EAAYoB,mBAC5Bc,IAAgBlC,EAAYsB,mBAC5BY,IAAgBlC,EAAYwB,WAC5BU,IAAgBlC,EAAY0B,UAC5BQ,IAAgBlC,EAAY4B,WAC5BM,IAAgBlC,EAAY8B,gBAC5BI,IAAgBlC,EAAYgC,QAEhC,EAKAhC,cAA4B,SAAUkC,GACpC,OACEA,IAAgBlC,EAAYG,OAC5B+B,IAAgBlC,EAAYI,KAC5B8B,IAAgBlC,EAAYK,MAC5B6B,IAAgBlC,EAAYM,WAC5B4B,IAAgBlC,EAAYO,eAEhC,EAKAP,cAA4B,SAAUkC,GACpC,OACEA,IAAgBlC,EAAYC,iBAC5BiC,IAAgBlC,EAAYE,aAEhC,EAKAF,mBAAiC,SAAUkC,GACzC,OACEA,IAAgBlC,EAAYQ,UAC5B0B,IAAgBlC,EAAYU,WAC5BwB,IAAgBlC,EAAYY,WAC5BsB,IAAgBlC,EAAYc,WAC5BoB,IAAgBlC,EAAYgB,kBAC5BkB,IAAgBlC,EAAYkB,kBAC5BgB,IAAgBlC,EAAYoB,mBAC5Bc,IAAgBlC,EAAYsB,mBAC5BY,IAAgBlC,EAAYwB,WAC5BU,IAAgBlC,EAAY0B,UAC5BQ,IAAgBlC,EAAY4B,WAC5BM,IAAgBlC,EAAY8B,gBAC5BI,IAAgBlC,EAAYgC,QAEhC,EAKAhC,YAA0B,SAAUkC,GAClC,OACEA,IAAgBlC,EAAYQ,UAC5B0B,IAAgBlC,EAAYU,WAC5BwB,IAAgBlC,EAAYY,WAC5BsB,IAAgBlC,EAAYc,SAEhC,EAKAd,cAA4B,SAAUkC,GACpC,OACEA,IAAgBlC,EAAYgB,kBAC5BkB,IAAgBlC,EAAYkB,kBAC5BgB,IAAgBlC,EAAYoB,mBAC5Bc,IAAgBlC,EAAYsB,iBAEhC,EAKAtB,aAA2B,SAAUkC,GACnC,OAAOA,IAAgBlC,EAAYwB,SACrC,EAKAxB,aAA2B,SAAUkC,GACnC,OAAOA,IAAgBlC,EAAY0B,QACrC,EAKA1B,aAA2B,SAAUkC,GACnC,OACEA,IAAgBlC,EAAY4B,WAC5BM,IAAgBlC,EAAY8B,cAEhC,EAKA9B,YAA0B,SAAUkC,GAClC,OAAOA,IAAgBlC,EAAYgC,QACrC,EAKAhC,6BAA2C,SACzCkC,EACAC,EACAC,GAEA,OAAQF,GACN,KAAKlC,EAAYQ,SACjB,KAAKR,EAAYU,UACjB,KAAKV,EAAY0B,SACjB,KAAK1B,EAAY4B,UACf,OAAOS,KAAKC,OAAOH,EAAQ,GAAK,GAAKE,KAAKC,OAAOF,EAAS,GAAK,GAAK,EAEtE,KAAKpC,EAAYY,UACjB,KAAKZ,EAAYc,UACjB,KAAKd,EAAYwB,UACjB,KAAKxB,EAAY8B,eACf,OAAOO,KAAKC,OAAOH,EAAQ,GAAK,GAAKE,KAAKC,OAAOF,EAAS,GAAK,GAAK,GAEtE,KAAKpC,EAAYgB,iBACjB,KAAKhB,EAAYoB,kBACf,OAAOiB,KAAKC,OAAOD,KAAKE,IAAIJ,EAAO,GAAKE,KAAKE,IAAIH,EAAQ,GAAK,EAAI,GAAK,GAEzE,KAAKpC,EAAYkB,iBACjB,KAAKlB,EAAYsB,kBACf,OAAOe,KAAKC,OACTD,KAAKE,IAAIJ,EAAO,IAAME,KAAKE,IAAIH,EAAQ,GAAK,EAAI,GAAK,GAG1D,KAAKpC,EAAYgC,SACf,OAAOK,KAAKG,KAAKL,EAAQ,GAAKE,KAAKG,KAAKJ,EAAS,GAAK,GAExD,QACE,OAAO,EAEb,EAKApC,mBAAiC,SAC/BkC,EACAxC,EACAyC,EACAC,GAEA,IAAIK,EAAmBzC,EAAYyC,iBAAiBP,GAIpD,OAHIpD,EAAc4D,SAAShD,KACzB+C,EAAmB,GAGnBA,EAAmB3D,EAAc6D,YAAYjD,GAAiByC,EAAQC,CAE1E,EAKApC,iBAA+B,SAAUkC,EAAaxC,EAAeyC,GACnE,MAAMS,EACJ5C,EAAY6C,mBAAmBX,EAAaxC,EAAeyC,EAAO,GAAK,EACzE,OAAe,IAARS,EAAY,EAAY,IAARA,EAAY,EAAI,CACzC,EAKA5C,iBAA+B,SAC7BkC,EACAxC,EACAyC,EACAC,GAEA,IAAIU,EACJ,MAAMH,EAAc7D,EAAc6D,YAAYjD,GAE5CoD,EADEH,IAAgBI,WAAWC,kBACfD,WACLJ,IAAgBM,YAAYD,kBACvBC,YAEdN,IAAgBO,aAAaF,mBAC7BtD,IAAkBZ,EAAcK,MAElB+D,aAEAC,YAIhB,OAAO,IAAIL,EADE9C,EAAYyC,iBAAiBP,GAAeC,EAAQC,EAEnE,EAKApC,MAAoB,SAClBoD,EACAlB,EACAxC,EACAyC,EACAC,GAEA,GAAe,IAAXA,EACF,OAAOgB,EAET,MAAMC,EAAUrD,EAAYsD,iBAC1BpB,EACAxC,EACAyC,EACAC,GAEImB,EAAqBvD,EAAYyC,iBAAiBP,GAClDsB,EAAerB,EAAQoB,EAC7B,IAAK,IAAIE,EAAI,EAAGA,EAAIrB,IAAUqB,EAAG,CAC/B,MAAMC,EAAMD,EAAItB,EAAQoB,EAClBI,GAAcvB,EAASqB,EAAI,GAAKtB,EAAQoB,EAC9C,IAAK,IAAIK,EAAI,EAAGA,EAAIJ,IAAgBI,EAClCP,EAAQM,EAAaC,GAAKR,EAAWM,EAAME,EAE9C,CACD,OAAOP,CACT,EAKArD,iBAA+B,SAAUkC,EAAaxC,EAAeC,GAEnE,IAAKA,EAAQC,OACX,OAAOsC,EAIT,GAAIA,IAAgBlC,EAAYE,cAC9B,OAAOlB,EAAAA,eAAe6E,iBAGxB,GAAI3B,IAAgBlC,EAAYC,gBAAiB,CAC/C,GAAIP,IAAkBZ,EAAcG,eAClC,OAAOD,EAAAA,eAAe8E,kBACjB,GAAIpE,IAAkBZ,EAAcI,aACzC,OAAOF,EAAAA,eAAe+E,iBAEzB,CAED,GAAIrE,IAAkBZ,EAAcK,MAClC,OAAQ+C,GACN,KAAKlC,EAAYK,KACf,OAAOrB,EAAAA,eAAegF,QACxB,KAAKhE,EAAYI,IACf,OAAOpB,EAAAA,eAAeiF,OACxB,KAAKjE,EAAYkE,GACf,OAAOlF,EAAAA,eAAemF,MACxB,KAAKnE,EAAYoE,EACf,OAAOpF,EAAAA,eAAeqF,KAI5B,GAAI3E,IAAkBZ,EAAcM,WAClC,OAAQ8C,GACN,KAAKlC,EAAYK,KACf,OAAOrB,EAAAA,eAAesF,QACxB,KAAKtE,EAAYI,IACf,OAAOpB,EAAAA,eAAeuF,OACxB,KAAKvE,EAAYkE,GACf,OAAOlF,EAAAA,eAAewF,MACxB,KAAKxE,EAAYoE,EACf,OAAOpF,EAAAA,eAAeyF,KAI5B,OAAOvC,CACT,GAEA,IAAAwC,EAAe5E,OAAOC,OAAOC,GC/M7B,IAAA2E,EAAe7E,OAAOC,OArRE,CACtB6E,oBAAqB,EACrBC,2BAA4B,EAC5BC,gCAAiC,EACjCC,gCAAiC,EACjCC,8BAA+B,EAC/BC,8BAA+B,EAC/BC,gCAAiC,EACjCC,gCAAiC,EACjCC,gCAAiC,EACjCC,mBAAoB,EACpBC,mBAAoB,GACpBC,qBAAsB,GACtBC,qBAAsB,GACtBC,kBAAmB,GACnBC,kBAAmB,GACnBC,kBAAmB,GACnBC,qBAAsB,GACtBC,qBAAsB,GACtBC,uBAAwB,GACxBC,uBAAwB,GACxBC,oBAAqB,GACrBC,oBAAqB,GACrBC,oBAAqB,GACrBC,uBAAwB,GACxBC,uBAAwB,GACxBC,yBAA0B,GAC1BC,yBAA0B,GAC1BC,sBAAuB,GACvBC,sBAAuB,GACvBC,sBAAuB,GACvBC,uBAAwB,GACxBC,uBAAwB,GACxBC,yBAA0B,GAC1BC,yBAA0B,GAC1BC,sBAAuB,GACvBC,sBAAuB,GACvBC,sBAAuB,GACvBC,yBAA0B,GAC1BC,yBAA0B,GAC1BC,2BAA4B,GAC5BC,2BAA4B,GAC5BC,wBAAyB,GACzBC,wBAAyB,GACzBC,wBAAyB,GACzBC,yBAA0B,GAC1BC,yBAA0B,GAC1BC,2BAA4B,GAC5BC,2BAA4B,GAC5BC,wBAAyB,GACzBC,wBAAyB,GACzBC,wBAAyB,GACzBC,gCAAiC,GACjCC,gCAAiC,GACjCC,kCAAmC,GACnCC,kCAAmC,GACnCC,+BAAgC,GAChCC,+BAAgC,GAChCC,+BAAgC,GAChCC,mCAAoC,GACpCC,mCAAoC,GACpCC,qCAAsC,GACtCC,qCAAsC,GACtCC,kCAAmC,GACnCC,kCAAmC,GACnCC,mCAAoC,GACpCC,mCAAoC,GACpCC,qCAAsC,GACtCC,qCAAsC,GACtCC,kCAAmC,GACnCC,kCAAmC,GACnCC,oBAAqB,GACrBC,oBAAqB,GACrBC,sBAAuB,GACvBC,sBAAuB,GACvBC,mBAAoB,GACpBC,mBAAoB,GACpBC,qBAAsB,GACtBC,uBAAwB,GACxBC,uBAAwB,GACxBC,yBAA0B,GAC1BC,yBAA0B,GAC1BC,sBAAuB,GACvBC,sBAAuB,GACvBC,wBAAyB,GACzBC,0BAA2B,GAC3BC,0BAA2B,GAC3BC,4BAA6B,GAC7BC,4BAA6B,GAC7BC,yBAA0B,GAC1BC,yBAA0B,GAC1BC,2BAA4B,GAC5BC,6BAA8B,GAC9BC,6BAA8B,GAC9BC,+BAAgC,GAChCC,+BAAgC,GAChCC,4BAA6B,GAC7BC,4BAA6B,GAC7BC,8BAA+B,GAC/BC,mBAAoB,GACpBC,mBAAoB,GACpBC,qBAAsB,IACtBC,sBAAuB,IACvBC,sBAAuB,IACvBC,wBAAyB,IACzBC,yBAA0B,IAC1BC,yBAA0B,IAC1BC,2BAA4B,IAC5BC,4BAA6B,IAC7BC,4BAA6B,IAC7BC,8BAA+B,IAC/BC,mBAAoB,IACpBC,mBAAoB,IACpBC,qBAAsB,IACtBC,sBAAuB,IACvBC,sBAAuB,IACvBC,wBAAyB,IACzBC,yBAA0B,IAC1BC,yBAA0B,IAC1BC,2BAA4B,IAC5BC,4BAA6B,IAC7BC,4BAA6B,IAC7BC,8BAA+B,IAC/BC,kCAAmC,IACnCC,iCAAkC,IAClCC,oBAAqB,IACrBC,8BAA+B,IAC/BC,qBAAsB,IACtBC,kBAAmB,IACnBC,4BAA6B,IAC7BC,4BAA6B,IAC7BC,6BAA8B,IAC9BC,8BAA+B,IAC/BC,6BAA8B,IAC9BC,+BAAgC,IAChCC,8BAA+B,IAC/BC,0BAA2B,IAC3BC,yBAA0B,IAC1BC,0BAA2B,IAC3BC,yBAA0B,IAC1BC,0BAA2B,IAC3BC,0BAA2B,IAC3BC,0BAA2B,IAC3BC,0BAA2B,IAC3BC,4BAA6B,IAC7BC,4BAA6B,IAC7BC,0BAA2B,IAC3BC,yBAA0B,IAC1BC,kCAAmC,IACnCC,iCAAkC,IAClCC,oCAAqC,IACrCC,mCAAoC,IACpCC,oCAAqC,IACrCC,mCAAoC,IACpCC,8BAA+B,IAC/BC,8BAA+B,IAC/BC,iCAAkC,IAClCC,iCAAkC,IAClCC,+BAAgC,IAChCC,8BAA+B,IAC/BC,+BAAgC,IAChCC,8BAA+B,IAC/BC,+BAAgC,IAChCC,8BAA+B,IAC/BC,+BAAgC,IAChCC,8BAA+B,IAC/BC,+BAAgC,IAChCC,8BAA+B,IAC/BC,+BAAgC,IAChCC,8BAA+B,IAC/BC,+BAAgC,IAChCC,8BAA+B,IAC/BC,+BAAgC,IAChCC,8BAA+B,IAC/BC,gCAAiC,IACjCC,+BAAgC,IAChCC,gCAAiC,IACjCC,+BAAgC,IAChCC,gCAAiC,IACjCC,+BAAgC,IAChCC,iCAAkC,IAClCC,gCAAiC,IACjCC,iCAAkC,IAClCC,gCAAiC,IACjCC,iCAAkC,IAClCC,gCAAiC,IACjCC,6BAA8B,UAC9BC,6BAA8B,WAC9BC,oCAAqC,WACrCC,mCAAoC,WACpCC,oCAAqC,WACrCC,mCAAoC,WACpCC,oCAAqC,WACrCC,6BAA8B,WAC9BC,mCAAoC,WACpCC,6CAA8C,WAC9CC,iDAAkD,WAClDC,iDAAkD,WAClDC,qDAAsD,WACtDC,oDAAqD,WACrDC,qDAAsD,WACtDC,oDAAqD,WACrDC,qDAAsD,WACtDC,6BAA8B,WAC9BC,mCAAoC,WACpCC,6CAA8C,WAC9CC,iDAAkD,WAClDC,iDAAkD,WAClDC,qDAAsD,WACtDC,oDAAqD,WACrDC,qDAAsD,WACtDC,oDAAqD,WACrDC,qDAAsD,WACtDC,iCAAkC,WAClCC,iCAAkC,WAClCC,uCAAwC,WACxCC,sCAAuC,WACvCC,uCAAwC,WACxCC,sCAAuC,WACvCC,uCAAwC,WACxCC,sCAAuC,UACvCC,sCAAuC,WACvCC,sCAAuC,WACvCC,sCAAuC,WACvCC,qCAAsC,WACtCC,qCAAsC,WACtCC,qCAAsC,WACtCC,qCAAsC,WACtCC,oCAAqC,UACrCC,oCAAqC,WACrCC,oCAAqC,WACrCC,oCAAqC,WACrCC,oCAAqC,WACrCC,oCAAqC,WACrCC,oCAAqC,WACrCC,oCAAqC,WACrCC,qCAAsC,WACtCC,qCAAsC,WACtCC,qCAAsC,WACtCC,sCAAuC,WACvCC,sCAAuC,WACvCC,sCAAuC,WACvCC,iCAAkC,UAClCC,iCAAkC,WAClCC,wCAAyC,WACzCC,uCAAwC,WACxCC,wCAAyC,WACzCC,uCAAwC,WACxCC,wCAAyC,WACzCC,iCAAkC,WAClCC,uCAAwC,WACxCC,iDAAkD,WAClDC,qDAAsD,WACtDC,qDAAsD,WACtDC,yDAA0D,WAC1DC,wDAAyD,WACzDC,yDAA0D,WAC1DC,wDAAyD,WACzDC,yDAA0D,WAC1DC,iCAAkC,WAClCC,uCAAwC,WACxCC,iDAAkD,WAClDC,qDAAsD,WACtDC,qDAAsD,WACtDC,yDAA0D,WAC1DC,wDAAyD,WACzDC,yDAA0D,WAC1DC,wDAAyD,WACzDC,yDAA0D,WAC1DC,qCAAsC,WACtCC,qCAAsC,WACtCC,2CAA4C,WAC5CC,0CAA2C,WAC3CC,2CAA4C,WAC5CC,0CAA2C,WAC3CC,2CAA4C,aC1R9C,MAAMC,EAAE,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAIC,EAAEvS,EAAEwS,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAYC,KAAuFP,IAAIA,EAAE,CAAA,IAAxFO,EAAEC,KAAK,GAAG,OAAOD,EAAEA,EAAEE,QAAQ,GAAG,UAAUF,EAAEA,EAAEG,KAAK,GAAG,OAAOH,EAAEA,EAAEI,KAAK,GAAG,OAAoB,SAASJ,GAAGA,EAAEA,EAAEK,YAAY,GAAG,aAAc,CAA7C,CAA+CnT,IAAIA,EAAE,CAAA,IAAK,SAAS8S,GAAGA,EAAEA,EAAEM,YAAY,GAAG,cAAcN,EAAEA,EAAEO,MAAM,KAAK,QAAQP,EAAEA,EAAEQ,MAAM,KAAK,OAAQ,CAA3F,CAA6Fd,IAAIA,EAAE,CAAE,IAAG,SAASM,GAAGA,EAAEA,EAAEM,YAAY,GAAG,cAAcN,EAAEA,EAAES,KAAK,GAAG,MAAO,CAAhE,CAAkEd,IAAIA,EAAE,CAAE,IAAG,SAASK,GAAGA,EAAEA,EAAEM,YAAY,GAAG,cAAcN,EAAEA,EAAEU,OAAO,GAAG,SAASV,EAAEA,EAAES,KAAK,GAAG,OAAOT,EAAEA,EAAEW,IAAI,GAAG,MAAMX,EAAEA,EAAEY,KAAK,GAAG,OAAOZ,EAAEA,EAAEa,KAAK,GAAG,OAAOb,EAAEA,EAAEc,MAAM,GAAG,OAAQ,CAAnK,CAAqKlB,IAAIA,EAAE,CAAE,IAAG,SAASI,GAAGA,EAAEA,EAAEe,eAAe,GAAG,iBAAiBf,EAAEA,EAAEgB,oBAAoB,GAAG,qBAAsB,CAApG,CAAsGnB,IAAIA,EAAE,CAAA,IAAK,SAASG,GAAGA,EAAEA,EAAEnW,IAAI,GAAG,MAAMmW,EAAEA,EAAEiB,IAAI,GAAG,MAAMjB,EAAEA,EAAEkB,IAAI,GAAG,MAAMlB,EAAEA,EAAEmB,IAAI,IAAI,KAAM,CAAjF,CAAmFrB,IAAIA,EAAE,CAAE,IAAG,SAASE,GAAGA,EAAEA,EAAEnW,IAAI,GAAG,MAAMmW,EAAEA,EAAElW,KAAK,GAAG,OAAOkW,EAAEA,EAAEiB,IAAI,GAAG,MAAMjB,EAAEA,EAAEoB,KAAK,GAAG,MAAO,CAApF,CAAsFrB,IAAIA,EAAE,CAAE,IAAG,MAAMsB,EAAE9U,cAAc+U,KAAKC,SAAS,EAAED,KAAKE,SAAS,EAAEF,KAAKG,WAAW,EAAEH,KAAKI,YAAY,EAAEJ,KAAKK,WAAW,EAAEL,KAAKM,WAAW,EAAEN,KAAKO,UAAU,EAAEP,KAAKQ,uBAAuBrC,EAAEQ,KAAKqB,KAAKS,OAAO,GAAGT,KAAKU,qBAAqB,CAAC,CAACC,SAAS,EAAEC,eAAehV,EAAEmT,YAAY8B,cAAc,EAAEC,oBAAoB,GAAGC,WAAW3C,EAAEY,YAAYgC,eAAe3C,EAAEc,KAAK8B,iBAAiB5C,EAAEc,KAAK+B,MAAM3C,EAAEkB,eAAe0B,oBAAoB,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,GAAGC,WAAW,GAAGC,QAAQ,KAAKzB,KAAK0B,SAAS,CAAE,EAAC1B,KAAK2B,WAAW,IAAK,EAAE,MAAMC,EAAE3W,YAAYyT,EAAER,EAAEC,EAAEvS,GAAGoU,KAAK6B,UAAU,IAAIC,SAASpD,EAAEqD,OAAOrD,EAAEsD,WAAW9D,EAAEC,GAAG6B,KAAKiC,cAAcrW,EAAEoU,KAAKkC,QAAQ,CAAE,CAACC,aAAa,MAAMzD,EAAEsB,KAAK6B,UAAUO,SAASpC,KAAKkC,SAAS,OAAOlC,KAAKkC,SAAS,EAAExD,CAAC,CAAC2D,cAAc,MAAM3D,EAAEsB,KAAK6B,UAAUS,UAAUtC,KAAKkC,QAAQlC,KAAKiC,eAAe,OAAOjC,KAAKkC,SAAS,EAAExD,CAAC,CAAC6D,cAAc,MAAM7D,EAAEsB,KAAK6B,UAAUW,UAAUxC,KAAKkC,QAAQlC,KAAKiC,eAAe,OAAOjC,KAAKkC,SAAS,EAAExD,CAAC,CAAC+D,cAAc,MAAM/D,EAAEsB,KAAK6B,UAAUW,UAAUxC,KAAKkC,QAAQlC,KAAKiC,eAAe,GAAG,GAAGjC,KAAK6B,UAAUW,UAAUxC,KAAKkC,QAAQ,EAAElC,KAAKiC,eAAe,OAAOjC,KAAKkC,SAAS,EAAExD,CAAC,CAACgE,MAAMhE,GAAG,OAAOsB,KAAKkC,SAASxD,EAAEsB,IAAI,CAAC2C,MAAMjE,EAAER,EAAE,GAAG,MAAMC,EAAE6B,KAAKkC,QAAQ,IAAItW,EAAE,EAAE,KAAKoU,KAAK6B,UAAUO,SAASpC,KAAKkC,WAAWhE,GAAGtS,EAAE8S,GAAG9S,IAAIoU,KAAKkC,UAAU,OAAOtW,EAAE8S,GAAGsB,KAAKkC,UAAU,IAAIhX,WAAW8U,KAAK6B,UAAUE,OAAO/B,KAAK6B,UAAUG,WAAW7D,EAAEvS,EAAE,EAAE,SAASgX,EAAElE,GAAG,MAAO,oBAAoBmE,aAAY,IAAKA,aAAaC,OAAOpE,GAAGqE,OAAOC,KAAKtE,GAAGuE,SAAS,OAAO,CCSpyE,MAAMC,EAAY,CAChB,YACA,YACA,YACA,YACA,YACA,aAOF,IAAIC,EACJ,SAASC,EAAUC,EAAYC,GAE7BC,EAAAA,MAAMC,OAAOC,OAAO,mBAAoBN,GAGxC,MAAMO,EAAOL,EAAWM,WAClBC,EAAyBP,EAAWO,uBAC1C,IAAIC,EACJ,IACEA,EDhCiyE,SAAWnF,GAAG,MAAMP,EAAE,IAAIjT,WAAWwT,EAAEqD,OAAOrD,EAAEsD,WAAW9D,EAAE4F,QAAQ,GAAG3F,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,KAAKD,EAAE,IAAIC,EAAE,MAAMD,EAAE,KAAKC,EAAE,MAAMD,EAAE,IAAI,MAAM,IAAI6F,MAAM,+BAA+B,MAAMnY,EAAE,IAAImU,EAAE3B,EAAE,GAAG9S,YAAYH,kBAAkBkT,EAAE,IAAIuD,EAAElD,EAAER,EAAE4F,OAAO1F,GAAE,GAAIxS,EAAEqU,SAAS5B,EAAEkE,cAAc3W,EAAEsU,SAAS7B,EAAEkE,cAAc3W,EAAEuU,WAAW9B,EAAEkE,cAAc3W,EAAEwU,YAAY/B,EAAEkE,cAAc3W,EAAEyU,WAAWhC,EAAEkE,cAAc3W,EAAE0U,WAAWjC,EAAEkE,cAAc3W,EAAE2U,UAAUlC,EAAEkE,cAAc,MAAMjE,EAAED,EAAEkE,cAAc3W,EAAE4U,uBAAuBnC,EAAEkE,cAAc,MAAMhE,EAAEF,EAAEkE,cAAc/D,EAAEH,EAAEkE,cAAc9D,EAAEJ,EAAEkE,cAAcyB,EAAE3F,EAAEkE,cAAc0B,EAAE5F,EAAEoE,cAAcyB,EAAE7F,EAAEoE,cAAcrB,EAAE,IAAIQ,EAAElD,EAAER,EAAE4F,OAAO1F,EAAE,EAAEE,EAAE,GAAE,GAAI,IAAI,IAAIJ,EAAE,EAAEA,EAAEI,EAAEJ,IAAItS,EAAE6U,OAAO0D,KAAK,CAACC,UAAU,IAAIlZ,WAAWwT,EAAEqD,OAAOrD,EAAEsD,WAAWZ,EAAEqB,cAAcrB,EAAEqB,eAAe4B,uBAAuBjD,EAAEqB,gBAAgB,MAAM6B,EAAE,IAAI1C,EAAElD,EAAEH,EAAEC,GAAE,GAAI6C,EAAE,CAACV,SAAS2D,EAAE5B,MAAM,GAAGL,cAAczB,eAAe0D,EAAEjC,cAAcxB,cAAcyD,EAAEjC,cAAcvB,oBAAoBwD,EAAEjC,cAActB,WAAWuD,EAAEnC,aAAanB,eAAesD,EAAEnC,aAAalB,iBAAiBqD,EAAEnC,aAAajB,MAAMoD,EAAEnC,aAAahB,oBAAoB,CAACC,EAAEkD,EAAEnC,aAAa,EAAEd,EAAEiD,EAAEnC,aAAa,EAAEb,EAAEgD,EAAEnC,aAAa,EAAEZ,EAAE+C,EAAEnC,aAAa,GAAGX,WAAW,CAAC8C,EAAEnC,aAAamC,EAAEnC,aAAamC,EAAEnC,aAAamC,EAAEnC,aAAamC,EAAEnC,aAAamC,EAAEnC,aAAamC,EAAEnC,aAAamC,EAAEnC,cAAcV,QAAQ,IAAI8C,GAAGlD,EAAEP,oBAAoB,EAAE,GAAG,EAAE,IAAI,IAAIpC,EAAE,EAAEA,EAAE6F,EAAE7F,IAAI2C,EAAEI,QAAQ/C,GAAG,CAAC8F,UAAUF,EAAEjC,cAAcoC,UAAUH,EAAEnC,aAAauC,UAAUJ,EAAEnC,aAAawC,eAAe,CAACL,EAAEnC,aAAamC,EAAEnC,aAAamC,EAAEnC,aAAamC,EAAEnC,cAAcyC,YAAYN,EAAE/B,cAAcsC,YAAYP,EAAE/B,eAAe3W,EAAE8U,qBAAqBoD,OAAO,EAAElY,EAAE8U,qBAAqByD,KAAK9C,GAAG,MAAMyD,EAAE,IAAIlD,EAAElD,EAAED,EAAEuF,GAAE,GAAI,KAAKc,EAAE5C,QAAQ8B,GAAG,CAAC,MAAMtF,EAAEoG,EAAEvC,cAAcrE,EAAE4G,EAAEnC,MAAMjE,GAAGP,EAAEyE,EAAE1E,GAAGE,EAAE0G,EAAEnC,MAAMjE,EAAER,EAAE6G,YAAYnZ,EAAE8V,SAASvD,GAAGA,EAAE6G,MAAM,SAASpC,EAAExE,GAAGA,EAAE0G,EAAE5C,QAAQ,GAAG4C,EAAEpC,MAAM,EAAEoC,EAAE5C,QAAQ,EAAG,CAAC,GAAGgC,GAAG,EAAE,OAAOtY,EAAE,MAAMqZ,EAAE,IAAIrD,EAAElD,EAAEuF,EAAEC,GAAE,GAAIgB,EAAED,EAAE5C,cAAcd,EAAE0D,EAAE5C,cAAc8C,EAAEF,EAAE1C,cAAc6C,EAAEH,EAAE1C,cAAc8C,EAAEJ,EAAE1C,cAAc+C,EAAEL,EAAE1C,cAAcgD,EAAE,GAAG,IAAI,IAAI7G,EAAE,EAAEA,EAAEJ,EAAEI,IAAI6G,EAAEpB,KAAK,CAACqB,WAAWP,EAAE1C,cAAckD,mBAAmBR,EAAE1C,cAAcmD,mBAAmBT,EAAE1C,cAAcoD,qBAAqBV,EAAE1C,cAAcqD,qBAAqBX,EAAE1C,gBAAgB,MAAMhW,EAAE0X,EAAEgB,EAAE/C,QAAQ2D,EAAEtZ,EAAE4Y,EAAEW,EAAED,EAAET,EAAEW,EAAED,EAAET,EAAEW,EAAE,IAAI9a,WAAWwT,EAAEqD,OAAOrD,EAAEsD,WAAWzV,EAAE4Y,GAAGc,EAAE,IAAI/a,WAAWwT,EAAEqD,OAAOrD,EAAEsD,WAAW6D,EAAET,GAAGc,EAAE,IAAIhb,WAAWwT,EAAEqD,OAAOrD,EAAEsD,WAAW8D,EAAET,GAAGc,EAAE,IAAIjb,WAAWwT,EAAEqD,OAAOrD,EAAEsD,WAAW+D,EAAET,GAAG,OAAO1Z,EAAE+V,WAAW,CAACyE,cAAclB,EAAEmB,cAAc9E,EAAE+E,WAAWf,EAAEgB,cAAcP,EAAEQ,cAAcP,EAAEQ,WAAWP,EAAEQ,aAAaP,GAAGva,CAAC,CCgCn3J+a,CAAUjD,EAGpB,CAFC,MAAOxF,GACP,MAAM,IAAI0I,EAAAA,aAAa,qBACxB,CAED,GAA0B,IAAtB/C,EAAOvD,WACT,MAAM,IAAIsG,EAAAA,aAAa,0CAGzB,GAA0B,IAAtB/C,EAAOxD,WACT,MAAM,IAAIuG,EAAAA,aAAa,qCAGzB,MAAMC,EAAMhD,EAAOnD,qBAAqB,GAClCoG,EAAS,IAAIC,MAAMlD,EAAOmD,YAoBhC,OAjBsB,IAApBnD,EAAO5D,UA9Ba,MA+BnB4G,EAAI9F,YA9Be,MA8BmB8F,EAAI9F,YAY3CuC,EAAoBa,KAAKT,EAAK3B,QAQlC,SAA2B8B,EAAQiD,GACjC,MAAMG,EACJpD,EAAO5D,WAAaiH,EAAgBtY,sBAChCzG,EAAYI,IACZJ,EAAYK,KAClB,IAAI2e,EACAtD,EAAO5D,WAAaiH,EAAgB9X,yBACtC+X,EAAWlgB,EAAcC,cAEzB2c,EAAO5D,WAAaiH,EAAgBlU,8BAEpCmU,EAAWlgB,EAAcM,WAEzBsc,EAAO5D,WAAaiH,EAAgBtT,gCAEpCuT,EAAWlgB,EAAcK,OAG3B,IAAK,IAAIsE,EAAI,EAAGA,EAAIiY,EAAOpD,OAAOqD,SAAUlY,EAAG,CAC7C,MAAMwb,EAAQ,CAAA,EACdN,EAAOlb,GAAKwb,EACZ,MAAMC,EAAcxD,EAAOpD,OAAO7U,GAAGwY,UAE/B9Z,EAAQuZ,EAAO1D,YAAcvU,EAC7BrB,EAASsZ,EAAOzD,aAAexU,EAC/B0b,EACJhd,EAAQC,EAASpC,EAAYyC,iBAAiBqc,GAEhD,IAAK,IAAIlb,EAAI,EAAGA,EAAI8X,EAAOtD,YAAaxU,EAAG,CAEzC,MAAMwb,EACJF,EAAYrF,WAAasF,EAAazD,EAAO3D,SAAWnU,EAC1D,IAAIyb,EAQFA,EAPGC,EAAOA,QAACN,IAAqD,IAAxClgB,EAAc6D,YAAYqc,GAMD,IAAxClgB,EAAc6D,YAAYqc,GACxB,IAAI/b,YACbic,EAAYtF,OACZwF,EACAD,GAGS,IAAIjc,aACbgc,EAAYtF,OACZwF,EACAD,GAfS,IAAIpc,WACbmc,EAAYtF,OACZwF,EACAD,GAgBJF,EAAMlE,EAAUnX,IAAM,CACpBkb,eAAgBA,EAChBE,SAAUA,EACV7c,MAAOA,EACPC,OAAQA,EACR8c,YAAaG,EAEhB,CACF,CACH,CArEIE,CAAkB7D,EAAQiD,IAuE9B,SACEpD,EACAG,EACAD,EACAT,EACAG,EACAwD,GAEA,MAAMa,EAAW,IAAIxE,EAAiByE,SAASlE,GAC/C,IAAIpZ,EAAQqd,EAASE,WACjBtd,EAASod,EAASG,YACtB,MAAMrH,EAASkH,EAASI,YAClBC,EAAWL,EAASM,cAE1B,KAAM3d,EAAQ,GAAQC,EAAS,GAAQkW,EAAS,GAG9C,MAFAkH,EAASO,QACTP,EAASQ,SACH,IAAIvB,EAAAA,aAAa,qBAGzB,IAAIK,EAAgBmB,EACpB,MAAMvB,EAAMhD,EAAOnD,qBAAqB,GAClC2H,EAAclF,EAAiBmF,0BAGrC,GA5IsB,MA4IlBzB,EAAI9F,WACN,GAAI6C,EAAuB2E,IACzBtB,EAAiBe,EACb7f,EAAY8B,eACZ9B,EAAY4B,UAChBqe,EAAmBJ,EACfK,EAAYG,aACZH,EAAYI,iBACX,GAAI7E,EAAuB8E,OAASV,EACzCf,EAAiB9e,EAAY0B,SAC7Bue,EAAmBC,EAAYI,iBAC1B,GAAI7E,EAAuB+E,KAChC1B,EAAiBe,EAAW7f,EAAYc,UAAYd,EAAYQ,SAChEyf,EAAmBJ,EACfK,EAAYO,YACZP,EAAYQ,gBACX,GAAIjF,EAAuBkF,MAChC7B,EAAiBe,EACb7f,EAAYoB,kBACZpB,EAAYgB,iBAChBif,EAAmBJ,EACfK,EAAYU,iBACZV,EAAYW,qBACX,GAAIpF,EAAuBqF,KAChChC,EAAiB9e,EAAYwB,UAC7Bye,EAAmBC,EAAYa,qBAC1B,KAAItF,EAAuBuF,IAIhC,MAAM,IAAIvC,EAAYA,aACpB,qEAJFK,EAAiB9e,EAAYgC,SAC7Bie,EAAmBC,EAAYe,WAKhC,MACI,GA7Ke,MA6KXvC,EAAI9F,WACb,GAAI6C,EAAuBqF,KACzBhC,EAAiB9e,EAAYwB,UAC7Bye,EAAmBC,EAAYa,sBAC1B,GAAItF,EAAuBuF,IAChClC,EAAiB9e,EAAYgC,SAC7Bie,EAAmBC,EAAYe,iBAC1B,GAAIxF,EAAuB+E,KAChC1B,EAAiBe,EAAW7f,EAAYc,UAAYd,EAAYQ,SAChEyf,EAAmBJ,EACfK,EAAYO,YACZP,EAAYQ,gBACX,GAAIjF,EAAuB2E,IAChCtB,EAAiBe,EACb7f,EAAY8B,eACZ9B,EAAY4B,UAChBqe,EAAmBJ,EACfK,EAAYG,aACZH,EAAYI,iBACX,GAAI7E,EAAuB8E,OAASV,EACzCf,EAAiB9e,EAAY0B,SAC7Bue,EAAmBC,EAAYI,gBAC1B,KAAI7E,EAAuBkF,MAQhC,MAAM,IAAIlC,EAAYA,aACpB,qEARFK,EAAiBe,EACb7f,EAAYoB,kBACZpB,EAAYgB,iBAChBif,EAAmBJ,EACfK,EAAYU,iBACZV,EAAYW,eAKjB,CAGH,IAAKrB,EAAS0B,mBAGZ,MAFA1B,EAASO,QACTP,EAASQ,SACH,IAAIvB,EAAAA,aAAa,6BAGzB,IAAK,IAAIhb,EAAI,EAAGA,EAAIiY,EAAOpD,OAAOqD,SAAUlY,EAAG,CAC7C,MAAMwb,EAAQ,CAAA,EACdN,EAAOlb,GAAKwb,EACZ9c,EAAQuZ,EAAO1D,YAAcvU,EAC7BrB,EAASsZ,EAAOzD,aAAexU,EAK/B,MAAM0d,EAAU3B,EAAS4B,8BACvB3d,EACA,EACA,EACAwc,EAAiBoB,OAEbC,EAAM,IAAIve,WAAWoe,GAErBI,EAAa/B,EAASgC,eAC1BF,EACA7d,EACA,EACA,EACAwc,EAAiBoB,MACjB,GACC,GACA,GAGH,IAAK/B,EAAAA,QAAQiC,GACX,MAAM,IAAI9C,EAAAA,aAAa,4BAGzBtD,EAAoBa,KAAKsF,EAAI1H,QAE7BqF,EAAMlE,EAAU,IAAM,CACpB+D,eAAgBA,EAChB3c,MAAOA,EACPC,OAAQA,EACR8c,YAAaoC,EAEhB,CAED9B,EAASO,QACTP,EAASQ,QAEX,CAnOIyB,CACElG,EACAG,EACAD,EACAT,EACAG,EACAwD,GAOGA,CACT,CAuNA,SAAS+C,EAAWC,GAClB3G,EAAmB2G,EACnB3G,EAAiB4G,kBAEjBC,KAAKC,UAAYC,EAA0B9G,GAC3C4G,KAAKG,aAAY,EACnB,QAEA,SAAuBC,GACrB,MAGMC,EAHOD,EAAM1G,KAGK4G,kBACxB,GAAI7C,EAAAA,QAAQ4C,GAEV,OAAOE,QAAQ,CAACF,EAAWG,aAAa,SAAUC,GAChD,IAAIhD,EAAOA,QAAC4C,EAAWK,gBASrB,OAAOD,IAAqBE,MAAK,SAAUC,GACzCf,EAAWe,EACrB,IAVanD,EAAAA,QAAQgD,KACXA,EAAqBT,KAAKa,gBAG5BJ,EAAmBJ,GAAYM,MAAK,SAAUb,GAC5CD,EAAWC,EACrB,GAMA,GAEA"}