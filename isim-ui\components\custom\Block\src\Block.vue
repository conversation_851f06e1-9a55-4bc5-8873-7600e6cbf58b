<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/1
* @Version : 1.0
* @Content : Block
-->
<template>
  <div class="sim-block" :class="{ 'sim-disabled': $attrs.disabled }">
    <template v-if="isRadius">
      <div class="sim-block__left"></div>
      <div class="sim-block__right"></div>
    </template>
    <slot />
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: 'SimBlock'
});
defineEmits(['change', 'input']);
defineProps({
  isRadius: {
    type: Boolean,
    default: false
  }
});
</script>
<style scoped lang="less">
.is-error .sim-block {
  --sim-block-border: var(--red-color);
  --sim-block--bgc: rgba(var(--red-color-val), 0.15);
  --sim-block-radial: var(--red-color);
}

.sim-block {
  --sim-block-border: var(--primary-color-5);
  --sim-block--bgc: rgba(var(--primary-color-val), 0.15);
  --sim-block-radial: var(--primary-color);
  display: inline-block;
  position: relative;
  border: 1px solid var(--sim-block-border);
  //border-radius: 8px;
  overflow: hidden;
  //padding: 2px;
  background: var(--sim-block--bgc);
  box-sizing: border-box;
  .sim-radial(@posX, @posY) {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    @start: 6px;
    @stop: calc(@start + 0.8px);
    background: radial-gradient(
      farthest-corner at @posX @posY,
      transparent @start,
      var(--sim-block-radial) @start,
      var(--sim-block-radial) @stop,
      transparent @stop
    );
  }

  &.disabled {
    --sim-block-border: rgba(var(--primary-color-val), 0.15);
    --sim-block-radial: rgba(var(--primary-color-val), 0.15);

    :deep(span) {
      color: rgba(var(--text-color-val), 0.4);
    }
  }

  .sim-block__left {
    position: absolute;
    left: 0;
    top: 0;
    width: 8px;
    height: 100%;

    &:before {
      .sim-radial(100%, 100%);
      top: 0;
      left: 0;
    }

    &:after {
      .sim-radial(100%, 0);
      left: 0;
      bottom: 0;
    }
  }

  .sim-block__right {
    position: absolute;
    right: 0;
    top: 0;
    width: 8px;
    height: 100%;

    &:before {
      .sim-radial(0, 100%);
      top: 0;
      left: 0;
    }

    &:after {
      .sim-radial(0, 0);
      bottom: 0;
      left: 0;
    }
  }
}
</style>

<style lang="less">
.sim-disabled.sim-block {
  border-color: rgba(var(--primary-color-val), 0.15);
  background: transparent;
  --sim-block-radial: rgba(var(--primary-color-val), 0.15);

  .el-input .el-input__wrapper:hover {
    background: transparent !important;
  }

  .el-textarea .el-textarea__inner {
    box-shadow: none;
  }

  .el-textarea .el-textarea__inner:hover {
    box-shadow: none;
    background: transparent !important;
  }

  .el-icon svg path {
    fill: rgba(var(--text-color-val), 0.3);
  }

  .el-input-number__decrease,
  .el-input-number__increase {
    border-color: rgba(var(--text-color-val), 0.3) !important;
  }
}

.sim-readonly {
  .el-col {
    padding: 0 1px 0 0 !important;
  }

  .el-form-item:not(.no-readonly) {
    margin-bottom: 1px;

    & + .schema-divider {
      margin-top: 24px;
    }

    .el-form-item__content {
      margin-left: 1px;
      padding: 0 8px;
      background: rgba(var(--text-color-val), 0.1);
    }

    .el-form-item__label {
      display: block;
      padding: 0 8px;
      height: auto;
      background: rgba(var(--primary-color-val), 0.15);
      text-align: left;

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:before,
      .genFormLabel:before {
        display: none;
      }
    }

    .el-input__wrapper,
    .el-textarea__inner,
    .el-input__inner {
      display: block;
      padding-left: 0 !important;
      text-align: left;
      border: none;
      box-shadow: none;
      resize: none;
      background: transparent;
      pointer-events: none;
      -webkit-text-fill-color: #fff;
    }

    .el-form-item__label,
    .el-textarea__inner,
    .el-input__inner,
    .el-select {
      pointer-events: none;
    }

    .el-tag--info {
      pointer-events: auto;
    }

    .el-input__wrapper {
      padding: 0;
    }

    ::-webkit-input-placeholder {
      opacity: 0;
    }

    .el-input__prefix,
    .el-input__suffix {
      display: none;
    }

    .sim-block {
      //height: auto;
      border: none;
      --sim-block-border: none;
      --sim-block-radial: 0;
      --sim-block--bgc: transparent;
    }

    .sim-block__left,
    .sim-block__right {
      display: none;
    }

    .el-input-number__decrease,
    .el-input-number__increase {
      display: none;
    }

    .el-upload,
    .el-upload-list__item-status-label {
      display: none;
    }
    .el-upload-list--picture-card {
      background: transparent !important;
      border: none !important;
    }

    .el-divider--horizontal {
      margin-top: 24px;
    }

    .sim-table {
      margin: 8px 0;
      .el-table__header-wrapper {
        margin-bottom: 0;
      }

      .el-table__cell {
        padding: 0;
        border-radius: 0;
      }

      .el-table__cell:not(:last-child) {
        border-right: 1px solid var(--model-bg-color);
      }

      .el-table__row {
        height: 32px;
      }
    }
  }
}
</style>
