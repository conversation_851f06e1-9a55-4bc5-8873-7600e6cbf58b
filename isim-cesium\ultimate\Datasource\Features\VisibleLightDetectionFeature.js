/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/08/08 18:18:19
 * @description content
 * @version 1.0
 * */
import { defaultValue } from 'cesium';
import VisibleLightDetectionPrimitive from '../../Modules/VisualizationModel/VisibleLightDetectionVisualizer.js';
import AbstractFeature from './AbstractFeature.js';
const EXCLUDE_PARAMETERS = ['orientation', 'scene'];

export default class VisibleLightDetectionFeature extends AbstractFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this.create();
  }

  create() {
    this.primitive = new VisibleLightDetectionPrimitive();
  }
  update(time) {
    this._options.position = this.position.getValue(time);
    if (this.primitive) {
      this._updateOptions(time, this.primitive);
    }
  }

  _updateOptions(time, options) {
    options = defaultValue(options, {});
    let property, value;
    Object.keys(this._options).forEach((key) => {
      if (!EXCLUDE_PARAMETERS.includes(key)) {
        property = this._options[key];
        if (property?.getValue) {
          value = property.getValue(time);
          if (key === 'show') {
            options[key] = this._setShow(time, value);
          } else {
            options[key] = value;
          }
        } else {
          if (key === 'show') {
            options[key] = this._setShow(time, property);
          } else {
            options[key] = property;
          }
        }
      }
    });
    return options;
  }
}
