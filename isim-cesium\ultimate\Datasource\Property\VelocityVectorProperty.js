/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/07/20 16:09:17
 * @description update and add Inertial Quaternion calculate Method
 * */

import { Cartesian3, defaultValue, defined, DeveloperError, Event, ExtrapolationType, JulianDate, Property, ReferenceFrame } from 'cesium';

function VelocityVectorProperty(position, normalize) {
  this._position = undefined;
  this._subscription = undefined;
  this._definitionChanged = new Event();
  this._normalize = defaultValue(normalize, true);
  this._currentCartesian = undefined;
  this._currentVelocity = undefined;
  this._forwardExtrapolationType = ExtrapolationType.HOLD;
  this.position = position;
}

Object.defineProperties(VelocityVectorProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._position);
    }
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },
  position: {
    get: function () {
      return this._position;
    },
    set: function (value) {
      let oldValue = this._position;
      if (oldValue !== value) {
        if (defined(oldValue)) {
          this._subscription();
        }

        this._position = value;

        if (defined(value)) {
          this._subscription = value._definitionChanged.addEventListener(function () {
            this._definitionChanged.raiseEvent(this);
          }, this);
        }

        this._definitionChanged.raiseEvent(this);
      }
    }
  },
  normalize: {
    get: function () {
      return this._normalize;
    },
    set: function (value) {
      if (this._normalize === value) {
        return;
      }

      this._normalize = value;
      this._definitionChanged.raiseEvent(this);
    }
  },
  /**
   * Gets or sets the type of extrapolation to perform when a value
   * is requested at a time before any available samples.
   * @memberof VelocityOrientationProperty.prototype
   * @type {ExtrapolationType}
   * @default ExtrapolationType.NONE
   */
  forwardExtrapolationType: {
    get: function () {
      return this.forwardExtrapolationType;
    },
    set: function (value) {
      this._forwardExtrapolationType = value;
    }
  }
});

VelocityVectorProperty.prototype.getValue = function (time, result) {
  return this._getValue(time, result);
};

let positionFixedScratch = new Cartesian3();
let cartesian1Scratch = new Cartesian3();
let cartesian2Scratch = new Cartesian3();
let timeInertialScratch = new JulianDate();

/**
 * @private
 */
VelocityVectorProperty.prototype._getValue = function (time, velocityResult, positionResult) {
  let property = this._position;
  if (!defined(this._forwardExtrapolationType) || this._forwardExtrapolationType !== ExtrapolationType.HOLD) {
    if (property.referenceFrame === ReferenceFrame.INERTIAL) {
      this._currentVelocity = this._getValueInertial(time, velocityResult, positionResult);
      return this._currentVelocity;
    }
    this._currentVelocity = this._getValueFixed(time, velocityResult, positionResult);
    return this._currentVelocity;
  }
  let velocity;
  if (property.referenceFrame === ReferenceFrame.INERTIAL) {
    let pos1 = property.getInertialValue(time, cartesian1Scratch);
    let pos2 = property.getInertialValue(JulianDate.addSeconds(time, step, timeInertialScratch), cartesian2Scratch);
    if (Cartesian3.equals(pos1, this._currentCartesian) || Cartesian3.equals(pos1, pos2)) {
      let position1 = property.getValue(time, positionFixedScratch);
      if (defined(position1) && defined(positionResult)) {
        position1.clone(positionResult);
      }
      return this._currentVelocity;
    }
    this._currentCartesian = pos1?.clone();
    velocity = this._getValueInertial(time, velocityResult, positionResult);
  } else {
    velocity = this._getValueFixed(time, velocityResult, positionResult);
  }

  if (!velocity) {
    let position1 = property.getValue(time, positionFixedScratch);
    if (defined(position1) && defined(positionResult)) {
      position1.clone(positionResult);
    }
    return this._currentVelocity;
  }
  this._currentVelocity = velocity;
  return this._currentVelocity;
};

let position1Scratch = new Cartesian3();
let position2Scratch = new Cartesian3();
let timeScratch = new JulianDate();
let step = 1.0 / 60.0;

/**
 * @private
 */
VelocityVectorProperty.prototype._getValueFixed = function (time, velocityResult, positionResult) {
  //>>includeStart('debug', pragmas.debug);
  if (!defined(time)) {
    // throw new DeveloperError('time is required');
    console.warn('time is required');
    return;
  }
  //>>includeEnd('debug');

  if (!defined(velocityResult)) {
    velocityResult = new Cartesian3();
  }

  let property = this._position;
  if (Property.isConstant(property)) {
    return this._normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
  }

  let position1 = property.getValue(time, position1Scratch);
  let position2 = property.getValue(JulianDate.addSeconds(time, step, timeScratch), position2Scratch);

  //If we don't have a position for now, return undefined.
  if (!defined(position1)) {
    return undefined;
  }

  //If we don't have a position for now + step, see if we have a position for now - step.
  if (!defined(position2)) {
    position2 = position1;
    position1 = property.getValue(JulianDate.addSeconds(time, -step, timeScratch), position2Scratch);

    if (!defined(position1)) {
      return undefined;
    }
  }

  if (Cartesian3.equals(position1, position2)) {
    return this._normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
  }

  if (defined(positionResult)) {
    position1.clone(positionResult);
  }

  let velocity = Cartesian3.subtract(position2, position1, velocityResult);
  if (this._normalize) {
    return Cartesian3.normalize(velocity, velocityResult);
  }

  return Cartesian3.divideByScalar(velocity, step, velocityResult);
};

let iPosition1Scratch = new Cartesian3();
let iPosition2Scratch = new Cartesian3();
let iTimeScratch = new JulianDate();
/**
 * @private
 */
VelocityVectorProperty.prototype._getValueInertial = function (time, velocityResult, positionResult) {
  //>>includeStart('debug', pragmas.debug);
  if (!defined(time)) {
    throw new DeveloperError('time is required');
  }
  //>>includeEnd('debug');

  if (!defined(velocityResult)) {
    velocityResult = new Cartesian3();
  }

  let property = this._position;
  if (Property.isConstant(property)) {
    return this._normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
  }

  let position1 = property.getValue(time, iPosition1Scratch);
  let position2 = property.getInertialValue(JulianDate.addSeconds(time, step, iTimeScratch), iPosition2Scratch);
  position2 = property.getOrbitFixedValue(time, position2);

  //If we don't have a position for now, return undefined.
  if (!defined(position1)) {
    return undefined;
  }

  //If we don't have a position for now + step, see if we have a position for now - step.
  if (!defined(position2)) {
    position2 = position1;
    position1 = property.getInertialValue(JulianDate.addSeconds(time, -step, iTimeScratch), iPosition2Scratch);
    position1 = property.getOrbitFixedValue(time, position1);

    if (!defined(position1)) {
      return undefined;
    }
  }

  if (Cartesian3.equals(position1, position2)) {
    return this._normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
  }

  if (defined(positionResult)) {
    position1.clone(positionResult);
  }

  let velocity = Cartesian3.subtract(position2, position1, velocityResult);
  if (this._normalize) {
    return Cartesian3.normalize(velocity, velocityResult);
  }

  return Cartesian3.divideByScalar(velocity, step, velocityResult);
};

VelocityVectorProperty.prototype.equals = function (other) {
  return (
    this === other || //
    (other instanceof VelocityVectorProperty && Property.equals(this._position, other._position))
  );
};

export default VelocityVectorProperty;
