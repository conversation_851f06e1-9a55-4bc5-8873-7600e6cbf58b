/**
 * @Author: 宋计民
 * @Date: 2023/10/16 13:41
 * @Version: 1.0
 * @Content: PolylineMilitaryMaterial.ts
 */
import { Cartesian2, Color, Material } from 'cesium';

export const POLYLINE_MILITARY_SOURCE = `
  uniform vec4 color;
  uniform sampler2D imageHead;
  uniform sampler2D image;
  uniform vec2 repeat;

  czm_material czm_getMaterial(czm_materialInput materialInput)
  {
      czm_material material = czm_getDefaultMaterial(materialInput);
      vec2 st = materialInput.st * repeat;
      vec4 colorImageHead = texture2D(imageHead, fract(st));
      vec4 colorImage = texture2D(image, fract(st));
      // support negative value
      if(repeat.x > 0.0){
        if(abs(st.s) < abs(repeat.x) - 1.0){
            material.alpha = colorImage.a * color.a;
        } else {
            material.alpha = colorImageHead.a * color.a;
        }
      } else {
        if(abs(st.s) > abs(repeat.x) - 1.0){
            material.alpha = colorImage.a * color.a;
        } else {
            material.alpha = colorImageHead.a * color.a;
        }
      }
      material.diffuse = color.rgb * material.alpha;
      return material;
}`;

export const PolylineMilitaryMaterial = new Material({
  fabric: {
    type: 'PolylineMilitary',
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      imageHead: Material.DefaultImageId,
      image: Material.DefaultImageId,
      repeat: new Cartesian2(1, 1)
    },
    source: POLYLINE_MILITARY_SOURCE
  },
  translucent: function (material) {
    return true;
  }
});
