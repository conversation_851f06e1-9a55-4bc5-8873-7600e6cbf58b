/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { Gantt, GanttDataType, GanttMode, rowFormatLabelType } from '../graphic';
import { ShallowRef } from 'vue';

// 监听props部分数据变化，对甘特图进行重新渲染
export const useWatchProps = (
  graphicMap: ShallowRef<Gantt | undefined>,
  props: {
    data: GanttDataType[];
    rowHeight: number;
    rowGap: number;
    mode: GanttMode;
    splitLine: boolean;
    leftRowTextFormat: rowFormatLabelType;
    taskTextFormat: rowFormatLabelType;
    connectLine: boolean;
    minTime?: number | undefined;
  }
) => {
  watch(
    () => props.connectLine,
    (newValue) => {
      console.log(11);
      const graphic = toValue(graphicMap)!;
      graphic.options.connectLine = newValue;
      graphic.render();
    }
  );

  return {};
};
