<!--
* @Author: 宋计民
* @Date: 2024/5/8
* @Version: 1.0
* @Content: gantt-slider.vue
-->
<template>
  <!--选中区域-->
  <div
    class="slider-select-area"
    :style="{
      left: `${startTimeLeft < endTimeLeft ? startTimeLeft : endTimeLeft}%`,
      width: `${Math.abs(endTimeLeft - startTimeLeft)}%`
    }"
    @mousedown="mouseDownArea"
    @wheel.ctrl.prevent="zoomArea"
  ></div>

  <div class="slider" :style="{ left: `${startTimeLeft}%` }" @mousedown="startMouseDown" @wheel="zoomArea"></div>

  <div class="slider" :style="{ left: `${endTimeLeft}%` }" @mousedown="endMouseDown" @wheel="zoomArea"></div>

  <div class="slider-current" :style="{ left: `${currentTimeLeft}%` }" @mousedown="currentMouseDown">
    <!--    <span class="cesium-timeline-icon16"></span>-->
  </div>
</template>

<script setup lang="ts">
import { useSlider, useSliderArea } from './hooks/slider-hook.ts';

defineOptions({
  name: 'GanttSlider'
});
const props = defineProps({
  parentRef: {
    type: Object as PropType<HTMLDivElement>,
    default: () => {}
  },
  currentStartTime: {
    type: Number,
    default: 0
  },
  currentEndTime: {
    type: Number,
    default: 0
  }
});

const emits = defineEmits(['change']);

const selectStartTime = defineModel<number>('selectStartTime', { required: true });
const selectEndTime = defineModel<number>('selectEndTime', { required: true });
const currentTime = defineModel<number>('currentTime', { required: true });

const change = () => {
  emits('change');
};

// 暂时不用，滑块超出另一个滑块时，跟随滑动
// const rectifyData = () => {
//   if (selectStartTime.value > selectEndTime.value) {
//     const temp = selectStartTime.value;
//     selectStartTime.value = selectEndTime.value;
//     selectEndTime.value = temp;
//   }
// };

// 当前时间
const { sliderLeft: currentTimeLeft, mouseDown: currentMouseDown } = useSlider(props, currentTime, change);

// 开始
const {
  sliderLeft: startTimeLeft,
  mouseDown: startMouseDown,
  setCurrentTime: setCurrentStartTime
} = useSlider(props, selectStartTime, change, exceedEnd);
// 结束
const { sliderLeft: endTimeLeft, mouseDown: endMouseDown, setCurrentTime: setCurrentEndTime } = useSlider(props, selectEndTime, change, exceedStart);
// 区域
const { mouseDownArea } = useSliderArea(
  props,
  {
    startTimeLeft,
    endTimeLeft,
    setCurrentStartTime,
    setCurrentEndTime
  },
  change
);

function exceedEnd(startLeft: number) {
  return startLeft <= endTimeLeft.value ? startLeft : endTimeLeft.value;
}
function exceedStart(endLeft: number) {
  return endLeft >= startTimeLeft.value ? endLeft : startTimeLeft.value;
}

const zoomArea = (e: WheelEvent) => {
  const dy = e.deltaY;
  const step = 0.5;

  // 缩小
  if (dy > 0) {
    let startLeft = startTimeLeft.value + step;
    let endLeft = endTimeLeft.value - step;
    if (startLeft >= endLeft) {
      const center = (startLeft + endLeft) / 2;
      startLeft = center;
      endLeft = center;
    }
    startTimeLeft.value = startLeft;
    endTimeLeft.value = endLeft;
    setCurrentStartTime();
    setCurrentEndTime();
    change();
  }
  // 放大
  if (dy < 0) {
    let startLeft = startTimeLeft.value - step;
    let endLeft = endTimeLeft.value + step;

    if (startLeft <= 0) {
      startLeft = 0;
    }
    if (endLeft >= 100) {
      endLeft = 100;
    }

    startTimeLeft.value = startLeft;
    endTimeLeft.value = endLeft;
    setCurrentStartTime();
    setCurrentEndTime();
    change();
  }
};
</script>

<style scoped lang="less">
.slider {
  position: absolute;
  left: 0px;
  bottom: 0px;
  transform: translateX(-50%);
  width: 10px;
  height: 100%;
  background-color: rgba(0, 122, 253, 0.9);
  border: 1px solid rgba(0, 122, 253, 1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.slider-current {
  position: absolute;
  left: 0px;
  bottom: 0px;
  transform: translateX(-50%);
  width: 4px;
  height: 100%;
  background-color: rgba(0, 122, 253, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: red;
  //.cesium-timeline-icon16 {
  //  left: 50%;
  //  bottom: 3px;
  //  transform: translateX(-50%);
  //}
}

.slider-select-area {
  position: absolute;
  left: 0px;
  bottom: 0px;
  //height: 30px;
  height: 100%;
  background-color: rgba(0, 122, 253, 0.3);
}
</style>
