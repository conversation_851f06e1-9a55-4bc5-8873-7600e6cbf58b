<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-22 11:10
* @Version : 1.0
* @Content : breastplate.vue
-->
<template>
  <div ref="breastplateRef" class="sim-breastplate">
    <div class="sim-breastplate-header">
      <slot name="header">
        <span>{{ data.name }}</span>
        <span>
          <n-dropdown :options="levelShowEnum">
            <span>{{ selectedLevel.label }}</span>
          </n-dropdown>
          <sim-icon name="guanbi" @click="handleClose" />
        </span>
      </slot>
    </div>
    <div class="sim-breastplate-content">
      <slot />
    </div>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  name: 'SimBreastplate'
});
</script>

<script lang="ts" setup>
import { PropType } from 'vue';
import { getEntitySource, onPreUpdate } from 'isim-cesium';
import { Cartesian2 } from 'cesium';

const emits = defineEmits(['close']);

const props = defineProps({
  data: {
    required: true,
    type: Object as PropType<{ id: string; source: string; name: string; level?: number }>
  }
});

const handleClose = () => {
  emits('close');
};
const selectedLevel = ref({
  label: 'Ⅰ',
  level: 1400
});

const levelShowEnum = ['Ⅰ', 'Ⅱ', 'Ⅲ', 'Ⅳ', 'Ⅴ'].reverse().map((item, dex) => ({
  label: item,
  key: item,
  props: {
    onClick() {
      selectedLevel.value = {
        label: item,
        level: 1000 + 100 * dex
      };
    }
  }
}));

const ZIndexValue = computed(() => selectedLevel.value.level);

const breastplateRef = ref<HTMLDivElement>();

let pos: Cartesian2;
const close = onPreUpdate((scene, time) => {
  const { source: sourceName, id } = props.data;
  const source = getEntitySource(sourceName);
  const en = source?.entities.getById(id);
  if (!en?.position) {
    handleClose();
    return;
  }

  const _pos = scene.cartesianToCanvasCoordinates(en.position.getValue(time));
  if (pos && Cartesian2.equals(pos, _pos)) {
    return;
  }
  pos = _pos;
  breastplateRef.value?.style.setProperty('--left-pos', pos.x + 'px');
  breastplateRef.value?.style.setProperty('--top-pos', pos.y + 'px');
});

onUnmounted(() => {
  close?.();
});
</script>

<style scoped lang="less">
.sim-breastplate {
  --left-pos: 0px;
  --top-pos: 0px;
  --border-style: 1px solid var(--primary-color-3);
  --head-height: 30px;
  position: absolute;
  z-index: v-bind('ZIndexValue');
  min-width: 200px;
  min-height: 100px;
  max-width: 400px;
  max-height: 400px;
  left: 0;
  top: 0;
  background-color: var(--model-bg-color);
  transform: translate(calc(var(--left-pos) + 10px), calc(var(--top-pos) - 100% - 10px));
  border: var(--border-style);
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--head-height);
    width: 100%;
    padding: 0 4px;
    box-sizing: border-box;
    border-bottom: var(--border-style);
  }
  &-content {
    width: 100%;
    padding: 4px;
    box-sizing: border-box;
    height: calc(100% - var(--head-height));
    overflow-y: auto;
  }
}
</style>
