define(["exports","./Matrix2-860854d4","./RuntimeError-1349fdaf","./ComponentDatatype-8f55628c","./when-4bbc8319","./EllipsoidRhumbLine-d4d43630","./GeometryAttribute-af18b102","./WebGLConstants-508b9636"],(function(e,t,n,r,i,a,u,o){"use strict";var x=p,s=p;function p(e,t,n){n=n||2;var r,i,a,u,o,x,s,p=t&&t.length,l=p?t[0]*n:e.length,c=h(e,0,l,n,!0),y=[];if(!c||c.next===c.prev)return y;if(p&&(c=function(e,t,n,r){var i,a,u,o=[];for(i=0,a=t.length;i<a;i++)(u=h(e,t[i]*r,i<a-1?t[i+1]*r:e.length,r,!1))===u.next&&(u.steiner=!0),o.push(b(u));for(o.sort(m),i=0;i<o.length;i++)n=C(o[i],n);return n}(e,t,c,n)),e.length>80*n){r=a=e[0],i=u=e[1];for(var v=n;v<l;v+=n)(o=e[v])<r&&(r=o),(x=e[v+1])<i&&(i=x),o>a&&(a=o),x>u&&(u=x);s=0!==(s=Math.max(a-r,u-i))?32767/s:0}return f(c,y,n,r,i,s,0),y}function h(e,t,n,r,i){var a,u;if(i===W(e,t,n,r)>0)for(a=t;a<n;a+=r)u=G(a,e[a],e[a+1],u);else for(a=n-r;a>=t;a-=r)u=G(a,e[a],e[a+1],u);return u&&S(u,u.next)&&(O(u),u=u.next),u}function l(e,t){if(!e)return e;t||(t=e);var n,r=e;do{if(n=!1,r.steiner||!S(r,r.next)&&0!==Z(r.prev,r,r.next))r=r.next;else{if(O(r),(r=t=r.prev)===r.next)break;n=!0}}while(n||r!==t);return t}function f(e,t,n,r,i,a,u){if(e){!u&&a&&function(e,t,n,r){var i=e;do{0===i.z&&(i.z=w(i.x,i.y,t,n,r)),i.prevZ=i.prev,i.nextZ=i.next,i=i.next}while(i!==e);i.prevZ.nextZ=null,i.prevZ=null,function(e){var t,n,r,i,a,u,o,x,s=1;do{for(n=e,e=null,a=null,u=0;n;){for(u++,r=n,o=0,t=0;t<s&&(o++,r=r.nextZ);t++);for(x=s;o>0||x>0&&r;)0!==o&&(0===x||!r||n.z<=r.z)?(i=n,n=n.nextZ,o--):(i=r,r=r.nextZ,x--),a?a.nextZ=i:e=i,i.prevZ=a,a=i;n=r}a.nextZ=null,s*=2}while(u>1)}(i)}(e,r,i,a);for(var o,x,s=e;e.prev!==e.next;)if(o=e.prev,x=e.next,a?y(e,r,i,a):c(e))t.push(o.i/n|0),t.push(e.i/n|0),t.push(x.i/n|0),O(e),e=x.next,s=x.next;else if((e=x)===s){u?1===u?f(e=v(l(e),t,n),t,n,r,i,a,2):2===u&&d(e,t,n,r,i,a):f(l(e),t,n,r,i,a,1);break}}}function c(e){var t=e.prev,n=e,r=e.next;if(Z(t,n,r)>=0)return!1;for(var i=t.x,a=n.x,u=r.x,o=t.y,x=n.y,s=r.y,p=i<a?i<u?i:u:a<u?a:u,h=o<x?o<s?o:s:x<s?x:s,l=i>a?i>u?i:u:a>u?a:u,f=o>x?o>s?o:s:x>s?x:s,c=r.next;c!==t;){if(c.x>=p&&c.x<=l&&c.y>=h&&c.y<=f&&E(i,o,a,x,u,s,c.x,c.y)&&Z(c.prev,c,c.next)>=0)return!1;c=c.next}return!0}function y(e,t,n,r){var i=e.prev,a=e,u=e.next;if(Z(i,a,u)>=0)return!1;for(var o=i.x,x=a.x,s=u.x,p=i.y,h=a.y,l=u.y,f=o<x?o<s?o:s:x<s?x:s,c=p<h?p<l?p:l:h<l?h:l,y=o>x?o>s?o:s:x>s?x:s,v=p>h?p>l?p:l:h>l?h:l,d=w(f,c,t,n,r),m=w(y,v,t,n,r),C=e.prevZ,g=e.nextZ;C&&C.z>=d&&g&&g.z<=m;){if(C.x>=f&&C.x<=y&&C.y>=c&&C.y<=v&&C!==i&&C!==u&&E(o,p,x,h,s,l,C.x,C.y)&&Z(C.prev,C,C.next)>=0)return!1;if(C=C.prevZ,g.x>=f&&g.x<=y&&g.y>=c&&g.y<=v&&g!==i&&g!==u&&E(o,p,x,h,s,l,g.x,g.y)&&Z(g.prev,g,g.next)>=0)return!1;g=g.nextZ}for(;C&&C.z>=d;){if(C.x>=f&&C.x<=y&&C.y>=c&&C.y<=v&&C!==i&&C!==u&&E(o,p,x,h,s,l,C.x,C.y)&&Z(C.prev,C,C.next)>=0)return!1;C=C.prevZ}for(;g&&g.z<=m;){if(g.x>=f&&g.x<=y&&g.y>=c&&g.y<=v&&g!==i&&g!==u&&E(o,p,x,h,s,l,g.x,g.y)&&Z(g.prev,g,g.next)>=0)return!1;g=g.nextZ}return!0}function v(e,t,n){var r=e;do{var i=r.prev,a=r.next.next;!S(i,a)&&A(i,r,r.next,a)&&L(i,a)&&L(a,i)&&(t.push(i.i/n|0),t.push(r.i/n|0),t.push(a.i/n|0),O(r),O(r.next),r=e=a),r=r.next}while(r!==e);return l(r)}function d(e,t,n,r,i,a){var u=e;do{for(var o=u.next.next;o!==u.prev;){if(u.i!==o.i&&M(u,o)){var x=D(u,o);return u=l(u,u.next),x=l(x,x.next),f(u,t,n,r,i,a,0),void f(x,t,n,r,i,a,0)}o=o.next}u=u.next}while(u!==e)}function m(e,t){return e.x-t.x}function C(e,t){var n=function(e,t){var n,r=t,i=e.x,a=e.y,u=-1/0;do{if(a<=r.y&&a>=r.next.y&&r.next.y!==r.y){var o=r.x+(a-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(o<=i&&o>u&&(u=o,n=r.x<r.next.x?r:r.next,o===i))return n}r=r.next}while(r!==t);if(!n)return null;var x,s=n,p=n.x,h=n.y,l=1/0;r=n;do{i>=r.x&&r.x>=p&&i!==r.x&&E(a<h?i:u,a,p,h,a<h?u:i,a,r.x,r.y)&&(x=Math.abs(a-r.y)/(i-r.x),L(r,e)&&(x<l||x===l&&(r.x>n.x||r.x===n.x&&g(n,r)))&&(n=r,l=x)),r=r.next}while(r!==s);return n}(e,t);if(!n)return t;var r=D(n,e);return l(r,r.next),l(n,n.next)}function g(e,t){return Z(e.prev,e,t.prev)<0&&Z(t.next,e,e.next)<0}function w(e,t,n,r,i){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-n)*i|0)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-r)*i|0)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function b(e){var t=e,n=e;do{(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next}while(t!==e);return n}function E(e,t,n,r,i,a,u,o){return(i-u)*(t-o)>=(e-u)*(a-o)&&(e-u)*(r-o)>=(n-u)*(t-o)&&(n-u)*(a-o)>=(i-u)*(r-o)}function M(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&A(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(e,t)&&(L(e,t)&&L(t,e)&&function(e,t){var n=e,r=!1,i=(e.x+t.x)/2,a=(e.y+t.y)/2;do{n.y>a!=n.next.y>a&&n.next.y!==n.y&&i<(n.next.x-n.x)*(a-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next}while(n!==e);return r}(e,t)&&(Z(e.prev,e,t.prev)||Z(e,t.prev,t))||S(e,t)&&Z(e.prev,e,e.next)>0&&Z(t.prev,t,t.next)>0)}function Z(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function S(e,t){return e.x===t.x&&e.y===t.y}function A(e,t,n,r){var i=R(Z(e,t,n)),a=R(Z(e,t,r)),u=R(Z(n,r,e)),o=R(Z(n,r,t));return i!==a&&u!==o||(!(0!==i||!z(e,n,t))||(!(0!==a||!z(e,r,t))||(!(0!==u||!z(n,e,r))||!(0!==o||!z(n,t,r)))))}function z(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function R(e){return e>0?1:e<0?-1:0}function L(e,t){return Z(e.prev,e,e.next)<0?Z(e,t,e.next)>=0&&Z(e,e.prev,t)>=0:Z(e,t,e.prev)<0||Z(e,e.next,t)<0}function D(e,t){var n=new T(e.i,e.x,e.y),r=new T(t.i,t.x,t.y),i=e.next,a=t.prev;return e.next=t,t.prev=e,n.next=i,i.prev=n,r.next=n,n.prev=r,a.next=r,r.prev=a,r}function G(e,t,n,r){var i=new T(e,t,n);return r?(i.next=r.next,i.prev=r,r.next.prev=i,r.next=i):(i.prev=i,i.next=i),i}function O(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function T(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}function W(e,t,n,r){for(var i=0,a=t,u=n-r;a<n;a+=r)i+=(e[u]-e[a])*(e[a+1]+e[u+1]),u=a;return i}p.deviation=function(e,t,n,r){var i=t&&t.length,a=i?t[0]*n:e.length,u=Math.abs(W(e,0,a,n));if(i)for(var o=0,x=t.length;o<x;o++){var s=t[o]*n,p=o<x-1?t[o+1]*n:e.length;u-=Math.abs(W(e,s,p,n))}var h=0;for(o=0;o<r.length;o+=3){var l=r[o]*n,f=r[o+1]*n,c=r[o+2]*n;h+=Math.abs((e[l]-e[c])*(e[f+1]-e[l+1])-(e[l]-e[f])*(e[c+1]-e[l+1]))}return 0===u&&0===h?0:Math.abs((h-u)/u)},p.flatten=function(e){for(var t=e[0][0].length,n={vertices:[],holes:[],dimensions:t},r=0,i=0;i<e.length;i++){for(var a=0;a<e[i].length;a++)for(var u=0;u<t;u++)n.vertices.push(e[i][a][u]);i>0&&(r+=e[i-1].length,n.holes.push(r))}return n},x.default=s;const P={CLOCKWISE:o.WebGLConstants.CW,COUNTER_CLOCKWISE:o.WebGLConstants.CCW,validate:function(e){return e===P.CLOCKWISE||e===P.COUNTER_CLOCKWISE}};var I=Object.freeze(P);const B=new t.Cartesian3,N=new t.Cartesian3,U={computeArea2D:function(e){const t=e.length;let n=0;for(let r=t-1,i=0;i<t;r=i++){const t=e[r],a=e[i];n+=t.x*a.y-a.x*t.y}return.5*n},computeWindingOrder2D:function(e){return U.computeArea2D(e)>0?I.COUNTER_CLOCKWISE:I.CLOCKWISE},triangulate:function(e,n){const r=t.Cartesian2.packArray(e);return x(r,n,2)}},_=new t.Cartesian3,K=new t.Cartesian3,V=new t.Cartesian3,k=new t.Cartesian3,q=new t.Cartesian3,F=new t.Cartesian3,j=new t.Cartesian3;U.computeSubdivision=function(e,n,a,o){o=i.defaultValue(o,r.CesiumMath.RADIANS_PER_DEGREE);const x=a.slice(0);let s;const p=n.length,h=new Array(3*p);let l=0;for(s=0;s<p;s++){const e=n[s];h[l++]=e.x,h[l++]=e.y,h[l++]=e.z}const f=[],c={},y=e.maximumRadius,v=r.CesiumMath.chordLength(o,y),d=v*v;for(;x.length>0;){const e=x.pop(),n=x.pop(),r=x.pop(),a=t.Cartesian3.fromArray(h,3*r,_),u=t.Cartesian3.fromArray(h,3*n,K),o=t.Cartesian3.fromArray(h,3*e,V),p=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(a,k),y,k),l=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(u,q),y,q),v=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(o,F),y,F),m=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(p,l,j)),C=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(l,v,j)),g=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(v,p,j)),w=Math.max(m,C,g);let b,E;w>d?m===w?(b=Math.min(r,n)+" "+Math.max(r,n),s=c[b],i.defined(s)||(E=t.Cartesian3.add(a,u,j),t.Cartesian3.multiplyByScalar(E,.5,E),h.push(E.x,E.y,E.z),s=h.length/3-1,c[b]=s),x.push(r,s,e),x.push(s,n,e)):C===w?(b=Math.min(n,e)+" "+Math.max(n,e),s=c[b],i.defined(s)||(E=t.Cartesian3.add(u,o,j),t.Cartesian3.multiplyByScalar(E,.5,E),h.push(E.x,E.y,E.z),s=h.length/3-1,c[b]=s),x.push(n,s,r),x.push(s,e,r)):g===w&&(b=Math.min(e,r)+" "+Math.max(e,r),s=c[b],i.defined(s)||(E=t.Cartesian3.add(o,a,j),t.Cartesian3.multiplyByScalar(E,.5,E),h.push(E.x,E.y,E.z),s=h.length/3-1,c[b]=s),x.push(e,s,n),x.push(s,r,n)):(f.push(r),f.push(n),f.push(e))}return new u.Geometry({attributes:{position:new u.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:h})},indices:f,primitiveType:u.PrimitiveType.TRIANGLES})};const H=new t.Cartographic,J=new t.Cartographic,Q=new t.Cartographic,X=new t.Cartographic;U.computeRhumbLineSubdivision=function(e,n,o,x){x=i.defaultValue(x,r.CesiumMath.RADIANS_PER_DEGREE);const s=o.slice(0);let p;const h=n.length,l=new Array(3*h);let f=0;for(p=0;p<h;p++){const e=n[p];l[f++]=e.x,l[f++]=e.y,l[f++]=e.z}const c=[],y={},v=e.maximumRadius,d=r.CesiumMath.chordLength(x,v),m=new a.EllipsoidRhumbLine(void 0,void 0,e),C=new a.EllipsoidRhumbLine(void 0,void 0,e),g=new a.EllipsoidRhumbLine(void 0,void 0,e);for(;s.length>0;){const n=s.pop(),r=s.pop(),a=s.pop(),u=t.Cartesian3.fromArray(l,3*a,_),o=t.Cartesian3.fromArray(l,3*r,K),x=t.Cartesian3.fromArray(l,3*n,V),h=e.cartesianToCartographic(u,H),f=e.cartesianToCartographic(o,J),v=e.cartesianToCartographic(x,Q);m.setEndPoints(h,f);const w=m.surfaceDistance;C.setEndPoints(f,v);const b=C.surfaceDistance;g.setEndPoints(v,h);const E=g.surfaceDistance,M=Math.max(w,b,E);let Z,S,A,z;M>d?w===M?(Z=Math.min(a,r)+" "+Math.max(a,r),p=y[Z],i.defined(p)||(S=m.interpolateUsingFraction(.5,X),A=.5*(h.height+f.height),z=t.Cartesian3.fromRadians(S.longitude,S.latitude,A,e,j),l.push(z.x,z.y,z.z),p=l.length/3-1,y[Z]=p),s.push(a,p,n),s.push(p,r,n)):b===M?(Z=Math.min(r,n)+" "+Math.max(r,n),p=y[Z],i.defined(p)||(S=C.interpolateUsingFraction(.5,X),A=.5*(f.height+v.height),z=t.Cartesian3.fromRadians(S.longitude,S.latitude,A,e,j),l.push(z.x,z.y,z.z),p=l.length/3-1,y[Z]=p),s.push(r,p,a),s.push(p,n,a)):E===M&&(Z=Math.min(n,a)+" "+Math.max(n,a),p=y[Z],i.defined(p)||(S=g.interpolateUsingFraction(.5,X),A=.5*(v.height+h.height),z=t.Cartesian3.fromRadians(S.longitude,S.latitude,A,e,j),l.push(z.x,z.y,z.z),p=l.length/3-1,y[Z]=p),s.push(n,p,r),s.push(p,a,r)):(c.push(a),c.push(r),c.push(n))}return new u.Geometry({attributes:{position:new u.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:l})},indices:c,primitiveType:u.PrimitiveType.TRIANGLES})},U.scaleToGeodeticHeight=function(e,n,r,a){r=i.defaultValue(r,t.Ellipsoid.WGS84);let u=B,o=N;if(n=i.defaultValue(n,0),a=i.defaultValue(a,!0),i.defined(e)){const i=e.length;for(let x=0;x<i;x+=3)t.Cartesian3.fromArray(e,x,o),a&&(o=r.scaleToGeodeticSurface(o,o)),0!==n&&(u=r.geodeticSurfaceNormal(o,u),t.Cartesian3.multiplyByScalar(u,n,u),t.Cartesian3.add(o,u,o)),e[x]=o.x,e[x+1]=o.y,e[x+2]=o.z}return e},e.PolygonPipeline=U,e.WindingOrder=I}));
