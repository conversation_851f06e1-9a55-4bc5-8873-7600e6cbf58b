varying vec3 v_positionEC;
varying vec3 v_normalEC;
uniform vec4 color;
uniform vec4 czm_pickColor;
void main()
{
    vec3 positionToEyeEC = -v_positionEC;
    vec3 normalEC = normalize(v_normalEC);
    czm_materialInput materialInput;
    materialInput.normalEC = normalEC;
    materialInput.positionToEyeEC = positionToEyeEC;
    czm_material material = czm_getDefaultMaterial(materialInput);
    // material.diffuse=vec3(1.0,0.,0.);
    material.diffuse = vec3(color.r, color.g, color.b);
    material.alpha = color.a;
    gl_FragColor = czm_phong(normalize(positionToEyeEC), material, czm_lightDirectionEC);
}
