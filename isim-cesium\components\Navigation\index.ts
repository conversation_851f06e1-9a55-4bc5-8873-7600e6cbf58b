/**
 * @Author: 宋计民
 * @Date: 2023/4/25
 * @Version: 1.0
 * @Content:
 */
import { getViewer, setCameraScale } from 'isim-cesium';
import type { App } from 'vue';
import CesiumNavigation from 'cesium-navigation-es6';
import './navigation.less';
import DistanceSelect from './distance-select/distance-select.vue';

export interface NavigationOptionType {
  duration?: number;
  enableCompass?: boolean;
  enableZoomControls?: boolean;
  enableDistanceLegend?: boolean;
  enableCompassOuterRing?: boolean;
  resetTooltip?: string;
  zoomInTooltip?: string;
  zoomOutTooltip?: string;
}

const SimNavigationComponent = defineComponent({
  name: 'SimNavigation',
  props: {
    duration: { type: Number, default: 3 },
    enableCompass: {
      type: Boolean,
      default: true
    },
    enableZoomControls: {
      type: Boolean,
      default: true
    },
    enableDistanceLegend: {
      type: Boolean,
      default: false
    },
    enableCompassOuterRing: {
      type: Boolean,
      default: true
    },
    resetTooltip: {
      type: String,
      default: '重置'
    },
    zoomInTooltip: {
      type: String,
      default: '放大'
    },
    zoomOutTooltip: {
      type: String,
      default: '缩小'
    }
  },
  setup(props) {
    const navigation = shallowRef();
    /**
     * 设置陆判是否显示
     * @param isShow
     */
    const setNavigationCompass = (isShow: boolean) => {
      const nav = navigation.value;
      if (!nav) {
        console.warn('请先开启 navigation 功能');
        return;
      }
      nav.navigationViewModel.showCompass = isShow;
    };

    watch(
      () => props.enableCompass,
      (val) => {
        setNavigationCompass(val);
      }
    );
    /**
     * 设置比例尺是否显示
     * @param isShow
     */
    const setNavigationDistance = (isShow: boolean) => {
      const nav = navigation.value;
      if (!nav) {
        console.error('请先开启 navigation 功能');
        return;
      }
      nav.distanceLegendViewModel.enableDistanceLegend = isShow;
    };

    const distanceState = reactive({
      show: false,
      x: 0,
      y: 0
    });

    watch(
      () => props.enableDistanceLegend,
      (val) => {
        setNavigationDistance(val);
      }
    );

    nextTick().then(() => {
      const viewer = getViewer();
      navigation.value = new CesiumNavigation(viewer, props);
      const distanceDom = document.querySelector('.distance-legend') as HTMLDivElement;
      if (!distanceDom) {
        return;
      }
      const enterEvent = () => {
        const rect = distanceDom.getBoundingClientRect();
        distanceState.x = rect.x + rect.width / 2;
        distanceState.y = rect.y;
        distanceState.show = true;
      };
      distanceDom.addEventListener('mouseenter', enterEvent);
    });

    return () =>
      h(DistanceSelect, {
        show: distanceState.show,
        x: distanceState.x,
        y: distanceState.y,
        'onUpdate:show': (value: boolean) => (distanceState.show = value),
        onSelect: (value: number) => {
          distanceState.show = false;
          setCameraScale(value);
        }
      });
  }
});

export const SimNavigation = {
  install(Vue: App) {
    Vue.component('SimNavigation', SimNavigationComponent);
  }
};
