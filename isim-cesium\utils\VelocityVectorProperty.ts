/**
 * @Author: 宋计民
 * @Date: 2023-09-27 11:07
 * @Version: 1.0
 * @Content: VelocityVectorProperty.ts
 */
import { Cartesian3, defined, DeveloperError, Event, ExtrapolationType, JulianDate, PositionProperty, ReferenceFrame } from 'cesium';

const iPosition1Scratch = new Cartesian3();
const iPosition2Scratch = new Cartesian3();
const iTimeScratch = new JulianDate();

const positionFixedScratch = new Cartesian3();
const cartesian1Scratch = new Cartesian3();
const cartesian2Scratch = new Cartesian3();
const timeInertialScratch = new JulianDate();

const STEP = 1.0 / 60.0;
export class VelocityVectorProperty {
  private _position?: PositionProperty;
  private _subscription?: Event.RemoveCallback;
  private readonly _definitionChanged: Event;
  private _normalize: boolean;
  private _currentCartesian: undefined | Cartesian3;
  private _currentVelocity: undefined | Cartesian3;
  private _forwardExtrapolationType: ExtrapolationType;
  constructor(position?: PositionProperty, normalize = true) {
    this._position = position;
    this._subscription = undefined;
    this._definitionChanged = new Event();
    this._normalize = normalize;
    this._currentCartesian = undefined;
    this._currentVelocity = undefined;
    this._forwardExtrapolationType = ExtrapolationType.HOLD;
  }
  get subscription() {
    return this._subscription;
  }

  set subscription(value) {
    this._subscription = value;
  }
  get definitionChanged() {
    return this._definitionChanged;
  }
  get position() {
    return this._position;
  }
  set position(value: PositionProperty | undefined) {
    const oldValue = this._position;
    if (oldValue !== value) {
      if (defined(oldValue)) {
        this._subscription?.();
      }

      this._position = value;

      if (defined(value)) {
        this._subscription = value?.definitionChanged.addEventListener(() => {
          this._definitionChanged.raiseEvent(this);
        }, this);
      }

      this._definitionChanged.raiseEvent(this);
    }
  }
  set normalize(value: boolean) {
    if (this._normalize === value) {
      return;
    }

    this._normalize = value;
    this._definitionChanged.raiseEvent(this);
  }
  get normalize() {
    return this._normalize;
  }
  get forwardExtrapolationType() {
    return this._forwardExtrapolationType;
  }
  set forwardExtrapolationType(value) {
    this._forwardExtrapolationType = value;
  }
  getValue(time: JulianDate, velocityResult: Cartesian3, positionResult: Cartesian3) {
    const property = this._position;
    if (!property) {
      return undefined;
    }
    if (!defined(this._forwardExtrapolationType) || this._forwardExtrapolationType !== ExtrapolationType.HOLD) {
      if (property.referenceFrame === ReferenceFrame.INERTIAL) {
        this._currentVelocity = this._getValueInertial(time, velocityResult, positionResult);
        return this._currentVelocity;
      }
      this._currentVelocity = this._getValueFixed(time, velocityResult, positionResult);
      return this._currentVelocity;
    }
    let velocity;
    if (property.referenceFrame === ReferenceFrame.INERTIAL) {
      const pos1 = property.getInertialValue(time);
      const pos2 = property.getInertialValue(JulianDate.addSeconds(time, STEP, timeInertialScratch));
      if (Cartesian3.equals(pos1, this._currentCartesian) || Cartesian3.equals(pos1, pos2)) {
        const position1 = property.getValue(time);
        if (defined(position1) && defined(positionResult)) {
          position1.clone(positionResult);
        }
        return this._currentVelocity;
      }
      this._currentCartesian = pos1?.clone();
      velocity = this._getValueInertial(time, velocityResult, positionResult);
    } else {
      velocity = this._getValueFixed(time, velocityResult, positionResult);
    }

    if (!velocity) {
      const position1 = property.getValue(time);
      if (defined(position1) && defined(positionResult)) {
        position1.clone(positionResult);
      }
      return this._currentVelocity;
    }
    this._currentVelocity = velocity;
    return this._currentVelocity;
  }
  _getValueInertial(time: JulianDate, velocityResult: Cartesian3, positionResult: Cartesian3) {
    //>>includeStart('debug', pragmas.debug);
    if (!defined(time)) {
      throw new DeveloperError('time is required');
    }
    //>>includeEnd('debug');

    if (!defined(velocityResult)) {
      velocityResult = new Cartesian3();
    }

    const property = this._position!;
    if (property.isConstant) {
      return this._normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
    }

    let position1 = property.getValue(time, iPosition1Scratch);
    let position2 = property.getInertialValue(JulianDate.addSeconds(time, STEP, iTimeScratch), iPosition2Scratch);
    position2 = property.getOrbitFixedValue(time, position2);

    //If we don't have a position for now, return undefined.
    if (!defined(position1)) {
      return undefined;
    }

    //If we don't have a position for now + step, see if we have a position for now - step.
    if (!defined(position2)) {
      position2 = position1;
      position1 = property.getInertialValue(JulianDate.addSeconds(time, -STEP, iTimeScratch), iPosition2Scratch);
      position1 = property.getOrbitFixedValue(time, position1);

      if (!defined(position1)) {
        return undefined;
      }
    }

    if (Cartesian3.equals(position1, position2)) {
      return this._normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
    }

    if (defined(positionResult)) {
      position1.clone(positionResult);
    }

    const velocity = Cartesian3.subtract(position2, position1, velocityResult);
    if (this._normalize) {
      return Cartesian3.normalize(velocity, velocityResult);
    }

    return Cartesian3.divideByScalar(velocity, STEP, velocityResult);
  }
  _getValueFixed(time: JulianDate, velocityResult: Cartesian3, positionResult: Cartesian3) {
    //>>includeStart('debug', pragmas.debug);
    if (!defined(time)) {
      // throw new DeveloperError('time is required');
      console.warn('time is required');
      return;
    }
    //>>includeEnd('debug');
    if (!defined(velocityResult)) {
      velocityResult = new Cartesian3();
    }
    const property = this._position!;
    if (property?.isConstant) {
      return this._normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
    }
    let position1 = property.getValue(time);
    let position2 = property.getValue(JulianDate.addSeconds(time, STEP, new JulianDate()));

    //If we don't have a position for now, return undefined.
    if (!defined(position1)) {
      return undefined;
    }

    //If we don't have a position for now + step, see if we have a position for now - step.
    if (!defined(position2)) {
      position2 = position1;
      position1 = property.getValue(JulianDate.addSeconds(time, -STEP, new JulianDate()));

      if (!defined(position1)) {
        return undefined;
      }
    }
    if (Cartesian3.equals(position1, position2)) {
      return this._normalize ? undefined : Cartesian3.clone(Cartesian3.ZERO, velocityResult);
    }

    if (defined(positionResult)) {
      position1.clone(positionResult);
    }

    const velocity = Cartesian3.subtract(position2, position1, velocityResult);
    if (this._normalize) {
      return Cartesian3.normalize(velocity, velocityResult);
    }

    return Cartesian3.divideByScalar(velocity, STEP, velocityResult);
  }

  equals(other: VelocityVectorProperty) {
    return this === other || (other instanceof VelocityVectorProperty && this._position?.equals(other._position));
  }
}
