<!--
* @Author: Geng<PERSON>inglang
* @Date: 2023/4/11
* @Version: 1.0
* @Content: InputFilter
-->
<template>
  <div class="sim-input-filter">
    <el-form v-show="!isShowFilter && !hideInput" ref="searchForm" :model="queryParams" :rules="rulesForm" @submit.prevent>
      <el-form-item prop="filterString">
        <sim-input
          v-model="queryParams.filterString"
          :placeholder="placeholder"
          :suffix-icon="Search"
          clearable
          @keydown.enter="debounce(queryParams.filterString)"
        />
      </el-form-item>
    </el-form>
    <div v-if="!hideFilter" :class="['filter-btn', { 'filter-btn--active': isShowFilter }]" @click="handleFilter">
      <el-link type="default" :underline="false" title="高级搜索">
        <n-icon>
          <FunnelOutline />
        </n-icon>
      </el-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, PropType } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { FunnelOutline } from '@vicons/ionicons5';
import { useDebounceFn } from '@vueuse/core';

defineOptions({
  name: 'SimInputFilter'
});
const props = defineProps({
  prop: {
    type: String as PropType<string | undefined>,
    required: true
  },
  hideFilter: {
    type: Boolean,
    default: false
  },
  hideInput: {
    type: Boolean,
    default: false
  },
  lengthLimit: {
    type: Number,
    default: 50
  },
  placeholder: {
    type: String,
    default: '请输入关键字'
  }
});

const queryParams = reactive({
  filterString: props.prop
});

watch(
  () => props.prop,
  (newVal) => {
    queryParams.filterString = newVal;
  }
);
// const filterString = ref(props.prop);
const emit = defineEmits(['change', 'handleSearch', 'update:prop']);

const rulesForm = {
  filterString: [
    {
      // pattern: platformNameRegExp,
      message: '不得包含特殊字符',
      trigger: 'blur'
    },
    {
      max: props.lengthLimit,
      message: `超过限制长度${props.lengthLimit}个字符`,
      trigger: 'blur'
    }
  ]
};
const isShowFilter = ref(false);

const searchForm = ref();
// 防抖
const debounce = useDebounceFn((val: string | undefined) => {
  emit('handleSearch', ref(val));
}, 500);
watch(
  () => queryParams.filterString,
  (val) => {
    emit('update:prop', val);
    searchForm.value.validate((valid: boolean) => {
      if (valid) {
        debounce(val);
      }
    });
  }
);

const handleFilter = () => {
  isShowFilter.value = !isShowFilter.value;
  emit('change', isShowFilter.value);
};

defineExpose({
  handleFilter
});
</script>

<style lang="less" scoped>
.sim-input-filter {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .el-form-item {
    margin: 0;
    padding: 0;
    .sim-input {
      width: 240px;
      height: 32px;
      :deep(.el-input) {
        vertical-align: baseline;
      }
    }
  }

  .filter-btn {
    margin-left: 3px;
    width: 40px;
    height: 28px;
    box-sizing: border-box;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    cursor: pointer;
    &--active {
      background: radial-gradient(rgba(var(--primary-color-val), 0.2980392156862745) 0%, rgba(var(--primary-color-val), 0) 169%),
        linear-gradient(
          180deg,
          rgba(var(--btn-top-color-val), 1) 0%,
          rgba(var(--btn-center-color-val), 1) 73%,
          rgba(var(--btn-bottom-color-val), 1) 100%
        );
      border: 1px solid var(--primary-color);
      box-shadow: inset 0 0 8px var(--primary-color);
    }
  }
}
</style>
