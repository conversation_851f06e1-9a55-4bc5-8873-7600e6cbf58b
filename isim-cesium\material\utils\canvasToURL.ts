/*
 * @Author:成超
 * @Date: 2023-04-03 15:38:03
 * @LastEditTime: 2023-06-28 15:16:22
 * @LastEditors: Please set LastEditors
 */

export function canvasToURL(startColor: string, endColor: string) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;
  const width = 320;
  const height = 80;
  // 创建一个矩形
  ctx.rect(0, 0, width, height);
  ctx.save();
  // 创建线性渐变
  const lg = ctx.createLinearGradient(0, 0, 300, 80);
  // 向线性渐变上添加颜色
  // lg.addColorStop(0.0, endColor || "#fff");
  // // lg.addColorStop(0.5, "#0f0");
  // lg.addColorStop(0.9, startColor || "red");

  lg.addColorStop(0.0, endColor || 'transparent');
  lg.addColorStop(0.9, startColor || 'red');
  // 设置使用线性渐变作为填充颜色
  ctx.fillStyle = lg;
  // 填充一个矩形
  ctx.fillRect(0, 0, width, height);

  const img = canvas.toDataURL();

  return img;
}

export const labelToImage = (text: string, fontSize: number = 50, color = '#fff') => {
  // 名称去掉空格，不然节点多了，叠加在一起会造成渲染失败
  text = text.split(' ').join('');
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;

  ctx.fillStyle = color;
  ctx.font = `${fontSize}px 黑体`;

  const metrics = ctx.measureText(text);
  const textWidth = metrics.width;
  // const height = fontSize;
  const height = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;
  canvas.width = textWidth + fontSize;
  canvas.height = height * 1.1;

  ctx.fillStyle = color;
  ctx.font = `${fontSize}px 黑体`;
  ctx.fillText(text, 0, height);

  const img = canvas.toDataURL();
  return { textWidth, textHeight: height, img };
};
