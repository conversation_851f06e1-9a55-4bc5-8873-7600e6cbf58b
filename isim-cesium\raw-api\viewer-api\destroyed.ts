/**
 * @Author: 宋计民
 * @Date: 2023/10/17 10:26
 * @Version: 1.0
 * @Content: life-cycle-method.ts
 */
import { Viewer } from 'cesium';
import { getViewer, getViewerName } from 'isim-cesium';

const LifeWeakMap: Map<string, Set<Function>> = new Map();

export function executeDestroyed(viewerName = getViewerName()) {
  const viewer = getViewer(viewerName);
  LifeWeakMap.get(viewerName)?.forEach((item) => item(viewer));
  LifeWeakMap.delete(viewerName);
}

export function onViewerDestroyed(fn: (viewer: Viewer) => void, opt?: { viewerName?: string }) {
  const viewerName = getViewerName(opt?.viewerName);
  if (!LifeWeakMap.has(viewerName)) {
    LifeWeakMap.set(viewerName, new Set());
  }
  LifeWeakMap.get(viewerName)!.add(fn);
}
