import SimModelCom from './src/Model.vue';
import DialogCom from './src/Dialog.vue';
import { WithInstallCom } from 'isim-ui';

const SimModel = SimModelCom as WithInstallCom<typeof SimModelCom>;
const SimDialog = DialogCom as WithInstallCom<typeof DialogCom>;

SimModel.install = function (Vue) {
  Vue.component('SimModel', SimModelCom);
  Vue.component('SimDialog', DialogCom);
};

export { SimModel, SimDialog };
