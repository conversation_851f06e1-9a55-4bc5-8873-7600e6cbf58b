/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/16 13:56:29
 * @description Abstract Primitive
 * @version 2.0
 * */

import { createGuid, defined, destroyObject } from 'cesium';

export default class AbstractPrimitive {
  constructor(options) {
    const { show = true, id = createGuid() } = options ?? {};
    this.show = show;
    this._id = defined(id) ? id : createGuid();
    this._primitive = undefined;
  }

  update(_frameState) {}

  equal() {}

  isDestroyed() {
    return false;
  }

  destroy() {
    this._primitive && this._primitive.destroy();
    this._primitive = undefined;
    return destroyObject(this);
  }

  get id() {
    return this._id;
  }

  set id(value) {
    this._id = value;
  }
}
