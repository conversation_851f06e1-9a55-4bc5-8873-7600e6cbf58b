/*
 * @Author: duyt
 * @Date: 2022-04-19 16:11:41
 * @Version: 0.0.2
 * @Content: 拓展和覆写ciesium 的 Timeline
 */

import { JulianDate, Timeline as CesiumTimeline } from 'cesium';
import dayjs from 'dayjs';
/**
 * @param {Object} option tiemline 配置
 * @param { boolean } option.showCurrentTime true 是否添加当前时间显示tooltip
 * @param { boolean } option.setTimeEvent true 开启点击拖拽红线设置时间的事件
 * @param { boolean } option.setMousewheel true 是否开启滚轮事件
 * @param { Number } option.max Infinity 时间轴最大值
 * @param { Number } option.min -Infinity 时间轴最小值
 */
const timelineMouseMode = {
  none: 0,
  scrub: 1,
  slide: 2,
  zoom: 3,
  touchOnly: 4
};
export class Timeline extends CesiumTimeline {
  constructor(el, clock, { showCurrentTime = true, setTimeEvent = true, setMousewheel = false, max = Infinity, min = -Infinity }) {
    super(el, clock);
    this._showCurrentTime = showCurrentTime; // 是否展示tooltip时间
    this._setTimeEvent = setTimeEvent; // 是否添加设置时间事件
    this._setMousewheel = setMousewheel; // 滚轮事件
    this._max = max;
    this._min = min;
    //el.addEventListener('settime', this._onTimelineScrubfunction)
    this._timeBarEle.style.zIndex = 1;
    // 禁用滚轮事件
    this.removeSetMousewheel();
    // 禁用点击和move事件
    this.removeSetTimeEvent();
    // 创建tooltip
    this._createTooltip(clock);

    this._onMouseDown = this._createMouseDownCallback(this);
    this._onMouseMove = this._createMouseMoveCallback(this);

    this.showCurrentTime = this._showCurrentTime;
    this.setTimeEvent = this._setTimeEvent;
    this.setMousewheel = this._setMousewheel;
    this.max = this._max;
    this.min = this._min;
  }
  get showCurrentTime() {
    return this._showCurrentTime;
  }
  set showCurrentTime(status) {
    this._showCurrentTime = status;
    if (status) {
      this.tooltip.style.display = 'flex';
    } else {
      this.tooltip.style.display = 'none';
    }
  }
  get setTimeEvent() {
    return this._setTimeEvent;
  }
  set setTimeEvent(status) {
    this._setTimeEvent = status;
    this.removeSetTimeEvent();
    if (status) {
      this.addSetTimeEvent();
    }
  }
  get setMousewheel() {
    return this._setMousewheel;
  }
  set setMousewheel(status) {
    this._setMousewheel = status;
    this.removeSetMousewheel();
    if (status) {
      this.addSetMousewheel();
    }
  }
  get max() {
    return this._max;
  }
  set max(value) {
    this._max = value;
  }
  get min() {
    return this._min;
  }
  set min(value) {
    this._min = value;
  }
  // 重写事件
  _createMouseDownCallback(timeline) {
    return function (e) {
      if (timeline._mouseMode !== timelineMouseMode.touchOnly) {
        if (e.button === 0) {
          // 左键
          timeline._mouseMode = timelineMouseMode.scrub;
          if (timeline._scrubElement) {
            timeline._scrubElement.style.backgroundPosition = '-16px 0';
          }
          timeline._onMouseMove(e);
        } else {
          timeline._mouseX = e.clientX;
          if (e.button === 2 && timeline._setMousewheel) {
            // 右键
            timeline._mouseMode = timelineMouseMode.slide;
          }
        }
      }
      e.preventDefault();
    };
  }
  _createMouseMoveCallback(timeline) {
    return function (e) {
      let dx;
      if (timeline._mouseMode === timelineMouseMode.scrub) {
        e.preventDefault();
        const x = e.clientX - timeline._topDiv.getBoundingClientRect().left;
        if (x < 0) {
          // timeline._timelineDragLocation = 0;
          // timeline._timelineDrag = -0.01 * timeline._timeBarSecondsSpan;
        } else if (x > timeline._topDiv.clientWidth) {
          // timeline._timelineDragLocation = timeline._topDiv.clientWidth;
          // timeline._timelineDrag = 0.01 * timeline._timeBarSecondsSpan;
        } else {
          timeline._timelineDragLocation = undefined;
          timeline._setTimeBarTime(x, (x * timeline._timeBarSecondsSpan) / timeline._topDiv.clientWidth);
        }
      } else if (timeline._mouseMode === timelineMouseMode.slide) {
        dx = timeline._mouseX - e.clientX;
        timeline._mouseX = e.clientX;
        if (dx !== 0) {
          const dsec = (dx * timeline._timeBarSecondsSpan) / timeline._topDiv.clientWidth;
          const _strat = JulianDate.addSeconds(timeline._startJulian, dsec, new JulianDate());
          const _end = JulianDate.addSeconds(timeline._endJulian, dsec, new JulianDate());
          const startTemp = new Date(JulianDate.toDate(_strat)).getTime();
          const endTemp = new Date(JulianDate.toDate(_end)).getTime();
          const start = startTemp < timeline.min ? timeline.min : startTemp;
          const end = endTemp > timeline.max ? timeline.max : endTemp;
          timeline.zoomTo(JulianDate.fromDate(new Date(start)), JulianDate.fromDate(new Date(end)));
        }
      } else if (timeline._mouseMode === timelineMouseMode.zoom) {
        dx = timeline._mouseX - e.clientX;
        timeline._mouseX = e.clientX;
        if (dx !== 0) {
          timeline.zoomFrom(Math.pow(1.01, dx));
        }
      }
    };
  }
  _setTimeBarTime(xPos, seconds) {
    const juDate = JulianDate.addSeconds(this._startJulian, seconds, new JulianDate());
    const timeTmpe = new Date(JulianDate.toDate(juDate)).getTime();
    if (timeTmpe <= this.max && timeTmpe >= this.min) {
      xPos = Math.round(xPos);
      this._scrubJulian = JulianDate.addSeconds(this._startJulian, seconds, new JulianDate());
      if (this._scrubElement) {
        const scrubX = xPos - 8;
        this._scrubElement.style.left = scrubX.toString() + 'px';
        this._needleEle.style.left = xPos.toString() + 'px';
      }

      const evt = document.createEvent('Event');
      evt.initEvent('settime', true, true);
      evt.clientX = xPos;
      evt.timeSeconds = seconds;
      evt.timeJulian = this._scrubJulian;
      evt.clock = this._clock;
      this._topDiv.dispatchEvent(evt);
    }
  }
  zoomFrom(amount) {
    let centerSec = JulianDate.secondsDifference(this._scrubJulian, this._startJulian);
    if (amount > 1 || centerSec < 0 || centerSec > this._timeBarSecondsSpan) {
      centerSec = this._timeBarSecondsSpan * 0.5;
    } else {
      centerSec += centerSec - this._timeBarSecondsSpan * 0.5;
    }
    const centerSecFlip = this._timeBarSecondsSpan - centerSec;
    const _strat = JulianDate.addSeconds(this._startJulian, centerSec - centerSec * amount, new JulianDate());
    const _end = JulianDate.addSeconds(this._endJulian, centerSecFlip * amount - centerSecFlip, new JulianDate());
    const startTemp = new Date(JulianDate.toDate(_strat)).getTime();
    const endTemp = new Date(JulianDate.toDate(_end)).getTime();
    const start = startTemp < this.min ? this.min : startTemp;
    const end = endTemp > this.max ? this.max : endTemp;
    this.zoomTo(JulianDate.fromDate(new Date(start)), JulianDate.fromDate(new Date(end)));
  }
  _createTooltip(clock) {
    this.tooltip = document.createElement('div');
    this.tooltip.style.cssText = `
          padding:2px 5px;
          position: absolute;
          z-index:99;
          top:0;
          left:50%;
          transform: translateX(-50%);
          background:#000;
          color:#fff;
          line-height:18px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap:nowrap;
          white-space:nowrap`;
    this.tooltip.innerHTML = this.makeLabel(clock.currentTime);
    this._needleEle.appendChild(this.tooltip);
    clock.onTick.addEventListener(this._onTick, this);
  }
  _onTick(clock) {
    this.tooltip.innerHTML = this.makeLabel(clock.currentTime);
  }
  // 开启点击拖拽红线设置时间的事件
  addSetTimeEvent() {
    this._topDiv.addEventListener('mouseup', this._onMouseUp, false);
    this.container.ownerDocument.addEventListener('mousemove', this._onMouseMove, false);
    this._timeBarEle.addEventListener('mousedown', this._onMouseDown, false);
  }
  // 关闭点击拖拽红线设置时间的事件
  removeSetTimeEvent() {
    this._topDiv.removeEventListener('mouseup', this._onMouseUp, false);
    this.container.ownerDocument.removeEventListener('mousemove', this._onMouseMove, false);
    this._timeBarEle.removeEventListener('mousedown', this._onMouseDown, false);
  }
  // 添加滚轮事件
  addSetMousewheel() {
    this._timeBarEle.addEventListener('mousewheel', this._onMouseWheel, false);
  }
  // 禁用滚轮事件
  removeSetMousewheel() {
    this._timeBarEle.removeEventListener('mousewheel', this._onMouseWheel, false);
  }

  makeLabel(time) {
    const jsDate = JulianDate.toDate(time);
    return dayjs(jsDate).format('YYYY/MM/DD HH:mm:ss');
  }

  _onTimelineScrubfunction(e) {
    const clock = e.clock;
    clock.currentTime = e.timeJulian;
    clock.shouldAnimate = false;
    clock.tick();
  }
}
