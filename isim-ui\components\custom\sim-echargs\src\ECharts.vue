<template>
  <div class="sim-echarts" ref="EchartsRef"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { EChartsType } from 'echarts';
defineOptions({
  name: 'SimEcharts'
});

const props = defineProps({
  options: {
    type: Object,
    default: () => ({})
  }
});
const EchartsRef = ref();
const echartsIns = shallowRef<EChartsType>();

watch(
  () => props.options,
  (value) => {
    echartsIns.value.setOption(value);
  }
);
onMounted(() => {
  const myChart = echarts.init(EchartsRef.value);
  echartsIns.value = myChart;
});
</script>

<style scoped lang="less">
.sim-echarts {
  width: 100%;
  height: 100%;
}
</style>
