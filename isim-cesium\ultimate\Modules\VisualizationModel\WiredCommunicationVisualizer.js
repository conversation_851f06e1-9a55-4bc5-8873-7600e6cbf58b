import { Cartesian2, Color, defaultValue, Material } from 'cesium';
import WirelessCommunicationPrimitive from './WirelessCommunicationVisualizer.js';

export default class WiredCommunicationPrimitive extends WirelessCommunicationPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._signerColor = defaultValue(options.signerColor, Color.AQUA);
    this._uniforms = {
      fadeOutColor: this._color.withAlpha(0.5),
      fadeInColor: this._signerColor,
      fadeDirection: {
        x: true,
        y: false
      },
      time: new Cartesian2(0, 0),
      ...options.uniforms
    };

    this._materialType = Material.FadeType;
  }

  update(frameState) {
    super.update(frameState);
    this._uniforms.time.x += 0.01;
    if (this._uniforms.time.x > 1.0) {
      this._uniforms.time.x = 0;
    }
  }

  set color(value) {
    if (Color.equals(this._color, value)) {
      return;
    }
    this._color = value;
    if (this._primitive) {
      this._uniforms.fadeInColor = this._color.withAlpha(0.5);
      this._primitive.uniforms = this._uniforms;
    }
  }

  set signerColor(value) {
    if (Color.equals(this._signerColor, value)) {
      return;
    }
    this._signerColor = value;
    if (this._primitive) {
      this._uniforms.fadeInColor = this._signerColor;
      this._primitive.uniforms = this._uniforms;
    }
  }
}
