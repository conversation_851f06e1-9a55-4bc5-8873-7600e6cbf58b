<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-20 16:58
* @Version : 1.0
* @Content : input-position.vue
-->
<template>
  <el-input v-bind="$attrs" v-model="posValue">
    <template v-if="showPick" #suffix>
      <el-icon @click="handleGetLocation"><MapLocation /></el-icon>
    </template>
  </el-input>
</template>

<script lang="ts" setup>
import { MapLocation } from '@element-plus/icons-vue';
import { onLeftClick, screenPosToLBH, setViewerCursor } from 'isim-cesium';

defineOptions({
  name: 'InputPosition'
});

const emits = defineEmits(['update:modelValue', 'update:longitude', 'update:latitude', 'update:altitude', 'before-pickup', 'after-pickup']);

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  showPick: {
    type: Boolean,
    default: true
  },
  separate: {
    type: String,
    default: ','
  },
  longitude: {
    type: Number,
    default: 0
  },
  latitude: {
    type: Number,
    default: 0
  },
  altitude: {
    type: Number,
    default: 0
  }
});
const posValue = ref(props.modelValue ?? '');

const setModelValue = (value: string) => {
  posValue.value = value;
  emits('update:modelValue', value);
};

const getNumber = (value: string | number) => {
  const num = Number(value);
  return isNaN(num) ? 0 : num;
};

const setLongitude = (value: string) => {
  emits('update:longitude', getNumber(value));
};

const setLatitude = (value: string) => {
  emits('update:latitude', getNumber(value));
};

const setAltitude = (value: string) => {
  emits('update:altitude', getNumber(value));
};

watch(
  () => posValue.value,
  (value: string) => {
    if (!value) {
      return;
    }
    console.log('value', value);
    const [long, lat, alt] = value.split(props.separate);
    setLongitude(long);
    setLatitude(lat);
    setAltitude(alt);
  },
  {
    immediate: true
  }
);

let leftDownClose: Function;
const handleGetLocation = (e: MouseEvent) => {
  emits('before-pickup', e);
  setViewerCursor('crosshair');
  leftDownClose = onLeftClick(({ position: pos }) => {
    const _post = screenPosToLBH(pos.position);
    emits('after-pickup', _post);
    setViewerCursor('');
    if (_post) {
      const { latitude, longitude, height } = _post;
      const _sep = props.separate;
      setModelValue(`${longitude}${_sep}${latitude}${_sep}${height}`);
    }
    leftDownClose();
  });
};
onBeforeUnmount(() => {
  leftDownClose?.();
  setViewerCursor('');
});
</script>

<style scoped lang="less"></style>
