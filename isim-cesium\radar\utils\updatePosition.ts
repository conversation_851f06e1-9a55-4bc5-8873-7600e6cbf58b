import { Cartesian3, HeightReference, Scene } from 'cesium';

export function updateClamping(data: {
  primitive: any;
  position: Cartesian3;
  heightReference: HeightReference;
  setModelMatrix: (clampedPosition: Cartesian3) => void;
}) {
  const { primitive, position, heightReference, setModelMatrix } = data;
  if (!position) return;

  const scene: Scene = primitive._scene;
  const globe = scene.globe;
  const ellipsoid = globe.ellipsoid;
  // @ts-ignore
  const surface = globe._surface;

  primitive?._removeUpdateHeightCallback?.();

  if (heightReference === HeightReference.NONE) return setModelMatrix(position);

  const cartoPosition = ellipsoid.cartesianToCartographic(position);
  // 原本的高度
  const originalHeight = cartoPosition.height;
  cartoPosition.height = 0;
  // 地形高度
  const height = globe.getHeight(cartoPosition) ?? 0;
  if (heightReference === HeightReference.CLAMP_TO_GROUND) {
    // 需要贴地
    cartoPosition.height = height;
    const clampedPosition = ellipsoid.cartographicToCartesian(cartoPosition);
    setModelMatrix(clampedPosition);
  } else if (heightReference === HeightReference.RELATIVE_TO_GROUND) {
    cartoPosition.height = originalHeight + height;
    const clampedPosition = ellipsoid.cartographicToCartesian(cartoPosition);
    setModelMatrix(clampedPosition);
  }

  // 地形更新时更新高度
  primitive._removeUpdateHeightCallback = surface.updateHeight(cartoPosition, (clampedPosition: Cartesian3) => {
    const carto = ellipsoid.cartesianToCartographic(clampedPosition);
    const height = globe.getHeight(cartoPosition) ?? 0;
    if (heightReference === HeightReference.CLAMP_TO_GROUND) {
      carto.height = height;
    } else if (heightReference === HeightReference.RELATIVE_TO_GROUND) {
      carto.height += originalHeight;
    }
    // console.log('heightReference', heightReference)
    // console.log('originalHeight', originalHeight)
    // console.log('carto', carto)
    clampedPosition = ellipsoid.cartographicToCartesian(carto);
    setModelMatrix(clampedPosition);
  });
}
