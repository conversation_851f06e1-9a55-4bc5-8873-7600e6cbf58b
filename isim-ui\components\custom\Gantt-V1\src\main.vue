<template>
  <div ref="ganttRef" :class="ns.b()">
    <div :class="ns.e('left')">
      <div :class="ns.b('header')">
        <span>{{ leftProp.label }}</span>
        <div v-if="$slots.leftTool" :class="ns.be('header', 'lefttool')">
          <slot name="leftTool"></slot>
        </div>
      </div>
      <div ref="leftContent" :class="ns.b('content')">
        <div :class="ns.b('scroll')">
          <left-node v-for="(item, dex) in ganttData" :key="dex" :data="item">
            <template #leftIcon>
              <slot name="leftIcon" :data="item"></slot>
            </template>

            <template #left="{ data }">
              <slot name="left" :data="data"></slot>
            </template>
          </left-node>
        </div>
      </div>
    </div>
    <div ref="rightTag" :class="ns.e('right')">
      <div :class="ns.b('header')">
        <timeLine v-bind="props"></timeLine>
      </div>
      <div ref="rightContent" :class="ns.b('content')">
        <div :class="ns.b('scroll')">
          <right-node v-for="(item, dex) in ganttData" :key="dex" :data="item">
            <template #range="{ data, offset, width, updateRangeFn }">
              <slot name="range" :data="data" :offset="offset" :width="width" :update-range-fn="updateRangeFn"></slot>
            </template>
          </right-node>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimGanttV1'
};
</script>
<script setup>
import LeftNode from './leftNode.vue';
import RightNode from './rightNode.vue';
import { onMounted, provide, ref, toRef, watch } from 'vue';
import { useNamespace } from './methods/namespace.js';
import { ganttEmits, ganttProps } from './gantt';
import { GanttNode } from './methods/ganttNode';
import TimeLine from './time-line.vue';

import { useRightContentSize } from './methods/contentSize';
import { useScrollEventBind } from './methods/scrollEventBind';

const props = defineProps(ganttProps);
const emit = defineEmits(ganttEmits);
const ns = useNamespace('gantt');

const ganttRef = ref();

const { leftContent, rightContent } = useScrollEventBind();

const _ganttHandle = new GanttNode(props);
const ganttData = ref(_ganttHandle.handleData());

watch(
  () => props.data,
  (val) => {
    const _ganttHandle = new GanttNode(props);
    ganttData.value = _ganttHandle.handleData();
  }
);

// 获取整个甘特图区域能容纳多少个时间刻度
const { rightTag, rightWidth } = useRightContentSize();

// const ganttState = reactive({
//     sliderStartTime: _ganttHandle.minTime,
//     // interval: 3600000
// })
// 根据容纳时间的刻度计算出每个时间刻度需要显示多少的时间
// const interval = computed(() => {
//     const start = GanttNode.getTime(props.startTime);
//     const end = GanttNode.getTime(props.endTime);
//     return (end - start) / timeRange.value
// })
/**
 * 根据时间差值 计算宽度
 * ganttState.interval 为时间刻度的差值
 * 200 为每个时间刻度之间的差值
 * ganttState.interval / 200 得出每一px应该代表的时间刻度
 * time/ (ganttState.interval / 200) 即得出这么多的时间差值需要多少个px表示
 * @param {number} time 时间差值
 */
// const getPxFromSecond = (time: number) => {
//     return time / (interval.value / 200)
// }

// 计算时间刻度
// const dateTextList = computed(() => {
//     const arr: any[] = []
//     // 如果数据为空的时候 _ganttHanlde.minTime 始终为0
//     // if (timeRange.value === 0 || !_ganttHandle.minTime) return arr;
//     for (let i = 0; i <= timeRange.value; i++) {
//         const timeSecond = GanttNode.getTime(props.startTime) + interval.value * i;
//         const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss')
//         const left = 200 * i + 'px';
//         arr.push({ timeSecond, time, left })
//     }
//     return arr;
// })

// const currentTimeStyle = computed(() => {
//     const current = GanttNode.getTime(props.currentTime)
//     const start = GanttNode.getTime(props.startTime);
//     const translateX = getPxFromSecond(current - start);
//     return {
//         transform: `translateX(${getPxFromSecond(current - start)}px)`,
//         display: translateX < 0 || translateX > rightWidth.value ? 'none' : 'flex'
//     }
// })

// const currentTimeFormat = computed(() => dayjs(props.currentTime).format('YYYY/MM/DD HH:mm:ss'))

// const scrollStyle = computed(() => {
//     return {
//         width: getPxFromSecond(_ganttHandle.maxTime - _ganttHandle.minTime) + 'px'
//     }
// })
// const defaultTime = {
//     startTime: props.startTime,
//     endTime: props.endTime
// }
/**
 *
 * @param data
 */
const rangeDoubleClick = (data) => {
  emit('update:startTime', data._startTime);
  emit('update:endTime', data._endTime);
};

const updateCurrentTime = (time) => {
  emit('update:currentTime', time);
};
onMounted(() => {
  // TODO
  // document.documentElement.addEventListener('dblclick', (e: MouseEvent) => {
  //     if (!ganttRef.value?.contains(e.target as HTMLElement)) {
  //         emit('update:startTime', defaultTime.startTime as number);
  //         emit('update:endTime', defaultTime.endTime as number)
  //     }
  // })
});

const ganttHandle = _ganttHandle;
provide('gantt', {
  leftProp: toRef(props, 'leftProp'),
  labelField: toRef(props, 'labelField'),
  childrenField: toRef(props, 'childrenField'),
  ganttHandle,
  ganttData,
  startTime: toRef(props, 'startTime'),
  endTime: toRef(props, 'endTime'),
  // interval: interval.value,
  rightWidth,

  // getPxFromSecond,
  rangeDoubleClick,
  updateCurrentTime
});
const handleContextmenu = (event, data) => {
  emit('contextmenu', event, data);
};
const handleClick = (event, data) => {
  emit('left-click', event, data);
};
provide('event', {
  handleContextmenu,
  handleClick
});
</script>

<style lang="less">
@node-height: 30px;
@left-width: 300px;
@header-height: 30px;
.rt-gantt {
  --border-color-6: rgba(var(--text-color-val), 0.6);
  --border-color-7: rgba(var(--text-color-val), 0.7);
  --white-color-1_5: rgba(var(--text-color-val), 0.15);
  --white-color-5: rgba(var(--text-color-val), 0.6);
  @red-color: #f00;
  @black-color: #000;
  display: flex;
  border: 1px solid var(--white-color-1_5);

  .rt-gantt-header {
    height: @header-height;
    line-height: @header-height;
    border-bottom: 1px solid var(--white-color-1_5);
    box-sizing: border-box;
  }

  .rt-gantt-content {
    height: calc(100% - @header-height);
    overflow-y: auto;
  }

  .rt-gantt-scroll {
    width: 100%;
    background: linear-gradient(0, var(--white-color-1_5) 1px, transparent 1px, transparent 100%);
    background-size: 100% @node-height;
  }

  // left
  .rt-gantt__left {
    width: @left-width;
    border-right: 1px solid var(--border-color-6);

    .rt-gantt-header {
      display: flex;
      justify-content: space-between;

      &__lefttool {
      }
    }

    .rt-gantt-content {
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .rt-gantt-left {
      &__current {
        height: @node-height;
        display: flex;
        align-items: center;
      }

      &__icon {
        display: flex;
        align-items: center;
        transform: rotate(0);
        transition: all 200ms linear;

        & > svg {
          width: 20px;
          height: 20px;
        }
      }

      &__text {
      }
    }
  }

  // right
  .rt-gantt__right {
    position: relative;
    flex-grow: 1;
    width: calc(100% - @left-width);

    .rt-gantt-time__current {
      position: absolute;
      top: 0;
      display: flex;
      align-items: flex-start;
      width: 1px;
      height: 100%;
      background-color: fade(@red-color, 100);
      z-index: 10;

      .rt-gantt-time__text {
        display: flex;
        white-space: nowrap;
        background-color: @black-color;
        padding: 0 3px;
        // box-shadow: 5px 5px 5px 5px fade(@white-color, 20%);
      }
    }

    .is__end {
      justify-content: flex-end;
    }

    .rt-gantt-header {
      overflow: hidden;

      &__time {
        position: absolute;
        top: -3px;
        white-space: nowrap;
        font-size: 13px;
      }
    }

    .rt-gantt-content {
      overflow-x: hidden;
    }

    .rt-gantt-scroll {
      // background-image: url('./logo.png');
      min-width: 100%;
    }

    .rt-gantt-header__scale {
      position: absolute;
      left: 0;
      bottom: 3px;
      width: 100%;
      background-image: linear-gradient(90deg, fadevar(--white-color-5) 1px, transparent 1px, transparent 100%);
      background-repeat: repeat-x;
    }

    .rt-gantt-header__scale--long {
      height: 10px;
      background-size: 200px 100%;
    }

    .rt-gantt-header__scale--short {
      height: 5px;
      background-size: 5px 100%;
    }

    .rt-gantt-right {
      position: relative;

      &__current {
        height: @node-height;
        display: flex;
        align-items: center;
      }

      &__range {
        background-color: var(--border-color-7);
        height: 20px;
        border-radius: 2px;
        padding: 0 2px;
        box-sizing: border-box;
        white-space: nowrap;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }

  .is-expand {
    transform: rotate(90deg) !important;
  }
}
</style>
