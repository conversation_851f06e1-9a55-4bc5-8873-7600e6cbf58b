<!--
* <AUTHOR> zhangle
* @Date :  2023/4/1
* @Version : 1.0
* @Content : InputNumber
-->
<template>
  <el-input-number ref="inputNumberRef" v-bind="{ ...props, ...$attrs }" />
</template>

<script lang="ts">
export default {
  name: 'SimInputNumber'
};
</script>
<script lang="ts" setup>
import { ref } from 'vue';
import { ElInputNumber, inputNumberProps } from 'element-plus';

const inputNumberRef = ref<InstanceType<typeof ElInputNumber>>();
const props = defineProps(inputNumberProps);

defineExpose({
  getInputIns: () => inputNumberRef.value
});
</script>
<style scoped></style>
