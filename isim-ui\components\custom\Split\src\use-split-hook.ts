/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */

const splitProvideKey = Symbol('split-provide-key');
export function useSplitHook() {
  const splitRef = ref<HTMLDivElement>();
  provide(splitProvideKey, {
    splitRef
  });
  return {
    splitRef
  };
}
export function useSplitInjectHook() {
  const injectState = inject(splitProvideKey) as ReturnType<typeof useSplitHook>;
  return injectState;
}
