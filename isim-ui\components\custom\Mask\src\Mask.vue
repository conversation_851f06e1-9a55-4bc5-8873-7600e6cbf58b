<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/6
* @Version : 1.0
* @Content : Mask
-->
<template>
  <div class="sim-mask" v-bind="$attrs" :style="maskStyle" @contextmenu.stop.prevent>
    <slot />
  </div>
</template>

<script lang="ts">
export default {
  name: 'SimMask'
};
</script>
<script lang="ts" setup>
import { useZIndex } from 'isim-ui';

const { nextZIndex } = useZIndex();
const ZIndex = ref(nextZIndex());
const maskStyle = computed(() => {
  return {
    zIndex: ZIndex.value
  };
});
</script>
<style scoped lang="less">
.sim-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}
</style>
