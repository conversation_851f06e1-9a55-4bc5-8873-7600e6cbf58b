/**
 * @Author: 宋计民
 * @Date: 2023/4/26
 * @Version: 1.0
 * @Content:
 */
import { App, ObjectPlugin } from 'vue';
export type WithInstallCom<T> = ObjectPlugin & T;

/**
 * 注册组件
 * @param main
 * @param extra
 */
export function withInstall<T, E extends Record<string, any>>(main: T, extra?: E) {
  const _main = main as WithInstallCom<T>;
  _main.install = (app: App) => {
    for (const comp of [_main, ...Object.values(extra ?? {})]) {
      app.component(comp.name, comp);
    }
  };
  if (extra) {
    for (const [key, comp] of Object.entries(extra)) {
      (main as any)[key] = comp;
    }
  }
  return _main;
}
