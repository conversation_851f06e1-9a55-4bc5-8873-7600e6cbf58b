import { getClock, getScene, onViewerCreated } from 'isim-cesium';
import { Clock, Event, JulianDate, Scene } from 'cesium';

type EventCallbackType = (...params: any[]) => any;

type SceTickEventType = (scene: Scene, time: JulianDate) => void;

export interface EventConfigType {
  viewerName?: string;
}
/**
 * onPreUpdate
 * @param cb
 * @param opt
 */
export function onPreUpdate(cb: SceTickEventType, opt?: EventConfigType) {
  const { viewerName } = opt ?? {};
  let preUpdateClose: Event.RemoveCallback | undefined;
  onViewerCreated(
    (viewer) => {
      const scene = viewer.scene;
      preUpdateClose = scene.preUpdate.addEventListener(cb);
    },
    { viewerName }
  );
  return preUpdateClose;
}

/**
 * postRender 事件
 * @param cb
 * @param opt
 */
export function onPostRender(cb: EventCallbackType, opt?: EventConfigType) {
  const { viewerName } = opt ?? {};
  const scene = getScene(viewerName);
  const postRenderClose: Event.RemoveCallback | undefined = scene.postRender.addEventListener(cb);
  return () => postRenderClose;
}

/**
 * Tick 事件
 * @param cb
 * @param opt
 */
export function onTick(cb: (clock: Clock) => void, opt?: EventConfigType) {
  const { viewerName } = opt ?? {};
  const clock = getClock(viewerName);
  const tickClose: Event.RemoveCallback | undefined = clock.onTick.addEventListener(cb);
  return tickClose;
}
