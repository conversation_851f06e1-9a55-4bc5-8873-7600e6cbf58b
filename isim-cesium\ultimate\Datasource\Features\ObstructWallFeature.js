import { BoundingSphere } from 'cesium';
import StaticFeature from './StaticFeature';
import ObstructWallVisualizer from '../../Modules/VisualizationModel/ObstructWallVisualizer';

export default class ObstructWallFeature extends StaticFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this.create();
  }

  create() {
    const bs = BoundingSphere.fromPoints(this._options.positions);
    this._entity.position = bs.center;
    this.primitive = new ObstructWallVisualizer(this._options);
  }
}
