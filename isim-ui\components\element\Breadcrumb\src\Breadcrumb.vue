<!--
* @Author: 王磊磊
* @Date: 2023/4/11
* @Version: 1.0
* @Content: 面包屑
-->
<template>
  <div class="rt-breadcrumb flex-wrap">
    <el-breadcrumb v-bind="$attrs">
      <el-breadcrumb-item v-for="(item, dex) in breadcrumbList" :key="item" :to="{ path: item.path }" @click="handleClick(item, dex)">{{
        item.meta.title
      }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="rt-breadcrumb__line">
      <div class="rt-breadcrumb__modification"></div>
    </div>
    <BreadSvg class="rt-breadcrumb__svg" />
    <slot name="right"></slot>
  </div>
</template>

<script>
export default {
  name: 'SimBreadcrumb'
};
</script>
<script setup>
import BreadSvg from 'isim-ui/components/element/Breadcrumb/src/svg/breadSvg.vue';
import { useRoute } from 'vue-router';

const { matched } = useRoute();

const emits = defineEmits(['item-click']);
const handleClick = (data, index) => {
  emits('item-click', data, index);
};

const breadcrumbList = computed(() => {
  return matched.filter((item) => item.meta.breadcrumb);
});
</script>

<style scoped lang="less">
.rt-breadcrumb {
  position: relative;
  padding-left: 6px;
  &__svg {
    position: absolute;
    right: 0;
    top: -2px;
  }
  &::before {
    content: '';
    position: absolute;
    z-index: 9;
    top: 1px;
    left: 0;
    bottom: 0;
    margin: auto;
    width: 2px;
    height: 12px;
    background: var(--primary-color);
  }
  &__line {
    position: relative;
    margin-left: 8px;
    height: 1px;
    background: rgba(var(--primary-color-val), 0.3);
    flex: 1;
    bottom: -3px;
  }
  &__modification {
    position: absolute;
    right: 0;
    bottom: -8px;
  }
}
</style>
