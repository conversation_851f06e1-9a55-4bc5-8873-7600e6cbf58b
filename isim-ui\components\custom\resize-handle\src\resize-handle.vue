<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : resize-handle.vue
-->
<template>
  <div
    v-for="handle in handles"
    :id="handle"
    :key="handle"
    :class="['sim-resize-handle', 'sim-resize-handle--' + handle]"
    style="display: block"
    @mousedown="handleMouseDown"
  ></div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';

defineOptions({
  name: 'SimResizeHandle'
});
defineProps({
  handles: {
    type: Array as PropType<Array<'tl' | 'tm' | 'tr' | 'mr' | 'br' | 'bm' | 'bl' | 'ml'>>,
    default: () => []
  }
});
defineEmits(['update']);
const handleMouseDown = () => {};
//const {} = useResizableEventBind(props, emits, )
</script>

<style scoped lang="less">
.sim-resize-handle {
  position: absolute;
  height: 4px;
  width: 4px;

  &--tl {
    cursor: nw-resize;
    float: left;
    left: -2px;
    top: -2px;
  }

  &--tm {
    position: absolute;
    width: calc(100% - 4px);
    left: 2px;
    top: -2px;
    cursor: n-resize;
  }

  &--tr {
    float: left;
    top: -2px;
    right: -2px;
    cursor: ne-resize;
  }

  &--ml {
    height: calc(100% - 4px);
    top: 2px;
    left: -2px;
    cursor: e-resize;
  }

  &--mr {
    height: calc(100% - 4px);
    top: 2px;
    right: -2px;
    cursor: e-resize;
  }

  &--bl {
    bottom: -2px;
    left: -2px;
    cursor: sw-resize;
  }

  &--bm {
    width: calc(100% - 4px);
    left: 2px;
    bottom: -2px;
    cursor: s-resize;
  }

  &--br {
    bottom: -2px;
    right: -2px;
    cursor: se-resize;
  }
</style>
