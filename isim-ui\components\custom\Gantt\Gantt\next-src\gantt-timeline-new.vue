<!--
* @Author: 宋计民
* @Date: 2024/4/24
* @Version: 1.0
* @Content: gantt-timeline.vue
-->
<template>
  <div class="sim-gantt-timeline" ref="timelineRef" @wheel.shift.prevent="scrollX">
    <time-line :min-time="minTime" :max-time="maxTime" :time-number="timeNumber" />
    <!--    滑块-->
    <gantt-slider
      :parent-ref="timelineRef"
      :current-start-time="minTime"
      :current-end-time="maxTime"
      v-model:selectStartTime="selectStartTime!"
      v-model:selectEndTime="selectEndTime!"
      v-model:currentTime="currentTime"
      @change="change"
    />
  </div>
</template>

<script setup lang="ts">
import GanttSlider from './gantt-slider.vue';
import TimeLine from './time-line.vue';
import { calculationTime } from './utils/time-utils.ts';

defineOptions({
  name: 'SimGanttTimelineNew'
});
const props = defineProps({
  minTime: {
    type: Number,
    default: 0
  },
  maxTime: {
    type: Number,
    default: 0
  }
});
const emits = defineEmits<{
  change: [data: { showStartTime: number; showEndTime: number; currentTime: number }];
}>();

const selectStartTime = defineModel<number>('showStartTime');
const selectEndTime = defineModel<number>('showEndTime');
const currentTime = defineModel<number>('currentTime');

watch(
  () => currentTime.value,
  () => {
    change();
  }
);
const change = () => {
  emits('change', { showStartTime: selectStartTime.value!, showEndTime: selectEndTime.value!, currentTime: currentTime.value! });
};
// 初始化检查选中区域的数据，如果没传值，默认选中所有区域
const checkSelectData = () => {
  if (!selectStartTime.value && !selectEndTime.value) {
    selectStartTime.value = props.minTime;
    selectEndTime.value = props.maxTime;
    change();
    return;
  }
  if (!selectStartTime.value) {
    selectStartTime.value = props.minTime;
    change();
    return;
  }
  if (!selectEndTime.value) {
    selectStartTime.value = props.maxTime;
    change();
    return;
  }
  // 如果需要初始加载就调用change函数，checkSelectData需要在onMounted执行
  // change();
};

const checkCurrentTime = () => {
  if (!currentTime.value) return (currentTime.value = selectStartTime.value);

  if (currentTime.value < selectStartTime.value! || currentTime.value > selectEndTime.value!) currentTime.value = selectStartTime.value;
};
checkSelectData();
checkCurrentTime();

const timeNumber = 7; //时间段数量

const timelineRef = ref<HTMLDivElement>();

const scrollX = (e: WheelEvent) => {
  const deltaY = e.deltaY;
  if ((deltaY <= 0 && selectStartTime.value == props.minTime) || (deltaY >= 0 && selectEndTime.value == props.maxTime)) return;

  const { startTime, endTime } = calculationTime({
    minTime: props.minTime,
    maxTime: props.maxTime,
    currentStartTime: selectStartTime.value!,
    currentEndTime: selectEndTime.value!,
    deltaY
  });
  selectStartTime.value = startTime;
  selectEndTime.value = endTime;

  change();
};

defineExpose({
  setShowTime({ showStartTime, showEndTime }: { showStartTime: number; showEndTime: number }) {
    selectStartTime.value = showStartTime;
    selectEndTime.value = showEndTime;
  }
});
</script>

<style scoped lang="less">
.sim-gantt-timeline {
  --long-range: v-bind(100 / timeNumber);
  --text-width: v-bind(100 / timeNumber);

  position: relative;
  height: 100%;
  min-height: 30px;
  font-size: 12px;
  background-color: rgba(0, 122, 253, 0.3);
  box-sizing: border-box;
  overflow-x: hidden;
  //border: 1px solid red;

  -webkit-user-select: none; /* Safari 3.1+ */
  -moz-user-select: none; /* Firefox 2+ */
  -ms-user-select: none; /* IE 10+ */
  user-select: none; /* 标准语法 */
}
</style>
