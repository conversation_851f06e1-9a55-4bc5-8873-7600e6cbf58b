// import { Resource } from 'cesium'
// import { SvgLoader } from 'yt-cesium'


/**
 * @Author: 宋计民
 * @Date: 2023-09-04 9:09
 * @Version: 1.0
 * @Content: billboard默认显示配置
 */
export const billboardDefaultConfig = {
  scale: 1,
  width: 26,
  height: 26
};

export async function getBillboardImage(url: string) {
  // const svgContent = await Resource.fetchText({ url })
  // const loader = new SvgLoader({
  //   svg: svgContent, // svgContent
  //   cssColor: 'white',
  //   width: 40,
  //   height: 40,
  //   strokeWidth: 1.0
  // })
  // return loader.exportIMG()
  return url;
}
