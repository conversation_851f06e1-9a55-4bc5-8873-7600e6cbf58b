/**
 * @Author: 宋计民
 * @Date: 2023/10/16 11:28
 * @Version: 1.0
 * @Content: 单个扩散的圆环材质
 */
// @ts-nocheck
import { Color, createPropertyDescriptor, defaultValue, defined, Event, Material, MaterialProperty, Property } from 'cesium';
import CIRCLE_DIFFUSE_SOURCE from './Sharder/CircleDiffuseMaterial.glsl?raw';

Material.CircleDiffuseType = 'CircleDiffuse';

Material._materialCache.addMaterial(Material.CircleDiffuseType, {
  fabric: {
    type: Material.CircleDiffuseType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 2,
      percent: 0.03,
      gradient: 0.2,
      number: 5,
      // rotation: 90,
      // openAngle: 90,
      maxAngle: 180,
      minAngle: -180
    },
    source: CIRCLE_DIFFUSE_SOURCE
  },
  translucent: function () {
    return true;
  }
});

export interface CircleDiffuseOptions {
  color?: Color;
  speed?: number;
}

export interface CircleDiffuseMaterialPropertyConstructor extends MaterialProperty {
  new (options?: CircleDiffuseOptions): CircleDiffuseMaterialPropertyConstructor;
}

export const CircleDiffuseMaterialProperty: CircleDiffuseMaterialPropertyConstructor = function (options?: CircleDiffuseOptions) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
};

Object.defineProperties(CircleDiffuseMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._color) && Property.isConstant(this._glow);
    }
  },

  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },

  color: createPropertyDescriptor('color'),

  /**
   * Gets or sets the numeric Property specifying the strength of the glow, as a percentage of the total line width (less than 1.0).
   * @memberof PolylineGlowMaterialProperty.prototype
   * @type {Property|undefined}
   */
  speed: createPropertyDescriptor('speed')
});

/**
 * Gets the {@link Material} type at the provided time.
 *
 * @param {JulianDate} _time The time for which to retrieve the type.
 * @returns {String} The type of material.
 */
CircleDiffuseMaterialProperty.prototype.getType = function (_time) {
  return 'CircleDiffuse';
};
const defaultColor = new Color(1.0, 0.0, 0.0, 0.7);
const defaultSpeed = 2;

CircleDiffuseMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  result.color = Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  result.speed = Property.getValueOrDefault(this._speed, time, defaultSpeed, result.speed);
  return result;
};

CircleDiffuseMaterialProperty.prototype.equals = function (other) {
  return this === other || (other instanceof CircleDiffuseMaterialProperty && Property.equals(this._color, other._color));
};
