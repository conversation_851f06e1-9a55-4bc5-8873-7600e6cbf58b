/**
 * @Author: 宋计民
 * @Date: 2023/10/17 14:29
 * @Version: 1.0
 * @Content: created.ts
 */
import { Viewer } from 'cesium';
import { getViewer, getViewerName, isObject } from 'isim-cesium';

type ViewerCreatedFn<T = any> = (viewer: Viewer) => T;

export const createdEventList: Map<string, Set<ViewerCreatedFn>> = new Map();

/**
 * 注册created事件
 * 如果已经创建好了 直接执行
 * 如果还未创建则加入到队列中 等viewer创建好之后再执行
 * @param fn
 * @param opt
 */
export function onViewerCreated<T>(fn: ViewerCreatedFn<T>, opt?: { viewerName?: string } | string) {
  const viewerName = getViewerName(isObject(opt) ? opt.viewerName : opt);
  const viewer = getViewer(getViewerName(viewerName));
  if (viewer) {
    return fn(viewer);
  }
  if (!createdEventList.has(viewerName)) {
    createdEventList.set(viewerName, new Set());
  }
  createdEventList.get(viewerName)!.add(fn);
}

/**
 * viewer创建好之后再执行注册的事件
 * @param viewerName
 * @param viewer
 */
export function emitViewerCreated(viewerName: string, viewer: Viewer) {
  const eventList = createdEventList.get(viewerName);
  if (eventList) {
    eventList.forEach((item) => item(viewer));
  }
  createdEventList.delete(viewerName);
}
