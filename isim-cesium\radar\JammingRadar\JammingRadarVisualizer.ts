// @ts-nocheck
import {
  AssociativeArray,
  Cartesian3,
  Color,
  defined,
  destroyObject,
  DeveloperError,
  Matrix3,
  Matrix4,
  <PERSON><PERSON><PERSON>,
  Quaternion,
  Spherical,
  Math as CesiumMath,
  MaterialProperty,
  Property,
  Transforms,
  HeightReference,
  HeadingPitchRoll,
  SceneMode
} from 'cesium';
import { removePrimitive } from '../CommonMethod';
import { updateClamping } from '../utils';
import { JammingRadarPrimitive } from './JammingRadarPrimitive';
import { JammingRadarGraphics } from './JammingRadarGraphics.ts';

const matrix3Scratch = new Matrix3();
const cachedPosition = new Cartesian3();
const cachedOrientation = new Quaternion(0, 0, 0, 1);
const cachedHeightReference = HeightReference.NONE;

export function JammingRadarVisualizer(scene, entityCollection) {
  if (!defined(scene)) {
    throw new DeveloperError('scene is required.');
  }
  if (!defined(entityCollection)) {
    throw new DeveloperError('entityCollection is required.');
  }
  entityCollection.collectionChanged.addEventListener(JammingRadarVisualizer.prototype._onCollectionChanged, this);

  this._scene = scene;
  this._primitives = scene.primitives;
  this._entityCollection = entityCollection;
  this._hash = {};
  this._entitiesToVisualize = new AssociativeArray();

  this._onCollectionChanged(entityCollection, entityCollection.values, [], []);
}

JammingRadarVisualizer.prototype.update = function (time) {
  if (!defined(time)) {
    throw new DeveloperError('time is required.');
  }
  const entities = this._entityCollection.values;
  const hash = this._hash;
  const primitives = this._primitives;
  for (let i = 0, len = entities.length; i < len; i++) {
    const entity = entities[i];
    let jammingRadarGraphics = entity.jammingRadar;
    // console.log('jammingRadarGraphics', jammingRadarGraphics)
    if (!jammingRadarGraphics) continue;
    if (!(jammingRadarGraphics instanceof JammingRadarGraphics)) {
      entity._jammingRadar = new JammingRadarGraphics(entity._jammingRadar);
      jammingRadarGraphics = entity._jammingRadar;
    }

    let position;
    let orientation;
    let heightReference;
    let data = hash[entity.id];
    const show = entity.isAvailable(time) && Property.getValueOrDefault(jammingRadarGraphics._show, time, true) && this._entityCollection.show;
    if (show) {
      position = Property.getValueOrUndefined(entity._position, time, cachedPosition);
      orientation = Property.getValueOrDefault(entity._orientation, time, null);
      heightReference = Property.getValueOrDefault(jammingRadarGraphics._heightReference, time, cachedHeightReference);
    }
    if (!show) {
      if (defined(data)) {
        data.primitive.show = false;
      }
      continue;
    }
    let primitive = defined(data) ? data.primitive : undefined;
    if (!defined(primitive)) {
      primitive = new JammingRadarPrimitive();
      primitive.id = entity;
      primitive._scene = this._scene;
      primitives.add(primitive);
      data = {
        scene_mode: this._scene.mode,
        primitive: primitive,
        position: undefined,
        orientation: undefined,
        heightReference: undefined,
        color: undefined,
        pitchList: undefined,
        horizontalList: undefined,
        radiusList: undefined,
        lineShow: undefined,
        lineColor: undefined
      };
      hash[entity.id] = data;
    }
    if (
      !Cartesian3.equals(position, data.position) ||
      !Quaternion.equals(orientation, data.orientation) ||
      data?.heightReference !== heightReference ||
      data?.scene_mode !== this._scene.mode
    ) {
      data.position = Cartesian3.clone(position, data.position);
      data.orientation = Quaternion.clone(orientation, data.orientation);
      data.heightReference = heightReference;
      const oldMode = data.scene_mode;
      data.scene_mode = primitive._scene.mode;

      // primitive.modelMatrix = Transforms.northUpEastToFixedFrame(position)
      // if (!orientation) return
      // Matrix4.multiplyByMatrix3(primitive.modelMatrix, Matrix3.fromQuaternion(orientation), primitive.modelMatrix)
      updateClamping({
        primitive,
        position,
        heightReference,
        setModelMatrix(clampedPosition: Cartesian3) {
          // console.log('heightReference', heightReference)
          // console.log('position', position)
          // console.log('clampedPosition', clampedPosition)

          if (!clampedPosition) return;
          // 二维模式
          if (primitive._scene.mode === SceneMode.SCENE2D) {
            // console.log('二维模式',)
            primitive.modelMatrix = Transforms.northUpEastToFixedFrame(clampedPosition);
            if (!orientation) return;
            Matrix4.multiplyByMatrix3(primitive.modelMatrix, Matrix3.fromQuaternion(orientation), primitive.modelMatrix);
            // 1.90 如果位置没有变化（证明二维转化并未完成），则恢复之前的模式(新版本待测试)
            if (Cartesian3.equals(position, clampedPosition)) {
              data.scene_mode = oldMode;
            }
            return;
          }

          // 三维模式
          // 平移的中心点是(0,0,0)，平移完进行地固系旋转，使其平行于地面，然后再叠加旋转矩阵，orientation中心点是平移+地固系旋转后的坐标
          // primitive.modelMatrix = Transforms.eastNorthUpToFixedFrame(clampedPosition)
          // if (!orientation) return
          // Matrix4.multiplyByMatrix3(primitive.modelMatrix, Matrix3.fromQuaternion(orientation), primitive.modelMatrix)

          // entity.model的平移旋转计算模式
          // 平移和旋转的中心点都是(0,0,0)
          if (!orientation) {
            primitive.modelMatrix = Transforms.eastNorthUpToFixedFrame(clampedPosition);
          } else {
            primitive.modelMatrix = Matrix4.fromTranslation(clampedPosition);
            Matrix4.multiplyByMatrix3(primitive.modelMatrix, Matrix3.fromQuaternion(orientation), primitive.modelMatrix);
          }
        }
      });
    }
    primitive.show = true;

    const color = Property.getValueOrDefault(jammingRadarGraphics._color, time, Color.RED);
    const pitchList = Property.getValueOrDefault(jammingRadarGraphics._pitchList, time, []);
    const horizontalList = Property.getValueOrDefault(jammingRadarGraphics._horizontalList, time, []);
    const radiusList = Property.getValueOrDefault(jammingRadarGraphics._radiusList, time, []);
    const lineShow = Property.getValueOrDefault(jammingRadarGraphics._lineShow, time, true);
    const lineColor = Property.getValueOrDefault(jammingRadarGraphics._lineColor, time, Color.RED);

    data.color = color;
    data.pitchList = pitchList;
    data.horizontalList = horizontalList;
    data.radiusList = radiusList;
    data.lineShow = lineShow;
    data.lineColor = lineColor;

    primitive.color = data.color;
    primitive.pitchList = data.pitchList;
    primitive.horizontalList = data.horizontalList;
    primitive.radiusList = data.radiusList;
    primitive.heightReference = data.heightReference;
    primitive.lineShow = data.lineShow;
    primitive.lineColor = data.lineColor;
  }

  return true;
};

JammingRadarVisualizer.prototype.isDestroyed = function () {
  return false;
};
JammingRadarVisualizer.prototype.destroy = function () {
  const entities = this._entitiesToVisualize.values;
  const hash = this._hash;
  const primitives = this._primitives;
  for (let i = entities.length - 1; i > -1; i--) {
    removePrimitive(entities[i], hash, primitives);
  }
  return destroyObject(this);
};
JammingRadarVisualizer.prototype._onCollectionChanged = function (entityCollection, added, removed, changed) {
  let i;
  let entity;
  const entities = this._entitiesToVisualize;
  const hash = this._hash;
  const primitives = this._primitives;
  // console.log('added',added)
  for (i = added.length - 1; i > -1; i--) {
    entity = added[i];
    if (defined(entity._cone) && defined(entity._position)) {
      entities.set(entity.id, entity);
    }
  }

  for (i = changed.length - 1; i > -1; i--) {
    entity = changed[i];
    if (defined(entity._cone) && defined(entity._position)) {
      entities.set(entity.id, entity);
    } else {
      removePrimitive(entity, hash, primitives);
      entities.remove(entity.id);
    }
  }

  for (i = removed.length - 1; i > -1; i--) {
    entity = removed[i];
    removePrimitive(entity, hash, primitives);
    entities.remove(entity.id);
  }
};
