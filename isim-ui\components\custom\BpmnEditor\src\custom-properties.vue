<!--
* @Author: 张乐
* @Date: 2023/6/5 9:32
* @Version: 1.0
* @Content: 自定义属性栏
-->
<template>
  <div class="custom-properties">
    <div class="custom-properties__header">{{ form?.name }}</div>
    <el-form class="custom-properties__content" :model="form" label-position="top">
      <el-form-item label="流程名称" prop="name" placeholder="请输入流程名称">
        <sim-input v-model="form.name" :disabled="isEdit" @blur="handleChange" />
      </el-form-item>
      <el-form-item label="流程Id">
        <sim-input :value="form.id" disabled />
      </el-form-item>
      <el-form-item label="审核角色">
        <sim-select v-model="form.assignee" :disabled="isEdit" placeholder="请选择审核角色" @change="handleChange">
          <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id" />
        </sim-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

defineOptions({
  name: 'CustomProperties'
});
const props = defineProps({
  businessObject: {
    type: Object as PropType<any>,
    default: () => ({})
  }
});
const emits = defineEmits(['change']);

const isEdit = computed(() => !Object.keys(props.businessObject).length);

const form = ref<any>({});
watch(
  () => props.businessObject,
  (newVal) => {
    form.value = newVal;
  }
);

const handleChange = () => {
  emits('change', form.value);
};
</script>

<style scoped lang="less">
.custom-properties {
  box-sizing: border-box;
  width: 320px;
  height: 100%;
  background: rgba(var(--text-color-val), 0.05);
  border: 1px solid var(--border-color);

  &__header {
    padding: 16px 8px;
    font-size: 16px;
    font-weight: bold;

    border-bottom: 1px solid rgba(var(--primary-color-val), 0.1);
  }

  &__content {
    padding: 16px 8px;
  }
}
</style>
