/**
 * @Author: 宋计民
 * @Date: 2023/10/16 13:36
 * @Version: 1.0
 * @Content: CommunicationFlowMaterial.ts
 */
// @ts-nocheck
import { Color, createPropertyDescriptor, defaultValue, defined, Event, Material, MaterialProperty, Property } from 'cesium';
import ShaderSource from './Sharder/CommunicationFlowMaterial.glsl?raw';

export const MaterialType = 'CommunicationFlow';
const getMaterialAttr = () => {
  return {
    fabric: {
      type: MaterialType,
      uniforms: {
        color: new Color(1.0, 1.0, 0.0, 0.5),
        repeat: 30.0,
        // offset: 0.0,
        speed: 0.1,
        thickness: 0.3
      },
      source: ShaderSource
    },
    translucent: function (_material) {
      // return false; // alpha 设置无效
      return true;
    }
  };
};
export const CommunicationFlowMaterial = new Material(getMaterialAttr());
Material._materialCache.addMaterial(MaterialType, getMaterialAttr());

interface CommunicationFlowMaterialOptions {
  color: Color;
  repeat: number;
  speed: number;
  thickness: number;
}
export interface CommunicationFlowMaterialProperty extends MaterialProperty {
  new (options?: CommunicationFlowMaterialOptions): CommunicationFlowMaterialProperty;
}

export const CommunicationFlowMaterialProperty: CommunicationFlowMaterialProperty = function (options?: CommunicationFlowMaterialOptions) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._repeat = undefined;
  this._repeatSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;
  this._thickness = undefined;
  this._thicknessSubscription = undefined;

  this.color = options.color;
  this.repeat = options.repeat;
  this.speed = options.speed;
  this.thickness = options.thickness;
};

Object.defineProperties(CommunicationFlowMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._color) && Property.isConstant(this._glow);
    }
  },

  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },

  color: createPropertyDescriptor('color'),

  /**
   * Gets or sets the numeric Property specifying the strength of the glow, as a percentage of the total line width (less than 1.0).
   * @memberof PolylineGlowMaterialProperty.prototype
   * @type {Property|undefined}
   */
  speed: createPropertyDescriptor('speed'),

  repeat: createPropertyDescriptor('repeat'),

  thickness: createPropertyDescriptor('thickness')
});

/**
 * Gets the {@link Material} type at the provided time.
 *
 * @param {JulianDate} _time The time for which to retrieve the type.
 * @returns {String} The type of material.
 */
CommunicationFlowMaterialProperty.prototype.getType = function (_time) {
  return MaterialType;
};

const CommunicationFlowDefaultValue: CommunicationFlowMaterialOptions = {
  color: new Color(1.0, 1.0, 0.0, 0.5),
  repeat: 30,
  speed: 0.1,
  thickness: 0.3
};

CommunicationFlowMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  result.color = Property.getValueOrClonedDefault(this._color, time, CommunicationFlowDefaultValue.color, result.color);
  result.speed = Property.getValueOrDefault(this._speed, time, CommunicationFlowDefaultValue.speed, result.speed);
  result.repeat = Property.getValueOrDefault(this._repeat, time, CommunicationFlowDefaultValue.repeat, result.repeat);
  result.gradient = Property.getValueOrDefault(this._thickness, time, CommunicationFlowDefaultValue.thickness, result.thickness);
  return result;
};

CommunicationFlowMaterialProperty.prototype.equals = function (other) {
  return (
    this === other ||
    (other instanceof CommunicationFlowMaterialProperty &&
      Property.equals(this._color, other._color) &&
      Property.equals(this._repeat, other._repeat) &&
      Property.equals(this._speed, other._speed) &&
      Property.equals(this._thickness, other._thickness))
  );
};
