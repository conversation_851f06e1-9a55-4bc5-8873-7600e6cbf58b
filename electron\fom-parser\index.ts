/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { FomXmlParse } from './fom-parser.ts';
import fs from 'node:fs';
import path from 'node:path';

let fomParser: FomXmlParse;
export function initFomParser() {
  fomParser = new FomXmlParse();
  fomParser.loadFolder();
}

interface InteractionReq {
  classNameList: string[];
  interaction: boolean;
}
export function getInteractionSemanticByKey(data: InteractionReq) {
  return {
    success: true,
    data: fomParser.getInteractionByKey(data.classNameList)
  };
}

/**
 * 获取交互指令的schema数据
 * @param data
 */
export function getCommandInteractionSchema(data: { clazzName: string; interaction: boolean }) {
  try {
    const file = fs.readFileSync(path.join(process.cwd(), '/config/resources/interaction-schema', `/${data.clazzName}.json`), 'utf-8');
    return {
      success: true,
      data: JSON.parse(file)
    };
  } catch {
    return {
      success: false,
      data: {}
    };
  }
}
