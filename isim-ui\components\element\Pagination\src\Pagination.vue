<!--
* <AUTHOR> 杨光伟
* @Date :  2023/4/10
* @Version : 1.0
* @Content : Pagination
-->
<template>
  <div class="sim-pagination">
    <el-pagination ref="paginationRef" v-bind="{ ...props, ...$attrs }">
      <template v-for="(value, name) in $slots" :key="name" #[name]>
        <slot :name="name" v-bind="value" />
      </template>
    </el-pagination>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue';
import { ElPagination, paginationProps } from 'element-plus';

defineOptions({
  name: 'SimPagination'
});

const props = defineProps({
  ...paginationProps,
  pageSizes: {
    type: Array as PropType<number[]>,
    default: () => [10, 15, 20, 30, 40, 50, 100]
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  }
});

const pagination = ref<InstanceType<typeof ElPagination>>();

defineExpose({
  getSelectInstant: () => pagination.value
});
</script>
<style lang="less">
.sim-pagination {
  width: 100%;
  min-height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
