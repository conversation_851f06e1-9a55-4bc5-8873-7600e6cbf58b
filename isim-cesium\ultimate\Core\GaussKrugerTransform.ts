/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/10/17 16:35:11
 * @description GaussKrugerTransform
 * @version 1.0
 * @CesiumVersion 1.96
 *  */

const { sqrt, pow, sin, cos, tan, abs } = Math;

interface EllipsoidParamValueType {
  a: number;
  b: number;
}

interface EllipsoidParamsType {
  BEIJING54: EllipsoidParamValueType;
  XIAN80: EllipsoidParamValueType;
  CGCS2000: EllipsoidParamValueType;
}

const EllipsoidParams = {
  BEIJING54: {
    a: 6378245,
    b: 6356863.0188
  },
  XIAN80: {
    a: 6378137,
    b: 6356752.3142
  },
  CGCS2000: {
    a: 6378137,
    b: 6356752.31414
  }
};

export default class GaussKrugerTransform {
  e1 = 0;
  a = 0;
  b = 0;
  f = 0;
  e = 0;

  constructor(ellipsoidType: keyof EllipsoidParamsType) {
    this.init(ellipsoidType);
  }

  init(ellipsoidType: keyof EllipsoidParamsType) {
    const { a, b } = EllipsoidParams[ellipsoidType];
    const f = (a - b) / a;
    const e = sqrt(2 * f - pow(f, 2));
    this.e1 = e / sqrt(1 - pow(e, 2));
    this.a = a;
    this.b = b;
    this.f = f;
    this.e = e;
  }

  getFloorInteger(value: string | number) {
    if (typeof value === 'string') {
      return Number.parseInt(value);
    }
    return Math.floor(value);
  }

  getXY(lon: number, lat: number, zoneWide = 6, originalLatitude = 0, eastOffset = 500000, northOffset = 0) {
    let L0 = this.getFloorInteger(abs(lon) / zoneWide + 1) * zoneWide - 3; // 6 degrees
    L0 = lon > 0 ? L0 : -L0;
    return this._getXY(lon, lat, L0, originalLatitude, eastOffset, northOffset);
  }

  getLonLat(x: number, y: number, zone: number, zoneWide = 6, originalLatitude = 0, eastOffset = 500000, northOffset = 0) {
    let L0 = abs(zone) * zoneWide - 3; // 6 degrees
    L0 = zone > 0 ? L0 : -L0;
    return this._getLonLat(x, y, L0, originalLatitude, eastOffset, northOffset);
  }

  _getXY(lon: number, lat: number, centerMeridian: number, originalLatitude = 0, eastOffset = 500000, northOffset = 0) {
    const { a, b, e, e1 } = this;
    const FE = eastOffset;
    const FN = northOffset;
    const W0 = originalLatitude;
    const L0 = centerMeridian;

    const BR = ((lat - W0) * Math.PI) / 180;
    const lo = ((lon - L0) * Math.PI) / 180;
    const N = a / sqrt(1 - pow(e * sin(BR), 2));
    const C = pow(a, 2) / b;
    const B0 = 1 - (3 * pow(e1, 2)) / 4 + (45 * pow(e1, 4)) / 64 - (175 * pow(e1, 6)) / 256 + (11025 * pow(e1, 8)) / 16384;
    const B2 = B0 - 1;
    const B4 = (15 / 32) * pow(e1, 4) - (175 / 384) * pow(e1, 6) + (3675 / 8192) * pow(e1, 8);
    const B6 = 0 - (35 / 96) * pow(e1, 6) + (735 / 2048) * pow(e1, 8);
    const B8 = (315 / 1024) * pow(e1, 8);
    const s = C * (B0 * BR + sin(BR) * (B2 * cos(BR) + B4 * pow(cos(BR), 3) + B6 * pow(cos(BR), 5) + B8 * pow(cos(BR), 7)));
    const t = tan(BR);
    const g = e1 * cos(BR);
    const XR =
      s +
      (pow(lo, 2) / 2) * N * sin(BR) * cos(BR) +
      ((pow(lo, 4) * N * sin(BR) * pow(cos(BR), 3)) / 24) * (5 - pow(t, 2) + 9 * pow(g, 2) + 4 * pow(g, 4)) +
      (pow(lo, 6) * N * sin(BR) * pow(cos(BR), 5) * (61 - 58 * pow(t, 2) + pow(t, 4))) / 720;
    const YR =
      lo * N * cos(BR) +
      ((pow(lo, 3) * N) / 6) * pow(cos(BR), 3) * (1 - pow(t, 2) + pow(g, 2)) +
      ((pow(lo, 5) * N) / 120) * pow(cos(BR), 5) * (5 - 18 * pow(t, 2) + pow(t, 4) + 14 * pow(g, 2) - 58 * pow(g, 2) * pow(t, 2));
    return {
      x: YR + FE,
      y: XR + FN
    };
  }

  _getLonLat(x: number, y: number, centerMeridian: number, originalLatitude = 0, eastOffset = 500000, northOffset = 0) {
    const { a, e, e1 } = this;
    const k0 = 1;
    const L0 = centerMeridian;
    const FE = eastOffset;
    const FN = northOffset;
    const W0 = originalLatitude;

    const El1 = (1 - sqrt(1 - pow(e, 2))) / (1 + sqrt(1 - pow(e, 2)));
    const Mf = (y - FN) / k0;
    const Q = Mf / (a * (1 - pow(e, 2) / 4 - (3 * pow(e, 4)) / 64 - (5 * pow(e, 6)) / 256)); //角度
    const Bf =
      Q +
      ((3 * El1) / 2 - (27 * pow(El1, 3)) / 32) * sin(2 * Q) +
      ((21 * pow(El1, 2)) / 16 - (55 * pow(El1, 4)) / 32) * sin(4 * Q) +
      ((151 * pow(El1, 3)) / 96) * sin(6 * Q) +
      (1097 / 512) * pow(El1, 4) * sin(8 * Q);
    const Rf = (a * (1 - pow(e, 2))) / sqrt(pow(1 - pow(e * sin(Bf), 2), 3));
    const Nf = a / sqrt(1 - pow(e * sin(Bf), 2));
    const Tf = pow(tan(Bf), 2);
    const D = (x - FE) / (k0 * Nf);
    const Cf = pow(e1, 2) * pow(cos(Bf), 2);
    const B =
      Bf -
      ((Nf * Math.tan(Bf)) / Rf) *
        (pow(D, 2) / 2 -
          ((5 + 3 * Tf + 10 * Cf - 9 * Tf * Cf - 4 * pow(Cf, 2) - 9 * pow(e1, 2)) * pow(D, 4)) / 24 +
          ((61 + 90 * Tf + 45 * pow(Tf, 2) - 256 * pow(e1, 2) - 3 * pow(Cf, 2)) * pow(D, 6)) / 720);
    const L =
      (L0 * Math.PI) / 180 +
      (1 / cos(Bf)) *
        (D - ((1 + 2 * Tf + Cf) * pow(D, 3)) / 6 + ((5 - 2 * Cf + 28 * Tf - 3 * pow(Cf, 2) + 8 * pow(e1, 2) + 24 * pow(Tf, 2)) * pow(D, 5)) / 120);

    return {
      longitude: (L * 180) / Math.PI,
      latitude: (B * 180) / Math.PI + W0
    };
  }
}
