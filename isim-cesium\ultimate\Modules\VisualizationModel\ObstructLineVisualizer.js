import AbstractPrimitive from './AbstractVisualizer.js';
import {
  defaultValue,
  Color,
  Cartesian2,
  Cartesian3,
  SceneMode,
  Material,
  Primitive,
  GeometryInstance,
  PolylineGeometry,
  ArcType,
  PolylineMaterialAppearance
} from 'cesium';
import ModelLinePrimitive from './Components/ModelLinePrimitive';

export default class ObstructLineVisualizer extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._url = options.url;
    this._positions = options.positions;
    this._gapSpace = defaultValue(options.gapSpace, 4.15);
    this._color = defaultValue(options.color, Color.YELLOW);
    this._image = options.image;
    this._width = defaultValue(options.width, 10);
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive3D = this._primitive3D && this._primitive3D.destroy();
      this._primitive2D = this._primitive2D && this._primitive2D.destroy();
      this._update3D();
      this._update2D();
    }

    if (frameState.mode === SceneMode.SCENE3D) {
      this._primitive3D?.update(frameState);
    } else if (frameState.mode === SceneMode.SCENE2D) {
      this._primitive2D?.update(frameState);
    }
  }

  _update3D() {
    if (!this._positions || this._positions?.length < 2) {
      return;
    }
    this._primitive3D = new ModelLinePrimitive({
      url: this._url,
      gapSpace: this._gapSpace,
      positions: this._positions
    });
  }

  _update2D() {
    if (!this._positions || this._positions?.length < 2) {
      return;
    }

    const positions = this._positions;
    let dis = 0;
    for (let i = 0; i < positions.length - 1; i++) {
      dis += Cartesian3.distance(positions[i], positions[i + 1]);
    }

    this._primitive2D = new Primitive({
      geometryInstances: new GeometryInstance({
        geometry: new PolylineGeometry({
          positions: this._positions,
          width: this._width,
          arcType: ArcType.NONE
        })
      }),
      appearance: new PolylineMaterialAppearance({
        material: Material.fromType(Material.ImageType, {
          image: this._image,
          repeat: new Cartesian2(dis / this._gapSpace, 1.0),
          color: this._color
        })
      })
    });
  }
}
