<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/1
* @Version : 1.0
* @Content : header-dec
-->
<template>
  <svg
    width="1520.000000"
    height="12.000000"
    viewBox="0 0 1520 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <desc>Created with Pixso.</desc>
    <g id="画板 1108">
      <g id="矩形 762">
        <rect y="7.000000" width="556.000000" height="5.000000" fill="var(--primary-color)" />
      </g>
      <g id="组合 1794">
        <g id="组合 1791">
          <g id="矩形 7 Copy">
            <path
              d="M644 2L752 2L756 6L764 6L768 2L875.96 2L880.96 7L896.958 7L898.958 5L912.958 5L920 12L919 12L601 12L600 12L607 5L621 5L623 7L639 7L644 2Z"
              fill-rule="evenodd"
              :fill="`url(#paint_linear_${randomUid})`"
            />
            <path
              d="M639 7L644 2L752 2L756 6L764 6L768 2L875.96 2L880.96 7L896.958 7L898.958 5L912.958 5L920 12L600 12L607 5L621 5L623 7L639 7ZM751.586 3.00049L644.415 3.00049L639.414 8.00049L622.586 8.00049L620.586 6.00049L607.414 6.00049L602.415 10.9995L917.574 10.9995L912.546 6.00049L899.373 6.00049L897.373 8.00049L880.546 8.00049L875.545 3.00049L768.414 3.00049L764.414 7.00049L755.586 7.00049L751.586 3.00049Z"
              fill-rule="evenodd"
              fill="#FFFFFF"
            />
          </g>
          <g id="矩形">
            <path d="M624 2L642 2L638 6L624 6L622 4L624 2Z" fill-rule="evenodd" fill="var(--primary-color)" />
          </g>
          <g id="矩形 751">
            <path d="M610 2L622 2L620 4L608 4L610 2Z" fill-rule="evenodd" fill="var(--primary-color)" />
          </g>
          <g id="矩形 Copy">
            <path d="M896 2L878 2L882 6L896 6L898 4L896 2Z" fill-rule="evenodd" fill="var(--primary-color)" />
          </g>
          <g id="矩形 Copy 3">
            <path d="M768 0L752 0L757 5L763 5L768 0Z" fill-rule="evenodd" fill="var(--primary-color)" />
          </g>
          <g id="矩形 2 Copy">
            <path d="M910 2L898 2L900 4L912 4L910 2Z" fill-rule="evenodd" fill="var(--primary-color)" />
          </g>
          <g id="矩形 752">
            <path d="M646.02 0L873.98 0L875 1L645 1L646.02 0Z" fill-rule="evenodd" fill="var(--primary-color)" />
          </g>
        </g>
        <g id="组合 1792">
          <g id="组合 970">
            <g id="组合 1789">
              <g id="矩形 740">
                <path d="M920 7L928 7L933 12L925 12L920 7Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.500000" />
              </g>
              <g id="矩形 741">
                <path d="M933 7L941 7L946 12L938 12L933 7Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.500000" />
              </g>
              <g id="矩形 742">
                <path d="M946 7L954 7L959 12L951 12L946 7Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.500000" />
              </g>
            </g>
          </g>
          <g id="矩形 740">
            <path d="M959 7L964 7L964 12L964 12L959 7Z" fill-rule="evenodd" fill="var(--primary-color)" />
          </g>
        </g>
        <g id="组合 1793">
          <g id="组合 970">
            <g id="组合 1789">
              <g id="矩形 740">
                <path d="M600 7L592 7L587 12L595 12L600 7Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.500000" />
              </g>
              <g id="矩形 741">
                <path d="M587 7L579 7L574 12L582 12L587 7Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.500000" />
              </g>
              <g id="矩形 742">
                <path d="M574 7L566 7L561 12L569 12L574 7Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.500000" />
              </g>
            </g>
          </g>
          <g id="矩形 740">
            <path d="M561 7L556 7L556 12L556 12L561 7Z" fill-rule="evenodd" fill="var(--primary-color)" />
          </g>
        </g>
      </g>
      <g id="矩形 762">
        <rect x="964.000000" y="7.000000" width="556.000000" height="5.000000" fill="var(--primary-color)" />
      </g>
    </g>
    <defs>
      <linearGradient :id="`paint_linear_${randomUid}`" x1="760.000000" y1="2.000000" x2="760.000000" y2="10.868313" gradientUnits="userSpaceOnUse">
        <stop stop-color="#F2F2F2" />
        <stop offset="0.265432" stop-color="#F2F2F2" />
        <stop offset="0.664609" stop-color="#A3A3A3" />
        <stop offset="1.000000" stop-color="#D4D4D4" />
      </linearGradient>
    </defs>
  </svg>
</template>

<script lang="ts">
export default {
  name: 'HeaderDec'
};
</script>
<script lang="ts" setup>
import { createUUIdV4 } from '@/utils/uuid-utils';

defineProps({
  width: {
    type: Number,
    default: 408
  }
});
const randomUid = createUUIdV4();
</script>
<style scoped lang="less"></style>
