/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */

const resizableProvideKey = Symbol('resizableProvideKey');

export function useResizableHook() {
  const resizableRef = ref<HTMLElement>();

  provide(resizableProvideKey, {
    resizableRef
  });
  return {
    resizableRef
  };
}

export function useResizableInjectHook() {
  const resizableInject = inject(resizableProvideKey) as ReturnType<typeof useResizableHook>;
  return resizableInject;
}
