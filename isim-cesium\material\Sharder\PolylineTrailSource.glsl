uniform vec4 color;
uniform float width;
uniform bool animation;
uniform float duration;
uniform sampler2D image;
varying float v_polylineAngle;

const float maskLength = 16.0;

mat2 rotate(float rad) {
    float c = cos(rad);
    float s = sin(rad);
    return mat2(
    c, s,
    -s, c
    );
}

czm_material czm_getMaterial(czm_materialInput materialInput)
{
    czm_material material = czm_getDefaultMaterial(materialInput);
    // 当前屏幕坐标
    vec2 pos = rotate(v_polylineAngle) * gl_FragCoord.xy;

    float currentX = mod(pos.x, width);
    if (animation) {
        currentX -= czm_frameNumber * duration;
    }

    vec2 st = materialInput.st;
    //    vec4 colorImage = texture2D(image, vec2(fract((currentX) / width - time), st.y));
    vec4 colorImage = texture2D(image, vec2(fract(currentX / width), st.y));

    material.alpha = colorImage.a * color.a;
    // 混合颜色
    // material.diffuse = (colorImage.rgb + color.rgb) / 2.0;
    // 使用color的颜色
    material.diffuse = color.rgb;

    return material;
}