/**
 * @Author: songjimin
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
 */
import type { ExtractPropTypes, PropType, Ref } from 'vue';

export interface GanttDataBase {
  label: string;
  startTime: number;
  endTime: number;
  children?: GanttDataOptions[];
}

export interface GanttNodeProps {
  expand: boolean;
  _startTime: number;
  _endTime: number;
}

export type GanttDataOptions<T = Record<string, any>> = GanttDataBase & GanttNodeProps & T & { [key: string]: any };

export interface LeftProps<T> {
  label: string;
  field: keyof T;
}

export type LabelField = keyof GanttDataOptions | ((data: GanttDataOptions, params?: any) => string);

export const ganttProvideKey = Symbol('gantt');

export const ganttProps = () => {
  return {
    data: {
      required: true,
      type: Array as PropType<GanttDataOptions[]>
    },
    startTime: {
      type: [Number, String],
      default: Date.now()
    },
    endTime: {
      type: [Number, String],
      default: Date.now() + 60000
    },
    leftProp: {
      type: Object as PropType<LeftProps<GanttDataOptions>>,
      default: () => ({ label: '名称', field: 'label' })
    },
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    labelField: {
      type: [String, Function] as PropType<LabelField>,
      default: 'label'
    },
    startTimeField: {
      type: String as PropType<keyof GanttDataOptions>,
      default: 'startTime'
    },
    endTimeField: {
      type: String as PropType<keyof GanttDataOptions>,
      default: 'endTime'
    },
    childrenField: {
      type: String as PropType<keyof GanttDataOptions>,
      default: 'children'
    },
    showCurrentTime: {
      type: Boolean,
      default: true
    },
    setTimeEvent: {
      type: Boolean,
      default: true
    },
    setMousewheel: {
      type: Boolean,
      default: true
    },
    currentTime: {
      type: [String, Number],
      default: Date.now()
    },
    min: {
      type: [Number],
      default: Date.now()
    },
    max: {
      type: [Number],
      default: Date.now()
    }
  };
};

export function generateGanttProps() {
  return {
    ...ganttProps()
  };
}

export type GanttProps = ExtractPropTypes<ReturnType<typeof ganttProps>>;

export function useGanttInject() {
  const {
    labelField,
    leftProp,
    interval,
    rightWidth,
    startTime,
    endTime,
    timeRange,
    getPxFromSecond,
    rangeDoubleClick,
    updateCurrentTime,
    childrenField
  } = inject(ganttProvideKey) as GanttInjection;
  return {
    labelField,
    leftProp,
    interval,
    rightWidth,
    startTime,
    endTime,
    timeRange,
    getPxFromSecond,
    rangeDoubleClick,
    updateCurrentTime,
    childrenField
  };
}
export interface GanttInjection {
  leftProp: LeftProps<GanttDataOptions>;
  interval: number;
  rightWidth: Ref<number>;
  startTime: Ref<number>;
  endTime: Ref<number>;
  labelField: Ref<LabelField>;
  childrenField: string;
  timeRange: Ref<number>;
  getPxFromSecond: (time: number) => number;
  rangeDoubleClick: (data: GanttDataOptions) => void;
  updateCurrentTime: (date: number) => void;
}

export interface EventInjection {
  handleContextmenu: (event: MouseEvent, data: GanttDataOptions) => void;
  handleClick: (e: MouseEvent, data: GanttDataOptions) => void;
  updateMouseWheel: (e: MouseEvent) => void;
  updateTimeRangeUp: () => void;
  updateTimeRangeDown: () => void;
}

export const ganttEventProvideKey = Symbol('event');

export function useGanttEvent() {
  const { handleContextmenu, handleClick, updateMouseWheel, updateTimeRangeUp, updateTimeRangeDown } = inject(ganttEventProvideKey) as EventInjection;
  return {
    handleContextmenu,
    handleClick,
    updateMouseWheel,
    updateTimeRangeUp,
    updateTimeRangeDown
  };
}
