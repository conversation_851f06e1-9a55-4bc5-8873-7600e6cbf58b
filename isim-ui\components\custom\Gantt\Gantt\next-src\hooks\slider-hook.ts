/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { Ref } from 'vue';

interface sliderPropType {
  parentRef: HTMLDivElement;
  currentStartTime: number;
  currentEndTime: number;
}

export const useSlider = (props: sliderPropType, currentTime: Ref<number>, moveCallBackFn: () => void, outProcess?: (left: number) => number) => {
  const sliderLeft = ref(0);

  let clientX: number;
  let maxWidth = 0;
  let currentX = 0;

  // 修改当前currentTime时间

  const mouseDown = (e: MouseEvent) => {
    clientX = e.clientX;
    maxWidth = props.parentRef.clientWidth;
    currentX = sliderLeft.value;
    document.addEventListener('mousemove', moveSlider);
    document.addEventListener('mouseup', mouseUp);
  };
  const mouseUp = (_e: MouseEvent) => {
    document.removeEventListener('mousemove', moveSlider);
  };
  const moveSlider = (e: MouseEvent) => {
    let offsetX = e.clientX - clientX;
    offsetX = currentX + (100 * offsetX) / maxWidth;

    if (offsetX <= 0) offsetX = 0;
    if (offsetX >= 100) offsetX = 100;
    outProcess && (offsetX = outProcess(offsetX));

    sliderLeft.value = offsetX;
    setCurrentTime();

    // rectifyData();
    moveCallBackFn();
  };
  // 根据滑块位置，设置滑块当前时间
  const setCurrentTime = () => {
    currentTime.value = (props.currentEndTime - props.currentStartTime) * (sliderLeft.value / 100) + props.currentStartTime;
  };
  // 根据当前时间设置滑块位置
  const setSliderLeft = () => {
    const left = ((currentTime.value - props.currentStartTime) / (props.currentEndTime - props.currentStartTime)) * 100;
    sliderLeft.value = left <= 0 ? 0 : left >= 100 ? 100 : left;
  };
  // 监听外部改变当前时间，修改滑块位置
  watch(
    () => currentTime.value,
    () => {
      setSliderLeft();
    }
  );

  setSliderLeft();
  return {
    sliderLeft,
    mouseDown,
    mouseUp,
    moveSlider,
    setCurrentTime
  };
};

interface sliderAreaType {
  startTimeLeft: Ref<number>;
  endTimeLeft: Ref<number>;
  setCurrentStartTime: () => void;
  setCurrentEndTime: () => void;
}

export const useSliderArea = (props: sliderPropType, sliderArea: sliderAreaType, moveCallBackFn: () => void) => {
  const { startTimeLeft, endTimeLeft, setCurrentStartTime, setCurrentEndTime } = sliderArea;

  let clientX: number;
  let maxWidth = 0;
  let currentStartTimeLeft = 0;
  let currentEndTimeLeft = 0;

  const mouseDownArea = (e: MouseEvent) => {
    clientX = e.clientX;
    maxWidth = props.parentRef.clientWidth;
    currentStartTimeLeft = startTimeLeft.value;
    currentEndTimeLeft = endTimeLeft.value;

    // console.log('currentStartTimeLeft', currentStartTimeLeft);
    // console.log('currentEndTimeLeft', currentEndTimeLeft);

    document.addEventListener('mousemove', moveSliderArea);
    document.addEventListener('mouseup', mouseUpArea);
  };
  const mouseUpArea = (_e: MouseEvent) => {
    document.removeEventListener('mousemove', moveSliderArea);
  };
  const moveSliderArea = (e: MouseEvent) => {
    // console.log(e);
    const offsetX = e.clientX - clientX;
    let left = currentStartTimeLeft + (100 * offsetX) / maxWidth;
    let right = currentEndTimeLeft + (100 * offsetX) / maxWidth;

    // 处理溢出情况
    if (left <= right) {
      if (left < 0) {
        left = 0;
        right = currentEndTimeLeft - currentStartTimeLeft;
      }
      if (right > 100) {
        right = 100;
        left = currentStartTimeLeft + (100 - currentEndTimeLeft);
      }
    } else {
      if (right < 0) {
        right = 0;
        left = currentStartTimeLeft - currentEndTimeLeft;
      }
      if (left > 100) {
        left = 100;
        right = currentEndTimeLeft + (100 - currentStartTimeLeft);
      }
    }

    startTimeLeft.value = left;
    endTimeLeft.value = right;
    setCurrentStartTime();
    setCurrentEndTime();

    moveCallBackFn();
  };

  return {
    mouseDownArea
  };
};
