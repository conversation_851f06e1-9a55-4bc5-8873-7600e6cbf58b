uniform vec4 color;
uniform float speed;
uniform float gradient;
uniform float percent;
uniform float number;
uniform float maxAngle;
uniform float minAngle;

const float PI = czm_twoPi / 2.0;

// 正反切
float atan2(float y, float x){
    if(x == 0.0){
        if(y > 0.0){ return PI / 2.0; }
        if(y < 0.0){ return -PI / 2.0; }
        return 0.0;
    }

    float angle = atan(y / x);

    if(x < 0.0){ angle += PI; }

    if(angle > PI){
        angle -= czm_twoPi;
    }else if(angle < -PI){
        angle += czm_twoPi;
    }

    return angle;
}

// 计算角度
float calculateAngle(vec2 v1, vec2 v2){
    float diffX = v2.x - v1.x;
    float diffY = v2.y - v1.y;
    float angleInRadians = atan2(diffY, diffX);

    return degrees(angleInRadians);
}

czm_material czm_getMaterial(czm_materialInput materialInput) {
    czm_material material = czm_getDefaultMaterial(materialInput);
    vec2 st = materialInput.st;
    // 底色透明度
    float alpha = color.a / 10.0;
    float alphaDis = 1.0 - alpha;
    // 动态值 0 - 0.5
    float t = fract(czm_frameNumber * speed / 100.0) - 0.5;
    // 当前像素与原点的距离
    float dis = distance(st,vec2(0.5,0.5));
    // 线条宽度
    float lineWidth = 0.01;

    for(int i = 1;i <= 10; i++){
        // 线条与原点的距离
        float current = (0.5 * float(i) / 10.0) + (t / 10.0);
        if(current > 0.5){
            current -=0.5;
        };

        if(dis > current && dis < current + lineWidth){
            // 渐变，透明度百分比(取 alpha - 1.0 的值)
            float currentDis = (dis - current) / lineWidth;
            alpha = color.a * currentDis;
        };
    };

    float currentAngle = calculateAngle(vec2(0.5,0.5),st);
    // float currentAngle = calculateAngle(st, vec2(0.5,0.5));


    float m_maxAngle = maxAngle;
    float m_minAngle = minAngle;

    // 判断当前夹角范围是否在 -180 ~ 180之间
    bool flag = true;
    if(m_maxAngle > 180.0){
        m_minAngle = maxAngle - 360.0;
        m_maxAngle = minAngle;
        // m_maxAngle = maxAngle - 360.0;
        // m_minAngle = minAngle;
        flag = false;
    }else if(m_minAngle < -180.0){
        m_maxAngle = minAngle + 360.0;
        m_minAngle = maxAngle;
        flag = false;
    }

    bool isShow = currentAngle < m_minAngle || currentAngle > m_maxAngle;
    if(flag){
        if(isShow){
            alpha = 0.0;
        };
    }else{
        if(!isShow){
            alpha = 0.0;
        };
    }

    // if(dis > 0.1 && dis < 0.11){
    //   alpha = 1.0;
    // };

    material.diffuse = color.rgb;
    material.alpha = alpha;
    return material;
}