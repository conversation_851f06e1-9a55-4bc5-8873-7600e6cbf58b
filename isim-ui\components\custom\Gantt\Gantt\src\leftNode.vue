<!--
 * @Author: 宋计民
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
-->
<template>
  <div :class="ns.b()">
    <div :class="ns.e('current')" @click="leftRowClick" @contextmenu="rangeContextmenu($event, data)">
      <div
        :class="[ns.e('icon'), ns.is('expand', data.expand)]"
        :style="{
          visibility: data[childrenField]?.length ? 'visible' : 'hidden'
        }"
        @click.stop="expandToggle"
      >
        <slot name="leftIcon">
          <svg>
            <path d="M7 5 L14 10 L7 15 Z" fill="var(--primary-color)" />
          </svg>
        </slot>
      </div>
      <div :class="[ns.e('text')]">
        <slot name="left" :data="data">{{ data[leftProp.field] }}</slot>
      </div>
    </div>
    <template v-if="data[childrenField]">
      <i-sim-gantt-left v-for="(item, dex) in data[childrenField]" v-show="data.expand" :key="dex" :data="item" :deep="deep + 1">
        <template #leftIcon>
          <slot name="leftIcon"></slot>
        </template>
        <template #left="{ data: _data }">
          <slot name="left" :data="_data"></slot>
        </template>
      </i-sim-gantt-left>
    </template>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ISimGanttLeft'
};
</script>

<script lang="ts" setup>
import { computed } from 'vue';
import type { PropType } from 'vue';
import { useNamespace } from 'isim-ui';
import type { GanttDataOptions } from './gantt';
import { useGanttEvent, useGanttInject } from './gantt';

const props = defineProps({
  data: {
    required: true,
    type: Object as PropType<GanttDataOptions>
  },
  deep: {
    type: Number,
    default: 0
  }
});

const expandToggle = () => {
  // eslint-disable-next-line vue/no-mutating-props
  props.data.expand = !props.data.expand;
};
const { leftProp, childrenField } = useGanttInject();

const ns = useNamespace('gantt-left');

const paddingLeft = computed(() => (props.deep > 0 ? '10px' : '0'));

const { handleContextmenu, handleClick } = useGanttEvent();

const leftRowClick = (e: MouseEvent) => {
  // eslint-disable-next-line vue/no-mutating-props
  props.data.expand = !props.data.expand;
  handleClick(e, props.data);
};

const rangeContextmenu = (event: MouseEvent, data: GanttDataOptions) => {
  handleContextmenu(event, data);
};
</script>

<style lang="less" scoped>
.i-gantt-left {
  padding-left: v-bind(paddingLeft);
}
</style>
