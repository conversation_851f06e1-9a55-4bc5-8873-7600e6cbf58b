/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { Entity, Event, JulianDate, Scene, SelectionIndicator, Viewer } from 'cesium';

type IndicatorType = [Entity, SelectionIndicator, HTMLDivElement];

export class MultipleSelectViewModel {
  private _indicators: Map<string, IndicatorType>;
  private scene: Scene;
  private preUpdateClose: Event.RemoveCallback | null;
  private entityCache: WeakSet<Entity>;
  constructor(private viewer: Viewer) {
    this.entityCache = new WeakSet();
    this._indicators = new Map();
    this.scene = this.viewer.scene;
    this.preUpdateClose = this.scene.preUpdate.addEventListener(this.preUpdate.bind(this));
  }
  add(entity: Entity) {
    if (!entity) {
      return;
    }
    if (this._indicators.has(entity.id)) {
      return;
    }
    this.entityCache.add(entity);
    const { indicate, container } = this.createIndicator();
    this._indicators.set(entity.id, [entity, indicate, container]);
    indicate.viewModel.animateAppear();
  }
  addOrRemove(entity: Entity) {
    if (this.has(entity)) {
      this.remove(entity);
      return;
    }
    this.add(entity);
  }
  private preUpdate(_scene: Scene, time: JulianDate) {
    if (this._indicators.size === 0) {
      return;
    }
    this._indicators.forEach(([entity, indicate]) => {
      this.updateIndicator(time, entity, indicate);
    });
  }
  private updateIndicator(time: JulianDate, entity: Entity, indicate: SelectionIndicator) {
    if (!this.entityCache.has(entity)) {
      this._indicators.delete(entity.id);
      return;
    }
    const viewModel = indicate.viewModel;
    if (!entity.isShowing || !entity.isAvailable(time)) {
      return;
    }
    const position = entity.position?.getValue(time);
    if (!position) {
      return;
    }
    viewModel.position = position;
    viewModel.showSelection = true;
    viewModel.update();
  }
  private createIndicator() {
    const viewerContainer = this.viewer.container;
    const selectionIndicatorContainer = document.createElement('div');
    selectionIndicatorContainer.className = 'cesium-viewer-selectionIndicatorContainer';
    viewerContainer.appendChild(selectionIndicatorContainer);
    return {
      indicate: new SelectionIndicator(selectionIndicatorContainer, this.viewer.scene),
      container: selectionIndicatorContainer
    };
  }
  remove(entity: Entity) {
    this.removeById(entity.id);
  }
  removeById(id: string) {
    const indicate = this._indicators.get(id);
    if (!indicate) {
      return;
    }
    const [entity, _in, container] = indicate;
    this.viewer.container.removeChild(container);
    _in.destroy();
    this._indicators.delete(id);
    this.entityCache.delete(entity);
  }
  removeAll() {
    this._indicators.forEach(([entity]) => {
      this.remove(entity);
    });
    this._indicators.clear();
  }

  getEntities() {
    return Array.from(this._indicators.values()).map((item) => item[0]);
  }

  has(entity: Entity) {
    return this._indicators.has(entity.id);
  }
  isDestroyed() {
    return !!this.preUpdateClose;
  }
  destroy() {
    this.removeAll();
    this.preUpdateClose?.();
    this.preUpdateClose = null;
    return this._indicators.size;
  }
}
