/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/11/21 14:13:35
 * @description Create Primitive Method (Primitive)
 * @version 1.0
 * @CesiumVersion 1.99
 * */

const {
  defined,
  defaultValue,
  ColorGeometryInstanceAttribute,
  GeometryInstance,
  GeometryOffsetAttribute,
  Material,
  MaterialAppearance,
  PerInstanceColorAppearance,
  Primitive,
  GroundPrimitive,
  OffsetGeometryInstanceAttribute,
} = Cesium;

export default class PrimitiveLibrary {
  static PRIMITIVE_MODE = Object.freeze({
    ALL: 0,
    FILL: 1,
    OUTLINE: 2,
    SCANNER: 3,
  });

  /**
   * @description 扫描片的扫描方式
   * @static
   */
  static SCAN_MODE = Object.freeze({
    SINGLE_WAY: 0,
    CIRCLE_WAY: 1,
  });

  /**
   * @param {Geometry} GeometryConstructor
   * @param {object} options
   * @param {object} options.geometryOptions
   * @param {Color} options.color
   * @param {Matrix4} options.modelMatrix
   * @param {Matrix4|undefined} [options.relativeMatrix = undefined]
   * @param {boolean|undefined} [options.allowPicking = false]
   * @param {boolean|undefined} [options.asynchronous = false]
   * @param {string|undefined} options.materialType
   * @param {object|undefined} options.uniforms
   * @returns {Primitive}
   */
  static createPrimitive(GeometryConstructor, options) {
    return this._createPrimitive(GeometryConstructor, options, this.createAppearance(options));
  }

  /**
   * @param {Geometry} GeometryConstructor
   * @param {object} options
   * @param {object} options.geometryOptions
   * @param {Color} options.color
   * @param {Matrix4} options.modelMatrix
   * @param {Matrix4|undefined} [options.relativeMatrix = undefined]
   * @param {boolean|undefined} [options.allowPicking = false]
   * @param {boolean|undefined} [options.asynchronous = false]
   * @returns {Primitive}
   */
  static createOutlinePrimitive(GeometryConstructor, options) {
    return this._createPrimitive(
      GeometryConstructor,
      options,
      new PerInstanceColorAppearance({
        flat: true,
        translucent: true,
      })
    );
  }

  /**
   * @description
   * @static
   * @param {Geometry} GeometryConstructor
   * @param {object} options
   * @param {object} options.geometryOptions
   * @param {Color} options.color
   * @param {Matrix4} options.modelMatrix
   * @param {Matrix4|undefined} [options.relativeMatrix = undefined]
   * @param {boolean|undefined} [options.allowPicking = false]
   * @param {boolean|undefined} [options.asynchronous = false]
   * @param {string|undefined} options.materialType
   * @param {object|undefined} options.uniforms
   * @param {Appearance} appearance
   * @param {boolean} [isGround = false]
   * @returns {Primitive|GroundPrimitive}
   */
  static _createPrimitive(GeometryConstructor, options, appearance, isGround = false) {
    // console.log('_createPrimitive: ', GeometryConstructor);
    const { geometryOptions, relativeMatrix = undefined, color, modelMatrix, allowPicking = false, asynchronous = false, materialType } = options;

    // const instance = new GeometryInstance({
    //   geometry: isGround ? new GeometryConstructor(geometryOptions) : GeometryConstructor.createGeometry(new GeometryConstructor(geometryOptions)),
    //   modelMatrix: relativeMatrix,
    //   attributes: {
    //     color: ColorGeometryInstanceAttribute.fromColor(color),
    //   },
    // });
    const instance = this.createGeometryInstance(GeometryConstructor, options, isGround);
    const PrimitiveConstructor = isGround ? GroundPrimitive : Primitive;

    return new PrimitiveConstructor({
      ...options,
      geometryInstances: instance,
      modelMatrix,
      appearance,
      allowPicking,
      asynchronous,
    });
  }

  /**
   *
   * @param {object} options
   * @param {string} options.materialType
   * @param {object} options.uniforms
   * @param {Color} options.color
   * @returns
   */
  static createAppearance(options) {
    const { materialType, uniforms = {}, color } = options;
    const appearance = materialType
      ? new MaterialAppearance({
          material: Material.fromType(materialType, {
            ...uniforms,
            color,
          }),
        })
      : new PerInstanceColorAppearance({
          flat: true,
          translucent: true,
        });
    return appearance;
  }

  /**
   * @description
   * @static
   * @param {Geometry} GeometryConstructor
   * @param {object} options
   * @param {object} options.geometryOptions
   * @param {Color} options.color
   * @param {Matrix4} options.modelMatrix
   * @param {Matrix4|undefined} [options.relativeMatrix = undefined]
   * @param {boolean|undefined} [options.allowPicking = false]
   * @param {boolean|undefined} [options.asynchronous = false]
   * @param {string|undefined} options.materialType
   * @param {object|undefined} options.uniforms
   * @returns {GroundPrimitive}
   */
  static createGroundPrimitive(GeometryConstructor, options) {
    return this._createGroundPrimitive(GeometryConstructor, options, this.createAppearance(options));
  }

  /**
   * @description
   * @static
   * @param {Geometry} GeometryConstructor
   * @param {object} options
   * @param {object} options.geometryOptions
   * @param {Color} options.color
   * @param {Matrix4} options.modelMatrix
   * @param {Matrix4|undefined} [options.relativeMatrix = undefined]
   * @param {boolean|undefined} [options.allowPicking = false]
   * @param {boolean|undefined} [options.asynchronous = false]
   * @param {string|undefined} options.materialType
   * @param {object|undefined} options.uniforms
   * @param {Appearance} appearance
   * @returns {GroundPrimitive}
   */
  static _createGroundPrimitive(GeometryConstructor, options, appearance) {
    return this._createPrimitive(GeometryConstructor, options, appearance, true);
  }

  /**
   * @description
   * @static
   * @param {Geometry} GeometryConstructor
   * @param {object} options
   * @param {object|String|undefined} options.id
   * @param {object} options.geometryOptions
   * @param {Color|undefined} options.color
   * @param {Color|undefined} options.depthFailColor
   * @param {Matrix4|undefined} [options.relativeMatrix = undefined]
   * @param {boolean} [isGround=false]
   * @returns {GeometryInstance}
   */
  static createGeometryInstance(GeometryConstructor, options, isGround = false) {
    const { geometryOptions, relativeMatrix = undefined, color, id, depthFailColor } = options;

    let attributes;
    if (color) {
      attributes = {};
      attributes.color = ColorGeometryInstanceAttribute.fromColor(color);
    }

    if (depthFailColor) {
      attributes.depthFailColor = ColorGeometryInstanceAttribute.fromColor(depthFailColor);
    }

    return new GeometryInstance({
      geometry: isGround ? new GeometryConstructor(geometryOptions) : GeometryConstructor.createGeometry(new GeometryConstructor(geometryOptions)),
      modelMatrix: relativeMatrix,
      attributes,
      id,
    });
  }

  /**
   * @description
   * @static
   * @param {GeometryInstance[]|GeometryInstance} instances
   * @param {object} options
   * @param {Color} options.color
   * @param {Matrix4} options.modelMatrix
   * @param {boolean|undefined} [options.allowPicking = false]
   * @param {boolean|undefined} [options.asynchronous = false]
   * @param {string|undefined} options.materialType
   * @param {object|undefined} options.uniforms
   * @param {Appearance} appearance
   * @param {Appearance} depthFailAppearance
   * @param {boolean}[isGround = false]
   * @param {boolean}[setDepthFailAppearance = false]
   * @returns {Primitive|GroundPrimitive}
   */
  static createPrimitiveForInstance(instances, options, appearance, depthFailAppearance, setDepthFailAppearance = false, isGround = false) {
    const { modelMatrix, allowPicking = false, asynchronous = false, materialType, uniforms, color, depthFailColor } = options;
    if (!defined(appearance)) {
      appearance = this.createAppearance({ materialType, uniforms, color });
    }

    if (setDepthFailAppearance && !depthFailAppearance) {
      depthFailAppearance = this.createAppearance({ materialType, uniforms, color: depthFailColor });
    }

    const PrimitiveConstructor = isGround ? GroundPrimitive : Primitive;
    return new PrimitiveConstructor({
      ...options,
      geometryInstances: instances,
      modelMatrix,
      appearance,
      depthFailAppearance: depthFailAppearance,
      allowPicking,
      asynchronous,
    });
  }

  static createInstanceWithOffset(GeometryConstructor, options) {
    const { geometryOptions, relativeMatrix = undefined, color, id, depthFailColor, offset } = options;

    let attributes;
    if (color) {
      attributes = {};
      attributes.color = ColorGeometryInstanceAttribute.fromColor(color);
    }

    if (depthFailColor) {
      attributes.depthFailColor = ColorGeometryInstanceAttribute.fromColor(depthFailColor);
    }

    attributes.offset = OffsetGeometryInstanceAttribute.fromCartesian3(offset);

    return new GeometryInstance({
      geometry: new GeometryConstructor({
        ...geometryOptions,
        offsetAttribute: GeometryOffsetAttribute.ALL,
      }),
      modelMatrix: relativeMatrix,
      attributes,
      id,
    });
  }
}
