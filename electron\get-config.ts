/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import path from 'node:path';
import fs from 'node:fs';

export interface GlobalConfig {
  httpProxy?: string;
  cesiumImageryProxy?: string;
}
export function getConfig(): GlobalConfig {
  const config = fs.readFileSync(path.join(process.cwd(), '/config/global-config.json'), 'utf-8');
  try {
    return JSON.parse(config);
  } catch (e) {
    return {};
  }
}
