<!--
* <AUTHOR> 宋计民
* @Date :  2023/3/20
* @Version : 1.0
* @Content : Draggable
-->
<template>
  <Teleport :to="appendTo" :disabled="!appendTo">
    <!--v-zindex="{ enabled: true }"-->
    <div
      v-show="show"
      ref="dragRef"
      :style="boxStyle"
      class="sim-draggable"
      :class="{
        'sim-draggable--transparent': transparent,
        'sim-draggable--border': border,
        'sim-draggable--border_color': borderColor,
        'sim-draggable--filter': filter,
        'sim-draggable--notfooter': !footerShow,
        'sim-draggable--notheader': !headerShow,
        'sim-draggable--onlycontent': !footerShow && !headerShow
      }"
      @select.prevent
    >
      <div
        v-for="handle in handles"
        :id="handle"
        :key="handle"
        :class="['i_draggable__resize', classNameHandle, 'i_draggable__resize--' + handle]"
        style="display: block"
        @mousedown.stop.prevent="resizableMouseDown($event, handle)"
      ></div>

      <div v-if="headerShow" class="sim-draggable__header">
        <slot name="header">
          <span class="sim-draggable__title">
            <n-ellipsis>{{ title }}</n-ellipsis>
          </span>
        </slot>
        <div v-if="closeShow" class="sim-draggable__close" @click.prevent.stop="closeDraggable">
          <sim-icon name="guanbi" />
        </div>
      </div>
      <slot name="header-box" v-else> </slot>
      <div :style="contentStyle" class="sim-draggable__content" :class="contentClass" @mousedown.stop>
        <slot></slot>
      </div>
      <div v-if="footerShow" class="sim-draggable__footer" @mousedown.stop>
        <slot name="footer">
          <sim-button :type="sureButtonType" @click.prevent.stop="handleSure">{{ sureButtonText }}</sim-button>
          <sim-button :type="cancelButtonType" @click.prevent.stop="handleCancel">{{ cancelButtonText }}</sim-button>
        </slot>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
// import { zindexable as vZindex } from 'vdirs';
import { useDragState, generateProps } from './draggable-hook';
import { useResizableEventBind } from './resizableEvent';
import { useDraggableEventBind } from './draggableEvent';

defineOptions({
  name: 'SimDraggable'
});
const props = defineProps({
  ...generateProps()
});
const emit = defineEmits<{
  'handle-close': [e: MouseEvent];
  'handle-sure': [e: MouseEvent];
  'handle-cancel': [e: MouseEvent];
  'handle-drag-start': [e: MouseEvent];
  'handle-drag-move': [e: MouseEvent];
  'handle-drag-end': [e: MouseEvent];
  close: [e: MouseEvent];
}>();
const dragState = useDragState(props);
const { boxStyle, dragRef, updateHeight, updateWidth, updateY, currentIndex } = dragState;

defineExpose({
  updateHeight,
  updateWidth,
  updateY
});
useDraggableEventBind(props, emit, dragState);
const { handles, resizableMouseDown } = useResizableEventBind(props, dragState);

const closeDraggable = (e: MouseEvent) => {
  emit('handle-close', e);
  emit('close', e);
};
const handleSure = (e: MouseEvent) => {
  emit('handle-sure', e);
};
const handleCancel = (e: MouseEvent) => {
  emit('handle-cancel', e);
  emit('close', e);
};
</script>

<style scoped lang="less">
.sim-draggable {
  position: absolute;
  z-index: v-bind(currentIndex);
  min-width: v-bind('props.minWidth');
  background: var(--primary-bg-color);
  --sim-draggable-header-height: 48px;
  --sim-draggable-footer-height: 48px;
  box-sizing: border-box;

  &__header {
    position: relative;
    left: -1px;
    width: calc(100% + 2px);
    height: var(--sim-draggable-header-height);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-x: clip;
    color: white;
    background: var(--secondary-header-bg-color);

    .sim-draggable__declaration {
      position: absolute;
      left: 50%;
      top: -13px;
      transform: translateX(-50%);
    }

    .sim-draggable__close {
      position: absolute;
      top: 50%;
      right: 15px;
      transform: translateY(-50%);
      text-align: center;
      line-height: 36px;
      cursor: pointer;
      .flex;
      .justify-center;
      .items-center;

      img.close {
        margin-left: 4px;
      }
    }
  }

  &__title {
    max-width: 80%;
    font-size: 16px;
    font-weight: 700;
  }

  &__content {
    width: 100%;
    height: calc(100% - 96px);
    overflow: auto;
    box-sizing: border-box;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 8px;
    height: var(--sim-draggable-footer-height);
    background: var(--sim-draggable-footer-bgc);
  }
}

.sim-draggable--filter {
  backdrop-filter: blur(10px);
}

.sim-draggable--border {
  border: 1px solid var(--border-color);
}

.sim-draggable--border_color {
  border: 1px solid v-bind(borderColor);
}

.sim-draggable--transparent {
  background: transparent;
}

.sim-draggable--notfooter {
  .sim-draggable__content {
    height: calc(100% - 48px);
    overflow: hidden;
  }
}

.sim-draggable--notheader {
  .sim-draggable__content {
    height: calc(100% - 40px);
  }
}

.sim-draggable--onlycontent {
  .sim-draggable__content {
    height: 100%;
  }
}

.i_draggable__resize {
  position: absolute;
  height: 4px;
  width: 4px;

  &--tl {
    cursor: nw-resize;
    float: left;
    left: -2px;
    top: -2px;
  }

  &--tm {
    position: absolute;
    width: calc(100% - 4px);
    left: 2px;
    top: -2px;
    cursor: n-resize;
  }

  &--tr {
    float: left;
    top: -2px;
    right: -2px;
    cursor: ne-resize;
  }

  &--ml {
    height: calc(100% - 4px);
    top: 2px;
    left: -2px;
    cursor: e-resize;
  }

  &--mr {
    height: calc(100% - 4px);
    top: 2px;
    right: -2px;
    cursor: e-resize;
  }

  &--bl {
    bottom: -2px;
    left: -2px;
    cursor: sw-resize;
  }

  &--bm {
    width: calc(100% - 4px);
    left: 2px;
    bottom: -2px;
    cursor: s-resize;
  }

  &--br {
    bottom: -2px;
    right: -2px;
    cursor: se-resize;
  }
}
</style>
