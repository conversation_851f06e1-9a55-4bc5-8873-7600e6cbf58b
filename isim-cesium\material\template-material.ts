/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
// @ts-nocheck

import { Color, defaultValue, defined, Material, Property, createPropertyDescriptor, Event } from 'cesium';
import ShaderSource from './Sharder/TemplateMaterial.glsl?raw';

// 材质的类型
const MaterialType = 'Template';
Material.TemplateType = MaterialType;

// 创建Material传参数据类型
type TemplateOptions = {};
// 默认的uniforms
const uniforms: TemplateOptions = {};

Material._materialCache.addMaterial(MaterialType, {
  fabric: {
    type: MaterialType,
    uniforms,
    source: ShaderSource
  },
  translucent: function () {
    return true;
  }
});
// 创建primitive使用的Material
export const createTemplateMaterial = (newUniforms: TemplateOptions) => {
  return new Material({
    fabric: {
      type: MaterialType,
      uniforms: {
        ...uniforms,
        ...newUniforms
      },
      source: ShaderSource
    },
    translucent: function () {
      return true;
    }
  });
};

export interface TemplateMaterialPropertyConstructor {
  new (options?: TemplateOptions): {};
}
// entity使用的MaterialProperty
export const TemplateMaterialProperty: TemplateMaterialPropertyConstructor = function (options?: TemplateOptions) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();

  // 传入参数需要有一个私有属性和一个公共属性
  // this._color1 = undefined;
  // this.color1 = options?.color1 ?? uniforms.color1;
};

Object.defineProperties(TemplateMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return false;
    }
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  }
  // 需要使用createPropertyDescriptor对公共属性加工成Property
  // color1: createPropertyDescriptor('color1'),
});

TemplateMaterialProperty.prototype.getType = function (_time) {
  return MaterialType;
};

TemplateMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  // 需要使用Property.getValueOrDefaul(this.私有属性, time, uniforms.默认值, result.公共属性）
  // result.color1 = Property.getValueOrDefault(this._color1, time, uniforms.color1, result.color1);
  return result;
};

// 非必要不修改，判断材质是否相同，cesium会使用这个方法做单例化
TemplateMaterialProperty.prototype.equals = function (other) {
  return this === other;
};
