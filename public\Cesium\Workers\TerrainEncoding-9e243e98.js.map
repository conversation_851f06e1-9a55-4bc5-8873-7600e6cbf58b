{"version": 3, "file": "TerrainEncoding-9e243e98.js", "sources": ["../../../../Source/Core/EllipsoidalOccluder.js", "../../../../Source/Core/TerrainExaggeration.js", "../../../../Source/Core/TerrainQuantization.js", "../../../../Source/Core/TerrainEncoding.js"], "sourcesContent": ["import BoundingSphere from \"./BoundingSphere.js\";\nimport Cartesian3 from \"./Cartesian3.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport Ellipsoid from \"./Ellipsoid.js\";\nimport Rectangle from \"./Rectangle.js\";\n\n/**\n * Determine whether or not other objects are visible or hidden behind the visible horizon defined by\n * an {@link Ellipsoid} and a camera position.  The ellipsoid is assumed to be located at the\n * origin of the coordinate system.  This class uses the algorithm described in the\n * {@link https://cesium.com/blog/2013/04/25/Horizon-culling/|Horizon Culling} blog post.\n *\n * @alias EllipsoidalOccluder\n *\n * @param {Ellipsoid} ellipsoid The ellipsoid to use as an occluder.\n * @param {Cartesian3} [cameraPosition] The coordinate of the viewer/camera.  If this parameter is not\n *        specified, {@link EllipsoidalOccluder#cameraPosition} must be called before\n *        testing visibility.\n *\n * @constructor\n *\n * @example\n * // Construct an ellipsoidal occluder with radii 1.0, 1.1, and 0.9.\n * const cameraPosition = new Cesium.Cartesian3(5.0, 6.0, 7.0);\n * const occluderEllipsoid = new Cesium.Ellipsoid(1.0, 1.1, 0.9);\n * const occluder = new Cesium.EllipsoidalOccluder(occluderEllipsoid, cameraPosition);\n *\n * @private\n */\nfunction EllipsoidalOccluder(ellipsoid, cameraPosition) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"ellipsoid\", ellipsoid);\n  //>>includeEnd('debug');\n\n  this._ellipsoid = ellipsoid;\n  this._cameraPosition = new Cartesian3();\n  this._cameraPositionInScaledSpace = new Cartesian3();\n  this._distanceToLimbInScaledSpaceSquared = 0.0;\n\n  // cameraPosition fills in the above values\n  if (defined(cameraPosition)) {\n    this.cameraPosition = cameraPosition;\n  }\n}\n\nObject.defineProperties(EllipsoidalOccluder.prototype, {\n  /**\n   * Gets the occluding ellipsoid.\n   * @memberof EllipsoidalOccluder.prototype\n   * @type {Ellipsoid}\n   */\n  ellipsoid: {\n    get: function () {\n      return this._ellipsoid;\n    },\n  },\n  /**\n   * Gets or sets the position of the camera.\n   * @memberof EllipsoidalOccluder.prototype\n   * @type {Cartesian3}\n   */\n  cameraPosition: {\n    get: function () {\n      return this._cameraPosition;\n    },\n    set: function (cameraPosition) {\n      // See https://cesium.com/blog/2013/04/25/Horizon-culling/\n      const ellipsoid = this._ellipsoid;\n      const cv = ellipsoid.transformPositionToScaledSpace(\n        cameraPosition,\n        this._cameraPositionInScaledSpace\n      );\n      const vhMagnitudeSquared = Cartesian3.magnitudeSquared(cv) - 1.0;\n\n      Cartesian3.clone(cameraPosition, this._cameraPosition);\n      this._cameraPositionInScaledSpace = cv;\n      this._distanceToLimbInScaledSpaceSquared = vhMagnitudeSquared;\n    },\n  },\n});\n\nconst scratchCartesian = new Cartesian3();\n\n/**\n * Determines whether or not a point, the <code>occludee</code>, is hidden from view by the occluder.\n *\n * @param {Cartesian3} occludee The point to test for visibility.\n * @returns {Boolean} <code>true</code> if the occludee is visible; otherwise <code>false</code>.\n *\n * @example\n * const cameraPosition = new Cesium.Cartesian3(0, 0, 2.5);\n * const ellipsoid = new Cesium.Ellipsoid(1.0, 1.1, 0.9);\n * const occluder = new Cesium.EllipsoidalOccluder(ellipsoid, cameraPosition);\n * const point = new Cesium.Cartesian3(0, -3, -3);\n * occluder.isPointVisible(point); //returns true\n */\nEllipsoidalOccluder.prototype.isPointVisible = function (occludee) {\n  const ellipsoid = this._ellipsoid;\n  const occludeeScaledSpacePosition = ellipsoid.transformPositionToScaledSpace(\n    occludee,\n    scratchCartesian\n  );\n  return isScaledSpacePointVisible(\n    occludeeScaledSpacePosition,\n    this._cameraPositionInScaledSpace,\n    this._distanceToLimbInScaledSpaceSquared\n  );\n};\n\n/**\n * Determines whether or not a point expressed in the ellipsoid scaled space, is hidden from view by the\n * occluder.  To transform a Cartesian X, Y, Z position in the coordinate system aligned with the ellipsoid\n * into the scaled space, call {@link Ellipsoid#transformPositionToScaledSpace}.\n *\n * @param {Cartesian3} occludeeScaledSpacePosition The point to test for visibility, represented in the scaled space.\n * @returns {Boolean} <code>true</code> if the occludee is visible; otherwise <code>false</code>.\n *\n * @example\n * const cameraPosition = new Cesium.Cartesian3(0, 0, 2.5);\n * const ellipsoid = new Cesium.Ellipsoid(1.0, 1.1, 0.9);\n * const occluder = new Cesium.EllipsoidalOccluder(ellipsoid, cameraPosition);\n * const point = new Cesium.Cartesian3(0, -3, -3);\n * const scaledSpacePoint = ellipsoid.transformPositionToScaledSpace(point);\n * occluder.isScaledSpacePointVisible(scaledSpacePoint); //returns true\n */\nEllipsoidalOccluder.prototype.isScaledSpacePointVisible = function (\n  occludeeScaledSpacePosition\n) {\n  return isScaledSpacePointVisible(\n    occludeeScaledSpacePosition,\n    this._cameraPositionInScaledSpace,\n    this._distanceToLimbInScaledSpaceSquared\n  );\n};\n\nconst scratchCameraPositionInScaledSpaceShrunk = new Cartesian3();\n\n/**\n * Similar to {@link EllipsoidalOccluder#isScaledSpacePointVisible} except tests against an\n * ellipsoid that has been shrunk by the minimum height when the minimum height is below\n * the ellipsoid. This is intended to be used with points generated by\n * {@link EllipsoidalOccluder#computeHorizonCullingPointPossiblyUnderEllipsoid} or\n * {@link EllipsoidalOccluder#computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid}.\n *\n * @param {Cartesian3} occludeeScaledSpacePosition The point to test for visibility, represented in the scaled space of the possibly-shrunk ellipsoid.\n * @returns {Boolean} <code>true</code> if the occludee is visible; otherwise <code>false</code>.\n */\nEllipsoidalOccluder.prototype.isScaledSpacePointVisiblePossiblyUnderEllipsoid = function (\n  occludeeScaledSpacePosition,\n  minimumHeight\n) {\n  const ellipsoid = this._ellipsoid;\n  let vhMagnitudeSquared;\n  let cv;\n\n  if (\n    defined(minimumHeight) &&\n    minimumHeight < 0.0 &&\n    ellipsoid.minimumRadius > -minimumHeight\n  ) {\n    // This code is similar to the cameraPosition setter, but unrolled for performance because it will be called a lot.\n    cv = scratchCameraPositionInScaledSpaceShrunk;\n    cv.x = this._cameraPosition.x / (ellipsoid.radii.x + minimumHeight);\n    cv.y = this._cameraPosition.y / (ellipsoid.radii.y + minimumHeight);\n    cv.z = this._cameraPosition.z / (ellipsoid.radii.z + minimumHeight);\n    vhMagnitudeSquared = cv.x * cv.x + cv.y * cv.y + cv.z * cv.z - 1.0;\n  } else {\n    cv = this._cameraPositionInScaledSpace;\n    vhMagnitudeSquared = this._distanceToLimbInScaledSpaceSquared;\n  }\n\n  return isScaledSpacePointVisible(\n    occludeeScaledSpacePosition,\n    cv,\n    vhMagnitudeSquared\n  );\n};\n\n/**\n * Computes a point that can be used for horizon culling from a list of positions.  If the point is below\n * the horizon, all of the positions are guaranteed to be below the horizon as well.  The returned point\n * is expressed in the ellipsoid-scaled space and is suitable for use with\n * {@link EllipsoidalOccluder#isScaledSpacePointVisible}.\n *\n * @param {Cartesian3} directionToPoint The direction that the computed point will lie along.\n *                     A reasonable direction to use is the direction from the center of the ellipsoid to\n *                     the center of the bounding sphere computed from the positions.  The direction need not\n *                     be normalized.\n * @param {Cartesian3[]} positions The positions from which to compute the horizon culling point.  The positions\n *                       must be expressed in a reference frame centered at the ellipsoid and aligned with the\n *                       ellipsoid's axes.\n * @param {Cartesian3} [result] The instance on which to store the result instead of allocating a new instance.\n * @returns {Cartesian3} The computed horizon culling point, expressed in the ellipsoid-scaled space.\n */\nEllipsoidalOccluder.prototype.computeHorizonCullingPoint = function (\n  directionToPoint,\n  positions,\n  result\n) {\n  return computeHorizonCullingPointFromPositions(\n    this._ellipsoid,\n    directionToPoint,\n    positions,\n    result\n  );\n};\n\nconst scratchEllipsoidShrunk = Ellipsoid.clone(Ellipsoid.UNIT_SPHERE);\n\n/**\n * Similar to {@link EllipsoidalOccluder#computeHorizonCullingPoint} except computes the culling\n * point relative to an ellipsoid that has been shrunk by the minimum height when the minimum height is below\n * the ellipsoid. The returned point is expressed in the possibly-shrunk ellipsoid-scaled space and is suitable\n * for use with {@link EllipsoidalOccluder#isScaledSpacePointVisiblePossiblyUnderEllipsoid}.\n *\n * @param {Cartesian3} directionToPoint The direction that the computed point will lie along.\n *                     A reasonable direction to use is the direction from the center of the ellipsoid to\n *                     the center of the bounding sphere computed from the positions.  The direction need not\n *                     be normalized.\n * @param {Cartesian3[]} positions The positions from which to compute the horizon culling point.  The positions\n *                       must be expressed in a reference frame centered at the ellipsoid and aligned with the\n *                       ellipsoid's axes.\n * @param {Number} [minimumHeight] The minimum height of all positions. If this value is undefined, all positions are assumed to be above the ellipsoid.\n * @param {Cartesian3} [result] The instance on which to store the result instead of allocating a new instance.\n * @returns {Cartesian3} The computed horizon culling point, expressed in the possibly-shrunk ellipsoid-scaled space.\n */\nEllipsoidalOccluder.prototype.computeHorizonCullingPointPossiblyUnderEllipsoid = function (\n  directionToPoint,\n  positions,\n  minimumHeight,\n  result\n) {\n  const possiblyShrunkEllipsoid = getPossiblyShrunkEllipsoid(\n    this._ellipsoid,\n    minimumHeight,\n    scratchEllipsoidShrunk\n  );\n  return computeHorizonCullingPointFromPositions(\n    possiblyShrunkEllipsoid,\n    directionToPoint,\n    positions,\n    result\n  );\n};\n/**\n * Computes a point that can be used for horizon culling from a list of positions.  If the point is below\n * the horizon, all of the positions are guaranteed to be below the horizon as well.  The returned point\n * is expressed in the ellipsoid-scaled space and is suitable for use with\n * {@link EllipsoidalOccluder#isScaledSpacePointVisible}.\n *\n * @param {Cartesian3} directionToPoint The direction that the computed point will lie along.\n *                     A reasonable direction to use is the direction from the center of the ellipsoid to\n *                     the center of the bounding sphere computed from the positions.  The direction need not\n *                     be normalized.\n * @param {Number[]} vertices  The vertices from which to compute the horizon culling point.  The positions\n *                   must be expressed in a reference frame centered at the ellipsoid and aligned with the\n *                   ellipsoid's axes.\n * @param {Number} [stride=3]\n * @param {Cartesian3} [center=Cartesian3.ZERO]\n * @param {Cartesian3} [result] The instance on which to store the result instead of allocating a new instance.\n * @returns {Cartesian3} The computed horizon culling point, expressed in the ellipsoid-scaled space.\n */\nEllipsoidalOccluder.prototype.computeHorizonCullingPointFromVertices = function (\n  directionToPoint,\n  vertices,\n  stride,\n  center,\n  result\n) {\n  return computeHorizonCullingPointFromVertices(\n    this._ellipsoid,\n    directionToPoint,\n    vertices,\n    stride,\n    center,\n    result\n  );\n};\n\n/**\n * Similar to {@link EllipsoidalOccluder#computeHorizonCullingPointFromVertices} except computes the culling\n * point relative to an ellipsoid that has been shrunk by the minimum height when the minimum height is below\n * the ellipsoid. The returned point is expressed in the possibly-shrunk ellipsoid-scaled space and is suitable\n * for use with {@link EllipsoidalOccluder#isScaledSpacePointVisiblePossiblyUnderEllipsoid}.\n *\n * @param {Cartesian3} directionToPoint The direction that the computed point will lie along.\n *                     A reasonable direction to use is the direction from the center of the ellipsoid to\n *                     the center of the bounding sphere computed from the positions.  The direction need not\n *                     be normalized.\n * @param {Number[]} vertices  The vertices from which to compute the horizon culling point.  The positions\n *                   must be expressed in a reference frame centered at the ellipsoid and aligned with the\n *                   ellipsoid's axes.\n * @param {Number} [stride=3]\n * @param {Cartesian3} [center=Cartesian3.ZERO]\n * @param {Number} [minimumHeight] The minimum height of all vertices. If this value is undefined, all vertices are assumed to be above the ellipsoid.\n * @param {Cartesian3} [result] The instance on which to store the result instead of allocating a new instance.\n * @returns {Cartesian3} The computed horizon culling point, expressed in the possibly-shrunk ellipsoid-scaled space.\n */\nEllipsoidalOccluder.prototype.computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid = function (\n  directionToPoint,\n  vertices,\n  stride,\n  center,\n  minimumHeight,\n  result\n) {\n  const possiblyShrunkEllipsoid = getPossiblyShrunkEllipsoid(\n    this._ellipsoid,\n    minimumHeight,\n    scratchEllipsoidShrunk\n  );\n  return computeHorizonCullingPointFromVertices(\n    possiblyShrunkEllipsoid,\n    directionToPoint,\n    vertices,\n    stride,\n    center,\n    result\n  );\n};\n\nconst subsampleScratch = [];\n\n/**\n * Computes a point that can be used for horizon culling of a rectangle.  If the point is below\n * the horizon, the ellipsoid-conforming rectangle is guaranteed to be below the horizon as well.\n * The returned point is expressed in the ellipsoid-scaled space and is suitable for use with\n * {@link EllipsoidalOccluder#isScaledSpacePointVisible}.\n *\n * @param {Rectangle} rectangle The rectangle for which to compute the horizon culling point.\n * @param {Ellipsoid} ellipsoid The ellipsoid on which the rectangle is defined.  This may be different from\n *                    the ellipsoid used by this instance for occlusion testing.\n * @param {Cartesian3} [result] The instance on which to store the result instead of allocating a new instance.\n * @returns {Cartesian3} The computed horizon culling point, expressed in the ellipsoid-scaled space.\n */\nEllipsoidalOccluder.prototype.computeHorizonCullingPointFromRectangle = function (\n  rectangle,\n  ellipsoid,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"rectangle\", rectangle);\n  //>>includeEnd('debug');\n\n  const positions = Rectangle.subsample(\n    rectangle,\n    ellipsoid,\n    0.0,\n    subsampleScratch\n  );\n  const bs = BoundingSphere.fromPoints(positions);\n\n  // If the bounding sphere center is too close to the center of the occluder, it doesn't make\n  // sense to try to horizon cull it.\n  if (Cartesian3.magnitude(bs.center) < 0.1 * ellipsoid.minimumRadius) {\n    return undefined;\n  }\n\n  return this.computeHorizonCullingPoint(bs.center, positions, result);\n};\n\nconst scratchEllipsoidShrunkRadii = new Cartesian3();\n\nfunction getPossiblyShrunkEllipsoid(ellipsoid, minimumHeight, result) {\n  if (\n    defined(minimumHeight) &&\n    minimumHeight < 0.0 &&\n    ellipsoid.minimumRadius > -minimumHeight\n  ) {\n    const ellipsoidShrunkRadii = Cartesian3.fromElements(\n      ellipsoid.radii.x + minimumHeight,\n      ellipsoid.radii.y + minimumHeight,\n      ellipsoid.radii.z + minimumHeight,\n      scratchEllipsoidShrunkRadii\n    );\n    ellipsoid = Ellipsoid.fromCartesian3(ellipsoidShrunkRadii, result);\n  }\n  return ellipsoid;\n}\n\nfunction computeHorizonCullingPointFromPositions(\n  ellipsoid,\n  directionToPoint,\n  positions,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"directionToPoint\", directionToPoint);\n  Check.defined(\"positions\", positions);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  const scaledSpaceDirectionToPoint = computeScaledSpaceDirectionToPoint(\n    ellipsoid,\n    directionToPoint\n  );\n  let resultMagnitude = 0.0;\n\n  for (let i = 0, len = positions.length; i < len; ++i) {\n    const position = positions[i];\n    const candidateMagnitude = computeMagnitude(\n      ellipsoid,\n      position,\n      scaledSpaceDirectionToPoint\n    );\n    if (candidateMagnitude < 0.0) {\n      // all points should face the same direction, but this one doesn't, so return undefined\n      return undefined;\n    }\n    resultMagnitude = Math.max(resultMagnitude, candidateMagnitude);\n  }\n\n  return magnitudeToPoint(scaledSpaceDirectionToPoint, resultMagnitude, result);\n}\n\nconst positionScratch = new Cartesian3();\n\nfunction computeHorizonCullingPointFromVertices(\n  ellipsoid,\n  directionToPoint,\n  vertices,\n  stride,\n  center,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"directionToPoint\", directionToPoint);\n  Check.defined(\"vertices\", vertices);\n  Check.typeOf.number(\"stride\", stride);\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  stride = defaultValue(stride, 3);\n  center = defaultValue(center, Cartesian3.ZERO);\n  const scaledSpaceDirectionToPoint = computeScaledSpaceDirectionToPoint(\n    ellipsoid,\n    directionToPoint\n  );\n  let resultMagnitude = 0.0;\n\n  for (let i = 0, len = vertices.length; i < len; i += stride) {\n    positionScratch.x = vertices[i] + center.x;\n    positionScratch.y = vertices[i + 1] + center.y;\n    positionScratch.z = vertices[i + 2] + center.z;\n\n    const candidateMagnitude = computeMagnitude(\n      ellipsoid,\n      positionScratch,\n      scaledSpaceDirectionToPoint\n    );\n    if (candidateMagnitude < 0.0) {\n      // all points should face the same direction, but this one doesn't, so return undefined\n      return undefined;\n    }\n    resultMagnitude = Math.max(resultMagnitude, candidateMagnitude);\n  }\n\n  return magnitudeToPoint(scaledSpaceDirectionToPoint, resultMagnitude, result);\n}\n\nfunction isScaledSpacePointVisible(\n  occludeeScaledSpacePosition,\n  cameraPositionInScaledSpace,\n  distanceToLimbInScaledSpaceSquared\n) {\n  // See https://cesium.com/blog/2013/04/25/Horizon-culling/\n  const cv = cameraPositionInScaledSpace;\n  const vhMagnitudeSquared = distanceToLimbInScaledSpaceSquared;\n  const vt = Cartesian3.subtract(\n    occludeeScaledSpacePosition,\n    cv,\n    scratchCartesian\n  );\n  const vtDotVc = -Cartesian3.dot(vt, cv);\n  // If vhMagnitudeSquared < 0 then we are below the surface of the ellipsoid and\n  // in this case, set the culling plane to be on V.\n  const isOccluded =\n    vhMagnitudeSquared < 0\n      ? vtDotVc > 0\n      : vtDotVc > vhMagnitudeSquared &&\n        (vtDotVc * vtDotVc) / Cartesian3.magnitudeSquared(vt) >\n          vhMagnitudeSquared;\n  return !isOccluded;\n}\n\nconst scaledSpaceScratch = new Cartesian3();\nconst directionScratch = new Cartesian3();\n\nfunction computeMagnitude(ellipsoid, position, scaledSpaceDirectionToPoint) {\n  const scaledSpacePosition = ellipsoid.transformPositionToScaledSpace(\n    position,\n    scaledSpaceScratch\n  );\n  let magnitudeSquared = Cartesian3.magnitudeSquared(scaledSpacePosition);\n  let magnitude = Math.sqrt(magnitudeSquared);\n  const direction = Cartesian3.divideByScalar(\n    scaledSpacePosition,\n    magnitude,\n    directionScratch\n  );\n\n  // For the purpose of this computation, points below the ellipsoid are consider to be on it instead.\n  magnitudeSquared = Math.max(1.0, magnitudeSquared);\n  magnitude = Math.max(1.0, magnitude);\n\n  const cosAlpha = Cartesian3.dot(direction, scaledSpaceDirectionToPoint);\n  const sinAlpha = Cartesian3.magnitude(\n    Cartesian3.cross(direction, scaledSpaceDirectionToPoint, direction)\n  );\n  const cosBeta = 1.0 / magnitude;\n  const sinBeta = Math.sqrt(magnitudeSquared - 1.0) * cosBeta;\n\n  return 1.0 / (cosAlpha * cosBeta - sinAlpha * sinBeta);\n}\n\nfunction magnitudeToPoint(\n  scaledSpaceDirectionToPoint,\n  resultMagnitude,\n  result\n) {\n  // The horizon culling point is undefined if there were no positions from which to compute it,\n  // the directionToPoint is pointing opposite all of the positions,  or if we computed NaN or infinity.\n  if (\n    resultMagnitude <= 0.0 ||\n    resultMagnitude === 1.0 / 0.0 ||\n    resultMagnitude !== resultMagnitude\n  ) {\n    return undefined;\n  }\n\n  return Cartesian3.multiplyByScalar(\n    scaledSpaceDirectionToPoint,\n    resultMagnitude,\n    result\n  );\n}\n\nconst directionToPointScratch = new Cartesian3();\n\nfunction computeScaledSpaceDirectionToPoint(ellipsoid, directionToPoint) {\n  if (Cartesian3.equals(directionToPoint, Cartesian3.ZERO)) {\n    return directionToPoint;\n  }\n\n  ellipsoid.transformPositionToScaledSpace(\n    directionToPoint,\n    directionToPointScratch\n  );\n  return Cartesian3.normalize(directionToPointScratch, directionToPointScratch);\n}\nexport default EllipsoidalOccluder;\n", "import Cartesian3 from \"./Cartesian3.js\";\n\n/**\n * @private\n */\nconst TerrainExaggeration = {};\n\n/**\n * Scales a height relative to an offset.\n *\n * @param {Number} height The height.\n * @param {Number} scale A scalar used to exaggerate the terrain. If the value is 1.0 there will be no effect.\n * @param {Number} relativeHeight The height relative to which terrain is exaggerated. If the value is 0.0 terrain will be exaggerated relative to the ellipsoid surface.\n */\nTerrainExaggeration.getHeight = function (height, scale, relativeHeight) {\n  return (height - relativeHeight) * scale + relativeHeight;\n};\n\nconst scratchCartographic = new Cartesian3();\n\n/**\n * Scales a position by exaggeration.\n */\nTerrainExaggeration.getPosition = function (\n  position,\n  ellipsoid,\n  terrainExaggeration,\n  terrainExaggerationRelativeHeight,\n  result\n) {\n  const cartographic = ellipsoid.cartesianToCartographic(\n    position,\n    scratchCartographic\n  );\n  const newHeight = TerrainExaggeration.getHeight(\n    cartographic.height,\n    terrainExaggeration,\n    terrainExaggerationRelativeHeight\n  );\n  return Cartesian3.fromRadians(\n    cartographic.longitude,\n    cartographic.latitude,\n    newHeight,\n    ellipsoid,\n    result\n  );\n};\n\nexport default TerrainExaggeration;\n", "/**\n * This enumerated type is used to determine how the vertices of the terrain mesh are compressed.\n *\n * @enum {Number}\n *\n * @private\n */\nconst TerrainQuantization = {\n  /**\n   * The vertices are not compressed.\n   *\n   * @type {Number}\n   * @constant\n   */\n  NONE: 0,\n\n  /**\n   * The vertices are compressed to 12 bits.\n   *\n   * @type {Number}\n   * @constant\n   */\n  BITS12: 1,\n};\nexport default Object.freeze(TerrainQuantization);\n", "import AttributeCompression from \"./AttributeCompression.js\";\nimport Cartesian2 from \"./Cartesian2.js\";\nimport Cartesian3 from \"./Cartesian3.js\";\nimport ComponentDatatype from \"./ComponentDatatype.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix4 from \"./Matrix4.js\";\nimport TerrainExaggeration from \"./TerrainExaggeration.js\";\nimport TerrainQuantization from \"./TerrainQuantization.js\";\n\nconst cartesian3Scratch = new Cartesian3();\nconst cartesian3DimScratch = new Cartesian3();\nconst cartesian2Scratch = new Cartesian2();\nconst matrix4Scratch = new Matrix4();\nconst matrix4Scratch2 = new Matrix4();\n\nconst SHIFT_LEFT_12 = Math.pow(2.0, 12.0);\n\n/**\n * Data used to quantize and pack the terrain mesh. The position can be unpacked for picking and all attributes\n * are unpacked in the vertex shader.\n *\n * @alias TerrainEncoding\n * @constructor\n *\n * @param {Cartesian3} center The center point of the vertices.\n * @param {AxisAlignedBoundingBox} axisAlignedBoundingBox The bounds of the tile in the east-north-up coordinates at the tiles center.\n * @param {Number} minimumHeight The minimum height.\n * @param {Number} maximumHeight The maximum height.\n * @param {Matrix4} fromENU The east-north-up to fixed frame matrix at the center of the terrain mesh.\n * @param {Boolean} hasVertexNormals If the mesh has vertex normals.\n * @param {Boolean} [hasWebMercatorT=false] true if the terrain data includes a Web Mercator texture coordinate; otherwise, false.\n * @param {Boolean} [hasGeodeticSurfaceNormals=false] true if the terrain data includes geodetic surface normals; otherwise, false.\n * @param {Number} [exaggeration=1.0] A scalar used to exaggerate terrain.\n * @param {Number} [exaggerationRelativeHeight=0.0] The relative height from which terrain is exaggerated.\n *\n * @private\n */\nfunction TerrainEncoding(\n  center,\n  axisAlignedBoundingBox,\n  minimumHeight,\n  maximumHeight,\n  fromENU,\n  hasVertexNormals,\n  hasWebMercatorT,\n  hasGeodeticSurfaceNormals,\n  exaggeration,\n  exaggerationRelativeHeight\n) {\n  let quantization = TerrainQuantization.NONE;\n  let toENU;\n  let matrix;\n\n  if (\n    defined(axisAlignedBoundingBox) &&\n    defined(minimumHeight) &&\n    defined(maximumHeight) &&\n    defined(fromENU)\n  ) {\n    const minimum = axisAlignedBoundingBox.minimum;\n    const maximum = axisAlignedBoundingBox.maximum;\n\n    const dimensions = Cartesian3.subtract(\n      maximum,\n      minimum,\n      cartesian3DimScratch\n    );\n    const hDim = maximumHeight - minimumHeight;\n    const maxDim = Math.max(Cartesian3.maximumComponent(dimensions), hDim);\n\n    if (maxDim < SHIFT_LEFT_12 - 1.0) {\n      quantization = TerrainQuantization.BITS12;\n    } else {\n      quantization = TerrainQuantization.NONE;\n    }\n\n    toENU = Matrix4.inverseTransformation(fromENU, new Matrix4());\n\n    const translation = Cartesian3.negate(minimum, cartesian3Scratch);\n    Matrix4.multiply(\n      Matrix4.fromTranslation(translation, matrix4Scratch),\n      toENU,\n      toENU\n    );\n\n    const scale = cartesian3Scratch;\n    scale.x = 1.0 / dimensions.x;\n    scale.y = 1.0 / dimensions.y;\n    scale.z = 1.0 / dimensions.z;\n    Matrix4.multiply(Matrix4.fromScale(scale, matrix4Scratch), toENU, toENU);\n\n    matrix = Matrix4.clone(fromENU);\n    Matrix4.setTranslation(matrix, Cartesian3.ZERO, matrix);\n\n    fromENU = Matrix4.clone(fromENU, new Matrix4());\n\n    const translationMatrix = Matrix4.fromTranslation(minimum, matrix4Scratch);\n    const scaleMatrix = Matrix4.fromScale(dimensions, matrix4Scratch2);\n    const st = Matrix4.multiply(translationMatrix, scaleMatrix, matrix4Scratch);\n\n    Matrix4.multiply(fromENU, st, fromENU);\n    Matrix4.multiply(matrix, st, matrix);\n  }\n\n  /**\n   * How the vertices of the mesh were compressed.\n   * @type {TerrainQuantization}\n   */\n  this.quantization = quantization;\n\n  /**\n   * The minimum height of the tile including the skirts.\n   * @type {Number}\n   */\n  this.minimumHeight = minimumHeight;\n\n  /**\n   * The maximum height of the tile.\n   * @type {Number}\n   */\n  this.maximumHeight = maximumHeight;\n\n  /**\n   * The center of the tile.\n   * @type {Cartesian3}\n   */\n  this.center = Cartesian3.clone(center);\n\n  /**\n   * A matrix that takes a vertex from the tile, transforms it to east-north-up at the center and scales\n   * it so each component is in the [0, 1] range.\n   * @type {Matrix4}\n   */\n  this.toScaledENU = toENU;\n\n  /**\n   * A matrix that restores a vertex transformed with toScaledENU back to the earth fixed reference frame\n   * @type {Matrix4}\n   */\n  this.fromScaledENU = fromENU;\n\n  /**\n   * The matrix used to decompress the terrain vertices in the shader for RTE rendering.\n   * @type {Matrix4}\n   */\n  this.matrix = matrix;\n\n  /**\n   * The terrain mesh contains normals.\n   * @type {Boolean}\n   */\n  this.hasVertexNormals = hasVertexNormals;\n\n  /**\n   * The terrain mesh contains a vertical texture coordinate following the Web Mercator projection.\n   * @type {Boolean}\n   */\n  this.hasWebMercatorT = defaultValue(hasWebMercatorT, false);\n\n  /**\n   * The terrain mesh contains geodetic surface normals, used for terrain exaggeration.\n   * @type {Boolean}\n   */\n  this.hasGeodeticSurfaceNormals = defaultValue(\n    hasGeodeticSurfaceNormals,\n    false\n  );\n\n  /**\n   * A scalar used to exaggerate terrain.\n   * @type {Number}\n   */\n  this.exaggeration = defaultValue(exaggeration, 1.0);\n\n  /**\n   * The relative height from which terrain is exaggerated.\n   */\n  this.exaggerationRelativeHeight = defaultValue(\n    exaggerationRelativeHeight,\n    0.0\n  );\n\n  /**\n   * The number of components in each vertex. This value can differ with different quantizations.\n   * @type {Number}\n   */\n  this.stride = 0;\n\n  this._offsetGeodeticSurfaceNormal = 0;\n  this._offsetVertexNormal = 0;\n\n  // Calculate the stride and offsets declared above\n  this._calculateStrideAndOffsets();\n}\n\nTerrainEncoding.prototype.encode = function (\n  vertexBuffer,\n  bufferIndex,\n  position,\n  uv,\n  height,\n  normalToPack,\n  webMercatorT,\n  geodeticSurfaceNormal\n) {\n  const u = uv.x;\n  const v = uv.y;\n\n  if (this.quantization === TerrainQuantization.BITS12) {\n    position = Matrix4.multiplyByPoint(\n      this.toScaledENU,\n      position,\n      cartesian3Scratch\n    );\n\n    position.x = CesiumMath.clamp(position.x, 0.0, 1.0);\n    position.y = CesiumMath.clamp(position.y, 0.0, 1.0);\n    position.z = CesiumMath.clamp(position.z, 0.0, 1.0);\n\n    const hDim = this.maximumHeight - this.minimumHeight;\n    const h = CesiumMath.clamp((height - this.minimumHeight) / hDim, 0.0, 1.0);\n\n    Cartesian2.fromElements(position.x, position.y, cartesian2Scratch);\n    const compressed0 = AttributeCompression.compressTextureCoordinates(\n      cartesian2Scratch\n    );\n\n    Cartesian2.fromElements(position.z, h, cartesian2Scratch);\n    const compressed1 = AttributeCompression.compressTextureCoordinates(\n      cartesian2Scratch\n    );\n\n    Cartesian2.fromElements(u, v, cartesian2Scratch);\n    const compressed2 = AttributeCompression.compressTextureCoordinates(\n      cartesian2Scratch\n    );\n\n    vertexBuffer[bufferIndex++] = compressed0;\n    vertexBuffer[bufferIndex++] = compressed1;\n    vertexBuffer[bufferIndex++] = compressed2;\n\n    if (this.hasWebMercatorT) {\n      Cartesian2.fromElements(webMercatorT, 0.0, cartesian2Scratch);\n      const compressed3 = AttributeCompression.compressTextureCoordinates(\n        cartesian2Scratch\n      );\n      vertexBuffer[bufferIndex++] = compressed3;\n    }\n  } else {\n    Cartesian3.subtract(position, this.center, cartesian3Scratch);\n\n    vertexBuffer[bufferIndex++] = cartesian3Scratch.x;\n    vertexBuffer[bufferIndex++] = cartesian3Scratch.y;\n    vertexBuffer[bufferIndex++] = cartesian3Scratch.z;\n    vertexBuffer[bufferIndex++] = height;\n    vertexBuffer[bufferIndex++] = u;\n    vertexBuffer[bufferIndex++] = v;\n\n    if (this.hasWebMercatorT) {\n      vertexBuffer[bufferIndex++] = webMercatorT;\n    }\n  }\n\n  if (this.hasVertexNormals) {\n    vertexBuffer[bufferIndex++] = AttributeCompression.octPackFloat(\n      normalToPack\n    );\n  }\n\n  if (this.hasGeodeticSurfaceNormals) {\n    vertexBuffer[bufferIndex++] = geodeticSurfaceNormal.x;\n    vertexBuffer[bufferIndex++] = geodeticSurfaceNormal.y;\n    vertexBuffer[bufferIndex++] = geodeticSurfaceNormal.z;\n  }\n\n  return bufferIndex;\n};\n\nconst scratchPosition = new Cartesian3();\nconst scratchGeodeticSurfaceNormal = new Cartesian3();\n\nTerrainEncoding.prototype.addGeodeticSurfaceNormals = function (\n  oldBuffer,\n  newBuffer,\n  ellipsoid\n) {\n  if (this.hasGeodeticSurfaceNormals) {\n    return;\n  }\n\n  const oldStride = this.stride;\n  const vertexCount = oldBuffer.length / oldStride;\n  this.hasGeodeticSurfaceNormals = true;\n  this._calculateStrideAndOffsets();\n  const newStride = this.stride;\n\n  for (let index = 0; index < vertexCount; index++) {\n    for (let offset = 0; offset < oldStride; offset++) {\n      const oldIndex = index * oldStride + offset;\n      const newIndex = index * newStride + offset;\n      newBuffer[newIndex] = oldBuffer[oldIndex];\n    }\n    const position = this.decodePosition(newBuffer, index, scratchPosition);\n    const geodeticSurfaceNormal = ellipsoid.geodeticSurfaceNormal(\n      position,\n      scratchGeodeticSurfaceNormal\n    );\n\n    const bufferIndex = index * newStride + this._offsetGeodeticSurfaceNormal;\n    newBuffer[bufferIndex] = geodeticSurfaceNormal.x;\n    newBuffer[bufferIndex + 1] = geodeticSurfaceNormal.y;\n    newBuffer[bufferIndex + 2] = geodeticSurfaceNormal.z;\n  }\n};\n\nTerrainEncoding.prototype.removeGeodeticSurfaceNormals = function (\n  oldBuffer,\n  newBuffer\n) {\n  if (!this.hasGeodeticSurfaceNormals) {\n    return;\n  }\n\n  const oldStride = this.stride;\n  const vertexCount = oldBuffer.length / oldStride;\n  this.hasGeodeticSurfaceNormals = false;\n  this._calculateStrideAndOffsets();\n  const newStride = this.stride;\n\n  for (let index = 0; index < vertexCount; index++) {\n    for (let offset = 0; offset < newStride; offset++) {\n      const oldIndex = index * oldStride + offset;\n      const newIndex = index * newStride + offset;\n      newBuffer[newIndex] = oldBuffer[oldIndex];\n    }\n  }\n};\n\nTerrainEncoding.prototype.decodePosition = function (buffer, index, result) {\n  if (!defined(result)) {\n    result = new Cartesian3();\n  }\n\n  index *= this.stride;\n\n  if (this.quantization === TerrainQuantization.BITS12) {\n    const xy = AttributeCompression.decompressTextureCoordinates(\n      buffer[index],\n      cartesian2Scratch\n    );\n    result.x = xy.x;\n    result.y = xy.y;\n\n    const zh = AttributeCompression.decompressTextureCoordinates(\n      buffer[index + 1],\n      cartesian2Scratch\n    );\n    result.z = zh.x;\n\n    return Matrix4.multiplyByPoint(this.fromScaledENU, result, result);\n  }\n\n  result.x = buffer[index];\n  result.y = buffer[index + 1];\n  result.z = buffer[index + 2];\n  return Cartesian3.add(result, this.center, result);\n};\n\nTerrainEncoding.prototype.getExaggeratedPosition = function (\n  buffer,\n  index,\n  result\n) {\n  result = this.decodePosition(buffer, index, result);\n\n  const exaggeration = this.exaggeration;\n  const exaggerationRelativeHeight = this.exaggerationRelativeHeight;\n  const hasExaggeration = exaggeration !== 1.0;\n  if (hasExaggeration && this.hasGeodeticSurfaceNormals) {\n    const geodeticSurfaceNormal = this.decodeGeodeticSurfaceNormal(\n      buffer,\n      index,\n      scratchGeodeticSurfaceNormal\n    );\n    const rawHeight = this.decodeHeight(buffer, index);\n    const heightDifference =\n      TerrainExaggeration.getHeight(\n        rawHeight,\n        exaggeration,\n        exaggerationRelativeHeight\n      ) - rawHeight;\n\n    // some math is unrolled for better performance\n    result.x += geodeticSurfaceNormal.x * heightDifference;\n    result.y += geodeticSurfaceNormal.y * heightDifference;\n    result.z += geodeticSurfaceNormal.z * heightDifference;\n  }\n\n  return result;\n};\n\nTerrainEncoding.prototype.decodeTextureCoordinates = function (\n  buffer,\n  index,\n  result\n) {\n  if (!defined(result)) {\n    result = new Cartesian2();\n  }\n\n  index *= this.stride;\n\n  if (this.quantization === TerrainQuantization.BITS12) {\n    return AttributeCompression.decompressTextureCoordinates(\n      buffer[index + 2],\n      result\n    );\n  }\n\n  return Cartesian2.fromElements(buffer[index + 4], buffer[index + 5], result);\n};\n\nTerrainEncoding.prototype.decodeHeight = function (buffer, index) {\n  index *= this.stride;\n\n  if (this.quantization === TerrainQuantization.BITS12) {\n    const zh = AttributeCompression.decompressTextureCoordinates(\n      buffer[index + 1],\n      cartesian2Scratch\n    );\n    return (\n      zh.y * (this.maximumHeight - this.minimumHeight) + this.minimumHeight\n    );\n  }\n\n  return buffer[index + 3];\n};\n\nTerrainEncoding.prototype.decodeWebMercatorT = function (buffer, index) {\n  index *= this.stride;\n\n  if (this.quantization === TerrainQuantization.BITS12) {\n    return AttributeCompression.decompressTextureCoordinates(\n      buffer[index + 3],\n      cartesian2Scratch\n    ).x;\n  }\n\n  return buffer[index + 6];\n};\n\nTerrainEncoding.prototype.getOctEncodedNormal = function (\n  buffer,\n  index,\n  result\n) {\n  index = index * this.stride + this._offsetVertexNormal;\n\n  const temp = buffer[index] / 256.0;\n  const x = Math.floor(temp);\n  const y = (temp - x) * 256.0;\n\n  return Cartesian2.fromElements(x, y, result);\n};\n\nTerrainEncoding.prototype.decodeGeodeticSurfaceNormal = function (\n  buffer,\n  index,\n  result\n) {\n  index = index * this.stride + this._offsetGeodeticSurfaceNormal;\n\n  result.x = buffer[index];\n  result.y = buffer[index + 1];\n  result.z = buffer[index + 2];\n  return result;\n};\n\nTerrainEncoding.prototype._calculateStrideAndOffsets = function () {\n  let vertexStride = 0;\n\n  switch (this.quantization) {\n    case TerrainQuantization.BITS12:\n      vertexStride += 3;\n      break;\n    default:\n      vertexStride += 6;\n  }\n  if (this.hasWebMercatorT) {\n    vertexStride += 1;\n  }\n  if (this.hasVertexNormals) {\n    this._offsetVertexNormal = vertexStride;\n    vertexStride += 1;\n  }\n  if (this.hasGeodeticSurfaceNormals) {\n    this._offsetGeodeticSurfaceNormal = vertexStride;\n    vertexStride += 3;\n  }\n\n  this.stride = vertexStride;\n};\n\nconst attributesIndicesNone = {\n  position3DAndHeight: 0,\n  textureCoordAndEncodedNormals: 1,\n  geodeticSurfaceNormal: 2,\n};\nconst attributesIndicesBits12 = {\n  compressed0: 0,\n  compressed1: 1,\n  geodeticSurfaceNormal: 2,\n};\n\nTerrainEncoding.prototype.getAttributes = function (buffer) {\n  const datatype = ComponentDatatype.FLOAT;\n  const sizeInBytes = ComponentDatatype.getSizeInBytes(datatype);\n  const strideInBytes = this.stride * sizeInBytes;\n  let offsetInBytes = 0;\n\n  const attributes = [];\n  function addAttribute(index, componentsPerAttribute) {\n    attributes.push({\n      index: index,\n      vertexBuffer: buffer,\n      componentDatatype: datatype,\n      componentsPerAttribute: componentsPerAttribute,\n      offsetInBytes: offsetInBytes,\n      strideInBytes: strideInBytes,\n    });\n    offsetInBytes += componentsPerAttribute * sizeInBytes;\n  }\n\n  if (this.quantization === TerrainQuantization.NONE) {\n    addAttribute(attributesIndicesNone.position3DAndHeight, 4);\n\n    let componentsTexCoordAndNormals = 2;\n    componentsTexCoordAndNormals += this.hasWebMercatorT ? 1 : 0;\n    componentsTexCoordAndNormals += this.hasVertexNormals ? 1 : 0;\n    addAttribute(\n      attributesIndicesNone.textureCoordAndEncodedNormals,\n      componentsTexCoordAndNormals\n    );\n\n    if (this.hasGeodeticSurfaceNormals) {\n      addAttribute(attributesIndicesNone.geodeticSurfaceNormal, 3);\n    }\n  } else {\n    // When there is no webMercatorT or vertex normals, the attribute only needs 3 components: x/y, z/h, u/v.\n    // WebMercatorT and vertex normals each take up one component, so if only one of them is present the first\n    // attribute gets a 4th component. If both are present, we need an additional attribute that has 1 component.\n    const usingAttribute0Component4 =\n      this.hasWebMercatorT || this.hasVertexNormals;\n    const usingAttribute1Component1 =\n      this.hasWebMercatorT && this.hasVertexNormals;\n    addAttribute(\n      attributesIndicesBits12.compressed0,\n      usingAttribute0Component4 ? 4 : 3\n    );\n\n    if (usingAttribute1Component1) {\n      addAttribute(attributesIndicesBits12.compressed1, 1);\n    }\n\n    if (this.hasGeodeticSurfaceNormals) {\n      addAttribute(attributesIndicesBits12.geodeticSurfaceNormal, 3);\n    }\n  }\n\n  return attributes;\n};\n\nTerrainEncoding.prototype.getAttributeLocations = function () {\n  if (this.quantization === TerrainQuantization.NONE) {\n    return attributesIndicesNone;\n  }\n  return attributesIndicesBits12;\n};\n\nTerrainEncoding.clone = function (encoding, result) {\n  if (!defined(encoding)) {\n    return undefined;\n  }\n  if (!defined(result)) {\n    result = new TerrainEncoding();\n  }\n\n  result.quantization = encoding.quantization;\n  result.minimumHeight = encoding.minimumHeight;\n  result.maximumHeight = encoding.maximumHeight;\n  result.center = Cartesian3.clone(encoding.center);\n  result.toScaledENU = Matrix4.clone(encoding.toScaledENU);\n  result.fromScaledENU = Matrix4.clone(encoding.fromScaledENU);\n  result.matrix = Matrix4.clone(encoding.matrix);\n  result.hasVertexNormals = encoding.hasVertexNormals;\n  result.hasWebMercatorT = encoding.hasWebMercatorT;\n  result.hasGeodeticSurfaceNormals = encoding.hasGeodeticSurfaceNormals;\n  result.exaggeration = encoding.exaggeration;\n  result.exaggerationRelativeHeight = encoding.exaggerationRelativeHeight;\n\n  result._calculateStrideAndOffsets();\n\n  return result;\n};\nexport default TerrainEncoding;\n"], "names": ["EllipsoidalOccluder", "ellipsoid", "cameraPosition", "Check", "typeOf", "object", "this", "_ellipsoid", "_cameraPosition", "Cartesian3", "_cameraPositionInScaledSpace", "_distanceToLimbInScaledSpaceSquared", "defined", "Object", "defineProperties", "prototype", "get", "set", "cv", "transformPositionToScaledSpace", "vhMagnitudeSquared", "magnitudeSquared", "clone", "scratchCartesian", "isPointVisible", "occludee", "isScaledSpacePointVisible", "occludeeScaledSpacePosition", "scratchCameraPositionInScaledSpaceShrunk", "isScaledSpacePointVisiblePossiblyUnderEllipsoid", "minimumHeight", "minimumRadius", "x", "radii", "y", "z", "computeHorizonCullingPoint", "directionToPoint", "positions", "result", "computeHorizonCullingPointFromPositions", "scratchEllipsoidShrunk", "Ellipsoid", "UNIT_SPHERE", "computeHorizonCullingPointPossiblyUnderEllipsoid", "getPossiblyShrunkEllipsoid", "computeHorizonCullingPointFromVertices", "vertices", "stride", "center", "computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid", "subsampleScratch", "computeHorizonCullingPointFromRectangle", "rectangle", "Rectangle", "subsample", "bs", "BoundingSphere", "fromPoints", "magnitude", "scratchEllipsoidShrunkRadii", "ellipsoidShrunkRadii", "fromElements", "fromCartesian3", "scaledSpaceDirectionToPoint", "computeScaledSpaceDirectionToPoint", "resultMagnitude", "i", "len", "length", "candidate<PERSON><PERSON><PERSON><PERSON>", "computeMagnitude", "Math", "max", "magnitudeToPoint", "position<PERSON><PERSON><PERSON>", "number", "defaultValue", "ZERO", "cameraPositionInScaledSpace", "distanceToLimbInScaledSpaceSquared", "vt", "subtract", "vtDotVc", "dot", "scaledSpaceScratch", "directionS<PERSON><PERSON>", "position", "scaledSpacePosition", "sqrt", "direction", "divideByScalar", "cosBeta", "cross", "multiplyByScalar", "directionToPointScratch", "equals", "normalize", "TerrainExaggeration", "height", "scale", "relativeHeight", "scratchCartographic", "getPosition", "terrainExaggeration", "terrainExaggerationRelativeHeight", "cartographic", "cartesianToCartographic", "newHeight", "getHeight", "fromRadians", "longitude", "latitude", "TerrainQuantization$1", "freeze", "NONE", "BITS12", "cartesian3Scratch", "cartesian3DimScratch", "cartesian2Scratch", "Cartesian2", "matrix4Scratch", "Matrix4", "matrix4Scratch2", "SHIFT_LEFT_12", "pow", "TerrainEncoding", "axisAlignedBoundingBox", "maximumHeight", "fromENU", "hasVertexNormals", "hasWebMercatorT", "hasGeodeticSurfaceNormals", "exaggeration", "exaggerationRelativeHeight", "toENU", "matrix", "quantization", "TerrainQuantization", "minimum", "maximum", "dimensions", "hDim", "maximumComponent", "inverseTransformation", "translation", "negate", "multiply", "fromTranslation", "fromScale", "setTranslation", "translationMatrix", "scaleMatrix", "st", "toScaledENU", "fromScaledENU", "_offsetGeodeticSurfaceNormal", "_offsetVertexNormal", "_calculateStrideAndOffsets", "encode", "vertexBuffer", "bufferIndex", "uv", "normalToPack", "webMercatorT", "geodeticSurfaceNormal", "u", "v", "multiplyByPoint", "CesiumMath", "clamp", "h", "compressed0", "AttributeCompression", "compressTextureCoordinates", "compressed1", "compressed2", "compressed3", "octPackFloat", "scratchPosition", "scratchGeodeticSurfaceNormal", "addGeodeticSurfaceNormals", "<PERSON><PERSON><PERSON><PERSON>", "new<PERSON>uffer", "<PERSON><PERSON><PERSON><PERSON>", "vertexCount", "<PERSON><PERSON><PERSON><PERSON>", "index", "offset", "oldIndex", "decodePosition", "removeGeodeticSurfaceNormals", "buffer", "xy", "decompressTextureCoordinates", "zh", "add", "getExaggeratedPosition", "decodeGeodeticSurfaceNormal", "rawHeight", "decodeHeight", "heightDifference", "decodeTextureCoordinates", "decodeWebMercatorT", "getOctEncodedNormal", "temp", "floor", "vertexStride", "attributesIndicesNone", "position3DAndHeight", "textureCoordAndEncodedNormals", "attributesIndicesBits12", "getAttributes", "datatype", "ComponentDatatype", "FLOAT", "sizeInBytes", "getSizeInBytes", "strideInBytes", "offsetInBytes", "attributes", "addAttribute", "componentsPerAttribute", "push", "componentDatatype", "componentsTexCoordAndNormals", "usingAttribute0Component4", "usingAttribute1Component1", "getAttributeLocations", "encoding"], "mappings": "mNA+BA,SAASA,EAAoBC,EAAWC,GAEtCC,EAAAA,MAAMC,OAAOC,OAAO,YAAaJ,GAGjCK,KAAKC,WAAaN,EAClBK,KAAKE,gBAAkB,IAAIC,EAAAA,WAC3BH,KAAKI,6BAA+B,IAAID,EAAAA,WACxCH,KAAKK,oCAAsC,EAGvCC,EAAAA,QAAQV,KACVI,KAAKJ,eAAiBA,EAE1B,CAEAW,OAAOC,iBAAiBd,EAAoBe,UAAW,CAMrDd,UAAW,CACTe,IAAK,WACH,OAAOV,KAAKC,UACb,GAOHL,eAAgB,CACdc,IAAK,WACH,OAAOV,KAAKE,eACb,EACDS,IAAK,SAAUf,GAEb,MACMgB,EADYZ,KAAKC,WACFY,+BACnBjB,EACAI,KAAKI,8BAEDU,EAAqBX,EAAUA,WAACY,iBAAiBH,GAAM,EAE7DT,EAAAA,WAAWa,MAAMpB,EAAgBI,KAAKE,iBACtCF,KAAKI,6BAA+BQ,EACpCZ,KAAKK,oCAAsCS,CAC5C,KAIL,MAAMG,EAAmB,IAAId,EAAAA,WAe7BT,EAAoBe,UAAUS,eAAiB,SAAUC,GAMvD,OAAOC,EALWpB,KAAKC,WACuBY,+BAC5CM,EACAF,GAIAjB,KAAKI,6BACLJ,KAAKK,oCAET,EAkBAX,EAAoBe,UAAUW,0BAA4B,SACxDC,GAEA,OAAOD,EACLC,EACArB,KAAKI,6BACLJ,KAAKK,oCAET,EAEA,MAAMiB,EAA2C,IAAInB,EAAAA,WAYrDT,EAAoBe,UAAUc,gDAAkD,SAC9EF,EACAG,GAEA,MAAM7B,EAAYK,KAAKC,WACvB,IAAIa,EACAF,EAkBJ,OAfEN,EAAAA,QAAQkB,IACRA,EAAgB,GAChB7B,EAAU8B,eAAiBD,GAG3BZ,EAAKU,EACLV,EAAGc,EAAI1B,KAAKE,gBAAgBwB,GAAK/B,EAAUgC,MAAMD,EAAIF,GACrDZ,EAAGgB,EAAI5B,KAAKE,gBAAgB0B,GAAKjC,EAAUgC,MAAMC,EAAIJ,GACrDZ,EAAGiB,EAAI7B,KAAKE,gBAAgB2B,GAAKlC,EAAUgC,MAAME,EAAIL,GACrDV,EAAqBF,EAAGc,EAAId,EAAGc,EAAId,EAAGgB,EAAIhB,EAAGgB,EAAIhB,EAAGiB,EAAIjB,EAAGiB,EAAI,IAE/DjB,EAAKZ,KAAKI,6BACVU,EAAqBd,KAAKK,qCAGrBe,EACLC,EACAT,EACAE,EAEJ,EAkBApB,EAAoBe,UAAUqB,2BAA6B,SACzDC,EACAC,EACAC,GAEA,OAAOC,EACLlC,KAAKC,WACL8B,EACAC,EACAC,EAEJ,EAEA,MAAME,EAAyBC,EAAAA,UAAUpB,MAAMoB,EAASA,UAACC,aAmBzD3C,EAAoBe,UAAU6B,iDAAmD,SAC/EP,EACAC,EACAR,EACAS,GAOA,OAAOC,EALyBK,EAC9BvC,KAAKC,WACLuB,EACAW,GAIAJ,EACAC,EACAC,EAEJ,EAmBAvC,EAAoBe,UAAU+B,uCAAyC,SACrET,EACAU,EACAC,EACAC,EACAV,GAEA,OAAOO,EACLxC,KAAKC,WACL8B,EACAU,EACAC,EACAC,EACAV,EAEJ,EAqBAvC,EAAoBe,UAAUmC,6DAA+D,SAC3Fb,EACAU,EACAC,EACAC,EACAnB,EACAS,GAOA,OAAOO,EALyBD,EAC9BvC,KAAKC,WACLuB,EACAW,GAIAJ,EACAU,EACAC,EACAC,EACAV,EAEJ,EAEA,MAAMY,EAAmB,GAczBnD,EAAoBe,UAAUqC,wCAA0C,SACtEC,EACApD,EACAsC,GAGApC,EAAAA,MAAMC,OAAOC,OAAO,YAAagD,GAGjC,MAAMf,EAAYgB,EAAAA,UAAUC,UAC1BF,EACApD,EACA,EACAkD,GAEIK,EAAKC,EAAAA,eAAeC,WAAWpB,GAIrC,KAAI7B,EAAAA,WAAWkD,UAAUH,EAAGP,QAAU,GAAMhD,EAAU8B,eAItD,OAAOzB,KAAK8B,2BAA2BoB,EAAGP,OAAQX,EAAWC,EAC/D,EAEA,MAAMqB,EAA8B,IAAInD,EAAAA,WAExC,SAASoC,EAA2B5C,EAAW6B,EAAeS,GAC5D,GACE3B,EAAAA,QAAQkB,IACRA,EAAgB,GAChB7B,EAAU8B,eAAiBD,EAC3B,CACA,MAAM+B,EAAuBpD,EAAAA,WAAWqD,aACtC7D,EAAUgC,MAAMD,EAAIF,EACpB7B,EAAUgC,MAAMC,EAAIJ,EACpB7B,EAAUgC,MAAME,EAAIL,EACpB8B,GAEF3D,EAAYyC,EAAAA,UAAUqB,eAAeF,EAAsBtB,EAC5D,CACD,OAAOtC,CACT,CAEA,SAASuC,EACPvC,EACAoC,EACAC,EACAC,GAGApC,EAAAA,MAAMC,OAAOC,OAAO,mBAAoBgC,GACxClC,EAAAA,MAAMS,QAAQ,YAAa0B,GAGtB1B,EAAAA,QAAQ2B,KACXA,EAAS,IAAI9B,EAAAA,YAGf,MAAMuD,EAA8BC,EAClChE,EACAoC,GAEF,IAAI6B,EAAkB,EAEtB,IAAK,IAAIC,EAAI,EAAGC,EAAM9B,EAAU+B,OAAQF,EAAIC,IAAOD,EAAG,CACpD,MACMG,EAAqBC,EACzBtE,EAFeqC,EAAU6B,GAIzBH,GAEF,GAAIM,EAAqB,EAEvB,OAEFJ,EAAkBM,KAAKC,IAAIP,EAAiBI,EAC7C,CAED,OAAOI,EAAiBV,EAA6BE,EAAiB3B,EACxE,CAEA,MAAMoC,EAAkB,IAAIlE,EAAAA,WAE5B,SAASqC,EACP7C,EACAoC,EACAU,EACAC,EACAC,EACAV,GAGApC,EAAAA,MAAMC,OAAOC,OAAO,mBAAoBgC,GACxClC,EAAAA,MAAMS,QAAQ,WAAYmC,GAC1B5C,EAAAA,MAAMC,OAAOwE,OAAO,SAAU5B,GAGzBpC,EAAAA,QAAQ2B,KACXA,EAAS,IAAI9B,EAAAA,YAGfuC,EAAS6B,EAAYA,aAAC7B,EAAQ,GAC9BC,EAAS4B,EAAAA,aAAa5B,EAAQxC,EAAUA,WAACqE,MACzC,MAAMd,EAA8BC,EAClChE,EACAoC,GAEF,IAAI6B,EAAkB,EAEtB,IAAK,IAAIC,EAAI,EAAGC,EAAMrB,EAASsB,OAAQF,EAAIC,EAAKD,GAAKnB,EAAQ,CAC3D2B,EAAgB3C,EAAIe,EAASoB,GAAKlB,EAAOjB,EACzC2C,EAAgBzC,EAAIa,EAASoB,EAAI,GAAKlB,EAAOf,EAC7CyC,EAAgBxC,EAAIY,EAASoB,EAAI,GAAKlB,EAAOd,EAE7C,MAAMmC,EAAqBC,EACzBtE,EACA0E,EACAX,GAEF,GAAIM,EAAqB,EAEvB,OAEFJ,EAAkBM,KAAKC,IAAIP,EAAiBI,EAC7C,CAED,OAAOI,EAAiBV,EAA6BE,EAAiB3B,EACxE,CAEA,SAASb,EACPC,EACAoD,EACAC,GAGA,MAAM9D,EAAK6D,EACL3D,EAAqB4D,EACrBC,EAAKxE,EAAAA,WAAWyE,SACpBvD,EACAT,EACAK,GAEI4D,GAAW1E,EAAUA,WAAC2E,IAAIH,EAAI/D,GASpC,QALEE,EAAqB,EACjB+D,EAAU,EACVA,EAAU/D,GACT+D,EAAUA,EAAW1E,aAAWY,iBAAiB4D,GAChD7D,EAEV,CAEA,MAAMiE,EAAqB,IAAI5E,EAAAA,WACzB6E,EAAmB,IAAI7E,EAAAA,WAE7B,SAAS8D,EAAiBtE,EAAWsF,EAAUvB,GAC7C,MAAMwB,EAAsBvF,EAAUkB,+BACpCoE,EACAF,GAEF,IAAIhE,EAAmBZ,EAAAA,WAAWY,iBAAiBmE,GAC/C7B,EAAYa,KAAKiB,KAAKpE,GAC1B,MAAMqE,EAAYjF,EAAAA,WAAWkF,eAC3BH,EACA7B,EACA2B,GAIFjE,EAAmBmD,KAAKC,IAAI,EAAKpD,GACjCsC,EAAYa,KAAKC,IAAI,EAAKd,GAE1B,MAIMiC,EAAU,EAAMjC,EAGtB,OAAO,GAPUlD,EAAUA,WAAC2E,IAAIM,EAAW1B,GAOlB4B,EANRnF,EAAAA,WAAWkD,UAC1BlD,EAAAA,WAAWoF,MAAMH,EAAW1B,EAA6B0B,KAG3ClB,KAAKiB,KAAKpE,EAAmB,GAAOuE,GAGtD,CAEA,SAASlB,EACPV,EACAE,EACA3B,GAIA,KACE2B,GAAmB,GACnBA,IAAoB,KACpBA,GAAoBA,GAKtB,OAAOzD,EAAUA,WAACqF,iBAChB9B,EACAE,EACA3B,EAEJ,CAEA,MAAMwD,EAA0B,IAAItF,EAAAA,WAEpC,SAASwD,EAAmChE,EAAWoC,GACrD,OAAI5B,EAAUA,WAACuF,OAAO3D,EAAkB5B,EAAUA,WAACqE,MAC1CzC,GAGTpC,EAAUkB,+BACRkB,EACA0D,GAEKtF,aAAWwF,UAAUF,EAAyBA,GACvD,CCxiBA,MAAMG,EAAsB,CAS5BA,UAAgC,SAAUC,EAAQC,EAAOC,GACvD,OAAQF,EAASE,GAAkBD,EAAQC,CAC7C,GAEMC,EAAsB,IAAI7F,EAAAA,WAKhCyF,EAAoBK,YAAc,SAChChB,EACAtF,EACAuG,EACAC,EACAlE,GAEA,MAAMmE,EAAezG,EAAU0G,wBAC7BpB,EACAe,GAEIM,EAAYV,EAAoBW,UACpCH,EAAaP,OACbK,EACAC,GAEF,OAAOhG,EAAUA,WAACqG,YAChBJ,EAAaK,UACbL,EAAaM,SACbJ,EACA3G,EACAsC,EAEJ,ECtBA,IAAA0E,EAAepG,OAAOqG,OAjBM,CAO1BC,KAAM,EAQNC,OAAQ,ICXV,MAAMC,EAAoB,IAAI5G,EAAAA,WACxB6G,EAAuB,IAAI7G,EAAAA,WAC3B8G,EAAoB,IAAIC,EAAAA,WACxBC,EAAiB,IAAIC,EAAAA,QACrBC,EAAkB,IAAID,EAAAA,QAEtBE,EAAgBpD,KAAKqD,IAAI,EAAK,IAsBpC,SAASC,EACP7E,EACA8E,EACAjG,EACAkG,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAEA,IACIC,EACAC,EAFAC,EAAeC,EAAoBvB,KAIvC,GACEvG,EAAAA,QAAQmH,IACRnH,EAAAA,QAAQkB,IACRlB,EAAAA,QAAQoH,IACRpH,EAAAA,QAAQqH,GACR,CACA,MAAMU,EAAUZ,EAAuBY,QACjCC,EAAUb,EAAuBa,QAEjCC,EAAapI,EAAAA,WAAWyE,SAC5B0D,EACAD,EACArB,GAEIwB,EAAOd,EAAgBlG,EAI3B2G,EAHajE,KAAKC,IAAIhE,EAAAA,WAAWsI,iBAAiBF,GAAaC,GAEpDlB,EAAgB,EACZc,EAAoBtB,OAEpBsB,EAAoBvB,KAGrCoB,EAAQb,EAAOA,QAACsB,sBAAsBf,EAAS,IAAIP,EAAAA,SAEnD,MAAMuB,EAAcxI,EAAUA,WAACyI,OAAOP,EAAStB,GAC/CK,EAAAA,QAAQyB,SACNzB,UAAQ0B,gBAAgBH,EAAaxB,GACrCc,EACAA,GAGF,MAAMnC,EAAQiB,EACdjB,EAAMpE,EAAI,EAAM6G,EAAW7G,EAC3BoE,EAAMlE,EAAI,EAAM2G,EAAW3G,EAC3BkE,EAAMjE,EAAI,EAAM0G,EAAW1G,EAC3BuF,UAAQyB,SAASzB,EAAAA,QAAQ2B,UAAUjD,EAAOqB,GAAiBc,EAAOA,GAElEC,EAASd,EAAOA,QAACpG,MAAM2G,GACvBP,EAAOA,QAAC4B,eAAed,EAAQ/H,EAAUA,WAACqE,KAAM0D,GAEhDP,EAAUP,EAAOA,QAACpG,MAAM2G,EAAS,IAAIP,EAAAA,SAErC,MAAM6B,EAAoB7B,EAAOA,QAAC0B,gBAAgBT,EAASlB,GACrD+B,EAAc9B,EAAOA,QAAC2B,UAAUR,EAAYlB,GAC5C8B,EAAK/B,EAAAA,QAAQyB,SAASI,EAAmBC,EAAa/B,GAE5DC,EAAAA,QAAQyB,SAASlB,EAASwB,EAAIxB,GAC9BP,EAAAA,QAAQyB,SAASX,EAAQiB,EAAIjB,EAC9B,CAMDlI,KAAKmI,aAAeA,EAMpBnI,KAAKwB,cAAgBA,EAMrBxB,KAAK0H,cAAgBA,EAMrB1H,KAAK2C,OAASxC,EAAAA,WAAWa,MAAM2B,GAO/B3C,KAAKoJ,YAAcnB,EAMnBjI,KAAKqJ,cAAgB1B,EAMrB3H,KAAKkI,OAASA,EAMdlI,KAAK4H,iBAAmBA,EAMxB5H,KAAK6H,gBAAkBtD,EAAAA,aAAasD,GAAiB,GAMrD7H,KAAK8H,0BAA4BvD,EAAYA,aAC3CuD,GACA,GAOF9H,KAAK+H,aAAexD,EAAAA,aAAawD,EAAc,GAK/C/H,KAAKgI,2BAA6BzD,EAAYA,aAC5CyD,EACA,GAOFhI,KAAK0C,OAAS,EAEd1C,KAAKsJ,6BAA+B,EACpCtJ,KAAKuJ,oBAAsB,EAG3BvJ,KAAKwJ,4BACP,CAEAhC,EAAgB/G,UAAUgJ,OAAS,SACjCC,EACAC,EACA1E,EACA2E,EACA/D,EACAgE,EACAC,EACAC,GAEA,MAAMC,EAAIJ,EAAGlI,EACPuI,EAAIL,EAAGhI,EAEb,GAAI5B,KAAKmI,eAAiBC,EAAoBtB,OAAQ,EACpD7B,EAAWmC,EAAOA,QAAC8C,gBACjBlK,KAAKoJ,YACLnE,EACA8B,IAGOrF,EAAIyI,EAAAA,WAAWC,MAAMnF,EAASvD,EAAG,EAAK,GAC/CuD,EAASrD,EAAIuI,EAAAA,WAAWC,MAAMnF,EAASrD,EAAG,EAAK,GAC/CqD,EAASpD,EAAIsI,EAAAA,WAAWC,MAAMnF,EAASpD,EAAG,EAAK,GAE/C,MAAM2G,EAAOxI,KAAK0H,cAAgB1H,KAAKwB,cACjC6I,EAAIF,EAAAA,WAAWC,OAAOvE,EAAS7F,KAAKwB,eAAiBgH,EAAM,EAAK,GAEtEtB,EAAUA,WAAC1D,aAAayB,EAASvD,EAAGuD,EAASrD,EAAGqF,GAChD,MAAMqD,EAAcC,EAAAA,qBAAqBC,2BACvCvD,GAGFC,EAAUA,WAAC1D,aAAayB,EAASpD,EAAGwI,EAAGpD,GACvC,MAAMwD,EAAcF,EAAAA,qBAAqBC,2BACvCvD,GAGFC,EAAAA,WAAW1D,aAAawG,EAAGC,EAAGhD,GAC9B,MAAMyD,EAAcH,EAAAA,qBAAqBC,2BACvCvD,GAOF,GAJAyC,EAAaC,KAAiBW,EAC9BZ,EAAaC,KAAiBc,EAC9Bf,EAAaC,KAAiBe,EAE1B1K,KAAK6H,gBAAiB,CACxBX,EAAAA,WAAW1D,aAAasG,EAAc,EAAK7C,GAC3C,MAAM0D,EAAcJ,EAAAA,qBAAqBC,2BACvCvD,GAEFyC,EAAaC,KAAiBgB,CAC/B,CACL,MACIxK,EAAUA,WAACyE,SAASK,EAAUjF,KAAK2C,OAAQoE,GAE3C2C,EAAaC,KAAiB5C,EAAkBrF,EAChDgI,EAAaC,KAAiB5C,EAAkBnF,EAChD8H,EAAaC,KAAiB5C,EAAkBlF,EAChD6H,EAAaC,KAAiB9D,EAC9B6D,EAAaC,KAAiBK,EAC9BN,EAAaC,KAAiBM,EAE1BjK,KAAK6H,kBACP6B,EAAaC,KAAiBG,GAgBlC,OAZI9J,KAAK4H,mBACP8B,EAAaC,KAAiBY,EAAAA,qBAAqBK,aACjDf,IAIA7J,KAAK8H,4BACP4B,EAAaC,KAAiBI,EAAsBrI,EACpDgI,EAAaC,KAAiBI,EAAsBnI,EACpD8H,EAAaC,KAAiBI,EAAsBlI,GAG/C8H,CACT,EAEA,MAAMkB,EAAkB,IAAI1K,EAAAA,WACtB2K,EAA+B,IAAI3K,EAAAA,WAEzCqH,EAAgB/G,UAAUsK,0BAA4B,SACpDC,EACAC,EACAtL,GAEA,GAAIK,KAAK8H,0BACP,OAGF,MAAMoD,EAAYlL,KAAK0C,OACjByI,EAAcH,EAAUjH,OAASmH,EACvClL,KAAK8H,2BAA4B,EACjC9H,KAAKwJ,6BACL,MAAM4B,EAAYpL,KAAK0C,OAEvB,IAAK,IAAI2I,EAAQ,EAAGA,EAAQF,EAAaE,IAAS,CAChD,IAAK,IAAIC,EAAS,EAAGA,EAASJ,EAAWI,IAAU,CACjD,MAAMC,EAAWF,EAAQH,EAAYI,EAErCL,EADiBI,EAAQD,EAAYE,GACfN,EAAUO,EACjC,CACD,MAAMtG,EAAWjF,KAAKwL,eAAeP,EAAWI,EAAOR,GACjDd,EAAwBpK,EAAUoK,sBACtC9E,EACA6F,GAGInB,EAAc0B,EAAQD,EAAYpL,KAAKsJ,6BAC7C2B,EAAUtB,GAAeI,EAAsBrI,EAC/CuJ,EAAUtB,EAAc,GAAKI,EAAsBnI,EACnDqJ,EAAUtB,EAAc,GAAKI,EAAsBlI,CACpD,CACH,EAEA2F,EAAgB/G,UAAUgL,6BAA+B,SACvDT,EACAC,GAEA,IAAKjL,KAAK8H,0BACR,OAGF,MAAMoD,EAAYlL,KAAK0C,OACjByI,EAAcH,EAAUjH,OAASmH,EACvClL,KAAK8H,2BAA4B,EACjC9H,KAAKwJ,6BACL,MAAM4B,EAAYpL,KAAK0C,OAEvB,IAAK,IAAI2I,EAAQ,EAAGA,EAAQF,EAAaE,IACvC,IAAK,IAAIC,EAAS,EAAGA,EAASF,EAAWE,IAAU,CACjD,MAAMC,EAAWF,EAAQH,EAAYI,EAErCL,EADiBI,EAAQD,EAAYE,GACfN,EAAUO,EACjC,CAEL,EAEA/D,EAAgB/G,UAAU+K,eAAiB,SAAUE,EAAQL,EAAOpJ,GAOlE,GANK3B,EAAAA,QAAQ2B,KACXA,EAAS,IAAI9B,EAAAA,YAGfkL,GAASrL,KAAK0C,OAEV1C,KAAKmI,eAAiBC,EAAoBtB,OAAQ,CACpD,MAAM6E,EAAKpB,EAAAA,qBAAqBqB,6BAC9BF,EAAOL,GACPpE,GAEFhF,EAAOP,EAAIiK,EAAGjK,EACdO,EAAOL,EAAI+J,EAAG/J,EAEd,MAAMiK,EAAKtB,EAAAA,qBAAqBqB,6BAC9BF,EAAOL,EAAQ,GACfpE,GAIF,OAFAhF,EAAOJ,EAAIgK,EAAGnK,EAEP0F,EAAAA,QAAQ8C,gBAAgBlK,KAAKqJ,cAAepH,EAAQA,EAC5D,CAKD,OAHAA,EAAOP,EAAIgK,EAAOL,GAClBpJ,EAAOL,EAAI8J,EAAOL,EAAQ,GAC1BpJ,EAAOJ,EAAI6J,EAAOL,EAAQ,GACnBlL,EAAAA,WAAW2L,IAAI7J,EAAQjC,KAAK2C,OAAQV,EAC7C,EAEAuF,EAAgB/G,UAAUsL,uBAAyB,SACjDL,EACAL,EACApJ,GAEAA,EAASjC,KAAKwL,eAAeE,EAAQL,EAAOpJ,GAE5C,MAAM8F,EAAe/H,KAAK+H,aACpBC,EAA6BhI,KAAKgI,2BAExC,GADyC,IAAjBD,GACD/H,KAAK8H,0BAA2B,CACrD,MAAMiC,EAAwB/J,KAAKgM,4BACjCN,EACAL,EACAP,GAEImB,EAAYjM,KAAKkM,aAAaR,EAAQL,GACtCc,EACJvG,EAAoBW,UAClB0F,EACAlE,EACAC,GACEiE,EAGNhK,EAAOP,GAAKqI,EAAsBrI,EAAIyK,EACtClK,EAAOL,GAAKmI,EAAsBnI,EAAIuK,EACtClK,EAAOJ,GAAKkI,EAAsBlI,EAAIsK,CACvC,CAED,OAAOlK,CACT,EAEAuF,EAAgB/G,UAAU2L,yBAA2B,SACnDV,EACAL,EACApJ,GAQA,OANK3B,EAAAA,QAAQ2B,KACXA,EAAS,IAAIiF,EAAAA,YAGfmE,GAASrL,KAAK0C,OAEV1C,KAAKmI,eAAiBC,EAAoBtB,OACrCyD,EAAoBA,qBAACqB,6BAC1BF,EAAOL,EAAQ,GACfpJ,GAIGiF,aAAW1D,aAAakI,EAAOL,EAAQ,GAAIK,EAAOL,EAAQ,GAAIpJ,EACvE,EAEAuF,EAAgB/G,UAAUyL,aAAe,SAAUR,EAAQL,GAGzD,GAFAA,GAASrL,KAAK0C,OAEV1C,KAAKmI,eAAiBC,EAAoBtB,OAAQ,CAKpD,OAJWyD,EAAAA,qBAAqBqB,6BAC9BF,EAAOL,EAAQ,GACfpE,GAGGrF,GAAK5B,KAAK0H,cAAgB1H,KAAKwB,eAAiBxB,KAAKwB,aAE3D,CAED,OAAOkK,EAAOL,EAAQ,EACxB,EAEA7D,EAAgB/G,UAAU4L,mBAAqB,SAAUX,EAAQL,GAG/D,OAFAA,GAASrL,KAAK0C,OAEV1C,KAAKmI,eAAiBC,EAAoBtB,OACrCyD,EAAoBA,qBAACqB,6BAC1BF,EAAOL,EAAQ,GACfpE,GACAvF,EAGGgK,EAAOL,EAAQ,EACxB,EAEA7D,EAAgB/G,UAAU6L,oBAAsB,SAC9CZ,EACAL,EACApJ,GAIA,MAAMsK,EAAOb,EAFbL,EAAQA,EAAQrL,KAAK0C,OAAS1C,KAAKuJ,qBAEN,IACvB7H,EAAIwC,KAAKsI,MAAMD,GACf3K,EAAiB,KAAZ2K,EAAO7K,GAElB,OAAOwF,EAAUA,WAAC1D,aAAa9B,EAAGE,EAAGK,EACvC,EAEAuF,EAAgB/G,UAAUuL,4BAA8B,SACtDN,EACAL,EACApJ,GAOA,OALAoJ,EAAQA,EAAQrL,KAAK0C,OAAS1C,KAAKsJ,6BAEnCrH,EAAOP,EAAIgK,EAAOL,GAClBpJ,EAAOL,EAAI8J,EAAOL,EAAQ,GAC1BpJ,EAAOJ,EAAI6J,EAAOL,EAAQ,GACnBpJ,CACT,EAEAuF,EAAgB/G,UAAU+I,2BAA6B,WACrD,IAAIiD,EAAe,EAEnB,GAAQzM,KAAKmI,eACNC,EAAoBtB,OACvB2F,GAAgB,OAGhBA,GAAgB,EAEhBzM,KAAK6H,kBACP4E,GAAgB,GAEdzM,KAAK4H,mBACP5H,KAAKuJ,oBAAsBkD,EAC3BA,GAAgB,GAEdzM,KAAK8H,4BACP9H,KAAKsJ,6BAA+BmD,EACpCA,GAAgB,GAGlBzM,KAAK0C,OAAS+J,CAChB,EAEA,MAAMC,EAAwB,CAC5BC,oBAAqB,EACrBC,8BAA+B,EAC/B7C,sBAAuB,GAEnB8C,EAA0B,CAC9BvC,YAAa,EACbG,YAAa,EACbV,sBAAuB,GAGzBvC,EAAgB/G,UAAUqM,cAAgB,SAAUpB,GAClD,MAAMqB,EAAWC,EAAiBA,kBAACC,MAC7BC,EAAcF,EAAAA,kBAAkBG,eAAeJ,GAC/CK,EAAgBpN,KAAK0C,OAASwK,EACpC,IAAIG,EAAgB,EAEpB,MAAMC,EAAa,GACnB,SAASC,EAAalC,EAAOmC,GAC3BF,EAAWG,KAAK,CACdpC,MAAOA,EACP3B,aAAcgC,EACdgC,kBAAmBX,EACnBS,uBAAwBA,EACxBH,cAAeA,EACfD,cAAeA,IAEjBC,GAAiBG,EAAyBN,CAC3C,CAED,GAAIlN,KAAKmI,eAAiBC,EAAoBvB,KAAM,CAClD0G,EAAab,EAAsBC,oBAAqB,GAExD,IAAIgB,EAA+B,EACnCA,GAAgC3N,KAAK6H,gBAAkB,EAAI,EAC3D8F,GAAgC3N,KAAK4H,iBAAmB,EAAI,EAC5D2F,EACEb,EAAsBE,8BACtBe,GAGE3N,KAAK8H,2BACPyF,EAAab,EAAsB3C,sBAAuB,EAEhE,KAAS,CAIL,MAAM6D,EACJ5N,KAAK6H,iBAAmB7H,KAAK4H,iBACzBiG,EACJ7N,KAAK6H,iBAAmB7H,KAAK4H,iBAC/B2F,EACEV,EAAwBvC,YACxBsD,EAA4B,EAAI,GAG9BC,GACFN,EAAaV,EAAwBpC,YAAa,GAGhDzK,KAAK8H,2BACPyF,EAAaV,EAAwB9C,sBAAuB,EAE/D,CAED,OAAOuD,CACT,EAEA9F,EAAgB/G,UAAUqN,sBAAwB,WAChD,OAAI9N,KAAKmI,eAAiBC,EAAoBvB,KACrC6F,EAEFG,CACT,EAEArF,EAAgBxG,MAAQ,SAAU+M,EAAU9L,GAC1C,GAAK3B,EAAAA,QAAQyN,GAsBb,OAnBKzN,EAAAA,QAAQ2B,KACXA,EAAS,IAAIuF,GAGfvF,EAAOkG,aAAe4F,EAAS5F,aAC/BlG,EAAOT,cAAgBuM,EAASvM,cAChCS,EAAOyF,cAAgBqG,EAASrG,cAChCzF,EAAOU,OAASxC,EAAUA,WAACa,MAAM+M,EAASpL,QAC1CV,EAAOmH,YAAchC,EAAOA,QAACpG,MAAM+M,EAAS3E,aAC5CnH,EAAOoH,cAAgBjC,EAAOA,QAACpG,MAAM+M,EAAS1E,eAC9CpH,EAAOiG,OAASd,EAAOA,QAACpG,MAAM+M,EAAS7F,QACvCjG,EAAO2F,iBAAmBmG,EAASnG,iBACnC3F,EAAO4F,gBAAkBkG,EAASlG,gBAClC5F,EAAO6F,0BAA4BiG,EAASjG,0BAC5C7F,EAAO8F,aAAegG,EAAShG,aAC/B9F,EAAO+F,2BAA6B+F,EAAS/F,2BAE7C/F,EAAOuH,6BAEAvH,CACT"}