<!--
* <AUTHOR> 杨光伟
* @Date :  2023/4/19 10:57
* @Version : 1.0
* @Content : 分割线
-->
<template>
  <div class="sim-divider">
    <div class="left">
      <svg
        width="17.000000"
        height="6.000000"
        viewBox="0 0 17 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矩形 1214">
          <path d="M13 0L0 0L0 6L17 6L13 0Z" fill-rule="evenodd" fill="var(--primary-color)" />
        </g>
        <defs />
      </svg>
      <svg
        width="8.000000"
        height="5.000000"
        viewBox="0 0 8 5"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矩形 1213">
          <path d="M4 0L0 0L4 5L8 5L4 0Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.600000" />
        </g>
        <defs />
      </svg>
      <svg
        width="8.000000"
        height="5.000000"
        viewBox="0 0 8 5"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矩形 1213">
          <path d="M4 0L0 0L4 5L8 5L4 0Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.300000" />
        </g>
        <defs />
      </svg>
    </div>
    <div class="center">
      <svg
        width="6.000000"
        height="3.000000"
        viewBox="0 0 6 3"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矢量图 Copy 2">
          <path d="M6 0L3 0L0 3L3 3L6 0Z" fill-rule="evenodd" fill="rgba(var(--primary-color-val), 0.3)" />
        </g>
        <defs />
      </svg>
      <svg
        width="6.000000"
        height="3.000000"
        viewBox="0 0 6 3"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矢量图 Copy 2">
          <path d="M6 0L3 0L0 3L3 3L6 0Z" fill-rule="evenodd" fill="rgba(var(--primary-color-val), 0.3)" />
        </g>
        <defs />
      </svg>
      <svg
        width="6.000000"
        height="3.000000"
        viewBox="0 0 6 3"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矢量图 Copy 2">
          <path d="M6 0L3 0L0 3L3 3L6 0Z" fill-rule="evenodd" fill="rgba(var(--primary-color-val), 0.3)" />
        </g>
        <defs />
      </svg>
      <svg
        width="360.000000"
        height="4.000000"
        viewBox="0 0 360 4"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矩形">
          <path d="M7.09351 0L352.904 0L359.997 4L360 4L0 4L0 4L7.09351 0Z" fill-rule="evenodd" fill="rgba(var(--primary-color-val), 0.3)" />
        </g>
        <defs />
      </svg>
      <svg
        width="6.000000"
        height="3.000000"
        viewBox="0 0 6 3"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矢量图">
          <path d="M0 0L3 0L6 3L3 3L0 0Z" fill-rule="evenodd" fill="rgba(var(--primary-color-val), 0.3)" />
        </g>
        <defs />
      </svg>
      <svg
        width="6.000000"
        height="3.000000"
        viewBox="0 0 6 3"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矢量图">
          <path d="M0 0L3 0L6 3L3 3L0 0Z" fill-rule="evenodd" fill="rgba(var(--primary-color-val), 0.3)" />
        </g>
        <defs />
      </svg>
      <svg
        width="6.000000"
        height="3.000000"
        viewBox="0 0 6 3"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矢量图">
          <path d="M0 0L3 0L6 3L3 3L0 0Z" fill-rule="evenodd" fill="rgba(var(--primary-color-val), 0.3)" />
        </g>
        <defs />
      </svg>
    </div>
    <div class="right">
      <svg
        width="8.000000"
        height="5.000000"
        viewBox="0 0 8 5"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矩形 1207">
          <path d="M4 0L8 0L4 5L0 5L4 0Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.300000" />
        </g>
        <defs />
      </svg>
      <svg
        width="8.000000"
        height="5.000000"
        viewBox="0 0 8 5"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矩形 1207">
          <path d="M4 0L8 0L4 5L0 5L4 0Z" fill-rule="evenodd" fill="var(--primary-color)" fill-opacity="0.600000" />
        </g>
        <defs />
      </svg>
      <svg
        width="17.000000"
        height="6.000000"
        viewBox="0 0 17 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <g id="矩形 1214">
          <path d="M4 0L17 0L17 6L0 6L4 0Z" fill-rule="evenodd" fill="var(--primary-color)" />
        </g>
        <defs />
      </svg>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'SimDivider'
};
</script>
<script setup lang="ts"></script>
<style lang="less" scoped>
.sim-divider {
  border-bottom: 1px solid rgba(var(--primary-color-val), 0.3);
  position: relative;
  > div {
    line-height: 1;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 0;
  }
  .left {
    left: 0;
  }
  .right {
    right: 0;
  }
  .center {
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
