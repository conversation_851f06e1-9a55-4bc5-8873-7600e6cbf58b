<template>
  <div :class="['i-ic-button', disabled && 'is__disabled', isSmall && 'i-ic-button--small']" @click="handleClick">
    <div v-if="icon || $slots.icon" :class="['i-ic-button__icon', `i-ic-button__icon--${type}`]">
      <slot name="icon">
        <i :class="icon"></i>
      </slot>
    </div>
    <div class="i-icn-button__center"></div>
    <div
      :class="['i-ic-button__text', `i-ic-button__text--${type}`, !icon && $slots.icon && `i-ic-button__text--noticon`, isSmall && 'font-size-small']"
    >
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ISimIconButton'
};
</script>
<script setup>
const props = defineProps({
  size: {
    type: String,
    default: 'default'
  },
  icon: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'primary'
  },
  disabled: Boolean,
  innerLoad: Boolean
});

const slots = useSlots();

// 判断是不是small尺寸
const isSmall = computed(() => props.size === 'small');

// 目前只有两个尺寸  small尺寸 标准尺寸
const getSizeNum = (size = 'default') => {
  return {
    small: '16px',
    default: '24px'
  }[size];
};
// 根据size取对应尺寸的高度和宽度 左侧icon的宽度和高度是相等的 所以只需要设置一个值
const sizeNum = computed(() => getSizeNum(props.size));

// 根据尺寸和是否有icon取得单独显示text的尺寸
const textWidth = computed(() => (props.icon || slots.icon ? `calc(100% - ${getSizeNum(props.size)})` : '100%'));

const emits = defineEmits(['click']);

const load = ref();
const handleClick = (e) => {
  if (props.disabled) {
    return;
  }
  if (props.innerLoad) {
    load.value = true;
    emits('click', e, () => {
      load.value = false;
    });
    return;
  }
  emits('click', e);
};
</script>
<style lang="less" scoped>
.i-ic-button {
  @width: 36px;
  display: inline-flex;
  align-items: center;
  user-select: none;
  min-height: 16px;
  white-space: nowrap;
  cursor: pointer;
  -webkit-appearance: none;
  box-sizing: border-box;
  -webkit-transition: 0.1s;
  transition: 0.1s;
  border-radius: 0;

  height: v-bind(sizeNum);
  line-height: v-bind(sizeNum);

  & + & {
    margin-left: 2px;
  }

  &:not(.is__disabled) {
    &:hover {
      opacity: 0.8;
    }
  }

  .i-ic-button__icon {
    width: v-bind(sizeNum);
    display: inline-block;
    text-align: center;
    height: 100%;
    //background: linear-gradient(90deg, var(--primary-color-7), var(--primary-color-dep2-7)), var(--isim-global-bgc);
    background: var(--isim-icon-button-bgc) 100% 100%;
  }

  .i-icn-button__center {
    display: inline-block;
    width: 2px;
    height: 80%;
    background: var(--isim-icon-button-bgc);

    //background-image: linear-gradient(
    //    var(--primary-color-dep2-7) 0,
    //    var(--primary-color-dep2-7) 100%
    //);
  }

  .i-ic-button__text {
    display: inline-block;
    position: relative;
    text-align: center;
    padding: 0 8px 0 4px;
    box-sizing: border-box;
    min-width: 40px;
    width: v-bind(textWidth);
    height: v-bind(sizeNum);
    line-height: v-bind(sizeNum);

    background: var(--isim-icon-button-bgc);

    //background-image: linear-gradient(135deg,
    //var(--primary-color-dep2-7) 0,
    //var(--primary-color-dep2-7) 40%,
    //var(--primary-color-7) 90%,
    //transparent 95%,)
  }
}
</style>
