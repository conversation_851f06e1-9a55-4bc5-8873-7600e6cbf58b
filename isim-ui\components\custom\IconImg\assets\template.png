<svg width="64.000000" height="64.000000" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<g id="实验设计模板">
		<g id="组合 3222">
			<g id="矩形 1887">
				<rect x="12.500000" y="6.500000" rx="2.000000" width="39.000000" height="53.000000" fill="#B3832C"/>
				<rect x="12.500000" y="6.500000" rx="2.000000" width="39.000000" height="53.000000" stroke="#CF9A39"/>
			</g>
			<g id="矩形 1887">
				<rect x="15.000000" y="8.000000" width="34.000000" height="49.000000" fill="#E6DECF"/>
			</g>
			<g id="矩形 1887" filter="url(#filter_271_2468_dd)">
				<rect x="18.500000" y="4.500000" rx="1.000000" width="27.000000" height="5.000000" fill="url(#paint_linear_271_2468_0)"/>
				<rect x="18.500000" y="4.500000" rx="1.000000" width="27.000000" height="5.000000" stroke="#FFFFFF"/>
			</g>
			<g id="矩形 1904">
				<rect x="18.500061" y="15.500000" width="27.000000" height="7.000000" fill="#DDCAA8"/>
				<rect x="18.500061" y="15.500000" width="27.000000" height="7.000000" stroke="#C9A564" stroke-dasharray="0 0"/>
			</g>
			<g id="矩形 1916">
				<rect x="18.500061" y="25.500000" width="27.000000" height="7.000000" fill="#DDCAA8"/>
				<rect x="18.500061" y="25.500000" width="27.000000" height="7.000000" stroke="#C9A564" stroke-dasharray="0 0"/>
			</g>
			<g id="矩形 1917">
				<rect x="18.500061" y="35.500000" width="12.000000" height="17.000000" fill="#DDCAA8"/>
				<rect x="18.500061" y="35.500000" width="12.000000" height="17.000000" stroke="#C9A564" stroke-dasharray="0 0"/>
			</g>
			<g id="矩形 1918">
				<rect x="33.500061" y="35.500000" width="12.000000" height="17.000000" fill="#DDCAA8"/>
				<rect x="33.500061" y="35.500000" width="12.000000" height="17.000000" stroke="#C9A564" stroke-dasharray="0 0"/>
			</g>
		</g>
	</g>
	<defs>
		<filter id="filter_271_2468_dd" x="18.000000" y="4.000000" width="28.000000" height="7.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
			<feOffset dx="0" dy="1"/>
			<feGaussianBlur stdDeviation="0"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<linearGradient id="paint_linear_271_2468_0" x1="32.000000" y1="4.000000" x2="32.000000" y2="9.320988" gradientUnits="userSpaceOnUse">
			<stop stop-color="#F2F2F2"/>
			<stop offset="0.265432" stop-color="#F2F2F2"/>
			<stop offset="0.664609" stop-color="#A3A3A3"/>
			<stop offset="1.000000" stop-color="#D4D4D4"/>
		</linearGradient>
	</defs>
</svg>
