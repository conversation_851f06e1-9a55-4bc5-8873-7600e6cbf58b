// @ts-nocheck
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/30 16:28:46
 * @description Base Radar Primitive
 * @version 2.0
 * */

import {
  Cartesian3,
  Color,
  defaultValue,
  JulianDate,
  Matrix3,
  Matrix4,
  Material,
  defined,
  PrimitiveCollection,
  PolylineCollection,
  Cartographic,
  Quaternion,
  Math as CesiumMath
} from 'cesium';

import YawPitchRoll from '../../Core/Transform/YawPitchRoll';
import Vector3 from '../../Core/Transform/Vector3';
import RectangularSensorPrimitive from '../../Modules/Sensor/RectangularSensorPrimitive';

import AbstractPrimitive from './AbstractPrimitive.js';

const { sin, cos, tan, atan, pow, sqrt, abs } = Math;
const matrix3Scratch = new Matrix3();
const earthRadius = 6378137.0;

export default class BaseRadarPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this.mode = defaultValue(options.mode, 1);

    this.direction = defaultValue(options.direction, YawPitchRoll.Direction.TO_NORTH);

    /**
     * track Sensor Position
     * @type {Cesium.Cartesian3}
     * @default undefined
     */
    this.cartesian = options.cartesian;
    this._cartesian = undefined;

    /**
     * Sensor Radius
     * @type {Number}
     * @default 100000
     */
    this.radius = defaultValue(options.radius, 100000);
    this._radius = undefined;

    /**
     * Sensor Params
     * @type {Array} [startAzimuth,endAzimuth,startElevation,endElevation]
     * @default undefined
     */
    this.sensorParams = options.sensorParams;
    this._sensorParams = undefined;
    this.color = defaultValue(options.color, Color.AQUA.withAlpha(0.8));
    this.sensorShow = defaultValue(options.sensorShow, true);

    /**
     * screen Params
     * @type {Array} [startScreenAzimuth,endScreenAzimuth,startScreenElevation,endScreenElevation]
     * @default undefined
     */
    this.screenParams = options.screenParams;
    this._screenParams = undefined;
    this.screenColor = defaultValue(options.screenColor, Color.RED);
    this.screenShow = defaultValue(options.screenShow, false);

    /**
     * search Params
     * @type {Array} [searchHorizonHalfAngle,searchVerticalHalfAngle,searchAzimuth,searchElevation]
     * @default undefined
     */
    this.searchParams = options.searchParams;
    this._searchParams = undefined;
    this.searchColor = defaultValue(options.searchColor, Color.LAWNGREEN);
    this.searchShow = defaultValue(options.searchShow, false);

    /**
     * sarStripe Params
     * @type {Array} [sarHorizonHalfAngle,sarVerticalHalfAngle,sarAzimuth,sarElevation]
     * @default undefined
     */
    this.sarParams = options.sarParams ?? [0, 180, 0, 180];
    this._sarParams = undefined;

    this.sarQuaternion = options.sarQuaternion;
    this._sarQuaternion = undefined;

    this.sarColor = defaultValue(options.sarColor, Color.MAGENTA);
    this.sarShow = defaultValue(options.sarShow, false);

    this.sarStripeShow = defaultValue(options.sarStripeShow, false);
    this.sarStripeColor = defaultValue(options.sarStripeColor, Color.LAWNGREEN.withAlpha(0.3));
    this.sarStripeDestroy = defaultValue(options.sarStripeDestroy, true);
    this.sarFrameInterval = defaultValue(options.sarFrameInterval, 30);

    this.scanRate = defaultValue(options.scanRate, 0);
    this._scanRateCount = 0;

    this._sarStripes = undefined;
    this._sarFrameNumber = 0;

    /**
     * track Params
     * @type {Array} [trackHorizonHalfAngle,trackVerticalHalfAngle]
     * @default [1.0,1.0]
     */
    this.trackParams = defaultValue(options.trackParams, [1.0, 1.0]);
    this._trackParams = undefined;
    this.trackTargets = defaultValue(options.trackTargets, []);
    this.trackShow = defaultValue(options.trackShow, false);
    // track sensor
    this.trackColor = defaultValue(options.trackColor, Color.YELLOW);
    this.trackSensorShow = defaultValue(options.trackSensorShow, false);
    // track line
    this.trackLineColor = defaultValue(options.trackLineColor, Color.RED);
    this.trackLineShow = defaultValue(options.trackLineShow, false);
    this._time = JulianDate.now();

    this._primitive = undefined;
    this._primitiveScreen = undefined;
    this._primitiveSearch = undefined;
    this._primitiveSar = undefined;
    this._sarStripes = undefined;
    this._primitiveTrack = undefined;
    this._lineTrack = undefined;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    this._updateScreen();
    this._updateSensor();
    this._updateSearch();
    this._updateSar(frameState);
    this._updateTrack(frameState);

    this.equal();
    this.control();
    this._lineTrack?.update(frameState);
    this._primitiveTrack?.update(frameState);
    this._primitiveSearch?.update(frameState);
    try {
      this._primitiveSar?.update(frameState);
      this._sarStripes?.update(frameState);
    } catch (e) {
      console.warn(e);
    }
    this._primitiveScreen?.update(frameState);
    this._primitive?.update(frameState);
    this._time = frameState.time;
  }

  _updateSensor() {
    if (!this.sensorShow) {
      return;
    }
    if (!defined(this._primitive) || this._sensorParams.toString() !== this.sensorParams.toString() || this._radius !== this.radius) {
      this._sensorParams = this.sensorParams;
      if (defined(this._primitive)) {
        this._primitive.destroy();
      }

      const p = getSensorMatrixParams(this.cartesian, this.sensorParams, this.direction);
      this._primitive = createSensor(
        p[0],
        CesiumMath.toRadians(p[1].xHalfAngle),
        CesiumMath.toRadians(p[1].yHalfAngle),
        this.radius,
        this.color,
        0.1,
        { showScanPlane: false }
      );
      return;
    }

    if (!Cartesian3.equals(this._cartesian, this.cartesian)) {
      const p = getSensorMatrixParams(this.cartesian, this.sensorParams, this.direction);
      this._primitive.modelMatrix = p[0];
    }
  }

  _updateScreen() {
    if (!this.screenShow) {
      return;
    }

    if (
      !defined(this._primitiveScreen) ||
      this._screenParams.toString() !== this.screenParams.toString() ||
      this._sensorParams.toString() !== this.sensorParams.toString() ||
      this._radius !== this.radius
    ) {
      if (defined(this._primitiveScreen)) {
        this._primitiveScreen.destroy();
      }
      this._screenParams = this.screenParams;
      const p = getScreenMatrixParams(this.cartesian, this.sensorParams, this.screenParams, this.direction);
      this._primitiveScreen = createSensor(p[0], p[1].xHalfAngle, p[1].yHalfAngle, this.radius, this.screenColor, 0.4, { slice: 20 });
      return;
    }

    if (!Cartesian3.equals(this._cartesian, this.cartesian)) {
      const p = getScreenMatrixParams(this.cartesian, this.sensorParams, this.screenParams, this.direction);
      this._primitiveScreen.modelMatrix = p[0];
    }
  }

  _updateSearch() {
    if (!this.searchShow) {
      return;
    }

    if (!defined(this._primitiveSearch) || this._searchParams.toString() !== this.searchParams.toString() || this._radius !== this.radius) {
      if (defined(this._primitiveSearch)) {
        this._primitiveSearch = this._primitiveSearch && this._primitiveSearch.destroy();
      }
      this._searchParams = this.searchParams;
      const p = getSearchMatrixParams(this.cartesian, this.searchParams, this.direction);
      this._primitiveSearch = createSensor(
        p[0],
        CesiumMath.toRadians(p[1].xHalfAngle),
        CesiumMath.toRadians(p[1].yHalfAngle),
        this.radius,
        this.searchColor,
        0.3,
        {
          slice: 20,
          showScanPlane: false,
          showDomeLines: false,
          intersectionWidth: 2.0
        }
      );
      return;
    }

    if (!Cartesian3.equals(this._cartesian, this.cartesian)) {
      const p = getSearchMatrixParams(this.cartesian, this.searchParams, this.direction);
      this._primitiveSearch.modelMatrix = p[0];
    }
  }

  _updateTrack(frameState) {
    if (this.trackShow && this.trackTargets.length > 0) {
      if (defined(this._primitiveTrack)) {
        this._primitiveTrack.destroy();
      }

      if (this.trackSensorShow) {
        this._trackParams = this.trackParams;
        this._primitiveTrack = new PrimitiveCollection();
      }

      if (defined(this._lineTrack)) {
        this._lineTrack.destroy();
      }

      if (this.trackLineShow) {
        this._lineTrack = new PolylineCollection();
      }

      createTrack(
        this._primitiveTrack,
        this._lineTrack,
        this.cartesian,
        this.trackParams,
        this.trackTargets,
        frameState.time,
        this.trackColor,
        this.trackLineColor
      );
    }
  }

  _updateSar(frameState) {
    if (!this.sarShow) {
      return;
    }

    if (
      !defined(this._primitiveSar) ||
      this._sarParams.toString() !== this.sarParams.toString() ||
      !Cartesian3.equals(this._cartesian, this.cartesian)
    ) {
      if (this._cartesian && this.cartesian) {
        if (!Cartesian3.equals(this._cartesian, this.cartesian) || !Quaternion.equals(this._sarQuaternion, this.sarQuaternion)) {
          if (defined(this._primitiveSar)) {
            this._primitiveSar = this._primitiveSar && this._primitiveSar.destroy();
          }
          // 暂时注释
          this._updateSarStripes(frameState);
        }
      }
      this._sarParams = this.sarParams;
    }
  }

  _updateSarStripes(frameState) {
    // console.log('this.scanRate ', this.scanRate);
    if (this.scanRate === 0) {
      this._scanRateCount = 0;
      const p = getSarMatrixParams(this.scanRate, this.cartesian, this.sarQuaternion, this.sarParams, 0);
      this._primitiveSar = createSensor(p[0], p[2].xHalfAngle, p[2].yHalfAngle, p[1], this.sarColor, 0.3, {
        slice: 20,
        showScanPlane: false,
        showDomeLines: false,
        intersectionWidth: 2.0
        // showThroughEllipsoid: true
      });
    } else {
      const count =
        this._scanRateCount === this.scanRate
          ? this._scanRateCount
          : ((abs(this.scanRate) / this.scanRate) * (frameState.frameNumber - this._sarFrameNumber)) / this.sarFrameInterval + this._scanRateCount;
      const p = getSarMatrixParams(this.scanRate, this.cartesian, this.sarQuaternion, this.sarParams, count);
      this._primitiveSar = createSensor(p[0], p[2].xHalfAngle, p[2].yHalfAngle, p[1], this.sarColor, 0.3, {
        slice: 20,
        showScanPlane: false,
        showDomeLines: false,
        intersectionWidth: 2.0
      });
    }

    if (!this._sarFrameNumber || frameState.frameNumber - this._sarFrameNumber > this.sarFrameInterval) {
      const p = getSarMatrixParams(this.scanRate, this.cartesian, this.sarQuaternion, this.sarParams, this._scanRateCount);
      // p[1] = p[1] < 0 ? 0 : p[1];
      if (this.sarStripeShow && !p[3]) {
        // console.log(p[3]);
        if (!this._sarStripes) {
          this._sarStripes = new PrimitiveCollection();
        }
        this._sarStripes.add(
          createSensor(p[0], p[2].xHalfAngle, p[2].yHalfAngle, p[1], Color.WHITE.withAlpha(0), 0.0, {
            slice: 20,
            showScanPlane: false,
            showDomeLines: false,
            intersectionWidth: 2.0,
            intersectionColor: this.sarStripeColor
          })
        );
      }
      this._sarRadius = p[1];
      this._sarFrameNumber = frameState.frameNumber;
      this._updateSarScanRateBySingleWay();
    }
  }

  _updateSarScanRateBySingleWay() {
    if (this.scanRate === 0) {
      return;
    }
    if (this.scanRate > 0) {
      if (this._scanRateCount < this.scanRate) {
        this._scanRateCount++;
      } else {
        this._scanRateCount = 0;
      }
    } else if (this._scanRateCount > this.scanRate) {
      this._scanRateCount--;
    } else {
      this._scanRateCount = 0;
    }
  }

  equal() {
    this._radius = this.radius;
    this._cartesian = this.cartesian;
  }

  destroy() {
    this._primitiveScreen = this._primitiveScreen && this._primitiveScreen.destroy();
    this._primitiveSearch = this._primitiveSearch && this._primitiveSearch.destroy();
    this._primitiveTrack = this._primitiveTrack && this._primitiveTrack.destroy();
    this._primitiveSar = this._primitiveSar && this._primitiveSar.destroy();
    if (this.sarStripeDestroy) {
      this._sarStripes = this._sarStripes && this._sarStripes.destroy();
    }
    this._lineTrack = this._lineTrack && this._lineTrack.destroy();
    return super.destroy();
  }

  control() {
    if (!this.sensorShow) {
      this._primitive = this._primitive && this._primitive?.destroy();
    }

    if (!this.screenShow) {
      this._primitiveScreen = this._primitiveScreen && this._primitiveScreen?.destroy();
    }

    if (!this.searchShow) {
      this._primitiveSearch = this._primitiveSearch && this._primitiveSearch?.destroy();
    }

    if (!this.sarShow) {
      this._primitiveSar = this._primitiveSar && this._primitiveSar.destroy();
    }

    if (!this.sarStripeShow && this.sarStripeDestroy) {
      this._sarStripes = this._sarStripes && this._sarStripes.destroy();
    }

    if (!this.trackShow || !this.trackLineShow || this.trackTargets.length === 0) {
      this._lineTrack = this._lineTrack && this._lineTrack?.destroy();
    }

    if (!this.trackShow || !this.trackSensorShow || this.trackTargets.length === 0) {
      this._primitiveTrack = this._primitiveTrack && this._primitiveTrack?.destroy();
    }
  }
}

/**
 * @private
 */
function computeScreenAngle(slice, xHalfAngle, yHalfAngle) {
  const cosYHalfAngle = cos(yHalfAngle);
  const tanXHalfAngle = tan(xHalfAngle);
  const maxX = atan(cosYHalfAngle * tanXHalfAngle);
  const zox = [];
  for (let i = 0; i < slice; i++) {
    const phi = (2 * maxX * i) / (slice - 1) - maxX;
    zox.push(new Cartesian3(sin(phi), 0, cos(phi)));
  }
  const matrix3 = Matrix3.fromRotationX(-yHalfAngle, matrix3Scratch);
  const zoxPos = zox
    .map((p) => {
      return Matrix3.multiplyByVector(matrix3, p, new Cartesian3());
    })
    .reverse();
  return Cartesian3.angleBetween(zoxPos[0], zoxPos[zoxPos.length - 1]) / 2;
}

/**
 * @private
 */
function createSensor(modelMatrix, xHalfAngle, yHalfAngle, radius, color, opacity, options) {
  const bodyColor = color.clone();
  return new RectangularSensorPrimitive({
    modelMatrix,
    xHalfAngle,
    yHalfAngle,
    radius,
    lineColor: color,
    scanPlaneColor: color,
    scanPlaneMode: 'vertical',
    material: new Material({
      fabric: {
        type: 'Color',
        uniforms: {
          color: bodyColor.withAlpha(opacity)
        }
      }
    }),
    showScanPlane: true,
    showDomeLines: true,
    showSectorLines: true,
    showLateralSurfaces: true,
    showSectorSegmentLines: true,
    slice: 8,
    showThroughEllipsoid: false,
    intersectionWidth: 2,
    ...options
  });
}

/**
 * @private
 * get锥体参数
 */
function getSensorParams(startAzimuth, endAzimuth, startElevation, endElevation) {
  return {
    yaw: (endAzimuth + startAzimuth) / 2,
    pitch: (endElevation + startElevation) / 2,
    xHalfAngle: (endAzimuth - startAzimuth) / 2,
    yHalfAngle: (endElevation - startElevation) / 2
  };
}

/**
 * @private
 * get锥体参数
 */
function getScreenSensorParams(sensorParams, startAzimuth, endAzimuth, startElevation, endElevation) {
  let { yHalfAngle } = getSensorParams(sensorParams[0], sensorParams[1], sensorParams[2], sensorParams[3]);
  yHalfAngle -= (endElevation - startElevation) / 2;
  const xHalfAngle = computeScreenAngle(8, CesiumMath.toRadians((endAzimuth - startAzimuth) / 2), CesiumMath.toRadians(yHalfAngle));
  return {
    yaw: (endAzimuth + startAzimuth) / 2,
    pitch: (endElevation + startElevation) / 2,
    xHalfAngle,
    yHalfAngle: CesiumMath.toRadians((endElevation - startElevation) / 2)
  };
}

/**
 * @private
 * get锥体参数
 */
function getSensorMatrixParams(cartesian, sensorParams, direction) {
  const params = getSensorParams(...sensorParams);
  const ypr = YawPitchRoll.fromDegrees(params.yaw, params.pitch, 0, direction);
  const m = ypr.toTransforms(cartesian);
  return [m, params];
}

/**
 * @private
 * get锥体参数
 */
function getScreenMatrixParams(cartesian, sensorParams, screenParams, direction) {
  const p = getScreenSensorParams(sensorParams, ...screenParams);
  const ypr = YawPitchRoll.fromDegrees(p.yaw, p.pitch, 0, direction);
  const m = ypr.toTransforms(cartesian);
  return [m, p];
}

/**
 * @private
 * get锥体参数
 */
function getSearchMatrixParams(cartesian, searchParams, direction) {
  const ypr = YawPitchRoll.fromDegrees(searchParams[2], searchParams[3], 0, direction);
  const m = ypr.toTransforms(cartesian);
  return [
    m,
    {
      xHalfAngle: searchParams[0],
      yHalfAngle: searchParams[1]
    }
  ];
}

/**
 * @private
 * get锥体参数
 */
function getSarMatrixParams(scanRate, cartesian, quaternion, sarParams, scanRateCount) {
  const horizontalAngle = CesiumMath.toRadians(sarParams[0]) / (abs(scanRate) + 1);
  const sarRadians =
    scanRate === 0
      ? [
          CesiumMath.toRadians(sarParams[0]),
          CesiumMath.toRadians(sarParams[1]),
          CesiumMath.toRadians(sarParams[2]),
          CesiumMath.toRadians(sarParams[3])
        ]
      : [
          horizontalAngle,
          CesiumMath.toRadians(sarParams[1]),
          CesiumMath.toRadians(sarParams[2]),
          CesiumMath.toRadians(sarParams[3]) - abs(scanRate) * horizontalAngle
        ];
  const { modelMatrix, elevation } = Vector3.getPBScannerMatrixByPositionOrientation(cartesian, quaternion, sarRadians, scanRateCount);
  const my = Matrix3.fromRotationY(Math.PI);
  Matrix4.multiplyByMatrix3(modelMatrix, my, modelMatrix);
  const a = earthRadius + Cartographic.fromCartesian(cartesian).height;
  const angle = Math.PI / 2 - elevation + sarRadians[0];
  let radius = (2 * a * cos(angle) - sqrt(pow(2 * a * cos(angle), 2) - 4 * (a ** 2 - pow(earthRadius, 2)))) / 2;
  const signer = !radius;
  const tangent = sqrt(pow(a, 2) - pow(earthRadius, 2));
  radius = radius && radius < tangent ? radius + 20000 : tangent;
  radius = radius || 0;
  return [modelMatrix, radius, { xHalfAngle: sarRadians[0], yHalfAngle: sarRadians[1] }, signer];
}

/**
 * @private
 * Track Sensor
 */
function createTrack(primitiveCollection, lineCollection, cartesian, trackParams, trackTargets, currentTime, color, trackLineColor) {
  let targetPos, radius, modelMatrix;
  const m = Matrix3.fromRotationY(Math.PI / 2);
  trackTargets.forEach((item) => {
    targetPos = item.position?.getValue(currentTime);
    if (targetPos) {
      radius = Cartesian3.distance(cartesian, targetPos);
      modelMatrix = Vector3.getMatrix(cartesian, targetPos);
      Matrix4.multiplyByMatrix3(modelMatrix, m, modelMatrix);
      lineCollection?.add({
        positions: [cartesian, targetPos],
        width: 1.0,
        material: Material.fromType('PolylineDash', {
          color: trackLineColor
        })
      });

      primitiveCollection?.add(
        createSensor(modelMatrix, CesiumMath.toRadians(trackParams[0]), CesiumMath.toRadians(trackParams[1]), radius, color, 0.3, {
          showScanPlane: false
          // showDomeLines: false,
        })
      );
    }
  });
}
