define(["exports","./Matrix2-57f130bc","./RuntimeError-1349fdaf","./ComponentDatatype-17ffa790","./when-4bbc8319","./EllipsoidRhumbLine-e39900fb","./GeometryAttribute-2c37d386","./WebGLConstants-508b9636"],(function(e,t,n,r,i,a,u,o){"use strict";var s=h,x=h;function h(e,t,n){n=n||2;var r,i,a,u,o,s,x,h=t&&t.length,l=h?t[0]*n:e.length,f=p(e,0,l,n,!0),y=[];if(!f||f.next===f.prev)return y;if(h&&(f=function(e,t,n,r){var i,a,u,o=[];for(i=0,a=t.length;i<a;i++)(u=p(e,t[i]*r,i<a-1?t[i+1]*r:e.length,r,!1))===u.next&&(u.steiner=!0),o.push(w(u));for(o.sort(C),i=0;i<o.length;i++)n=m(o[i],n);return n}(e,t,f,n)),e.length>80*n){r=a=e[0],i=u=e[1];for(var d=n;d<l;d+=n)(o=e[d])<r&&(r=o),(s=e[d+1])<i&&(i=s),o>a&&(a=o),s>u&&(u=s);x=0!==(x=Math.max(a-r,u-i))?32767/x:0}return c(f,y,n,r,i,x,0),y}function p(e,t,n,r,i){var a,u;if(i===G(e,t,n,r)>0)for(a=t;a<n;a+=r)u=T(a,e[a],e[a+1],u);else for(a=n-r;a>=t;a-=r)u=T(a,e[a],e[a+1],u);return u&&O(u,u.next)&&(k(u),u=u.next),u}function l(e,t){if(!e)return e;t||(t=e);var n,r=e;do{if(n=!1,r.steiner||!O(r,r.next)&&0!==Z(r.prev,r,r.next))r=r.next;else{if(k(r),(r=t=r.prev)===r.next)break;n=!0}}while(n||r!==t);return t}function c(e,t,n,r,i,a,u){if(e){!u&&a&&function(e,t,n,r){var i=e;do{0===i.z&&(i.z=b(i.x,i.y,t,n,r)),i.prevZ=i.prev,i.nextZ=i.next,i=i.next}while(i!==e);i.prevZ.nextZ=null,i.prevZ=null,function(e){var t,n,r,i,a,u,o,s,x=1;do{for(n=e,e=null,a=null,u=0;n;){for(u++,r=n,o=0,t=0;t<x&&(o++,r=r.nextZ);t++);for(s=x;o>0||s>0&&r;)0!==o&&(0===s||!r||n.z<=r.z)?(i=n,n=n.nextZ,o--):(i=r,r=r.nextZ,s--),a?a.nextZ=i:e=i,i.prevZ=a,a=i;n=r}a.nextZ=null,x*=2}while(u>1)}(i)}(e,r,i,a);for(var o,s,x=e;e.prev!==e.next;)if(o=e.prev,s=e.next,a?y(e,r,i,a):f(e))t.push(o.i/n|0),t.push(e.i/n|0),t.push(s.i/n|0),k(e),e=s.next,x=s.next;else if((e=s)===x){u?1===u?c(e=d(l(e),t,n),t,n,r,i,a,2):2===u&&v(e,t,n,r,i,a):c(l(e),t,n,r,i,a,1);break}}}function f(e){var t=e.prev,n=e,r=e.next;if(Z(t,n,r)>=0)return!1;for(var i=t.x,a=n.x,u=r.x,o=t.y,s=n.y,x=r.y,h=i<a?i<u?i:u:a<u?a:u,p=o<s?o<x?o:x:s<x?s:x,l=i>a?i>u?i:u:a>u?a:u,c=o>s?o>x?o:x:s>x?s:x,f=r.next;f!==t;){if(f.x>=h&&f.x<=l&&f.y>=p&&f.y<=c&&E(i,o,a,s,u,x,f.x,f.y)&&Z(f.prev,f,f.next)>=0)return!1;f=f.next}return!0}function y(e,t,n,r){var i=e.prev,a=e,u=e.next;if(Z(i,a,u)>=0)return!1;for(var o=i.x,s=a.x,x=u.x,h=i.y,p=a.y,l=u.y,c=o<s?o<x?o:x:s<x?s:x,f=h<p?h<l?h:l:p<l?p:l,y=o>s?o>x?o:x:s>x?s:x,d=h>p?h>l?h:l:p>l?p:l,v=b(c,f,t,n,r),C=b(y,d,t,n,r),m=e.prevZ,g=e.nextZ;m&&m.z>=v&&g&&g.z<=C;){if(m.x>=c&&m.x<=y&&m.y>=f&&m.y<=d&&m!==i&&m!==u&&E(o,h,s,p,x,l,m.x,m.y)&&Z(m.prev,m,m.next)>=0)return!1;if(m=m.prevZ,g.x>=c&&g.x<=y&&g.y>=f&&g.y<=d&&g!==i&&g!==u&&E(o,h,s,p,x,l,g.x,g.y)&&Z(g.prev,g,g.next)>=0)return!1;g=g.nextZ}for(;m&&m.z>=v;){if(m.x>=c&&m.x<=y&&m.y>=f&&m.y<=d&&m!==i&&m!==u&&E(o,h,s,p,x,l,m.x,m.y)&&Z(m.prev,m,m.next)>=0)return!1;m=m.prevZ}for(;g&&g.z<=C;){if(g.x>=c&&g.x<=y&&g.y>=f&&g.y<=d&&g!==i&&g!==u&&E(o,h,s,p,x,l,g.x,g.y)&&Z(g.prev,g,g.next)>=0)return!1;g=g.nextZ}return!0}function d(e,t,n){var r=e;do{var i=r.prev,a=r.next.next;!O(i,a)&&S(i,r,r.next,a)&&R(i,a)&&R(a,i)&&(t.push(i.i/n|0),t.push(r.i/n|0),t.push(a.i/n|0),k(r),k(r.next),r=e=a),r=r.next}while(r!==e);return l(r)}function v(e,t,n,r,i,a){var u=e;do{for(var o=u.next.next;o!==u.prev;){if(u.i!==o.i&&M(u,o)){var s=L(u,o);return u=l(u,u.next),s=l(s,s.next),c(u,t,n,r,i,a,0),void c(s,t,n,r,i,a,0)}o=o.next}u=u.next}while(u!==e)}function C(e,t){return e.x-t.x}function m(e,t){var n=function(e,t){var n,r=t,i=e.x,a=e.y,u=-1/0;do{if(a<=r.y&&a>=r.next.y&&r.next.y!==r.y){var o=r.x+(a-r.y)*(r.next.x-r.x)/(r.next.y-r.y);if(o<=i&&o>u&&(u=o,n=r.x<r.next.x?r:r.next,o===i))return n}r=r.next}while(r!==t);if(!n)return null;var s,x=n,h=n.x,p=n.y,l=1/0;r=n;do{i>=r.x&&r.x>=h&&i!==r.x&&E(a<p?i:u,a,h,p,a<p?u:i,a,r.x,r.y)&&(s=Math.abs(a-r.y)/(i-r.x),R(r,e)&&(s<l||s===l&&(r.x>n.x||r.x===n.x&&g(n,r)))&&(n=r,l=s)),r=r.next}while(r!==x);return n}(e,t);if(!n)return t;var r=L(n,e);return l(r,r.next),l(n,n.next)}function g(e,t){return Z(e.prev,e,t.prev)<0&&Z(t.next,e,e.next)<0}function b(e,t,n,r,i){return(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-n)*i|0)|e<<8))|e<<4))|e<<2))|e<<1))|(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-r)*i|0)|t<<8))|t<<4))|t<<2))|t<<1))<<1}function w(e){var t=e,n=e;do{(t.x<n.x||t.x===n.x&&t.y<n.y)&&(n=t),t=t.next}while(t!==e);return n}function E(e,t,n,r,i,a,u,o){return(i-u)*(t-o)>=(e-u)*(a-o)&&(e-u)*(r-o)>=(n-u)*(t-o)&&(n-u)*(a-o)>=(i-u)*(r-o)}function M(e,t){return e.next.i!==t.i&&e.prev.i!==t.i&&!function(e,t){var n=e;do{if(n.i!==e.i&&n.next.i!==e.i&&n.i!==t.i&&n.next.i!==t.i&&S(n,n.next,e,t))return!0;n=n.next}while(n!==e);return!1}(e,t)&&(R(e,t)&&R(t,e)&&function(e,t){var n=e,r=!1,i=(e.x+t.x)/2,a=(e.y+t.y)/2;do{n.y>a!=n.next.y>a&&n.next.y!==n.y&&i<(n.next.x-n.x)*(a-n.y)/(n.next.y-n.y)+n.x&&(r=!r),n=n.next}while(n!==e);return r}(e,t)&&(Z(e.prev,e,t.prev)||Z(e,t.prev,t))||O(e,t)&&Z(e.prev,e,e.next)>0&&Z(t.prev,t,t.next)>0)}function Z(e,t,n){return(t.y-e.y)*(n.x-t.x)-(t.x-e.x)*(n.y-t.y)}function O(e,t){return e.x===t.x&&e.y===t.y}function S(e,t,n,r){var i=z(Z(e,t,n)),a=z(Z(e,t,r)),u=z(Z(n,r,e)),o=z(Z(n,r,t));return i!==a&&u!==o||(!(0!==i||!A(e,n,t))||(!(0!==a||!A(e,r,t))||(!(0!==u||!A(n,e,r))||!(0!==o||!A(n,t,r)))))}function A(e,t,n){return t.x<=Math.max(e.x,n.x)&&t.x>=Math.min(e.x,n.x)&&t.y<=Math.max(e.y,n.y)&&t.y>=Math.min(e.y,n.y)}function z(e){return e>0?1:e<0?-1:0}function R(e,t){return Z(e.prev,e,e.next)<0?Z(e,t,e.next)>=0&&Z(e,e.prev,t)>=0:Z(e,t,e.prev)<0||Z(e,e.next,t)<0}function L(e,t){var n=new D(e.i,e.x,e.y),r=new D(t.i,t.x,t.y),i=e.next,a=t.prev;return e.next=t,t.prev=e,n.next=i,i.prev=n,r.next=n,n.prev=r,a.next=r,r.prev=a,r}function T(e,t,n,r){var i=new D(e,t,n);return r?(i.next=r.next,i.prev=r,r.next.prev=i,r.next=i):(i.prev=i,i.next=i),i}function k(e){e.next.prev=e.prev,e.prev.next=e.next,e.prevZ&&(e.prevZ.nextZ=e.nextZ),e.nextZ&&(e.nextZ.prevZ=e.prevZ)}function D(e,t,n){this.i=e,this.x=t,this.y=n,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}function G(e,t,n,r){for(var i=0,a=t,u=n-r;a<n;a+=r)i+=(e[u]-e[a])*(e[a+1]+e[u+1]),u=a;return i}h.deviation=function(e,t,n,r){var i=t&&t.length,a=i?t[0]*n:e.length,u=Math.abs(G(e,0,a,n));if(i)for(var o=0,s=t.length;o<s;o++){var x=t[o]*n,h=o<s-1?t[o+1]*n:e.length;u-=Math.abs(G(e,x,h,n))}var p=0;for(o=0;o<r.length;o+=3){var l=r[o]*n,c=r[o+1]*n,f=r[o+2]*n;p+=Math.abs((e[l]-e[f])*(e[c+1]-e[l+1])-(e[l]-e[c])*(e[f+1]-e[l+1]))}return 0===u&&0===p?0:Math.abs((p-u)/u)},h.flatten=function(e){for(var t=e[0][0].length,n={vertices:[],holes:[],dimensions:t},r=0,i=0;i<e.length;i++){for(var a=0;a<e[i].length;a++)for(var u=0;u<t;u++)n.vertices.push(e[i][a][u]);i>0&&(r+=e[i-1].length,n.holes.push(r))}return n},s.default=x;const W={CLOCKWISE:o.WebGLConstants.CW,COUNTER_CLOCKWISE:o.WebGLConstants.CCW,validate:function(e){return e===W.CLOCKWISE||e===W.COUNTER_CLOCKWISE}};var P=Object.freeze(W);const I=new t.Cartesian3,B=new t.Cartesian3,q={computeArea2D:function(e){n.Check.defined("positions",e),n.Check.typeOf.number.greaterThanOrEquals("positions.length",e.length,3);const t=e.length;let r=0;for(let n=t-1,i=0;i<t;n=i++){const t=e[n],a=e[i];r+=t.x*a.y-a.x*t.y}return.5*r},computeWindingOrder2D:function(e){return q.computeArea2D(e)>0?P.COUNTER_CLOCKWISE:P.CLOCKWISE},triangulate:function(e,r){n.Check.defined("positions",e);const i=t.Cartesian2.packArray(e);return s(i,r,2)}},N=new t.Cartesian3,U=new t.Cartesian3,_=new t.Cartesian3,K=new t.Cartesian3,V=new t.Cartesian3,j=new t.Cartesian3,F=new t.Cartesian3;q.computeSubdivision=function(e,a,o,s){s=i.defaultValue(s,r.CesiumMath.RADIANS_PER_DEGREE),n.Check.typeOf.object("ellipsoid",e),n.Check.defined("positions",a),n.Check.defined("indices",o),n.Check.typeOf.number.greaterThanOrEquals("indices.length",o.length,3),n.Check.typeOf.number.equals("indices.length % 3","0",o.length%3,0),n.Check.typeOf.number.greaterThan("granularity",s,0);const x=o.slice(0);let h;const p=a.length,l=new Array(3*p);let c=0;for(h=0;h<p;h++){const e=a[h];l[c++]=e.x,l[c++]=e.y,l[c++]=e.z}const f=[],y={},d=e.maximumRadius,v=r.CesiumMath.chordLength(s,d),C=v*v;for(;x.length>0;){const e=x.pop(),n=x.pop(),r=x.pop(),a=t.Cartesian3.fromArray(l,3*r,N),u=t.Cartesian3.fromArray(l,3*n,U),o=t.Cartesian3.fromArray(l,3*e,_),s=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(a,K),d,K),p=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(u,V),d,V),c=t.Cartesian3.multiplyByScalar(t.Cartesian3.normalize(o,j),d,j),v=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(s,p,F)),m=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(p,c,F)),g=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(c,s,F)),b=Math.max(v,m,g);let w,E;b>C?v===b?(w=Math.min(r,n)+" "+Math.max(r,n),h=y[w],i.defined(h)||(E=t.Cartesian3.add(a,u,F),t.Cartesian3.multiplyByScalar(E,.5,E),l.push(E.x,E.y,E.z),h=l.length/3-1,y[w]=h),x.push(r,h,e),x.push(h,n,e)):m===b?(w=Math.min(n,e)+" "+Math.max(n,e),h=y[w],i.defined(h)||(E=t.Cartesian3.add(u,o,F),t.Cartesian3.multiplyByScalar(E,.5,E),l.push(E.x,E.y,E.z),h=l.length/3-1,y[w]=h),x.push(n,h,r),x.push(h,e,r)):g===b&&(w=Math.min(e,r)+" "+Math.max(e,r),h=y[w],i.defined(h)||(E=t.Cartesian3.add(o,a,F),t.Cartesian3.multiplyByScalar(E,.5,E),l.push(E.x,E.y,E.z),h=l.length/3-1,y[w]=h),x.push(e,h,n),x.push(h,r,n)):(f.push(r),f.push(n),f.push(e))}return new u.Geometry({attributes:{position:new u.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:l})},indices:f,primitiveType:u.PrimitiveType.TRIANGLES})};const H=new t.Cartographic,J=new t.Cartographic,Q=new t.Cartographic,X=new t.Cartographic;q.computeRhumbLineSubdivision=function(e,o,s,x){x=i.defaultValue(x,r.CesiumMath.RADIANS_PER_DEGREE),n.Check.typeOf.object("ellipsoid",e),n.Check.defined("positions",o),n.Check.defined("indices",s),n.Check.typeOf.number.greaterThanOrEquals("indices.length",s.length,3),n.Check.typeOf.number.equals("indices.length % 3","0",s.length%3,0),n.Check.typeOf.number.greaterThan("granularity",x,0);const h=s.slice(0);let p;const l=o.length,c=new Array(3*l);let f=0;for(p=0;p<l;p++){const e=o[p];c[f++]=e.x,c[f++]=e.y,c[f++]=e.z}const y=[],d={},v=e.maximumRadius,C=r.CesiumMath.chordLength(x,v),m=new a.EllipsoidRhumbLine(void 0,void 0,e),g=new a.EllipsoidRhumbLine(void 0,void 0,e),b=new a.EllipsoidRhumbLine(void 0,void 0,e);for(;h.length>0;){const n=h.pop(),r=h.pop(),a=h.pop(),u=t.Cartesian3.fromArray(c,3*a,N),o=t.Cartesian3.fromArray(c,3*r,U),s=t.Cartesian3.fromArray(c,3*n,_),x=e.cartesianToCartographic(u,H),l=e.cartesianToCartographic(o,J),f=e.cartesianToCartographic(s,Q);m.setEndPoints(x,l);const v=m.surfaceDistance;g.setEndPoints(l,f);const w=g.surfaceDistance;b.setEndPoints(f,x);const E=b.surfaceDistance,M=Math.max(v,w,E);let Z,O,S,A;M>C?v===M?(Z=Math.min(a,r)+" "+Math.max(a,r),p=d[Z],i.defined(p)||(O=m.interpolateUsingFraction(.5,X),S=.5*(x.height+l.height),A=t.Cartesian3.fromRadians(O.longitude,O.latitude,S,e,F),c.push(A.x,A.y,A.z),p=c.length/3-1,d[Z]=p),h.push(a,p,n),h.push(p,r,n)):w===M?(Z=Math.min(r,n)+" "+Math.max(r,n),p=d[Z],i.defined(p)||(O=g.interpolateUsingFraction(.5,X),S=.5*(l.height+f.height),A=t.Cartesian3.fromRadians(O.longitude,O.latitude,S,e,F),c.push(A.x,A.y,A.z),p=c.length/3-1,d[Z]=p),h.push(r,p,a),h.push(p,n,a)):E===M&&(Z=Math.min(n,a)+" "+Math.max(n,a),p=d[Z],i.defined(p)||(O=b.interpolateUsingFraction(.5,X),S=.5*(f.height+x.height),A=t.Cartesian3.fromRadians(O.longitude,O.latitude,S,e,F),c.push(A.x,A.y,A.z),p=c.length/3-1,d[Z]=p),h.push(n,p,r),h.push(p,a,r)):(y.push(a),y.push(r),y.push(n))}return new u.Geometry({attributes:{position:new u.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})},indices:y,primitiveType:u.PrimitiveType.TRIANGLES})},q.scaleToGeodeticHeight=function(e,n,r,a){r=i.defaultValue(r,t.Ellipsoid.WGS84);let u=I,o=B;if(n=i.defaultValue(n,0),a=i.defaultValue(a,!0),i.defined(e)){const i=e.length;for(let s=0;s<i;s+=3)t.Cartesian3.fromArray(e,s,o),a&&(o=r.scaleToGeodeticSurface(o,o)),0!==n&&(u=r.geodeticSurfaceNormal(o,u),t.Cartesian3.multiplyByScalar(u,n,u),t.Cartesian3.add(o,u,o)),e[s]=o.x,e[s+1]=o.y,e[s+2]=o.z}return e},e.PolygonPipeline=q,e.WindingOrder=P}));
//# sourceMappingURL=PolygonPipeline-2de14cd1.js.map
