define(["exports","./Matrix2-57f130bc","./RuntimeError-1349fdaf","./when-4bbc8319","./ComponentDatatype-17ffa790","./combine-e9466e32"],(function(e,t,r,n,o,i){"use strict";function s(e){this._ellipsoid=n.defaultValue(e,t.Ellipsoid.WGS84),this._semimajorAxis=this._ellipsoid.maximumRadius,this._oneOverSemimajorAxis=1/this._semimajorAxis}Object.defineProperties(s.prototype,{ellipsoid:{get:function(){return this._ellipsoid}}}),s.prototype.project=function(e,r){const o=this._semimajorAxis,i=e.longitude*o,s=e.latitude*o,a=e.height;return n.defined(r)?(r.x=i,r.y=s,r.z=a,r):new t.Car<PERSON>ian3(i,s,a)},s.prototype.unproject=function(e,o){if(!n.defined(e))throw new r.DeveloperError("cartesian is required");const i=this._oneOverSemimajorAxis,s=e.x*i,a=e.y*i,u=e.z;return n.defined(o)?(o.longitude=s,o.latitude=a,o.height=u,o):new t.Cartographic(s,a,u)};var a=Object.freeze({OUTSIDE:-1,INTERSECTING:0,INSIDE:1});function u(e,t){this.start=n.defaultValue(e,0),this.stop=n.defaultValue(t,0)}function c(e,r){this.center=t.Cartesian3.clone(n.defaultValue(e,t.Cartesian3.ZERO)),this.radius=n.defaultValue(r,0)}const l=new t.Cartesian3,d=new t.Cartesian3,f=new t.Cartesian3,p=new t.Cartesian3,h=new t.Cartesian3,m=new t.Cartesian3,g=new t.Cartesian3,y=new t.Cartesian3,v=new t.Cartesian3,w=new t.Cartesian3,C=new t.Cartesian3,b=new t.Cartesian3,_=4/3*o.CesiumMath.PI;c.fromPoints=function(e,r){if(n.defined(r)||(r=new c),!n.defined(e)||0===e.length)return r.center=t.Cartesian3.clone(t.Cartesian3.ZERO,r.center),r.radius=0,r;const o=t.Cartesian3.clone(e[0],g),i=t.Cartesian3.clone(o,l),s=t.Cartesian3.clone(o,d),a=t.Cartesian3.clone(o,f),u=t.Cartesian3.clone(o,p),_=t.Cartesian3.clone(o,h),x=t.Cartesian3.clone(o,m),E=e.length;let O;for(O=1;O<E;O++){t.Cartesian3.clone(e[O],o);const r=o.x,n=o.y,c=o.z;r<i.x&&t.Cartesian3.clone(o,i),r>u.x&&t.Cartesian3.clone(o,u),n<s.y&&t.Cartesian3.clone(o,s),n>_.y&&t.Cartesian3.clone(o,_),c<a.z&&t.Cartesian3.clone(o,a),c>x.z&&t.Cartesian3.clone(o,x)}const S=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(u,i,y)),A=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(_,s,y)),q=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(x,a,y));let D=i,k=u,R=S;A>R&&(R=A,D=s,k=_),q>R&&(R=q,D=a,k=x);const I=v;I.x=.5*(D.x+k.x),I.y=.5*(D.y+k.y),I.z=.5*(D.z+k.z);let P=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(k,I,y)),T=Math.sqrt(P);const z=w;z.x=i.x,z.y=s.y,z.z=a.z;const M=C;M.x=u.x,M.y=_.y,M.z=x.z;const U=t.Cartesian3.midpoint(z,M,b);let j=0;for(O=0;O<E;O++){t.Cartesian3.clone(e[O],o);const r=t.Cartesian3.magnitude(t.Cartesian3.subtract(o,U,y));r>j&&(j=r);const n=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(o,I,y));if(n>P){const e=Math.sqrt(n);T=.5*(T+e),P=T*T;const t=e-T;I.x=(T*I.x+t*o.x)/e,I.y=(T*I.y+t*o.y)/e,I.z=(T*I.z+t*o.z)/e}}return T<j?(t.Cartesian3.clone(I,r.center),r.radius=T):(t.Cartesian3.clone(U,r.center),r.radius=j),r};const x=new s,E=new t.Cartesian3,O=new t.Cartesian3,S=new t.Cartographic,A=new t.Cartographic;c.fromRectangle2D=function(e,t,r){return c.fromRectangleWithHeights2D(e,t,0,0,r)},c.fromRectangleWithHeights2D=function(e,r,o,i,s){if(n.defined(s)||(s=new c),!n.defined(e))return s.center=t.Cartesian3.clone(t.Cartesian3.ZERO,s.center),s.radius=0,s;r=n.defaultValue(r,x),t.Rectangle.southwest(e,S),S.height=o,t.Rectangle.northeast(e,A),A.height=i;const a=r.project(S,E),u=r.project(A,O),l=u.x-a.x,d=u.y-a.y,f=u.z-a.z;s.radius=.5*Math.sqrt(l*l+d*d+f*f);const p=s.center;return p.x=a.x+.5*l,p.y=a.y+.5*d,p.z=a.z+.5*f,s};const q=[];c.fromRectangle3D=function(e,r,o,i){if(r=n.defaultValue(r,t.Ellipsoid.WGS84),o=n.defaultValue(o,0),n.defined(i)||(i=new c),!n.defined(e))return i.center=t.Cartesian3.clone(t.Cartesian3.ZERO,i.center),i.radius=0,i;const s=t.Rectangle.subsample(e,r,o,q);return c.fromPoints(s,i)},c.fromVertices=function(e,o,i,s){if(n.defined(s)||(s=new c),!n.defined(e)||0===e.length)return s.center=t.Cartesian3.clone(t.Cartesian3.ZERO,s.center),s.radius=0,s;o=n.defaultValue(o,t.Cartesian3.ZERO),i=n.defaultValue(i,3),r.Check.typeOf.number.greaterThanOrEquals("stride",i,3);const a=g;a.x=e[0]+o.x,a.y=e[1]+o.y,a.z=e[2]+o.z;const u=t.Cartesian3.clone(a,l),_=t.Cartesian3.clone(a,d),x=t.Cartesian3.clone(a,f),E=t.Cartesian3.clone(a,p),O=t.Cartesian3.clone(a,h),S=t.Cartesian3.clone(a,m),A=e.length;let q;for(q=0;q<A;q+=i){const r=e[q]+o.x,n=e[q+1]+o.y,i=e[q+2]+o.z;a.x=r,a.y=n,a.z=i,r<u.x&&t.Cartesian3.clone(a,u),r>E.x&&t.Cartesian3.clone(a,E),n<_.y&&t.Cartesian3.clone(a,_),n>O.y&&t.Cartesian3.clone(a,O),i<x.z&&t.Cartesian3.clone(a,x),i>S.z&&t.Cartesian3.clone(a,S)}const D=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(E,u,y)),k=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(O,_,y)),R=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(S,x,y));let I=u,P=E,T=D;k>T&&(T=k,I=_,P=O),R>T&&(T=R,I=x,P=S);const z=v;z.x=.5*(I.x+P.x),z.y=.5*(I.y+P.y),z.z=.5*(I.z+P.z);let M=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(P,z,y)),U=Math.sqrt(M);const j=w;j.x=u.x,j.y=_.y,j.z=x.z;const F=C;F.x=E.x,F.y=O.y,F.z=S.z;const N=t.Cartesian3.midpoint(j,F,b);let B=0;for(q=0;q<A;q+=i){a.x=e[q]+o.x,a.y=e[q+1]+o.y,a.z=e[q+2]+o.z;const r=t.Cartesian3.magnitude(t.Cartesian3.subtract(a,N,y));r>B&&(B=r);const n=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(a,z,y));if(n>M){const e=Math.sqrt(n);U=.5*(U+e),M=U*U;const t=e-U;z.x=(U*z.x+t*a.x)/e,z.y=(U*z.y+t*a.y)/e,z.z=(U*z.z+t*a.z)/e}}return U<B?(t.Cartesian3.clone(z,s.center),s.radius=U):(t.Cartesian3.clone(N,s.center),s.radius=B),s},c.fromEncodedCartesianVertices=function(e,r,o){if(n.defined(o)||(o=new c),!n.defined(e)||!n.defined(r)||e.length!==r.length||0===e.length)return o.center=t.Cartesian3.clone(t.Cartesian3.ZERO,o.center),o.radius=0,o;const i=g;i.x=e[0]+r[0],i.y=e[1]+r[1],i.z=e[2]+r[2];const s=t.Cartesian3.clone(i,l),a=t.Cartesian3.clone(i,d),u=t.Cartesian3.clone(i,f),_=t.Cartesian3.clone(i,p),x=t.Cartesian3.clone(i,h),E=t.Cartesian3.clone(i,m),O=e.length;let S;for(S=0;S<O;S+=3){const n=e[S]+r[S],o=e[S+1]+r[S+1],c=e[S+2]+r[S+2];i.x=n,i.y=o,i.z=c,n<s.x&&t.Cartesian3.clone(i,s),n>_.x&&t.Cartesian3.clone(i,_),o<a.y&&t.Cartesian3.clone(i,a),o>x.y&&t.Cartesian3.clone(i,x),c<u.z&&t.Cartesian3.clone(i,u),c>E.z&&t.Cartesian3.clone(i,E)}const A=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(_,s,y)),q=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(x,a,y)),D=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(E,u,y));let k=s,R=_,I=A;q>I&&(I=q,k=a,R=x),D>I&&(I=D,k=u,R=E);const P=v;P.x=.5*(k.x+R.x),P.y=.5*(k.y+R.y),P.z=.5*(k.z+R.z);let T=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(R,P,y)),z=Math.sqrt(T);const M=w;M.x=s.x,M.y=a.y,M.z=u.z;const U=C;U.x=_.x,U.y=x.y,U.z=E.z;const j=t.Cartesian3.midpoint(M,U,b);let F=0;for(S=0;S<O;S+=3){i.x=e[S]+r[S],i.y=e[S+1]+r[S+1],i.z=e[S+2]+r[S+2];const n=t.Cartesian3.magnitude(t.Cartesian3.subtract(i,j,y));n>F&&(F=n);const o=t.Cartesian3.magnitudeSquared(t.Cartesian3.subtract(i,P,y));if(o>T){const e=Math.sqrt(o);z=.5*(z+e),T=z*z;const t=e-z;P.x=(z*P.x+t*i.x)/e,P.y=(z*P.y+t*i.y)/e,P.z=(z*P.z+t*i.z)/e}}return z<F?(t.Cartesian3.clone(P,o.center),o.radius=z):(t.Cartesian3.clone(j,o.center),o.radius=F),o},c.fromCornerPoints=function(e,o,i){r.Check.typeOf.object("corner",e),r.Check.typeOf.object("oppositeCorner",o),n.defined(i)||(i=new c);const s=t.Cartesian3.midpoint(e,o,i.center);return i.radius=t.Cartesian3.distance(s,o),i},c.fromEllipsoid=function(e,o){return r.Check.typeOf.object("ellipsoid",e),n.defined(o)||(o=new c),t.Cartesian3.clone(t.Cartesian3.ZERO,o.center),o.radius=e.maximumRadius,o};const D=new t.Cartesian3;c.fromBoundingSpheres=function(e,r){if(n.defined(r)||(r=new c),!n.defined(e)||0===e.length)return r.center=t.Cartesian3.clone(t.Cartesian3.ZERO,r.center),r.radius=0,r;const o=e.length;if(1===o)return c.clone(e[0],r);if(2===o)return c.union(e[0],e[1],r);const i=[];let s;for(s=0;s<o;s++)i.push(e[s].center);const a=(r=c.fromPoints(i,r)).center;let u=r.radius;for(s=0;s<o;s++){const r=e[s];u=Math.max(u,t.Cartesian3.distance(a,r.center,D)+r.radius)}return r.radius=u,r};const k=new t.Cartesian3,R=new t.Cartesian3,I=new t.Cartesian3;c.fromOrientedBoundingBox=function(e,o){r.Check.defined("orientedBoundingBox",e),n.defined(o)||(o=new c);const i=e.halfAxes,s=t.Matrix3.getColumn(i,0,k),a=t.Matrix3.getColumn(i,1,R),u=t.Matrix3.getColumn(i,2,I);return t.Cartesian3.add(s,a,s),t.Cartesian3.add(s,u,s),o.center=t.Cartesian3.clone(e.center,o.center),o.radius=t.Cartesian3.magnitude(s),o},c.clone=function(e,r){if(n.defined(e))return n.defined(r)?(r.center=t.Cartesian3.clone(e.center,r.center),r.radius=e.radius,r):new c(e.center,e.radius)},c.packedLength=4,c.pack=function(e,t,o){r.Check.typeOf.object("value",e),r.Check.defined("array",t),o=n.defaultValue(o,0);const i=e.center;return t[o++]=i.x,t[o++]=i.y,t[o++]=i.z,t[o]=e.radius,t},c.unpack=function(e,t,o){r.Check.defined("array",e),t=n.defaultValue(t,0),n.defined(o)||(o=new c);const i=o.center;return i.x=e[t++],i.y=e[t++],i.z=e[t++],o.radius=e[t],o};const P=new t.Cartesian3,T=new t.Cartesian3;c.union=function(e,o,i){r.Check.typeOf.object("left",e),r.Check.typeOf.object("right",o),n.defined(i)||(i=new c);const s=e.center,a=e.radius,u=o.center,l=o.radius,d=t.Cartesian3.subtract(u,s,P),f=t.Cartesian3.magnitude(d);if(a>=f+l)return e.clone(i),i;if(l>=f+a)return o.clone(i),i;const p=.5*(a+f+l),h=t.Cartesian3.multiplyByScalar(d,(-a+p)/f,T);return t.Cartesian3.add(h,s,h),t.Cartesian3.clone(h,i.center),i.radius=p,i};const z=new t.Cartesian3;c.expand=function(e,n,o){r.Check.typeOf.object("sphere",e),r.Check.typeOf.object("point",n),o=c.clone(e,o);const i=t.Cartesian3.magnitude(t.Cartesian3.subtract(n,o.center,z));return i>o.radius&&(o.radius=i),o},c.intersectPlane=function(e,n){r.Check.typeOf.object("sphere",e),r.Check.typeOf.object("plane",n);const o=e.center,i=e.radius,s=n.normal,u=t.Cartesian3.dot(s,o)+n.distance;return u<-i?a.OUTSIDE:u<i?a.INTERSECTING:a.INSIDE},c.transform=function(e,o,i){return r.Check.typeOf.object("sphere",e),r.Check.typeOf.object("transform",o),n.defined(i)||(i=new c),i.center=t.Matrix4.multiplyByPoint(o,e.center,i.center),i.radius=t.Matrix4.getMaximumScale(o)*e.radius,i};const M=new t.Cartesian3;c.distanceSquaredTo=function(e,n){r.Check.typeOf.object("sphere",e),r.Check.typeOf.object("cartesian",n);const o=t.Cartesian3.subtract(e.center,n,M),i=t.Cartesian3.magnitude(o)-e.radius;return i<=0?0:i*i},c.transformWithoutScale=function(e,o,i){return r.Check.typeOf.object("sphere",e),r.Check.typeOf.object("transform",o),n.defined(i)||(i=new c),i.center=t.Matrix4.multiplyByPoint(o,e.center,i.center),i.radius=e.radius,i};const U=new t.Cartesian3;c.computePlaneDistances=function(e,o,i,s){r.Check.typeOf.object("sphere",e),r.Check.typeOf.object("position",o),r.Check.typeOf.object("direction",i),n.defined(s)||(s=new u);const a=t.Cartesian3.subtract(e.center,o,U),c=t.Cartesian3.dot(i,a);return s.start=c-e.radius,s.stop=c+e.radius,s};const j=new t.Cartesian3,F=new t.Cartesian3,N=new t.Cartesian3,B=new t.Cartesian3,V=new t.Cartesian3,L=new t.Cartographic,Q=new Array(8);for(let e=0;e<8;++e)Q[e]=new t.Cartesian3;const W=new s;let H;c.projectTo2D=function(e,o,i){r.Check.typeOf.object("sphere",e);const s=(o=n.defaultValue(o,W)).ellipsoid;let a=e.center;const u=e.radius;let l;l=t.Cartesian3.equals(a,t.Cartesian3.ZERO)?t.Cartesian3.clone(t.Cartesian3.UNIT_X,j):s.geodeticSurfaceNormal(a,j);const d=t.Cartesian3.cross(t.Cartesian3.UNIT_Z,l,F);t.Cartesian3.normalize(d,d);const f=t.Cartesian3.cross(l,d,N);t.Cartesian3.normalize(f,f),t.Cartesian3.multiplyByScalar(l,u,l),t.Cartesian3.multiplyByScalar(f,u,f),t.Cartesian3.multiplyByScalar(d,u,d);const p=t.Cartesian3.negate(f,V),h=t.Cartesian3.negate(d,B),m=Q;let g=m[0];t.Cartesian3.add(l,f,g),t.Cartesian3.add(g,d,g),g=m[1],t.Cartesian3.add(l,f,g),t.Cartesian3.add(g,h,g),g=m[2],t.Cartesian3.add(l,p,g),t.Cartesian3.add(g,h,g),g=m[3],t.Cartesian3.add(l,p,g),t.Cartesian3.add(g,d,g),t.Cartesian3.negate(l,l),g=m[4],t.Cartesian3.add(l,f,g),t.Cartesian3.add(g,d,g),g=m[5],t.Cartesian3.add(l,f,g),t.Cartesian3.add(g,h,g),g=m[6],t.Cartesian3.add(l,p,g),t.Cartesian3.add(g,h,g),g=m[7],t.Cartesian3.add(l,p,g),t.Cartesian3.add(g,d,g);const y=m.length;for(let e=0;e<y;++e){const r=m[e];t.Cartesian3.add(a,r,r);const n=s.cartesianToCartographic(r,L);o.project(n,r)}a=(i=c.fromPoints(m,i)).center;const v=a.x,w=a.y,C=a.z;return a.x=C,a.y=v,a.z=w,i},c.isOccluded=function(e,t){return r.Check.typeOf.object("sphere",e),r.Check.typeOf.object("occluder",t),!t.isBoundingSphereVisible(e)},c.equals=function(e,r){return e===r||n.defined(e)&&n.defined(r)&&t.Cartesian3.equals(e.center,r.center)&&e.radius===r.radius},c.prototype.intersectPlane=function(e){return c.intersectPlane(this,e)},c.prototype.distanceSquaredTo=function(e){return c.distanceSquaredTo(this,e)},c.prototype.computePlaneDistances=function(e,t,r){return c.computePlaneDistances(this,e,t,r)},c.prototype.isOccluded=function(e){return c.isOccluded(this,e)},c.prototype.equals=function(e){return c.equals(this,e)},c.prototype.clone=function(e){return c.clone(this,e)},c.prototype.volume=function(){const e=this.radius;return _*e*e*e};const Y={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},Z={};let G,J,$,X,K,ee,te,re,ne,oe,ie,se,ae,ue,ce,le,de;function fe(e){const t=e.split(".");for(let e=0,r=t.length;e<r;++e)t[e]=parseInt(t[e],10);return t}function pe(){if(!n.defined(J)&&(J=!1,!ye())){const e=/ Chrome\/([\.0-9]+)/.exec(G.userAgent);null!==e&&(J=!0,$=fe(e[1]))}return J}function he(){if(!n.defined(X)&&(X=!1,!pe()&&!ye()&&/ Safari\/[\.0-9]+/.test(G.userAgent))){const e=/ Version\/([\.0-9]+)/.exec(G.userAgent);null!==e&&(X=!0,K=fe(e[1]))}return X}function me(){if(!n.defined(ee)){ee=!1;const e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(G.userAgent);null!==e&&(ee=!0,te=fe(e[1]),te.isNightly=!!e[2])}return ee}function ge(){if(!n.defined(re)){let e;re=!1,"Microsoft Internet Explorer"===G.appName?(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(G.userAgent),null!==e&&(re=!0,ne=fe(e[1]))):"Netscape"===G.appName&&(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(G.userAgent),null!==e&&(re=!0,ne=fe(e[1])))}return re}function ye(){if(!n.defined(oe)){oe=!1;const e=/ Edge\/([\.0-9]+)/.exec(G.userAgent);null!==e&&(oe=!0,ie=fe(e[1]))}return oe}function ve(){if(!n.defined(se)){se=!1;const e=/Firefox\/([\.0-9]+)/.exec(G.userAgent);null!==e&&(se=!0,ae=fe(e[1]))}return se}function we(){if(!n.defined(de)){const e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");const t=e.style.imageRendering;de=n.defined(t)&&""!==t,de&&(le=t)}return de}function Ce(){if(!Ce.initialized)throw new r.DeveloperError("You must call FeatureDetection.supportsWebP.initialize and wait for the promise to resolve before calling FeatureDetection.supportsWebP");return Ce._result}Object.defineProperties(Z,{element:{get:function(){if(Z.supportsFullscreen())return document[Y.fullscreenElement]}},changeEventName:{get:function(){if(Z.supportsFullscreen())return Y.fullscreenchange}},errorEventName:{get:function(){if(Z.supportsFullscreen())return Y.fullscreenerror}},enabled:{get:function(){if(Z.supportsFullscreen())return document[Y.fullscreenEnabled]}},fullscreen:{get:function(){if(Z.supportsFullscreen())return null!==Z.element}}}),Z.supportsFullscreen=function(){if(n.defined(H))return H;H=!1;const e=document.body;if("function"==typeof e.requestFullscreen)return Y.requestFullscreen="requestFullscreen",Y.exitFullscreen="exitFullscreen",Y.fullscreenEnabled="fullscreenEnabled",Y.fullscreenElement="fullscreenElement",Y.fullscreenchange="fullscreenchange",Y.fullscreenerror="fullscreenerror",H=!0,H;const t=["webkit","moz","o","ms","khtml"];let r;for(let n=0,o=t.length;n<o;++n){const o=t[n];r=o+"RequestFullscreen","function"==typeof e[r]?(Y.requestFullscreen=r,H=!0):(r=o+"RequestFullScreen","function"==typeof e[r]&&(Y.requestFullscreen=r,H=!0)),r=o+"ExitFullscreen","function"==typeof document[r]?Y.exitFullscreen=r:(r=o+"CancelFullScreen","function"==typeof document[r]&&(Y.exitFullscreen=r)),r=o+"FullscreenEnabled",void 0!==document[r]?Y.fullscreenEnabled=r:(r=o+"FullScreenEnabled",void 0!==document[r]&&(Y.fullscreenEnabled=r)),r=o+"FullscreenElement",void 0!==document[r]?Y.fullscreenElement=r:(r=o+"FullScreenElement",void 0!==document[r]&&(Y.fullscreenElement=r)),r=o+"fullscreenchange",void 0!==document["on"+r]&&("ms"===o&&(r="MSFullscreenChange"),Y.fullscreenchange=r),r=o+"fullscreenerror",void 0!==document["on"+r]&&("ms"===o&&(r="MSFullscreenError"),Y.fullscreenerror=r)}return H},Z.requestFullscreen=function(e,t){Z.supportsFullscreen()&&e[Y.requestFullscreen]({vrDisplay:t})},Z.exitFullscreen=function(){Z.supportsFullscreen()&&document[Y.exitFullscreen]()},Z._names=Y,G="undefined"!=typeof navigator?navigator:{},Ce._promise=void 0,Ce._result=void 0,Ce.initialize=function(){if(n.defined(Ce._promise))return Ce._promise;const e=n.when.defer();if(Ce._promise=e.promise,ye())return Ce._result=!1,e.resolve(Ce._result),e.promise;const t=new Image;return t.onload=function(){Ce._result=t.width>0&&t.height>0,e.resolve(Ce._result)},t.onerror=function(){Ce._result=!1,e.resolve(Ce._result)},t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",e.promise},Object.defineProperties(Ce,{initialized:{get:function(){return n.defined(Ce._result)}}});const be=[];"undefined"!=typeof ArrayBuffer&&(be.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),"undefined"!=typeof Uint8ClampedArray&&be.push(Uint8ClampedArray),"undefined"!=typeof Uint8ClampedArray&&be.push(Uint8ClampedArray),"undefined"!=typeof BigInt64Array&&be.push(BigInt64Array),"undefined"!=typeof BigUint64Array&&be.push(BigUint64Array));const _e={isChrome:pe,chromeVersion:function(){return pe()&&$},isSafari:he,safariVersion:function(){return he()&&K},isWebkit:me,webkitVersion:function(){return me()&&te},isInternetExplorer:ge,internetExplorerVersion:function(){return ge()&&ne},isEdge:ye,edgeVersion:function(){return ye()&&ie},isFirefox:ve,firefoxVersion:function(){return ve()&&ae},isWindows:function(){return n.defined(ue)||(ue=/Windows/i.test(G.appVersion)),ue},hardwareConcurrency:n.defaultValue(G.hardwareConcurrency,3),supportsPointerEvents:function(){return n.defined(ce)||(ce=!ve()&&"undefined"!=typeof PointerEvent&&(!n.defined(G.pointerEnabled)||G.pointerEnabled)),ce},supportsImageRenderingPixelated:we,supportsWebP:Ce,imageRenderingValue:function(){return we()?le:void 0},typedArrayTypes:be};function xe(e,t,r,o){this.x=n.defaultValue(e,0),this.y=n.defaultValue(t,0),this.z=n.defaultValue(r,0),this.w=n.defaultValue(o,0)}_e.supportsBasis=function(e){return _e.supportsWebAssembly()&&e.context.supportsBasis},_e.supportsFullscreen=function(){return Z.supportsFullscreen()},_e.supportsTypedArrays=function(){return"undefined"!=typeof ArrayBuffer},_e.supportsBigInt64Array=function(){return"undefined"!=typeof BigInt64Array},_e.supportsBigUint64Array=function(){return"undefined"!=typeof BigUint64Array},_e.supportsBigInt=function(){return"undefined"!=typeof BigInt},_e.supportsWebWorkers=function(){return"undefined"!=typeof Worker},_e.supportsWebAssembly=function(){return"undefined"!=typeof WebAssembly&&!_e.isEdge()};let Ee=new t.Cartesian3;xe.fromAxisAngle=function(e,o,i){r.Check.typeOf.object("axis",e),r.Check.typeOf.number("angle",o);const s=o/2,a=Math.sin(s);Ee=t.Cartesian3.normalize(e,Ee);const u=Ee.x*a,c=Ee.y*a,l=Ee.z*a,d=Math.cos(s);return n.defined(i)?(i.x=u,i.y=c,i.z=l,i.w=d,i):new xe(u,c,l,d)};const Oe=[1,2,0],Se=new Array(3);xe.fromRotationMatrix=function(e,o){let i,s,a,u,c;r.Check.typeOf.object("matrix",e);const l=e[t.Matrix3.COLUMN0ROW0],d=e[t.Matrix3.COLUMN1ROW1],f=e[t.Matrix3.COLUMN2ROW2],p=l+d+f;if(p>0)i=Math.sqrt(p+1),c=.5*i,i=.5/i,s=(e[t.Matrix3.COLUMN1ROW2]-e[t.Matrix3.COLUMN2ROW1])*i,a=(e[t.Matrix3.COLUMN2ROW0]-e[t.Matrix3.COLUMN0ROW2])*i,u=(e[t.Matrix3.COLUMN0ROW1]-e[t.Matrix3.COLUMN1ROW0])*i;else{const r=Oe;let n=0;d>l&&(n=1),f>l&&f>d&&(n=2);const o=r[n],p=r[o];i=Math.sqrt(e[t.Matrix3.getElementIndex(n,n)]-e[t.Matrix3.getElementIndex(o,o)]-e[t.Matrix3.getElementIndex(p,p)]+1);const h=Se;h[n]=.5*i,i=.5/i,c=(e[t.Matrix3.getElementIndex(p,o)]-e[t.Matrix3.getElementIndex(o,p)])*i,h[o]=(e[t.Matrix3.getElementIndex(o,n)]+e[t.Matrix3.getElementIndex(n,o)])*i,h[p]=(e[t.Matrix3.getElementIndex(p,n)]+e[t.Matrix3.getElementIndex(n,p)])*i,s=-h[0],a=-h[1],u=-h[2]}return n.defined(o)?(o.x=s,o.y=a,o.z=u,o.w=c,o):new xe(s,a,u,c)};const Ae=new xe;let qe=new xe,De=new xe,ke=new xe;xe.fromHeadingPitchRoll=function(e,n){return r.Check.typeOf.object("headingPitchRoll",e),ke=xe.fromAxisAngle(t.Cartesian3.UNIT_X,e.roll,Ae),De=xe.fromAxisAngle(t.Cartesian3.UNIT_Y,-e.pitch,n),n=xe.multiply(De,ke,De),qe=xe.fromAxisAngle(t.Cartesian3.UNIT_Z,-e.heading,Ae),xe.multiply(qe,n,n)};const Re=new t.Cartesian3,Ie=new t.Cartesian3,Pe=new xe,Te=new xe,ze=new xe;xe.packedLength=4,xe.pack=function(e,t,o){return r.Check.typeOf.object("value",e),r.Check.defined("array",t),o=n.defaultValue(o,0),t[o++]=e.x,t[o++]=e.y,t[o++]=e.z,t[o]=e.w,t},xe.unpack=function(e,t,o){return r.Check.defined("array",e),t=n.defaultValue(t,0),n.defined(o)||(o=new xe),o.x=e[t],o.y=e[t+1],o.z=e[t+2],o.w=e[t+3],o},xe.packedInterpolationLength=3,xe.convertPackedArrayForInterpolation=function(e,t,r,o){xe.unpack(e,4*r,ze),xe.conjugate(ze,ze);for(let i=0,s=r-t+1;i<s;i++){const r=3*i;xe.unpack(e,4*(t+i),Pe),xe.multiply(Pe,ze,Pe),Pe.w<0&&xe.negate(Pe,Pe),xe.computeAxis(Pe,Re);const s=xe.computeAngle(Pe);n.defined(o)||(o=[]),o[r]=Re.x*s,o[r+1]=Re.y*s,o[r+2]=Re.z*s}},xe.unpackInterpolationResult=function(e,r,o,i,s){n.defined(s)||(s=new xe),t.Cartesian3.fromArray(e,0,Ie);const a=t.Cartesian3.magnitude(Ie);return xe.unpack(r,4*i,Te),0===a?xe.clone(xe.IDENTITY,Pe):xe.fromAxisAngle(Ie,a,Pe),xe.multiply(Pe,Te,s)},xe.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new xe(e.x,e.y,e.z,e.w)},xe.conjugate=function(e,t){return r.Check.typeOf.object("quaternion",e),r.Check.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},xe.magnitudeSquared=function(e){return r.Check.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},xe.magnitude=function(e){return Math.sqrt(xe.magnitudeSquared(e))},xe.normalize=function(e,t){r.Check.typeOf.object("result",t);const n=1/xe.magnitude(e),o=e.x*n,i=e.y*n,s=e.z*n,a=e.w*n;return t.x=o,t.y=i,t.z=s,t.w=a,t},xe.inverse=function(e,t){r.Check.typeOf.object("result",t);const n=xe.magnitudeSquared(e);return t=xe.conjugate(e,t),xe.multiplyByScalar(t,1/n,t)},xe.add=function(e,t,n){return r.Check.typeOf.object("left",e),r.Check.typeOf.object("right",t),r.Check.typeOf.object("result",n),n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},xe.subtract=function(e,t,n){return r.Check.typeOf.object("left",e),r.Check.typeOf.object("right",t),r.Check.typeOf.object("result",n),n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},xe.negate=function(e,t){return r.Check.typeOf.object("quaternion",e),r.Check.typeOf.object("result",t),t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},xe.dot=function(e,t){return r.Check.typeOf.object("left",e),r.Check.typeOf.object("right",t),e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},xe.multiply=function(e,t,n){r.Check.typeOf.object("left",e),r.Check.typeOf.object("right",t),r.Check.typeOf.object("result",n);const o=e.x,i=e.y,s=e.z,a=e.w,u=t.x,c=t.y,l=t.z,d=t.w,f=a*u+o*d+i*l-s*c,p=a*c-o*l+i*d+s*u,h=a*l+o*c-i*u+s*d,m=a*d-o*u-i*c-s*l;return n.x=f,n.y=p,n.z=h,n.w=m,n},xe.multiplyByScalar=function(e,t,n){return r.Check.typeOf.object("quaternion",e),r.Check.typeOf.number("scalar",t),r.Check.typeOf.object("result",n),n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},xe.divideByScalar=function(e,t,n){return r.Check.typeOf.object("quaternion",e),r.Check.typeOf.number("scalar",t),r.Check.typeOf.object("result",n),n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},xe.computeAxis=function(e,t){r.Check.typeOf.object("quaternion",e),r.Check.typeOf.object("result",t);const n=e.w;if(Math.abs(n-1)<o.CesiumMath.EPSILON6)return t.x=t.y=t.z=0,t;const i=1/Math.sqrt(1-n*n);return t.x=e.x*i,t.y=e.y*i,t.z=e.z*i,t},xe.computeAngle=function(e){return r.Check.typeOf.object("quaternion",e),Math.abs(e.w-1)<o.CesiumMath.EPSILON6?0:2*Math.acos(e.w)};let Me=new xe;xe.lerp=function(e,t,n,o){return r.Check.typeOf.object("start",e),r.Check.typeOf.object("end",t),r.Check.typeOf.number("t",n),r.Check.typeOf.object("result",o),Me=xe.multiplyByScalar(t,n,Me),o=xe.multiplyByScalar(e,1-n,o),xe.add(Me,o,o)};let Ue=new xe,je=new xe,Fe=new xe;xe.slerp=function(e,t,n,i){r.Check.typeOf.object("start",e),r.Check.typeOf.object("end",t),r.Check.typeOf.number("t",n),r.Check.typeOf.object("result",i);let s=xe.dot(e,t),a=t;if(s<0&&(s=-s,a=Ue=xe.negate(t,Ue)),1-s<o.CesiumMath.EPSILON6)return xe.lerp(e,a,n,i);const u=Math.acos(s);return je=xe.multiplyByScalar(e,Math.sin((1-n)*u),je),Fe=xe.multiplyByScalar(a,Math.sin(n*u),Fe),i=xe.add(je,Fe,i),xe.multiplyByScalar(i,1/Math.sin(u),i)},xe.log=function(e,n){r.Check.typeOf.object("quaternion",e),r.Check.typeOf.object("result",n);const i=o.CesiumMath.acosClamped(e.w);let s=0;return 0!==i&&(s=i/Math.sin(i)),t.Cartesian3.multiplyByScalar(e,s,n)},xe.exp=function(e,n){r.Check.typeOf.object("cartesian",e),r.Check.typeOf.object("result",n);const o=t.Cartesian3.magnitude(e);let i=0;return 0!==o&&(i=Math.sin(o)/o),n.x=e.x*i,n.y=e.y*i,n.z=e.z*i,n.w=Math.cos(o),n};const Ne=new t.Cartesian3,Be=new t.Cartesian3,Ve=new xe,Le=new xe;xe.computeInnerQuadrangle=function(e,n,o,i){r.Check.typeOf.object("q0",e),r.Check.typeOf.object("q1",n),r.Check.typeOf.object("q2",o),r.Check.typeOf.object("result",i);const s=xe.conjugate(n,Ve);xe.multiply(s,o,Le);const a=xe.log(Le,Ne);xe.multiply(s,e,Le);const u=xe.log(Le,Be);return t.Cartesian3.add(a,u,a),t.Cartesian3.multiplyByScalar(a,.25,a),t.Cartesian3.negate(a,a),xe.exp(a,Ve),xe.multiply(n,Ve,i)},xe.squad=function(e,t,n,o,i,s){r.Check.typeOf.object("q0",e),r.Check.typeOf.object("q1",t),r.Check.typeOf.object("s0",n),r.Check.typeOf.object("s1",o),r.Check.typeOf.number("t",i),r.Check.typeOf.object("result",s);const a=xe.slerp(e,t,i,Ve),u=xe.slerp(n,o,i,Le);return xe.slerp(a,u,2*i*(1-i),s)};const Qe=new xe,We=1.9011074535173003,He=_e.supportsTypedArrays()?new Float32Array(8):[],Ye=_e.supportsTypedArrays()?new Float32Array(8):[],Ze=_e.supportsTypedArrays()?new Float32Array(8):[],Ge=_e.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){const t=e+1,r=2*t+1;He[e]=1/(t*r),Ye[e]=t/r}function Je(e,t,n){r.Check.defined("array",e),r.Check.defined("itemToFind",t),r.Check.defined("comparator",n);let o,i,s=0,a=e.length-1;for(;s<=a;)if(o=~~((s+a)/2),i=n(e[o],t),i<0)s=o+1;else{if(!(i>0))return o;a=o-1}return~(a+1)}function $e(e,t,r,n,o){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=r,this.yPoleOffset=n,this.ut1MinusUtc=o}function Xe(e,t,r,n,o,i,s,a){this.year=e,this.month=t,this.day=r,this.hour=n,this.minute=o,this.second=i,this.millisecond=s,this.isLeapSecond=a}function Ke(e){if(null===e||isNaN(e))throw new r.DeveloperError("year is required and must be a number.");return e%4==0&&e%100!=0||e%400==0}function et(e,t){this.julianDate=e,this.offset=t}He[7]=We/136,Ye[7]=8*We/17,xe.fastSlerp=function(e,t,n,o){r.Check.typeOf.object("start",e),r.Check.typeOf.object("end",t),r.Check.typeOf.number("t",n),r.Check.typeOf.object("result",o);let i,s=xe.dot(e,t);s>=0?i=1:(i=-1,s=-s);const a=s-1,u=1-n,c=n*n,l=u*u;for(let e=7;e>=0;--e)Ze[e]=(He[e]*c-Ye[e])*a,Ge[e]=(He[e]*l-Ye[e])*a;const d=i*n*(1+Ze[0]*(1+Ze[1]*(1+Ze[2]*(1+Ze[3]*(1+Ze[4]*(1+Ze[5]*(1+Ze[6]*(1+Ze[7])))))))),f=u*(1+Ge[0]*(1+Ge[1]*(1+Ge[2]*(1+Ge[3]*(1+Ge[4]*(1+Ge[5]*(1+Ge[6]*(1+Ge[7])))))))),p=xe.multiplyByScalar(e,f,Qe);return xe.multiplyByScalar(t,d,o),xe.add(p,o,o)},xe.fastSquad=function(e,t,n,o,i,s){r.Check.typeOf.object("q0",e),r.Check.typeOf.object("q1",t),r.Check.typeOf.object("s0",n),r.Check.typeOf.object("s1",o),r.Check.typeOf.number("t",i),r.Check.typeOf.object("result",s);const a=xe.fastSlerp(e,t,i,Ve),u=xe.fastSlerp(n,o,i,Le);return xe.fastSlerp(a,u,2*i*(1-i),s)},xe.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},xe.equalsEpsilon=function(e,t,r){return r=n.defaultValue(r,0),e===t||n.defined(e)&&n.defined(t)&&Math.abs(e.x-t.x)<=r&&Math.abs(e.y-t.y)<=r&&Math.abs(e.z-t.z)<=r&&Math.abs(e.w-t.w)<=r},xe.ZERO=Object.freeze(new xe(0,0,0,0)),xe.IDENTITY=Object.freeze(new xe(0,0,0,1)),xe.prototype.clone=function(e){return xe.clone(this,e)},xe.prototype.equals=function(e){return xe.equals(this,e)},xe.prototype.equalsEpsilon=function(e,t){return xe.equalsEpsilon(this,e,t)},xe.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var tt=Object.freeze({SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5});var rt=Object.freeze({UTC:0,TAI:1});const nt=new Xe,ot=[31,28,31,30,31,30,31,31,30,31,30,31];function it(e,t){return bt.compare(e.julianDate,t.julianDate)}const st=new et;function at(e){st.julianDate=e;const t=bt.leapSeconds;let r=Je(t,st,it);r<0&&(r=~r),r>=t.length&&(r=t.length-1);let n=t[r].offset;if(r>0){bt.secondsDifference(t[r].julianDate,e)>n&&(r--,n=t[r].offset)}bt.addSeconds(e,n,e)}function ut(e,t){st.julianDate=e;const r=bt.leapSeconds;let n=Je(r,st,it);if(n<0&&(n=~n),0===n)return bt.addSeconds(e,-r[0].offset,t);if(n>=r.length)return bt.addSeconds(e,-r[n-1].offset,t);const o=bt.secondsDifference(r[n].julianDate,e);return 0===o?bt.addSeconds(e,-r[n].offset,t):o<=1?void 0:bt.addSeconds(e,-r[--n].offset,t)}function ct(e,t,r){const n=t/tt.SECONDS_PER_DAY|0;return e+=n,(t-=tt.SECONDS_PER_DAY*n)<0&&(e--,t+=tt.SECONDS_PER_DAY),r.dayNumber=e,r.secondsOfDay=t,r}function lt(e,t,r,n,o,i,s){const a=(t-14)/12|0,u=e+4800+a;let c=(1461*u/4|0)+(367*(t-2-12*a)/12|0)-(3*((u+100)/100|0)/4|0)+r-32075;(n-=12)<0&&(n+=24);const l=i+(n*tt.SECONDS_PER_HOUR+o*tt.SECONDS_PER_MINUTE+s*tt.SECONDS_PER_MILLISECOND);return l>=43200&&(c-=1),[c,l]}const dt=/^(\d{4})$/,ft=/^(\d{4})-(\d{2})$/,pt=/^(\d{4})-?(\d{3})$/,ht=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,mt=/^(\d{4})-?(\d{2})-?(\d{2})$/,gt=/([Z+\-])?(\d{2})?:?(\d{2})?$/,yt=/^(\d{2})(\.\d+)?/.source+gt.source,vt=/^(\d{2}):?(\d{2})(\.\d+)?/.source+gt.source,wt=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+gt.source,Ct="Invalid ISO 8601 date.";function bt(e,t,r){this.dayNumber=void 0,this.secondsOfDay=void 0,e=n.defaultValue(e,0),t=n.defaultValue(t,0),r=n.defaultValue(r,rt.UTC);const o=0|e;ct(o,t+=(e-o)*tt.SECONDS_PER_DAY,this),r===rt.UTC&&at(this)}bt.fromGregorianDate=function(e,t){if(!(e instanceof Xe))throw new r.DeveloperError("date must be a valid GregorianDate.");const o=lt(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return n.defined(t)?(ct(o[0],o[1],t),at(t),t):new bt(o[0],o[1],rt.UTC)},bt.fromDate=function(e,t){if(!(e instanceof Date)||isNaN(e.getTime()))throw new r.DeveloperError("date must be a valid JavaScript Date.");const o=lt(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return n.defined(t)?(ct(o[0],o[1],t),at(t),t):new bt(o[0],o[1],rt.UTC)},bt.fromIso8601=function(e,t){if("string"!=typeof e)throw new r.DeveloperError(Ct);let o,i=(e=e.replace(",",".")).split("T"),s=1,a=1,u=0,c=0,l=0,d=0;const f=i[0],p=i[1];let h,m,g,y;if(!n.defined(f))throw new r.DeveloperError(Ct);if(i=f.match(mt),null!==i){if(g=f.split("-").length-1,g>0&&2!==g)throw new r.DeveloperError(Ct);o=+i[1],s=+i[2],a=+i[3]}else if(i=f.match(ft),null!==i)o=+i[1],s=+i[2];else if(i=f.match(dt),null!==i)o=+i[1];else{let e;if(i=f.match(pt),null!==i){if(o=+i[1],e=+i[2],m=Ke(o),e<1||m&&e>366||!m&&e>365)throw new r.DeveloperError(Ct)}else{if(i=f.match(ht),null===i)throw new r.DeveloperError(Ct);{o=+i[1];const t=+i[2],s=+i[3]||0;if(g=f.split("-").length-1,g>0&&(!n.defined(i[3])&&1!==g||n.defined(i[3])&&2!==g))throw new r.DeveloperError(Ct);e=7*t+s-new Date(Date.UTC(o,0,4)).getUTCDay()-3}}h=new Date(Date.UTC(o,0,1)),h.setUTCDate(e),s=h.getUTCMonth()+1,a=h.getUTCDate()}if(m=Ke(o),s<1||s>12||a<1||(2!==s||!m)&&a>ot[s-1]||m&&2===s&&a>29)throw new r.DeveloperError(Ct);if(n.defined(p)){if(i=p.match(wt),null!==i){if(g=p.split(":").length-1,g>0&&2!==g&&3!==g)throw new r.DeveloperError(Ct);u=+i[1],c=+i[2],l=+i[3],d=1e3*+(i[4]||0),y=5}else if(i=p.match(vt),null!==i){if(g=p.split(":").length-1,g>2)throw new r.DeveloperError(Ct);u=+i[1],c=+i[2],l=60*+(i[3]||0),y=4}else{if(i=p.match(yt),null===i)throw new r.DeveloperError(Ct);u=+i[1],c=60*+(i[2]||0),y=3}if(c>=60||l>=61||u>24||24===u&&(c>0||l>0||d>0))throw new r.DeveloperError(Ct);const e=i[y],t=+i[y+1],n=+(i[y+2]||0);switch(e){case"+":u-=t,c-=n;break;case"-":u+=t,c+=n;break;case"Z":break;default:c+=new Date(Date.UTC(o,s-1,a,u,c)).getTimezoneOffset()}}const v=60===l;for(v&&l--;c>=60;)c-=60,u++;for(;u>=24;)u-=24,a++;for(h=m&&2===s?29:ot[s-1];a>h;)a-=h,s++,s>12&&(s-=12,o++),h=m&&2===s?29:ot[s-1];for(;c<0;)c+=60,u--;for(;u<0;)u+=24,a--;for(;a<1;)s--,s<1&&(s+=12,o--),h=m&&2===s?29:ot[s-1],a+=h;const w=lt(o,s,a,u,c,l,d);return n.defined(t)?(ct(w[0],w[1],t),at(t)):t=new bt(w[0],w[1],rt.UTC),v&&bt.addSeconds(t,1,t),t},bt.now=function(e){return bt.fromDate(new Date,e)};const _t=new bt(0,0,rt.TAI);bt.toGregorianDate=function(e,t){if(!n.defined(e))throw new r.DeveloperError("julianDate is required.");let o=!1,i=ut(e,_t);n.defined(i)||(bt.addSeconds(e,-1,_t),i=ut(_t,_t),o=!0);let s=i.dayNumber;const a=i.secondsOfDay;a>=43200&&(s+=1);let u=s+68569|0;const c=4*u/146097|0;u=u-((146097*c+3)/4|0)|0;const l=4e3*(u+1)/1461001|0;u=u-(1461*l/4|0)+31|0;const d=80*u/2447|0,f=u-(2447*d/80|0)|0;u=d/11|0;const p=d+2-12*u|0,h=100*(c-49)+l+u|0;let m=a/tt.SECONDS_PER_HOUR|0,g=a-m*tt.SECONDS_PER_HOUR;const y=g/tt.SECONDS_PER_MINUTE|0;g-=y*tt.SECONDS_PER_MINUTE;let v=0|g;const w=(g-v)/tt.SECONDS_PER_MILLISECOND;return m+=12,m>23&&(m-=24),o&&(v+=1),n.defined(t)?(t.year=h,t.month=p,t.day=f,t.hour=m,t.minute=y,t.second=v,t.millisecond=w,t.isLeapSecond=o,t):new Xe(h,p,f,m,y,v,w,o)},bt.toDate=function(e){if(!n.defined(e))throw new r.DeveloperError("julianDate is required.");const t=bt.toGregorianDate(e,nt);let o=t.second;return t.isLeapSecond&&(o-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,o,t.millisecond))},bt.toIso8601=function(e,t){if(!n.defined(e))throw new r.DeveloperError("julianDate is required.");const o=bt.toGregorianDate(e,nt);let i=o.year,s=o.month,a=o.day,u=o.hour;const c=o.minute,l=o.second,d=o.millisecond;let f;return 1e4===i&&1===s&&1===a&&0===u&&0===c&&0===l&&0===d&&(i=9999,s=12,a=31,u=24),n.defined(t)||0===d?n.defined(t)&&0!==t?(f=(.01*d).toFixed(t).replace(".","").slice(0,t),i.toString().padStart(4,"0")+"-"+s.toString().padStart(2,"0")+"-"+a.toString().padStart(2,"0")+"T"+u.toString().padStart(2,"0")+":"+c.toString().padStart(2,"0")+":"+l.toString().padStart(2,"0")+"."+f+"Z"):i.toString().padStart(4,"0")+"-"+s.toString().padStart(2,"0")+"-"+a.toString().padStart(2,"0")+"T"+u.toString().padStart(2,"0")+":"+c.toString().padStart(2,"0")+":"+l.toString().padStart(2,"0")+"Z":(f=(.01*d).toString().replace(".",""),i.toString().padStart(4,"0")+"-"+s.toString().padStart(2,"0")+"-"+a.toString().padStart(2,"0")+"T"+u.toString().padStart(2,"0")+":"+c.toString().padStart(2,"0")+":"+l.toString().padStart(2,"0")+"."+f+"Z")},bt.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new bt(e.dayNumber,e.secondsOfDay,rt.TAI)},bt.compare=function(e,t){if(!n.defined(e))throw new r.DeveloperError("left is required.");if(!n.defined(t))throw new r.DeveloperError("right is required.");const o=e.dayNumber-t.dayNumber;return 0!==o?o:e.secondsOfDay-t.secondsOfDay},bt.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay},bt.equalsEpsilon=function(e,t,r){return r=n.defaultValue(r,0),e===t||n.defined(e)&&n.defined(t)&&Math.abs(bt.secondsDifference(e,t))<=r},bt.totalDays=function(e){if(!n.defined(e))throw new r.DeveloperError("julianDate is required.");return e.dayNumber+e.secondsOfDay/tt.SECONDS_PER_DAY},bt.secondsDifference=function(e,t){if(!n.defined(e))throw new r.DeveloperError("left is required.");if(!n.defined(t))throw new r.DeveloperError("right is required.");return(e.dayNumber-t.dayNumber)*tt.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)},bt.daysDifference=function(e,t){if(!n.defined(e))throw new r.DeveloperError("left is required.");if(!n.defined(t))throw new r.DeveloperError("right is required.");return e.dayNumber-t.dayNumber+(e.secondsOfDay-t.secondsOfDay)/tt.SECONDS_PER_DAY},bt.computeTaiMinusUtc=function(e){st.julianDate=e;const t=bt.leapSeconds;let r=Je(t,st,it);return r<0&&(r=~r,--r,r<0&&(r=0)),t[r].offset},bt.addSeconds=function(e,t,o){if(!n.defined(e))throw new r.DeveloperError("julianDate is required.");if(!n.defined(t))throw new r.DeveloperError("seconds is required.");if(!n.defined(o))throw new r.DeveloperError("result is required.");return ct(e.dayNumber,e.secondsOfDay+t,o)},bt.addMinutes=function(e,t,o){if(!n.defined(e))throw new r.DeveloperError("julianDate is required.");if(!n.defined(t))throw new r.DeveloperError("minutes is required.");if(!n.defined(o))throw new r.DeveloperError("result is required.");const i=e.secondsOfDay+t*tt.SECONDS_PER_MINUTE;return ct(e.dayNumber,i,o)},bt.addHours=function(e,t,o){if(!n.defined(e))throw new r.DeveloperError("julianDate is required.");if(!n.defined(t))throw new r.DeveloperError("hours is required.");if(!n.defined(o))throw new r.DeveloperError("result is required.");const i=e.secondsOfDay+t*tt.SECONDS_PER_HOUR;return ct(e.dayNumber,i,o)},bt.addDays=function(e,t,o){if(!n.defined(e))throw new r.DeveloperError("julianDate is required.");if(!n.defined(t))throw new r.DeveloperError("days is required.");if(!n.defined(o))throw new r.DeveloperError("result is required.");return ct(e.dayNumber+t,e.secondsOfDay,o)},bt.lessThan=function(e,t){return bt.compare(e,t)<0},bt.lessThanOrEquals=function(e,t){return bt.compare(e,t)<=0},bt.greaterThan=function(e,t){return bt.compare(e,t)>0},bt.greaterThanOrEquals=function(e,t){return bt.compare(e,t)>=0},bt.prototype.clone=function(e){return bt.clone(this,e)},bt.prototype.equals=function(e){return bt.equals(this,e)},bt.prototype.equalsEpsilon=function(e,t){return bt.equalsEpsilon(this,e,t)},bt.prototype.toString=function(){return bt.toIso8601(this)},bt.leapSeconds=[new et(new bt(2441317,43210,rt.TAI),10),new et(new bt(2441499,43211,rt.TAI),11),new et(new bt(2441683,43212,rt.TAI),12),new et(new bt(2442048,43213,rt.TAI),13),new et(new bt(2442413,43214,rt.TAI),14),new et(new bt(2442778,43215,rt.TAI),15),new et(new bt(2443144,43216,rt.TAI),16),new et(new bt(2443509,43217,rt.TAI),17),new et(new bt(2443874,43218,rt.TAI),18),new et(new bt(2444239,43219,rt.TAI),19),new et(new bt(2444786,43220,rt.TAI),20),new et(new bt(2445151,43221,rt.TAI),21),new et(new bt(2445516,43222,rt.TAI),22),new et(new bt(2446247,43223,rt.TAI),23),new et(new bt(2447161,43224,rt.TAI),24),new et(new bt(2447892,43225,rt.TAI),25),new et(new bt(2448257,43226,rt.TAI),26),new et(new bt(2448804,43227,rt.TAI),27),new et(new bt(2449169,43228,rt.TAI),28),new et(new bt(2449534,43229,rt.TAI),29),new et(new bt(2450083,43230,rt.TAI),30),new et(new bt(2450630,43231,rt.TAI),31),new et(new bt(2451179,43232,rt.TAI),32),new et(new bt(2453736,43233,rt.TAI),33),new et(new bt(2454832,43234,rt.TAI),34),new et(new bt(2456109,43235,rt.TAI),35),new et(new bt(2457204,43236,rt.TAI),36),new et(new bt(2457754,43237,rt.TAI),37)];var xt=n.createCommonjsModule((function(e,t){!function(r){var o=t&&!t.nodeType&&t,i=e&&!e.nodeType&&e,s="object"==typeof n.commonjsGlobal&&n.commonjsGlobal;s.global!==s&&s.window!==s&&s.self!==s||(r=s);var a,u,c=2147483647,l=36,d=/^xn--/,f=/[^\x20-\x7E]/,p=/[\x2E\u3002\uFF0E\uFF61]/g,h={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},m=Math.floor,g=String.fromCharCode;function y(e){throw new RangeError(h[e])}function v(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function w(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+v((e=e.replace(p,".")).split("."),t).join(".")}function C(e){for(var t,r,n=[],o=0,i=e.length;o<i;)(t=e.charCodeAt(o++))>=55296&&t<=56319&&o<i?56320==(64512&(r=e.charCodeAt(o++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),o--):n.push(t);return n}function b(e){return v(e,(function(e){var t="";return e>65535&&(t+=g((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=g(e)})).join("")}function _(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function x(e,t,r){var n=0;for(e=r?m(e/700):e>>1,e+=m(e/t);e>455;n+=l)e=m(e/35);return m(n+36*e/(e+38))}function E(e){var t,r,n,o,i,s,a,u,d,f,p,h=[],g=e.length,v=0,w=128,C=72;for((r=e.lastIndexOf("-"))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&y("not-basic"),h.push(e.charCodeAt(n));for(o=r>0?r+1:0;o<g;){for(i=v,s=1,a=l;o>=g&&y("invalid-input"),((u=(p=e.charCodeAt(o++))-48<10?p-22:p-65<26?p-65:p-97<26?p-97:l)>=l||u>m((c-v)/s))&&y("overflow"),v+=u*s,!(u<(d=a<=C?1:a>=C+26?26:a-C));a+=l)s>m(c/(f=l-d))&&y("overflow"),s*=f;C=x(v-i,t=h.length+1,0==i),m(v/t)>c-w&&y("overflow"),w+=m(v/t),v%=t,h.splice(v++,0,w)}return b(h)}function O(e){var t,r,n,o,i,s,a,u,d,f,p,h,v,w,b,E=[];for(h=(e=C(e)).length,t=128,r=0,i=72,s=0;s<h;++s)(p=e[s])<128&&E.push(g(p));for(n=o=E.length,o&&E.push("-");n<h;){for(a=c,s=0;s<h;++s)(p=e[s])>=t&&p<a&&(a=p);for(a-t>m((c-r)/(v=n+1))&&y("overflow"),r+=(a-t)*v,t=a,s=0;s<h;++s)if((p=e[s])<t&&++r>c&&y("overflow"),p==t){for(u=r,d=l;!(u<(f=d<=i?1:d>=i+26?26:d-i));d+=l)b=u-f,w=l-f,E.push(g(_(f+b%w,0))),u=m(b/w);E.push(g(_(u,0))),i=x(r,v,n==o),r=0,++n}++r,++t}return E.join("")}if(a={version:"1.3.2",ucs2:{decode:C,encode:b},decode:E,encode:O,toASCII:function(e){return w(e,(function(e){return f.test(e)?"xn--"+O(e):e}))},toUnicode:function(e){return w(e,(function(e){return d.test(e)?E(e.slice(4).toLowerCase()):e}))}},o&&i)if(e.exports==o)i.exports=a;else for(u in a)a.hasOwnProperty(u)&&(o[u]=a[u]);else r.punycode=a}(n.commonjsGlobal)})),Et=n.createCommonjsModule((function(e){
/*!
   * URI.js - Mutating URLs
   * IPv6 Support
   *
   * Version: 1.19.11
   *
   * Author: Rodney Rehm
   * Web: http://medialize.github.io/URI.js/
   *
   * Licensed under
   *   MIT License http://www.opensource.org/licenses/mit-license
   *
   */var t,r;t=n.commonjsGlobal,r=function(e){var t=e&&e.IPv6;return{best:function(e){var t,r,n=e.toLowerCase().split(":"),o=n.length,i=8;for(""===n[0]&&""===n[1]&&""===n[2]?(n.shift(),n.shift()):""===n[0]&&""===n[1]?n.shift():""===n[o-1]&&""===n[o-2]&&n.pop(),-1!==n[(o=n.length)-1].indexOf(".")&&(i=7),t=0;t<o&&""!==n[t];t++);if(t<i)for(n.splice(t,1,"0000");n.length<i;)n.splice(t,0,"0000");for(var s=0;s<i;s++){r=n[s].split("");for(var a=0;a<3&&"0"===r[0]&&r.length>1;a++)r.splice(0,1);n[s]=r.join("")}var u=-1,c=0,l=0,d=-1,f=!1;for(s=0;s<i;s++)f?"0"===n[s]?l+=1:(f=!1,l>c&&(u=d,c=l)):"0"===n[s]&&(f=!0,d=s,l=1);l>c&&(u=d,c=l),c>1&&n.splice(u,c,""),o=n.length;var p="";for(""===n[0]&&(p=":"),s=0;s<o&&(p+=n[s],s!==o-1);s++)p+=":";return""===n[o-1]&&(p+=":"),p},noConflict:function(){return e.IPv6===this&&(e.IPv6=t),this}}},e.exports?e.exports=r():t.IPv6=r(t)})),Ot=n.createCommonjsModule((function(e){
/*!
   * URI.js - Mutating URLs
   * Second Level Domain (SLD) Support
   *
   * Version: 1.19.11
   *
   * Author: Rodney Rehm
   * Web: http://medialize.github.io/URI.js/
   *
   * Licensed under
   *   MIT License http://www.opensource.org/licenses/mit-license
   *
   */var t,r;t=n.commonjsGlobal,r=function(e){var t=e&&e.SecondLevelDomains,r={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;var n=e.lastIndexOf(".",t-1);if(n<=0||n>=t-1)return!1;var o=r.list[e.slice(t+1)];return!!o&&o.indexOf(" "+e.slice(n+1,t)+" ")>=0},is:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return!1;if(e.lastIndexOf(".",t-1)>=0)return!1;var n=r.list[e.slice(t+1)];return!!n&&n.indexOf(" "+e.slice(0,t)+" ")>=0},get:function(e){var t=e.lastIndexOf(".");if(t<=0||t>=e.length-1)return null;var n=e.lastIndexOf(".",t-1);if(n<=0||n>=t-1)return null;var o=r.list[e.slice(t+1)];return o?o.indexOf(" "+e.slice(n+1,t)+" ")<0?null:e.slice(n+1):null},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=t),this}};return r},e.exports?e.exports=r():t.SecondLevelDomains=r(t)})),St=n.createCommonjsModule((function(e){
/*!
   * URI.js - Mutating URLs
   *
   * Version: 1.19.11
   *
   * Author: Rodney Rehm
   * Web: http://medialize.github.io/URI.js/
   *
   * Licensed under
   *   MIT License http://www.opensource.org/licenses/mit-license
   *
   */var t,r;t=n.commonjsGlobal,r=function(e,t,r,n){var o=n&&n.URI;function i(e,t){var r=arguments.length>=1,n=arguments.length>=2;if(!(this instanceof i))return r?n?new i(e,t):new i(e):new i;if(void 0===e){if(r)throw new TypeError("undefined is not a valid argument for URI");e="undefined"!=typeof location?location.href+"":""}if(null===e&&r)throw new TypeError("null is not a valid argument for URI");return this.href(e),void 0!==t?this.absoluteTo(t):this}i.version="1.19.11";var s=i.prototype,a=Object.prototype.hasOwnProperty;function u(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function c(e){return void 0===e?"Undefined":String(Object.prototype.toString.call(e)).slice(8,-1)}function l(e){return"Array"===c(e)}function d(e,t){var r,n,o={};if("RegExp"===c(t))o=null;else if(l(t))for(r=0,n=t.length;r<n;r++)o[t[r]]=!0;else o[t]=!0;for(r=0,n=e.length;r<n;r++)(o&&void 0!==o[e[r]]||!o&&t.test(e[r]))&&(e.splice(r,1),n--,r--);return e}function f(e,t){var r,n;if(l(t)){for(r=0,n=t.length;r<n;r++)if(!f(e,t[r]))return!1;return!0}var o=c(t);for(r=0,n=e.length;r<n;r++)if("RegExp"===o){if("string"==typeof e[r]&&e[r].match(t))return!0}else if(e[r]===t)return!0;return!1}function p(e,t){if(!l(e)||!l(t))return!1;if(e.length!==t.length)return!1;e.sort(),t.sort();for(var r=0,n=e.length;r<n;r++)if(e[r]!==t[r])return!1;return!0}function h(e){return e.replace(/^\/+|\/+$/g,"")}function m(e){return escape(e)}function g(e){return encodeURIComponent(e).replace(/[!'()*]/g,m).replace(/\*/g,"%2A")}i._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:i.preventInvalidHostname,duplicateQueryParameters:i.duplicateQueryParameters,escapeQuerySpace:i.escapeQuerySpace}},i.preventInvalidHostname=!1,i.duplicateQueryParameters=!1,i.escapeQuerySpace=!0,i.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,i.idn_expression=/[^a-z0-9\._-]/i,i.punycode_expression=/(xn--)/i,i.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,i.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,i.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,i.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},i.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,i.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},i.hostProtocols=["http","https"],i.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,i.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},i.getDomAttribute=function(e){if(e&&e.nodeName){var t=e.nodeName.toLowerCase();if("input"!==t||"image"===e.type)return i.domAttributes[t]}},i.encode=g,i.decode=decodeURIComponent,i.iso8859=function(){i.encode=escape,i.decode=unescape},i.unicode=function(){i.encode=g,i.decode=decodeURIComponent},i.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},i.encodeQuery=function(e,t){var r=i.encode(e+"");return void 0===t&&(t=i.escapeQuerySpace),t?r.replace(/%20/g,"+"):r},i.decodeQuery=function(e,t){e+="",void 0===t&&(t=i.escapeQuerySpace);try{return i.decode(t?e.replace(/\+/g,"%20"):e)}catch(t){return e}};var y,v={encode:"encode",decode:"decode"},w=function(e,t){return function(r){try{return i[t](r+"").replace(i.characters[e][t].expression,(function(r){return i.characters[e][t].map[r]}))}catch(e){return r}}};for(y in v)i[y+"PathSegment"]=w("pathname",v[y]),i[y+"UrnPathSegment"]=w("urnpath",v[y]);var C=function(e,t,r){return function(n){var o;o=r?function(e){return i[t](i[r](e))}:i[t];for(var s=(n+"").split(e),a=0,u=s.length;a<u;a++)s[a]=o(s[a]);return s.join(e)}};function b(e){return function(t,r){return void 0===t?this._parts[e]||"":(this._parts[e]=t||null,this.build(!r),this)}}function _(e,t){return function(r,n){return void 0===r?this._parts[e]||"":(null!==r&&(r+="").charAt(0)===t&&(r=r.substring(1)),this._parts[e]=r,this.build(!n),this)}}i.decodePath=C("/","decodePathSegment"),i.decodeUrnPath=C(":","decodeUrnPathSegment"),i.recodePath=C("/","encodePathSegment","decode"),i.recodeUrnPath=C(":","encodeUrnPathSegment","decode"),i.encodeReserved=w("reserved","encode"),i.parse=function(e,t){var r;return t||(t={preventInvalidHostname:i.preventInvalidHostname}),(r=(e=(e=e.replace(i.leading_whitespace_expression,"")).replace(i.ascii_tab_whitespace,"")).indexOf("#"))>-1&&(t.fragment=e.substring(r+1)||null,e=e.substring(0,r)),(r=e.indexOf("?"))>-1&&(t.query=e.substring(r+1)||null,e=e.substring(0,r)),"//"===(e=(e=e.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://")).replace(/^[/\\]{2,}/i,"//")).substring(0,2)?(t.protocol=null,e=e.substring(2),e=i.parseAuthority(e,t)):(r=e.indexOf(":"))>-1&&(t.protocol=e.substring(0,r)||null,t.protocol&&!t.protocol.match(i.protocol_expression)?t.protocol=void 0:"//"===e.substring(r+1,r+3).replace(/\\/g,"/")?(e=e.substring(r+3),e=i.parseAuthority(e,t)):(e=e.substring(r+1),t.urn=!0)),t.path=e,t},i.parseHost=function(e,t){e||(e="");var r,n,o=(e=e.replace(/\\/g,"/")).indexOf("/");if(-1===o&&(o=e.length),"["===e.charAt(0))r=e.indexOf("]"),t.hostname=e.substring(1,r)||null,t.port=e.substring(r+2,o)||null,"/"===t.port&&(t.port=null);else{var s=e.indexOf(":"),a=e.indexOf("/"),u=e.indexOf(":",s+1);-1!==u&&(-1===a||u<a)?(t.hostname=e.substring(0,o)||null,t.port=null):(n=e.substring(0,o).split(":"),t.hostname=n[0]||null,t.port=n[1]||null)}return t.hostname&&"/"!==e.substring(o).charAt(0)&&(o++,e="/"+e),t.preventInvalidHostname&&i.ensureValidHostname(t.hostname,t.protocol),t.port&&i.ensureValidPort(t.port),e.substring(o)||"/"},i.parseAuthority=function(e,t){return e=i.parseUserinfo(e,t),i.parseHost(e,t)},i.parseUserinfo=function(e,t){var r=e;-1!==e.indexOf("\\")&&(e=e.replace(/\\/g,"/"));var n,o=e.indexOf("/"),s=e.lastIndexOf("@",o>-1?o:e.length-1);return s>-1&&(-1===o||s<o)?(n=e.substring(0,s).split(":"),t.username=n[0]?i.decode(n[0]):null,n.shift(),t.password=n[0]?i.decode(n.join(":")):null,e=r.substring(s+1)):(t.username=null,t.password=null),e},i.parseQuery=function(e,t){if(!e)return{};if(!(e=e.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var r,n,o,s={},u=e.split("&"),c=u.length,l=0;l<c;l++)r=u[l].split("="),n=i.decodeQuery(r.shift(),t),o=r.length?i.decodeQuery(r.join("="),t):null,"__proto__"!==n&&(a.call(s,n)?("string"!=typeof s[n]&&null!==s[n]||(s[n]=[s[n]]),s[n].push(o)):s[n]=o);return s},i.build=function(e){var t="",r=!1;return e.protocol&&(t+=e.protocol+":"),e.urn||!t&&!e.hostname||(t+="//",r=!0),t+=i.buildAuthority(e)||"","string"==typeof e.path&&("/"!==e.path.charAt(0)&&r&&(t+="/"),t+=e.path),"string"==typeof e.query&&e.query&&(t+="?"+e.query),"string"==typeof e.fragment&&e.fragment&&(t+="#"+e.fragment),t},i.buildHost=function(e){var t="";return e.hostname?(i.ip6_expression.test(e.hostname)?t+="["+e.hostname+"]":t+=e.hostname,e.port&&(t+=":"+e.port),t):""},i.buildAuthority=function(e){return i.buildUserinfo(e)+i.buildHost(e)},i.buildUserinfo=function(e){var t="";return e.username&&(t+=i.encode(e.username)),e.password&&(t+=":"+i.encode(e.password)),t&&(t+="@"),t},i.buildQuery=function(e,t,r){var n,o,s,u,c="";for(o in e)if("__proto__"!==o&&a.call(e,o))if(l(e[o]))for(n={},s=0,u=e[o].length;s<u;s++)void 0!==e[o][s]&&void 0===n[e[o][s]+""]&&(c+="&"+i.buildQueryParameter(o,e[o][s],r),!0!==t&&(n[e[o][s]+""]=!0));else void 0!==e[o]&&(c+="&"+i.buildQueryParameter(o,e[o],r));return c.substring(1)},i.buildQueryParameter=function(e,t,r){return i.encodeQuery(e,r)+(null!==t?"="+i.encodeQuery(t,r):"")},i.addQuery=function(e,t,r){if("object"==typeof t)for(var n in t)a.call(t,n)&&i.addQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===e[t])return void(e[t]=r);"string"==typeof e[t]&&(e[t]=[e[t]]),l(r)||(r=[r]),e[t]=(e[t]||[]).concat(r)}},i.setQuery=function(e,t,r){if("object"==typeof t)for(var n in t)a.call(t,n)&&i.setQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");e[t]=void 0===r?null:r}},i.removeQuery=function(e,t,r){var n,o,s;if(l(t))for(n=0,o=t.length;n<o;n++)e[t[n]]=void 0;else if("RegExp"===c(t))for(s in e)t.test(s)&&(e[s]=void 0);else if("object"==typeof t)for(s in t)a.call(t,s)&&i.removeQuery(e,s,t[s]);else{if("string"!=typeof t)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==r?"RegExp"===c(r)?!l(e[t])&&r.test(e[t])?e[t]=void 0:e[t]=d(e[t],r):e[t]!==String(r)||l(r)&&1!==r.length?l(e[t])&&(e[t]=d(e[t],r)):e[t]=void 0:e[t]=void 0}},i.hasQuery=function(e,t,r,n){switch(c(t)){case"String":break;case"RegExp":for(var o in e)if(a.call(e,o)&&t.test(o)&&(void 0===r||i.hasQuery(e,o,r)))return!0;return!1;case"Object":for(var s in t)if(a.call(t,s)&&!i.hasQuery(e,s,t[s]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(c(r)){case"Undefined":return t in e;case"Boolean":return r===Boolean(l(e[t])?e[t].length:e[t]);case"Function":return!!r(e[t],t,e);case"Array":return!!l(e[t])&&(n?f:p)(e[t],r);case"RegExp":return l(e[t])?!!n&&f(e[t],r):Boolean(e[t]&&e[t].match(r));case"Number":r=String(r);case"String":return l(e[t])?!!n&&f(e[t],r):e[t]===r;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},i.joinPaths=function(){for(var e=[],t=[],r=0,n=0;n<arguments.length;n++){var o=new i(arguments[n]);e.push(o);for(var s=o.segment(),a=0;a<s.length;a++)"string"==typeof s[a]&&t.push(s[a]),s[a]&&r++}if(!t.length||!r)return new i("");var u=new i("").segment(t);return""!==e[0].path()&&"/"!==e[0].path().slice(0,1)||u.path("/"+u.path()),u.normalize()},i.commonPath=function(e,t){var r,n=Math.min(e.length,t.length);for(r=0;r<n;r++)if(e.charAt(r)!==t.charAt(r)){r--;break}return r<1?e.charAt(0)===t.charAt(0)&&"/"===e.charAt(0)?"/":"":("/"===e.charAt(r)&&"/"===t.charAt(r)||(r=e.substring(0,r).lastIndexOf("/")),e.substring(0,r+1))},i.withinString=function(e,t,r){r||(r={});var n=r.start||i.findUri.start,o=r.end||i.findUri.end,s=r.trim||i.findUri.trim,a=r.parens||i.findUri.parens,u=/[a-z0-9-]=["']?$/i;for(n.lastIndex=0;;){var c=n.exec(e);if(!c)break;var l=c.index;if(r.ignoreHtml){var d=e.slice(Math.max(l-3,0),l);if(d&&u.test(d))continue}for(var f=l+e.slice(l).search(o),p=e.slice(l,f),h=-1;;){var m=a.exec(p);if(!m)break;var g=m.index+m[0].length;h=Math.max(h,g)}if(!((p=h>-1?p.slice(0,h)+p.slice(h).replace(s,""):p.replace(s,"")).length<=c[0].length||r.ignore&&r.ignore.test(p))){var y=t(p,l,f=l+p.length,e);void 0!==y?(y=String(y),e=e.slice(0,l)+y+e.slice(f),n.lastIndex=l+y.length):n.lastIndex=f}}return n.lastIndex=0,e},i.ensureValidHostname=function(t,r){var n=!!t,o=!1;if(!!r&&(o=f(i.hostProtocols,r)),o&&!n)throw new TypeError("Hostname cannot be empty, if protocol is "+r);if(t&&t.match(i.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(t).match(i.invalid_hostname_characters))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_]')}},i.ensureValidPort=function(e){if(e){var t=Number(e);if(!(/^[0-9]+$/.test(t)&&t>0&&t<65536))throw new TypeError('Port "'+e+'" is not a valid port')}},i.noConflict=function(e){if(e){var t={URI:this.noConflict()};return n.URITemplate&&"function"==typeof n.URITemplate.noConflict&&(t.URITemplate=n.URITemplate.noConflict()),n.IPv6&&"function"==typeof n.IPv6.noConflict&&(t.IPv6=n.IPv6.noConflict()),n.SecondLevelDomains&&"function"==typeof n.SecondLevelDomains.noConflict&&(t.SecondLevelDomains=n.SecondLevelDomains.noConflict()),t}return n.URI===this&&(n.URI=o),this},s.build=function(e){return!0===e?this._deferred_build=!0:(void 0===e||this._deferred_build)&&(this._string=i.build(this._parts),this._deferred_build=!1),this},s.clone=function(){return new i(this)},s.valueOf=s.toString=function(){return this.build(!1)._string},s.protocol=b("protocol"),s.username=b("username"),s.password=b("password"),s.hostname=b("hostname"),s.port=b("port"),s.query=_("query","?"),s.fragment=_("fragment","#"),s.search=function(e,t){var r=this.query(e,t);return"string"==typeof r&&r.length?"?"+r:r},s.hash=function(e,t){var r=this.fragment(e,t);return"string"==typeof r&&r.length?"#"+r:r},s.pathname=function(e,t){if(void 0===e||!0===e){var r=this._parts.path||(this._parts.hostname?"/":"");return e?(this._parts.urn?i.decodeUrnPath:i.decodePath)(r):r}return this._parts.urn?this._parts.path=e?i.recodeUrnPath(e):"":this._parts.path=e?i.recodePath(e):"/",this.build(!t),this},s.path=s.pathname,s.href=function(e,t){var r;if(void 0===e)return this.toString();this._string="",this._parts=i._parts();var n=e instanceof i,o="object"==typeof e&&(e.hostname||e.path||e.pathname);if(e.nodeName&&(e=e[i.getDomAttribute(e)]||"",o=!1),!n&&o&&void 0!==e.pathname&&(e=e.toString()),"string"==typeof e||e instanceof String)this._parts=i.parse(String(e),this._parts);else{if(!n&&!o)throw new TypeError("invalid input");var s=n?e._parts:e;for(r in s)"query"!==r&&a.call(this._parts,r)&&(this._parts[r]=s[r]);s.query&&this.query(s.query,!1)}return this.build(!t),this},s.is=function(e){var t=!1,n=!1,o=!1,s=!1,a=!1,u=!1,c=!1,l=!this._parts.urn;switch(this._parts.hostname&&(l=!1,n=i.ip4_expression.test(this._parts.hostname),o=i.ip6_expression.test(this._parts.hostname),a=(s=!(t=n||o))&&r&&r.has(this._parts.hostname),u=s&&i.idn_expression.test(this._parts.hostname),c=s&&i.punycode_expression.test(this._parts.hostname)),e.toLowerCase()){case"relative":return l;case"absolute":return!l;case"domain":case"name":return s;case"sld":return a;case"ip":return t;case"ip4":case"ipv4":case"inet4":return n;case"ip6":case"ipv6":case"inet6":return o;case"idn":return u;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return c}return null};var x=s.protocol,E=s.port,O=s.hostname;s.protocol=function(e,t){if(e&&!(e=e.replace(/:(\/\/)?$/,"")).match(i.protocol_expression))throw new TypeError('Protocol "'+e+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return x.call(this,e,t)},s.scheme=s.protocol,s.port=function(e,t){return this._parts.urn?void 0===e?"":this:(void 0!==e&&(0===e&&(e=null),e&&(":"===(e+="").charAt(0)&&(e=e.substring(1)),i.ensureValidPort(e))),E.call(this,e,t))},s.hostname=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e){var r={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==i.parseHost(e,r))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');e=r.hostname,this._parts.preventInvalidHostname&&i.ensureValidHostname(e,this._parts.protocol)}return O.call(this,e,t)},s.origin=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var r=this.protocol();return this.authority()?(r?r+"://":"")+this.authority():""}var n=i(e);return this.protocol(n.protocol()).authority(n.authority()).build(!t),this},s.host=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildHost(this._parts):"";if("/"!==i.parseHost(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.authority=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?i.buildAuthority(this._parts):"";if("/"!==i.parseAuthority(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},s.userinfo=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var r=i.buildUserinfo(this._parts);return r?r.substring(0,r.length-1):r}return"@"!==e[e.length-1]&&(e+="@"),i.parseUserinfo(e,this._parts),this.build(!t),this},s.resource=function(e,t){var r;return void 0===e?this.path()+this.search()+this.hash():(r=i.parse(e),this._parts.path=r.path,this._parts.query=r.query,this._parts.fragment=r.fragment,this.build(!t),this)},s.subdomain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,r)||""}var n=this._parts.hostname.length-this.domain().length,o=this._parts.hostname.substring(0,n),s=new RegExp("^"+u(o));if(e&&"."!==e.charAt(e.length-1)&&(e+="."),-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");return e&&i.ensureValidHostname(e,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(s,e),this.build(!t),this},s.domain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.match(/\./g);if(r&&r.length<2)return this._parts.hostname;var n=this._parts.hostname.length-this.tld(t).length-1;return n=this._parts.hostname.lastIndexOf(".",n-1)+1,this._parts.hostname.substring(n)||""}if(!e)throw new TypeError("cannot set domain empty");if(-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(i.ensureValidHostname(e,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=e;else{var o=new RegExp(u(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(o,e)}return this.build(!t),this},s.tld=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.lastIndexOf("."),o=this._parts.hostname.substring(n+1);return!0!==t&&r&&r.list[o.toLowerCase()]&&r.get(this._parts.hostname)||o}var i;if(!e)throw new TypeError("cannot set TLD empty");if(e.match(/[^a-zA-Z0-9-]/)){if(!r||!r.is(e))throw new TypeError('TLD "'+e+'" contains characters other than [A-Z0-9]');i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,e)}return this.build(!t),this},s.directory=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var r=this._parts.path.length-this.filename().length-1,n=this._parts.path.substring(0,r)||(this._parts.hostname?"/":"");return e?i.decodePath(n):n}var o=this._parts.path.length-this.filename().length,s=this._parts.path.substring(0,o),a=new RegExp("^"+u(s));return this.is("relative")||(e||(e="/"),"/"!==e.charAt(0)&&(e="/"+e)),e&&"/"!==e.charAt(e.length-1)&&(e+="/"),e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e),this.build(!t),this},s.filename=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("string"!=typeof e){if(!this._parts.path||"/"===this._parts.path)return"";var r=this._parts.path.lastIndexOf("/"),n=this._parts.path.substring(r+1);return e?i.decodePathSegment(n):n}var o=!1;"/"===e.charAt(0)&&(e=e.substring(1)),e.match(/\.?\//)&&(o=!0);var s=new RegExp(u(this.filename())+"$");return e=i.recodePath(e),this._parts.path=this._parts.path.replace(s,e),o?this.normalizePath(t):this.build(!t),this},s.suffix=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path||"/"===this._parts.path)return"";var r,n,o=this.filename(),s=o.lastIndexOf(".");return-1===s?"":(r=o.substring(s+1),n=/^[a-z0-9%]+$/i.test(r)?r:"",e?i.decodePathSegment(n):n)}"."===e.charAt(0)&&(e=e.substring(1));var a,c=this.suffix();if(c)a=e?new RegExp(u(c)+"$"):new RegExp(u("."+c)+"$");else{if(!e)return this;this._parts.path+="."+i.recodePath(e)}return a&&(e=i.recodePath(e),this._parts.path=this._parts.path.replace(a,e)),this.build(!t),this},s.segment=function(e,t,r){var n=this._parts.urn?":":"/",o=this.path(),i="/"===o.substring(0,1),s=o.split(n);if(void 0!==e&&"number"!=typeof e&&(r=t,t=e,e=void 0),void 0!==e&&"number"!=typeof e)throw new Error('Bad segment "'+e+'", must be 0-based integer');if(i&&s.shift(),e<0&&(e=Math.max(s.length+e,0)),void 0===t)return void 0===e?s:s[e];if(null===e||void 0===s[e])if(l(t)){s=[];for(var a=0,u=t.length;a<u;a++)(t[a].length||s.length&&s[s.length-1].length)&&(s.length&&!s[s.length-1].length&&s.pop(),s.push(h(t[a])))}else(t||"string"==typeof t)&&(t=h(t),""===s[s.length-1]?s[s.length-1]=t:s.push(t));else t?s[e]=h(t):s.splice(e,1);return i&&s.unshift(""),this.path(s.join(n),r)},s.segmentCoded=function(e,t,r){var n,o,s;if("number"!=typeof e&&(r=t,t=e,e=void 0),void 0===t){if(l(n=this.segment(e,t,r)))for(o=0,s=n.length;o<s;o++)n[o]=i.decode(n[o]);else n=void 0!==n?i.decode(n):void 0;return n}if(l(t))for(o=0,s=t.length;o<s;o++)t[o]=i.encode(t[o]);else t="string"==typeof t||t instanceof String?i.encode(t):t;return this.segment(e,t,r)};var S=s.query;return s.query=function(e,t){if(!0===e)return i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof e){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace),n=e.call(this,r);return this._parts.query=i.buildQuery(n||r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this}return void 0!==e&&"string"!=typeof e?(this._parts.query=i.buildQuery(e,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this):S.call(this,e,t)},s.setQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof e||e instanceof String)n[e]=void 0!==t?t:null;else{if("object"!=typeof e)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var o in e)a.call(e,o)&&(n[o]=e[o])}return this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.addQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.addQuery(n,e,void 0===t?null:t),this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.removeQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.removeQuery(n,e,t),this._parts.query=i.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},s.hasQuery=function(e,t,r){var n=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.hasQuery(n,e,t,r)},s.setSearch=s.setQuery,s.addSearch=s.addQuery,s.removeSearch=s.removeQuery,s.hasSearch=s.hasQuery,s.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},s.normalizeProtocol=function(e){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!e)),this},s.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},s.normalizePort=function(e){return"string"==typeof this._parts.protocol&&this._parts.port===i.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!e)),this},s.normalizePath=function(e){var t,r=this._parts.path;if(!r)return this;if(this._parts.urn)return this._parts.path=i.recodeUrnPath(this._parts.path),this.build(!e),this;if("/"===this._parts.path)return this;var n,o,s="";for("/"!==(r=i.recodePath(r)).charAt(0)&&(t=!0,r="/"+r),"/.."!==r.slice(-3)&&"/."!==r.slice(-2)||(r+="/"),r=r.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),t&&(s=r.substring(1).match(/^(\.\.\/)+/)||"")&&(s=s[0]);-1!==(n=r.search(/\/\.\.(\/|$)/));)0!==n?(-1===(o=r.substring(0,n).lastIndexOf("/"))&&(o=n),r=r.substring(0,o)+r.substring(n+3)):r=r.substring(3);return t&&this.is("relative")&&(r=s+r.substring(1)),this._parts.path=r,this.build(!e),this},s.normalizePathname=s.normalizePath,s.normalizeQuery=function(e){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(i.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!e)),this},s.normalizeFragment=function(e){return this._parts.fragment||(this._parts.fragment=null,this.build(!e)),this},s.normalizeSearch=s.normalizeQuery,s.normalizeHash=s.normalizeFragment,s.iso8859=function(){var e=i.encode,t=i.decode;i.encode=escape,i.decode=decodeURIComponent;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.unicode=function(){var e=i.encode,t=i.decode;i.encode=g,i.decode=unescape;try{this.normalize()}finally{i.encode=e,i.decode=t}return this},s.readable=function(){var t=this.clone();t.username("").password("").normalize();var r="";if(t._parts.protocol&&(r+=t._parts.protocol+"://"),t._parts.hostname&&(t.is("punycode")&&e?(r+=e.toUnicode(t._parts.hostname),t._parts.port&&(r+=":"+t._parts.port)):r+=t.host()),t._parts.hostname&&t._parts.path&&"/"!==t._parts.path.charAt(0)&&(r+="/"),r+=t.path(!0),t._parts.query){for(var n="",o=0,s=t._parts.query.split("&"),a=s.length;o<a;o++){var u=(s[o]||"").split("=");n+="&"+i.decodeQuery(u[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==u[1]&&(n+="="+i.decodeQuery(u[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}r+="?"+n.substring(1)}return r+=i.decodeQuery(t.hash(),!0)},s.absoluteTo=function(e){var t,r,n,o=this.clone(),s=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e instanceof i||(e=new i(e)),o._parts.protocol)return o;if(o._parts.protocol=e._parts.protocol,this._parts.hostname)return o;for(r=0;n=s[r];r++)o._parts[n]=e._parts[n];return o._parts.path?(".."===o._parts.path.substring(-2)&&(o._parts.path+="/"),"/"!==o.path().charAt(0)&&(t=(t=e.directory())||(0===e.path().indexOf("/")?"/":""),o._parts.path=(t?t+"/":"")+o._parts.path,o.normalizePath())):(o._parts.path=e._parts.path,o._parts.query||(o._parts.query=e._parts.query)),o.build(),o},s.relativeTo=function(e){var t,r,n,o,s,a=this.clone().normalize();if(a._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e=new i(e).normalize(),t=a._parts,r=e._parts,o=a.path(),s=e.path(),"/"!==o.charAt(0))throw new Error("URI is already relative");if("/"!==s.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(t.protocol===r.protocol&&(t.protocol=null),t.username!==r.username||t.password!==r.password)return a.build();if(null!==t.protocol||null!==t.username||null!==t.password)return a.build();if(t.hostname!==r.hostname||t.port!==r.port)return a.build();if(t.hostname=null,t.port=null,o===s)return t.path="",a.build();if(!(n=i.commonPath(o,s)))return a.build();var u=r.path.substring(n.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return t.path=u+t.path.substring(n.length)||"./",a.build()},s.equals=function(e){var t,r,n,o,s,u=this.clone(),c=new i(e),d={};if(u.normalize(),c.normalize(),u.toString()===c.toString())return!0;if(n=u.query(),o=c.query(),u.query(""),c.query(""),u.toString()!==c.toString())return!1;if(n.length!==o.length)return!1;for(s in t=i.parseQuery(n,this._parts.escapeQuerySpace),r=i.parseQuery(o,this._parts.escapeQuerySpace),t)if(a.call(t,s)){if(l(t[s])){if(!p(t[s],r[s]))return!1}else if(t[s]!==r[s])return!1;d[s]=!0}for(s in r)if(a.call(r,s)&&!d[s])return!1;return!0},s.preventInvalidHostname=function(e){return this._parts.preventInvalidHostname=!!e,this},s.duplicateQueryParameters=function(e){return this._parts.duplicateQueryParameters=!!e,this},s.escapeQuerySpace=function(e){return this._parts.escapeQuerySpace=!!e,this},i},e.exports?e.exports=r(xt,Et,Ot):t.URI=r(t.punycode,t.IPv6,t.SecondLevelDomains,t)}));function At(e,t){if(null===e||"object"!=typeof e)return e;t=n.defaultValue(t,!1);const r=new e.constructor;for(const n in e)if(e.hasOwnProperty(n)){let o=e[n];t&&(o=At(o,t)),r[n]=o}return r}function qt(e,t){let r;return"undefined"!=typeof document&&(r=document),qt._implementation(e,t,r)}qt._implementation=function(e,t,o){if(!n.defined(e))throw new r.DeveloperError("relative uri is required.");if(!n.defined(t)){if(void 0===o)return e;t=n.defaultValue(o.baseURI,o.location.href)}const i=new St(e);return""!==i.scheme()?i.toString():i.absoluteTo(t).toString()};const Dt=/^blob:/i;function kt(e){return r.Check.typeOf.string("uri",e),Dt.test(e)}let Rt;const It=/^data:/i;function Pt(e){return r.Check.typeOf.string("uri",e),It.test(e)}var Tt=Object.freeze({UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5});var zt=Object.freeze({TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3});function Mt(e){e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT);const t=n.defaultValue(e.throttleByServer,!1),r=n.defaultValue(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=n.defaultValue(e.priority,0),this.throttle=r,this.throttleByServer=t,this.type=n.defaultValue(e.type,zt.OTHER),this.serverKey=void 0,this.state=Tt.UNISSUED,this.deferred=void 0,this.cancelled=!1}function Ut(e,t,r){this.statusCode=e,this.response=t,this.responseHeaders=r,"string"==typeof this.responseHeaders&&(this.responseHeaders=function(e){const t={};if(!e)return t;const r=e.split("\r\n");for(let e=0;e<r.length;++e){const n=r[e],o=n.indexOf(": ");if(o>0){const e=n.substring(0,o),r=n.substring(o+2);t[e]=r}}return t}(this.responseHeaders))}function jt(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}function Ft(e,t){return t-e}function Nt(e){r.Check.typeOf.object("options",e),r.Check.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}function Bt(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}Mt.prototype.cancel=function(){this.cancelled=!0},Mt.prototype.clone=function(e){return n.defined(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=this.RequestState.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new Mt(this)},Ut.prototype.toString=function(){let e="Request has failed.";return n.defined(this.statusCode)&&(e+=" Status Code: "+this.statusCode),e},Object.defineProperties(jt.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}}),jt.prototype.addEventListener=function(e,t){r.Check.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(t);const n=this;return function(){n.removeEventListener(e,t)}},jt.prototype.removeEventListener=function(e,t){r.Check.typeOf.func("listener",e);const n=this._listeners,o=this._scopes;let i=-1;for(let r=0;r<n.length;r++)if(n[r]===e&&o[r]===t){i=r;break}return-1!==i&&(this._insideRaiseEvent?(this._toRemove.push(i),n[i]=void 0,o[i]=void 0):(n.splice(i,1),o.splice(i,1)),!0)},jt.prototype.raiseEvent=function(){let e;this._insideRaiseEvent=!0;const t=this._listeners,r=this._scopes;let o=t.length;for(e=0;e<o;e++){const o=t[e];n.defined(o)&&t[e].apply(r[e],arguments)}const i=this._toRemove;if(o=i.length,o>0){for(i.sort(Ft),e=0;e<o;e++){const n=i[e];t.splice(n,1),r.splice(n,1)}i.length=0}this._insideRaiseEvent=!1},Object.defineProperties(Nt.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){r.Check.typeOf.number.greaterThanOrEquals("maximumLength",e,0);const t=this._length;if(e<t){const r=this._array;for(let n=e;n<t;++n)r[n]=void 0;this._length=e,r.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}}),Nt.prototype.reserve=function(e){e=n.defaultValue(e,this._length),this._array.length=e},Nt.prototype.heapify=function(e){e=n.defaultValue(e,0);const t=this._length,r=this._comparator,o=this._array;let i=-1,s=!0;for(;s;){const n=2*(e+1),a=n-1;i=a<t&&r(o[a],o[e])<0?a:e,n<t&&r(o[n],o[i])<0&&(i=n),i!==e?(Bt(o,i,e),e=i):s=!1}},Nt.prototype.resort=function(){const e=this._length;for(let t=Math.ceil(e/2);t>=0;--t)this.heapify(t)},Nt.prototype.insert=function(e){r.Check.defined("element",e);const t=this._array,o=this._comparator,i=this._maximumLength;let s,a=this._length++;for(a<t.length?t[a]=e:t.push(e);0!==a;){const e=Math.floor((a-1)/2);if(!(o(t[a],t[e])<0))break;Bt(t,a,e),a=e}return n.defined(i)&&this._length>i&&(s=t[i],this._length=i),s},Nt.prototype.pop=function(e){if(e=n.defaultValue(e,0),0===this._length)return;r.Check.typeOf.number.lessThan("index",e,this._length);const t=this._array,o=t[e];return Bt(t,e,--this._length),this.heapify(e),t[this._length]=void 0,o};const Vt={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0};let Lt=20;const Qt=new Nt({comparator:function(e,t){return e.priority-t.priority}});Qt.maximumLength=Lt,Qt.reserve(Lt);const Wt=[];let Ht={};const Yt="undefined"!=typeof document?new St(document.location.href):new St,Zt=new jt;function Gt(){}function Jt(e){n.defined(e.priorityFunction)&&(e.priority=e.priorityFunction())}function $t(e){return e.state===Tt.UNISSUED&&(e.state=Tt.ISSUED,e.deferred=n.when.defer()),e.deferred.promise}function Xt(e){const t=$t(e);return e.state=Tt.ACTIVE,Wt.push(e),++Vt.numberOfActiveRequests,++Vt.numberOfActiveRequestsEver,++Ht[e.serverKey],e.requestFunction().then(function(e){return function(t){if(e.state===Tt.CANCELLED)return;const r=e.deferred;--Vt.numberOfActiveRequests,--Ht[e.serverKey],Zt.raiseEvent(),e.state=Tt.RECEIVED,e.deferred=void 0,r.resolve(t)}}(e)).otherwise(function(e){return function(t){e.state!==Tt.CANCELLED&&(++Vt.numberOfFailedRequests,--Vt.numberOfActiveRequests,--Ht[e.serverKey],Zt.raiseEvent(t),e.state=Tt.FAILED,e.deferred.reject(t))}}(e)),t}function Kt(e){const t=e.state===Tt.ACTIVE;if(e.state=Tt.CANCELLED,++Vt.numberOfCancelledRequests,n.defined(e.deferred)){const t=e.deferred;e.deferred=void 0,t.reject()}t&&(--Vt.numberOfActiveRequests,--Ht[e.serverKey],++Vt.numberOfCancelledActiveRequests),n.defined(e.cancelFunction)&&e.cancelFunction()}Gt.maximumRequests=50,Gt.maximumRequestsPerServer=6,Gt.requestsByServer={"api.cesium.com:443":18,"assets.cesium.com:443":18},Gt.throttleRequests=!0,Gt.debugShowStatistics=!1,Gt.requestCompletedEvent=Zt,Object.defineProperties(Gt,{statistics:{get:function(){return Vt}},priorityHeapLength:{get:function(){return Lt},set:function(e){if(e<Lt)for(;Qt.length>e;){Kt(Qt.pop())}Lt=e,Qt.maximumLength=e,Qt.reserve(e)}}}),Gt.serverHasOpenSlots=function(e,t){t=n.defaultValue(t,1);const r=n.defaultValue(Gt.requestsByServer[e],Gt.maximumRequestsPerServer);return Ht[e]+t<=r},Gt.heapHasOpenSlots=function(e){return Qt.length+e<=Lt},Gt.update=function(){let e,t,r=0;const n=Wt.length;for(e=0;e<n;++e)t=Wt[e],t.cancelled&&Kt(t),t.state===Tt.ACTIVE?r>0&&(Wt[e-r]=t):++r;Wt.length-=r;const o=Qt.internalArray,i=Qt.length;for(e=0;e<i;++e)Jt(o[e]);Qt.resort();const s=Math.max(Gt.maximumRequests-Wt.length,0);let a=0;for(;a<s&&Qt.length>0;)t=Qt.pop(),t.cancelled?Kt(t):!t.throttleByServer||Gt.serverHasOpenSlots(t.serverKey)?(Xt(t),++a):Kt(t);!function(){if(!Gt.debugShowStatistics)return;0===Vt.numberOfActiveRequests&&Vt.lastNumberOfActiveRequests>0&&(Vt.numberOfAttemptedRequests>0&&(console.log("Number of attempted requests: "+Vt.numberOfAttemptedRequests),Vt.numberOfAttemptedRequests=0),Vt.numberOfCancelledRequests>0&&(console.log("Number of cancelled requests: "+Vt.numberOfCancelledRequests),Vt.numberOfCancelledRequests=0),Vt.numberOfCancelledActiveRequests>0&&(console.log("Number of cancelled active requests: "+Vt.numberOfCancelledActiveRequests),Vt.numberOfCancelledActiveRequests=0),Vt.numberOfFailedRequests>0&&(console.log("Number of failed requests: "+Vt.numberOfFailedRequests),Vt.numberOfFailedRequests=0));Vt.lastNumberOfActiveRequests=Vt.numberOfActiveRequests}()},Gt.getServerKey=function(e){r.Check.typeOf.string("url",e);let t=new St(e);""===t.scheme()&&(t=new St(e).absoluteTo(Yt),t.normalize());let o=t.authority();/:/.test(o)||(o=o+":"+("https"===t.scheme()?"443":"80"));const i=Ht[o];return n.defined(i)||(Ht[o]=0),o},Gt.request=function(e){if(r.Check.typeOf.object("request",e),r.Check.typeOf.string("request.url",e.url),r.Check.typeOf.func("request.requestFunction",e.requestFunction),Pt(e.url)||kt(e.url))return Zt.raiseEvent(),e.state=Tt.RECEIVED,e.requestFunction();if(++Vt.numberOfAttemptedRequests,n.defined(e.serverKey)||(e.serverKey=Gt.getServerKey(e.url)),Gt.throttleRequests&&e.throttleByServer&&!Gt.serverHasOpenSlots(e.serverKey))return;if(!Gt.throttleRequests||!e.throttle)return Xt(e);if(Wt.length>=Gt.maximumRequests)return;Jt(e);const t=Qt.insert(e);if(n.defined(t)){if(t===e)return;Kt(t)}return $t(e)},Gt.clearForSpecs=function(){for(;Qt.length>0;){Kt(Qt.pop())}const e=Wt.length;for(let t=0;t<e;++t)Kt(Wt[t]);Wt.length=0,Ht={},Vt.numberOfAttemptedRequests=0,Vt.numberOfActiveRequests=0,Vt.numberOfCancelledRequests=0,Vt.numberOfCancelledActiveRequests=0,Vt.numberOfFailedRequests=0,Vt.numberOfActiveRequestsEver=0,Vt.lastNumberOfActiveRequests=0},Gt.numberOfActiveRequestsByServer=function(e){return Ht[e]},Gt.requestHeap=Qt;const er={};let tr={};er.add=function(e,t){if(!n.defined(e))throw new r.DeveloperError("host is required.");if(!n.defined(t)||t<=0)throw new r.DeveloperError("port is required to be greater than 0.");const o=e.toLowerCase()+":"+t;n.defined(tr[o])||(tr[o]=!0)},er.remove=function(e,t){if(!n.defined(e))throw new r.DeveloperError("host is required.");if(!n.defined(t)||t<=0)throw new r.DeveloperError("port is required to be greater than 0.");const o=e.toLowerCase()+":"+t;n.defined(tr[o])&&delete tr[o]},er.contains=function(e){if(!n.defined(e))throw new r.DeveloperError("url is required.");const t=function(e){const t=new St(e);t.normalize();let r=t.authority();if(0!==r.length){if(t.authority(r),-1!==r.indexOf("@")){const e=r.split("@");r=e[1]}if(-1===r.indexOf(":")){let e=t.scheme();if(0===e.length&&(e=window.location.protocol,e=e.substring(0,e.length-1)),"http"===e)r+=":80";else{if("https"!==e)return;r+=":443"}}return r}}(e);return!(!n.defined(t)||!n.defined(tr[t]))},er.clear=function(){tr={}};const rr=function(){try{const e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob","blob"===e.responseType}catch(e){return!1}}();function nr(e,t,o,i){const s=e.query();if(0===s.length)return{};let a;if(-1===s.indexOf("=")){const e={};e[s]=void 0,a=e}else a=function(e){if(!n.defined(e))throw new r.DeveloperError("queryString is required.");const t={};if(""===e)return t;const o=e.replace(/\+/g,"%20").split(/[&;]/);for(let e=0,r=o.length;e<r;++e){const r=o[e].split("="),i=decodeURIComponent(r[0]);let s=r[1];s=n.defined(s)?decodeURIComponent(s):"";const a=t[i];"string"==typeof a?t[i]=[a,s]:Array.isArray(a)?a.push(s):t[i]=s}return t}(s);t._queryParameters=o?ar(a,t._queryParameters,i):a,e.search("")}function or(e,t){const o=t._queryParameters,i=Object.keys(o);1!==i.length||n.defined(o[i[0]])?e.search(function(e){if(!n.defined(e))throw new r.DeveloperError("obj is required.");let t="";for(const r in e)if(e.hasOwnProperty(r)){const n=e[r],o=encodeURIComponent(r)+"=";if(Array.isArray(n))for(let e=0,r=n.length;e<r;++e)t+=o+encodeURIComponent(n[e])+"&";else t+=o+encodeURIComponent(n)+"&"}return t=t.slice(0,-1),t}(o)):e.search(i[0])}function ir(e,t){return n.defined(e)?n.defined(e.clone)?e.clone():At(e):t}function sr(e){if(e.state===Tt.ISSUED||e.state===Tt.ACTIVE)throw new r.RuntimeError("The Resource is already being fetched.");e.state=Tt.UNISSUED,e.deferred=void 0}function ar(e,t,r){if(!r)return i.combine(e,t);const o=At(e,!0);for(const e in t)if(t.hasOwnProperty(e)){let r=o[e];const i=t[e];n.defined(r)?(Array.isArray(r)||(r=o[e]=[r]),o[e]=r.concat(i)):o[e]=Array.isArray(i)?i.slice():i}return o}function ur(e){"string"==typeof(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT))&&(e={url:e}),r.Check.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=ir(e.templateValues,{}),this._queryParameters=ir(e.queryParameters,{}),this.headers=ir(e.headers,{}),this.request=n.defaultValue(e.request,new Mt),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=n.defaultValue(e.retryAttempts,0),this._retryCount=0;const t=new St(e.url);nr(t,this,!0,!0),t.fragment(""),this._url=t.toString()}let cr;function lr(e){const t=e.resource,r=e.flipY,o=e.skipColorSpaceConversion,i=e.preferImageBitmap,s=t.request;s.url=t.url,s.requestFunction=function(){let e=!1;t.isDataUri||t.isBlobUri||(e=t.isCrossOriginUrl);const a=n.when.defer();return ur._Implementations.createImage(s,e,a,r,o,i),a.promise};const a=Gt.request(s);if(n.defined(a))return a.otherwise((function(e){return s.state!==Tt.FAILED?n.when.reject(e):t.retryOnError(e).then((function(a){return a?(s.state=Tt.UNISSUED,s.deferred=void 0,lr({resource:t,flipY:r,skipColorSpaceConversion:o,preferImageBitmap:i})):n.when.reject(e)}))}))}function dr(e,t,r){const o={};o[t]=r,e.setQueryParameters(o);const i=e.request;i.url=e.url,i.requestFunction=function(){const t=n.when.defer();return window[r]=function(e){t.resolve(e);try{delete window[r]}catch(e){window[r]=void 0}},ur._Implementations.loadAndExecuteScript(e.url,r,t),t.promise};const s=Gt.request(i);if(n.defined(s))return s.otherwise((function(o){return i.state!==Tt.FAILED?n.when.reject(o):e.retryOnError(o).then((function(s){return s?(i.state=Tt.UNISSUED,i.deferred=void 0,dr(e,t,r)):n.when.reject(o)}))}))}ur.createIfNeeded=function(e){return e instanceof ur?e.getDerivedResource({request:e.request}):"string"!=typeof e?e:new ur({url:e})},ur.supportsImageBitmapOptions=function(){if(n.defined(cr))return cr;if("function"!=typeof createImageBitmap)return cr=n.when.resolve(!1),cr;return cr=ur.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWP4////fwAJ+wP9CNHoHgAAAABJRU5ErkJggg=="}).then((function(e){return createImageBitmap(e,{imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"})})).then((function(e){return!0})).otherwise((function(){return!1})),cr},Object.defineProperties(ur,{isBlobSupported:{get:function(){return rr}}}),Object.defineProperties(ur.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){const t=new St(e);nr(t,this,!1),t.fragment(""),this._url=t.toString()}},extension:{get:function(){return function(e){if(!n.defined(e))throw new r.DeveloperError("uri is required.");const t=new St(e);t.normalize();let o=t.path(),i=o.lastIndexOf("/");return-1!==i&&(o=o.substr(i+1)),i=o.lastIndexOf("."),o=-1===i?"":o.substr(i+1),o}(this._url)}},isDataUri:{get:function(){return Pt(this._url)}},isBlobUri:{get:function(){return kt(this._url)}},isCrossOriginUrl:{get:function(){return function(e){n.defined(Rt)||(Rt=document.createElement("a")),Rt.href=window.location.href;const t=Rt.host,r=Rt.protocol;return Rt.href=e,Rt.href=Rt.href,r!==Rt.protocol||t!==Rt.host}(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}}}),ur.prototype.toString=function(){return this.getUrlComponent(!0,!0)},ur.prototype.getUrlComponent=function(e,t){if(this.isDataUri)return this._url;const r=new St(this._url);e&&or(r,this);let o=r.toString().replace(/%7B/g,"{").replace(/%7D/g,"}");const i=this._templateValues;return o=o.replace(/{(.*?)}/g,(function(e,t){const r=i[t];return n.defined(r)?encodeURIComponent(r):e})),t&&n.defined(this.proxy)&&(o=this.proxy.getURL(o)),o},ur.prototype.setQueryParameters=function(e,t){this._queryParameters=t?ar(this._queryParameters,e,!1):ar(e,this._queryParameters,!1)},ur.prototype.appendQueryParameters=function(e){this._queryParameters=ar(e,this._queryParameters,!0)},ur.prototype.setTemplateValues=function(e,t){this._templateValues=t?i.combine(this._templateValues,e):i.combine(e,this._templateValues)},ur.prototype.getDerivedResource=function(e){const t=this.clone();if(t._retryCount=0,n.defined(e.url)){const r=new St(e.url);nr(r,t,!0,n.defaultValue(e.preserveQueryParameters,!1)),r.fragment(""),""!==r.scheme()?t._url=r.toString():t._url=r.absoluteTo(new St(qt(this._url))).toString()}return n.defined(e.queryParameters)&&(t._queryParameters=i.combine(e.queryParameters,t._queryParameters)),n.defined(e.templateValues)&&(t._templateValues=i.combine(e.templateValues,t.templateValues)),n.defined(e.headers)&&(t.headers=i.combine(e.headers,t.headers)),n.defined(e.proxy)&&(t.proxy=e.proxy),n.defined(e.request)&&(t.request=e.request),n.defined(e.retryCallback)&&(t.retryCallback=e.retryCallback),n.defined(e.retryAttempts)&&(t.retryAttempts=e.retryAttempts),t},ur.prototype.retryOnError=function(e){const t=this.retryCallback;if("function"!=typeof t||this._retryCount>=this.retryAttempts)return n.when(!1);const r=this;return n.when(t(this,e)).then((function(e){return++r._retryCount,e}))},ur.prototype.clone=function(e){return n.defined(e)||(e=new ur({url:this._url})),e._url=this._url,e._queryParameters=At(this._queryParameters),e._templateValues=At(this._templateValues),e.headers=At(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e},ur.prototype.getBaseUri=function(e){return function(e,t){if(!n.defined(e))throw new r.DeveloperError("uri is required.");let o="";const i=e.lastIndexOf("/");return-1!==i&&(o=e.substring(0,i+1)),t?(0!==(e=new St(e)).query().length&&(o+="?"+e.query()),0!==e.fragment().length&&(o+="#"+e.fragment()),o):o}(this.getUrlComponent(e),e)},ur.prototype.appendForwardSlash=function(){var e;this._url=(0!==(e=this._url).length&&"/"===e[e.length-1]||(e+="/"),e)},ur.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})},ur.fetchArrayBuffer=function(e){return new ur(e).fetchArrayBuffer()},ur.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})},ur.fetchBlob=function(e){return new ur(e).fetchBlob()},ur.prototype.fetchImage=function(e){e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT);const t=n.defaultValue(e.preferImageBitmap,!1),r=n.defaultValue(e.preferBlob,!1),o=n.defaultValue(e.flipY,!1),i=n.defaultValue(e.skipColorSpaceConversion,!1);if(sr(this.request),!rr||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!r)return lr({resource:this,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:t});const s=this.fetchBlob();if(!n.defined(s))return;let a,u,c,l;return ur.supportsImageBitmapOptions().then((function(e){return a=e,u=a&&t,s})).then((function(e){if(!n.defined(e))return;if(l=e,u)return ur.createImageBitmapFromBlob(e,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i});const t=window.URL.createObjectURL(e);return c=new ur({url:t}),lr({resource:c,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:!1})})).then((function(e){if(n.defined(e))return e.blob=l,u||window.URL.revokeObjectURL(c.url),e})).otherwise((function(e){return n.defined(c)&&window.URL.revokeObjectURL(c.url),e.blob=l,n.when.reject(e)}))},ur.fetchImage=function(e){return new ur(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})},ur.prototype.fetchText=function(){return this.fetch({responseType:"text"})},ur.fetchText=function(e){return new ur(e).fetchText()},ur.prototype.fetchJson=function(){const e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(n.defined(e))return e.then((function(e){if(n.defined(e))return JSON.parse(e)}))},ur.fetchJson=function(e){return new ur(e).fetchJson()},ur.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})},ur.fetchXML=function(e){return new ur(e).fetchXML()},ur.prototype.fetchJsonp=function(e){let t;e=n.defaultValue(e,"callback"),sr(this.request);do{t="loadJsonp"+o.CesiumMath.nextRandomNumber().toString().substring(2,8)}while(n.defined(window[t]));return dr(this,e,t)},ur.fetchJsonp=function(e){return new ur(e).fetchJsonp(e.callbackParameterName)},ur.prototype._makeRequest=function(e){const t=this;sr(t.request);const r=t.request;r.url=t.url,r.requestFunction=function(){const o=e.responseType,s=i.combine(e.headers,t.headers),a=e.overrideMimeType,u=e.method,c=e.data,l=n.when.defer(),d=ur._Implementations.loadWithXhr(t.url,o,u,c,s,l,a);return n.defined(d)&&n.defined(d.abort)&&(r.cancelFunction=function(){d.abort()}),l.promise};const o=Gt.request(r);if(n.defined(o))return o.then((function(e){return r.cancelFunction=void 0,e})).otherwise((function(o){return r.cancelFunction=void 0,r.state!==Tt.FAILED?n.when.reject(o):t.retryOnError(o).then((function(i){return i?(r.state=Tt.UNISSUED,r.deferred=void 0,t.fetch(e)):n.when.reject(o)}))}))};const fr=/^data:(.*?)(;base64)?,(.*)$/;function pr(e,t){const r=decodeURIComponent(t);return e?atob(r):r}function hr(e,t){const r=pr(e,t),n=new ArrayBuffer(r.length),o=new Uint8Array(n);for(let e=0;e<r.length;e++)o[e]=r.charCodeAt(e);return n}function mr(e,t){switch(t){case"text":return e.toString("utf8");case"json":return JSON.parse(e.toString("utf8"));default:return new Uint8Array(e).buffer}}ur.prototype.fetch=function(e){return(e=ir(e,{})).method="GET",this._makeRequest(e)},ur.fetch=function(e){return new ur(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ur.prototype.delete=function(e){return(e=ir(e,{})).method="DELETE",this._makeRequest(e)},ur.delete=function(e){return new ur(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})},ur.prototype.head=function(e){return(e=ir(e,{})).method="HEAD",this._makeRequest(e)},ur.head=function(e){return new ur(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ur.prototype.options=function(e){return(e=ir(e,{})).method="OPTIONS",this._makeRequest(e)},ur.options=function(e){return new ur(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ur.prototype.post=function(e,t){return r.Check.defined("data",e),(t=ir(t,{})).method="POST",t.data=e,this._makeRequest(t)},ur.post=function(e){return new ur(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ur.prototype.put=function(e,t){return r.Check.defined("data",e),(t=ir(t,{})).method="PUT",t.data=e,this._makeRequest(t)},ur.put=function(e){return new ur(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ur.prototype.patch=function(e,t){return r.Check.defined("data",e),(t=ir(t,{})).method="PATCH",t.data=e,this._makeRequest(t)},ur.patch=function(e){return new ur(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})},ur._Implementations={},ur._Implementations.createImage=function(e,t,o,i,s,a){const u=e.url;ur.supportsImageBitmapOptions().then((function(c){if(!c||!a)return void function(e,t,r){const n=new Image;n.onload=function(){r.resolve(n)},n.onerror=function(e){r.reject(e)},t&&(er.contains(e)?n.crossOrigin="use-credentials":n.crossOrigin=""),n.src=e}(u,t,o);const l=n.when.defer(),d=ur._Implementations.loadWithXhr(u,"blob","GET",void 0,void 0,l,void 0,void 0,void 0);return n.defined(d)&&n.defined(d.abort)&&(e.cancelFunction=function(){d.abort()}),l.promise.then((function(e){if(n.defined(e))return ur.createImageBitmapFromBlob(e,{flipY:i,premultiplyAlpha:!1,skipColorSpaceConversion:s});o.reject(new r.RuntimeError("Successfully retrieved "+u+" but it contained no content."))})).then(o.resolve)})).otherwise(o.reject)},ur.createImageBitmapFromBlob=function(e,t){return r.Check.defined("options",t),r.Check.typeOf.bool("options.flipY",t.flipY),r.Check.typeOf.bool("options.premultiplyAlpha",t.premultiplyAlpha),r.Check.typeOf.bool("options.skipColorSpaceConversion",t.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:t.flipY?"flipY":"none",premultiplyAlpha:t.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:t.skipColorSpaceConversion?"none":"default"})};const gr="undefined"==typeof XMLHttpRequest;function yr(e){if(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._downloadPromise=void 0,this._dataError=void 0,this._addNewLeapSeconds=n.defaultValue(e.addNewLeapSeconds,!0),n.defined(e.data))wr(this,e.data);else if(n.defined(e.url)){const t=ur.createIfNeeded(e.url),r=this;this._downloadPromise=t.fetchJson().then((function(e){wr(r,e)})).otherwise((function(){r._dataError="An error occurred while retrieving the EOP data from the URL "+t.url+"."}))}else wr(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function vr(e,t){return bt.compare(e.julianDate,t)}function wr(e,t){if(!n.defined(t.columnNames))return void(e._dataError="Error in loaded EOP data: The columnNames property is required.");if(!n.defined(t.samples))return void(e._dataError="Error in loaded EOP data: The samples property is required.");const r=t.columnNames.indexOf("modifiedJulianDateUtc"),o=t.columnNames.indexOf("xPoleWanderRadians"),i=t.columnNames.indexOf("yPoleWanderRadians"),s=t.columnNames.indexOf("ut1MinusUtcSeconds"),a=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),c=t.columnNames.indexOf("taiMinusUtcSeconds");if(r<0||o<0||i<0||s<0||a<0||u<0||c<0)return void(e._dataError="Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");const l=e._samples=t.samples,d=e._dates=[];let f;e._dateColumn=r,e._xPoleWanderRadiansColumn=o,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=s,e._xCelestialPoleOffsetRadiansColumn=a,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=c,e._columnCount=t.columnNames.length,e._lastIndex=void 0;const p=e._addNewLeapSeconds;for(let t=0,o=l.length;t<o;t+=e._columnCount){const e=l[t+r],o=l[t+c],i=new bt(e+tt.MODIFIED_JULIAN_DATE_DIFFERENCE,o,rt.TAI);if(d.push(i),p){if(o!==f&&n.defined(f)){const e=bt.leapSeconds,t=Je(e,i,vr);if(t<0){const r=new et(i,o);e.splice(~t,0,r)}}f=o}}}function Cr(e,t,r,n,o){const i=r*n;o.xPoleWander=t[i+e._xPoleWanderRadiansColumn],o.yPoleWander=t[i+e._yPoleWanderRadiansColumn],o.xPoleOffset=t[i+e._xCelestialPoleOffsetRadiansColumn],o.yPoleOffset=t[i+e._yCelestialPoleOffsetRadiansColumn],o.ut1MinusUtc=t[i+e._ut1MinusUtcSecondsColumn]}function br(e,t,r){return t+e*(r-t)}function _r(e,t,r,n,o,i,s){const a=e._columnCount;if(i>t.length-1)return s.xPoleWander=0,s.yPoleWander=0,s.xPoleOffset=0,s.yPoleOffset=0,s.ut1MinusUtc=0,s;const u=t[o],c=t[i];if(u.equals(c)||n.equals(u))return Cr(e,r,o,a,s),s;if(n.equals(c))return Cr(e,r,i,a,s),s;const l=bt.secondsDifference(n,u)/bt.secondsDifference(c,u),d=o*a,f=i*a;let p=r[d+e._ut1MinusUtcSecondsColumn],h=r[f+e._ut1MinusUtcSecondsColumn];const m=h-p;if(m>.5||m<-.5){const t=r[d+e._taiMinusUtcSecondsColumn],o=r[f+e._taiMinusUtcSecondsColumn];t!==o&&(c.equals(n)?p=h:h-=o-t)}return s.xPoleWander=br(l,r[d+e._xPoleWanderRadiansColumn],r[f+e._xPoleWanderRadiansColumn]),s.yPoleWander=br(l,r[d+e._yPoleWanderRadiansColumn],r[f+e._yPoleWanderRadiansColumn]),s.xPoleOffset=br(l,r[d+e._xCelestialPoleOffsetRadiansColumn],r[f+e._xCelestialPoleOffsetRadiansColumn]),s.yPoleOffset=br(l,r[d+e._yCelestialPoleOffsetRadiansColumn],r[f+e._yCelestialPoleOffsetRadiansColumn]),s.ut1MinusUtc=br(l,p,h),s}function xr(e,t,r){this.heading=n.defaultValue(e,0),this.pitch=n.defaultValue(t,0),this.roll=n.defaultValue(r,0)}ur._Implementations.loadWithXhr=function(e,t,o,i,s,a,u){const c=fr.exec(e);if(null!==c)return void a.resolve(function(e,t){t=n.defaultValue(t,"");const o=e[1],i=!!e[2],s=e[3];let a,u;switch(t){case"":case"text":return pr(i,s);case"arraybuffer":return hr(i,s);case"blob":return a=hr(i,s),new Blob([a],{type:o});case"document":return u=new DOMParser,u.parseFromString(pr(i,s),o);case"json":return JSON.parse(pr(i,s));default:throw new r.DeveloperError("Unhandled responseType: "+t)}}(c,t));if(gr)return void function(e,t,n,o,i,s,a){const u=require("url").parse(e),c="https:"===u.protocol?require("https"):require("http"),l=require("zlib"),d={protocol:u.protocol,hostname:u.hostname,port:u.port,path:u.path,query:u.query,method:n,headers:i};c.request(d).on("response",(function(e){if(e.statusCode<200||e.statusCode>=300)return void s.reject(new Ut(e.statusCode,e,e.headers));const n=[];e.on("data",(function(e){n.push(e)})),e.on("end",(function(){const o=Buffer.concat(n);"gzip"===e.headers["content-encoding"]?l.gunzip(o,(function(e,n){e?s.reject(new r.RuntimeError("Error decompressing response.")):s.resolve(mr(n,t))})):s.resolve(mr(o,t))}))})).on("error",(function(e){s.reject(new Ut)})).end()}(e,t,o,0,s,a);const l=new XMLHttpRequest;if(er.contains(e)&&(l.withCredentials=!0),l.open(o,e,!0),n.defined(u)&&n.defined(l.overrideMimeType)&&l.overrideMimeType(u),n.defined(s))for(const e in s)s.hasOwnProperty(e)&&l.setRequestHeader(e,s[e]);n.defined(t)&&(l.responseType=t);let d=!1;return"string"==typeof e&&(d=0===e.indexOf("file://")||"undefined"!=typeof window&&"file://"===window.location.origin),l.onload=function(){if((l.status<200||l.status>=300)&&(!d||0!==l.status))return void a.reject(new Ut(l.status,l.response,l.getAllResponseHeaders()));const e=l.response,i=l.responseType;if("HEAD"===o||"OPTIONS"===o){const e=l.getAllResponseHeaders().trim().split(/[\r\n]+/),t={};return e.forEach((function(e){const r=e.split(": "),n=r.shift();t[n]=r.join(": ")})),void a.resolve(t)}if(204===l.status)a.resolve();else if(!n.defined(e)||n.defined(t)&&i!==t)if("json"===t&&"string"==typeof e)try{a.resolve(JSON.parse(e))}catch(e){a.reject(e)}else(""===i||"document"===i)&&n.defined(l.responseXML)&&l.responseXML.hasChildNodes()?a.resolve(l.responseXML):""!==i&&"text"!==i||!n.defined(l.responseText)?a.reject(new r.RuntimeError("Invalid XMLHttpRequest response type.")):a.resolve(l.responseText);else a.resolve(e)},l.onerror=function(e){a.reject(new Ut)},l.send(i),l},ur._Implementations.loadAndExecuteScript=function(e,t,r){return function(e){const t=n.when.defer(),r=document.createElement("script");r.async=!0,r.src=e;const o=document.getElementsByTagName("head")[0];return r.onload=function(){r.onload=void 0,o.removeChild(r),t.resolve()},r.onerror=function(e){t.reject(e)},o.appendChild(r),t.promise}(e).otherwise(r.reject)},ur._DefaultImplementations={},ur._DefaultImplementations.createImage=ur._Implementations.createImage,ur._DefaultImplementations.loadWithXhr=ur._Implementations.loadWithXhr,ur._DefaultImplementations.loadAndExecuteScript=ur._Implementations.loadAndExecuteScript,ur.DEFAULT=Object.freeze(new ur({url:"undefined"==typeof document?"":document.location.href.split("?")[0]})),yr.NONE=Object.freeze({getPromiseToLoad:function(){return n.when.resolve()},compute:function(e,t){return n.defined(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new $e(0,0,0,0,0),t}}),yr.prototype.getPromiseToLoad=function(){return n.when(this._downloadPromise)},yr.prototype.compute=function(e,t){if(!n.defined(this._samples)){if(n.defined(this._dataError))throw new r.RuntimeError(this._dataError);return}if(n.defined(t)||(t=new $e(0,0,0,0,0)),0===this._samples.length)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;const o=this._dates,i=this._lastIndex;let s=0,a=0;if(n.defined(i)){const r=o[i],u=o[i+1],c=bt.lessThanOrEquals(r,e),l=!n.defined(u),d=l||bt.greaterThanOrEquals(u,e);if(c&&d)return s=i,!l&&u.equals(e)&&++s,a=s+1,_r(this,o,this._samples,e,s,a,t),t}let u=Je(o,e,bt.compare,this._dateColumn);return u>=0?(u<o.length-1&&o[u+1].equals(e)&&++u,s=u,a=u):(a=~u,s=a-1,s<0&&(s=0)),this._lastIndex=s,_r(this,o,this._samples,e,s,a,t),t},xr.fromQuaternion=function(e,t){if(!n.defined(e))throw new r.DeveloperError("quaternion is required");n.defined(t)||(t=new xr);const i=2*(e.w*e.y-e.z*e.x),s=1-2*(e.x*e.x+e.y*e.y),a=2*(e.w*e.x+e.y*e.z),u=1-2*(e.y*e.y+e.z*e.z),c=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(c,u),t.roll=Math.atan2(a,s),t.pitch=-o.CesiumMath.asinClamped(i),t},xr.fromDegrees=function(e,t,i,s){if(!n.defined(e))throw new r.DeveloperError("heading is required");if(!n.defined(t))throw new r.DeveloperError("pitch is required");if(!n.defined(i))throw new r.DeveloperError("roll is required");return n.defined(s)||(s=new xr),s.heading=e*o.CesiumMath.RADIANS_PER_DEGREE,s.pitch=t*o.CesiumMath.RADIANS_PER_DEGREE,s.roll=i*o.CesiumMath.RADIANS_PER_DEGREE,s},xr.clone=function(e,t){if(n.defined(e))return n.defined(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new xr(e.heading,e.pitch,e.roll)},xr.equals=function(e,t){return e===t||n.defined(e)&&n.defined(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll},xr.equalsEpsilon=function(e,t,r,i){return e===t||n.defined(e)&&n.defined(t)&&o.CesiumMath.equalsEpsilon(e.heading,t.heading,r,i)&&o.CesiumMath.equalsEpsilon(e.pitch,t.pitch,r,i)&&o.CesiumMath.equalsEpsilon(e.roll,t.roll,r,i)},xr.prototype.clone=function(e){return xr.clone(this,e)},xr.prototype.equals=function(e){return xr.equals(this,e)},xr.prototype.equalsEpsilon=function(e,t,r){return xr.equalsEpsilon(this,e,t,r)},xr.prototype.toString=function(){return"("+this.heading+", "+this.pitch+", "+this.roll+")"};var Er,Or,Sr,Ar=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;function qr(e){return"undefined"==typeof document?e:(n.defined(Er)||(Er=document.createElement("a")),Er.href=e,Er.href=Er.href,Er.href)}function Dr(){if(n.defined(Or))return Or;var e;if(e="undefined"!=typeof CESIUM_BASE_URL?CESIUM_BASE_URL:"object"==typeof define&&n.defined(define.amd)&&!define.amd.toUrlUndefined&&n.defined(require.toUrl)?qt("..",Ir("Core/buildModuleUrl.js")):function(){for(var e=document.getElementsByTagName("script"),t=0,r=e.length;t<r;++t){var n=e[t].getAttribute("src"),o=Ar.exec(n);if(null!==o)return o[1]}}(),!n.defined(e))throw new r.DeveloperError("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return(Or=new ur({url:qr(e)})).appendForwardSlash(),Or}function kr(e){return qr(require.toUrl("../"+e))}function Rr(e){return Dr().getDerivedResource({url:e}).url}function Ir(e){return n.defined(Sr)||(Sr="object"==typeof define&&n.defined(define.amd)&&!define.amd.toUrlUndefined&&n.defined(require.toUrl)?kr:Rr),Sr(e)}function Pr(e,t,r){this.x=e,this.y=t,this.s=r}function Tr(e){e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT),this._xysFileUrlTemplate=ur.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=n.defaultValue(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=n.defaultValue(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new bt(this._sampleZeroJulianEphemerisDate,0,rt.TAI),this._stepSizeDays=n.defaultValue(e.stepSizeDays,1),this._samplesPerXysFile=n.defaultValue(e.samplesPerXysFile,1e3),this._totalSamples=n.defaultValue(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];const t=this._interpolationOrder,r=this._denominators=new Array(t+1),o=this._xTable=new Array(t+1),i=Math.pow(this._stepSizeDays,t);for(let e=0;e<=t;++e){r[e]=i,o[e]=e*this._stepSizeDays;for(let n=0;n<=t;++n)n!==e&&(r[e]*=e-n);r[e]=1/r[e]}this._work=new Array(t+1),this._coef=new Array(t+1)}Ir._cesiumScriptRegex=Ar,Ir._buildModuleUrlFromBaseUrl=Rr,Ir._clearBaseResource=function(){Or=void 0},Ir.setBaseUrl=function(e){Or=ur.DEFAULT.getDerivedResource({url:e})},Ir.getCesiumBaseUrl=Dr;const zr=new bt(0,0,rt.TAI);function Mr(e,t,r){const n=zr;return n.dayNumber=t,n.secondsOfDay=r,bt.daysDifference(n,e._sampleZeroDateTT)}function Ur(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];const r=n.when.defer();let o;e._chunkDownloadsInProgress[t]=r;const i=e._xysFileUrlTemplate;return o=n.defined(i)?i.getDerivedResource({templateValues:{0:t}}):new ur({url:Ir("Assets/IAU2006_XYS/IAU2006_XYS_"+t+".json")}),n.when(o.fetchJson(),(function(n){e._chunkDownloadsInProgress[t]=!1;const o=e._samples,i=n.samples,s=t*e._samplesPerXysFile*3;for(let e=0,t=i.length;e<t;++e)o[s+e]=i[e];r.resolve()})),r.promise}Tr.prototype.preload=function(e,t,r,o){const i=Mr(this,e,t),s=Mr(this,r,o);let a=i/this._stepSizeDays-this._interpolationOrder/2|0;a<0&&(a=0);let u=s/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);const c=a/this._samplesPerXysFile|0,l=u/this._samplesPerXysFile|0,d=[];for(let e=c;e<=l;++e)d.push(Ur(this,e));return n.when.all(d)},Tr.prototype.computeXysRadians=function(e,t,r){const o=Mr(this,e,t);if(o<0)return;const i=o/this._stepSizeDays|0;if(i>=this._totalSamples)return;const s=this._interpolationOrder;let a=i-(s/2|0);a<0&&(a=0);let u=a+s;u>=this._totalSamples&&(u=this._totalSamples-1,a=u-s,a<0&&(a=0));let c=!1;const l=this._samples;if(n.defined(l[3*a])||(Ur(this,a/this._samplesPerXysFile|0),c=!0),n.defined(l[3*u])||(Ur(this,u/this._samplesPerXysFile|0),c=!0),c)return;n.defined(r)?(r.x=0,r.y=0,r.s=0):r=new Pr(0,0,0);const d=o-a*this._stepSizeDays,f=this._work,p=this._denominators,h=this._coef,m=this._xTable;let g,y;for(g=0;g<=s;++g)f[g]=d-m[g];for(g=0;g<=s;++g){for(h[g]=1,y=0;y<=s;++y)y!==g&&(h[g]*=f[y]);h[g]*=p[g];let e=3*(a+g);r.x+=h[g]*l[e++],r.y+=h[g]*l[e++],r.s+=h[g]*l[e]}return r};const jr={},Fr={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},Nr={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},Br={},Vr={east:new t.Cartesian3,north:new t.Cartesian3,up:new t.Cartesian3,west:new t.Cartesian3,south:new t.Cartesian3,down:new t.Cartesian3};let Lr=new t.Cartesian3,Qr=new t.Cartesian3,Wr=new t.Cartesian3;jr.localFrameToFixedFrameGenerator=function(e,i){if(!Fr.hasOwnProperty(e)||!Fr[e].hasOwnProperty(i))throw new r.DeveloperError("firstAxis and secondAxis must be east, north, up, west, south or down.");const s=Fr[e][i];let a;const u=e+i;return n.defined(Br[u])?a=Br[u]:(a=function(a,u,c){if(!n.defined(a))throw new r.DeveloperError("origin is required.");if(n.defined(c)||(c=new t.Matrix4),t.Cartesian3.equalsEpsilon(a,t.Cartesian3.ZERO,o.CesiumMath.EPSILON14))t.Cartesian3.unpack(Nr[e],0,Lr),t.Cartesian3.unpack(Nr[i],0,Qr),t.Cartesian3.unpack(Nr[s],0,Wr);else if(o.CesiumMath.equalsEpsilon(a.x,0,o.CesiumMath.EPSILON14)&&o.CesiumMath.equalsEpsilon(a.y,0,o.CesiumMath.EPSILON14)){const r=o.CesiumMath.sign(a.z);t.Cartesian3.unpack(Nr[e],0,Lr),"east"!==e&&"west"!==e&&t.Cartesian3.multiplyByScalar(Lr,r,Lr),t.Cartesian3.unpack(Nr[i],0,Qr),"east"!==i&&"west"!==i&&t.Cartesian3.multiplyByScalar(Qr,r,Qr),t.Cartesian3.unpack(Nr[s],0,Wr),"east"!==s&&"west"!==s&&t.Cartesian3.multiplyByScalar(Wr,r,Wr)}else{(u=n.defaultValue(u,t.Ellipsoid.WGS84)).geodeticSurfaceNormal(a,Vr.up);const r=Vr.up,o=Vr.east;o.x=-a.y,o.y=a.x,o.z=0,t.Cartesian3.normalize(o,Vr.east),t.Cartesian3.cross(r,o,Vr.north),t.Cartesian3.multiplyByScalar(Vr.up,-1,Vr.down),t.Cartesian3.multiplyByScalar(Vr.east,-1,Vr.west),t.Cartesian3.multiplyByScalar(Vr.north,-1,Vr.south),Lr=Vr[e],Qr=Vr[i],Wr=Vr[s]}return c[0]=Lr.x,c[1]=Lr.y,c[2]=Lr.z,c[3]=0,c[4]=Qr.x,c[5]=Qr.y,c[6]=Qr.z,c[7]=0,c[8]=Wr.x,c[9]=Wr.y,c[10]=Wr.z,c[11]=0,c[12]=a.x,c[13]=a.y,c[14]=a.z,c[15]=1,c},Br[u]=a),a},jr.eastNorthUpToFixedFrame=jr.localFrameToFixedFrameGenerator("east","north"),jr.northEastDownToFixedFrame=jr.localFrameToFixedFrameGenerator("north","east"),jr.northUpEastToFixedFrame=jr.localFrameToFixedFrameGenerator("north","up"),jr.northWestUpToFixedFrame=jr.localFrameToFixedFrameGenerator("north","west");const Hr=new xe,Yr=new t.Cartesian3(1,1,1),Zr=new t.Matrix4;jr.headingPitchRollToFixedFrame=function(e,o,i,s,a){r.Check.typeOf.object("HeadingPitchRoll",o),s=n.defaultValue(s,jr.eastNorthUpToFixedFrame);const u=xe.fromHeadingPitchRoll(o,Hr),c=t.Matrix4.fromTranslationQuaternionRotationScale(t.Cartesian3.ZERO,u,Yr,Zr);return a=s(e,i,a),t.Matrix4.multiply(a,c,a)};const Gr=new t.Matrix4,Jr=new t.Matrix3;jr.headingPitchRollQuaternion=function(e,n,o,i,s){r.Check.typeOf.object("HeadingPitchRoll",n);const a=jr.headingPitchRollToFixedFrame(e,n,o,i,Gr),u=t.Matrix4.getMatrix3(a,Jr);return xe.fromRotationMatrix(u,s)};const $r=new t.Cartesian3(1,1,1),Xr=new t.Cartesian3,Kr=new t.Matrix4,en=new t.Matrix4,tn=new t.Matrix3,rn=new xe;jr.fixedFrameToHeadingPitchRoll=function(e,o,i,s){r.Check.defined("transform",e),o=n.defaultValue(o,t.Ellipsoid.WGS84),i=n.defaultValue(i,jr.eastNorthUpToFixedFrame),n.defined(s)||(s=new xr);const a=t.Matrix4.getTranslation(e,Xr);if(t.Cartesian3.equals(a,t.Cartesian3.ZERO))return s.heading=0,s.pitch=0,s.roll=0,s;let u=t.Matrix4.inverseTransformation(i(a,o,Kr),Kr),c=t.Matrix4.setScale(e,$r,en);c=t.Matrix4.setTranslation(c,t.Cartesian3.ZERO,c),u=t.Matrix4.multiply(u,c,u);let l=xe.fromRotationMatrix(t.Matrix4.getMatrix3(u,tn),rn);return l=xe.normalize(l,l),xr.fromQuaternion(l,s)};const nn=o.CesiumMath.TWO_PI/86400;let on=new bt;jr.computeTemeToPseudoFixedMatrix=function(e,i){if(!n.defined(e))throw new r.DeveloperError("date is required.");on=bt.addSeconds(e,-bt.computeTaiMinusUtc(e),on);const s=on.dayNumber,a=on.secondsOfDay;let u;const c=s-2451545;u=a>=43200?(c+.5)/tt.DAYS_PER_JULIAN_CENTURY:(c-.5)/tt.DAYS_PER_JULIAN_CENTURY;const l=(24110.54841+u*(8640184.812866+u*(.093104+-62e-7*u)))*nn%o.CesiumMath.TWO_PI+(72921158553e-15+11772758384668e-32*(s-2451545.5))*((a+.5*tt.SECONDS_PER_DAY)%tt.SECONDS_PER_DAY),d=Math.cos(l),f=Math.sin(l);return n.defined(i)?(i[0]=d,i[1]=-f,i[2]=0,i[3]=f,i[4]=d,i[5]=0,i[6]=0,i[7]=0,i[8]=1,i):new t.Matrix3(d,f,0,-f,d,0,0,0,1)},jr.iau2006XysData=new Tr,jr.earthOrientationParameters=yr.NONE;const sn=32.184;jr.preloadIcrfFixed=function(e){const t=e.start.dayNumber,r=e.start.secondsOfDay+sn,o=e.stop.dayNumber,i=e.stop.secondsOfDay+sn,s=jr.iau2006XysData.preload(t,r,o,i),a=jr.earthOrientationParameters.getPromiseToLoad();return n.when.all([s,a])},jr.computeIcrfToFixedMatrix=function(e,o){if(!n.defined(e))throw new r.DeveloperError("date is required.");n.defined(o)||(o=new t.Matrix3);const i=jr.computeFixedToIcrfMatrix(e,o);if(n.defined(i))return t.Matrix3.transpose(i,o)};const an=new Pr(0,0,0),un=new $e(0,0,0,0,0),cn=new t.Matrix3,ln=new t.Matrix3;jr.computeFixedToIcrfMatrix=function(e,i){if(!n.defined(e))throw new r.DeveloperError("date is required.");n.defined(i)||(i=new t.Matrix3);const s=jr.earthOrientationParameters.compute(e,un);if(!n.defined(s))return;const a=e.dayNumber,u=e.secondsOfDay+sn,c=jr.iau2006XysData.computeXysRadians(a,u,an);if(!n.defined(c))return;const l=c.x+s.xPoleOffset,d=c.y+s.yPoleOffset,f=1/(1+Math.sqrt(1-l*l-d*d)),p=cn;p[0]=1-f*l*l,p[3]=-f*l*d,p[6]=l,p[1]=-f*l*d,p[4]=1-f*d*d,p[7]=d,p[2]=-l,p[5]=-d,p[8]=1-f*(l*l+d*d);const h=t.Matrix3.fromRotationZ(-c.s,ln),m=t.Matrix3.multiply(p,h,cn),g=e.dayNumber-2451545,y=(e.secondsOfDay-bt.computeTaiMinusUtc(e)+s.ut1MinusUtc)/tt.SECONDS_PER_DAY;let v=.779057273264+y+.00273781191135448*(g+y);v=v%1*o.CesiumMath.TWO_PI;const w=t.Matrix3.fromRotationZ(v,ln),C=t.Matrix3.multiply(m,w,cn),b=Math.cos(s.xPoleWander),_=Math.cos(s.yPoleWander),x=Math.sin(s.xPoleWander),E=Math.sin(s.yPoleWander);let O=a-2451545+u/tt.SECONDS_PER_DAY;O/=36525;const S=-47e-6*O*o.CesiumMath.RADIANS_PER_DEGREE/3600,A=Math.cos(S),q=Math.sin(S),D=ln;return D[0]=b*A,D[1]=b*q,D[2]=x,D[3]=-_*q+E*x*A,D[4]=_*A+E*x*q,D[5]=-E*b,D[6]=-E*q-_*x*A,D[7]=E*A-_*x*q,D[8]=_*b,t.Matrix3.multiply(C,D,i)};const dn=new t.Cartesian4;jr.pointToWindowCoordinates=function(e,t,r,n){return(n=jr.pointToGLWindowCoordinates(e,t,r,n)).y=2*t[5]-n.y,n},jr.pointToGLWindowCoordinates=function(e,o,i,s){if(!n.defined(e))throw new r.DeveloperError("modelViewProjectionMatrix is required.");if(!n.defined(o))throw new r.DeveloperError("viewportTransformation is required.");if(!n.defined(i))throw new r.DeveloperError("point is required.");n.defined(s)||(s=new t.Cartesian2);const a=dn;return t.Matrix4.multiplyByVector(e,t.Cartesian4.fromElements(i.x,i.y,i.z,1,a),a),t.Cartesian4.multiplyByScalar(a,1/a.w,a),t.Matrix4.multiplyByVector(o,a,a),t.Cartesian2.fromCartesian4(a,s)};const fn=new t.Cartesian3,pn=new t.Cartesian3,hn=new t.Cartesian3;jr.rotationMatrixFromPositionVelocity=function(e,i,s,a){if(!n.defined(e))throw new r.DeveloperError("position is required.");if(!n.defined(i))throw new r.DeveloperError("velocity is required.");const u=n.defaultValue(s,t.Ellipsoid.WGS84).geodeticSurfaceNormal(e,fn);let c=t.Cartesian3.cross(i,u,pn);t.Cartesian3.equalsEpsilon(c,t.Cartesian3.ZERO,o.CesiumMath.EPSILON6)&&(c=t.Cartesian3.clone(t.Cartesian3.UNIT_X,c));const l=t.Cartesian3.cross(c,i,hn);return t.Cartesian3.normalize(l,l),t.Cartesian3.cross(i,l,c),t.Cartesian3.negate(c,c),t.Cartesian3.normalize(c,c),n.defined(a)||(a=new t.Matrix3),a[0]=i.x,a[1]=i.y,a[2]=i.z,a[3]=c.x,a[4]=c.y,a[5]=c.z,a[6]=l.x,a[7]=l.y,a[8]=l.z,a};const mn=new t.Matrix4(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),gn=new t.Cartographic,yn=new t.Cartesian3,vn=new t.Cartesian3,wn=new t.Matrix3,Cn=new t.Matrix4,bn=new t.Matrix4;jr.basisTo2D=function(e,o,i){if(!n.defined(e))throw new r.DeveloperError("projection is required.");if(!n.defined(o))throw new r.DeveloperError("matrix is required.");if(!n.defined(i))throw new r.DeveloperError("result is required.");const s=t.Matrix4.getTranslation(o,vn),a=e.ellipsoid,u=a.cartesianToCartographic(s,gn),c=e.project(u,yn);t.Cartesian3.fromElements(c.z,c.x,c.y,c);const l=jr.eastNorthUpToFixedFrame(s,a,Cn),d=t.Matrix4.inverseTransformation(l,bn),f=t.Matrix4.getMatrix3(o,wn),p=t.Matrix4.multiplyByMatrix3(d,f,i);return t.Matrix4.multiply(mn,p,i),t.Matrix4.setTranslation(i,c,i),i},jr.wgs84To2DModelMatrix=function(e,o,i){if(!n.defined(e))throw new r.DeveloperError("projection is required.");if(!n.defined(o))throw new r.DeveloperError("center is required.");if(!n.defined(i))throw new r.DeveloperError("result is required.");const s=e.ellipsoid,a=jr.eastNorthUpToFixedFrame(o,s,Cn),u=t.Matrix4.inverseTransformation(a,bn),c=s.cartesianToCartographic(o,gn),l=e.project(c,yn);t.Cartesian3.fromElements(l.z,l.x,l.y,l);const d=t.Matrix4.fromTranslation(l,Cn);return t.Matrix4.multiply(mn,u,i),t.Matrix4.multiply(d,i,i),i},e.BoundingSphere=c,e.FeatureDetection=_e,e.GeographicProjection=s,e.Intersect=a,e.Interval=u,e.Quaternion=xe,e.Resource=ur,e.Transforms=jr,e.buildModuleUrl=Ir}));
//# sourceMappingURL=Transforms-4b8effe1.js.map
