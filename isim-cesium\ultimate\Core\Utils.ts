import { Cartesian3, ConstantPositionProperty, Entity, NearFarScalar, Viewer } from 'cesium';

export default class Utils {
  static setScaleByDistance(entity: Entity, viewer: Viewer, nearValue = 1.0, farValue = 0.3, nearFarRatio = 10) {
    if (!entity || !viewer) {
      console.warn('The params is required.', entity, viewer);
      return;
    }
    const position = (entity.position as ConstantPositionProperty)._value;
    const { longitude, latitude, height } = viewer.camera.positionCartographic;
    const cameraPosition = Cartesian3.fromRadians(longitude, latitude, height);
    const distance = Cartesian3.distance(position, cameraPosition);
    if (entity.billboard) {
      //@ts-ignore
      entity.billboard.scaleByDistance = new NearFarScalar(distance, nearValue, distance * nearFarRatio, farValue);
    }
  }
}
