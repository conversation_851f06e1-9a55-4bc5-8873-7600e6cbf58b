{"version": 3, "file": "WebGLConstants-508b9636.js", "sources": ["../../../../Source/Core/WebGLConstants.js"], "sourcesContent": ["/**\n * Enum containing WebGL Constant values by name.\n * for use without an active WebGL context, or in cases where certain constants are unavailable using the WebGL context\n * (For example, in [Safari 9]{@link https://github.com/CesiumGS/cesium/issues/2989}).\n *\n * These match the constants from the [WebGL 1.0]{@link https://www.khronos.org/registry/webgl/specs/latest/1.0/}\n * and [WebGL 2.0]{@link https://www.khronos.org/registry/webgl/specs/latest/2.0/}\n * specifications.\n *\n * @enum {Number}\n */\nconst WebGLConstants = {\n  DEPTH_BUFFER_BIT: 0x00000100,\n  STENCIL_BUFFER_BIT: 0x00000400,\n  COLOR_BUFFER_BIT: 0x00004000,\n  POINTS: 0x0000,\n  LINES: 0x0001,\n  LINE_LOOP: 0x0002,\n  LINE_STRIP: 0x0003,\n  TRIANGLES: 0x0004,\n  TRIANGLE_STRIP: 0x0005,\n  TRIANGLE_FAN: 0x0006,\n  ZERO: 0,\n  ONE: 1,\n  SRC_COLOR: 0x0300,\n  ONE_MINUS_SRC_COLOR: 0x0301,\n  SRC_ALPHA: 0x0302,\n  ONE_MINUS_SRC_ALPHA: 0x0303,\n  DST_ALPHA: 0x0304,\n  ONE_MINUS_DST_ALPHA: 0x0305,\n  DST_COLOR: 0x0306,\n  ONE_MINUS_DST_COLOR: 0x0307,\n  SRC_ALPHA_SATURATE: 0x0308,\n  FUNC_ADD: 0x8006,\n  BLEND_EQUATION: 0x8009,\n  BLEND_EQUATION_RGB: 0x8009, // same as BLEND_EQUATION\n  BLEND_EQUATION_ALPHA: 0x883d,\n  FUNC_SUBTRACT: 0x800a,\n  FUNC_REVERSE_SUBTRACT: 0x800b,\n  BLEND_DST_RGB: 0x80c8,\n  BLEND_SRC_RGB: 0x80c9,\n  BLEND_DST_ALPHA: 0x80ca,\n  BLEND_SRC_ALPHA: 0x80cb,\n  CONSTANT_COLOR: 0x8001,\n  ONE_MINUS_CONSTANT_COLOR: 0x8002,\n  CONSTANT_ALPHA: 0x8003,\n  ONE_MINUS_CONSTANT_ALPHA: 0x8004,\n  BLEND_COLOR: 0x8005,\n  ARRAY_BUFFER: 0x8892,\n  ELEMENT_ARRAY_BUFFER: 0x8893,\n  ARRAY_BUFFER_BINDING: 0x8894,\n  ELEMENT_ARRAY_BUFFER_BINDING: 0x8895,\n  STREAM_DRAW: 0x88e0,\n  STATIC_DRAW: 0x88e4,\n  DYNAMIC_DRAW: 0x88e8,\n  BUFFER_SIZE: 0x8764,\n  BUFFER_USAGE: 0x8765,\n  CURRENT_VERTEX_ATTRIB: 0x8626,\n  FRONT: 0x0404,\n  BACK: 0x0405,\n  FRONT_AND_BACK: 0x0408,\n  CULL_FACE: 0x0b44,\n  BLEND: 0x0be2,\n  DITHER: 0x0bd0,\n  STENCIL_TEST: 0x0b90,\n  DEPTH_TEST: 0x0b71,\n  SCISSOR_TEST: 0x0c11,\n  POLYGON_OFFSET_FILL: 0x8037,\n  SAMPLE_ALPHA_TO_COVERAGE: 0x809e,\n  SAMPLE_COVERAGE: 0x80a0,\n  NO_ERROR: 0,\n  INVALID_ENUM: 0x0500,\n  INVALID_VALUE: 0x0501,\n  INVALID_OPERATION: 0x0502,\n  OUT_OF_MEMORY: 0x0505,\n  CW: 0x0900,\n  CCW: 0x0901,\n  LINE_WIDTH: 0x0b21,\n  ALIASED_POINT_SIZE_RANGE: 0x846d,\n  ALIASED_LINE_WIDTH_RANGE: 0x846e,\n  CULL_FACE_MODE: 0x0b45,\n  FRONT_FACE: 0x0b46,\n  DEPTH_RANGE: 0x0b70,\n  DEPTH_WRITEMASK: 0x0b72,\n  DEPTH_CLEAR_VALUE: 0x0b73,\n  DEPTH_FUNC: 0x0b74,\n  STENCIL_CLEAR_VALUE: 0x0b91,\n  STENCIL_FUNC: 0x0b92,\n  STENCIL_FAIL: 0x0b94,\n  STENCIL_PASS_DEPTH_FAIL: 0x0b95,\n  STENCIL_PASS_DEPTH_PASS: 0x0b96,\n  STENCIL_REF: 0x0b97,\n  STENCIL_VALUE_MASK: 0x0b93,\n  STENCIL_WRITEMASK: 0x0b98,\n  STENCIL_BACK_FUNC: 0x8800,\n  STENCIL_BACK_FAIL: 0x8801,\n  STENCIL_BACK_PASS_DEPTH_FAIL: 0x8802,\n  STENCIL_BACK_PASS_DEPTH_PASS: 0x8803,\n  STENCIL_BACK_REF: 0x8ca3,\n  STENCIL_BACK_VALUE_MASK: 0x8ca4,\n  STENCIL_BACK_WRITEMASK: 0x8ca5,\n  VIEWPORT: 0x0ba2,\n  SCISSOR_BOX: 0x0c10,\n  COLOR_CLEAR_VALUE: 0x0c22,\n  COLOR_WRITEMASK: 0x0c23,\n  UNPACK_ALIGNMENT: 0x0cf5,\n  PACK_ALIGNMENT: 0x0d05,\n  MAX_TEXTURE_SIZE: 0x0d33,\n  MAX_VIEWPORT_DIMS: 0x0d3a,\n  SUBPIXEL_BITS: 0x0d50,\n  RED_BITS: 0x0d52,\n  GREEN_BITS: 0x0d53,\n  BLUE_BITS: 0x0d54,\n  ALPHA_BITS: 0x0d55,\n  DEPTH_BITS: 0x0d56,\n  STENCIL_BITS: 0x0d57,\n  POLYGON_OFFSET_UNITS: 0x2a00,\n  POLYGON_OFFSET_FACTOR: 0x8038,\n  TEXTURE_BINDING_2D: 0x8069,\n  SAMPLE_BUFFERS: 0x80a8,\n  SAMPLES: 0x80a9,\n  SAMPLE_COVERAGE_VALUE: 0x80aa,\n  SAMPLE_COVERAGE_INVERT: 0x80ab,\n  COMPRESSED_TEXTURE_FORMATS: 0x86a3,\n  DONT_CARE: 0x1100,\n  FASTEST: 0x1101,\n  NICEST: 0x1102,\n  GENERATE_MIPMAP_HINT: 0x8192,\n  BYTE: 0x1400,\n  UNSIGNED_BYTE: 0x1401,\n  SHORT: 0x1402,\n  UNSIGNED_SHORT: 0x1403,\n  INT: 0x1404,\n  UNSIGNED_INT: 0x1405,\n  FLOAT: 0x1406,\n  DEPTH_COMPONENT: 0x1902,\n  ALPHA: 0x1906,\n  RGB: 0x1907,\n  RGBA: 0x1908,\n  LUMINANCE: 0x1909,\n  LUMINANCE_ALPHA: 0x190a,\n  UNSIGNED_SHORT_4_4_4_4: 0x8033,\n  UNSIGNED_SHORT_5_5_5_1: 0x8034,\n  UNSIGNED_SHORT_5_6_5: 0x8363,\n  FRAGMENT_SHADER: 0x8b30,\n  VERTEX_SHADER: 0x8b31,\n  MAX_VERTEX_ATTRIBS: 0x8869,\n  MAX_VERTEX_UNIFORM_VECTORS: 0x8dfb,\n  MAX_VARYING_VECTORS: 0x8dfc,\n  MAX_COMBINED_TEXTURE_IMAGE_UNITS: 0x8b4d,\n  MAX_VERTEX_TEXTURE_IMAGE_UNITS: 0x8b4c,\n  MAX_TEXTURE_IMAGE_UNITS: 0x8872,\n  MAX_FRAGMENT_UNIFORM_VECTORS: 0x8dfd,\n  SHADER_TYPE: 0x8b4f,\n  DELETE_STATUS: 0x8b80,\n  LINK_STATUS: 0x8b82,\n  VALIDATE_STATUS: 0x8b83,\n  ATTACHED_SHADERS: 0x8b85,\n  ACTIVE_UNIFORMS: 0x8b86,\n  ACTIVE_ATTRIBUTES: 0x8b89,\n  SHADING_LANGUAGE_VERSION: 0x8b8c,\n  CURRENT_PROGRAM: 0x8b8d,\n  NEVER: 0x0200,\n  LESS: 0x0201,\n  EQUAL: 0x0202,\n  LEQUAL: 0x0203,\n  GREATER: 0x0204,\n  NOTEQUAL: 0x0205,\n  GEQUAL: 0x0206,\n  ALWAYS: 0x0207,\n  KEEP: 0x1e00,\n  REPLACE: 0x1e01,\n  INCR: 0x1e02,\n  DECR: 0x1e03,\n  INVERT: 0x150a,\n  INCR_WRAP: 0x8507,\n  DECR_WRAP: 0x8508,\n  VENDOR: 0x1f00,\n  RENDERER: 0x1f01,\n  VERSION: 0x1f02,\n  NEAREST: 0x2600,\n  LINEAR: 0x2601,\n  NEAREST_MIPMAP_NEAREST: 0x2700,\n  LINEAR_MIPMAP_NEAREST: 0x2701,\n  NEAREST_MIPMAP_LINEAR: 0x2702,\n  LINEAR_MIPMAP_LINEAR: 0x2703,\n  TEXTURE_MAG_FILTER: 0x2800,\n  TEXTURE_MIN_FILTER: 0x2801,\n  TEXTURE_WRAP_S: 0x2802,\n  TEXTURE_WRAP_T: 0x2803,\n  TEXTURE_2D: 0x0de1,\n  TEXTURE: 0x1702,\n  TEXTURE_CUBE_MAP: 0x8513,\n  TEXTURE_BINDING_CUBE_MAP: 0x8514,\n  TEXTURE_CUBE_MAP_POSITIVE_X: 0x8515,\n  TEXTURE_CUBE_MAP_NEGATIVE_X: 0x8516,\n  TEXTURE_CUBE_MAP_POSITIVE_Y: 0x8517,\n  TEXTURE_CUBE_MAP_NEGATIVE_Y: 0x8518,\n  TEXTURE_CUBE_MAP_POSITIVE_Z: 0x8519,\n  TEXTURE_CUBE_MAP_NEGATIVE_Z: 0x851a,\n  MAX_CUBE_MAP_TEXTURE_SIZE: 0x851c,\n  TEXTURE0: 0x84c0,\n  TEXTURE1: 0x84c1,\n  TEXTURE2: 0x84c2,\n  TEXTURE3: 0x84c3,\n  TEXTURE4: 0x84c4,\n  TEXTURE5: 0x84c5,\n  TEXTURE6: 0x84c6,\n  TEXTURE7: 0x84c7,\n  TEXTURE8: 0x84c8,\n  TEXTURE9: 0x84c9,\n  TEXTURE10: 0x84ca,\n  TEXTURE11: 0x84cb,\n  TEXTURE12: 0x84cc,\n  TEXTURE13: 0x84cd,\n  TEXTURE14: 0x84ce,\n  TEXTURE15: 0x84cf,\n  TEXTURE16: 0x84d0,\n  TEXTURE17: 0x84d1,\n  TEXTURE18: 0x84d2,\n  TEXTURE19: 0x84d3,\n  TEXTURE20: 0x84d4,\n  TEXTURE21: 0x84d5,\n  TEXTURE22: 0x84d6,\n  TEXTURE23: 0x84d7,\n  TEXTURE24: 0x84d8,\n  TEXTURE25: 0x84d9,\n  TEXTURE26: 0x84da,\n  TEXTURE27: 0x84db,\n  TEXTURE28: 0x84dc,\n  TEXTURE29: 0x84dd,\n  TEXTURE30: 0x84de,\n  TEXTURE31: 0x84df,\n  ACTIVE_TEXTURE: 0x84e0,\n  REPEAT: 0x2901,\n  CLAMP_TO_EDGE: 0x812f,\n  MIRRORED_REPEAT: 0x8370,\n  FLOAT_VEC2: 0x8b50,\n  FLOAT_VEC3: 0x8b51,\n  FLOAT_VEC4: 0x8b52,\n  INT_VEC2: 0x8b53,\n  INT_VEC3: 0x8b54,\n  INT_VEC4: 0x8b55,\n  BOOL: 0x8b56,\n  BOOL_VEC2: 0x8b57,\n  BOOL_VEC3: 0x8b58,\n  BOOL_VEC4: 0x8b59,\n  FLOAT_MAT2: 0x8b5a,\n  FLOAT_MAT3: 0x8b5b,\n  FLOAT_MAT4: 0x8b5c,\n  SAMPLER_2D: 0x8b5e,\n  SAMPLER_CUBE: 0x8b60,\n  VERTEX_ATTRIB_ARRAY_ENABLED: 0x8622,\n  VERTEX_ATTRIB_ARRAY_SIZE: 0x8623,\n  VERTEX_ATTRIB_ARRAY_STRIDE: 0x8624,\n  VERTEX_ATTRIB_ARRAY_TYPE: 0x8625,\n  VERTEX_ATTRIB_ARRAY_NORMALIZED: 0x886a,\n  VERTEX_ATTRIB_ARRAY_POINTER: 0x8645,\n  VERTEX_ATTRIB_ARRAY_BUFFER_BINDING: 0x889f,\n  IMPLEMENTATION_COLOR_READ_TYPE: 0x8b9a,\n  IMPLEMENTATION_COLOR_READ_FORMAT: 0x8b9b,\n  COMPILE_STATUS: 0x8b81,\n  LOW_FLOAT: 0x8df0,\n  MEDIUM_FLOAT: 0x8df1,\n  HIGH_FLOAT: 0x8df2,\n  LOW_INT: 0x8df3,\n  MEDIUM_INT: 0x8df4,\n  HIGH_INT: 0x8df5,\n  FRAMEBUFFER: 0x8d40,\n  RENDERBUFFER: 0x8d41,\n  RGBA4: 0x8056,\n  RGB5_A1: 0x8057,\n  RGB565: 0x8d62,\n  DEPTH_COMPONENT16: 0x81a5,\n  STENCIL_INDEX: 0x1901,\n  STENCIL_INDEX8: 0x8d48,\n  DEPTH_STENCIL: 0x84f9,\n  RENDERBUFFER_WIDTH: 0x8d42,\n  RENDERBUFFER_HEIGHT: 0x8d43,\n  RENDERBUFFER_INTERNAL_FORMAT: 0x8d44,\n  RENDERBUFFER_RED_SIZE: 0x8d50,\n  RENDERBUFFER_GREEN_SIZE: 0x8d51,\n  RENDERBUFFER_BLUE_SIZE: 0x8d52,\n  RENDERBUFFER_ALPHA_SIZE: 0x8d53,\n  RENDERBUFFER_DEPTH_SIZE: 0x8d54,\n  RENDERBUFFER_STENCIL_SIZE: 0x8d55,\n  FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE: 0x8cd0,\n  FRAMEBUFFER_ATTACHMENT_OBJECT_NAME: 0x8cd1,\n  FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL: 0x8cd2,\n  FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE: 0x8cd3,\n  COLOR_ATTACHMENT0: 0x8ce0,\n  DEPTH_ATTACHMENT: 0x8d00,\n  STENCIL_ATTACHMENT: 0x8d20,\n  DEPTH_STENCIL_ATTACHMENT: 0x821a,\n  NONE: 0,\n  FRAMEBUFFER_COMPLETE: 0x8cd5,\n  FRAMEBUFFER_INCOMPLETE_ATTACHMENT: 0x8cd6,\n  FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT: 0x8cd7,\n  FRAMEBUFFER_INCOMPLETE_DIMENSIONS: 0x8cd9,\n  FRAMEBUFFER_UNSUPPORTED: 0x8cdd,\n  FRAMEBUFFER_BINDING: 0x8ca6,\n  RENDERBUFFER_BINDING: 0x8ca7,\n  MAX_RENDERBUFFER_SIZE: 0x84e8,\n  INVALID_FRAMEBUFFER_OPERATION: 0x0506,\n  UNPACK_FLIP_Y_WEBGL: 0x9240,\n  UNPACK_PREMULTIPLY_ALPHA_WEBGL: 0x9241,\n  CONTEXT_LOST_WEBGL: 0x9242,\n  UNPACK_COLORSPACE_CONVERSION_WEBGL: 0x9243,\n  BROWSER_DEFAULT_WEBGL: 0x9244,\n\n  // WEBGL_compressed_texture_s3tc\n  COMPRESSED_RGB_S3TC_DXT1_EXT: 0x83f0,\n  COMPRESSED_RGBA_S3TC_DXT1_EXT: 0x83f1,\n  COMPRESSED_RGBA_S3TC_DXT3_EXT: 0x83f2,\n  COMPRESSED_RGBA_S3TC_DXT5_EXT: 0x83f3,\n\n  // WEBGL_compressed_texture_pvrtc\n  COMPRESSED_RGB_PVRTC_4BPPV1_IMG: 0x8c00,\n  COMPRESSED_RGB_PVRTC_2BPPV1_IMG: 0x8c01,\n  COMPRESSED_RGBA_PVRTC_4BPPV1_IMG: 0x8c02,\n  COMPRESSED_RGBA_PVRTC_2BPPV1_IMG: 0x8c03,\n\n  // WEBGL_compressed_texture_astc\n  COMPRESSED_RGBA_ASTC_4x4_WEBGL: 0x93b0,\n\n  // WEBGL_compressed_texture_etc1\n  COMPRESSED_RGB_ETC1_WEBGL: 0x8d64,\n\n  // EXT_texture_compression_bptc\n  COMPRESSED_RGBA_BPTC_UNORM: 0x8e8c,\n\n  // EXT_color_buffer_half_float\n  HALF_FLOAT_OES: 0x8d61,\n\n  // Desktop OpenGL\n  DOUBLE: 0x140a,\n\n  // WebGL 2\n  READ_BUFFER: 0x0c02,\n  UNPACK_ROW_LENGTH: 0x0cf2,\n  UNPACK_SKIP_ROWS: 0x0cf3,\n  UNPACK_SKIP_PIXELS: 0x0cf4,\n  PACK_ROW_LENGTH: 0x0d02,\n  PACK_SKIP_ROWS: 0x0d03,\n  PACK_SKIP_PIXELS: 0x0d04,\n  COLOR: 0x1800,\n  DEPTH: 0x1801,\n  STENCIL: 0x1802,\n  RED: 0x1903,\n  RGB8: 0x8051,\n  RGBA8: 0x8058,\n  RGB10_A2: 0x8059,\n  TEXTURE_BINDING_3D: 0x806a,\n  UNPACK_SKIP_IMAGES: 0x806d,\n  UNPACK_IMAGE_HEIGHT: 0x806e,\n  TEXTURE_3D: 0x806f,\n  TEXTURE_WRAP_R: 0x8072,\n  MAX_3D_TEXTURE_SIZE: 0x8073,\n  UNSIGNED_INT_2_10_10_10_REV: 0x8368,\n  MAX_ELEMENTS_VERTICES: 0x80e8,\n  MAX_ELEMENTS_INDICES: 0x80e9,\n  TEXTURE_MIN_LOD: 0x813a,\n  TEXTURE_MAX_LOD: 0x813b,\n  TEXTURE_BASE_LEVEL: 0x813c,\n  TEXTURE_MAX_LEVEL: 0x813d,\n  MIN: 0x8007,\n  MAX: 0x8008,\n  DEPTH_COMPONENT24: 0x81a6,\n  MAX_TEXTURE_LOD_BIAS: 0x84fd,\n  TEXTURE_COMPARE_MODE: 0x884c,\n  TEXTURE_COMPARE_FUNC: 0x884d,\n  CURRENT_QUERY: 0x8865,\n  QUERY_RESULT: 0x8866,\n  QUERY_RESULT_AVAILABLE: 0x8867,\n  STREAM_READ: 0x88e1,\n  STREAM_COPY: 0x88e2,\n  STATIC_READ: 0x88e5,\n  STATIC_COPY: 0x88e6,\n  DYNAMIC_READ: 0x88e9,\n  DYNAMIC_COPY: 0x88ea,\n  MAX_DRAW_BUFFERS: 0x8824,\n  DRAW_BUFFER0: 0x8825,\n  DRAW_BUFFER1: 0x8826,\n  DRAW_BUFFER2: 0x8827,\n  DRAW_BUFFER3: 0x8828,\n  DRAW_BUFFER4: 0x8829,\n  DRAW_BUFFER5: 0x882a,\n  DRAW_BUFFER6: 0x882b,\n  DRAW_BUFFER7: 0x882c,\n  DRAW_BUFFER8: 0x882d,\n  DRAW_BUFFER9: 0x882e,\n  DRAW_BUFFER10: 0x882f,\n  DRAW_BUFFER11: 0x8830,\n  DRAW_BUFFER12: 0x8831,\n  DRAW_BUFFER13: 0x8832,\n  DRAW_BUFFER14: 0x8833,\n  DRAW_BUFFER15: 0x8834,\n  MAX_FRAGMENT_UNIFORM_COMPONENTS: 0x8b49,\n  MAX_VERTEX_UNIFORM_COMPONENTS: 0x8b4a,\n  SAMPLER_3D: 0x8b5f,\n  SAMPLER_2D_SHADOW: 0x8b62,\n  FRAGMENT_SHADER_DERIVATIVE_HINT: 0x8b8b,\n  PIXEL_PACK_BUFFER: 0x88eb,\n  PIXEL_UNPACK_BUFFER: 0x88ec,\n  PIXEL_PACK_BUFFER_BINDING: 0x88ed,\n  PIXEL_UNPACK_BUFFER_BINDING: 0x88ef,\n  FLOAT_MAT2x3: 0x8b65,\n  FLOAT_MAT2x4: 0x8b66,\n  FLOAT_MAT3x2: 0x8b67,\n  FLOAT_MAT3x4: 0x8b68,\n  FLOAT_MAT4x2: 0x8b69,\n  FLOAT_MAT4x3: 0x8b6a,\n  SRGB: 0x8c40,\n  SRGB8: 0x8c41,\n  SRGB8_ALPHA8: 0x8c43,\n  COMPARE_REF_TO_TEXTURE: 0x884e,\n  RGBA32F: 0x8814,\n  RGB32F: 0x8815,\n  RGBA16F: 0x881a,\n  RGB16F: 0x881b,\n  VERTEX_ATTRIB_ARRAY_INTEGER: 0x88fd,\n  MAX_ARRAY_TEXTURE_LAYERS: 0x88ff,\n  MIN_PROGRAM_TEXEL_OFFSET: 0x8904,\n  MAX_PROGRAM_TEXEL_OFFSET: 0x8905,\n  MAX_VARYING_COMPONENTS: 0x8b4b,\n  TEXTURE_2D_ARRAY: 0x8c1a,\n  TEXTURE_BINDING_2D_ARRAY: 0x8c1d,\n  R11F_G11F_B10F: 0x8c3a,\n  UNSIGNED_INT_10F_11F_11F_REV: 0x8c3b,\n  RGB9_E5: 0x8c3d,\n  UNSIGNED_INT_5_9_9_9_REV: 0x8c3e,\n  TRANSFORM_FEEDBACK_BUFFER_MODE: 0x8c7f,\n  MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS: 0x8c80,\n  TRANSFORM_FEEDBACK_VARYINGS: 0x8c83,\n  TRANSFORM_FEEDBACK_BUFFER_START: 0x8c84,\n  TRANSFORM_FEEDBACK_BUFFER_SIZE: 0x8c85,\n  TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN: 0x8c88,\n  RASTERIZER_DISCARD: 0x8c89,\n  MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS: 0x8c8a,\n  MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS: 0x8c8b,\n  INTERLEAVED_ATTRIBS: 0x8c8c,\n  SEPARATE_ATTRIBS: 0x8c8d,\n  TRANSFORM_FEEDBACK_BUFFER: 0x8c8e,\n  TRANSFORM_FEEDBACK_BUFFER_BINDING: 0x8c8f,\n  RGBA32UI: 0x8d70,\n  RGB32UI: 0x8d71,\n  RGBA16UI: 0x8d76,\n  RGB16UI: 0x8d77,\n  RGBA8UI: 0x8d7c,\n  RGB8UI: 0x8d7d,\n  RGBA32I: 0x8d82,\n  RGB32I: 0x8d83,\n  RGBA16I: 0x8d88,\n  RGB16I: 0x8d89,\n  RGBA8I: 0x8d8e,\n  RGB8I: 0x8d8f,\n  RED_INTEGER: 0x8d94,\n  RGB_INTEGER: 0x8d98,\n  RGBA_INTEGER: 0x8d99,\n  SAMPLER_2D_ARRAY: 0x8dc1,\n  SAMPLER_2D_ARRAY_SHADOW: 0x8dc4,\n  SAMPLER_CUBE_SHADOW: 0x8dc5,\n  UNSIGNED_INT_VEC2: 0x8dc6,\n  UNSIGNED_INT_VEC3: 0x8dc7,\n  UNSIGNED_INT_VEC4: 0x8dc8,\n  INT_SAMPLER_2D: 0x8dca,\n  INT_SAMPLER_3D: 0x8dcb,\n  INT_SAMPLER_CUBE: 0x8dcc,\n  INT_SAMPLER_2D_ARRAY: 0x8dcf,\n  UNSIGNED_INT_SAMPLER_2D: 0x8dd2,\n  UNSIGNED_INT_SAMPLER_3D: 0x8dd3,\n  UNSIGNED_INT_SAMPLER_CUBE: 0x8dd4,\n  UNSIGNED_INT_SAMPLER_2D_ARRAY: 0x8dd7,\n  DEPTH_COMPONENT32F: 0x8cac,\n  DEPTH32F_STENCIL8: 0x8cad,\n  FLOAT_32_UNSIGNED_INT_24_8_REV: 0x8dad,\n  FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING: 0x8210,\n  FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE: 0x8211,\n  FRAMEBUFFER_ATTACHMENT_RED_SIZE: 0x8212,\n  FRAMEBUFFER_ATTACHMENT_GREEN_SIZE: 0x8213,\n  FRAMEBUFFER_ATTACHMENT_BLUE_SIZE: 0x8214,\n  FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE: 0x8215,\n  FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE: 0x8216,\n  FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE: 0x8217,\n  FRAMEBUFFER_DEFAULT: 0x8218,\n  UNSIGNED_INT_24_8: 0x84fa,\n  DEPTH24_STENCIL8: 0x88f0,\n  UNSIGNED_NORMALIZED: 0x8c17,\n  DRAW_FRAMEBUFFER_BINDING: 0x8ca6, // Same as FRAMEBUFFER_BINDING\n  READ_FRAMEBUFFER: 0x8ca8,\n  DRAW_FRAMEBUFFER: 0x8ca9,\n  READ_FRAMEBUFFER_BINDING: 0x8caa,\n  RENDERBUFFER_SAMPLES: 0x8cab,\n  FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER: 0x8cd4,\n  MAX_COLOR_ATTACHMENTS: 0x8cdf,\n  COLOR_ATTACHMENT1: 0x8ce1,\n  COLOR_ATTACHMENT2: 0x8ce2,\n  COLOR_ATTACHMENT3: 0x8ce3,\n  COLOR_ATTACHMENT4: 0x8ce4,\n  COLOR_ATTACHMENT5: 0x8ce5,\n  COLOR_ATTACHMENT6: 0x8ce6,\n  COLOR_ATTACHMENT7: 0x8ce7,\n  COLOR_ATTACHMENT8: 0x8ce8,\n  COLOR_ATTACHMENT9: 0x8ce9,\n  COLOR_ATTACHMENT10: 0x8cea,\n  COLOR_ATTACHMENT11: 0x8ceb,\n  COLOR_ATTACHMENT12: 0x8cec,\n  COLOR_ATTACHMENT13: 0x8ced,\n  COLOR_ATTACHMENT14: 0x8cee,\n  COLOR_ATTACHMENT15: 0x8cef,\n  FRAMEBUFFER_INCOMPLETE_MULTISAMPLE: 0x8d56,\n  MAX_SAMPLES: 0x8d57,\n  HALF_FLOAT: 0x140b,\n  RG: 0x8227,\n  RG_INTEGER: 0x8228,\n  R8: 0x8229,\n  RG8: 0x822b,\n  R16F: 0x822d,\n  R32F: 0x822e,\n  RG16F: 0x822f,\n  RG32F: 0x8230,\n  R8I: 0x8231,\n  R8UI: 0x8232,\n  R16I: 0x8233,\n  R16UI: 0x8234,\n  R32I: 0x8235,\n  R32UI: 0x8236,\n  RG8I: 0x8237,\n  RG8UI: 0x8238,\n  RG16I: 0x8239,\n  RG16UI: 0x823a,\n  RG32I: 0x823b,\n  RG32UI: 0x823c,\n  VERTEX_ARRAY_BINDING: 0x85b5,\n  R8_SNORM: 0x8f94,\n  RG8_SNORM: 0x8f95,\n  RGB8_SNORM: 0x8f96,\n  RGBA8_SNORM: 0x8f97,\n  SIGNED_NORMALIZED: 0x8f9c,\n  COPY_READ_BUFFER: 0x8f36,\n  COPY_WRITE_BUFFER: 0x8f37,\n  COPY_READ_BUFFER_BINDING: 0x8f36, // Same as COPY_READ_BUFFER\n  COPY_WRITE_BUFFER_BINDING: 0x8f37, // Same as COPY_WRITE_BUFFER\n  UNIFORM_BUFFER: 0x8a11,\n  UNIFORM_BUFFER_BINDING: 0x8a28,\n  UNIFORM_BUFFER_START: 0x8a29,\n  UNIFORM_BUFFER_SIZE: 0x8a2a,\n  MAX_VERTEX_UNIFORM_BLOCKS: 0x8a2b,\n  MAX_FRAGMENT_UNIFORM_BLOCKS: 0x8a2d,\n  MAX_COMBINED_UNIFORM_BLOCKS: 0x8a2e,\n  MAX_UNIFORM_BUFFER_BINDINGS: 0x8a2f,\n  MAX_UNIFORM_BLOCK_SIZE: 0x8a30,\n  MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS: 0x8a31,\n  MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS: 0x8a33,\n  UNIFORM_BUFFER_OFFSET_ALIGNMENT: 0x8a34,\n  ACTIVE_UNIFORM_BLOCKS: 0x8a36,\n  UNIFORM_TYPE: 0x8a37,\n  UNIFORM_SIZE: 0x8a38,\n  UNIFORM_BLOCK_INDEX: 0x8a3a,\n  UNIFORM_OFFSET: 0x8a3b,\n  UNIFORM_ARRAY_STRIDE: 0x8a3c,\n  UNIFORM_MATRIX_STRIDE: 0x8a3d,\n  UNIFORM_IS_ROW_MAJOR: 0x8a3e,\n  UNIFORM_BLOCK_BINDING: 0x8a3f,\n  UNIFORM_BLOCK_DATA_SIZE: 0x8a40,\n  UNIFORM_BLOCK_ACTIVE_UNIFORMS: 0x8a42,\n  UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES: 0x8a43,\n  UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER: 0x8a44,\n  UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER: 0x8a46,\n  INVALID_INDEX: 0xffffffff,\n  MAX_VERTEX_OUTPUT_COMPONENTS: 0x9122,\n  MAX_FRAGMENT_INPUT_COMPONENTS: 0x9125,\n  MAX_SERVER_WAIT_TIMEOUT: 0x9111,\n  OBJECT_TYPE: 0x9112,\n  SYNC_CONDITION: 0x9113,\n  SYNC_STATUS: 0x9114,\n  SYNC_FLAGS: 0x9115,\n  SYNC_FENCE: 0x9116,\n  SYNC_GPU_COMMANDS_COMPLETE: 0x9117,\n  UNSIGNALED: 0x9118,\n  SIGNALED: 0x9119,\n  ALREADY_SIGNALED: 0x911a,\n  TIMEOUT_EXPIRED: 0x911b,\n  CONDITION_SATISFIED: 0x911c,\n  WAIT_FAILED: 0x911d,\n  SYNC_FLUSH_COMMANDS_BIT: 0x00000001,\n  VERTEX_ATTRIB_ARRAY_DIVISOR: 0x88fe,\n  ANY_SAMPLES_PASSED: 0x8c2f,\n  ANY_SAMPLES_PASSED_CONSERVATIVE: 0x8d6a,\n  SAMPLER_BINDING: 0x8919,\n  RGB10_A2UI: 0x906f,\n  INT_2_10_10_10_REV: 0x8d9f,\n  TRANSFORM_FEEDBACK: 0x8e22,\n  TRANSFORM_FEEDBACK_PAUSED: 0x8e23,\n  TRANSFORM_FEEDBACK_ACTIVE: 0x8e24,\n  TRANSFORM_FEEDBACK_BINDING: 0x8e25,\n  COMPRESSED_R11_EAC: 0x9270,\n  COMPRESSED_SIGNED_R11_EAC: 0x9271,\n  COMPRESSED_RG11_EAC: 0x9272,\n  COMPRESSED_SIGNED_RG11_EAC: 0x9273,\n  COMPRESSED_RGB8_ETC2: 0x9274,\n  COMPRESSED_SRGB8_ETC2: 0x9275,\n  COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2: 0x9276,\n  COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2: 0x9277,\n  COMPRESSED_RGBA8_ETC2_EAC: 0x9278,\n  COMPRESSED_SRGB8_ALPHA8_ETC2_EAC: 0x9279,\n  TEXTURE_IMMUTABLE_FORMAT: 0x912f,\n  MAX_ELEMENT_INDEX: 0x8d6b,\n  TEXTURE_IMMUTABLE_LEVELS: 0x82df,\n\n  // Extensions\n  MAX_TEXTURE_MAX_ANISOTROPY_EXT: 0x84ff,\n};\nexport default Object.freeze(WebGLConstants);\n"], "names": ["WebGLConstants$1", "Object", "freeze", "DEPTH_BUFFER_BIT", "STENCIL_BUFFER_BIT", "COLOR_BUFFER_BIT", "POINTS", "LINES", "LINE_LOOP", "LINE_STRIP", "TRIANGLES", "TRIANGLE_STRIP", "TRIANGLE_FAN", "ZERO", "ONE", "SRC_COLOR", "ONE_MINUS_SRC_COLOR", "SRC_ALPHA", "ONE_MINUS_SRC_ALPHA", "DST_ALPHA", "ONE_MINUS_DST_ALPHA", "DST_COLOR", "ONE_MINUS_DST_COLOR", "SRC_ALPHA_SATURATE", "FUNC_ADD", "BLEND_EQUATION", "BLEND_EQUATION_RGB", "BLEND_EQUATION_ALPHA", "FUNC_SUBTRACT", "FUNC_REVERSE_SUBTRACT", "BLEND_DST_RGB", "BLEND_SRC_RGB", "BLEND_DST_ALPHA", "BLEND_SRC_ALPHA", "CONSTANT_COLOR", "ONE_MINUS_CONSTANT_COLOR", "CONSTANT_ALPHA", "ONE_MINUS_CONSTANT_ALPHA", "BLEND_COLOR", "ARRAY_BUFFER", "ELEMENT_ARRAY_BUFFER", "ARRAY_BUFFER_BINDING", "ELEMENT_ARRAY_BUFFER_BINDING", "STREAM_DRAW", "STATIC_DRAW", "DYNAMIC_DRAW", "BUFFER_SIZE", "BUFFER_USAGE", "CURRENT_VERTEX_ATTRIB", "FRONT", "BACK", "FRONT_AND_BACK", "CULL_FACE", "BLEND", "DITHER", "STENCIL_TEST", "DEPTH_TEST", "SCISSOR_TEST", "POLYGON_OFFSET_FILL", "SAMPLE_ALPHA_TO_COVERAGE", "SAMPLE_COVERAGE", "NO_ERROR", "INVALID_ENUM", "INVALID_VALUE", "INVALID_OPERATION", "OUT_OF_MEMORY", "CW", "CCW", "LINE_WIDTH", "ALIASED_POINT_SIZE_RANGE", "ALIASED_LINE_WIDTH_RANGE", "CULL_FACE_MODE", "FRONT_FACE", "DEPTH_RANGE", "DEPTH_WRITEMASK", "DEPTH_CLEAR_VALUE", "DEPTH_FUNC", "STENCIL_CLEAR_VALUE", "STENCIL_FUNC", "STENCIL_FAIL", "STENCIL_PASS_DEPTH_FAIL", "STENCIL_PASS_DEPTH_PASS", "STENCIL_REF", "STENCIL_VALUE_MASK", "STENCIL_WRITEMASK", "STENCIL_BACK_FUNC", "STENCIL_BACK_FAIL", "STENCIL_BACK_PASS_DEPTH_FAIL", "STENCIL_BACK_PASS_DEPTH_PASS", "STENCIL_BACK_REF", "STENCIL_BACK_VALUE_MASK", "STENCIL_BACK_WRITEMASK", "VIEWPORT", "SCISSOR_BOX", "COLOR_CLEAR_VALUE", "COLOR_WRITEMASK", "UNPACK_ALIGNMENT", "PACK_ALIGNMENT", "MAX_TEXTURE_SIZE", "MAX_VIEWPORT_DIMS", "SUBPIXEL_BITS", "RED_BITS", "GREEN_BITS", "BLUE_BITS", "ALPHA_BITS", "DEPTH_BITS", "STENCIL_BITS", "POLYGON_OFFSET_UNITS", "POLYGON_OFFSET_FACTOR", "TEXTURE_BINDING_2D", "SAMPLE_BUFFERS", "SAMPLES", "SAMPLE_COVERAGE_VALUE", "SAMPLE_COVERAGE_INVERT", "COMPRESSED_TEXTURE_FORMATS", "DONT_CARE", "FASTEST", "NICEST", "GENERATE_MIPMAP_HINT", "BYTE", "UNSIGNED_BYTE", "SHORT", "UNSIGNED_SHORT", "INT", "UNSIGNED_INT", "FLOAT", "DEPTH_COMPONENT", "ALPHA", "RGB", "RGBA", "LUMINANCE", "LUMINANCE_ALPHA", "UNSIGNED_SHORT_4_4_4_4", "UNSIGNED_SHORT_5_5_5_1", "UNSIGNED_SHORT_5_6_5", "FRAGMENT_SHADER", "VERTEX_SHADER", "MAX_VERTEX_ATTRIBS", "MAX_VERTEX_UNIFORM_VECTORS", "MAX_VARYING_VECTORS", "MAX_COMBINED_TEXTURE_IMAGE_UNITS", "MAX_VERTEX_TEXTURE_IMAGE_UNITS", "MAX_TEXTURE_IMAGE_UNITS", "MAX_FRAGMENT_UNIFORM_VECTORS", "SHADER_TYPE", "DELETE_STATUS", "LINK_STATUS", "VALIDATE_STATUS", "ATTACHED_SHADERS", "ACTIVE_UNIFORMS", "ACTIVE_ATTRIBUTES", "SHADING_LANGUAGE_VERSION", "CURRENT_PROGRAM", "NEVER", "LESS", "EQUAL", "LEQUAL", "GREATER", "NOTEQUAL", "GEQUAL", "ALWAYS", "KEEP", "REPLACE", "INCR", "DECR", "INVERT", "INCR_WRAP", "DECR_WRAP", "VENDOR", "RENDERER", "VERSION", "NEAREST", "LINEAR", "NEAREST_MIPMAP_NEAREST", "LINEAR_MIPMAP_NEAREST", "NEAREST_MIPMAP_LINEAR", "LINEAR_MIPMAP_LINEAR", "TEXTURE_MAG_FILTER", "TEXTURE_MIN_FILTER", "TEXTURE_WRAP_S", "TEXTURE_WRAP_T", "TEXTURE_2D", "TEXTURE", "TEXTURE_CUBE_MAP", "TEXTURE_BINDING_CUBE_MAP", "TEXTURE_CUBE_MAP_POSITIVE_X", "TEXTURE_CUBE_MAP_NEGATIVE_X", "TEXTURE_CUBE_MAP_POSITIVE_Y", "TEXTURE_CUBE_MAP_NEGATIVE_Y", "TEXTURE_CUBE_MAP_POSITIVE_Z", "TEXTURE_CUBE_MAP_NEGATIVE_Z", "MAX_CUBE_MAP_TEXTURE_SIZE", "TEXTURE0", "TEXTURE1", "TEXTURE2", "TEXTURE3", "TEXTURE4", "TEXTURE5", "TEXTURE6", "TEXTURE7", "TEXTURE8", "TEXTURE9", "TEXTURE10", "TEXTURE11", "TEXTURE12", "TEXTURE13", "TEXTURE14", "TEXTURE15", "TEXTURE16", "TEXTURE17", "TEXTURE18", "TEXTURE19", "TEXTURE20", "TEXTURE21", "TEXTURE22", "TEXTURE23", "TEXTURE24", "TEXTURE25", "TEXTURE26", "TEXTURE27", "TEXTURE28", "TEXTURE29", "TEXTURE30", "TEXTURE31", "ACTIVE_TEXTURE", "REPEAT", "CLAMP_TO_EDGE", "MIRRORED_REPEAT", "FLOAT_VEC2", "FLOAT_VEC3", "FLOAT_VEC4", "INT_VEC2", "INT_VEC3", "INT_VEC4", "BOOL", "BOOL_VEC2", "BOOL_VEC3", "BOOL_VEC4", "FLOAT_MAT2", "FLOAT_MAT3", "FLOAT_MAT4", "SAMPLER_2D", "SAMPLER_CUBE", "VERTEX_ATTRIB_ARRAY_ENABLED", "VERTEX_ATTRIB_ARRAY_SIZE", "VERTEX_ATTRIB_ARRAY_STRIDE", "VERTEX_ATTRIB_ARRAY_TYPE", "VERTEX_ATTRIB_ARRAY_NORMALIZED", "VERTEX_ATTRIB_ARRAY_POINTER", "VERTEX_ATTRIB_ARRAY_BUFFER_BINDING", "IMPLEMENTATION_COLOR_READ_TYPE", "IMPLEMENTATION_COLOR_READ_FORMAT", "COMPILE_STATUS", "LOW_FLOAT", "MEDIUM_FLOAT", "HIGH_FLOAT", "LOW_INT", "MEDIUM_INT", "HIGH_INT", "FRAMEBUFFER", "RENDERBUFFER", "RGBA4", "RGB5_A1", "RGB565", "DEPTH_COMPONENT16", "STENCIL_INDEX", "STENCIL_INDEX8", "DEPTH_STENCIL", "RENDERBUFFER_WIDTH", "RENDERBUFFER_HEIGHT", "RENDERBUFFER_INTERNAL_FORMAT", "RENDERBUFFER_RED_SIZE", "RENDERBUFFER_GREEN_SIZE", "RENDERBUFFER_BLUE_SIZE", "RENDERBUFFER_ALPHA_SIZE", "RENDERBUFFER_DEPTH_SIZE", "RENDERBUFFER_STENCIL_SIZE", "FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE", "FRAMEBUFFER_ATTACHMENT_OBJECT_NAME", "FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL", "FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE", "COLOR_ATTACHMENT0", "DEPTH_ATTACHMENT", "STENCIL_ATTACHMENT", "DEPTH_STENCIL_ATTACHMENT", "NONE", "FRAMEBUFFER_COMPLETE", "FRAMEBUFFER_INCOMPLETE_ATTACHMENT", "FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT", "FRAMEBUFFER_INCOMPLETE_DIMENSIONS", "FRAMEBUFFER_UNSUPPORTED", "FRAMEBUFFER_BINDING", "RENDERBUFFER_BINDING", "MAX_RENDERBUFFER_SIZE", "INVALID_FRAMEBUFFER_OPERATION", "UNPACK_FLIP_Y_WEBGL", "UNPACK_PREMULTIPLY_ALPHA_WEBGL", "CONTEXT_LOST_WEBGL", "UNPACK_COLORSPACE_CONVERSION_WEBGL", "BROWSER_DEFAULT_WEBGL", "COMPRESSED_RGB_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT1_EXT", "COMPRESSED_RGBA_S3TC_DXT3_EXT", "COMPRESSED_RGBA_S3TC_DXT5_EXT", "COMPRESSED_RGB_PVRTC_4BPPV1_IMG", "COMPRESSED_RGB_PVRTC_2BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "COMPRESSED_RGBA_PVRTC_2BPPV1_IMG", "COMPRESSED_RGBA_ASTC_4x4_WEBGL", "COMPRESSED_RGB_ETC1_WEBGL", "COMPRESSED_RGBA_BPTC_UNORM", "HALF_FLOAT_OES", "DOUBLE", "READ_BUFFER", "UNPACK_ROW_LENGTH", "UNPACK_SKIP_ROWS", "UNPACK_SKIP_PIXELS", "PACK_ROW_LENGTH", "PACK_SKIP_ROWS", "PACK_SKIP_PIXELS", "COLOR", "DEPTH", "STENCIL", "RED", "RGB8", "RGBA8", "RGB10_A2", "TEXTURE_BINDING_3D", "UNPACK_SKIP_IMAGES", "UNPACK_IMAGE_HEIGHT", "TEXTURE_3D", "TEXTURE_WRAP_R", "MAX_3D_TEXTURE_SIZE", "UNSIGNED_INT_2_10_10_10_REV", "MAX_ELEMENTS_VERTICES", "MAX_ELEMENTS_INDICES", "TEXTURE_MIN_LOD", "TEXTURE_MAX_LOD", "TEXTURE_BASE_LEVEL", "TEXTURE_MAX_LEVEL", "MIN", "MAX", "DEPTH_COMPONENT24", "MAX_TEXTURE_LOD_BIAS", "TEXTURE_COMPARE_MODE", "TEXTURE_COMPARE_FUNC", "CURRENT_QUERY", "QUERY_RESULT", "QUERY_RESULT_AVAILABLE", "STREAM_READ", "STREAM_COPY", "STATIC_READ", "STATIC_COPY", "DYNAMIC_READ", "DYNAMIC_COPY", "MAX_DRAW_BUFFERS", "DRAW_BUFFER0", "DRAW_BUFFER1", "DRAW_BUFFER2", "DRAW_BUFFER3", "DRAW_BUFFER4", "DRAW_BUFFER5", "DRAW_BUFFER6", "DRAW_BUFFER7", "DRAW_BUFFER8", "DRAW_BUFFER9", "DRAW_BUFFER10", "DRAW_BUFFER11", "DRAW_BUFFER12", "DRAW_BUFFER13", "DRAW_BUFFER14", "DRAW_BUFFER15", "MAX_FRAGMENT_UNIFORM_COMPONENTS", "MAX_VERTEX_UNIFORM_COMPONENTS", "SAMPLER_3D", "SAMPLER_2D_SHADOW", "FRAGMENT_SHADER_DERIVATIVE_HINT", "PIXEL_PACK_BUFFER", "PIXEL_UNPACK_BUFFER", "PIXEL_PACK_BUFFER_BINDING", "PIXEL_UNPACK_BUFFER_BINDING", "FLOAT_MAT2x3", "FLOAT_MAT2x4", "FLOAT_MAT3x2", "FLOAT_MAT3x4", "FLOAT_MAT4x2", "FLOAT_MAT4x3", "SRGB", "SRGB8", "SRGB8_ALPHA8", "COMPARE_REF_TO_TEXTURE", "RGBA32F", "RGB32F", "RGBA16F", "RGB16F", "VERTEX_ATTRIB_ARRAY_INTEGER", "MAX_ARRAY_TEXTURE_LAYERS", "MIN_PROGRAM_TEXEL_OFFSET", "MAX_PROGRAM_TEXEL_OFFSET", "MAX_VARYING_COMPONENTS", "TEXTURE_2D_ARRAY", "TEXTURE_BINDING_2D_ARRAY", "R11F_G11F_B10F", "UNSIGNED_INT_10F_11F_11F_REV", "RGB9_E5", "UNSIGNED_INT_5_9_9_9_REV", "TRANSFORM_FEEDBACK_BUFFER_MODE", "MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS", "TRANSFORM_FEEDBACK_VARYINGS", "TRANSFORM_FEEDBACK_BUFFER_START", "TRANSFORM_FEEDBACK_BUFFER_SIZE", "TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN", "RASTERIZER_DISCARD", "MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS", "MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS", "INTERLEAVED_ATTRIBS", "SEPARATE_ATTRIBS", "TRANSFORM_FEEDBACK_BUFFER", "TRANSFORM_FEEDBACK_BUFFER_BINDING", "RGBA32UI", "RGB32UI", "RGBA16UI", "RGB16UI", "RGBA8UI", "RGB8UI", "RGBA32I", "RGB32I", "RGBA16I", "RGB16I", "RGBA8I", "RGB8I", "RED_INTEGER", "RGB_INTEGER", "RGBA_INTEGER", "SAMPLER_2D_ARRAY", "SAMPLER_2D_ARRAY_SHADOW", "SAMPLER_CUBE_SHADOW", "UNSIGNED_INT_VEC2", "UNSIGNED_INT_VEC3", "UNSIGNED_INT_VEC4", "INT_SAMPLER_2D", "INT_SAMPLER_3D", "INT_SAMPLER_CUBE", "INT_SAMPLER_2D_ARRAY", "UNSIGNED_INT_SAMPLER_2D", "UNSIGNED_INT_SAMPLER_3D", "UNSIGNED_INT_SAMPLER_CUBE", "UNSIGNED_INT_SAMPLER_2D_ARRAY", "DEPTH_COMPONENT32F", "DEPTH32F_STENCIL8", "FLOAT_32_UNSIGNED_INT_24_8_REV", "FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING", "FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE", "FRAMEBUFFER_ATTACHMENT_RED_SIZE", "FRAMEBUFFER_ATTACHMENT_GREEN_SIZE", "FRAMEBUFFER_ATTACHMENT_BLUE_SIZE", "FRAMEBUFFER_ATTACHMENT_ALPHA_SIZE", "FRAMEBUFFER_ATTACHMENT_DEPTH_SIZE", "FRAMEBUFFER_ATTACHMENT_STENCIL_SIZE", "FRAMEBUFFER_DEFAULT", "UNSIGNED_INT_24_8", "DEPTH24_STENCIL8", "UNSIGNED_NORMALIZED", "DRAW_FRAMEBUFFER_BINDING", "READ_FRAMEBUFFER", "DRAW_FRAMEBUFFER", "READ_FRAMEBUFFER_BINDING", "RENDERBUFFER_SAMPLES", "FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER", "MAX_COLOR_ATTACHMENTS", "COLOR_ATTACHMENT1", "COLOR_ATTACHMENT2", "COLOR_ATTACHMENT3", "COLOR_ATTACHMENT4", "COLOR_ATTACHMENT5", "COLOR_ATTACHMENT6", "COLOR_ATTACHMENT7", "COLOR_ATTACHMENT8", "COLOR_ATTACHMENT9", "COLOR_ATTACHMENT10", "COLOR_ATTACHMENT11", "COLOR_ATTACHMENT12", "COLOR_ATTACHMENT13", "COLOR_ATTACHMENT14", "COLOR_ATTACHMENT15", "FRAMEBUFFER_INCOMPLETE_MULTISAMPLE", "MAX_SAMPLES", "HALF_FLOAT", "RG", "RG_INTEGER", "R8", "RG8", "R16F", "R32F", "RG16F", "RG32F", "R8I", "R8UI", "R16I", "R16UI", "R32I", "R32UI", "RG8I", "RG8UI", "RG16I", "RG16UI", "RG32I", "RG32UI", "VERTEX_ARRAY_BINDING", "R8_SNORM", "RG8_SNORM", "RGB8_SNORM", "RGBA8_SNORM", "SIGNED_NORMALIZED", "COPY_READ_BUFFER", "COPY_WRITE_BUFFER", "COPY_READ_BUFFER_BINDING", "COPY_WRITE_BUFFER_BINDING", "UNIFORM_BUFFER", "UNIFORM_BUFFER_BINDING", "UNIFORM_BUFFER_START", "UNIFORM_BUFFER_SIZE", "MAX_VERTEX_UNIFORM_BLOCKS", "MAX_FRAGMENT_UNIFORM_BLOCKS", "MAX_COMBINED_UNIFORM_BLOCKS", "MAX_UNIFORM_BUFFER_BINDINGS", "MAX_UNIFORM_BLOCK_SIZE", "MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS", "MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS", "UNIFORM_BUFFER_OFFSET_ALIGNMENT", "ACTIVE_UNIFORM_BLOCKS", "UNIFORM_TYPE", "UNIFORM_SIZE", "UNIFORM_BLOCK_INDEX", "UNIFORM_OFFSET", "UNIFORM_ARRAY_STRIDE", "UNIFORM_MATRIX_STRIDE", "UNIFORM_IS_ROW_MAJOR", "UNIFORM_BLOCK_BINDING", "UNIFORM_BLOCK_DATA_SIZE", "UNIFORM_BLOCK_ACTIVE_UNIFORMS", "UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES", "UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER", "UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER", "INVALID_INDEX", "MAX_VERTEX_OUTPUT_COMPONENTS", "MAX_FRAGMENT_INPUT_COMPONENTS", "MAX_SERVER_WAIT_TIMEOUT", "OBJECT_TYPE", "SYNC_CONDITION", "SYNC_STATUS", "SYNC_FLAGS", "SYNC_FENCE", "SYNC_GPU_COMMANDS_COMPLETE", "UNSIGNALED", "SIGNALED", "ALREADY_SIGNALED", "TIMEOUT_EXPIRED", "CONDITION_SATISFIED", "WAIT_FAILED", "SYNC_FLUSH_COMMANDS_BIT", "VERTEX_ATTRIB_ARRAY_DIVISOR", "ANY_SAMPLES_PASSED", "ANY_SAMPLES_PASSED_CONSERVATIVE", "SAMPLER_BINDING", "RGB10_A2UI", "INT_2_10_10_10_REV", "TRANSFORM_FEEDBACK", "TRANSFORM_FEEDBACK_PAUSED", "TRANSFORM_FEEDBACK_ACTIVE", "TRANSFORM_FEEDBACK_BINDING", "COMPRESSED_R11_EAC", "COMPRESSED_SIGNED_R11_EAC", "COMPRESSED_RG11_EAC", "COMPRESSED_SIGNED_RG11_EAC", "COMPRESSED_RGB8_ETC2", "COMPRESSED_SRGB8_ETC2", "COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2", "COMPRESSED_RGBA8_ETC2_EAC", "COMPRESSED_SRGB8_ALPHA8_ETC2_EAC", "TEXTURE_IMMUTABLE_FORMAT", "MAX_ELEMENT_INDEX", "TEXTURE_IMMUTABLE_LEVELS", "MAX_TEXTURE_MAX_ANISOTROPY_EXT"], "mappings": "6CAqmBA,IAAAA,EAAeC,OAAOC,OA1lBC,CACrBC,iBAAkB,IAClBC,mBAAoB,KACpBC,iBAAkB,MAClBC,OAAQ,EACRC,MAAO,EACPC,UAAW,EACXC,WAAY,EACZC,UAAW,EACXC,eAAgB,EAChBC,aAAc,EACdC,KAAM,EACNC,IAAK,EACLC,UAAW,IACXC,oBAAqB,IACrBC,UAAW,IACXC,oBAAqB,IACrBC,UAAW,IACXC,oBAAqB,IACrBC,UAAW,IACXC,oBAAqB,IACrBC,mBAAoB,IACpBC,SAAU,MACVC,eAAgB,MAChBC,mBAAoB,MACpBC,qBAAsB,MACtBC,cAAe,MACfC,sBAAuB,MACvBC,cAAe,MACfC,cAAe,MACfC,gBAAiB,MACjBC,gBAAiB,MACjBC,eAAgB,MAChBC,yBAA0B,MAC1BC,eAAgB,MAChBC,yBAA0B,MAC1BC,YAAa,MACbC,aAAc,MACdC,qBAAsB,MACtBC,qBAAsB,MACtBC,6BAA8B,MAC9BC,YAAa,MACbC,YAAa,MACbC,aAAc,MACdC,YAAa,MACbC,aAAc,MACdC,sBAAuB,MACvBC,MAAO,KACPC,KAAM,KACNC,eAAgB,KAChBC,UAAW,KACXC,MAAO,KACPC,OAAQ,KACRC,aAAc,KACdC,WAAY,KACZC,aAAc,KACdC,oBAAqB,MACrBC,yBAA0B,MAC1BC,gBAAiB,MACjBC,SAAU,EACVC,aAAc,KACdC,cAAe,KACfC,kBAAmB,KACnBC,cAAe,KACfC,GAAI,KACJC,IAAK,KACLC,WAAY,KACZC,yBAA0B,MAC1BC,yBAA0B,MAC1BC,eAAgB,KAChBC,WAAY,KACZC,YAAa,KACbC,gBAAiB,KACjBC,kBAAmB,KACnBC,WAAY,KACZC,oBAAqB,KACrBC,aAAc,KACdC,aAAc,KACdC,wBAAyB,KACzBC,wBAAyB,KACzBC,YAAa,KACbC,mBAAoB,KACpBC,kBAAmB,KACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,6BAA8B,MAC9BC,6BAA8B,MAC9BC,iBAAkB,MAClBC,wBAAyB,MACzBC,uBAAwB,MACxBC,SAAU,KACVC,YAAa,KACbC,kBAAmB,KACnBC,gBAAiB,KACjBC,iBAAkB,KAClBC,eAAgB,KAChBC,iBAAkB,KAClBC,kBAAmB,KACnBC,cAAe,KACfC,SAAU,KACVC,WAAY,KACZC,UAAW,KACXC,WAAY,KACZC,WAAY,KACZC,aAAc,KACdC,qBAAsB,MACtBC,sBAAuB,MACvBC,mBAAoB,MACpBC,eAAgB,MAChBC,QAAS,MACTC,sBAAuB,MACvBC,uBAAwB,MACxBC,2BAA4B,MAC5BC,UAAW,KACXC,QAAS,KACTC,OAAQ,KACRC,qBAAsB,MACtBC,KAAM,KACNC,cAAe,KACfC,MAAO,KACPC,eAAgB,KAChBC,IAAK,KACLC,aAAc,KACdC,MAAO,KACPC,gBAAiB,KACjBC,MAAO,KACPC,IAAK,KACLC,KAAM,KACNC,UAAW,KACXC,gBAAiB,KACjBC,uBAAwB,MACxBC,uBAAwB,MACxBC,qBAAsB,MACtBC,gBAAiB,MACjBC,cAAe,MACfC,mBAAoB,MACpBC,2BAA4B,MAC5BC,oBAAqB,MACrBC,iCAAkC,MAClCC,+BAAgC,MAChCC,wBAAyB,MACzBC,6BAA8B,MAC9BC,YAAa,MACbC,cAAe,MACfC,YAAa,MACbC,gBAAiB,MACjBC,iBAAkB,MAClBC,gBAAiB,MACjBC,kBAAmB,MACnBC,yBAA0B,MAC1BC,gBAAiB,MACjBC,MAAO,IACPC,KAAM,IACNC,MAAO,IACPC,OAAQ,IACRC,QAAS,IACTC,SAAU,IACVC,OAAQ,IACRC,OAAQ,IACRC,KAAM,KACNC,QAAS,KACTC,KAAM,KACNC,KAAM,KACNC,OAAQ,KACRC,UAAW,MACXC,UAAW,MACXC,OAAQ,KACRC,SAAU,KACVC,QAAS,KACTC,QAAS,KACTC,OAAQ,KACRC,uBAAwB,KACxBC,sBAAuB,KACvBC,sBAAuB,KACvBC,qBAAsB,KACtBC,mBAAoB,MACpBC,mBAAoB,MACpBC,eAAgB,MAChBC,eAAgB,MAChBC,WAAY,KACZC,QAAS,KACTC,iBAAkB,MAClBC,yBAA0B,MAC1BC,4BAA6B,MAC7BC,4BAA6B,MAC7BC,4BAA6B,MAC7BC,4BAA6B,MAC7BC,4BAA6B,MAC7BC,4BAA6B,MAC7BC,0BAA2B,MAC3BC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,KACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,eAAgB,MAChBC,OAAQ,MACRC,cAAe,MACfC,gBAAiB,MACjBC,WAAY,MACZC,WAAY,MACZC,WAAY,MACZC,SAAU,MACVC,SAAU,MACVC,SAAU,MACVC,KAAM,MACNC,UAAW,MACXC,UAAW,MACXC,UAAW,MACXC,WAAY,MACZC,WAAY,MACZC,WAAY,MACZC,WAAY,MACZC,aAAc,MACdC,4BAA6B,MAC7BC,yBAA0B,MAC1BC,2BAA4B,MAC5BC,yBAA0B,MAC1BC,+BAAgC,MAChCC,4BAA6B,MAC7BC,mCAAoC,MACpCC,+BAAgC,MAChCC,iCAAkC,MAClCC,eAAgB,MAChBC,UAAW,MACXC,aAAc,MACdC,WAAY,MACZC,QAAS,MACTC,WAAY,MACZC,SAAU,MACVC,YAAa,MACbC,aAAc,MACdC,MAAO,MACPC,QAAS,MACTC,OAAQ,MACRC,kBAAmB,MACnBC,cAAe,KACfC,eAAgB,MAChBC,cAAe,MACfC,mBAAoB,MACpBC,oBAAqB,MACrBC,6BAA8B,MAC9BC,sBAAuB,MACvBC,wBAAyB,MACzBC,uBAAwB,MACxBC,wBAAyB,MACzBC,wBAAyB,MACzBC,0BAA2B,MAC3BC,mCAAoC,MACpCC,mCAAoC,MACpCC,qCAAsC,MACtCC,6CAA8C,MAC9CC,kBAAmB,MACnBC,iBAAkB,MAClBC,mBAAoB,MACpBC,yBAA0B,MAC1BC,KAAM,EACNC,qBAAsB,MACtBC,kCAAmC,MACnCC,0CAA2C,MAC3CC,kCAAmC,MACnCC,wBAAyB,MACzBC,oBAAqB,MACrBC,qBAAsB,MACtBC,sBAAuB,MACvBC,8BAA+B,KAC/BC,oBAAqB,MACrBC,+BAAgC,MAChCC,mBAAoB,MACpBC,mCAAoC,MACpCC,sBAAuB,MAGvBC,6BAA8B,MAC9BC,8BAA+B,MAC/BC,8BAA+B,MAC/BC,8BAA+B,MAG/BC,gCAAiC,MACjCC,gCAAiC,MACjCC,iCAAkC,MAClCC,iCAAkC,MAGlCC,+BAAgC,MAGhCC,0BAA2B,MAG3BC,2BAA4B,MAG5BC,eAAgB,MAGhBC,OAAQ,KAGRC,YAAa,KACbC,kBAAmB,KACnBC,iBAAkB,KAClBC,mBAAoB,KACpBC,gBAAiB,KACjBC,eAAgB,KAChBC,iBAAkB,KAClBC,MAAO,KACPC,MAAO,KACPC,QAAS,KACTC,IAAK,KACLC,KAAM,MACNC,MAAO,MACPC,SAAU,MACVC,mBAAoB,MACpBC,mBAAoB,MACpBC,oBAAqB,MACrBC,WAAY,MACZC,eAAgB,MAChBC,oBAAqB,MACrBC,4BAA6B,MAC7BC,sBAAuB,KACvBC,qBAAsB,MACtBC,gBAAiB,MACjBC,gBAAiB,MACjBC,mBAAoB,MACpBC,kBAAmB,MACnBC,IAAK,MACLC,IAAK,MACLC,kBAAmB,MACnBC,qBAAsB,MACtBC,qBAAsB,MACtBC,qBAAsB,MACtBC,cAAe,MACfC,aAAc,MACdC,uBAAwB,MACxBC,YAAa,MACbC,YAAa,MACbC,YAAa,MACbC,YAAa,MACbC,aAAc,MACdC,aAAc,MACdC,iBAAkB,MAClBC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,cAAe,MACfC,cAAe,MACfC,cAAe,MACfC,cAAe,MACfC,cAAe,MACfC,cAAe,MACfC,gCAAiC,MACjCC,8BAA+B,MAC/BC,WAAY,MACZC,kBAAmB,MACnBC,gCAAiC,MACjCC,kBAAmB,MACnBC,oBAAqB,MACrBC,0BAA2B,MAC3BC,4BAA6B,MAC7BC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,aAAc,MACdC,KAAM,MACNC,MAAO,MACPC,aAAc,MACdC,uBAAwB,MACxBC,QAAS,MACTC,OAAQ,MACRC,QAAS,MACTC,OAAQ,MACRC,4BAA6B,MAC7BC,yBAA0B,MAC1BC,yBAA0B,MAC1BC,yBAA0B,MAC1BC,uBAAwB,MACxBC,iBAAkB,MAClBC,yBAA0B,MAC1BC,eAAgB,MAChBC,6BAA8B,MAC9BC,QAAS,MACTC,yBAA0B,MAC1BC,+BAAgC,MAChCC,2CAA4C,MAC5CC,4BAA6B,MAC7BC,gCAAiC,MACjCC,+BAAgC,MAChCC,sCAAuC,MACvCC,mBAAoB,MACpBC,8CAA+C,MAC/CC,wCAAyC,MACzCC,oBAAqB,MACrBC,iBAAkB,MAClBC,0BAA2B,MAC3BC,kCAAmC,MACnCC,SAAU,MACVC,QAAS,MACTC,SAAU,MACVC,QAAS,MACTC,QAAS,MACTC,OAAQ,MACRC,QAAS,MACTC,OAAQ,MACRC,QAAS,MACTC,OAAQ,MACRC,OAAQ,MACRC,MAAO,MACPC,YAAa,MACbC,YAAa,MACbC,aAAc,MACdC,iBAAkB,MAClBC,wBAAyB,MACzBC,oBAAqB,MACrBC,kBAAmB,MACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,eAAgB,MAChBC,eAAgB,MAChBC,iBAAkB,MAClBC,qBAAsB,MACtBC,wBAAyB,MACzBC,wBAAyB,MACzBC,0BAA2B,MAC3BC,8BAA+B,MAC/BC,mBAAoB,MACpBC,kBAAmB,MACnBC,+BAAgC,MAChCC,sCAAuC,MACvCC,sCAAuC,MACvCC,gCAAiC,MACjCC,kCAAmC,MACnCC,iCAAkC,MAClCC,kCAAmC,MACnCC,kCAAmC,MACnCC,oCAAqC,MACrCC,oBAAqB,MACrBC,kBAAmB,MACnBC,iBAAkB,MAClBC,oBAAqB,MACrBC,yBAA0B,MAC1BC,iBAAkB,MAClBC,iBAAkB,MAClBC,yBAA0B,MAC1BC,qBAAsB,MACtBC,qCAAsC,MACtCC,sBAAuB,MACvBC,kBAAmB,MACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,kBAAmB,MACnBC,mBAAoB,MACpBC,mBAAoB,MACpBC,mBAAoB,MACpBC,mBAAoB,MACpBC,mBAAoB,MACpBC,mBAAoB,MACpBC,mCAAoC,MACpCC,YAAa,MACbC,WAAY,KACZC,GAAI,MACJC,WAAY,MACZC,GAAI,MACJC,IAAK,MACLC,KAAM,MACNC,KAAM,MACNC,MAAO,MACPC,MAAO,MACPC,IAAK,MACLC,KAAM,MACNC,KAAM,MACNC,MAAO,MACPC,KAAM,MACNC,MAAO,MACPC,KAAM,MACNC,MAAO,MACPC,MAAO,MACPC,OAAQ,MACRC,MAAO,MACPC,OAAQ,MACRC,qBAAsB,MACtBC,SAAU,MACVC,UAAW,MACXC,WAAY,MACZC,YAAa,MACbC,kBAAmB,MACnBC,iBAAkB,MAClBC,kBAAmB,MACnBC,yBAA0B,MAC1BC,0BAA2B,MAC3BC,eAAgB,MAChBC,uBAAwB,MACxBC,qBAAsB,MACtBC,oBAAqB,MACrBC,0BAA2B,MAC3BC,4BAA6B,MAC7BC,4BAA6B,MAC7BC,4BAA6B,MAC7BC,uBAAwB,MACxBC,uCAAwC,MACxCC,yCAA0C,MAC1CC,gCAAiC,MACjCC,sBAAuB,MACvBC,aAAc,MACdC,aAAc,MACdC,oBAAqB,MACrBC,eAAgB,MAChBC,qBAAsB,MACtBC,sBAAuB,MACvBC,qBAAsB,MACtBC,sBAAuB,MACvBC,wBAAyB,MACzBC,8BAA+B,MAC/BC,qCAAsC,MACtCC,0CAA2C,MAC3CC,4CAA6C,MAC7CC,cAAe,WACfC,6BAA8B,MAC9BC,8BAA+B,MAC/BC,wBAAyB,MACzBC,YAAa,MACbC,eAAgB,MAChBC,YAAa,MACbC,WAAY,MACZC,WAAY,MACZC,2BAA4B,MAC5BC,WAAY,MACZC,SAAU,MACVC,iBAAkB,MAClBC,gBAAiB,MACjBC,oBAAqB,MACrBC,YAAa,MACbC,wBAAyB,EACzBC,4BAA6B,MAC7BC,mBAAoB,MACpBC,gCAAiC,MACjCC,gBAAiB,MACjBC,WAAY,MACZC,mBAAoB,MACpBC,mBAAoB,MACpBC,0BAA2B,MAC3BC,0BAA2B,MAC3BC,2BAA4B,MAC5BC,mBAAoB,MACpBC,0BAA2B,MAC3BC,oBAAqB,MACrBC,2BAA4B,MAC5BC,qBAAsB,MACtBC,sBAAuB,MACvBC,yCAA0C,MAC1CC,0CAA2C,MAC3CC,0BAA2B,MAC3BC,iCAAkC,MAClCC,yBAA0B,MAC1BC,kBAAmB,MACnBC,yBAA0B,MAG1BC,+BAAgC"}