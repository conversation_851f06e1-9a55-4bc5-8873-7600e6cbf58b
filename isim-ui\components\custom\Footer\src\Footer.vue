<!--
* @Author: 张乐
* @Date: 2023/4/26/026 10:26
* @Version: 1.0
* @Content:
-->
<template>
  <div class="flex-wrap sim-footer">
    <div class="sim-drawer__footer-bg"></div>
    <slot></slot>
  </div>
</template>
<script lang="ts">
export default {
  name: 'SimFooter'
};
</script>
<script setup lang="ts">
// 第三方包

// 组件

// hooks
</script>

<style scoped lang="less">
.sim-footer {
  position: relative;
  width: 100%;
  height: 60px;
  justify-content: center;
  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // -webkit-mask-image: url('@/assets/images/footer-bg.svg');
    // -webkit-mask-repeat: no-repeat;
    // -webkit-mask-position: center center;
    background: rgba(var(--primary-color-val), 1);
  }
}
</style>
