import { createPropertyDescriptor, DataSourceDisplay } from 'cesium';
// import {processConicSensor, processCustomPatternSensor} from './Sensor'
// import {processRectangularSensor, RectangularSensorVisualizer} from './RegularSensor'
// import ConicSensorVisualizer from './Sensor/ConicSensorVisualizer.js'
// import CustomPatternSensorVisualizer from './Sensor/CustomSensorVisualizer.js'
// import {ProgressVisualizer} from './Progess/ProgressVisualizer.js'
// import {processProgress} from './Progess/ProgressProcess.js'
//
// import ConeVisualizer from './Cone/ConeVisualizer.ts'
import { JammingRadarVisualizer } from './JammingRadar/JammingRadarVisualizer';
// import {processCone} from "./Cone/index.ts";
// import {ProgressGraphics} from "./Progess/ProgressGraphics";

// import ConeGraphics from "./Cone/ConeGraphics";

/**
 * @function
 * @export
 * @private
 */
export function initEntityGraphic() {
  // CzmlDataSource.updaters.push(processConicSensor, processCustomPatternSensor, processRectangularSensor, processProgress, processCone)
  console.log('initEntityGraphic');
  const originalDefaultVisualizersCallback = DataSourceDisplay.defaultVisualizersCallback;
  DataSourceDisplay.defaultVisualizersCallback = function (scene, entityCluster, dataSource) {
    const entities = dataSource.entities;
    const array = originalDefaultVisualizersCallback(scene, entityCluster, dataSource);
    return array.concat([
      // new RectangularSensorVisualizer(scene, entities),
      // new ConicSensorVisualizer(scene, entities),
      // new CustomPatternSensorVisualizer(scene, entities),
      // new ConeVisualizer(scene, entities),
      // new ProgressVisualizer(entityCluster, entities),
      new JammingRadarVisualizer(scene, entities)
    ]);
  };
}
// 全局只注册一次
initEntityGraphic();
function createPropertyTypeDescriptor(name, Type) {
  return createPropertyDescriptor(name, undefined, function (value) {
    if (value instanceof Type) {
      return value;
    }
    return new Type(value);
  });
}
