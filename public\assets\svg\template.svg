<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120" height="120" viewBox="0 0 120 120">
  <defs>
    <linearGradient id="linear-gradient" x1="0.253" y1="0.092" x2="0.585" y2="0.836" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#19ebfd" stop-opacity="0.502"/>
      <stop offset="1" stop-color="#4998ff" stop-opacity="0.8"/>
    </linearGradient>
    <filter id="路径_10">
      <feOffset dx="1" dy="-3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#d7fffc" result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-2" x1="0.253" y1="0.092" x2="0.585" y2="0.836" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#5ef2ff"/>
      <stop offset="1" stop-color="#49aaff"/>
    </linearGradient>
    <filter id="交叉_1">
      <feOffset dx="1" dy="-3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-color="#d7fffc" result="color-2"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-2"/>
      <feComposite operator="in" in="color-2"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-3" x1="0.3" y1="0.671" x2="0.397" y2="0.774" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3bd3f1"/>
      <stop offset="1" stop-color="#16d9ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.245" y1="0.5" x2="0.345" y2="0.802" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#75c4ff"/>
      <stop offset="1" stop-color="#374bff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.253" y1="0.5" x2="0.458" y2="0.898" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#0a22ff"/>
      <stop offset="1" stop-color="#cfd3ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0.161" y1="0.585" x2="0.623" y2="0.966" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#5ca2ff"/>
      <stop offset="1" stop-color="#0068ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="0.098" y1="0.218" x2="0.674" y2="0.994" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#7fb4ff"/>
      <stop offset="1" stop-color="#0068ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#cdebff"/>
      <stop offset="1" stop-color="#f5fbff"/>
    </linearGradient>
    <filter id="路径_15" x="22.951" y="16.968" width="63.406" height="93.358" filterUnits="userSpaceOnUse">
      <feOffset dx="-0.5" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur-3"/>
      <feFlood flood-color="#04134c"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-9" x1="0.831" y1="0.027" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffe8ca"/>
      <stop offset="1" stop-color="#730"/>
    </linearGradient>
    <filter id="路径_17" x="24.873" y="17.851" width="63.119" height="91.038" filterUnits="userSpaceOnUse">
      <feOffset dx="-0.3" dy="-0.3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur-4"/>
      <feFlood flood-opacity="0.639"/>
      <feComposite operator="in" in2="blur-4"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-10" x1="0.887" y1="0.042" x2="0.292" y2="0.968" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3400ff" stop-opacity="0.588"/>
      <stop offset="1" stop-color="#5f25ff"/>
    </linearGradient>
    <filter id="路径_19" x="26.042" y="18.81" width="63.001" height="90.969" filterUnits="userSpaceOnUse">
      <feOffset dx="-0.5" dy="-0.5" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur-5"/>
      <feFlood flood-color="#00285d"/>
      <feComposite operator="in" in2="blur-5"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-11" x1="0.16" y1="0.872" x2="0.736" y2="0.09" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#ad42ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="0.19" y1="0.251" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e6f5fe"/>
      <stop offset="1" stop-color="#02f"/>
    </linearGradient>
    <filter id="路径_21" x="28.128" y="19.904" width="62.798" height="91.79" filterUnits="userSpaceOnUse">
      <feOffset dx="-0.5" dy="-0.5" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur-6"/>
      <feFlood flood-color="#054690" flood-opacity="0.549"/>
      <feComposite operator="in" in2="blur-6"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-13" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4b63f1"/>
      <stop offset="0.801" stop-color="#f5f7ff"/>
      <stop offset="1" stop-color="#f6f8ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#91b2df"/>
    </linearGradient>
    <filter id="路径_23" x="31.74" y="19.922" width="67.245" height="94.051" filterUnits="userSpaceOnUse">
      <feOffset dx="-0.5" dy="-0.5" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur-7"/>
      <feFlood flood-color="#0c3fc6" flood-opacity="0.749"/>
      <feComposite operator="in" in2="blur-7"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-15" x1="0.378" y1="0.195" x2="0.685" y2="0.697" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#09f" stop-opacity="0.58"/>
      <stop offset="0.134" stop-color="#5ec6ff" stop-opacity="0.58"/>
      <stop offset="1" stop-color="#3177ff" stop-opacity="0.8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4058f8"/>
      <stop offset="1" stop-color="#000839"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="0.445" y1="0.411" x2="0.56" y2="0.757" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#6a93fe"/>
      <stop offset="1" stop-color="#004cfc"/>
    </linearGradient>
    <filter id="路径_32" x="38.049" y="40.671" width="69.615" height="40.492" filterUnits="userSpaceOnUse">
      <feOffset dy="0.5" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur-8"/>
      <feFlood flood-color="#fff"/>
      <feComposite operator="in" in2="blur-8"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="路径_32-2" x="38.049" y="40.671" width="69.615" height="40.492" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-9"/>
      <feFlood flood-color="#fff" flood-opacity="0.278" result="color-3"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-9"/>
      <feComposite operator="in" in="color-3"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-18" x1="0.412" y1="0.318" x2="0.466" y2="0.872" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#cedbf0"/>
      <stop offset="1" stop-color="#194df0"/>
    </linearGradient>
    <filter id="路径_32-3" x="45.085" y="62.778" width="69.615" height="40.492" filterUnits="userSpaceOnUse">
      <feOffset dy="0.5" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="0.5" result="blur-10"/>
      <feFlood flood-color="#fff" flood-opacity="0.91"/>
      <feComposite operator="in" in2="blur-10"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-画板_4">
      <rect width="120" height="120"/>
    </clipPath>
  </defs>
  <g id="画板_4" data-name="画板 – 4" clip-path="url(#clip-画板_4)">
    <g id="图层_2" data-name="图层 2" transform="translate(3.713 6.988)">
      <g id="图层_1" data-name="图层 1" transform="translate(-0.004 -0.001)">
        <g id="底" transform="translate(0 2.828)">
          <path id="路径_8" data-name="路径 8" d="M57.017,3.184a3.18,3.18,0,0,0-.93.356L40.67,12.448l.666-.138L56.753,3.4a3.237,3.237,0,0,1,.941-.4Z" transform="translate(6.022 -2.383)" fill="#bdf6ff"/>
          <path id="路径_9" data-name="路径 9" d="M12.758,18.65v-.1A6.67,6.67,0,0,1,13,17.824l.092-.2a3.111,3.111,0,0,1,.241-.425,4.259,4.259,0,0,1,.276-.356,2.663,2.663,0,0,1,.241-.287,3.984,3.984,0,0,1,.333-.287,2.445,2.445,0,0,1,.39-.287L37.255,2.866a3.18,3.18,0,0,1,.93-.356l-.666.138A3.375,3.375,0,0,0,36.578,3L13.906,16.1l-.2.126a1.492,1.492,0,0,0-.207.172,3.984,3.984,0,0,0-.333.287l-.241.287a4.076,4.076,0,0,0-.264.356l-.08.126c0,.1-.1.207-.149.31a1.492,1.492,0,0,0-.092.2,4,4,0,0,0-.241.712v.115a6.2,6.2,0,0,0-.1.666,1.779,1.779,0,0,1,0,.253v.471a5.166,5.166,0,0,0,.08.723,1.734,1.734,0,0,0,0,.321,4.787,4.787,0,0,0,.321,1.022l3.191,7.232.666-.138-3.111-7.232a6.578,6.578,0,0,1-.321-1.022c0-.1,0-.218,0-.321a5.166,5.166,0,0,1-.08-.723,3.065,3.065,0,0,1,0-.459,1.779,1.779,0,0,1,0-.253,4.385,4.385,0,0,1,.011-.677Z" transform="translate(1.78 -2.455)" fill="#50a1ca"/>
          <g data-type="innerShadowGroup">
            <path id="路径_10-2" data-name="路径 10" d="M38.945,2.873c1.825-1.056,4.133,0,5.154,2.3l3.157,7.2L62.685,3.481c1.825-1.045,4.133,0,5.154,2.3L92.348,61.752c1.022,2.3.367,5.074-1.458,6.13L30.725,102.62c-1.825,1.056-4.133,0-5.166-2.3L1.062,44.407c-1.022-2.3-.356-5.086,1.469-6.142l15.417-8.908-3.111-7.244c-1.022-2.3-.367-5.074,1.469-6.13Z" transform="translate(0.09 -2.462)" fill="url(#linear-gradient)"/>
            <g transform="matrix(1, 0, 0, 1, -3.71, -9.82)" filter="url(#路径_10)">
              <path id="路径_10-3" data-name="路径 10" d="M38.945,2.873c1.825-1.056,4.133,0,5.154,2.3l3.157,7.2L62.685,3.481c1.825-1.045,4.133,0,5.154,2.3L92.348,61.752c1.022,2.3.367,5.074-1.458,6.13L30.725,102.62c-1.825,1.056-4.133,0-5.166-2.3L1.062,44.407c-1.022-2.3-.356-5.086,1.469-6.142l15.417-8.908-3.111-7.244c-1.022-2.3-.367-5.074,1.469-6.13Z" transform="translate(3.8 7.35)" fill="#fff"/>
            </g>
          </g>
          <path id="路径_11" data-name="路径 11" d="M25.646,96.84,1.149,40.946a6.578,6.578,0,0,1-.321-1.022q-.008-.166,0-.333a4.821,4.821,0,0,1-.08-.712,3.226,3.226,0,0,1,0-.471,1.86,1.86,0,0,1,0-.253,6.406,6.406,0,0,1,.1-.677s0-.069,0-.1a6.67,6.67,0,0,1,.241-.723,1.493,1.493,0,0,0,.057-.241,3.111,3.111,0,0,1,.241-.425,4.26,4.26,0,0,1,.264-.367,1.8,1.8,0,0,1,.241-.276,2.7,2.7,0,0,1,.333-.3,3.444,3.444,0,0,1,.39-.287L18.035,25.85l-.666.138L1.941,34.9l-.184.115a1.492,1.492,0,0,0-.207.172,3.985,3.985,0,0,0-.333.287l-.241.287a2.95,2.95,0,0,0-.253.367.448.448,0,0,0-.092.115c-.057.1-.1.207-.149.31a1.492,1.492,0,0,0-.092.2A4.856,4.856,0,0,0,.15,37.5s0,.069,0,.1a6.406,6.406,0,0,0-.1.677,1.952,1.952,0,0,1,0,.253A.454.454,0,0,0,0,38.788a1.01,1.01,0,0,0,0,.184,5.2,5.2,0,0,0,.08.712,2.709,2.709,0,0,0,0,.321,5.59,5.59,0,0,0,.333,1.022L24.98,96.978a5.281,5.281,0,0,0,.792,1.286,4.764,4.764,0,0,0,1.033.907A3.444,3.444,0,0,0,28.6,99.7a2.147,2.147,0,0,0,.608-.057l.643-.149a4.006,4.006,0,0,1-4.2-2.652Z" transform="translate(0.004 0.999)" fill="#003799"/>
          <g data-type="innerShadowGroup">
            <path id="交叉_1-2" data-name="交叉 1" d="M.48,41.945c-1.022-2.3-.356-5.086,1.469-6.142l15.417-8.908-3.111-7.244c-1.022-2.3-.368-5.074,1.469-6.131L38.362.411c1.826-1.057,4.133,0,5.155,2.3l3.157,7.2L62.1,1.019c1.826-1.045,4.133,0,5.154,2.3l.764,1.745-66.7,38.8Z" transform="translate(0.673 0)" fill="url(#linear-gradient-2)"/>
            <g transform="matrix(1, 0, 0, 1, -3.71, -9.82)" filter="url(#交叉_1)">
              <path id="交叉_1-3" data-name="交叉 1" d="M.48,41.945c-1.022-2.3-.356-5.086,1.469-6.142l15.417-8.908-3.111-7.244c-1.022-2.3-.368-5.074,1.469-6.131L38.362.411c1.826-1.057,4.133,0,5.155,2.3l3.157,7.2L62.1,1.019c1.826-1.045,4.133,0,5.154,2.3l.764,1.745-66.7,38.8Z" transform="translate(4.38 9.82)" fill="#fff"/>
            </g>
          </g>
          <path id="路径_33" data-name="路径 33" d="M41.325,90.69l-12.316,9.549.792.347,12.111-9.043Z" fill="#fff" opacity="0.36"/>
          <path id="路径_34" data-name="路径 34" d="M42.178,90.147,28.759,100.06l1.188.156,13.239-8.971Z" transform="translate(1 0.516)" fill="url(#linear-gradient-3)"/>
          <path id="路径_35" data-name="路径 35" d="M43.2,89.6,28.931,99.831l1.906.25L44.687,91.4Z" transform="translate(2 0.885)" fill="url(#linear-gradient-4)"/>
          <path id="路径_36" data-name="路径 36" d="M43.864,89.789,28.817,99.884l5.349.6,11.983-7.328Z" transform="translate(4 1.051)" fill="url(#linear-gradient-5)"/>
          <path id="路径_37" data-name="路径 37" d="M40.729,91.7l-12.52,8.794,9.955,1.429L42.5,92.88Z" transform="translate(10 1.051)" fill="url(#linear-gradient-6)"/>
          <path id="路径_29" data-name="路径 29" d="M24.845,90.4l4.3.5L44.8,92.862l-.526-.276-9.837-1.453A4.447,4.447,0,0,0,32.468,88.8c-1.281-.491-3.318,1.83-3.318,1.83l-3.969-.466Z" transform="translate(3.68 10.303)" fill="#002b79"/>
        </g>
        <g id="蓝" transform="translate(17.69)">
          <path id="路径_12" data-name="路径 12" d="M42.893,16.833a5.327,5.327,0,0,1,.126-.746V16a5.419,5.419,0,0,1,.207-.608l.1-.207a2.974,2.974,0,0,1,.218-.379,2.3,2.3,0,0,1,.276-.379,2.524,2.524,0,0,1,.23-.264,3,3,0,0,1,.356-.31,4.683,4.683,0,0,1,.379-.287L67.6.417A3.1,3.1,0,0,1,68.562.05l-.677.138a3.2,3.2,0,0,0-.964.367L44.156,13.7l-.207.126a2.3,2.3,0,0,0-.184.161,4.281,4.281,0,0,0-.344.3c-.08.092-.161.172-.23.264a3.938,3.938,0,0,0-.276.379,1.079,1.079,0,0,0-.1.138,2.456,2.456,0,0,0-.115.253l-.1.207a4.752,4.752,0,0,0-.218.608.218.218,0,0,0,0,.08,4.053,4.053,0,0,0-.126.746,1.539,1.539,0,0,0,0,.23v.494a6.362,6.362,0,0,0,.08.654,2.881,2.881,0,0,0,0,.31,5.6,5.6,0,0,0,.3.941l1.63,3.719.677-.126-1.63-3.674a5.316,5.316,0,0,1-.31-.941,2.881,2.881,0,0,1,0-.31,4.224,4.224,0,0,1-.069-.654,3.549,3.549,0,0,1,0-.494,1.32,1.32,0,0,1-.034-.276Z" transform="translate(-11.435 0.009)" fill="#229bff"/>
          <path id="路径_13" data-name="路径 13" d="M39.587,96.77,16.455,43.963a4.718,4.718,0,0,1-.3-.941,2.938,2.938,0,0,1,0-.31,6.326,6.326,0,0,1-.069-.654,3.708,3.708,0,0,1,0-.505,1.389,1.389,0,0,0,0-.218,5.327,5.327,0,0,1,.126-.746V40.5a3.846,3.846,0,0,1,.218-.608,1.733,1.733,0,0,1,.1-.207,2.606,2.606,0,0,1,.218-.39,3.938,3.938,0,0,1,.276-.379,2.525,2.525,0,0,1,.23-.264,3.135,3.135,0,0,1,.356-.31,4.592,4.592,0,0,1,.379-.276L48.908,20.2l-.677.126L17.316,38.177l-.207.126c-.069,0-.115.115-.184.161a2.893,2.893,0,0,0-.344.31,2.629,2.629,0,0,0-.241.264l-.276.379-.092.138-.126.241-.1.218a4.73,4.73,0,0,0-.207.608v.092a5.327,5.327,0,0,0-.126.746,1.389,1.389,0,0,0,0,.218v.505a4.374,4.374,0,0,0,.069.654,2.881,2.881,0,0,0,0,.31,6.015,6.015,0,0,0,.31.941l23.12,52.8a3.788,3.788,0,0,0,4.075,2.468l.666-.126a3.777,3.777,0,0,1-4.064-2.457Z" transform="translate(-15.406 2.99)" fill="url(#linear-gradient-7)"/>
          <path id="路径_14" data-name="路径 14" d="M71.5.425c1.8-1.045,4.052-.1,5.028,2.112l3.9,8.943,22.6,51.659a4.684,4.684,0,0,1-1.538,5.9L44.541,101.86c-1.8,1.056-4.064.115-5.028-2.1L16.382,46.952a4.684,4.684,0,0,1,1.527-5.9L48.835,23.189,47.2,19.516a4.684,4.684,0,0,1,1.515-5.889Z" transform="translate(-15.333 0.001)" fill="url(#linear-gradient-8)"/>
        </g>
        <g id="绿" transform="translate(21.242 11.481)">
          <g transform="matrix(1, 0, 0, 1, -24.95, -18.47)" filter="url(#路径_15)">
            <path id="路径_15-2" data-name="路径 15" d="M19.074,47.206a3.5,3.5,0,0,1,0-.471,3.224,3.224,0,0,1,0-.333,2.687,2.687,0,0,1,.1-.528c.034-.172,0-.184.057-.276a3.352,3.352,0,0,1,.161-.436,1.046,1.046,0,0,1,.115-.333,4.649,4.649,0,0,1,.241-.4l.161-.287a2.7,2.7,0,0,1,.344-.379,1.4,1.4,0,0,1,.172-.2,2.927,2.927,0,0,1,.608-.459L77.632,10.436A2.927,2.927,0,0,1,78.906,10l-.677.069a2.858,2.858,0,0,0-1.228.39L20.406,43.142a3.938,3.938,0,0,0-.62.459l-.172.2c-.115.126-.23.253-.333.39a2.01,2.01,0,0,0-.161.276,3.989,3.989,0,0,0-.241.413c-.069.149-.08.218-.126.321a4.107,4.107,0,0,0-.149.413,2.123,2.123,0,0,0,0,.264,4.075,4.075,0,0,0-.1.54c0,.1,0,.218,0,.321v.494a5.9,5.9,0,0,0,.069.631,2.881,2.881,0,0,0,0,.31,4.592,4.592,0,0,0,.276.93l19.4,48.617a3.777,3.777,0,0,0,3.685,2.64h.677a3.788,3.788,0,0,1-3.674-2.64L19.476,49.031a5.74,5.74,0,0,1-.276-.93v-.31q-.077-.289-.126-.585Z" transform="translate(6.45 8.47)" fill="#b1daff"/>
          </g>
          <path id="路径_16" data-name="路径 16" d="M77.6,10.412c1.791-1.045,3.995,0,4.9,2.227L102.02,61.222c.907,2.3.184,4.971-1.607,6L43.76,99.9c-1.791,1.033-3.995,0-4.9-2.3L19.4,49.03c-.907-2.3-.184-4.959,1.607-5.992Z" transform="translate(-18.425 -9.999)" fill="#f1f7fe"/>
        </g>
        <g id="红1" transform="translate(22.964 12.63)">
          <g transform="matrix(1, 0, 0, 1, -26.67, -19.62)" filter="url(#路径_17)">
            <path id="路径_17-2" data-name="路径 17" d="M20.643,48.018a5.74,5.74,0,0,1,0-.769h0a4.339,4.339,0,0,1,.149-.769,5.04,5.04,0,0,1,.241-.7.643.643,0,0,1,0-.138,4.271,4.271,0,0,1,.436-.735,3.57,3.57,0,0,1,.333-.379,1.4,1.4,0,0,1,.172-.2,3.329,3.329,0,0,1,.608-.448L78.833,11.432a2.9,2.9,0,0,1,.482-.207l.2-.08a2.973,2.973,0,0,1,.608-.115h-.677a2.939,2.939,0,0,0-.62.115l-.2.08a1.561,1.561,0,0,0-.321.126l-.161.08L21.894,43.885a3.329,3.329,0,0,0-.608.448l-.172.2a3.135,3.135,0,0,0-.31.356h0a5.292,5.292,0,0,0-.436.746.872.872,0,0,1,0,.126,5.04,5.04,0,0,0-.241.7h0a4.856,4.856,0,0,0-.126.712h0v.758a5.74,5.74,0,0,0,.069.62,2.984,2.984,0,0,0,0,.3,6.44,6.44,0,0,0,.264.918L38.494,96.428a3.731,3.731,0,0,0,3.57,2.64h.689a3.731,3.731,0,0,1-3.57-2.64L21.01,49.843a5.108,5.108,0,0,1-.264-.907,2.846,2.846,0,0,1,0-.31,3.662,3.662,0,0,1-.1-.608Z" transform="translate(6.67 8.62)" fill="url(#linear-gradient-9)"/>
          </g>
          <path id="路径_18" data-name="路径 18" d="M78.78,11.4c1.791-1.033,3.972,0,4.867,2.3L101.82,60.179a4.971,4.971,0,0,1-1.653,6L43.916,98.624c-1.791,1.045-3.972,0-4.856-2.3L20.934,49.847a4.925,4.925,0,0,1,1.642-5.992Z" transform="translate(-19.924 -11.001)" fill="#ffc9a0"/>
        </g>
        <g id="红2" transform="translate(24.333 13.812)">
          <g transform="matrix(1, 0, 0, 1, -28.04, -20.8)" filter="url(#路径_19)">
            <path id="路径_19-2" data-name="路径 19" d="M21.844,48.959v-.574a1.619,1.619,0,0,0,0-.23,6.405,6.405,0,0,1,.149-.792,5.407,5.407,0,0,1,.367-.93,2.433,2.433,0,0,1,.138-.23,3.674,3.674,0,0,1,.413-.6,1.963,1.963,0,0,1,.207-.23,2.055,2.055,0,0,1,.287-.253,4.041,4.041,0,0,1,.425-.31l56.1-32.384a3,3,0,0,1,1.263-.39h-.689a3.122,3.122,0,0,0-1.263.4L23.164,44.872l-.23.138c-.069,0-.126.115-.2.172l-.287.253-.1.08-.092.149a4.224,4.224,0,0,0-.425.585l-.138.241a4.592,4.592,0,0,0-.356.93h0a4.442,4.442,0,0,0-.138.781,1.458,1.458,0,0,0,0,.23,3.444,3.444,0,0,0,0,.482V49a1.907,1.907,0,0,0,.069.62c.069.207,0,.207,0,.31a5.223,5.223,0,0,0,.264.918L39.9,97.369a3.742,3.742,0,0,0,3.616,2.64h.689a3.765,3.765,0,0,1-3.628-2.652l-18.368-46.5a7.129,7.129,0,0,1-.276-.93v-.31a5.739,5.739,0,0,1-.092-.654Z" transform="translate(6.85 8.77)" fill="url(#linear-gradient-10)"/>
          </g>
          <path id="路径_20" data-name="路径 20" d="M79.852,12.432c1.791-1.033,3.983,0,4.879,2.3L103.1,61.243a4.948,4.948,0,0,1-1.63,5.992L45.413,99.574c-1.8,1.033-3.983,0-4.89-2.3l-18.368-46.5a4.925,4.925,0,0,1,1.63-6Z" transform="translate(-21.113 -12.031)" fill="url(#linear-gradient-11)"/>
        </g>
        <g id="红3" transform="translate(26.419 14.902)">
          <g transform="matrix(1, 0, 0, 1, -30.13, -21.89)" filter="url(#路径_21)">
            <path id="路径_21-2" data-name="路径 21" d="M23.527,49.862h0V48.91a4.707,4.707,0,0,1,.115-.551,2.584,2.584,0,0,1,.057-.253c.069-.2.138-.39.218-.574l.057-.138a4.191,4.191,0,0,1,.367-.62,1.688,1.688,0,0,1,.115-.149,4.041,4.041,0,0,1,.356-.4l.149-.149a3.443,3.443,0,0,1,.563-.413L81.407,13.38a3.249,3.249,0,0,1,.62-.264h.149A3,3,0,0,1,82.808,13h-.7a3.444,3.444,0,0,0-.482,0h-.3a2.87,2.87,0,0,0-.62.264L24.893,45.626a3.215,3.215,0,0,0-.563.413l-.184.138c-.126.126-.241.264-.356.4l-.115.161a4.97,4.97,0,0,0-.356.585h0v.138a4.982,4.982,0,0,0-.23.574c0,.08,0,.172,0,.253a1.854,1.854,0,0,1-.08.287v1.217h0a1.706,1.706,0,0,0,.069.585,2.446,2.446,0,0,0,0,.287,5.636,5.636,0,0,0,.241.861L40.826,99.156a4.431,4.431,0,0,0,1.412,1.975,3.3,3.3,0,0,0,.941.517,2.826,2.826,0,0,0,.517.126,3.906,3.906,0,0,0,.517,0h.7a3.662,3.662,0,0,1-3.444-2.652l-17.6-47.515a3.949,3.949,0,0,1-.241-.872,2.446,2.446,0,0,1,0-.287,5.086,5.086,0,0,1-.1-.585Z" transform="translate(7.12 8.91)" fill="url(#linear-gradient-12)"/>
          </g>
          <path id="路径_22" data-name="路径 22" d="M81.345,13.382c1.791-1.033,3.926,0,4.776,2.239l17.667,47.537a5.028,5.028,0,0,1-1.722,6L46.251,101.4c-1.791,1.033-3.938,0-4.787-2.3L23.809,51.609a5.04,5.04,0,0,1,1.722-6Z" transform="translate(-22.948 -12.98)" fill="url(#linear-gradient-13)"/>
        </g>
        <g id="面" transform="translate(30.03 14.881)">
          <g transform="matrix(1, 0, 0, 1, -33.74, -21.87)" filter="url(#路径_23)">
            <path id="路径_23-2" data-name="路径 23" d="M26.758,52.34a3.076,3.076,0,0,1,0-.563V51.4a2.5,2.5,0,0,1,.092-.459c.034-.149.057-.276.092-.4a3.938,3.938,0,0,1,.218-.551,1.366,1.366,0,0,1,.126-.3,1.148,1.148,0,0,1,.115-.207,4.466,4.466,0,0,1,.287-.459c0-.08.115-.149.172-.23a3.536,3.536,0,0,1,.344-.379l.149-.138a3.937,3.937,0,0,1,.563-.413L88.668,13.423l.184-.092a3.019,3.019,0,0,1,.4-.172h.161a2.629,2.629,0,0,1,.413-.08h.115a3.065,3.065,0,0,1,.459,0l-.723-.057a2.147,2.147,0,0,0-.471,0h-.092l-.413.08h-.161a2.8,2.8,0,0,0-.39.161h-.184L28.273,47.794a3.375,3.375,0,0,0-.574.425l-.149.126a2.125,2.125,0,0,1-.161.161l-.172.218-.184.23c-.1.149-.2.3-.287.459l-.115.207a2.3,2.3,0,0,0-.1.253c-.069.184-.149.367-.207.551v.092a.6.6,0,0,1-.057.31,1.693,1.693,0,0,0-.1.459.413.413,0,0,1,0,.115v.987a3.628,3.628,0,0,0,0,.367v.264a3.983,3.983,0,0,0,.184.815l15.172,47.542a3.536,3.536,0,0,0,2.893,2.686h.723a3.513,3.513,0,0,1-2.916-2.583L27,53.935a6.279,6.279,0,0,1-.184-.8,2.054,2.054,0,0,1,0-.264,4.076,4.076,0,0,1-.057-.528Z" transform="translate(7.58 8.91)" fill="url(#linear-gradient-14)"/>
          </g>
          <path id="路径_24" data-name="路径 24" d="M88.653,13.373c1.825-1.056,3.892,0,4.592,2.3l15.119,47.526a5.372,5.372,0,0,1-1.986,6.061L46.683,103.7c-1.814,1.056-3.892,0-4.592-2.3L26.927,53.942A5.327,5.327,0,0,1,28.9,47.869Z" transform="translate(-26.084 -12.962)" fill="url(#linear-gradient-15)"/>
        </g>
        <g id="环" transform="translate(19.741 8.448)">
          <path id="路径_25" data-name="路径 25" d="M37.5,12.817a1.906,1.906,0,0,0,.138-.356l.069-.184a3.053,3.053,0,0,0,.08-.448.494.494,0,0,0,0-.126h0a3.283,3.283,0,0,0,0-.746v-.172a3.18,3.18,0,0,0-1.045-1.86,2.1,2.1,0,0,0-1.906-.54l-.643.149a2.48,2.48,0,0,1,2.686,1.607,3.18,3.18,0,0,1,.23.758v.184a4.109,4.109,0,0,1,0,.746.574.574,0,0,0,0,.138.886.886,0,0,1-.08.448l-.069.2a3.112,3.112,0,0,1-.149.344.176.176,0,0,1-.092.184,2.169,2.169,0,0,1-.23.31l-.115.138a2.089,2.089,0,0,1-.436.333l-14.8,8.564a1.906,1.906,0,0,1-.551.218l.654-.138a1.974,1.974,0,0,0,.54-.218l14.843-8.564a1.745,1.745,0,0,0,.425-.344.666.666,0,0,0,.115-.138,2.939,2.939,0,0,0,.23-.3Z" transform="translate(-16.69 -7.213)" fill="#092ca3"/>
          <path id="路径_26" data-name="路径 26" d="M17.844,20.152V19a5.835,5.835,0,0,1,.115-.62,2.583,2.583,0,0,0,.092-.276,3.731,3.731,0,0,1,.207-.494l.138-.253a2.95,2.95,0,0,1,.321-.425,1.32,1.32,0,0,1,.161-.2,2.916,2.916,0,0,1,.6-.471L34.317,7.708a2.48,2.48,0,0,1,.758-.3l-.643.138a2.881,2.881,0,0,0-.769.3L18.831,16.41a3.123,3.123,0,0,0-.608.482l-.161.184a2.985,2.985,0,0,0-.31.436,1.779,1.779,0,0,0-.138.253,3.18,3.18,0,0,0-.207.494,1.78,1.78,0,0,0-.092.264,3.823,3.823,0,0,0-.115.631v.413a4.236,4.236,0,0,0,0,.54,1.561,1.561,0,0,0,0,.23,3.892,3.892,0,0,0,.092.505,1.86,1.86,0,0,0,0,.218q.1.344.241.689a3.5,3.5,0,0,0,3.765,2.3l.643-.149a3.444,3.444,0,0,1-3.754-2.3,5.1,5.1,0,0,1-.253-.7v-.218C17.936,20.6,17.867,20.313,17.844,20.152Z" transform="translate(-17.193 -7.35)" fill="#2957d2"/>
          <path id="路径_27" data-name="路径 27" d="M34.249,7.716c1.584-.918,3.616,0,4.523,1.952a4.179,4.179,0,0,1-1.205,5.315L22.643,23.524c-1.584.918-3.616,0-4.523-1.952a4.179,4.179,0,0,1,1.24-5.292Zm2.813,6.211a2.973,2.973,0,0,0,.884-3.777,2.3,2.3,0,0,0-3.237-1.4L19.877,17.279a2.962,2.962,0,0,0-.884,3.777,2.3,2.3,0,0,0,3.226,1.4l14.843-8.564" transform="translate(-17.125 -7.358)" fill="url(#linear-gradient-16)"/>
        </g>
      </g>
      <g data-type="innerShadowGroup">
        <g transform="matrix(1, 0, 0, 1, -3.71, -6.99)" filter="url(#路径_32)">
          <path id="路径_32-4" data-name="路径 32" d="M-8.5-339.475l-.281-.5,30.231-16.961a.287.287,0,0,1,.246-.017.287.287,0,0,1,.168.18l2.738,8.659,14.655-8.7-2.33-7.516a.287.287,0,0,1,.126-.331l20.484-12.3.3.492L37.544-364.289l2.331,7.517a.287.287,0,0,1-.128.332l-15.161,9a.287.287,0,0,1-.249.021.287.287,0,0,1-.171-.182l-2.741-8.668Z" transform="translate(48.33 418.64)" fill="url(#linear-gradient-17)"/>
        </g>
        <g transform="matrix(1, 0, 0, 1, -3.71, -6.99)" filter="url(#路径_32-2)">
          <path id="路径_32-5" data-name="路径 32" d="M-8.5-339.475l-.281-.5,30.231-16.961a.287.287,0,0,1,.246-.017.287.287,0,0,1,.168.18l2.738,8.659,14.655-8.7-2.33-7.516a.287.287,0,0,1,.126-.331l20.484-12.3.3.492L37.544-364.289l2.331,7.517a.287.287,0,0,1-.128.332l-15.161,9a.287.287,0,0,1-.249.021.287.287,0,0,1-.171-.182l-2.741-8.668Z" transform="translate(48.33 418.64)" fill="#fff"/>
        </g>
      </g>
      <g transform="matrix(1, 0, 0, 1, -3.71, -6.99)" filter="url(#路径_32-3)">
        <path id="路径_32-6" data-name="路径 32" d="M-8.5-339.475l-.281-.5,30.231-16.961a.287.287,0,0,1,.246-.017.287.287,0,0,1,.168.18l2.738,8.659,14.655-8.7-2.33-7.516a.287.287,0,0,1,.126-.331l20.484-12.3.3.492L37.544-364.289l2.331,7.517a.287.287,0,0,1-.128.332l-15.161,9a.287.287,0,0,1-.249.021.287.287,0,0,1-.171-.182l-2.741-8.668Z" transform="translate(55.36 440.74)" fill="url(#linear-gradient-18)"/>
      </g>
    </g>
  </g>
</svg>
