/**
 * @Author: 宋计民
 * @Date: 2023/4/27
 * @Version: 1.0
 * @Content:
 */
import SimDragItemCom from './src/dragItem.vue';
import { withInstall } from 'isim-ui';

const SimDragItem = withInstall(SimDragItemCom);
// import { WithInstallCom } from 'isim-ui';
// const SimDragItem = SimDragItemCom as WithInstallCom<typeof SimDragItemCom>;
//
// SimDragItem.install = function (Vue) {
//   Vue.component('SimDragItem', SimDragItem);
// };

export { SimDragItem };
