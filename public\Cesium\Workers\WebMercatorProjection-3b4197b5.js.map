{"version": 3, "file": "WebMercatorProjection-3b4197b5.js", "sources": ["../../../../Source/Core/WebMercatorProjection.js"], "sourcesContent": ["import Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport Ellipsoid from \"./Ellipsoid.js\";\nimport CesiumMath from \"./Math.js\";\n\n/**\n * The map projection used by Google Maps, Bing Maps, and most of ArcGIS Online, EPSG:3857.  This\n * projection use longitude and latitude expressed with the WGS84 and transforms them to Mercator using\n * the spherical (rather than ellipsoidal) equations.\n *\n * @alias WebMercatorProjection\n * @constructor\n *\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid.\n *\n * @see GeographicProjection\n */\nfunction WebMercatorProjection(ellipsoid) {\n  this._ellipsoid = defaultValue(ellipsoid, Ellipsoid.WGS84);\n  this._semimajorAxis = this._ellipsoid.maximumRadius;\n  this._oneOverSemimajorAxis = 1.0 / this._semimajorAxis;\n}\n\nObject.defineProperties(WebMercatorProjection.prototype, {\n  /**\n   * Gets the {@link Ellipsoid}.\n   *\n   * @memberof WebMercatorProjection.prototype\n   *\n   * @type {Ellipsoid}\n   * @readonly\n   */\n  ellipsoid: {\n    get: function () {\n      return this._ellipsoid;\n    },\n  },\n});\n\n/**\n * Converts a Mercator angle, in the range -PI to PI, to a geodetic latitude\n * in the range -PI/2 to PI/2.\n *\n * @param {Number} mercatorAngle The angle to convert.\n * @returns {Number} The geodetic latitude in radians.\n */\nWebMercatorProjection.mercatorAngleToGeodeticLatitude = function (\n  mercatorAngle\n) {\n  return CesiumMath.PI_OVER_TWO - 2.0 * Math.atan(Math.exp(-mercatorAngle));\n};\n\n/**\n * Converts a geodetic latitude in radians, in the range -PI/2 to PI/2, to a Mercator\n * angle in the range -PI to PI.\n *\n * @param {Number} latitude The geodetic latitude in radians.\n * @returns {Number} The Mercator angle.\n */\nWebMercatorProjection.geodeticLatitudeToMercatorAngle = function (latitude) {\n  // Clamp the latitude coordinate to the valid Mercator bounds.\n  if (latitude > WebMercatorProjection.MaximumLatitude) {\n    latitude = WebMercatorProjection.MaximumLatitude;\n  } else if (latitude < -WebMercatorProjection.MaximumLatitude) {\n    latitude = -WebMercatorProjection.MaximumLatitude;\n  }\n  const sinLatitude = Math.sin(latitude);\n  return 0.5 * Math.log((1.0 + sinLatitude) / (1.0 - sinLatitude));\n};\n\n/**\n * The maximum latitude (both North and South) supported by a Web Mercator\n * (EPSG:3857) projection.  Technically, the Mercator projection is defined\n * for any latitude up to (but not including) 90 degrees, but it makes sense\n * to cut it off sooner because it grows exponentially with increasing latitude.\n * The logic behind this particular cutoff value, which is the one used by\n * Google Maps, Bing Maps, and Esri, is that it makes the projection\n * square.  That is, the rectangle is equal in the X and Y directions.\n *\n * The constant value is computed by calling:\n *    WebMercatorProjection.mercatorAngleToGeodeticLatitude(Math.PI)\n *\n * @type {Number}\n */\nWebMercatorProjection.MaximumLatitude = WebMercatorProjection.mercatorAngleToGeodeticLatitude(\n  Math.PI\n);\n\n/**\n * Converts geodetic ellipsoid coordinates, in radians, to the equivalent Web Mercator\n * X, Y, Z coordinates expressed in meters and returned in a {@link Cartesian3}.  The height\n * is copied unmodified to the Z coordinate.\n *\n * @param {Cartographic} cartographic The cartographic coordinates in radians.\n * @param {Cartesian3} [result] The instance to which to copy the result, or undefined if a\n *        new instance should be created.\n * @returns {Cartesian3} The equivalent web mercator X, Y, Z coordinates, in meters.\n */\nWebMercatorProjection.prototype.project = function (cartographic, result) {\n  const semimajorAxis = this._semimajorAxis;\n  const x = cartographic.longitude * semimajorAxis;\n  const y =\n    WebMercatorProjection.geodeticLatitudeToMercatorAngle(\n      cartographic.latitude\n    ) * semimajorAxis;\n  const z = cartographic.height;\n\n  if (!defined(result)) {\n    return new Cartesian3(x, y, z);\n  }\n\n  result.x = x;\n  result.y = y;\n  result.z = z;\n  return result;\n};\n\n/**\n * Converts Web Mercator X, Y coordinates, expressed in meters, to a {@link Cartographic}\n * containing geodetic ellipsoid coordinates.  The Z coordinate is copied unmodified to the\n * height.\n *\n * @param {Cartesian3} cartesian The web mercator Cartesian position to unrproject with height (z) in meters.\n * @param {Cartographic} [result] The instance to which to copy the result, or undefined if a\n *        new instance should be created.\n * @returns {Cartographic} The equivalent cartographic coordinates.\n */\nWebMercatorProjection.prototype.unproject = function (cartesian, result) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(cartesian)) {\n    throw new DeveloperError(\"cartesian is required\");\n  }\n  //>>includeEnd('debug');\n\n  const oneOverEarthSemimajorAxis = this._oneOverSemimajorAxis;\n  const longitude = cartesian.x * oneOverEarthSemimajorAxis;\n  const latitude = WebMercatorProjection.mercatorAngleToGeodeticLatitude(\n    cartesian.y * oneOverEarthSemimajorAxis\n  );\n  const height = cartesian.z;\n\n  if (!defined(result)) {\n    return new Cartographic(longitude, latitude, height);\n  }\n\n  result.longitude = longitude;\n  result.latitude = latitude;\n  result.height = height;\n  return result;\n};\nexport default WebMercatorProjection;\n"], "names": ["WebMercatorProjection", "ellipsoid", "this", "_ellipsoid", "defaultValue", "Ellipsoid", "WGS84", "_semimajorAxis", "maximumRadius", "_oneOverSemimajorAxis", "Object", "defineProperties", "prototype", "get", "mercatorAngleToGeodeticLatitude", "mercatorAngle", "CesiumMath", "PI_OVER_TWO", "Math", "atan", "exp", "geodeticLatitudeToMercatorAngle", "latitude", "MaximumLatitude", "sinLatitude", "sin", "log", "PI", "project", "cartographic", "result", "semimajor<PERSON>xis", "x", "longitude", "y", "z", "height", "defined", "Cartesian3", "unproject", "cartesian", "DeveloperError", "oneOverEarthSemimajorAxis", "Cartographic"], "mappings": "qJAoBA,SAASA,EAAsBC,GAC7BC,KAAKC,WAAaC,EAAYA,aAACH,EAAWI,EAASA,UAACC,OACpDJ,KAAKK,eAAiBL,KAAKC,WAAWK,cACtCN,KAAKO,sBAAwB,EAAMP,KAAKK,cAC1C,CAEAG,OAAOC,iBAAiBX,EAAsBY,UAAW,CASvDX,UAAW,CACTY,IAAK,WACH,OAAOX,KAAKC,UACb,KAWLH,EAAsBc,gCAAkC,SACtDC,GAEA,OAAOC,EAAUA,WAACC,YAAc,EAAMC,KAAKC,KAAKD,KAAKE,KAAKL,GAC5D,EASAf,EAAsBqB,gCAAkC,SAAUC,GAE5DA,EAAWtB,EAAsBuB,gBACnCD,EAAWtB,EAAsBuB,gBACxBD,GAAYtB,EAAsBuB,kBAC3CD,GAAYtB,EAAsBuB,iBAEpC,MAAMC,EAAcN,KAAKO,IAAIH,GAC7B,MAAO,GAAMJ,KAAKQ,KAAK,EAAMF,IAAgB,EAAMA,GACrD,EAgBAxB,EAAsBuB,gBAAkBvB,EAAsBc,gCAC5DI,KAAKS,IAaP3B,EAAsBY,UAAUgB,QAAU,SAAUC,EAAcC,GAChE,MAAMC,EAAgB7B,KAAKK,eACrByB,EAAIH,EAAaI,UAAYF,EAC7BG,EACJlC,EAAsBqB,gCACpBQ,EAAaP,UACXS,EACAI,EAAIN,EAAaO,OAEvB,OAAKC,EAAAA,QAAQP,IAIbA,EAAOE,EAAIA,EACXF,EAAOI,EAAIA,EACXJ,EAAOK,EAAIA,EACJL,GANE,IAAIQ,EAAAA,WAAWN,EAAGE,EAAGC,EAOhC,EAYAnC,EAAsBY,UAAU2B,UAAY,SAAUC,EAAWV,GAE/D,IAAKO,EAAAA,QAAQG,GACX,MAAM,IAAIC,EAAAA,eAAe,yBAI3B,MAAMC,EAA4BxC,KAAKO,sBACjCwB,EAAYO,EAAUR,EAAIU,EAC1BpB,EAAWtB,EAAsBc,gCACrC0B,EAAUN,EAAIQ,GAEVN,EAASI,EAAUL,EAEzB,OAAKE,EAAAA,QAAQP,IAIbA,EAAOG,UAAYA,EACnBH,EAAOR,SAAWA,EAClBQ,EAAOM,OAASA,EACTN,GANE,IAAIa,EAAAA,aAAaV,EAAWX,EAAUc,EAOjD"}