// @ts-nocheck
import { BlendingState, CullFace, defaultValue, RenderState, Cartesian3, Matrix4, VertexFormat, Color, PrimitiveType, SceneMode } from 'cesium';
import * as Cesium from 'cesium';
import vs from './shader/JammingRadarVS.glsl?raw';
import fs from './shader/JammingRadarFS.glsl?raw';

export class JammingRadarPrimitive {
  show: Boolean;
  _pitchList: number[];
  pitchList: number[];
  _horizontalList: number[];
  horizontalList: number[];
  _radiusList: number[];
  radiusList: number[];
  color: Color;
  lineColor: Color;

  _modelMatrix;
  modelMatrix;
  uniforms;
  vertexArray;
  shaderProgram;
  renderState;
  command;

  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    this.show = defaultValue(options.show, true);
    this.lineShow = defaultValue(options.lineShow, true);
    // 俯仰角
    this.pitchList = defaultValue(options.pitchList, []);
    // 水平角
    this.horizontalList = defaultValue(options.horizontalList, []);
    // 半径
    this.radiusList = defaultValue(options.radiusList, []);

    this.color = defaultValue(options.color, new Color(1, 0, 0, 0.3));
    this.lineColor = defaultValue(options.lineColor ?? options.color, new Color(1, 1, 0, 1));
  }

  // 创建顶点数组
  createVertexArray(context) {
    const { pitchList, horizontalList, radiusList } = this;
    const geometry = JammingRadarPrimitive.createGeomtry(pitchList, horizontalList, radiusList);
    // const geometry = Cesium.EllipsoidGeometry.createGeometry(
    //   new Cesium.EllipsoidGeometry({
    //     vertexFormat: VertexFormat.POSITION_ONLY,
    //     stackPartitions: 32 * 30,
    //     slicePartitions: 32 * 30,
    //   })
    // );
    this.geometry = geometry;
    this.vertexArray = Cesium.VertexArray.fromGeometry({
      context: context,
      geometry: geometry,
      attributeLocations: Cesium.GeometryPipeline.createAttributeLocations(geometry)
    });
  }

  // 创建shader渲染程序
  createShaderProgram(context) {
    this.shaderProgram = Cesium.ShaderProgram.fromCache({
      context: context,
      vertexShaderSource: vs,
      fragmentShaderSource: fs,
      attributeLocations: {
        position: 0,
        normal: 1
      }
    });
  }

  // 创建渲染状态
  createRenderState() {
    if (this.renderState) return;
    // 正面
    this.renderState = Cesium.RenderState.fromCache({
      depthTest: {
        enabled: false
      },
      depthMask: true,
      blending: BlendingState.ALPHA_BLEND,
      cull: {
        enabled: true,
        face: CullFace.BACK
      }
    });
    // 背面
    this.backFaceRS = RenderState.fromCache({
      depthTest: {
        enabled: false
      },
      depthMask: false,
      blending: BlendingState.ALPHA_BLEND,
      cull: {
        enabled: true,
        face: CullFace.FRONT
      }
    });
  }

  // 创建绘制命令
  createCommand(primitive) {
    // const pass = Cesium.Pass.ENVIRONMENT;
    const pass = Cesium.Pass.OPAQUE;
    // console.log('primitive.geometry', primitive.geometry.boundingSphere)
    if (!primitive.command) {
      const options = {
        owner: primitive,
        pass,
        // primitiveType: PrimitiveType.TRIANGLES,
        // boundingVolume: new BoundingSphere(),
        uniformMap: {
          color: () => primitive.color
        }
      };
      primitive.command = new Cesium.DrawCommand(options);
      primitive.backCommand = new Cesium.DrawCommand(options);
      primitive.lineCommand = new Cesium.DrawCommand({
        ...options,
        primitiveType: PrimitiveType.LINES,
        uniformMap: {
          color: () => primitive.lineColor
        }
      });
    }
    primitive.command.modelMatrix = primitive.modelMatrix;
    primitive.command.vertexArray = primitive.vertexArray;
    primitive.command.shaderProgram = primitive.shaderProgram;
    primitive.command.renderState = primitive.renderState;

    primitive.backCommand.modelMatrix = primitive.modelMatrix;
    primitive.backCommand.vertexArray = primitive.vertexArray;
    primitive.backCommand.shaderProgram = primitive.shaderProgram;
    primitive.backCommand.renderState = primitive.backFaceRS;
    // primitive.command.modelMatrix = primitive.modelMatrix

    primitive.lineCommand.modelMatrix = primitive.modelMatrix;
    primitive.lineCommand.vertexArray = primitive.vertexArray;
    primitive.lineCommand.shaderProgram = primitive.shaderProgram;
    primitive.lineCommand.renderState = primitive.renderState;
  }

  update(frameState) {
    if (!this.show) return;
    const { context, passes, mode } = frameState;
    // if (mode === SceneMode.SCENE2D) return

    if (
      this.pitchList !== this._pitchList ||
      this.horizontalList !== this._horizontalList ||
      this.radiusList !== this._radiusList ||
      this.modelMatrix !== this._modelMatrix
    ) {
      this._pitchList = this.pitchList;
      this._horizontalList = this.horizontalList;
      this._radiusList = this.radiusList;
      this._modelMatrix = this.modelMatrix;

      this.createVertexArray(context);
      this.createShaderProgram(context);
      this.createRenderState(context);
      this.createCommand(this);
    }
    // pick
    let pickId = this.pickId;
    if (!pickId) {
      pickId = context.createPickId({
        primitive: this,
        id: this.id,
        type: 'JammingRadar'
      });
      this.pickId = pickId;
    }

    const strPickId = 'czm_pickColor';
    const czm_pickColor = pickId.color;
    this.command.uniformMap[strPickId] = () => czm_pickColor;
    this.command.pickId = strPickId;
    this.backCommand.uniformMap[strPickId] = () => czm_pickColor;
    this.backCommand.pickId = strPickId;
    this.lineCommand.uniformMap[strPickId] = () => czm_pickColor;
    this.lineCommand.pickId = strPickId;
    // 添加正反面两个绘制命令解决展示不全问题
    frameState.commandList.push(this.command);
    frameState.commandList.push(this.backCommand);

    if (this.lineShow) {
      frameState.commandList.push(this.lineCommand);
    }
  }

  isDestroyed() {
    return false;
  }

  destroy() {
    this.pickId?.destroy(); // 销毁pickId
  }

  static createGeomtry(pitchList, horizontalList, radiusList) {
    if (pitchList.length * horizontalList.length !== radiusList.length) {
      console.error('俯仰角数组长度 * 水平角数组长度 不等于 半径数组长度');
    }
    let attrs;
    if (pitchList.length <= 1) {
      attrs = generateVertics_2D(pitchList, horizontalList, radiusList);
    } else {
      attrs = generateVertics(pitchList, horizontalList, radiusList);
    }
    // console.log('attrs', attrs)
    const positions = new Float32Array(attrs.vertices);
    const indices = new Uint16Array(attrs.indices);
    const uvs = new Uint16Array(attrs.uv);
    let geometry = new Cesium.Geometry({
      attributes: {
        position: new Cesium.GeometryAttribute({
          componentDatatype: Cesium.ComponentDatatype.DOUBLE,
          componentsPerAttribute: 3,
          values: positions
        }),
        st: new Cesium.GeometryAttribute({
          componentDatatype: Cesium.ComponentDatatype.FLOAT,
          componentsPerAttribute: 2,
          values: uvs
        })
      },
      indices: indices,
      primitiveType: Cesium.PrimitiveType.TRIANGLES, //三角形
      boundingSphere: Cesium.BoundingSphere.fromVertices(positions)
    });
    // console.log('geometry', geometry);
    geometry = Cesium.GeometryPipeline.computeNormal(geometry);
    return geometry;
  }
}

// 计算顶点
function generateVertics(pitchList, horizontalList, radiusList) {
  const vertices = [];
  const indices = [];
  const uv = [];
  const pitchListLength = pitchList.length;
  const horizontalListLength = horizontalList.length;
  const radiusListLength = radiusList.length;

  pitchList.forEach((pitch: number, pitchIndex: number) => {
    const u = pitchIndex / pitchListLength;
    horizontalList.forEach((horizontal: number, horizontalIndex: number) => {
      const v = horizontalIndex / horizontalListLength;
      const i = pitchIndex * horizontalListLength + horizontalIndex;
      const obj = { pitch, horizontal, radius: radiusList[i] };
      const point = calculateVertexCoordinates(pitch, horizontal);
      vertices.push(point.x * radiusList[i], point.y * radiusList[i], point.z * radiusList[i]);
      uv.push(u, v);

      if (pitchIndex >= pitchListLength - 1 || horizontalIndex >= horizontalListLength - 1) return;
      const left_top = pitchIndex * horizontalListLength + horizontalIndex;
      const left_bottom = left_top + 1;
      const right_top = (pitchIndex + 1) * horizontalListLength + horizontalIndex;
      const right_bottom = right_top + 1;
      indices.push(left_top, left_bottom, right_top);
      indices.push(left_bottom, right_bottom, right_top);
    });
  });

  // for (let i = 0; i < radiusListLength; i++) {
  //   const pitchIndex = Math.floor(i / horizontalListLength)
  //   const horizontalIndex = i % horizontalListLength
  //   const pitch = pitchList[pitchIndex]
  //   const horizontal = horizontalList[horizontalIndex]
  //   const radius = radiusList[i]
  //   const u = pitchIndex / pitchListLength
  //   const v = horizontalIndex / horizontalListLength
  //   // 获取归一化的向量
  //   const point = calculateVertexCoordinates(pitch, horizontal)
  //   vertices.push(point.x * radius, point.y * radius, point.z * radius)
  //   uv.push(u, v)
  //   // if (pitchIndex >= pitchListLength - 1 || horizontalIndex >= horizontalListLength - 1) break
  //   // const current=
  // }
  for (let pitchIndex = 0; pitchIndex < pitchListLength - 1; pitchIndex++) {
    for (let horizontalIndex = 0; horizontalIndex < horizontalListLength - 1; horizontalIndex++) {
      if (pitchIndex >= pitchListLength - 1 || horizontalIndex >= horizontalListLength - 1) break;
      const left_top = pitchIndex * horizontalListLength + horizontalIndex;
      const left_bottom = left_top + 1;
      const right_top = (pitchIndex + 1) * horizontalListLength + horizontalIndex;
      const right_bottom = right_top + 1;
      // indices.push(left_top, left_bottom, right_top)
      // indices.push(left_bottom, right_bottom, right_top,)
    }
  }

  // const vertices = [
  //   1, 1, 0,
  //   1, -1, 0,
  //   -1, -1, 0,
  //   -1, 1, 0
  // ]
  // const indices = [
  //   0, 1, 2,
  // ]
  // const uv = [
  //   0, 0,
  //   0, 1,
  //   1, 1,
  //   1, 0
  // ]
  return {
    vertices,
    indices,
    uv
  };
}

// 计算顶点2D
function generateVertics_2D(pitchList, horizontalList, radiusList) {
  const vertices = [0, 0, 0];
  const indices = [];
  const uv = [0, 0];
  const pitchListLength = pitchList.length;
  const horizontalListLength = horizontalList.length;
  const radiusListLength = radiusList.length;
  const pitch = pitchList[0];

  horizontalList.forEach((horizontal: number, horizontalIndex: number) => {
    const point = calculateVertexCoordinates(pitch, horizontal);
    vertices.push(point.x * radiusList[horizontalIndex], point.y * radiusList[horizontalIndex], point.z * radiusList[horizontalIndex]);

    const v = horizontalIndex / horizontalListLength;
    uv.push(1, v);

    if (horizontalIndex >= horizontalListLength - 1) return;
    const left = horizontalIndex + 1;
    const right = horizontalIndex + 2;
    indices.push(0, left, right);
  });

  return {
    vertices,
    indices,
    uv
  };
}

// 根据俯仰角和航向角计算坐标
function calculateVertexCoordinates(pitch: number, yaw: number) {
  // 将角度转换为弧度
  const theta = (pitch * Math.PI) / 180; // 俯仰角
  const phi = ((yaw + 90) * Math.PI) / 180; // 水平角

  // 计算坐标
  const x = Math.cos(theta) * Math.sin(phi);
  const y = Math.cos(theta) * Math.cos(phi);
  const z = Math.sin(theta);

  return new Cartesian3(x, y, z);
}
