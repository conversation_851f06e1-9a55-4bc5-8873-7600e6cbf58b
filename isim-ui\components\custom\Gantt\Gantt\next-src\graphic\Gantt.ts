import { GanttGraphicCollection } from './GanttGraphicCollection';

import { GanttOption, type GanttOptionType } from './GanttOption';
import { GanttRow } from './GanttRow';
import type { GanttDataBase } from './GanttType';
import { GanttConnectLine } from './GanttConnectLine';
import { GanttScrollBar } from './GanttScrollBar.ts';
import { GanttRangeGraphic } from './GanttRangeGraphic.ts';
import { GanttCurrentTimeLine } from './GanttCurrentTimeLine.ts';
import { GanttOnLine } from './GanttOnLine.ts';

type RequiredParams<T, U extends keyof T> = Partial<Omit<T, U>> & { [P in U]: T[P] };

const defaultOptions: Partial<GanttOptionType> = {
  rowHeight: 30,
  rowGap: 6,
  fontSize: 16,
  splitLine: false,
  connectLine: true,
  startX: 0,
  startY: 0,
  mode: 'default',
  scrollStep: 10
};

export class Gantt extends GanttOption {
  // graphicCollection = new GanttGraphicCollection<GanttGraphicAbstract>();
  graphicCollection = new GanttGraphicCollection<GanttRangeGraphic>(); // 图形集合
  rowCollection = new GanttGraphicCollection<GanttRow>(); // 节点行集合
  connectLineCollection = new GanttGraphicCollection<GanttConnectLine>(); // 任务连线集合
  selectedRowCollection = new GanttGraphicCollection<GanttRow>(); // 高亮row集合
  selectedConnectLineCollection = new GanttGraphicCollection<GanttConnectLine>(); // 选中连线集合
  online: GanttOnLine; // 连线类

  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  scrollBar: GanttScrollBar; //滚动条
  currentTimeLine: GanttCurrentTimeLine; //当前时间标线

  constructor(config: RequiredParams<GanttOptionType, 'minTime' | 'maxTime'> & { container: HTMLCanvasElement }) {
    const { container } = config;
    // @ts-ignore
    super(Object.assign({}, defaultOptions, config));
    this.canvas = container;
    // this.canvas.width = rest.width;
    // this.canvas.height = rest.height;
    this.ctx = this.canvas.getContext('2d')!;
    this.scrollBar = new GanttScrollBar(this);
    this.currentTimeLine = new GanttCurrentTimeLine(this);
    this.online = new GanttOnLine(this);
  }

  setData<T extends GanttDataBase<T>>(data: T[]) {
    const connectList: Map<string, string[]> = new Map();

    // 重置图形集合
    this.graphicCollection.removeAll();
    this.rowCollection.removeAll();

    data.forEach((item) => {
      if (this.connectLine.show && item.target?.length) {
        connectList.set(item.id, item.target);
      }
      this.addData(item);
      // this.rowCollection.add(new GanttRow(item, this));
    });

    if (this.connectLine.show) {
      this.connectLineCollection.removeAll();
      Array.from(connectList.entries()).forEach(([key, value]) => {
        const source = this.graphicCollection.getById(key)!;
        if (!source) return;
        value.forEach((item) => {
          const target = this.graphicCollection.getById(item)!;
          this.connectLineCollection.add(
            new GanttConnectLine({
              gantt: this,
              ctx: this.ctx,
              source,
              connectLine: this.connectLine,
              target: target
            })
          );
        });
      });
    }
    if (!this.scrollBar) this.scrollBar = new GanttScrollBar(this);

    // this.scrollBar.setScrollY(this.scrollBar.scrollBarStartY);
    // 重新计算滚动条位置
    this.scrollBar.calcBarStartY();
    // 解决触底收缩节点引起的渲染问题
    this.scrollBar.checkStartY();

    this.render();
  }

  zoom(start: number, end: number) {
    this.showStartTime = start;
    this.showEndTime = end;
    this.calcStartX();
    this.calculateInterval();
    this.currentTimeLine.calcLeft();
    this.render();
  }

  horizonScroll(toLeft: boolean) {
    const step = (this.showEndTime - this.showStartTime) / 100;
    if (toLeft) {
      if (this.showEndTime >= this.maxTime) return;
      this.showStartTime += step;
      this.showEndTime += step;
      // this.startX -= this.scrollStep;
      this.calcStartX();
      this.calculateInterval();
      this.currentTimeLine.calcLeft();
      this.render();
      return;
    }
    if (this.showStartTime <= this.minTime) return;
    this.showStartTime -= step;
    this.showEndTime -= step;
    this.calcStartX();
    this.calculateInterval();
    this.currentTimeLine.calcLeft();
    this.render();
  }
  verticalScroll(toTop: boolean) {
    this.scrollBar.scrollTo(toTop);
    this.render();
  }

  addData<T extends GanttDataBase>(data: T) {
    if (this.graphicCollection.has(data.id)) {
      console.warn(`${data.id} 已经存在`);
      return;
    }
    this.rowCollection.add(new GanttRow(data, this));
  }

  pickNode(x: number, y: number) {
    // 浏览器界面倍率放大缩小时，能获取相应的位置
    x *= window.devicePixelRatio;
    y *= window.devicePixelRatio;

    const values = this.graphicCollection.values();
    const rowList = values.filter((v) => v.pick(x, y)).map((v) => v.row);
    // 去重
    return [...new Set(rowList)];
  }

  pickEdge(x: number, y: number) {
    // 浏览器界面倍率放大缩小时，能获取相应的位置
    x *= window.devicePixelRatio;
    y *= window.devicePixelRatio;

    const values = this.connectLineCollection.values();
    // console.log('this.connectLineCollection.values()', values);
    const rowList = values.filter((v) => v.pick(x, y));
    return [...new Set(rowList)];
  }

  setSelectedtRow(list: GanttRow[]) {
    this.selectedRowCollection.removeAll();
    list.forEach((item) => {
      this.selectedRowCollection.add(item);
    });
    this.render();
  }
  setSelectedtRowByIdList(list: string[]) {
    const rowList = this.rowCollection.values().filter((item) => list.includes(item.id));
    this.setSelectedtRow(rowList);
  }
  addSelectedRowById(id: string) {
    const row = this.rowCollection.values().find((item) => item.id == id);
    if (!row) return;
    this.selectedRowCollection.add(row!);
  }

  setSelectedConnectLine(connectLine: GanttConnectLine) {
    this.clearSelectedConnectLine();
    this.selectedConnectLineCollection.add(connectLine);
  }
  clearSelectedConnectLine() {
    this.selectedConnectLineCollection.removeAll();
  }

  setCurrentTime(time: number) {
    this.options.currentTime = time;
    this.currentTimeLine.render();
  }

  clearRect() {
    this.ctx.clearRect(0, 0, this.width, this.height);
  }

  render() {
    this.clearRect();
    this.graphicCollection.render();
    // this.rowCollection.render();
    if (this.connectLine.show) {
      this.connectLineCollection.render();
    }
    this.scrollBar.render();
    this.currentTimeLine.render();
  }
}
