/**
 * @Author: 宋计民
 * @Date: 2023-08-24 10:37
 * @Version: 1.0
 * @Content: 地名搜索定位的实体创建
 */
import {
  clearEntitySourceBySourceName,
  createEntitySource,
  DegreesPosType,
  deleteDatasourceByName,
  labelDefaultConfig,
  onDoubleClick
} from 'isim-cesium';
import { Cartesian3, Color, VerticalOrigin } from 'cesium';

const PLACE_ENTITY_SOURCE = 'webPlaceEntitySource';
export function useCreatePlaceEntity() {
  const create = (name: string, pos: DegreesPosType) => {
    const { source } = createEntitySource(PLACE_ENTITY_SOURCE, true);
    source.entities.add({
      position: Cartesian3.fromDegrees(pos.longitude, pos.latitude, 0),
      label: {
        ...labelDefaultConfig,
        verticalOrigin: VerticalOrigin.BOTTOM,
        text: `${name}\n坐标:${pos.longitude},${pos.latitude},0`
      },
      point: {
        pixelSize: 4,
        color: Color.RED
      }
    });
  };

  const destroy = () => {
    deleteDatasourceByName(PLACE_ENTITY_SOURCE);
  };
  const clear = () => {
    clearEntitySourceBySourceName(PLACE_ENTITY_SOURCE);
  };
  const downClose = onDoubleClick(
    () => {
      destroy?.();
    },
    {
      isEmpty: true
    }
  );
  onScopeDispose(() => {
    destroy?.();
    downClose?.();
  });
  return {
    create,
    clear,
    destroy
  };
}
