import type { GanttRow } from './GanttRow';
import type { GanttDataBase } from './GanttType';
import { Gantt } from './Gantt.ts';

export class GanttGraphicBase<T extends GanttDataBase> {
  id: string;
  x: number = 0;
  y: number = 0;
  width: number = 1;
  height: number;
  row: GanttRow;
  gantt: Gantt;
  data: T;
  constructor(data: T, row: GanttRow) {
    this.id = data.id;
    this.data = data;
    this.row = row;
    this.gantt = row.gantt;
    this.calcWidth();
    this.calcY();
    this.height = row.gantt.rowHeight - row.gantt.rowGap;
  }
  // 是否在窗口内，是否需要展示
  get isShow() {
    const { height } = this.gantt;
    const y = this.y * window.devicePixelRatio;
    if (y <= -this.row.rowHeight * window.devicePixelRatio || y >= height * window.devicePixelRatio) return false;

    return true;
  }

  calcPos() {
    this.calcX();
    this.calcY();
    this.calcWidth();
  }

  pointInPath(x: number, y: number) {
    return x >= this.x && x <= this.x + this.width && y >= this.y && y <= this.y + this.height;
  }
  // 计算X轴开始位置
  calcX() {
    // this.x = (this.data.startTime - this.row.gantt.showStartTime) / this.row.gantt.interval + this.row.startX;
    const { startX, currentWidth, minTime, maxTime } = this.gantt;
    this.x = startX + (currentWidth * (this.data.startTime - minTime)) / (maxTime - minTime);
  }
  // 计算Y轴开始位置
  calcY() {
    // this.y = (this.row.rowHeight + this.row.rowGap) * this.row.index + this.row.startY + this.row.rowGap / 2;
    this.y = (this.row.rowHeight + this.row.rowGap) * this.row.index + this.row.startY;
  }
  // 计算宽度
  calcWidth() {
    // this.width = (this.data.endTime - this.data.startTime) / this.row.gantt.interval;
    const { currentWidth, minTime, maxTime } = this.gantt;
    this.width = (currentWidth * (this.data.endTime - this.data.startTime)) / (maxTime - minTime);
  }
}
