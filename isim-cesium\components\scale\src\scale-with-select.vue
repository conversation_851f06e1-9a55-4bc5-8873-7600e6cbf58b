<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : scale-with-select.vue
-->
<template>
  <el-popover v-bind="$attrs.popover">
    <template #reference>
      <sim-scale v-bind="$attrs" />
    </template>
    <div>
      <div v-for="item in scaleSelectList" :key="item.ratio" @click="handleSelect(item)">{{ item.label }}</div>
    </div>
  </el-popover>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ScaleWithSelect'
});
</script>

<script setup lang="ts">
import SimScale from './scale.vue';
import { setCameraScale } from 'isim-cesium';

interface ScaleSelectType {
  ratio: number;
  label: string;
}
const handleSelect = (data: ScaleSelectType) => {
  setCameraScale(data.ratio);
  console.log('data', data);
};
const scaleSelectList: ScaleSelectType[] = [
  {
    ratio: 1,
    label: '1:1400万' // 1400公里
  },
  {
    ratio: 4,
    label: '1:100万' // 1000公里
  },
  {
    ratio: 5,
    label: '1:50万' // 500公里
  },
  {
    ratio: 13.5,
    label: '1:25万' // 250公里
  },
  {
    ratio: 60,
    label: '1:5万' // 50公里
  },
  {
    ratio: 200,
    label: '1:1万' // 10公里
  }
];
</script>

<style scoped lang="less"></style>
