<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/1
* @Version : 1.0
* @Content : Input
-->
<template>
  <el-input ref="inputRef" v-bind="$attrs">
    <template v-for="(value, name) in $slots" :key="name" #[name]="slotData">
      <slot :name="name" v-bind="slotData" />
    </template>
  </el-input>
</template>

<script lang="ts">
export default {
  name: 'SimInput'
};
</script>
<script lang="ts" setup>
import { ElInput } from 'element-plus';

const inputRef = shallowRef<InstanceType<typeof ElInput>>();
defineExpose({ inputRef });
onUnmounted(() => {
  inputRef.value?.blur();
});
</script>
<style scoped>
.sim-input {
  padding: 0 !important;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
</style>
<style lang="less">
.sim-input {
  width: 100%;
  //height: 32px;
  --sim-block-bgc: rgba(var(--primary-color-val), 0.15);

  .el-input {
    --el-input-bg-color: transparent;
    height: 30px;
  }

  .el-input__wrapper {
    outline: none;
    box-shadow: none;
    background: transparent;

    &:hover {
      box-shadow: none;
      border: none;
    }

    & > input {
      color: white;
    }
  }

  outline: none;
  border: none;
}
</style>
