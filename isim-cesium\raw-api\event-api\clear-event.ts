import { EventConfigType, getViewer, getViewerName, onViewerCreated, onViewerDestroyed } from 'isim-cesium';

const clearEventMap: Map<string, Set<Function>> = new Map();

/**
 * 注册清除事件
 * @param fn
 * @param option
 */
export function onClearEvent(fn: Function, option?: EventConfigType) {
  onViewerCreated(
    () => {
      const viewerName = getViewerName(option?.viewerName);
      if (!clearEventMap.has(viewerName)) {
        clearEventMap.set(viewerName, new Set());
        onViewerDestroyed(() => clearEventMap.delete(viewerName), { viewerName: getViewerName(viewerName) });
      }
      clearEventMap.get(viewerName)!.add(fn);
    },
    { viewerName: option?.viewerName }
  );
  return () => clearEventMap.get(getViewerName(option?.viewerName))?.delete(fn);
}

/**
 * 执行清除事件
 * @param viewerName
 */
export function executeClearEvent(viewerName?: string) {
  clearEventMap.get(getViewerName(viewerName))?.forEach((fn) => {
    const viewer = getViewer();
    fn(viewer);
  });
}

export function removeAllClearEvent(option?: EventConfigType) {
  clearEventMap.get(getViewerName(option?.viewerName))?.clear();
}
