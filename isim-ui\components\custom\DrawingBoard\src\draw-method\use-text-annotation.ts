import { AnnotationConfig, AnnotationGraphic, AnnotationOptionsType, setAnnotations } from './annotation-graphic';
import { useAnnotationsInject } from '../use-annotations-inject';

/**
 * @Author: 宋计民
 * @Date: 2023-08-30 15:41
 * @Version: 1.0
 * @Content: use-text-annotation.ts
 */
export function useTextAnnotation(config: AnnotationConfig) {
  const { drawBoxRef, ctx, clearALlEvent, clearMouseDown } = useAnnotationsInject();
  const start = () => {
    const canvas = drawBoxRef.value;
    clearMouseDown();
    clearALlEvent();
    canvas.ondblclick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      const text = new TextAnnotation(ctx.value, { ...toRaw(config), startX: e.offsetX, startY: e.offsetY });
      text.createInput();
    };
  };
  return {
    start
  };
}

export class TextAnnotation extends AnnotationGraphic {
  color: string;
  ctx: CanvasRenderingContext2D;
  startX: number;
  startY: number;
  width: number;
  text: string;
  fontSize: number;
  constructor(ctx: CanvasRenderingContext2D, options: AnnotationOptionsType) {
    super();
    const { color = '#f00', startX, startY, width = 1 } = options;
    this.width = width;
    this.color = color;
    this.startX = startX;
    this.startY = startY;
    this.ctx = ctx;
    this.text = '';
    this.fontSize = 20;
  }

  getParentElement() {
    return document.getElementById('sim-drawing-board');
  }

  createInput() {
    const inputElement = document.createElement('input');
    inputElement.classList.add('annotation-text__input');
    inputElement.style.left = this.startX + 'px';
    inputElement.style.top = this.startY + 'px';
    inputElement.onblur = (e: FocusEvent) => {
      const target = e.target as HTMLInputElement;
      const value = target.value;
      if (!value) {
        this.getParentElement()?.removeChild(inputElement);
        return;
      }
      this.addText(value);
      setAnnotations(this);
      this.getParentElement()?.removeChild(inputElement);
      inputElement.blur();
    };
    inputElement.onkeydown = (e) => {
      if (e.key === 'Enter') {
        e.stopPropagation();
        e.preventDefault();
        inputElement.blur();
      }
    };
    this.getParentElement()?.append(inputElement);
    inputElement.focus();
  }

  setTextConfig() {
    this.ctx.font = `${this.fontSize}px Arial`;
    this.ctx.fillStyle = this.color;
  }

  addText(text: string) {
    this.setTextConfig();
    this.text = text;
    this.ctx.fillText(text, this.startX, this.startY);
  }

  draw() {
    this.setTextConfig();
    this.ctx.fillText(this.text, this.startX, this.startY);
  }
}
