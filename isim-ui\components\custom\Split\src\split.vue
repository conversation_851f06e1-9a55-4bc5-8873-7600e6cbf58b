<!--
* @Author: 宋计民
* @Date: 2024/4/23
* @Version: 1.0
* @Content: split.vue
-->
<template>
  <div class="sim-split" ref="splitRef">
    <slot>
      <component v-for="item in $slots" :is="item" :key="item" />
    </slot>
  </div>
</template>

<script setup lang="ts">
import { useSplitHook } from './use-split-hook.ts';

defineOptions({
  name: 'SimSplit'
});
const { splitRef } = useSplitHook();
</script>

<style scoped lang="less">
.sim-split {
  display: flex;
  width: 100%;
  height: 100%;
  overflow-x: auto;
}
.sim-split--horizon {
  flex-direction: column;
}
</style>
