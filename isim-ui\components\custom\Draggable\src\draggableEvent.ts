/**
 * @Author: 宋计民
 * @Date: 2022-04-06 12:09:37
 * @Version: 0.0.1
 * @Content:
 */
import { UseDragState, DraggableEmits, DraggableProps } from './draggable-hook';

import { nextTick, onMounted, ref } from 'vue';
import { updateLeft, updateTop } from './utils';
import { useZIndex } from 'element-plus';

/** 绑定拖拽修改位置
 * @param props
 * @param emit
 * @param dragHook
 * @returns
 */
export function useDraggableEventBind(props: Readonly<DraggableProps>, emit: DraggableEmits, dragHook: UseDragState) {
  const { dragRef, currentIndex, parentHtml } = dragHook;
  const dragRange = {
    xMin: 0,
    xMax: 0,
    yMin: 0,
    yMax: 0
  };
  const defaultDragRef = ref();
  const { nextZIndex } = useZIndex();
  let dragEl: HTMLElement; //  用户绑定拖拽的mousedown 事件
  const downOffset = { x: 0, y: 0, downW: 2, downH: 0 };
  const parentSize = { width: 0, height: 0 };
  const dragMoveEvent = (e: MouseEvent) => {
    e.stopPropagation();
    // e.preventDefault();
    let left = e.movementX + dragRef.value?.offsetLeft;
    if (parentHtml.value.clientWidth < dragRef.value.clientWidth) {
      left = 0;
    }
    updateLeft({
      pos: left,
      dragState: dragHook,
      props,
      opt: { min: dragRange.xMin, max: dragRange.xMax }
    });
    let top = e.movementY + dragRef.value?.offsetTop;
    if (parentHtml.value.clientHeight < dragRef.value.clientHeight) {
      top = 0;
    }
    updateTop({
      pos: top,
      dragState: dragHook,
      props,
      opt: {
        min: dragRange.yMin,
        max: dragRange.yMax
      }
    });
    emit('handle-drag-move', e, { top, left });
  };

  const dragMouseDown = (downE: MouseEvent) => {
    downE.preventDefault();
    downE.stopPropagation();
    if (!props.zIndex && props.zIndex !== 0) {
      currentIndex.value = nextZIndex();
    }
    const _xMax = parentHtml.value.clientWidth - dragEl.clientWidth;
    // 父容器的宽度小于组件本身
    dragRange.xMax = _xMax < 0 ? 0 : _xMax;
    const _yMax = parentHtml.value.clientHeight - dragRef.value.clientHeight;
    // 父容器的高度小于组件本身
    dragRange.yMax = _yMax < 0 ? 0 : _yMax;
    if (props.draggable) {
      emit('handle-drag-start', downE);
      downOffset.x = downE.clientX;
      downOffset.y = downE.clientY;
      downOffset.downW = dragRef.value.clientWidth;
      downOffset.downH = dragRef.value.clientHeight;
      parentSize.width = parentHtml.value.clientWidth - 2;
      parentSize.height = parentHtml.value.clientHeight;
      parentHtml.value.addEventListener('mousemove', dragMoveEvent);
      parentHtml.value.addEventListener('mouseup', (e: MouseEvent) => {
        parentHtml.value.removeEventListener('mousemove', dragMoveEvent);
        emit('handle-drag-end', e);
        // 吸附功能
        if (props.stick) {
          const left = e.movementX + dragRef.value?.offsetLeft;
          const top = e.movementY + dragRef.value?.offsetTop;
          if (left <= props.stickDistance) {
            updateLeft({ pos: 0, dragState: dragHook, props });
          } else if (parentSize.width - (left + downOffset.downW) <= props.stickDistance) {
            updateLeft({
              pos: parentSize.width - downOffset.downW,
              dragState: dragHook,
              props
            });
          }
          if (top <= props.stickDistance) {
            updateTop({ pos: 0, dragState: dragHook, props });
          } else if (parentSize.height - (top + downOffset.downH) <= props.stickDistance) {
            updateTop({
              pos: parentSize.height - downOffset.downH,
              dragState: dragHook,
              props
            });
          }
        }
      });
      parentHtml.value.addEventListener('mouseleave', () => {
        parentHtml.value.removeEventListener('mousemove', dragMoveEvent);
      });
    }
  };
  // 判断是否传入 draggable 若 draggable为false 则不绑定拖拽事件
  const eventBindByCondition = () => {
    if (!props.draggable) {
      return;
    }
    dragEl = defaultDragRef.value || dragRef.value;
    if (props.dragElement) {
      if (typeof props.dragElement === 'string') {
        dragEl = dragRef.value.querySelector(props.dragElement);
      } else {
        dragEl = props.dragElement;
      }
    }
    dragEl.addEventListener('mousedown', dragMouseDown);
  };
  onMounted(() => {
    if (props.draggable) {
      nextTick().then(() => {
        eventBindByCondition();
      });
    }
  });
  return {
    defaultDragRef
  };
}
