/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { Ref } from 'vue';
import { getDomBoundingRect } from './use-draggable-hook.ts';

export interface MoveCallbackParam {
  event: MouseEvent;
  resizeRect: DOMRect;
}
interface UseResizableHookOption {
  resizeDom: Ref<HTMLElement>;
  eventDom: Ref<HTMLElement>;
  vertical: boolean;
  startCallback: () => void;
  moveCallback: (data: MoveCallbackParam) => void;
}
export function useResizableHook<T extends HTMLElement>(handle: Ref<T>, option?: Partial<UseResizableHookOption>) {
  const getResizeDom = () => (option.resizeDom ? toValue(option.resizeDom) : toValue(handle));
  const getEventDom = () => (option.eventDom ? toValue(option.eventDom) : document.documentElement);
  const mousedownEvent = () => {
    const _resizeDom = getResizeDom();
    const _resizeDomRect = getDomBoundingRect(_resizeDom);
    option.startCallback?.();
    const moveEvent = (e: MouseEvent) => {
      option.moveCallback?.({ event: e, resizeRect: _resizeDomRect });
    };

    const mouseup = () => {
      getEventDom().removeEventListener('mousemove', moveEvent);
      getEventDom().removeEventListener('mouseup', mouseup);
      getEventDom().removeEventListener('mouseleave', mouseup);
    };
    getEventDom().addEventListener('mousemove', moveEvent);
    getEventDom().addEventListener('mouseup', mouseup);
    getEventDom().addEventListener('mouseleave', mouseup);
  };

  const handleBindMousedown = () => {
    toValue(handle).addEventListener('mousedown', mousedownEvent);
  };

  onMounted(handleBindMousedown);
  onBeforeUnmount(() => {
    toValue(handle).removeEventListener('mousedown', mousedownEvent);
  });
}
