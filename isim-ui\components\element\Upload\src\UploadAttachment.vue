<!--
* <AUTHOR> <PERSON>g<PERSON><PERSON><PERSON>
* @Date : 2022/04/18
* @Version : 1.0
* @Content : 全局上传组件
-->
<template>
  <el-upload
    ref="upload"
    v-bind="$attrs"
    class="upload-attachment"
    :action="action"
    :name="name"
    :file-list="fileList"
    :multiple="multiple"
    :limit="limit"
    :drag="drag"
    :data="data"
    :list-type="listType"
    :show-file-list="showFileList"
    :accept="accept || acceptTypeDetail"
    :on-exceed="handleExceed"
    :on-success="handleSuccess"
    :on-error="handleError"
    :before-upload="beforeAvatarUpload"
    :on-preview="handlePictureCardPreview"
    :on-remove="handleRemove"
  >
    <slot></slot>

    <!--  自定义预览  -->
    <template #file="{ file }">
      <slot name="preview" :file="file"></slot>
    </template>
  </el-upload>

  <sim-model v-model:visible="dialogVisible" title="图片预览">
    <template #footer>
      <div></div>
    </template>
    <div class="upload-attachment__preview">
      <el-image w-full :src="dialogImageUrl" alt="预览图片" />
    </div>
  </sim-model>
</template>

<script lang="ts">
// 限制的文件上传类型 可扩展
const acceptTypeMap = {
  file: '.doc, .docx, .xls, .xlsx, .ppt, .pptx, .png, .jpg, .jpeg, .txt, .scn, .xml, .json',
  image: '.png, .jpg, .jpeg, .gif',
  json: '.json',
  zip: '.zip',
  none: ''
};

export default {
  name: 'SimUploadAttachment'
};
</script>
<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';

const emits = defineEmits(['success', 'handlePreview', 'beforeUpload']);

const props = defineProps({
  action: {
    type: String,
    default: '/api/oss/file/uploadFile'
  },
  name: {
    type: String,
    default: 'file'
  },
  data: {
    type: Object,
    default: () => {
      return { bussType: 'sce', systemId: '0' };
    }
  },
  fileList: {
    // 默认上传文件
    type: Array,
    default: () => []
  },
  multiple: {
    // 是否支持多选
    type: Boolean,
    default: true
  },
  limit: {
    // 允许上传文件最大数量
    type: Number
  },
  listType: {
    // 文件列表的类型
    type: String,
    default: 'text'
  },
  drag: {
    // 是否支持拖拽上传
    type: Boolean,
    default: false
  },
  accept: {
    // 接受上传的文件类型
    type: String
  },
  acceptType: {
    // 自定义上传类型参数，目前可接收file、image、json
    type: String,
    default: 'image',
    validator(val) {
      return Object.keys(acceptTypeMap).includes(val);
    }
  },
  customAccept: {
    // 只在上传之前做校验
    type: String
  },
  fileMaxSize: {
    // 文件限制最大尺寸，M
    type: Number
  },
  showFileList: {
    type: Boolean,
    default: true
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '120px'
  }
});

const upload = ref();

const updateFileMaxSize = ref(props.fileMaxSize);

let load;

// let fileListParams = computed(() => props.fileList)

/**
 * 上传成功事件
 * @param {*} data
 */
const handleSuccess = (response, file, fileList) => {
  console.log('fileList', fileList);
  const fileListParams = fileList.map((it) => {
    if (it?.response) {
      return it.response.data;
    }
    return it;
  });
  const fileIds = fileListParams.map((it) => it.fileId);
  emits('success', fileIds, fileListParams);
};
/**
 * 上传失败时间
 */
const handleError = () => {
  // load?.close()
  // load = null
};
/**
 * 文件列表移除文件时的钩子
 */
const handleRemove = (file, fileList) => {
  const fileListParams = fileList.map((it) => {
    if (it?.response) {
      return it.response.data;
    }
    return it;
  });
  const fileIds = fileListParams.map((it) => it.fileId);
  emits('success', fileIds, fileList);
};

const acceptTypeDetail = computed(() => {
  return acceptTypeMap[props.acceptType || 'default'];
});

/**
 * 当超出限制时，执行的钩子函数
 * @param {*} files
 */
const handleExceed = (files, uploadFiles) => {
  if (files.length + uploadFiles.length > props.limit) {
    ElMessage({
      type: 'warning',
      message: '上传文件数量不得超过' + props.limit + '个'
    });
  }
};

/**
 * 上传文件之前钩子
 */
const beforeAvatarUpload = (rawFile) => {
  // 根据上传文件的不同格式修改不同大小限制
  emits('beforeUpload', rawFile, (size) => {
    updateFileMaxSize.value = size;
  });
  // 上传文件格式限制
  const type = props.accept || acceptTypeDetail.value || props.customAccept;
  if (type) {
    const fileSuffix = rawFile.name.toLowerCase().substring(rawFile.name.lastIndexOf('.') + 1);
    if (type.indexOf(fileSuffix) === -1) {
      ElMessage({
        type: 'warning',
        message: '上传文件只能是 ' + type + ' 格式'
      });
      return false;
    }
  }
  // 上传文件大小限制
  if (updateFileMaxSize.value || updateFileMaxSize.value === 0) {
    if (updateFileMaxSize.value >= 1 && rawFile.size / 1024 / 1024 > updateFileMaxSize.value) {
      ElMessage({
        type: 'warning',
        message: '单个文件大小不超过' + updateFileMaxSize.value + 'MB'
      });
      return false;
    } else if (updateFileMaxSize.value < 1 && rawFile.size / 1024 / 1024 > updateFileMaxSize.value) {
      ElMessage({
        type: 'warning',
        message: '单个文件大小不超过' + updateFileMaxSize.value * 1024 + 'KB'
      });
      return false;
    }
  }
  // if (!load) {
  // load = ElLoading.service({
  //   lock: true,
  //   text: "上传中...",
  // })
  // }
};

const dialogImageUrl = ref('');
const dialogVisible = ref(false);
/**
 * 点击文件列表中已上传的文件时的钩子函数
 */
const handlePictureCardPreview = (uploadFile) => {
  console.log(props.listType, uploadFile, '(props.listType');
  if (props.listType === 'picture-card') {
    dialogImageUrl.value = uploadFile.url;
    dialogVisible.value = true;
    return;
  }
  emits('handlePreview', uploadFile);
};

defineExpose({ upload });
</script>
<style lang="less">
.upload-attachment {
  width: 100%;

  .el-upload-list--picture-card {
    display: flex;
    flex-direction: column;

    .el-upload-list__item {
      width: v-bind(width);
      height: v-bind(height);
      border-radius: 0;
      border: 1px dashed var(--primary-color);
      background-color: rgba(var(--text-color-val), 0.1);
    }

    .el-upload {
      width: v-bind(width);
      height: v-bind(height);
      border-radius: 0;
      border: 1px dashed var(--primary-color);
      background-color: rgba(var(--text-color-val), 0.1);
    }
  }

  .el-upload-list__item {
    &:hover {
      background-color: rgba(var(--text-color-val), 0.3) !important;
    }
  }
}

.upload-attachment__preview {
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    max-width: 1000px;
    max-height: 800px;
  }
}
</style>
