import { CustomDataSource, CzmlDataSource, GeoJsonDataSource } from 'cesium';
import { getViewer, getViewerName, onViewerCreated, onViewerDestroyed } from 'isim-cesium';

export type SimSourceType = CustomDataSource | CzmlDataSource;

const entitySourceMap: Map<string, Map<string, SimSourceType>> = new Map();

/**
 * 设置缓存中的
 * @param sourceName
 * @param source
 * @param viewerName
 */
function setEntitySource(sourceName: string, source: SimSourceType, viewerName = getViewerName()) {
  if (!entitySourceMap.has(viewerName)) {
    entitySourceMap.set(viewerName, new Map());
    onViewerDestroyed(() => entitySourceMap.delete(viewerName), { viewerName });
  }
  entitySourceMap.get(viewerName)!.set(sourceName, source);
}

export function hasEntitySource(sourceName: string, viewerName = getViewerName()) {
  return entitySourceMap.get(viewerName)?.has(sourceName);
}

/**
 * 从缓存中获取
 * @param sourceName
 * @param viewerName
 */
export function getEntitySource(sourceName: string, viewerName = getViewerName()): CzmlDataSource | CustomDataSource | undefined {
  return entitySourceMap.get(viewerName)?.get(sourceName);
}
export const getCzmlSourceByName = getEntitySource;

/**
 * 创建source
 * @param sourceName
 * @param viewerName
 */
export function createEntityDatasource(sourceName: string, viewerName = getViewerName()) {
  if (!sourceName) {
    console.warn('请输入 datasource 名称');
    return;
  }
  const source = new CustomDataSource(sourceName);
  setEntitySource(sourceName, source, viewerName);
  return source;
}

/**
 * 获取source
 * @param sourceName
 * @param viewerName
 */
export function getEntityDatasource(sourceName: string, viewerName = getViewerName()) {
  return getEntitySource(sourceName, viewerName);
}

/**
 * 根据source名称删除source
 * @param sourceName
 * @param viewerName
 */
export function deleteDatasourceByName(sourceName: string, viewerName = getViewerName()) {
  const source = getEntityDatasource(sourceName, viewerName);
  if (!source) {
    return;
  }
  const viewer = getViewer(viewerName);
  viewer.dataSources.remove(source);
  entitySourceMap.get(viewerName)?.delete(sourceName);
}

/**
 * 清空entitySource
 * @param viewerName
 */
export function deleteEntitySourceByViewer(viewerName: string = getViewerName()) {
  entitySourceMap.delete(viewerName);
}

/**
 * 清除指定source的实体
 * @param sourceName
 * @param viewerName
 */
export function clearEntitySourceBySourceName(sourceName: string, viewerName = getViewerName()) {
  getEntitySource(sourceName, viewerName)?.entities.removeAll();
}

/**
 * 清除所有的entity
 * @param viewerName
 */
export function clearAllEntity(viewerName = getViewerName()) {
  entitySourceMap.get(viewerName)?.forEach((item) => item.entities.removeAll());
}

export function createEntitySource(sourceName: string, isMount = false, viewerName?: string) {
  let source: CustomDataSource | undefined;
  if (getEntitySource(sourceName)) {
    source = getEntitySource(sourceName);
  } else {
    source = new CustomDataSource(sourceName);
  }
  const mount = generateMountFn(sourceName, source!);

  if (isMount) {
    mount(viewerName);
  }
  return {
    source: source!,
    mount
  };
}

export function createCzmlSource(sourceName: string, isMount = false) {
  let source: CzmlDataSource | undefined;
  if (getEntitySource(sourceName)) {
    source = getEntitySource(sourceName) as CzmlDataSource;
  } else {
    source = new CzmlDataSource(sourceName);
  }
  const mount = generateMountFn(sourceName, source!);

  if (isMount) {
    mount();
  }
  return {
    source: source!,
    mount
  };
}

export function createGeoJsonSource(sourceName: string, isMount = false) {
  let source: GeoJsonDataSource | undefined;
  if (getEntitySource(sourceName)) {
    source = getEntitySource(sourceName) as GeoJsonDataSource;
  } else {
    source = new GeoJsonDataSource(sourceName);
  }
  const mount = generateMountFn(sourceName, source!);

  if (isMount) {
    mount();
  }
  return {
    source: source!,
    mount
  };
}

/**
 * 生成Datasource挂在函数 mount 高阶函数
 * @param sourceName
 * @param source
 */
export function generateMountFn(sourceName: string, source: CustomDataSource | CzmlDataSource) {
  return function (viewerName = getViewerName()) {
    onViewerCreated(
      (viewer) => {
        if (hasEntitySource(sourceName, viewerName)) {
          return;
        }
        viewer.dataSources.add(source);
        setEntitySource(sourceName, source, viewerName);
      },
      { viewerName }
    );
  };
}
