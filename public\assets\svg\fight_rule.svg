<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120" height="120" viewBox="0 0 120 120">
  <defs>
    <clipPath id="clip-path">
      <path id="路径_83" data-name="路径 83" d="M0,0H0V138.709H138.709V0Z"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.849" y1="-0.236" x2="0.037" y2="0.944" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d5dbff"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <clipPath id="clip-path-2">
      <path id="路径_82" data-name="路径 82" d="M.7.153.706,11.366.053,13.5,0,2.5Z" transform="translate(-0.004 -0.153)"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="0.91" y1="0.025" x2="0.05" y2="0.993" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.298"/>
      <stop offset="1" stop-color="#70caff" stop-opacity="0.294"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.298"/>
      <stop offset="1" stop-color="#acc8ff" stop-opacity="0.298"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.899" x2="0.16" y2="0.996" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#5ea1ff" stop-opacity="0.431"/>
      <stop offset="0.394" stop-color="#92caff" stop-opacity="0.431"/>
      <stop offset="1" stop-color="#03f" stop-opacity="0.29"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.249" y1="0.076" x2="0.84" y2="0.951" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0.139" y1="0.917" x2="0.864" y2="0.052" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#6565ff"/>
      <stop offset="0.747" stop-color="#68acff"/>
      <stop offset="1" stop-color="#2e6fff"/>
    </linearGradient>
    <clipPath id="clip-path-3">
      <path id="路径_85" data-name="路径 85" d="M15.6,9.628a10.527,10.527,0,0,0-6.283,3.646h0L1.854,24.761h0a10.515,10.515,0,0,0-.61,5.729l9.922,57.878A10.588,10.588,0,0,0,21.627,97.15l3.569-.015A10.3,10.3,0,0,0,26.841,97l48.152-7.8a10.549,10.549,0,0,0,7.573-5.343h0l4.917-7.345a10.626,10.626,0,0,0,.752-1.128l.124-.186-.013-.01a10.522,10.522,0,0,0,1.162-6.822L80.094,11.778A13.21,13.21,0,0,0,64.763.937L15.6,9.628Z" transform="translate(-1.09 -0.733)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-7" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-opacity="0.502"/>
      <stop offset="1" stop-color="#383838" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#cff4ff" stop-opacity="0.941"/>
      <stop offset="1" stop-color="#c4ceff" stop-opacity="0.839"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b9f4ff"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="0.968" y1="0.777" x2="0.039" y2="0.301" gradientUnits="objectBoundingBox">
      <stop offset="0.143" stop-color="#541bff"/>
      <stop offset="0.663" stop-color="#68acff"/>
      <stop offset="1" stop-color="#2e6fff"/>
    </linearGradient>
    <filter id="矢量图" x="32.766" y="16.839" width="67.476" height="72.047" filterUnits="userSpaceOnUse">
      <feOffset dx="-2" dy="2" input="SourceAlpha"/>
      <feGaussianBlur result="blur"/>
      <feFlood flood-color="#fff"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-作战规则">
      <rect width="120" height="120"/>
    </clipPath>
  </defs>
  <g id="作战规则" clip-path="url(#clip-作战规则)">
    <g id="编组" transform="translate(-64.447 43.347)" clip-path="url(#clip-path)">
      <path id="矢量图-2" d="M53.4.426l-.684,2.34S41.056,1.208,24.06,4.207a69.66,69.66,0,0,0-21.7,8.04L0,10.862S8.328,5.033,24.659,1.914A99.816,99.816,0,0,1,53.4.426Z" transform="translate(85.313 0)" fill-rule="evenodd" fill="url(#linear-gradient)"/>
      <g id="矢量图-4" transform="translate(137.996 0.443)">
        <g id="组_10" data-name="组 10" clip-path="url(#clip-path-2)">
          <path id="路径_81" data-name="路径 81" d="M.7.153.706,11.366.053,13.5,0,2.5Z" transform="translate(-0.004 -0.153)" fill="none" stroke="#707070" stroke-width="2"/>
        </g>
      </g>
      <path id="矢量图-5" d="M.27,21.708a79.046,79.046,0,0,1,23.311-8.293A105.27,105.27,0,0,1,50.62,12.189c.009-1.289-.048-10.957-.048-10.957a103.574,103.574,0,0,0-28.847,1.45C13.271,4.239,3.206,8.728.23,10.712" transform="translate(87.424 1.551)" fill-rule="evenodd" fill="url(#linear-gradient-2)"/>
      <path id="矢量图-6" d="M17.648,3.749l.007,11.008,2.373,1.427L20,5.152" transform="translate(67.683 7.085)" fill-rule="evenodd" fill="url(#linear-gradient-3)"/>
    </g>
    <g id="组_15" data-name="组 15" transform="translate(-33.437 11.559)">
      <g id="图标底座" transform="translate(46.997 2.119)">
        <path id="地块厚度" d="M15.6,9.628a10.527,10.527,0,0,0-6.283,3.646h0L1.854,24.761h0a10.518,10.518,0,0,0-.61,5.729l9.922,57.878A10.588,10.588,0,0,0,21.627,97.15l3.569-.015A10.3,10.3,0,0,0,26.841,97l48.152-7.8a10.549,10.549,0,0,0,7.573-5.343l.014.01,4.9-7.355a10.626,10.626,0,0,0,.752-1.128l.124-.186-.013-.01a10.522,10.522,0,0,0,1.162-6.822L80.094,11.778A13.21,13.21,0,0,0,64.763.937L15.6,9.628Z" transform="translate(-1.09 -0.733)" fill-rule="evenodd" fill="url(#linear-gradient-4)"/>
        <path id="高光" d="M26.716,13.108l3.706-5.594L42.683,76.425l-5.465,6.318a11.187,11.187,0,0,1-.3-1.258L26.048,19.266a11.107,11.107,0,0,1,.668-6.158Z" transform="translate(-24.879 10.833)" fill-rule="evenodd" fill="url(#linear-gradient-5)"/>
        <path id="矩形-2350-复制-2" d="M9.821,9.628A10.568,10.568,0,0,0,1.244,21.82L11.166,79.7a10.588,10.588,0,0,0,10.461,8.783l3.569-.015a10.294,10.294,0,0,0,1.646-.136l48.152-7.8a10.568,10.568,0,0,0,8.734-12.166L74.314,11.778A13.21,13.21,0,0,0,58.984.937L9.821,9.628Z" transform="translate(5.689 0.85)" fill-rule="evenodd" fill="url(#linear-gradient-6)"/>
        <g id="边线" transform="translate(0 0)">
          <g id="组_11" data-name="组 11" clip-path="url(#clip-path-3)">
            <path id="路径_84" data-name="路径 84" d="M67.079-.017h0A13.964,13.964,0,0,1,80.834,11.655l9.413,56.583a11.191,11.191,0,0,1-.951,6.736l.073.057-.5.746a11.422,11.422,0,0,1-.778,1.167l-4.887,7.3a11.256,11.256,0,0,1-8.093,5.692l-48.152,7.8a11.088,11.088,0,0,1-1.766.145L21.63,97.9a11.338,11.338,0,0,1-11.2-9.406L.5,30.616a11.3,11.3,0,0,1,.322-5.2l-.279-.025.677-1.043L8.712,12.828l.028-.034a11.325,11.325,0,0,1,6.729-3.9L64.633.2A14.091,14.091,0,0,1,67.079-.017ZM87.607,74.967l.08-.146a9.716,9.716,0,0,0,1.079-6.337L79.354,11.9A12.412,12.412,0,0,0,67.079,1.483h0a12.585,12.585,0,0,0-2.185.193L15.731,10.366A9.823,9.823,0,0,0,9.922,13.72l-7.4,11.386a9.793,9.793,0,0,0-.54,5.256l9.922,57.878A9.822,9.822,0,0,0,21.627,96.4l3.565-.015a9.594,9.594,0,0,0,1.529-.126l48.151-7.8a9.765,9.765,0,0,0,7.035-4.965l.214-.388h.043l4.721-7.05a9.916,9.916,0,0,0,.7-1.048Z" transform="translate(-1.09 -0.733)" fill="url(#linear-gradient-7)"/>
          </g>
        </g>
        <path id="矢量图-2-2" data-name="矢量图" d="M73.9,72.376a10.538,10.538,0,0,0,7.082-4.54,10.531,10.531,0,0,1-4.193,1.65l-48.152,7.8a10.3,10.3,0,0,1-1.646.136l-3.569.015a10.588,10.588,0,0,1-10.461-8.782L3.043,10.779A10.533,10.533,0,0,1,4.687,3.1,10.57,10.57,0,0,0,.154,13.669l9.922,57.878a10.588,10.588,0,0,0,10.461,8.782l3.569-.015a10.3,10.3,0,0,0,1.646-.136l48.152-7.8Z" transform="translate(10.728 3.742)" fill="rgba(255,255,255,0.9)" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="组_16" data-name="组 16" transform="translate(-6.055 0)">
      <path id="路径_91" data-name="路径 91" d="M17.3,11.628A10.568,10.568,0,0,0,8.72,23.82L18.641,81.7A10.588,10.588,0,0,0,29.1,90.481l3.569-.015a10.294,10.294,0,0,0,1.646-.136l48.152-7.8A10.568,10.568,0,0,0,91.2,70.361L81.79,13.778A13.21,13.21,0,0,0,66.459,2.937L17.3,11.628Z" transform="translate(22.608 5.165)" fill="url(#linear-gradient-8)"/>
      <g id="组_18" data-name="组 18" transform="translate(31.174 7.898)">
        <path id="路径_92" data-name="路径 92" d="M68.775,1.983h0A13.964,13.964,0,0,1,82.53,13.655l9.413,56.583a11.318,11.318,0,0,1-9.354,13.029l-48.152,7.8a11.083,11.083,0,0,1-1.766.145l-3.565.015a11.338,11.338,0,0,1-11.2-9.406L7.98,23.947a11.318,11.318,0,0,1,9.185-13.058L66.329,2.2A14.092,14.092,0,0,1,68.775,1.983ZM29.1,89.731l3.565-.015a9.589,9.589,0,0,0,1.529-.126l48.152-7.8a9.818,9.818,0,0,0,8.115-11.3L81.05,13.9A12.412,12.412,0,0,0,68.775,3.483h0a12.586,12.586,0,0,0-2.185.193L17.427,12.366A9.84,9.84,0,0,0,9.459,23.693l9.922,57.878A9.822,9.822,0,0,0,29.1,89.731Z" transform="translate(-8.566 -2.733)" fill="url(#linear-gradient-9)"/>
      </g>
    </g>
    <g id="组_17" data-name="组 17" transform="translate(115.13 153) rotate(169)">
      <g transform="matrix(-0.98, -0.19, 0.19, -0.98, 83.82, 172.16)" filter="url(#矢量图)">
        <path id="矢量图-3" data-name="矢量图" d="M29.231,60.685H25.676v-9.97a9.8,9.8,0,0,1-7.9-9.579,9.654,9.654,0,0,1,1.138-4.559L12.682,19.631H1.778a1.721,1.721,0,0,1-1.257-.515,1.747,1.747,0,0,1,0-2.488,1.721,1.721,0,0,1,1.257-.515h9.611L9.875,12,11.786,5.76,10.22,1.5,10.929,0l1.025,2.787L12.361,3.9h0l4.492,12.215h8.822V12.2H29.23v3.91h7.52l4.5-12.236.4-1.089L42.674,0l.708,1.5L41.817,5.76,43.728,12l-1.513,4.116H53.128a1.721,1.721,0,0,1,1.257.515,1.747,1.747,0,0,1,0,2.488,1.721,1.721,0,0,1-1.257.515H40.921l-5.63,15.311a9.675,9.675,0,0,1,2.235,6.194,9.808,9.808,0,0,1-8.3,9.65v9.9Zm-1.58-15.64a3.91,3.91,0,1,1,3.95-3.91,3.93,3.93,0,0,1-3.95,3.91ZM25.675,19.631H18.147L22.9,32.563a9.995,9.995,0,0,1,8.036-.647l4.517-12.285H29.23v3.91H25.675v-3.91Z" transform="translate(100.24 76.41) rotate(169)" fill-rule="evenodd" fill="url(#linear-gradient-10)"/>
      </g>
    </g>
  </g>
</svg>
