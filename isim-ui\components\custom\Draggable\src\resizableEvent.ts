/**
 * @Author: 宋计民
 * @Date: 2022-04-06 12:09:49
 * @Version: 0.0.1
 * @Content:
 */
import type { UseDragState, DraggableProps } from './draggable-hook';

import { ref, toRefs } from 'vue';
import { updateHeight, updateLeft, updateTop, updateWidth } from './utils';

// 绑定拖拽修改尺寸事件
export function useResizableEventBind(props: DraggableProps, dragHook: UseDragState) {
  const { resizable } = toRefs(props);
  const { parentHtml, dragRef } = dragHook;
  const handles = ref<Array<string>>([]);
  if (resizable.value) {
    handles.value.push(...['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml']);
  }
  const resizableSizeCache = { endPosX: 0, endPosY: 0 };

  // 拖拽顶部 修改高度
  const resizableHeightByTop = (e: MouseEvent) => {
    if (resizableSizeCache.endPosY - props.minHeight <= e.y) {
      return;
    }
    const parentTop = parentHtml.value.getBoundingClientRect().top;
    updateTop({
      pos: e.y - parentTop,
      dragState: dragHook,
      props
    });
    updateHeight({
      pos: resizableSizeCache.endPosY - e.y,
      dragState: dragHook,
      props
    });
  };

  // 拖拽左边 修改宽度
  const resizableWidthByLeft = (e: MouseEvent) => {
    const parentLeft = parentHtml.value.getBoundingClientRect().left;
    // 达到最小宽度就不允许修改尺寸
    if (resizableSizeCache.endPosX - props.minWidth <= e.x) {
      return;
    }
    updateLeft({ pos: e.x - parentLeft, dragState: dragHook, props });
    updateWidth({
      pos: resizableSizeCache.endPosX - e.x,
      dragState: dragHook,
      props
    });
  };
  const parentMouseMove = (e: MouseEvent, type: string) => {
    const boxPos = dragRef.value.getBoundingClientRect();
    const boxX = boxPos.left;
    const boxY = boxPos.top;
    const diffLeft = e.x - boxX;
    const diffTop = e.y - boxY;
    switch (type) {
      case 'tl': //上左
        resizableHeightByTop(e);
        resizableWidthByLeft(e);
        break;
      case 'tm': // 上中
        resizableHeightByTop(e);
        break;
      case 'tr': // 上右
        resizableHeightByTop(e);
        updateWidth({ pos: diffLeft, dragState: dragHook, props });
        break;
      case 'mr': // 中右
        updateWidth({ pos: diffLeft, dragState: dragHook, props });
        break;
      case 'ml': // 中左
        resizableWidthByLeft(e);
        break;
      case 'bl': // 下左
        resizableWidthByLeft(e);
        updateHeight({ pos: diffTop, dragState: dragHook });
        break;
      case 'bm': // 下中
        updateHeight({ pos: diffTop, dragState: dragHook, props });
        break;
      case 'br': // 下右
        updateHeight({ pos: diffTop, dragState: dragHook });
        updateWidth({ pos: diffLeft, dragState: dragHook });
        break;
      default:
        break;
    }
  };
  const resizableMouseDown = (e: MouseEvent, type: string) => {
    // 缓存 修改尺寸时 盒子的原始尺寸 用于拖左 上 计算宽度和高度
    const dragRefRect = dragRef.value.getBoundingClientRect();
    resizableSizeCache.endPosX = dragRef.value.clientWidth + dragRefRect.left;
    resizableSizeCache.endPosY = dragRef.value.clientHeight + dragRefRect.top;
    const _eventBind = (me: MouseEvent) => {
      e.preventDefault();
      parentMouseMove(me, type);
    };
    parentHtml.value.addEventListener('mousemove', _eventBind);
    parentHtml.value.addEventListener('mouseup', () => {
      parentHtml.value.removeEventListener('mousemove', _eventBind);
    });
  };
  return {
    handles,
    resizableMouseDown
  };
}
