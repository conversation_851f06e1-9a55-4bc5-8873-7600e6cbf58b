/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/26 09:24:21
 * @description Remove Listener , rewrite the Module,
 * The Abstract Feature is used to calculate Matrix and update options
 * @version 3.0
 * */

import {
  defaultValue,
  defined,
  destroyObject,
  JulianDate,
  Matrix3,
  Matrix4,
  Transforms,
  HeadingPitchRoll,
  HeightReference,
  Cartesian3,
  Quaternion
} from 'cesium';
import YawPitchRoll from '../../Core/Transform/YawPitchRoll';
import { getViewer } from 'isim-cesium';

const EXCLUDE_PARAMETERS = ['heading', 'pitch', 'roll', 'yaw', 'orientation', 'scene'];
export default class AbstractFeature {
  _options;
  _pluginCollection;
  primitive;
  _orientation;
  heightReference;
  _currentTime;
  _isPositionConstant = false;

  constructor(options, pluginCollection) {
    options = defaultValue(options, {});
    this._options = options;
    this.setPluginCollection(pluginCollection);
    //  高度计算方式
    this.heightReference = options?.heightReference ?? HeightReference.NONE;
  }

  setPluginCollection(pluginCollection) {
    if (!pluginCollection || this._pluginCollection) return;
    this._pluginCollection = pluginCollection;
    this.updateFns = AbstractFeature.getUpdateFunction(this);
    console.log('updateFns', this.updateFns);
  }

  create() {}

  update(time) {
    if (this.primitive) {
      this._updateOptions(time, this.primitive);
      this._updateTransform(time);
    }
  }

  _updateTransform(time) {
    // if (this.updateWithTime && this._currentTime && !JulianDate.compare(time, this._currentTime)) {
    // if (this._currentTime && !JulianDate.compare(time, this._currentTime)) {
    //   return;
    // }
    if (!this.updateFns.isUpdate(time)) return;
    const { position, orientation } = this.updateFns.getPositionAndOrientation();
    const m = this.cTransform(position, orientation);
    if (m) {
      this.primitive.modelMatrix = m;
    }
    this.updateFns.resetUpdate(time);
  }

  _setShow(time, value) {
    const cartesian = this.position.getValue(time);
    const show = defined(this.entity) && this.entity.isShowing && this.entity.isAvailable(time) && defined(cartesian);
    if (value !== false) {
      return show;
    }
    return value;
  }

  cTransform(cartesian, quaternion) {
    // let cartesian = this.position?.getValue(time);
    // const scene = this?._pluginCollection?._owner?._scene;
    // if (scene) {
    //   cartesian = AbstractFeature.getPosition(scene, cartesian, this.heightReference);
    // }

    if (this._options.hasOwnProperty('yaw') && cartesian) {
      return AbstractFeature.getTransformByYpr(this._options, cartesian);
    }

    if (this._options.hasOwnProperty('heading') && cartesian) {
      return AbstractFeature.getTransformByHpr(this._options, cartesian);
    }

    // const quaternion = this.orientation?.getValue(time);
    if (cartesian && quaternion) {
      return Matrix4.fromRotationTranslation(Matrix3.fromQuaternion(quaternion), cartesian);
    }

    if (cartesian) {
      return Transforms.eastNorthUpToFixedFrame(cartesian);
    }
  }

  isDestroyed() {
    return false;
  }

  destroy() {
    this.primitive = this.primitive && this.primitive.destroy();
    return destroyObject(this);
  }

  /**
   * @description support property
   * get first init options or update options
   * @param {JulianDate} time
   * @param {Object|undefined} options
   * @returns
   */
  _updateOptions(time, options) {
    options = defaultValue(options, {});
    let property, value;
    Object.keys(this._options).forEach((key) => {
      if (!EXCLUDE_PARAMETERS.includes(key)) {
        property = this._options[key];
        if (property?.getValue) {
          value = property.getValue(time);
          if (key === 'show') {
            options[key] = this._setShow(time, value);
          } else {
            options[key] = value;
          }
        } else {
          if (key === 'show') {
            options[key] = this._setShow(time, property);
          } else {
            options[key] = property;
          }
        }
      }
    });

    return options;
  }

  set options(value) {
    // console.log(value);
    this._options = {
      ...this._options,
      ...value
    };
  }

  get options() {
    return this._options;
  }

  get entity() {
    return this?._pluginCollection?.entity;
  }

  get position() {
    return this.entity?.position;
  }

  set orientation(value) {
    this._orientation = value;
  }

  get orientation() {
    if (this._orientation) {
      return this._orientation;
    }
    if (this.entity?.orientation) {
      return this.entity.orientation;
    }
    return undefined;
  }
}

AbstractFeature.getTransformByYpr = function (options, cartesian) {
  const direction = defaultValue(options.direction, YawPitchRoll.Direction.TO_GROUND);
  const { yaw, pitch, roll } = options;
  const ypr = YawPitchRoll.fromDegrees(yaw, pitch, roll, direction);
  return ypr.toTransforms(cartesian);
};

AbstractFeature.getTransformByHpr = function (options, cartesian) {
  const { heading, pitch, roll } = options;
  const hpr = HeadingPitchRoll.fromDegrees(heading, pitch, roll);
  return Transforms.headingPitchRollToFixedFrame(cartesian, hpr);
};
// 按照heightReference类型，计算高度
AbstractFeature.getPosition = (scene, cartesian3, heightReference = HeightReference.NONE) => {
  if (heightReference === HeightReference.NONE) return cartesian3;
  const cartoPosition = scene.globe.ellipsoid.cartesianToCartographic(cartesian3);
  const height = scene.globe.getHeight(cartoPosition);

  // 贴地
  if (heightReference === HeightReference.CLAMP_TO_GROUND) {
    cartoPosition.height = height;
  }
  // 以当前地形高度为基准进行高度计算
  if (heightReference === HeightReference.RELATIVE_TO_GROUND) {
    cartoPosition.height += height;
  }
  return scene.globe.ellipsoid.cartographicToCartesian(cartoPosition, new Cartesian3());
};
// 获取 计算是否需要更新的函数
AbstractFeature.getUpdateFunction = (abstractFeature) => {
  let isUpdate = false;

  // 每一帧update调用 watchEntityChange，比对entity的关键属性判断是否需要更新
  // orientation变化，直接判断需要更新
  // position变化,判断需要更新,并重新绑定当前位置的地形高度变化函数
  let positionCache = new Cartesian3();
  let orientationCache = new Quaternion();
  function watchEntityChange(time) {
    const entity = abstractFeature?.entity;
    if (!entity) return;
    const orientation = entity?.orientation?.getValue(time);
    if (orientationCache && orientationCache !== orientation && !orientationCache.equals(orientation)) {
      console.log('orientation 变化', abstractFeature);
      orientationCache = orientation;
      isUpdate = true;
    }
    let position = entity?.position?.getValue(time);
    const scene = abstractFeature?._pluginCollection?._owner?._scene;
    position = AbstractFeature.getPosition(scene, position, abstractFeature?.heightReference);
    if (positionCache && positionCache !== position && !positionCache.equals(position)) {
      console.log('position 变化', abstractFeature);
      positionCache = position;
      // watchTerrain(positionCache);
      isUpdate = true;
    }
  }

  return {
    // update方法内判断是否更新
    isUpdate(time) {
      watchEntityChange(time);
      return isUpdate;
    },
    // 重置更新状态
    resetUpdate(time) {
      isUpdate = false;
      abstractFeature._currentTime = time;
    },
    // 获取平移和旋转值
    getPositionAndOrientation() {
      return {
        position: positionCache,
        orientation: orientationCache
      };
    }
  };
};
