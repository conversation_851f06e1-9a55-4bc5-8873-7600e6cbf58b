<!--
* @Author: 宋计民
* @Date: 2024/3/1
* @Version: 1.0
* @Content: monaco.vue
-->
<template>
  <div ref="editorRef" class="sim-monaco-editor"></div>
</template>

<script setup lang="ts">
import * as monaco from 'monaco-editor';
import EditorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import JsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import CssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
import HtmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
import TsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';
import { isJson } from '@/utils';
import { PropType } from 'vue';
import { useDebounceFn } from '@vueuse/core';

defineOptions({
  name: 'SimMonaco'
});

const props = defineProps({
  readonly: {
    type: Boolean,
    default: false
  },
  language: {
    type: String as PropType<'json' | 'html' | 'javascript' | 'typescript'>,
    default: 'json'
  }
});

self.MonacoEnvironment = {
  getWorker(_workerId, label) {
    if (label === 'json') {
      return new JsonWorker();
    }
    if (label === 'css') {
      return new CssWorker();
    }
    if (label === 'html') {
      return new HtmlWorker();
    }
    if (label === 'typescript' || label === 'javascript') {
      return new TsWorker();
    }
    return new EditorWorker();
  }
};

const modelValue = defineModel<string>({ required: true });

const editorRef = ref();
let instance: monaco.editor.IStandaloneCodeEditor | null = null;

const getModelValue = () => {
  const modelVal = toValue(modelValue);
  return isJson(modelVal) ? JSON.stringify(JSON.parse(modelVal), null, 2) : modelVal;
};

const handleShowToEditor = () => {
  const value = getModelValue();
  // const valueStr = JSON.stringify(value, null, '\t');
  if (!instance) return;
  if (instance.getValue() !== value) {
    const jsonModel = monaco.editor.createModel(value, 'json');
    const position = instance.getPosition();
    instance.setModel(jsonModel);
    instance.setPosition(position);
  }
};

watch(
  () => modelValue.value,
  () => {
    handleShowToEditor();
  }
);

const initEditor = () => {
  const value = getModelValue();
  const jsonModel = monaco.editor.createModel(value, 'json');
  monaco.editor.defineTheme('myTheme', {
    base: 'vs-dark',
    inherit: true,
    rules: [{ background: '#ffffff01', token: '' }],
    colors: {
      'editor.background': '#ffffff05'
    }
  });
  instance = monaco.editor.create(editorRef.value, {
    model: jsonModel,
    tabSize: 2,
    automaticLayout: true,
    scrollBeyondLastLine: false,
    theme: 'myTheme',
    roundedSelection: false,
    minimap: {
      enabled: false
    }
  });
  instance.updateOptions({
    readOnly: props.readonly
  });
  const changeValue = useDebounceFn(() => {
    modelValue.value = instance.getValue();
  }, 600);
  instance.onDidChangeModelContent(changeValue);
};
onMounted(() => {
  initEditor();
});
onBeforeUnmount(() => {
  instance?.dispose(); //销毁实例
});
</script>

<style scoped lang="less">
.sim-monaco-editor {
  height: 100%;
  width: 100%;
  min-width: 300px;
  min-height: 200px;
}
</style>
