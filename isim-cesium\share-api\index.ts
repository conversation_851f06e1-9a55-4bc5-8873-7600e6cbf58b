import { ViewerNameType } from 'isim-cesium';

export * from './nextTick';
export * from './data-type';
export * from './position-utils';

export * from './turf-utils';

export const assign = Object.assign;

export function getFieldByInstance<T, K extends keyof T>(getInstance: (viewerName?: ViewerNameType) => T, field: K) {
  return function (viewerName?: ViewerNameType) {
    return getInstance(viewerName)[field];
  };
}
