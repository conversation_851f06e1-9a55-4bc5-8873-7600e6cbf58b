/**
 * @Author: 宋计民
 * @Date: 2023/10/16 11:25
 * @Version: 1.0
 * @Content: 未实现
 */
// @ts-nocheck
import { Color, createPropertyDescriptor, defined, Event, Material, MaterialProperty, Property, defaultValue } from 'cesium';
import ShaderSource from './Sharder/SensorWaveMaterial.glsl?raw';

const MaterialType = 'SensorWave';

export const SensorWaveMaterial = new Material({
  fabric: {
    type: MaterialType,
    uniforms: {
      color: new Color(1.0, 0.0, 0.0, 0.7),
      speed: 3.0,
      count: 4,
      gradient: 0.2
    },
    source: ShaderSource
  },
  translucent: function (_material) {
    return true;
  }
});

export interface SensorWaveMaterialPropertyOptions {
  color: Color;
  speed: number;
  count: number;
  gradient: number;
}

interface SensorWaveMaterialProperty extends MaterialProperty {
  new (options?: SensorWaveMaterialPropertyOptions): SensorWaveMaterialProperty;
}
export const SensorWaveMaterialProperty: SensorWaveMaterialProperty = function (options?: SensorWaveMaterialPropertyOptions) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this._speed = undefined;
  this._speedSubscription = undefined;
  this._count = undefined;
  this._countSubscription = undefined;
  this._gradient = undefined;
  this._imageSubscription = undefined;

  this.color = options.color;
  this.speed = options.speed;
  this.count = options.count;
  this.gradient = options.gradient;
};

Object.defineProperties(SensorWaveMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return Property.isConstant(this._color) && Property.isConstant(this._glow);
    }
  },

  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },

  color: createPropertyDescriptor('color'),

  /**
   * Gets or sets the numeric Property specifying the strength of the glow, as a percentage of the total line width (less than 1.0).
   * @memberof SensorWaveMaterialProperty.prototype
   * @type {Property|undefined}
   */
  speed: createPropertyDescriptor('speed'),
  /**
   * Gets or sets the numeric Property specifying the strength of the glow, as a percentage of the total line width (less than 1.0).
   * @memberof SensorWaveMaterialProperty.prototype
   * @type {Property|undefined}
   */
  count: createPropertyDescriptor('count'),
  /**
   * Gets or sets the numeric Property specifying the strength of the glow, as a percentage of the total line width (less than 1.0).
   * @memberof SensorWaveMaterialProperty.prototype
   * @type {Property|undefined}
   */
  gradient: createPropertyDescriptor('gradient')
});

/**
 * Gets the {@link Material} type at the provided time.
 *
 * @param {JulianDate} _time The time for which to retrieve the type.
 * @returns {String} The type of material.
 */
SensorWaveMaterialProperty.prototype.getType = function (_time) {
  return MaterialType;
};
const defaultColor = new Color(1.0, 0.0, 0.0, 0.7);
const defaultSpeed = 3;
const defaultCount = 4;
const defaultGradient = 0.2;

SensorWaveMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  result.color = Property.getValueOrClonedDefault(this._color, time, defaultColor, result.color);
  result.speed = Property.getValueOrDefault(this._speed, time, defaultSpeed, result.speed);
  result.count = Property.getValueOrDefault(this._count, time, defaultCount, result.count);
  result.gradient = Property.getValueOrDefault(this._gradient, time, defaultGradient, result.gradient);
  return result;
};

SensorWaveMaterialProperty.prototype.equals = function (other) {
  return this === other || (other instanceof SensorWaveMaterialProperty && Property.equals(this._color, other._color));
};
