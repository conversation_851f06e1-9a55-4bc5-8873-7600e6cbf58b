<!--
 * @Author: 杜云涛
 * @Date: 2022-04-14 14:46:13
 * @Version: 0.0.1
 * @Content: cesium 的时间轴
-->

<template>
  <div ref="timeLineRef" class="cesium-time-line"></div>
</template>
<script setup>
import { useThrottleFn } from '@vueuse/core';
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { Timeline } from './custom-timeline';
import { Clock, JulianDate, ClockRange } from 'cesium';
import { ganttProps, useGanttInject } from './gantt';
const { updateCurrentTime, rangeDoubleClick } = useGanttInject();
const timeLineRef = ref();
const props = defineProps({ ...ganttProps() });
/**
 * 初始化clock 用于timeline
 */
const clock = new Clock({
  startTime: JulianDate.fromDate(new Date(props.startTime)),
  currentTime: JulianDate.fromDate(new Date(props.currentTime)),
  stopTime: JulianDate.fromDate(new Date(props.endTime)),
  clockRange: ClockRange.UNBOUNDED,
  shouldAnimate: true
});
let timeLine;
const initTimeLine = (timelineContainer, clock) => {
  const timeline = new Timeline(timelineContainer, clock, {
    setMousewheel: props.setMousewheel,
    showCurrentTime: props.showCurrentTime,
    setTimeEvent: props.setTimeEvent,
    max: props.max,
    min: props.min
  });
  return timeline;
};
const handleSetTime = (e) => {
  const date = JulianDate.toDate(e.timeJulian);
  const currentTime = new Date(date).getTime();
  updateCurrentTime(currentTime);
};

const _onMouseWheelAndMouseMove = () => {
  debounce();
};
const rangeDoubleClickFn = () => {
  const _startJulian = new Date(JulianDate.toDate(timeLine._startJulian)).getTime();
  const _endJulian = new Date(JulianDate.toDate(timeLine._endJulian)).getTime();
  if (JulianDate.lessThanOrEquals(timeLine._endJulian, timeLine._startJulian)) {
    return;
  }
  rangeDoubleClick({
    _startTime: _startJulian,
    _endTime: _endJulian
  });
};
const debounce = useThrottleFn(rangeDoubleClickFn, 30);

onMounted(() => {
  const timelineContainer = timeLineRef.value;
  timeLine = initTimeLine(timelineContainer, clock);
  timeLine.addEventListener('settime', handleSetTime, false);
  //timeLine._timeBarEle.addEventListener('mousewheel', _onMouseWheelAndMouseMove, false)
  timeLine.container.ownerDocument.addEventListener('mousemove', _onMouseWheelAndMouseMove, false);
});
onUnmounted(() => {
  timeLine && timeLine.removeEventListener('settime', handleSetTime, false);
  //timeLine._timeBarEle.removeEventListener('mousewheel', _onMouseWheelAndMouseMove, false)
  timeLine.container.ownerDocument.removeEventListener('mousemove', _onMouseWheelAndMouseMove, false);
  timeLine.destroy();
});
watch(
  () => [props.startTime, props.endTime],
  ([startTime, endTime]) => {
    clock.startTime = JulianDate.fromDate(new Date(startTime));
    clock.stopTime = JulianDate.fromDate(new Date(endTime));
    timeLine && timeLine.zoomTo(clock.startTime, clock.stopTime);
  }
);

watch(
  () => [props.max, props.min],
  ([max, min]) => {
    if (timeLine) {
      timeLine.max = max;
      timeLine.min = min;
    }
  }
);

watch(
  () => props.currentTime,
  (currentTime) => {
    clock.currentTime = JulianDate.fromDate(new Date(currentTime));
    clock.tick();
  }
);
</script>
<style lang="less" scoped>
.cesium-time-line {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
  border: 0;
  pointer-events: none;
  line-height: normal;
  :deep(.cesium-timeline-bar) {
    pointer-events: all;
    background: transparent;
    height: 29px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      -1px -1px 0px rgba(0, 179, 255, 0.3) inset,
      1px 0px 0px rgba(0, 179, 255, 0.31) inset;
  }
  :deep(.cesium-timeline-main) {
    border: none;
    position: static;
  }
  :deep(.cesium-timeline-icon16) {
    display: none;
    background-image: linear-gradient(90deg, #f00 1px, transparent 1px, transparent 100%);
  }
  :deep(.cesium-timeline-needle) {
    top: 1px;
    z-index: 1;
    width: 3px;
    height: 100%;
  }
}
</style>
