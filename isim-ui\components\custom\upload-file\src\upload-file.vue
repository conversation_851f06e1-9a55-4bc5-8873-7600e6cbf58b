<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-28 16:33
* @Version : 1.0
* @Content : upload-file.vue
-->
<template>
  <div class="upload-file">
    <!-- 已上传文件列表 -->
    <!-- <div v-for="file in fileList" :key="file?.fileId" class="file-item">{{ file?.fileName }}</div> -->
    <el-tag
      v-for="(file, dex) in modelValue"
      :key="file?.fileId"
      class="file-item"
      :closable="closable"
      :disable-transitions="false"
      @click="$emit('click', file.fileId)"
      @close="handleClose(dex)"
    >
      {{ file?.fileName }}
    </el-tag>
    <!-- 上传按钮 -->
    <sim-upload-attachment
      v-if="allowUpload"
      :multiple="false"
      :data="{ bussType: 'DocumentTemplate', systemId: '0' }"
      :file-max-size="100"
      accept=".doc, .docx, .xlsx, .csv, .ppt, .pptx, .txt, .pdf"
      :limit="100"
      :show-file-list="false"
      :file-list="modelValue"
      class="file-btn"
      style="float: left; width: auto; height: 26px"
      @success="uploadMsgAnnex"
    >
      <el-button class="yt-button--text original-edit__preview" size="small"
        ><el-icon size="16" style="margin-right: 4px"><UploadFilled /></el-icon>上传文件
      </el-button>
    </sim-upload-attachment>
    <div style="clear: both"></div>
  </div>
</template>

<script lang="ts" setup>
import { UploadFilled } from '@element-plus/icons-vue';
import { FileType } from '@api/inference-homework/index-type';

const emits = defineEmits(['update:modelValue', 'success', 'click']);

const props = defineProps({
  allowUpload: {
    type: Boolean,
    default: true
  },
  allowDownload: {
    type: Boolean,
    default: true
  },
  closable: {
    type: Boolean,
    default: true
  },
  modelValue: {
    type: Array,
    default: () => []
  }
});

/**
 * 上传附件
 * @param val
 * @param file
 * @returns
 */
const uploadMsgAnnex = (val: string[], file: FileType[]) => {
  const _file = file.map((item) => ({
    fileName: item.fileName,
    fileId: item.fileId
  }));
  emits('update:modelValue', _file);
  emits('success', val, file);
};

const handleClose = (dex: number) => {
  const _value = [...props.modelValue];
  _value.splice(dex, 1);
  emits('update:modelValue', _value);
};
</script>

<style scoped lang="less">
.upload-file {
  width: 100%;
  .file-item {
    float: left;
    margin: 0 5px 5px 0;
    padding: 5px;
    line-height: 1;
    border: 1px solid var(--primary-color-3);
    border-radius: 4px;
    cursor: default;
    &:last-child {
      margin-right: 20px;
    }
    &:hover {
      background-color: rgba(var(--primary-color-val), 0.1);
    }
  }
  .file-btn {
    float: left;
    .el-button {
      .el-icon {
        position: relative;
        top: -1px;
      }
    }
  }
}
</style>
