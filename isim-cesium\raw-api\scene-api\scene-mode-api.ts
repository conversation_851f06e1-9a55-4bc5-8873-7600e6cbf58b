import { getViewer, nextTick, ViewerNameType } from 'isim-cesium';
import { SceneMode, Viewer } from 'cesium';

export type SceneModeChangeFunction = (scene: SceneMode) => void;

const sceneWeak = new WeakMap<Viewer, Set<SceneModeChangeFunction>>();

/**
 * 切换到2维
 */
export const changeSceneMode2D = generateSceneModeChange(SceneMode.SCENE2D);

/**
 * 切换到3维
 */
export const changeSceneMode3D = generateSceneModeChange(SceneMode.SCENE3D);

/**
 * 切换到1.5维
 */
export const changeSceneModeColumbus = generateSceneModeChange(SceneMode.COLUMBUS_VIEW);

/**
 * 设置纬度
 * @param sceneMode
 */
export function generateSceneModeChange(sceneMode: SceneMode) {
  return function (viewerName?: ViewerNameType) {
    const viewer = getViewer(viewerName);
    viewer.scene.mode = sceneMode;
    sceneWeak.get(viewer)?.forEach((item) => {
      item?.(sceneMode);
    });
  };
}

export function onSceneChange(
  callback: SceneModeChangeFunction,
  opt?: {
    viewerName?: Viewer;
  }
) {
  nextTick().then(() => {
    const viewer = getViewer(opt?.viewerName);
    if (!sceneWeak.has(viewer)) {
      sceneWeak.set(viewer, new Set());
    }
    sceneWeak.get(viewer)?.add(callback);
  });
}
