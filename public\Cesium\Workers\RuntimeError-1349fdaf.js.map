{"version": 3, "file": "RuntimeError-1349fdaf.js", "sources": ["../../../../Source/Core/DeveloperError.js", "../../../../Source/Core/Check.js", "../../../../Source/Core/RuntimeError.js"], "sourcesContent": ["import defined from \"./defined.js\";\n\n/**\n * Constructs an exception object that is thrown due to a developer error, e.g., invalid argument,\n * argument out of range, etc.  This exception should only be thrown during development;\n * it usually indicates a bug in the calling code.  This exception should never be\n * caught; instead the calling code should strive not to generate it.\n * <br /><br />\n * On the other hand, a {@link RuntimeError} indicates an exception that may\n * be thrown at runtime, e.g., out of memory, that the calling code should be prepared\n * to catch.\n *\n * @alias DeveloperError\n * @constructor\n * @extends Error\n *\n * @param {String} [message] The error message for this exception.\n *\n * @see RuntimeError\n */\nfunction DeveloperError(message) {\n  /**\n   * 'DeveloperError' indicating that this exception was thrown due to a developer error.\n   * @type {String}\n   * @readonly\n   */\n  this.name = \"DeveloperError\";\n\n  /**\n   * The explanation for why this exception was thrown.\n   * @type {String}\n   * @readonly\n   */\n  this.message = message;\n\n  //Browsers such as IE don't have a stack property until you actually throw the error.\n  let stack;\n  try {\n    throw new Error();\n  } catch (e) {\n    stack = e.stack;\n  }\n\n  /**\n   * The stack trace of this exception, if available.\n   * @type {String}\n   * @readonly\n   */\n  this.stack = stack;\n}\n\nif (defined(Object.create)) {\n  DeveloperError.prototype = Object.create(Error.prototype);\n  DeveloperError.prototype.constructor = DeveloperError;\n}\n\nDeveloperError.prototype.toString = function () {\n  let str = this.name + \": \" + this.message;\n\n  if (defined(this.stack)) {\n    str += \"\\n\" + this.stack.toString();\n  }\n\n  return str;\n};\n\n/**\n * @private\n */\nDeveloperError.throwInstantiationError = function () {\n  throw new DeveloperError(\n    \"This function defines an interface and should not be called directly.\"\n  );\n};\nexport default DeveloperError;\n", "import defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\n\n/**\n * Contains functions for checking that supplied arguments are of a specified type\n * or meet specified conditions\n * @private\n */\nconst Check = {};\n\n/**\n * Contains type checking functions, all using the typeof operator\n */\nCheck.typeOf = {};\n\nfunction getUndefinedErrorMessage(name) {\n  return name + \" is required, actual value was undefined\";\n}\n\nfunction getFailedTypeErrorMessage(actual, expected, name) {\n  return (\n    \"Expected \" +\n    name +\n    \" to be typeof \" +\n    expected +\n    \", actual typeof was \" +\n    actual\n  );\n}\n\n/**\n * Throws if test is not defined\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value that is to be checked\n * @exception {DeveloperError} test must be defined\n */\nCheck.defined = function (name, test) {\n  if (!defined(test)) {\n    throw new DeveloperError(getUndefinedErrorMessage(name));\n  }\n};\n\n/**\n * Throws if test is not typeof 'function'\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @exception {DeveloperError} test must be typeof 'function'\n */\nCheck.typeOf.func = function (name, test) {\n  if (typeof test !== \"function\") {\n    throw new DeveloperError(\n      getFailedTypeErrorMessage(typeof test, \"function\", name)\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'string'\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @exception {DeveloperError} test must be typeof 'string'\n */\nCheck.typeOf.string = function (name, test) {\n  if (typeof test !== \"string\") {\n    throw new DeveloperError(\n      getFailedTypeErrorMessage(typeof test, \"string\", name)\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'number'\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @exception {DeveloperError} test must be typeof 'number'\n */\nCheck.typeOf.number = function (name, test) {\n  if (typeof test !== \"number\") {\n    throw new DeveloperError(\n      getFailedTypeErrorMessage(typeof test, \"number\", name)\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'number' and less than limit\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @param {Number} limit The limit value to compare against\n * @exception {DeveloperError} test must be typeof 'number' and less than limit\n */\nCheck.typeOf.number.lessThan = function (name, test, limit) {\n  Check.typeOf.number(name, test);\n  if (test >= limit) {\n    throw new DeveloperError(\n      \"Expected \" +\n        name +\n        \" to be less than \" +\n        limit +\n        \", actual value was \" +\n        test\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'number' and less than or equal to limit\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @param {Number} limit The limit value to compare against\n * @exception {DeveloperError} test must be typeof 'number' and less than or equal to limit\n */\nCheck.typeOf.number.lessThanOrEquals = function (name, test, limit) {\n  Check.typeOf.number(name, test);\n  if (test > limit) {\n    throw new DeveloperError(\n      \"Expected \" +\n        name +\n        \" to be less than or equal to \" +\n        limit +\n        \", actual value was \" +\n        test\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'number' and greater than limit\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @param {Number} limit The limit value to compare against\n * @exception {DeveloperError} test must be typeof 'number' and greater than limit\n */\nCheck.typeOf.number.greaterThan = function (name, test, limit) {\n  Check.typeOf.number(name, test);\n  if (test <= limit) {\n    throw new DeveloperError(\n      \"Expected \" +\n        name +\n        \" to be greater than \" +\n        limit +\n        \", actual value was \" +\n        test\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'number' and greater than or equal to limit\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @param {Number} limit The limit value to compare against\n * @exception {DeveloperError} test must be typeof 'number' and greater than or equal to limit\n */\nCheck.typeOf.number.greaterThanOrEquals = function (name, test, limit) {\n  Check.typeOf.number(name, test);\n  if (test < limit) {\n    throw new DeveloperError(\n      \"Expected \" +\n        name +\n        \" to be greater than or equal to \" +\n        limit +\n        \", actual value was \" +\n        test\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'object'\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @exception {DeveloperError} test must be typeof 'object'\n */\nCheck.typeOf.object = function (name, test) {\n  if (typeof test !== \"object\") {\n    throw new DeveloperError(\n      getFailedTypeErrorMessage(typeof test, \"object\", name)\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'boolean'\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @exception {DeveloperError} test must be typeof 'boolean'\n */\nCheck.typeOf.bool = function (name, test) {\n  if (typeof test !== \"boolean\") {\n    throw new DeveloperError(\n      getFailedTypeErrorMessage(typeof test, \"boolean\", name)\n    );\n  }\n};\n\n/**\n * Throws if test is not typeof 'bigint'\n *\n * @param {String} name The name of the variable being tested\n * @param {*} test The value to test\n * @exception {DeveloperError} test must be typeof 'bigint'\n */\nCheck.typeOf.bigint = function (name, test) {\n  if (typeof test !== \"bigint\") {\n    throw new DeveloperError(\n      getFailedTypeErrorMessage(typeof test, \"bigint\", name)\n    );\n  }\n};\n\n/**\n * Throws if test1 and test2 is not typeof 'number' and not equal in value\n *\n * @param {String} name1 The name of the first variable being tested\n * @param {String} name2 The name of the second variable being tested against\n * @param {*} test1 The value to test\n * @param {*} test2 The value to test against\n * @exception {DeveloperError} test1 and test2 should be type of 'number' and be equal in value\n */\nCheck.typeOf.number.equals = function (name1, name2, test1, test2) {\n  Check.typeOf.number(name1, test1);\n  Check.typeOf.number(name2, test2);\n  if (test1 !== test2) {\n    throw new DeveloperError(\n      name1 +\n        \" must be equal to \" +\n        name2 +\n        \", the actual values are \" +\n        test1 +\n        \" and \" +\n        test2\n    );\n  }\n};\nexport default Check;\n", "import defined from \"./defined.js\";\n\n/**\n * Constructs an exception object that is thrown due to an error that can occur at runtime, e.g.,\n * out of memory, could not compile shader, etc.  If a function may throw this\n * exception, the calling code should be prepared to catch it.\n * <br /><br />\n * On the other hand, a {@link DeveloperError} indicates an exception due\n * to a developer error, e.g., invalid argument, that usually indicates a bug in the\n * calling code.\n *\n * @alias RuntimeError\n * @constructor\n * @extends Error\n *\n * @param {String} [message] The error message for this exception.\n *\n * @see DeveloperError\n */\nfunction RuntimeError(message) {\n  /**\n   * 'RuntimeError' indicating that this exception was thrown due to a runtime error.\n   * @type {String}\n   * @readonly\n   */\n  this.name = \"RuntimeError\";\n\n  /**\n   * The explanation for why this exception was thrown.\n   * @type {String}\n   * @readonly\n   */\n  this.message = message;\n\n  //Browsers such as IE don't have a stack property until you actually throw the error.\n  let stack;\n  try {\n    throw new Error();\n  } catch (e) {\n    stack = e.stack;\n  }\n\n  /**\n   * The stack trace of this exception, if available.\n   * @type {String}\n   * @readonly\n   */\n  this.stack = stack;\n}\n\nif (defined(Object.create)) {\n  RuntimeError.prototype = Object.create(Error.prototype);\n  RuntimeError.prototype.constructor = RuntimeError;\n}\n\nRuntimeError.prototype.toString = function () {\n  let str = this.name + \": \" + this.message;\n\n  if (defined(this.stack)) {\n    str += \"\\n\" + this.stack.toString();\n  }\n\n  return str;\n};\nexport default RuntimeError;\n"], "names": ["DeveloperError", "message", "stack", "this", "name", "Error", "e", "defined", "Object", "create", "prototype", "constructor", "toString", "str", "throwInstantiationError", "Check", "getFailedTypeErrorMessage", "actual", "expected", "RuntimeError", "typeOf", "test", "getUndefinedErrorMessage", "func", "string", "number", "lessThan", "limit", "lessThanOrEquals", "greaterThan", "greaterThanOrEquals", "object", "bool", "bigint", "equals", "name1", "name2", "test1", "test2"], "mappings": "iEAoBA,SAASA,EAAeC,GAgBtB,IAAIC,EAVJC,KAAKC,KAAO,iBAOZD,KAAKF,QAAUA,EAIf,IACE,MAAM,IAAII,KAGX,CAFC,MAAOC,GACPJ,EAAQI,EAAEJ,KACX,CAODC,KAAKD,MAAQA,CACf,CAEIK,UAAQC,OAAOC,UACjBT,EAAeU,UAAYF,OAAOC,OAAOJ,MAAMK,WAC/CV,EAAeU,UAAUC,YAAcX,GAGzCA,EAAeU,UAAUE,SAAW,WAClC,IAAIC,EAAMV,KAAKC,KAAO,KAAOD,KAAKF,QAMlC,OAJIM,EAAOA,QAACJ,KAAKD,SACfW,GAAO,KAAOV,KAAKD,MAAMU,YAGpBC,CACT,EAKAb,EAAec,wBAA0B,WACvC,MAAM,IAAId,EACR,wEAEJ,ECjEM,MAAAe,EAAQ,CAAG,EAWjB,SAASC,EAA0BC,EAAQC,EAAUd,GACnD,MACE,YACAA,EACA,iBACAc,EACA,uBACAD,CAEJ,CCTA,SAASE,EAAalB,GAgBpB,IAAIC,EAVJC,KAAKC,KAAO,eAOZD,KAAKF,QAAUA,EAIf,IACE,MAAM,IAAII,KAGX,CAFC,MAAOC,GACPJ,EAAQI,EAAEJ,KACX,CAODC,KAAKD,MAAQA,CACf,CDnCAa,EAAMK,OAAS,CAAA,EAwBfL,EAAMR,QAAU,SAAUH,EAAMiB,GAC9B,IAAKd,EAAAA,QAAQc,GACX,MAAM,IAAIrB,EAxBd,SAAkCI,GAChC,OAAOA,EAAO,0CAChB,CAsB6BkB,CAAyBlB,GAEtD,EASAW,EAAMK,OAAOG,KAAO,SAAUnB,EAAMiB,GAClC,GAAoB,mBAATA,EACT,MAAM,IAAIrB,EACRgB,SAAiCK,EAAM,WAAYjB,GAGzD,EASAW,EAAMK,OAAOI,OAAS,SAAUpB,EAAMiB,GACpC,GAAoB,iBAATA,EACT,MAAM,IAAIrB,EACRgB,SAAiCK,EAAM,SAAUjB,GAGvD,EASAW,EAAMK,OAAOK,OAAS,SAAUrB,EAAMiB,GACpC,GAAoB,iBAATA,EACT,MAAM,IAAIrB,EACRgB,SAAiCK,EAAM,SAAUjB,GAGvD,EAUAW,EAAMK,OAAOK,OAAOC,SAAW,SAAUtB,EAAMiB,EAAMM,GAEnD,GADAZ,EAAMK,OAAOK,OAAOrB,EAAMiB,GACtBA,GAAQM,EACV,MAAM,IAAI3B,EACR,YACEI,EACA,oBACAuB,EACA,sBACAN,EAGR,EAUAN,EAAMK,OAAOK,OAAOG,iBAAmB,SAAUxB,EAAMiB,EAAMM,GAE3D,GADAZ,EAAMK,OAAOK,OAAOrB,EAAMiB,GACtBA,EAAOM,EACT,MAAM,IAAI3B,EACR,YACEI,EACA,gCACAuB,EACA,sBACAN,EAGR,EAUAN,EAAMK,OAAOK,OAAOI,YAAc,SAAUzB,EAAMiB,EAAMM,GAEtD,GADAZ,EAAMK,OAAOK,OAAOrB,EAAMiB,GACtBA,GAAQM,EACV,MAAM,IAAI3B,EACR,YACEI,EACA,uBACAuB,EACA,sBACAN,EAGR,EAUAN,EAAMK,OAAOK,OAAOK,oBAAsB,SAAU1B,EAAMiB,EAAMM,GAE9D,GADAZ,EAAMK,OAAOK,OAAOrB,EAAMiB,GACtBA,EAAOM,EACT,MAAM,IAAI3B,EACR,YACEI,EACA,mCACAuB,EACA,sBACAN,EAGR,EASAN,EAAMK,OAAOW,OAAS,SAAU3B,EAAMiB,GACpC,GAAoB,iBAATA,EACT,MAAM,IAAIrB,EACRgB,SAAiCK,EAAM,SAAUjB,GAGvD,EASAW,EAAMK,OAAOY,KAAO,SAAU5B,EAAMiB,GAClC,GAAoB,kBAATA,EACT,MAAM,IAAIrB,EACRgB,SAAiCK,EAAM,UAAWjB,GAGxD,EASAW,EAAMK,OAAOa,OAAS,SAAU7B,EAAMiB,GACpC,GAAoB,iBAATA,EACT,MAAM,IAAIrB,EACRgB,SAAiCK,EAAM,SAAUjB,GAGvD,EAWAW,EAAMK,OAAOK,OAAOS,OAAS,SAAUC,EAAOC,EAAOC,EAAOC,GAG1D,GAFAvB,EAAMK,OAAOK,OAAOU,EAAOE,GAC3BtB,EAAMK,OAAOK,OAAOW,EAAOE,GACvBD,IAAUC,EACZ,MAAM,IAAItC,EACRmC,EACE,qBACAC,EACA,2BACAC,EACA,QACAC,EAGR,EClMI/B,UAAQC,OAAOC,UACjBU,EAAaT,UAAYF,OAAOC,OAAOJ,MAAMK,WAC7CS,EAAaT,UAAUC,YAAcQ,GAGvCA,EAAaT,UAAUE,SAAW,WAChC,IAAIC,EAAMV,KAAKC,KAAO,KAAOD,KAAKF,QAMlC,OAJIM,EAAOA,QAACJ,KAAKD,SACfW,GAAO,KAAOV,KAAKD,MAAMU,YAGpBC,CACT"}