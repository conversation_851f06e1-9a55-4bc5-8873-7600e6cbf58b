{"version": 3, "file": "RectangleGeometryLibrary-64a11d7a.js", "sources": ["../../../../Source/Core/RectangleGeometryLibrary.js"], "sourcesContent": ["import Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport GeographicProjection from \"./GeographicProjection.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix2 from \"./Matrix2.js\";\nimport Rectangle from \"./Rectangle.js\";\n\nconst cos = Math.cos;\nconst sin = Math.sin;\nconst sqrt = Math.sqrt;\n\n/**\n * @private\n */\nconst RectangleGeometryLibrary = {};\n\n/**\n * @private\n */\nRectangleGeometryLibrary.computePosition = function (\n  computedOptions,\n  ellipsoid,\n  computeST,\n  row,\n  col,\n  position,\n  st\n) {\n  const radiiSquared = ellipsoid.radiiSquared;\n  const nwCorner = computedOptions.nwCorner;\n  const rectangle = computedOptions.boundingRectangle;\n\n  let stLatitude =\n    nwCorner.latitude -\n    computedOptions.granYCos * row +\n    col * computedOptions.granXSin;\n  const cosLatitude = cos(stLatitude);\n  const nZ = sin(stLatitude);\n  const kZ = radiiSquared.z * nZ;\n\n  let stLongitude =\n    nwCorner.longitude +\n    row * computedOptions.granYSin +\n    col * computedOptions.granXCos;\n  const nX = cosLatitude * cos(stLongitude);\n  const nY = cosLatitude * sin(stLongitude);\n\n  const kX = radiiSquared.x * nX;\n  const kY = radiiSquared.y * nY;\n\n  const gamma = sqrt(kX * nX + kY * nY + kZ * nZ);\n\n  position.x = kX / gamma;\n  position.y = kY / gamma;\n  position.z = kZ / gamma;\n\n  if (computeST) {\n    const stNwCorner = computedOptions.stNwCorner;\n    if (defined(stNwCorner)) {\n      stLatitude =\n        stNwCorner.latitude -\n        computedOptions.stGranYCos * row +\n        col * computedOptions.stGranXSin;\n      stLongitude =\n        stNwCorner.longitude +\n        row * computedOptions.stGranYSin +\n        col * computedOptions.stGranXCos;\n\n      st.x = (stLongitude - computedOptions.stWest) * computedOptions.lonScalar;\n      st.y = (stLatitude - computedOptions.stSouth) * computedOptions.latScalar;\n    } else {\n      st.x = (stLongitude - rectangle.west) * computedOptions.lonScalar;\n      st.y = (stLatitude - rectangle.south) * computedOptions.latScalar;\n    }\n  }\n};\n\nconst rotationMatrixScratch = new Matrix2();\nlet nwCartesian = new Cartesian3();\nconst centerScratch = new Cartographic();\nlet centerCartesian = new Cartesian3();\nconst proj = new GeographicProjection();\n\nfunction getRotationOptions(\n  nwCorner,\n  rotation,\n  granularityX,\n  granularityY,\n  center,\n  width,\n  height\n) {\n  const cosRotation = Math.cos(rotation);\n  const granYCos = granularityY * cosRotation;\n  const granXCos = granularityX * cosRotation;\n\n  const sinRotation = Math.sin(rotation);\n  const granYSin = granularityY * sinRotation;\n  const granXSin = granularityX * sinRotation;\n\n  nwCartesian = proj.project(nwCorner, nwCartesian);\n\n  nwCartesian = Cartesian3.subtract(nwCartesian, centerCartesian, nwCartesian);\n  const rotationMatrix = Matrix2.fromRotation(rotation, rotationMatrixScratch);\n  nwCartesian = Matrix2.multiplyByVector(\n    rotationMatrix,\n    nwCartesian,\n    nwCartesian\n  );\n  nwCartesian = Cartesian3.add(nwCartesian, centerCartesian, nwCartesian);\n  nwCorner = proj.unproject(nwCartesian, nwCorner);\n\n  width -= 1;\n  height -= 1;\n\n  const latitude = nwCorner.latitude;\n  const latitude0 = latitude + width * granXSin;\n  const latitude1 = latitude - granYCos * height;\n  const latitude2 = latitude - granYCos * height + width * granXSin;\n\n  const north = Math.max(latitude, latitude0, latitude1, latitude2);\n  const south = Math.min(latitude, latitude0, latitude1, latitude2);\n\n  const longitude = nwCorner.longitude;\n  const longitude0 = longitude + width * granXCos;\n  const longitude1 = longitude + height * granYSin;\n  const longitude2 = longitude + height * granYSin + width * granXCos;\n\n  const east = Math.max(longitude, longitude0, longitude1, longitude2);\n  const west = Math.min(longitude, longitude0, longitude1, longitude2);\n\n  return {\n    north: north,\n    south: south,\n    east: east,\n    west: west,\n    granYCos: granYCos,\n    granYSin: granYSin,\n    granXCos: granXCos,\n    granXSin: granXSin,\n    nwCorner: nwCorner,\n  };\n}\n\n/**\n * @private\n */\nRectangleGeometryLibrary.computeOptions = function (\n  rectangle,\n  granularity,\n  rotation,\n  stRotation,\n  boundingRectangleScratch,\n  nwCornerResult,\n  stNwCornerResult\n) {\n  let east = rectangle.east;\n  let west = rectangle.west;\n  let north = rectangle.north;\n  let south = rectangle.south;\n\n  let northCap = false;\n  let southCap = false;\n\n  if (north === CesiumMath.PI_OVER_TWO) {\n    northCap = true;\n  }\n  if (south === -CesiumMath.PI_OVER_TWO) {\n    southCap = true;\n  }\n\n  let dx;\n  const dy = north - south;\n  if (west > east) {\n    dx = CesiumMath.TWO_PI - west + east;\n  } else {\n    dx = east - west;\n  }\n\n  const width = Math.ceil(dx / granularity) + 1;\n  const height = Math.ceil(dy / granularity) + 1;\n  const granularityX = dx / (width - 1);\n  const granularityY = dy / (height - 1);\n\n  const nwCorner = Rectangle.northwest(rectangle, nwCornerResult);\n  const center = Rectangle.center(rectangle, centerScratch);\n  if (rotation !== 0 || stRotation !== 0) {\n    if (center.longitude < nwCorner.longitude) {\n      center.longitude += CesiumMath.TWO_PI;\n    }\n    centerCartesian = proj.project(center, centerCartesian);\n  }\n\n  const granYCos = granularityY;\n  const granXCos = granularityX;\n  const granYSin = 0.0;\n  const granXSin = 0.0;\n\n  const boundingRectangle = Rectangle.clone(\n    rectangle,\n    boundingRectangleScratch\n  );\n\n  const computedOptions = {\n    granYCos: granYCos,\n    granYSin: granYSin,\n    granXCos: granXCos,\n    granXSin: granXSin,\n    nwCorner: nwCorner,\n    boundingRectangle: boundingRectangle,\n    width: width,\n    height: height,\n    northCap: northCap,\n    southCap: southCap,\n  };\n\n  if (rotation !== 0) {\n    const rotationOptions = getRotationOptions(\n      nwCorner,\n      rotation,\n      granularityX,\n      granularityY,\n      center,\n      width,\n      height\n    );\n    north = rotationOptions.north;\n    south = rotationOptions.south;\n    east = rotationOptions.east;\n    west = rotationOptions.west;\n\n    //>>includeStart('debug', pragmas.debug);\n    if (\n      north < -CesiumMath.PI_OVER_TWO ||\n      north > CesiumMath.PI_OVER_TWO ||\n      south < -CesiumMath.PI_OVER_TWO ||\n      south > CesiumMath.PI_OVER_TWO\n    ) {\n      throw new DeveloperError(\n        \"Rotated rectangle is invalid.  It crosses over either the north or south pole.\"\n      );\n    }\n    //>>includeEnd('debug')\n\n    computedOptions.granYCos = rotationOptions.granYCos;\n    computedOptions.granYSin = rotationOptions.granYSin;\n    computedOptions.granXCos = rotationOptions.granXCos;\n    computedOptions.granXSin = rotationOptions.granXSin;\n\n    boundingRectangle.north = north;\n    boundingRectangle.south = south;\n    boundingRectangle.east = east;\n    boundingRectangle.west = west;\n  }\n\n  if (stRotation !== 0) {\n    rotation = rotation - stRotation;\n    const stNwCorner = Rectangle.northwest(boundingRectangle, stNwCornerResult);\n\n    const stRotationOptions = getRotationOptions(\n      stNwCorner,\n      rotation,\n      granularityX,\n      granularityY,\n      center,\n      width,\n      height\n    );\n\n    computedOptions.stGranYCos = stRotationOptions.granYCos;\n    computedOptions.stGranXCos = stRotationOptions.granXCos;\n    computedOptions.stGranYSin = stRotationOptions.granYSin;\n    computedOptions.stGranXSin = stRotationOptions.granXSin;\n    computedOptions.stNwCorner = stNwCorner;\n    computedOptions.stWest = stRotationOptions.west;\n    computedOptions.stSouth = stRotationOptions.south;\n  }\n\n  return computedOptions;\n};\nexport default RectangleGeometryLibrary;\n"], "names": ["cos", "Math", "sin", "sqrt", "RectangleGeometryLibrary", "computedOptions", "ellipsoid", "computeST", "row", "col", "position", "st", "radiiSquared", "nw<PERSON><PERSON><PERSON>", "rectangle", "boundingRectangle", "stLatitude", "latitude", "granYCos", "granXSin", "cosLatitude", "nZ", "kZ", "z", "stLongitude", "longitude", "granYSin", "granXCos", "nX", "nY", "kX", "x", "kY", "y", "gamma", "stNwCorner", "defined", "stGranYCos", "stGranXSin", "stGranYSin", "stGranXCos", "stWest", "lonScalar", "stSouth", "latScalar", "west", "south", "rotationMatrixScratch", "Matrix2", "nwCartesian", "Cartesian3", "centerScratch", "Cartographic", "centerCartesian", "proj", "GeographicProjection", "getRotationOptions", "rotation", "granularityX", "granularityY", "center", "width", "height", "cosRotation", "sinRotation", "project", "subtract", "rotationMatrix", "fromRotation", "multiplyByVector", "add", "unproject", "latitude0", "latitude1", "latitude2", "north", "max", "min", "longitude0", "longitude1", "longitude2", "east", "computeOptions", "granularity", "stRotation", "boundingRectangleScratch", "nwCornerResult", "stNwCornerResult", "dx", "northCap", "southCap", "CesiumMath", "PI_OVER_TWO", "dy", "TWO_PI", "ceil", "Rectangle", "northwest", "clone", "rotationOptions", "DeveloperError", "stRotationOptions"], "mappings": "+KASA,MAAMA,EAAMC,KAAKD,IACXE,EAAMD,KAAKC,IACXC,EAAOF,KAAKE,KAKZC,EAA2B,CAKjCA,gBAA2C,SACzCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAEA,MAAMC,EAAeN,EAAUM,aACzBC,EAAWR,EAAgBQ,SAC3BC,EAAYT,EAAgBU,kBAElC,IAAIC,EACFH,EAASI,SACTZ,EAAgBa,SAAWV,EAC3BC,EAAMJ,EAAgBc,SACxB,MAAMC,EAAcpB,EAAIgB,GAClBK,EAAKnB,EAAIc,GACTM,EAAKV,EAAaW,EAAIF,EAE5B,IAAIG,EACFX,EAASY,UACTjB,EAAMH,EAAgBqB,SACtBjB,EAAMJ,EAAgBsB,SACxB,MAAMC,EAAKR,EAAcpB,EAAIwB,GACvBK,EAAKT,EAAclB,EAAIsB,GAEvBM,EAAKlB,EAAamB,EAAIH,EACtBI,EAAKpB,EAAaqB,EAAIJ,EAEtBK,EAAQ/B,EAAK2B,EAAKF,EAAKI,EAAKH,EAAKP,EAAKD,GAM5C,GAJAX,EAASqB,EAAID,EAAKI,EAClBxB,EAASuB,EAAID,EAAKE,EAClBxB,EAASa,EAAID,EAAKY,EAEd3B,EAAW,CACb,MAAM4B,EAAa9B,EAAgB8B,WAC/BC,EAAAA,QAAQD,IACVnB,EACEmB,EAAWlB,SACXZ,EAAgBgC,WAAa7B,EAC7BC,EAAMJ,EAAgBiC,WACxBd,EACEW,EAAWV,UACXjB,EAAMH,EAAgBkC,WACtB9B,EAAMJ,EAAgBmC,WAExB7B,EAAGoB,GAAKP,EAAcnB,EAAgBoC,QAAUpC,EAAgBqC,UAChE/B,EAAGsB,GAAKjB,EAAaX,EAAgBsC,SAAWtC,EAAgBuC,YAEhEjC,EAAGoB,GAAKP,EAAcV,EAAU+B,MAAQxC,EAAgBqC,UACxD/B,EAAGsB,GAAKjB,EAAaF,EAAUgC,OAASzC,EAAgBuC,UAE3D,CACH,GAEMG,EAAwB,IAAIC,EAAAA,QAClC,IAAIC,EAAc,IAAIC,EAAAA,WACtB,MAAMC,EAAgB,IAAIC,EAAAA,aAC1B,IAAIC,EAAkB,IAAIH,EAAAA,WAC1B,MAAMI,EAAO,IAAIC,EAAAA,qBAEjB,SAASC,EACP3C,EACA4C,EACAC,EACAC,EACAC,EACAC,EACAC,GAEA,MAAMC,EAAc9D,KAAKD,IAAIyD,GACvBvC,EAAWyC,EAAeI,EAC1BpC,EAAW+B,EAAeK,EAE1BC,EAAc/D,KAAKC,IAAIuD,GACvB/B,EAAWiC,EAAeK,EAC1B7C,EAAWuC,EAAeM,EAEhCf,EAAcK,EAAKW,QAAQpD,EAAUoC,GAErCA,EAAcC,EAAUA,WAACgB,SAASjB,EAAaI,EAAiBJ,GAChE,MAAMkB,EAAiBnB,EAAOA,QAACoB,aAAaX,EAAUV,GACtDE,EAAcD,EAAOA,QAACqB,iBACpBF,EACAlB,EACAA,GAEFA,EAAcC,EAAUA,WAACoB,IAAIrB,EAAaI,EAAiBJ,GAG3DY,GAAS,EACTC,GAAU,EAEV,MAAM7C,GALNJ,EAAWyC,EAAKiB,UAAUtB,EAAapC,IAKbI,SACpBuD,EAAYvD,EAAW4C,EAAQ1C,EAC/BsD,EAAYxD,EAAWC,EAAW4C,EAClCY,EAAYzD,EAAWC,EAAW4C,EAASD,EAAQ1C,EAEnDwD,EAAQ1E,KAAK2E,IAAI3D,EAAUuD,EAAWC,EAAWC,GACjD5B,EAAQ7C,KAAK4E,IAAI5D,EAAUuD,EAAWC,EAAWC,GAEjDjD,EAAYZ,EAASY,UACrBqD,EAAarD,EAAYoC,EAAQlC,EACjCoD,EAAatD,EAAYqC,EAASpC,EAClCsD,EAAavD,EAAYqC,EAASpC,EAAWmC,EAAQlC,EAK3D,MAAO,CACLgD,MAAOA,EACP7B,MAAOA,EACPmC,KANWhF,KAAK2E,IAAInD,EAAWqD,EAAYC,EAAYC,GAOvDnC,KANW5C,KAAK4E,IAAIpD,EAAWqD,EAAYC,EAAYC,GAOvD9D,SAAUA,EACVQ,SAAUA,EACVC,SAAUA,EACVR,SAAUA,EACVN,SAAUA,EAEd,CAKAT,EAAyB8E,eAAiB,SACxCpE,EACAqE,EACA1B,EACA2B,EACAC,EACAC,EACAC,GAEA,IAeIC,EAfAP,EAAOnE,EAAUmE,KACjBpC,EAAO/B,EAAU+B,KACjB8B,EAAQ7D,EAAU6D,MAClB7B,EAAQhC,EAAUgC,MAElB2C,GAAW,EACXC,GAAW,EAEXf,IAAUgB,EAAUA,WAACC,cACvBH,GAAW,GAET3C,KAAW6C,EAAUA,WAACC,cACxBF,GAAW,GAIb,MAAMG,EAAKlB,EAAQ7B,EAEjB0C,EADE3C,EAAOoC,EACJU,EAAUA,WAACG,OAASjD,EAAOoC,EAE3BA,EAAOpC,EAGd,MAAMgB,EAAQ5D,KAAK8F,KAAKP,EAAKL,GAAe,EACtCrB,EAAS7D,KAAK8F,KAAKF,EAAKV,GAAe,EACvCzB,EAAe8B,GAAM3B,EAAQ,GAC7BF,EAAekC,GAAM/B,EAAS,GAE9BjD,EAAWmF,EAASA,UAACC,UAAUnF,EAAWwE,GAC1C1B,EAASoC,EAASA,UAACpC,OAAO9C,EAAWqC,GAC1B,IAAbM,GAAiC,IAAf2B,IAChBxB,EAAOnC,UAAYZ,EAASY,YAC9BmC,EAAOnC,WAAakE,EAAUA,WAACG,QAEjCzC,EAAkBC,EAAKW,QAAQL,EAAQP,IAGzC,MAAMnC,EAAWyC,EACXhC,EAAW+B,EAIX3C,EAAoBiF,EAAAA,UAAUE,MAClCpF,EACAuE,GAGIhF,EAAkB,CACtBa,SAAUA,EACVQ,SAVe,EAWfC,SAAUA,EACVR,SAXe,EAYfN,SAAUA,EACVE,kBAAmBA,EACnB8C,MAAOA,EACPC,OAAQA,EACR2B,SAAUA,EACVC,SAAUA,GAGZ,GAAiB,IAAbjC,EAAgB,CAClB,MAAM0C,EAAkB3C,EACtB3C,EACA4C,EACAC,EACAC,EACAC,EACAC,EACAC,GAQF,GANAa,EAAQwB,EAAgBxB,MACxB7B,EAAQqD,EAAgBrD,MACxBmC,EAAOkB,EAAgBlB,KACvBpC,EAAOsD,EAAgBtD,KAIrB8B,GAASgB,EAAAA,WAAWC,aACpBjB,EAAQgB,EAAUA,WAACC,aACnB9C,GAAS6C,EAAAA,WAAWC,aACpB9C,EAAQ6C,EAAUA,WAACC,YAEnB,MAAM,IAAIQ,EAAcA,eACtB,kFAKJ/F,EAAgBa,SAAWiF,EAAgBjF,SAC3Cb,EAAgBqB,SAAWyE,EAAgBzE,SAC3CrB,EAAgBsB,SAAWwE,EAAgBxE,SAC3CtB,EAAgBc,SAAWgF,EAAgBhF,SAE3CJ,EAAkB4D,MAAQA,EAC1B5D,EAAkB+B,MAAQA,EAC1B/B,EAAkBkE,KAAOA,EACzBlE,EAAkB8B,KAAOA,CAC1B,CAED,GAAmB,IAAfuC,EAAkB,CACpB3B,GAAsB2B,EACtB,MAAMjD,EAAa6D,EAASA,UAACC,UAAUlF,EAAmBwE,GAEpDc,EAAoB7C,EACxBrB,EACAsB,EACAC,EACAC,EACAC,EACAC,EACAC,GAGFzD,EAAgBgC,WAAagE,EAAkBnF,SAC/Cb,EAAgBmC,WAAa6D,EAAkB1E,SAC/CtB,EAAgBkC,WAAa8D,EAAkB3E,SAC/CrB,EAAgBiC,WAAa+D,EAAkBlF,SAC/Cd,EAAgB8B,WAAaA,EAC7B9B,EAAgBoC,OAAS4D,EAAkBxD,KAC3CxC,EAAgBsC,QAAU0D,EAAkBvD,KAC7C,CAED,OAAOzC,CACT"}