define(["exports","./Matrix2-57f130bc","./when-4bbc8319","./RuntimeError-1349fdaf","./Transforms-4b8effe1","./ComponentDatatype-17ffa790"],(function(e,r,t,n,i,a){"use strict";const o={};function s(e,r,t){const n=e+r;return a.CesiumMath.sign(e)!==a.CesiumMath.sign(r)&&Math.abs(n/Math.max(Math.abs(e),Math.abs(r)))<t?0:n}o.computeDiscriminant=function(e,r,t){if("number"!=typeof e)throw new n.DeveloperError("a is a required number.");if("number"!=typeof r)throw new n.DeveloperError("b is a required number.");if("number"!=typeof t)throw new n.DeveloperError("c is a required number.");return r*r-4*e*t},o.computeRealRoots=function(e,r,t){if("number"!=typeof e)throw new n.DeveloperError("a is a required number.");if("number"!=typeof r)throw new n.DeveloperError("b is a required number.");if("number"!=typeof t)throw new n.DeveloperError("c is a required number.");let i;if(0===e)return 0===r?[]:[-t/r];if(0===r){if(0===t)return[0,0];const r=Math.abs(t),n=Math.abs(e);if(r<n&&r/n<a.CesiumMath.EPSILON14)return[0,0];if(r>n&&n/r<a.CesiumMath.EPSILON14)return[];if(i=-t/e,i<0)return[];const o=Math.sqrt(i);return[-o,o]}if(0===t)return i=-r/e,i<0?[i,0]:[0,i];const o=s(r*r,-(4*e*t),a.CesiumMath.EPSILON14);if(o<0)return[];const u=-.5*s(r,a.CesiumMath.sign(r)*Math.sqrt(o),a.CesiumMath.EPSILON14);return r>0?[u/e,t/u]:[t/u,u/e]};const u={};function c(e,r,t,n){const i=e,a=r/3,o=t/3,s=n,u=i*o,c=a*s,d=a*a,f=o*o,l=i*o-d,h=i*s-a*o,m=a*s-f,p=4*l*m-h*h;let C,w;if(p<0){let e,r,t;d*c>=u*f?(e=i,r=l,t=-2*a*l+i*h):(e=s,r=m,t=-s*h+2*o*m);const n=-(t<0?-1:1)*Math.abs(e)*Math.sqrt(-p);w=-t+n;const M=w/2,b=M<0?-Math.pow(-M,1/3):Math.pow(M,1/3),g=w===n?-b:-r/b;return C=r<=0?b+g:-t/(b*b+g*g+r),d*c>=u*f?[(C-a)/i]:[-s/(C+o)]}const M=l,b=-2*a*l+i*h,g=m,q=-s*h+2*o*m,E=Math.sqrt(p),y=Math.sqrt(3)/2;let v=Math.abs(Math.atan2(i*E,-b)/3);C=2*Math.sqrt(-M);let D=Math.cos(v);w=C*D;let R=C*(-D/2-y*Math.sin(v));const S=w+R>2*a?w-a:R-a,O=i,x=S/O;v=Math.abs(Math.atan2(s*E,-q)/3),C=2*Math.sqrt(-g),D=Math.cos(v),w=C*D,R=C*(-D/2-y*Math.sin(v));const P=-s,N=w+R<2*o?w+o:R+o,L=P/N,I=-S*N-O*P,z=(o*I-a*(S*P))/(-a*I+o*(O*N));return x<=z?x<=L?z<=L?[x,z,L]:[x,L,z]:[L,x,z]:x<=L?[z,x,L]:z<=L?[z,L,x]:[L,z,x]}u.computeDiscriminant=function(e,r,t,i){if("number"!=typeof e)throw new n.DeveloperError("a is a required number.");if("number"!=typeof r)throw new n.DeveloperError("b is a required number.");if("number"!=typeof t)throw new n.DeveloperError("c is a required number.");if("number"!=typeof i)throw new n.DeveloperError("d is a required number.");const a=r*r,o=t*t;return 18*e*r*t*i+a*o-27*(e*e)*(i*i)-4*(e*o*t+a*r*i)},u.computeRealRoots=function(e,r,t,i){if("number"!=typeof e)throw new n.DeveloperError("a is a required number.");if("number"!=typeof r)throw new n.DeveloperError("b is a required number.");if("number"!=typeof t)throw new n.DeveloperError("c is a required number.");if("number"!=typeof i)throw new n.DeveloperError("d is a required number.");let a,s;if(0===e)return o.computeRealRoots(r,t,i);if(0===r){if(0===t){if(0===i)return[0,0,0];s=-i/e;const r=s<0?-Math.pow(-s,1/3):Math.pow(s,1/3);return[r,r,r]}return 0===i?(a=o.computeRealRoots(e,0,t),0===a.Length?[0]:[a[0],0,a[1]]):c(e,0,t,i)}return 0===t?0===i?(s=-r/e,s<0?[s,0,0]:[0,0,s]):c(e,r,0,i):0===i?(a=o.computeRealRoots(e,r,t),0===a.length?[0]:a[1]<=0?[a[0],a[1],0]:a[0]>=0?[0,a[0],a[1]]:[a[0],0,a[1]]):c(e,r,t,i)};const d={};function f(e,r,t,n){const i=e*e,s=r-3*i/8,c=t-r*e/2+i*e/8,d=n-t*e/4+r*i/16-3*i*i/256,f=u.computeRealRoots(1,2*s,s*s-4*d,-c*c);if(f.length>0){const r=-e/4,t=f[f.length-1];if(Math.abs(t)<a.CesiumMath.EPSILON14){const e=o.computeRealRoots(1,s,d);if(2===e.length){const t=e[0],n=e[1];let i;if(t>=0&&n>=0){const e=Math.sqrt(t),i=Math.sqrt(n);return[r-i,r-e,r+e,r+i]}if(t>=0&&n<0)return i=Math.sqrt(t),[r-i,r+i];if(t<0&&n>=0)return i=Math.sqrt(n),[r-i,r+i]}return[]}if(t>0){const e=Math.sqrt(t),n=(s+t-c/e)/2,i=(s+t+c/e)/2,a=o.computeRealRoots(1,e,n),u=o.computeRealRoots(1,-e,i);return 0!==a.length?(a[0]+=r,a[1]+=r,0!==u.length?(u[0]+=r,u[1]+=r,a[1]<=u[0]?[a[0],a[1],u[0],u[1]]:u[1]<=a[0]?[u[0],u[1],a[0],a[1]]:a[0]>=u[0]&&a[1]<=u[1]?[u[0],a[0],a[1],u[1]]:u[0]>=a[0]&&u[1]<=a[1]?[a[0],u[0],u[1],a[1]]:a[0]>u[0]&&a[0]<u[1]?[u[0],a[0],u[1],a[1]]:[a[0],u[0],a[1],u[1]]):a):0!==u.length?(u[0]+=r,u[1]+=r,u):[]}}return[]}function l(e,r,t,n){const i=e*e,s=-2*r,c=t*e+r*r-4*n,d=i*n-t*r*e+t*t,f=u.computeRealRoots(1,s,c,d);if(f.length>0){const s=f[0],u=r-s,c=u*u,d=e/2,l=u/2,h=c-4*n,m=c+4*Math.abs(n),p=i-4*s,C=i+4*Math.abs(s);let w,M,b,g,q,E;if(s<0||h*C<p*m){const r=Math.sqrt(p);w=r/2,M=0===r?0:(e*l-t)/r}else{const r=Math.sqrt(h);w=0===r?0:(e*l-t)/r,M=r/2}0===d&&0===w?(b=0,g=0):a.CesiumMath.sign(d)===a.CesiumMath.sign(w)?(b=d+w,g=s/b):(g=d-w,b=s/g),0===l&&0===M?(q=0,E=0):a.CesiumMath.sign(l)===a.CesiumMath.sign(M)?(q=l+M,E=n/q):(E=l-M,q=n/E);const y=o.computeRealRoots(1,b,q),v=o.computeRealRoots(1,g,E);if(0!==y.length)return 0!==v.length?y[1]<=v[0]?[y[0],y[1],v[0],v[1]]:v[1]<=y[0]?[v[0],v[1],y[0],y[1]]:y[0]>=v[0]&&y[1]<=v[1]?[v[0],y[0],y[1],v[1]]:v[0]>=y[0]&&v[1]<=y[1]?[y[0],v[0],v[1],y[1]]:y[0]>v[0]&&y[0]<v[1]?[v[0],y[0],v[1],y[1]]:[y[0],v[0],y[1],v[1]]:y;if(0!==v.length)return v}return[]}function h(e,n){n=r.Cartesian3.clone(t.defaultValue(n,r.Cartesian3.ZERO)),r.Cartesian3.equals(n,r.Cartesian3.ZERO)||r.Cartesian3.normalize(n,n),this.origin=r.Cartesian3.clone(t.defaultValue(e,r.Cartesian3.ZERO)),this.direction=n}d.computeDiscriminant=function(e,r,t,i,a){if("number"!=typeof e)throw new n.DeveloperError("a is a required number.");if("number"!=typeof r)throw new n.DeveloperError("b is a required number.");if("number"!=typeof t)throw new n.DeveloperError("c is a required number.");if("number"!=typeof i)throw new n.DeveloperError("d is a required number.");if("number"!=typeof a)throw new n.DeveloperError("e is a required number.");const o=e*e,s=r*r,u=s*r,c=t*t,d=c*t,f=i*i,l=f*i,h=a*a;return s*c*f-4*u*l-4*e*d*f+18*e*r*t*l-27*o*f*f+256*(o*e)*(h*a)+a*(18*u*t*i-4*s*d+16*e*c*c-80*e*r*c*i-6*e*s*f+144*o*t*f)+h*(144*e*s*t-27*s*s-128*o*c-192*o*r*i)},d.computeRealRoots=function(e,r,t,i,o){if("number"!=typeof e)throw new n.DeveloperError("a is a required number.");if("number"!=typeof r)throw new n.DeveloperError("b is a required number.");if("number"!=typeof t)throw new n.DeveloperError("c is a required number.");if("number"!=typeof i)throw new n.DeveloperError("d is a required number.");if("number"!=typeof o)throw new n.DeveloperError("e is a required number.");if(Math.abs(e)<a.CesiumMath.EPSILON15)return u.computeRealRoots(r,t,i,o);const s=r/e,c=t/e,d=i/e,h=o/e;let m=s<0?1:0;switch(m+=c<0?m+1:m,m+=d<0?m+1:m,m+=h<0?m+1:m,m){case 0:case 3:case 4:case 6:case 7:case 9:case 10:case 12:case 13:case 14:case 15:return f(s,c,d,h);case 1:case 2:case 5:case 8:case 11:return l(s,c,d,h);default:return}},h.clone=function(e,n){if(t.defined(e))return t.defined(n)?(n.origin=r.Cartesian3.clone(e.origin),n.direction=r.Cartesian3.clone(e.direction),n):new h(e.origin,e.direction)},h.getPoint=function(e,i,a){return n.Check.typeOf.object("ray",e),n.Check.typeOf.number("t",i),t.defined(a)||(a=new r.Cartesian3),a=r.Cartesian3.multiplyByScalar(e.direction,i,a),r.Cartesian3.add(e.origin,a,a)};const m={rayPlane:function(e,i,o){if(!t.defined(e))throw new n.DeveloperError("ray is required.");if(!t.defined(i))throw new n.DeveloperError("plane is required.");t.defined(o)||(o=new r.Cartesian3);const s=e.origin,u=e.direction,c=i.normal,d=r.Cartesian3.dot(c,u);if(Math.abs(d)<a.CesiumMath.EPSILON15)return;const f=(-i.distance-r.Cartesian3.dot(c,s))/d;return f<0?void 0:(o=r.Cartesian3.multiplyByScalar(u,f,o),r.Cartesian3.add(s,o,o))}},p=new r.Cartesian3,C=new r.Cartesian3,w=new r.Cartesian3,M=new r.Cartesian3,b=new r.Cartesian3;m.rayTriangleParametric=function(e,i,o,s,u){if(!t.defined(e))throw new n.DeveloperError("ray is required.");if(!t.defined(i))throw new n.DeveloperError("p0 is required.");if(!t.defined(o))throw new n.DeveloperError("p1 is required.");if(!t.defined(s))throw new n.DeveloperError("p2 is required.");u=t.defaultValue(u,!1);const c=e.origin,d=e.direction,f=r.Cartesian3.subtract(o,i,p),l=r.Cartesian3.subtract(s,i,C),h=r.Cartesian3.cross(d,l,w),m=r.Cartesian3.dot(f,h);let g,q,E,y,v;if(u){if(m<a.CesiumMath.EPSILON6)return;if(g=r.Cartesian3.subtract(c,i,M),E=r.Cartesian3.dot(g,h),E<0||E>m)return;if(q=r.Cartesian3.cross(g,f,b),y=r.Cartesian3.dot(d,q),y<0||E+y>m)return;v=r.Cartesian3.dot(l,q)/m}else{if(Math.abs(m)<a.CesiumMath.EPSILON6)return;const e=1/m;if(g=r.Cartesian3.subtract(c,i,M),E=r.Cartesian3.dot(g,h)*e,E<0||E>1)return;if(q=r.Cartesian3.cross(g,f,b),y=r.Cartesian3.dot(d,q)*e,y<0||E+y>1)return;v=r.Cartesian3.dot(l,q)*e}return v},m.rayTriangle=function(e,n,i,a,o,s){const u=m.rayTriangleParametric(e,n,i,a,o);if(t.defined(u)&&!(u<0))return t.defined(s)||(s=new r.Cartesian3),r.Cartesian3.multiplyByScalar(e.direction,u,s),r.Cartesian3.add(e.origin,s,s)};const g=new h;m.lineSegmentTriangle=function(e,i,a,o,s,u,c){if(!t.defined(e))throw new n.DeveloperError("v0 is required.");if(!t.defined(i))throw new n.DeveloperError("v1 is required.");if(!t.defined(a))throw new n.DeveloperError("p0 is required.");if(!t.defined(o))throw new n.DeveloperError("p1 is required.");if(!t.defined(s))throw new n.DeveloperError("p2 is required.");const d=g;r.Cartesian3.clone(e,d.origin),r.Cartesian3.subtract(i,e,d.direction),r.Cartesian3.normalize(d.direction,d.direction);const f=m.rayTriangleParametric(d,a,o,s,u);if(!(!t.defined(f)||f<0||f>r.Cartesian3.distance(e,i)))return t.defined(c)||(c=new r.Cartesian3),r.Cartesian3.multiplyByScalar(d.direction,f,c),r.Cartesian3.add(d.origin,c,c)};const q={root0:0,root1:0};function E(e,n,a){t.defined(a)||(a=new i.Interval);const o=e.origin,s=e.direction,u=n.center,c=n.radius*n.radius,d=r.Cartesian3.subtract(o,u,w),f=function(e,r,t,n){const i=r*r-4*e*t;if(i<0)return;if(i>0){const t=1/(2*e),a=Math.sqrt(i),o=(-r+a)*t,s=(-r-a)*t;return o<s?(n.root0=o,n.root1=s):(n.root0=s,n.root1=o),n}const a=-r/(2*e);return 0!==a?(n.root0=n.root1=a,n):void 0}(r.Cartesian3.dot(s,s),2*r.Cartesian3.dot(s,d),r.Cartesian3.magnitudeSquared(d)-c,q);if(t.defined(f))return a.start=f.root0,a.stop=f.root1,a}m.raySphere=function(e,r,i){if(!t.defined(e))throw new n.DeveloperError("ray is required.");if(!t.defined(r))throw new n.DeveloperError("sphere is required.");if(i=E(e,r,i),t.defined(i)&&!(i.stop<0))return i.start=Math.max(i.start,0),i};const y=new h;m.lineSegmentSphere=function(e,i,a,o){if(!t.defined(e))throw new n.DeveloperError("p0 is required.");if(!t.defined(i))throw new n.DeveloperError("p1 is required.");if(!t.defined(a))throw new n.DeveloperError("sphere is required.");const s=y;r.Cartesian3.clone(e,s.origin);const u=r.Cartesian3.subtract(i,e,s.direction),c=r.Cartesian3.magnitude(u);if(r.Cartesian3.normalize(u,u),o=E(s,a,o),!(!t.defined(o)||o.stop<0||o.start>c))return o.start=Math.max(o.start,0),o.stop=Math.min(o.stop,c),o};const v=new r.Cartesian3,D=new r.Cartesian3;function R(e,r,t){const n=e+r;return a.CesiumMath.sign(e)!==a.CesiumMath.sign(r)&&Math.abs(n/Math.max(Math.abs(e),Math.abs(r)))<t?0:n}m.rayEllipsoid=function(e,a){if(!t.defined(e))throw new n.DeveloperError("ray is required.");if(!t.defined(a))throw new n.DeveloperError("ellipsoid is required.");const o=a.oneOverRadii,s=r.Cartesian3.multiplyComponents(o,e.origin,v),u=r.Cartesian3.multiplyComponents(o,e.direction,D),c=r.Cartesian3.magnitudeSquared(s),d=r.Cartesian3.dot(s,u);let f,l,h,m,p;if(c>1){if(d>=0)return;const e=d*d;if(f=c-1,l=r.Cartesian3.magnitudeSquared(u),h=l*f,e<h)return;if(e>h){m=d*d-h,p=-d+Math.sqrt(m);const e=p/l,r=f/p;return e<r?new i.Interval(e,r):{start:r,stop:e}}const t=Math.sqrt(f/l);return new i.Interval(t,t)}return c<1?(f=c-1,l=r.Cartesian3.magnitudeSquared(u),h=l*f,m=d*d-h,p=-d+Math.sqrt(m),new i.Interval(0,p/l)):d<0?(l=r.Cartesian3.magnitudeSquared(u),new i.Interval(0,-d/l)):void 0};const S=new r.Cartesian3,O=new r.Cartesian3,x=new r.Cartesian3,P=new r.Cartesian3,N=new r.Cartesian3,L=new r.Matrix3,I=new r.Matrix3,z=new r.Matrix3,T=new r.Matrix3,U=new r.Matrix3,W=new r.Matrix3,B=new r.Matrix3,V=new r.Cartesian3,Z=new r.Cartesian3,A=new r.Cartographic;m.grazingAltitudeLocation=function(e,i){if(!t.defined(e))throw new n.DeveloperError("ray is required.");if(!t.defined(i))throw new n.DeveloperError("ellipsoid is required.");const s=e.origin,u=e.direction;if(!r.Cartesian3.equals(s,r.Cartesian3.ZERO)){const e=i.geodeticSurfaceNormal(s,S);if(r.Cartesian3.dot(u,e)>=0)return s}const c=t.defined(this.rayEllipsoid(e,i)),f=i.transformPositionToScaledSpace(u,S),l=r.Cartesian3.normalize(f,f),h=r.Cartesian3.mostOrthogonalAxis(f,P),m=r.Cartesian3.normalize(r.Cartesian3.cross(h,l,O),O),p=r.Cartesian3.normalize(r.Cartesian3.cross(l,m,x),x),C=L;C[0]=l.x,C[1]=l.y,C[2]=l.z,C[3]=m.x,C[4]=m.y,C[5]=m.z,C[6]=p.x,C[7]=p.y,C[8]=p.z;const w=r.Matrix3.transpose(C,I),M=r.Matrix3.fromScale(i.radii,z),b=r.Matrix3.fromScale(i.oneOverRadii,T),g=U;g[0]=0,g[1]=-u.z,g[2]=u.y,g[3]=u.z,g[4]=0,g[5]=-u.x,g[6]=-u.y,g[7]=u.x,g[8]=0;const q=r.Matrix3.multiply(r.Matrix3.multiply(w,b,W),g,W),E=r.Matrix3.multiply(r.Matrix3.multiply(q,M,B),C,B),y=r.Matrix3.multiplyByVector(q,s,N),v=function(e,t,n,i,s){const u=i*i,c=s*s,f=(e[r.Matrix3.COLUMN1ROW1]-e[r.Matrix3.COLUMN2ROW2])*c,l=s*(i*R(e[r.Matrix3.COLUMN1ROW0],e[r.Matrix3.COLUMN0ROW1],a.CesiumMath.EPSILON15)+t.y),h=e[r.Matrix3.COLUMN0ROW0]*u+e[r.Matrix3.COLUMN2ROW2]*c+i*t.x+n,m=c*R(e[r.Matrix3.COLUMN2ROW1],e[r.Matrix3.COLUMN1ROW2],a.CesiumMath.EPSILON15),p=s*(i*R(e[r.Matrix3.COLUMN2ROW0],e[r.Matrix3.COLUMN0ROW2])+t.z);let C;const w=[];if(0===p&&0===m){if(C=o.computeRealRoots(f,l,h),0===C.length)return w;const e=C[0],t=Math.sqrt(Math.max(1-e*e,0));if(w.push(new r.Cartesian3(i,s*e,s*-t)),w.push(new r.Cartesian3(i,s*e,s*t)),2===C.length){const e=C[1],t=Math.sqrt(Math.max(1-e*e,0));w.push(new r.Cartesian3(i,s*e,s*-t)),w.push(new r.Cartesian3(i,s*e,s*t))}return w}const M=p*p,b=m*m,g=p*m,q=f*f+b,E=2*(l*f+g),y=2*h*f+l*l-b+M,v=2*(h*l-g),D=h*h-M;if(0===q&&0===E&&0===y&&0===v)return w;C=d.computeRealRoots(q,E,y,v,D);const S=C.length;if(0===S)return w;for(let e=0;e<S;++e){const t=C[e],n=t*t,o=Math.max(1-n,0),u=Math.sqrt(o);let c;c=a.CesiumMath.sign(f)===a.CesiumMath.sign(h)?R(f*n+h,l*t,a.CesiumMath.EPSILON12):a.CesiumMath.sign(h)===a.CesiumMath.sign(l*t)?R(f*n,l*t+h,a.CesiumMath.EPSILON12):R(f*n+l*t,h,a.CesiumMath.EPSILON12);const d=c*R(m*t,p,a.CesiumMath.EPSILON15);d<0?w.push(new r.Cartesian3(i,s*t,s*u)):d>0?w.push(new r.Cartesian3(i,s*t,s*-u)):0!==u?(w.push(new r.Cartesian3(i,s*t,s*-u)),w.push(new r.Cartesian3(i,s*t,s*u)),++e):w.push(new r.Cartesian3(i,s*t,s*u))}return w}(E,r.Cartesian3.negate(y,S),0,0,1);let D,k;const j=v.length;if(j>0){let e=r.Cartesian3.clone(r.Cartesian3.ZERO,Z),t=Number.NEGATIVE_INFINITY;for(let n=0;n<j;++n){D=r.Matrix3.multiplyByVector(M,r.Matrix3.multiplyByVector(C,v[n],V),V);const i=r.Cartesian3.normalize(r.Cartesian3.subtract(D,s,P),P),a=r.Cartesian3.dot(i,u);a>t&&(t=a,e=r.Cartesian3.clone(D,e))}const n=i.cartesianToCartographic(e,A);return t=a.CesiumMath.clamp(t,0,1),k=r.Cartesian3.magnitude(r.Cartesian3.subtract(e,s,P))*Math.sqrt(1-t*t),k=c?-k:k,n.height=k,i.cartographicToCartesian(n,new r.Cartesian3)}};const k=new r.Cartesian3;m.lineSegmentPlane=function(e,i,o,s){if(!t.defined(e))throw new n.DeveloperError("endPoint0 is required.");if(!t.defined(i))throw new n.DeveloperError("endPoint1 is required.");if(!t.defined(o))throw new n.DeveloperError("plane is required.");t.defined(s)||(s=new r.Cartesian3);const u=r.Cartesian3.subtract(i,e,k),c=o.normal,d=r.Cartesian3.dot(c,u);if(Math.abs(d)<a.CesiumMath.EPSILON6)return;const f=r.Cartesian3.dot(c,e),l=-(o.distance+f)/d;return l<0||l>1?void 0:(r.Cartesian3.multiplyByScalar(u,l,s),r.Cartesian3.add(e,s,s),s)},m.trianglePlaneIntersection=function(e,i,a,o){if(!(t.defined(e)&&t.defined(i)&&t.defined(a)&&t.defined(o)))throw new n.DeveloperError("p0, p1, p2, and plane are required.");const s=o.normal,u=o.distance,c=r.Cartesian3.dot(s,e)+u<0,d=r.Cartesian3.dot(s,i)+u<0,f=r.Cartesian3.dot(s,a)+u<0;let l,h,p=0;if(p+=c?1:0,p+=d?1:0,p+=f?1:0,1!==p&&2!==p||(l=new r.Cartesian3,h=new r.Cartesian3),1===p){if(c)return m.lineSegmentPlane(e,i,o,l),m.lineSegmentPlane(e,a,o,h),{positions:[e,i,a,l,h],indices:[0,3,4,1,2,4,1,4,3]};if(d)return m.lineSegmentPlane(i,a,o,l),m.lineSegmentPlane(i,e,o,h),{positions:[e,i,a,l,h],indices:[1,3,4,2,0,4,2,4,3]};if(f)return m.lineSegmentPlane(a,e,o,l),m.lineSegmentPlane(a,i,o,h),{positions:[e,i,a,l,h],indices:[2,3,4,0,1,4,0,4,3]}}else if(2===p){if(!c)return m.lineSegmentPlane(i,e,o,l),m.lineSegmentPlane(a,e,o,h),{positions:[e,i,a,l,h],indices:[1,2,4,1,4,3,0,3,4]};if(!d)return m.lineSegmentPlane(a,i,o,l),m.lineSegmentPlane(e,i,o,h),{positions:[e,i,a,l,h],indices:[2,0,4,2,4,3,1,3,4]};if(!f)return m.lineSegmentPlane(e,a,o,l),m.lineSegmentPlane(i,a,o,h),{positions:[e,i,a,l,h],indices:[0,1,4,0,4,3,2,3,4]}}},e.IntersectionTests=m,e.Ray=h}));
//# sourceMappingURL=IntersectionTests-90b8c33b.js.map
