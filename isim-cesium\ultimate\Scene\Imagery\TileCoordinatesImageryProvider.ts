/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/04/15 12:54:41
 * @description  Create New TileCoordinatesImageryProvider
 * @version 1.0
 * */
import { Color, Event, GeographicTilingScheme, when, VERSION } from 'cesium';

interface TileCoordinatesImageryProviderType {
  color?: Color;
  labelColor?: Color;
  tileWidth?: number;
  tileHeight?: number;
}

/**
 * An {@link ImageryProvider} that draws a box around every rendered tile in the tiling scheme, and draws
 * a label inside it indicating the X, Y, Level coordinates of the tile.  This is mostly useful for
 * debugging terrain and imagery rendering problems.
 *
 * @alias TileCoordinatesImageryProvider
 * @constructor
 */
export default class TileCoordinatesImageryProvider1 {
  private _tilingScheme: GeographicTilingScheme;
  private _color: string;
  private _labelColor: string;
  private _errorEvent: Event;
  private _tileWidth: number;
  private _tileHeight: number;
  private _readyPromise: Promise<any>;

  constructor(options: TileCoordinatesImageryProviderType) {
    options = options ?? {};
    const { color = Color.YELLOW, labelColor = Color.YELLOW, tileWidth = 256, tileHeight = 256 } = options;
    this._tilingScheme = new GeographicTilingScheme();
    this._color = color.toCssColorString();
    this._labelColor = labelColor.toCssColorString();
    this._errorEvent = new Event();
    this._tileWidth = tileWidth;
    this._tileHeight = tileHeight;
    if (parseFloat(VERSION) < 1.92) {
      this._readyPromise = Promise.resolve(true);
    } else {
      //@ts-ignore
      this._readyPromise = when.resolve(true);
    }
  }
  get proxy() {
    return undefined;
  }
  get tileWidth() {
    return this._tileWidth;
  }
  get tileHeight() {
    return this._tileHeight;
  }
  get maximumLevel() {
    return undefined;
  }
  get minimumLevel() {
    return undefined;
  }
  get tilingScheme() {
    return this._tilingScheme;
  }
  get rectangle() {
    return this._tilingScheme.rectangle;
  }
  get tileDiscardPolicy() {
    return undefined;
  }
  get errorEvent() {
    return this._errorEvent;
  }
  get ready() {
    return true;
  }
  get readyPromise() {
    return this._readyPromise;
  }
  get credit() {
    return undefined;
  }
  get hasAlphaChannel() {
    return true;
  }

  getTileCredits() {
    return undefined;
  }
  pickFeatures() {
    return undefined;
  }

  /**
   * Requests the image for a given tile.
   * @param {Number} x The tile X coordinate.
   * @param {Number} y The tile Y coordinate.
   * @param {Number} level The tile level.
   * @param {Request} [request] The request object. Intended for internal use only.
   * @returns
   * */
  requestImage(x: any, y: any, level: any) {
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 256;
    const context = canvas.getContext('2d') as CanvasRenderingContext2D;
    context.strokeStyle = this._color;
    context.lineWidth = 1;
    context.strokeRect(1, 1, 255, 255);
    context.font = 'bold 12px Arial';
    context.textAlign = 'center';
    context.fillStyle = this._labelColor;

    const { west, north } = this._tilingScheme.tileXYToNativeRectangle(x, y, level);
    const text = formatNumber(level, west, north);
    context.fillText(text, (text.length * 45) / 14, 15);

    if (parseFloat(VERSION) < 1.92) {
      return canvas;
    }
    return Promise.resolve(canvas);
  }
}

/**
 * calculate data accuracy by zoom level
 * @private
 */
function formatNumber(level: number, west: number, north: number) {
  if (level > 10) {
    const num = (level - 8) / 2;
    const _west = west.toFixed(num);
    const _north = north.toFixed(num);
    return `${_west}°, ${_north}°`;
  }
  return `${west.toFixed(1)}°, ${north.toFixed(1)}°`;
}
