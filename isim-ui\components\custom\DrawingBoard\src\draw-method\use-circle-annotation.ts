/**
 * @Author: 宋计民
 * @Date: 2023-08-31 9:37
 * @Version: 1.0
 * @Content: use-circle-annotation.ts
 */
import { AnnotationConfig, AnnotationGraphic, AnnotationOptionsType, refreshAnnotations, setAnnotations } from './annotation-graphic';
import { useAnnotationsInject } from '../use-annotations-inject';

export function useCircleAnnotation(config: AnnotationConfig) {
  const { drawBoxRef, ctx, bindMouseUpAndLeave, clearAnnotations } = useAnnotationsInject();
  const start = () => {
    const box = drawBoxRef.value;
    box.onmousedown = (e) => {
      const circle = new CircleAnnotation(ctx.value, { ...toRaw(config), startX: e.offsetX, startY: e.offsetY });
      setAnnotations(circle);
      box.onmousemove = (ev) => {
        circle.stopX = ev.offsetX;
        circle.stopY = ev.offsetY;
        clearAnnotations();
        refreshAnnotations();
      };
      bindMouseUpAndLeave();
    };
  };

  return {
    start
  };
}

export class CircleAnnotation extends AnnotationGraphic {
  ctx: CanvasRenderingContext2D;
  color: string;
  startX: number;
  startY: number;
  width: number;
  stopX: number;
  stopY: number;

  constructor(ctx: CanvasRenderingContext2D, options: AnnotationOptionsType) {
    super();
    this.ctx = ctx;
    const { color = '#f00', startX, startY, width = 1 } = options;
    this.color = color;
    this.startX = startX;
    this.startY = startY;
    this.width = width;
    this.stopX = startX;
    this.stopY = startY;
  }

  calculateDistance(x: number, y: number) {
    return Math.sqrt(Math.pow(x - this.startX, 2) + Math.pow(y - this.startY, 2));
  }

  draw() {
    const ctx = this.ctx;
    const cx = (this.stopX + this.startX) / 2;
    const cy = (this.stopY + this.startY) / 2;
    ctx.beginPath();
    ctx.lineWidth = this.width;
    ctx.arc(cx, cy, this.calculateDistance(cx, cy), 0, Math.PI * 2);
    ctx.closePath();
    ctx.strokeStyle = this.color;
    ctx.stroke();
  }
}
