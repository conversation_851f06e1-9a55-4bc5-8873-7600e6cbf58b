<!--
 * @Author: wll
 * @Date: 2022-11-03 16:00:00
 * @Version: 0.0.1
 * @Content: upload 上传组件
-->
<template>
  <el-upload
    v-model:file-list="fileList"
    class="sim-upload"
    action="/oss-api/file/uploadFile?bussType=sce&systemId=0"
    :before-upload="onBeforeUpload"
    :on-success="onSuccess"
    :on-remove="onRemove"
  >
    <slot>
      <span v-if="attrs['list-type'] === 'picture-card'" class="sim-icon-jia" style="font-size: 30px; color: #fff"></span>
      <sim-button v-if="attrs['list-type'] === 'text'">上传文件</sim-button>
    </slot>
  </el-upload>
</template>
<script lang="ts">
export default {
  name: 'SimUpload'
};
</script>
<script lang="ts" setup>
import { useAttrs, ref } from 'vue';
import { ElMessage, UploadProps } from 'element-plus';

import { getDownLoadFileUrl as getMilitaryForStatic } from '@/utils';

interface FileOpt {
  url: string;
  name: string;
  response?: any;
}

const attrs = useAttrs();
console.log(attrs);

const props = defineProps({
  // 文件列表，如果传入,组件会处于受控状态
  fileList: {
    type: [Array, String],
    default: () => []
  },
  fileMaxSize: {
    type: Number,
    default: 200
  },
  width: {
    type: String,
    default: '120px'
  },
  height: {
    type: String,
    default: '120px'
  }
});

const emits = defineEmits(['update:fileList']);

// 限制的文件上传类型 可扩展
const acceptTypeMap = {
  file: '.doc, .docx, .xls, .xlsx, .ppt, .pptx, .png, .jpg, .jpeg, .txt, .scn, .xml, .json',
  image: '.png, .jpg, .jpeg, .gif',
  json: '.json',
  zip: '.zip',
  none: ''
};

// 获取上传类型
const acceptType = computed(() => {
  if (!attrs.accept) {
    return '';
  }
  // @ts-ignore
  return acceptTypeMap[(attrs.accept as string) || 'none'];
});
// 获取文件上传size限制
const fileMaxSize = computed(() => {
  return props.fileMaxSize * 1024 * 1024; // 计算中使用字节B单位
});

const uploadBtnRef = ref();
onMounted(() => {
  uploadBtnRef.value = document.querySelector('.el-upload--picture-card');
});

const fileList = ref<FileOpt[]>([]);
const initFileList = () => {
  if (props.fileList) {
    const file = toRaw(props.fileList);
    if (typeof file === 'string') {
      fileList.value = [
        {
          name: file,
          url: getMilitaryForStatic(file)
        }
      ];
    }
    if (Array.isArray(file)) {
      fileList.value = file.map((item) => {
        if (typeof item === 'string') {
          return {
            name: item,
            url: getMilitaryForStatic(item)
          };
        }
        return {
          name: item.fileId,
          url: getMilitaryForStatic(item.fileId)
        };
      });
    }
  }
};
initFileList();

const updateUploadBtnState = () => {
  const display = attrs.limit !== fileList.value.length ? 'flex' : 'none';
  uploadBtnRef.value.style.display = display;
  return display;
};

/**
 * 文件上传之前的回调，返回 false、Promise resolve false、Promise rejected 时会取消本次上传
 * @returns {Promise<boolean | void> | boolean | void}
 * @param rawFile
 */
const onBeforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  // 上传文件格式限制
  const type = acceptType.value;
  if (type) {
    const fileSuffix = rawFile.type.toLowerCase().substring(rawFile.name.lastIndexOf('.') + 1);
    if (type.indexOf(fileSuffix) === -1) {
      ElMessage({
        type: 'warning',
        message: `上传文件只能是${type}格式`
      });
      return false;
    }
  }
  // 上传文件大小限制
  if (fileMaxSize.value) {
    if (rawFile.size > fileMaxSize.value) {
      ElMessage({
        type: 'warning',
        message: `单个文件大小不超过${props.fileMaxSize}MB`
      });
      return false;
    }
  }
  return true;
};
/**
 * 文件上传成功时的钩子
 * @param uploadFile
 */
const onSuccess: UploadProps['onSuccess'] = ({ code }) => {
  if (code === '0000') {
    const files = fileList.value.map(({ response }) => response.data.fileId);
    emits('update:fileList', files);
    updateUploadBtnState();
  }
};

const onRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
  const files = uploadFiles.map(({ response }: any) => response.data.fileId);
  emits('update:fileList', files);
  updateUploadBtnState();
};

const width = ref(props.width);
const height = ref(props.height);
</script>

<style lang="less">
.sim-upload {
  .el-upload-list--picture-card {
    display: flex;
    flex-direction: column;

    .el-upload-list__item {
      width: v-bind(width);
      height: v-bind(height);
      border-radius: 0;
      border: 1px dashed var(--primary-color);
      background-color: rgba(var(--text-color-val), 0.1);
    }

    .el-upload {
      width: v-bind(width);
      height: v-bind(height);
      border-radius: 0;
      border: 1px dashed var(--primary-color);
      background-color: rgba(var(--text-color-val), 0.1);
    }
  }

  .el-upload-list__item {
    &:hover {
      background-color: rgba(var(--text-color-val), 0.3) !important;
    }
  }
}
</style>
