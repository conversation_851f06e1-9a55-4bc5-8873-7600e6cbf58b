/**
 * @Author: 宋计民
 * @Date: 2023-08-18 15:07
 * @Version: 1.0
 * @Content: turf-utils.ts
 */

//@ts-ignore
import { point, midpoint, polygon, distance, featureCollection, center, destination as turfDestination, area as turfArea } from '@turf/turf';
//@ts-ignore
export { polygon as turfPolygon, area as turfArea } from '@turf/turf';
interface PointType {
  longitude: number;
  latitude: number;
}

export type TurfUnits = 'kilometers' | 'miles' | 'degrees' | 'radians';

export function getTurfPoint(pointPos: number[] | PointType) {
  if (Array.isArray(pointPos)) {
    return point(pointPos);
  }
  return point([pointPos.longitude, pointPos.latitude]);
}

/**
 * 计算两个点的中心点
 * @param point1
 * @param point2
 */
export function getTwoPointsCenter(point1: number[], point2: number[]) {
  const _point1 = point(point1);
  const _point2 = point(point2);
  return midpoint(_point1, _point2);
}

export function getTurnPolygon(data: any) {
  return polygon(data);
}

/**
 * 计算两点间的距离
 * @param from
 * @param to
 * @param options
 */
export function getTwoPointsDistance(from: any, to: any, options?: any) {
  const _from = getTurfPoint(from);
  const _to = getTurfPoint(to);
  return distance(_from, _to, options);
}

/**
 * 获取中心点位
 * @param points
 */
export function getCenterByPoints(points: PointType[]) {
  const _points = points.map((item) => {
    return getTurfPoint(item);
  });
  const feature = featureCollection(_points);
  return center(feature);
}

interface GeometryType {
  coordinates: number[];
  type: string;
}
interface FeatureType {
  geometry: GeometryType;
}
export function getCoordinatesFromFeature(feature: FeatureType) {
  return feature.geometry.coordinates;
}

/**
 * 计算面积
 * @param points
 */
export function calculateAreaByPoints(points: PointType[]) {
  const _points = points.map((item) => {
    return [item.longitude, item.latitude];
  });
  _points.push([points[0].longitude, points[0].latitude]);
  const _polygon = polygon([_points]);
  const area = Math.floor(turfArea(_polygon)) / 1000000;
  return area ?? 0;
}

interface CalculateDestinationType {
  origin: PointType | number[];
  distance: number;
  bearing: number;
  options?: {
    units: TurfUnits;
    properties: Record<string, any>;
  };
}
export function calculateDestination(data: CalculateDestinationType) {
  const { origin, distance, bearing, options } = data;
  const point = getTurfPoint(origin);
  const destination = turfDestination(point, distance, bearing, options);
  return getCoordinatesFromFeature(destination);
}
