<!--
 * @Author: songjimin
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
-->
<template>
  <div :class="ns.b()">
    <div :class="ns.e('current')" @click="leftRowClick" @contextmenu="rangeContextmenu($event, data)">
      <div
        :class="[ns.e('icon'), ns.is('expand', data.expand)]"
        :style="{
          visibility: data[childrenField]?.length ? 'visible' : 'hidden'
        }"
        @click.stop="handleExpandChange"
      >
        <slot name="leftIcon">
          <svg>
            <path d="M7 5 L14 10 L7 15 Z" fill="white" />
          </svg>
        </slot>
      </div>
      <div :class="[ns.e('text')]">
        <slot name="left" :data="data">{{ data[leftProp.field] }}</slot>
      </div>
    </div>
    <template v-if="data[childrenField]">
      <y-gantt-left v-for="(item, dex) in data[childrenField]" v-show="data.expand" :key="dex" :data="item" :deep="deep + 1">
        <template #leftIcon>
          <slot name="leftIcon"></slot>
        </template>
        <template #left="{ data: leftData }">
          <slot name="left" :data="leftData"></slot>
        </template>
      </y-gantt-left>
    </template>
  </div>
</template>

<script>
import { computed, inject } from 'vue';
import { useNamespace } from './methods/namespace.js';

export default {
  name: 'YGanttLeft'
};
</script>

<script setup>
const props = defineProps({
  data: {
    required: true,
    type: Object
  },
  deep: {
    type: Number,
    default: 0
  }
});

const handleExpandChange = () => {
  // eslint-disable-next-line vue/no-mutating-props
  props.data.expand = !props.data.expand;
};

const { leftProp, childrenField } = inject('gantt');

const ns = useNamespace('gantt-left');

const paddingLeft = computed(() => (props.deep > 0 ? '10px' : '0'));

const { handleContextmenu, handleClick } = inject('event');

const leftRowClick = (e) => {
  // eslint-disable-next-line vue/no-mutating-props
  props.data.expand = !props.data.expand;
  handleClick(e, props.data);
};

const rangeContextmenu = (event, data) => {
  handleContextmenu(event, data);
};
</script>

<style lang="less" scoped>
.rt-gantt-left {
  padding-left: v-bind(paddingLeft);
}
</style>
