/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/06/18 10:04:56
 * @description BombEffect Tests
 * @version 1.0
 * */

import {
  buildModuleUrl,
  BoxEmitter,
  Cartesian2,
  Cartesian3,
  defaultValue,
  HeadingPitchRoll,
  Matrix4,
  ParticleBurst,
  ParticleSystem,
  Quaternion,
  TranslationRotationScale
} from 'cesium';
import AbstractPrimitive from './AbstractPrimitive';

const PARTICLE_OPTIONS = {
  emissionRate: 5.0,
  minimumParticleLife: 1,
  maximumParticleLife: 6,
  minimumSpeed: 1.0,
  maximumSpeed: 4.0,
  startScale: 5.0,
  endScale: 10.0,
  lifetime: 10.0,
  minimumMass: 1.0,
  maximumMass: 10
};
export default class BombEffectPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    // this._image = defaultValue(options.image, buildModuleUrl('Assets/Textures/fire.png'));
    this._image = defaultValue(options.image, '/Cesium/Assets/Textures/fire.png');
    this._particleOptions = defaultValue(options.particleOptions, PARTICLE_OPTIONS);
    this._particleSize = defaultValue(options.particleSize, 5);
    this._emitter = defaultValue(options.emitter, new BoxEmitter(new Cartesian3(0.1, 0.1, 0.1)));

    this._update = true;
    this._matrixUpdate = false;
    this._modelMatrix = undefined;
    this._bursts = undefined;
    this._bursts = [
      new ParticleBurst({
        time: 5.0,
        minimum: 10,
        maximum: 100
      }),
      new ParticleBurst({
        time: 10.0,
        minimum: 50,
        maximum: 100
      }),
      new ParticleBurst({
        time: 15.0,
        minimum: 200,
        maximum: 300
      })
    ];
  }

  update(frameState) {
    if (!this.show) {
      return;
    }
    if (this._update) {
      this._update = false;
      this._primitive = this._primitive && this._primitive.destroy();
      if (this._image) {
        this._primitive = new ParticleSystem({
          ...this._particleOptions,
          image: this._image,
          imageSize: new Cartesian2(this._particleSize, this._particleSize),
          emitter: this._emitter,
          bursts: this._bursts,
          emitterModelMatrix: computeEmitterModelMatrix()
        });
      }
    }

    if (this._matrixUpdate && this._primitive) {
      this._matrixUpdate = false;
      this._primitive.modelMatrix = this._modelMatrix;
    }

    this._primitive?.update(frameState);
  }

  set modelMatrix(value) {
    if (Matrix4.equals(this._modelMatrix, value)) {
      return;
    }
    this._modelMatrix = value;
    this._matrixUpdate = true;
  }

  set image(value) {
    if (this._image === value) {
      return;
    }
    this._image = value;
    this._update = true;
  }

  set emitter(value) {
    if (this._emitter === value) {
      return;
    }
    if (this._primitive) {
      this._primitive.emitter = value;
    }
    this._emitter = value;
  }

  // 每次更新都会赋值（会不会影响性能）
  set particleOptions(value) {
    this._particleOptions = {
      ...this._particleOptions,
      ...value
    };
    if (this._primitive) {
      Object.keys(this._particleOptions).forEach((key) => {
        this._primitive[key] = this._particleOptions[key];
      });
    }
  }

  set particleSize(value) {
    if (this._particleSize === value) {
      return;
    }
    this._particleSize = value;
    if (this._primitive) {
      this._primitive.minimumImageSize.x = value;
      this._primitive.minimumImageSize.y = value;
      this._primitive.maximumImageSize.x = value;
      this._primitive.maximumImageSize.y = value;
    }
  }
}

const translation = new Cartesian3();
const rotation = new Quaternion();
let hpr = new HeadingPitchRoll();
const trs = new TranslationRotationScale();

function computeEmitterModelMatrix() {
  const emitterModelMatrix = new Matrix4();
  hpr = HeadingPitchRoll.fromDegrees(0.0, 0.0, 0.0, hpr);
  //   trs.translation = Cartesian3.fromElements(2.5, 4.0, 1.0, translation);
  trs.rotation = Quaternion.fromHeadingPitchRoll(hpr, rotation);

  return Matrix4.fromTranslationRotationScale(trs, emitterModelMatrix);
}
