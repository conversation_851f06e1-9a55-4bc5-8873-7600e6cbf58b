/**
 * @Author: zhang<PERSON>e
 * @Date: 2023/04/11 10:06
 * @Version: 1.0
 * @Content:
 */

type DataType = Record<string, any>;

type ValueType = ((data: DataType, item: KeyValType) => string) | string;

export interface KeyValType {
  title: string;
  value: ValueType;
  readonly?: boolean; // 是否只读
  slotName?: string; // 插槽名称
  isShow?: ((data: DataType, item: KeyValType) => boolean) | boolean; // 是否展示
  [key: string]: any;
}
