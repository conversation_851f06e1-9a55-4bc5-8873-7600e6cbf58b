uniform vec4 color;
uniform float speed;
uniform float gradient;
uniform float percent;
uniform float number;

czm_material czm_getMaterial(czm_materialInput materialInput) {
    czm_material material = czm_getDefaultMaterial(materialInput);
    vec2 st = materialInput.st;
    float t = fract(czm_frameNumber * speed / 1000.0);
    // t *= (1.0 + percent);
    // float alpha = smoothstep(t- percent, t, st.s) * step(-t, -st.s);

    float tTemp = t;
    float tAdd = t;
    float tTemp2 = t * (1.0 + percent);
    float tAdd2 = t * (1.0 + percent);
    float alpha = 0.0;
    // 'i' : Loop index cannot be compared with non-constant expression
    for (float i = 0.; i < 50.0; i += 1.0) {
        if (i >= number) {
            break;
        }
        alpha = smoothstep(tTemp2 - percent, tTemp2, st.s) * step(-tTemp2, -st.s);
        tTemp -= 1.0 / number;
        tTemp2 = tTemp * (1.0 + percent);
        // tTemp *= (1.0 - 1.0/number + percent);

        if (alpha > 0.0) {
            break;
        }
        alpha = smoothstep(tAdd2 - percent, tAdd2, st.s) * step(-tAdd2, -st.s);
        tAdd += 1.0 / number;
        tAdd2 = tAdd * (1.0 + percent);
        // tAdd *= (1.0 + 1.0/number + percent);
        if (alpha > 0.0) {
            break;
        }
    }

    alpha += gradient;
    material.diffuse = color.rgb;
    material.alpha = alpha;
    return material;
}