import { computed, unref } from 'vue';
/*
 * @Author: songjimin
 * @Date: 2022-04-13 17:54:27
 * @Version: 0.0.1
 * @Content:
 */
const defaultNamespace = 'rt';
const statePrefix = 'is-';
function _bem(namespace, block, blockSurffix, element, modifier) {
  let cls = `${namespace}-${block}`;
  if (blockSurffix) {
    cls += `-${blockSurffix}`;
  }
  if (element) {
    cls += `__${element}`;
  }

  if (modifier) {
    cls += `--${modifier}`;
  }
  return cls;
}
export function useNamespace(block) {
  const namespace = computed(() => defaultNamespace);

  const b = (blockSuffix = '') => _bem(unref(namespace), block, blockSuffix, '', '');

  const e = (element = '') => (element ? _bem(unref(namespace), block, '', element, '') : '');

  const m = (modifier = '') => (modifier ? _bem(unref(namespace), block, '', '', modifier) : '');

  const be = (blockSuffix, element) => (blockSuffix && element ? _bem(unref(namespace), block, blockSuffix, element, '') : '');

  const em = (element, modifier) => (element && modifier ? _bem(unref(namespace), block, '', element, modifier) : '');

  const bm = (blockSuffix, modifier) => (blockSuffix && modifier ? _bem(unref(namespace), block, blockSuffix, '', modifier) : '');

  const bem = (blockSuffix, element, modifier) =>
    blockSuffix && element && modifier ? _bem(unref(namespace), block, blockSuffix, element, modifier) : '';
  const is = (name, ...args) => {
    const state = args.length >= 1 ? args[0] : true;
    return name && state ? `${statePrefix}${name}` : '';
  };
  return {
    b,
    e,
    m,
    be,
    em,
    bm,
    bem,
    is
  };
}
