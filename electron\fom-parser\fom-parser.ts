/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import fs from 'node:fs';
import path from 'node:path';
import fsPromise from 'node:fs/promises';
import { FomStruct, FomMetaData, InteractionClass, ObjectClass } from './fom-type.ts';
import X2js from 'x2js';
const x2json = new X2js();

class DataTypeCollect {
  dataTypeMap: Map<string, InteractionClass>;
  private classificationMap: Map<string, Set<InteractionClass>>;
  constructor() {
    this.dataTypeMap = new Map();
    this.classificationMap = new Map();
  }
  set(key: string, value: { data: InteractionClass[]; metaData: FomMetaData }) {
    if (!this.classificationMap.has(key)) {
      this.classificationMap.set(key, new Set());
    }
    const listData = this.classificationMap.get(key)!;
    value.data.forEach((item) => {
      const _data = Object.assign(item, item.metaData);
      this.dataTypeMap.set(item.name, _data);
      listData.add(_data);
    });
  }
  has(key: string) {
    return this.dataTypeMap.has(key);
  }
  values() {
    return this.dataTypeMap.values();
  }
  get(key: string) {
    this.classificationMap.get(key);
  }
  getByName(name: string) {
    this.dataTypeMap.get(name);
  }
}
// const config = fs.readFileSync(path.join(process.cwd(), '/config/global-config.json'), 'utf-8');

export class FomXmlParse {
  sourcePath = path.join(process.cwd(), '/config/resources/fom');
  resultPath = path.join(process.cwd(), '/config/resources/fom-json');
  private primitiveFomMap: Map<any, any>;
  private objectClassMap: Map<any, any>;
  private interactionMap: Map<string, InteractionClass>;
  private dataTypeMap: DataTypeCollect;
  constructor() {
    this.primitiveFomMap = new Map();
    this.objectClassMap = new Map();
    this.interactionMap = new Map();
    this.dataTypeMap = new DataTypeCollect();
  }

  /**
   * 加载文件
   * @param fileName
   */
  loadFile(fileName: string) {
    const name = fileName.replace('.xml', '');
    const xmlString = fs.readFileSync(path.join(this.sourcePath, fileName)).toString();
    const xmlJson = x2json.xml2js(xmlString) as FomStruct;
    this.primitiveFomMap.set(name, xmlJson);
    this.moduleParser(name, xmlJson.objectModel);
  }

  /**
   * 加载文件夹下的FOM文件
   * @returns {Promise<void>}
   */
  async loadFolder() {
    const fomFileNames = await fsPromise.readdir(this.sourcePath);
    fomFileNames.forEach((fileName) => {
      if (fileName.includes('.xml')) {
        this.loadFile(fileName);
      }
    });
    this.writeInteractionJson();
  }

  /**
   * 模块解析
   * @param moduleName
   * @param data
   */
  moduleParser(moduleName: string, data: FomStruct['objectModel']) {
    const metaData = {
      from: moduleName
    };
    // this.objectsParse([data.objects.objectClass], metaData);
    //
    // this.dataTypeParse(data.dataTypes?.basicDataRepresentations, metaData);
    // this.dataTypeParse(data.dataTypes?.simpleDataTypes, metaData);
    // this.dataTypeParse(data.dataTypes?.enumeratedDataTypes, metaData);
    // this.dataTypeParse(data.dataTypes?.arrayDataTypes, metaData);
    // this.dataTypeParse(data.dataTypes?.fixedRecordDataTypes, metaData);
    // this.dataTypeParse(data.dataTypes?.variantRecordDataTypes, metaData);

    this.interactionParse(data.interactions.interactionClass, metaData);
  }

  /**
   * object解析
   * @param data
   * @param metaData
   */
  objectsParse(data: ObjectClass[], metaData: FomMetaData) {
    if (!data) return;
    data.forEach((item) => {
      const { objectClass, ...others } = item;
      if (!this.objectClassMap.has(item.name) || others.attribute?.length) {
        this.objectClassMap.set(item.name, { ...others, metaData });
      }
      if (objectClass) {
        this.objectsParse(objectClass, { ...metaData, parent: item.name });
      }
    });
  }

  /**
   * 数据类型解析
   * @param data
   * @param metaData
   */
  dataTypeParse(data: any, metaData: FomMetaData) {
    if (!data) return;
    for (const dataKey in data) {
      this.dataTypeMap.set(dataKey, { data: data[dataKey], metaData: { ...metaData, type: dataKey } });
    }
  }

  /**
   * 交互类解析
   */
  interactionParse(data: InteractionClass | InteractionClass[], metaData: FomMetaData) {
    if (!data) return;

    if (!Array.isArray(data)) {
      data = [data];
    }
    data.forEach((item) => {
      const { interactionClass, ...others } = item;
      this.interactionMap.set(item.name, Object.assign(others, { metaData }));
      if (interactionClass) {
        this.interactionParse(interactionClass, { ...metaData, parent: item.name });
      }
    });
  }
  getInteractionByKey(key: string[]) {
    return key
      .map((item) => {
        const res = this.interactionMap.get(item);
        if (res) {
          return {
            className: res.name,
            semantic: res.semantics
          };
        }
      })
      .filter(Boolean);
  }

  writeInteractionJson() {
    const interactionJsonPath = path.join(this.resultPath, '/interaction.json');
    if (fs.existsSync(interactionJsonPath)) return;
    const result: Record<string, any> = {};
    this.interactionMap.forEach((item) => {
      result[item.name] = item;
    });
    fs.writeFileSync(interactionJsonPath, JSON.stringify(result, null, 2));
  }
}
