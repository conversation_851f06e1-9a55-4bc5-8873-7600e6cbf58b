<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-12 10:53
* @Version : 1.0
* @Content : situation
-->
<template>
  <div class="sim-ui-demo">
    <sim-drawing-board v-model:is-show="show" />
  </div>
</template>

<script lang="ts">
export default defineComponent({
  name: 'SimDrawingBoardDemo'
});
</script>

<script lang="ts" setup>
import { SimDrawingBoard } from '../index';
import { ref } from 'vue';

const show = ref(true);
</script>

<style scoped lang="less">
.sim-ui-demo {
  width: 100%;
  height: 100%;
}
</style>
