{"version": 3, "file": "PolylinePipeline-b95ae532.js", "sources": ["../../../../Source/Core/PolylinePipeline.js"], "sourcesContent": ["import Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport Ellipsoid from \"./Ellipsoid.js\";\nimport EllipsoidGeodesic from \"./EllipsoidGeodesic.js\";\nimport EllipsoidRhumbLine from \"./EllipsoidRhumbLine.js\";\nimport IntersectionTests from \"./IntersectionTests.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix4 from \"./Matrix4.js\";\nimport Plane from \"./Plane.js\";\n\n/**\n * @private\n */\nconst PolylinePipeline = {};\n\nPolylinePipeline.numberOfPoints = function (p0, p1, minDistance) {\n  const distance = Cartesian3.distance(p0, p1);\n  return Math.ceil(distance / minDistance);\n};\n\nPolylinePipeline.numberOfPointsRhumbLine = function (p0, p1, granularity) {\n  const radiansDistanceSquared =\n    Math.pow(p0.longitude - p1.longitude, 2) +\n    Math.pow(p0.latitude - p1.latitude, 2);\n\n  return Math.max(\n    1,\n    Math.ceil(Math.sqrt(radiansDistanceSquared / (granularity * granularity)))\n  );\n};\n\nconst cartoScratch = new Cartographic();\nPolylinePipeline.extractHeights = function (positions, ellipsoid) {\n  const length = positions.length;\n  const heights = new Array(length);\n  for (let i = 0; i < length; i++) {\n    const p = positions[i];\n    heights[i] = ellipsoid.cartesianToCartographic(p, cartoScratch).height;\n  }\n  return heights;\n};\n\nconst wrapLongitudeInversMatrix = new Matrix4();\nconst wrapLongitudeOrigin = new Cartesian3();\nconst wrapLongitudeXZNormal = new Cartesian3();\nconst wrapLongitudeXZPlane = new Plane(Cartesian3.UNIT_X, 0.0);\nconst wrapLongitudeYZNormal = new Cartesian3();\nconst wrapLongitudeYZPlane = new Plane(Cartesian3.UNIT_X, 0.0);\nconst wrapLongitudeIntersection = new Cartesian3();\nconst wrapLongitudeOffset = new Cartesian3();\n\nconst subdivideHeightsScratchArray = [];\n\nfunction subdivideHeights(numPoints, h0, h1) {\n  const heights = subdivideHeightsScratchArray;\n  heights.length = numPoints;\n\n  let i;\n  if (h0 === h1) {\n    for (i = 0; i < numPoints; i++) {\n      heights[i] = h0;\n    }\n    return heights;\n  }\n\n  const dHeight = h1 - h0;\n  const heightPerVertex = dHeight / numPoints;\n\n  for (i = 0; i < numPoints; i++) {\n    const h = h0 + i * heightPerVertex;\n    heights[i] = h;\n  }\n\n  return heights;\n}\n\nconst carto1 = new Cartographic();\nconst carto2 = new Cartographic();\nconst cartesian = new Cartesian3();\nconst scaleFirst = new Cartesian3();\nconst scaleLast = new Cartesian3();\nconst ellipsoidGeodesic = new EllipsoidGeodesic();\nlet ellipsoidRhumb = new EllipsoidRhumbLine();\n\n//Returns subdivided line scaled to ellipsoid surface starting at p1 and ending at p2.\n//Result includes p1, but not include p2.  This function is called for a sequence of line segments,\n//and this prevents duplication of end point.\nfunction generateCartesianArc(\n  p0,\n  p1,\n  minDistance,\n  ellipsoid,\n  h0,\n  h1,\n  array,\n  offset\n) {\n  const first = ellipsoid.scaleToGeodeticSurface(p0, scaleFirst);\n  const last = ellipsoid.scaleToGeodeticSurface(p1, scaleLast);\n  const numPoints = PolylinePipeline.numberOfPoints(p0, p1, minDistance);\n  const start = ellipsoid.cartesianToCartographic(first, carto1);\n  const end = ellipsoid.cartesianToCartographic(last, carto2);\n  const heights = subdivideHeights(numPoints, h0, h1);\n\n  ellipsoidGeodesic.setEndPoints(start, end);\n  const surfaceDistanceBetweenPoints =\n    ellipsoidGeodesic.surfaceDistance / numPoints;\n\n  let index = offset;\n  start.height = h0;\n  let cart = ellipsoid.cartographicToCartesian(start, cartesian);\n  Cartesian3.pack(cart, array, index);\n  index += 3;\n\n  for (let i = 1; i < numPoints; i++) {\n    const carto = ellipsoidGeodesic.interpolateUsingSurfaceDistance(\n      i * surfaceDistanceBetweenPoints,\n      carto2\n    );\n    carto.height = heights[i];\n    cart = ellipsoid.cartographicToCartesian(carto, cartesian);\n    Cartesian3.pack(cart, array, index);\n    index += 3;\n  }\n\n  return index;\n}\n\n//Returns subdivided line scaled to ellipsoid surface starting at p1 and ending at p2.\n//Result includes p1, but not include p2.  This function is called for a sequence of line segments,\n//and this prevents duplication of end point.\nfunction generateCartesianRhumbArc(\n  p0,\n  p1,\n  granularity,\n  ellipsoid,\n  h0,\n  h1,\n  array,\n  offset\n) {\n  const start = ellipsoid.cartesianToCartographic(p0, carto1);\n  const end = ellipsoid.cartesianToCartographic(p1, carto2);\n  const numPoints = PolylinePipeline.numberOfPointsRhumbLine(\n    start,\n    end,\n    granularity\n  );\n  start.height = 0.0;\n  end.height = 0.0;\n  const heights = subdivideHeights(numPoints, h0, h1);\n\n  if (!ellipsoidRhumb.ellipsoid.equals(ellipsoid)) {\n    ellipsoidRhumb = new EllipsoidRhumbLine(undefined, undefined, ellipsoid);\n  }\n  ellipsoidRhumb.setEndPoints(start, end);\n  const surfaceDistanceBetweenPoints =\n    ellipsoidRhumb.surfaceDistance / numPoints;\n\n  let index = offset;\n  start.height = h0;\n  let cart = ellipsoid.cartographicToCartesian(start, cartesian);\n  Cartesian3.pack(cart, array, index);\n  index += 3;\n\n  for (let i = 1; i < numPoints; i++) {\n    const carto = ellipsoidRhumb.interpolateUsingSurfaceDistance(\n      i * surfaceDistanceBetweenPoints,\n      carto2\n    );\n    carto.height = heights[i];\n    cart = ellipsoid.cartographicToCartesian(carto, cartesian);\n    Cartesian3.pack(cart, array, index);\n    index += 3;\n  }\n\n  return index;\n}\n\n/**\n * Breaks a {@link Polyline} into segments such that it does not cross the &plusmn;180 degree meridian of an ellipsoid.\n *\n * @param {Cartesian3[]} positions The polyline's Cartesian positions.\n * @param {Matrix4} [modelMatrix=Matrix4.IDENTITY] The polyline's model matrix. Assumed to be an affine\n * transformation matrix, where the upper left 3x3 elements are a rotation matrix, and\n * the upper three elements in the fourth column are the translation.  The bottom row is assumed to be [0, 0, 0, 1].\n * The matrix is not verified to be in the proper form.\n * @returns {Object} An object with a <code>positions</code> property that is an array of positions and a\n * <code>segments</code> property.\n *\n *\n * @example\n * const polylines = new Cesium.PolylineCollection();\n * const polyline = polylines.add(...);\n * const positions = polyline.positions;\n * const modelMatrix = polylines.modelMatrix;\n * const segments = Cesium.PolylinePipeline.wrapLongitude(positions, modelMatrix);\n *\n * @see PolygonPipeline.wrapLongitude\n * @see Polyline\n * @see PolylineCollection\n */\nPolylinePipeline.wrapLongitude = function (positions, modelMatrix) {\n  const cartesians = [];\n  const segments = [];\n\n  if (defined(positions) && positions.length > 0) {\n    modelMatrix = defaultValue(modelMatrix, Matrix4.IDENTITY);\n    const inverseModelMatrix = Matrix4.inverseTransformation(\n      modelMatrix,\n      wrapLongitudeInversMatrix\n    );\n\n    const origin = Matrix4.multiplyByPoint(\n      inverseModelMatrix,\n      Cartesian3.ZERO,\n      wrapLongitudeOrigin\n    );\n    const xzNormal = Cartesian3.normalize(\n      Matrix4.multiplyByPointAsVector(\n        inverseModelMatrix,\n        Cartesian3.UNIT_Y,\n        wrapLongitudeXZNormal\n      ),\n      wrapLongitudeXZNormal\n    );\n    const xzPlane = Plane.fromPointNormal(\n      origin,\n      xzNormal,\n      wrapLongitudeXZPlane\n    );\n    const yzNormal = Cartesian3.normalize(\n      Matrix4.multiplyByPointAsVector(\n        inverseModelMatrix,\n        Cartesian3.UNIT_X,\n        wrapLongitudeYZNormal\n      ),\n      wrapLongitudeYZNormal\n    );\n    const yzPlane = Plane.fromPointNormal(\n      origin,\n      yzNormal,\n      wrapLongitudeYZPlane\n    );\n\n    let count = 1;\n    cartesians.push(Cartesian3.clone(positions[0]));\n    let prev = cartesians[0];\n\n    const length = positions.length;\n    for (let i = 1; i < length; ++i) {\n      const cur = positions[i];\n\n      // intersects the IDL if either endpoint is on the negative side of the yz-plane\n      if (\n        Plane.getPointDistance(yzPlane, prev) < 0.0 ||\n        Plane.getPointDistance(yzPlane, cur) < 0.0\n      ) {\n        // and intersects the xz-plane\n        const intersection = IntersectionTests.lineSegmentPlane(\n          prev,\n          cur,\n          xzPlane,\n          wrapLongitudeIntersection\n        );\n        if (defined(intersection)) {\n          // move point on the xz-plane slightly away from the plane\n          const offset = Cartesian3.multiplyByScalar(\n            xzNormal,\n            5.0e-9,\n            wrapLongitudeOffset\n          );\n          if (Plane.getPointDistance(xzPlane, prev) < 0.0) {\n            Cartesian3.negate(offset, offset);\n          }\n\n          cartesians.push(\n            Cartesian3.add(intersection, offset, new Cartesian3())\n          );\n          segments.push(count + 1);\n\n          Cartesian3.negate(offset, offset);\n          cartesians.push(\n            Cartesian3.add(intersection, offset, new Cartesian3())\n          );\n          count = 1;\n        }\n      }\n\n      cartesians.push(Cartesian3.clone(positions[i]));\n      count++;\n\n      prev = cur;\n    }\n\n    segments.push(count);\n  }\n\n  return {\n    positions: cartesians,\n    lengths: segments,\n  };\n};\n\n/**\n * Subdivides polyline and raises all points to the specified height.  Returns an array of numbers to represent the positions.\n * @param {Object} options Object with the following properties:\n * @param {Cartesian3[]} options.positions The array of type {Cartesian3} representing positions.\n * @param {Number|Number[]} [options.height=0.0] A number or array of numbers representing the heights of each position.\n * @param {Number} [options.granularity = CesiumMath.RADIANS_PER_DEGREE] The distance, in radians, between each latitude and longitude. Determines the number of positions in the buffer.\n * @param {Ellipsoid} [options.ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the positions lie.\n * @returns {Number[]} A new array of positions of type {Number} that have been subdivided and raised to the surface of the ellipsoid.\n *\n * @example\n * const positions = Cesium.Cartesian3.fromDegreesArray([\n *   -105.0, 40.0,\n *   -100.0, 38.0,\n *   -105.0, 35.0,\n *   -100.0, 32.0\n * ]);\n * const surfacePositions = Cesium.PolylinePipeline.generateArc({\n *   positons: positions\n * });\n */\nPolylinePipeline.generateArc = function (options) {\n  if (!defined(options)) {\n    options = {};\n  }\n  const positions = options.positions;\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(positions)) {\n    throw new DeveloperError(\"options.positions is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const length = positions.length;\n  const ellipsoid = defaultValue(options.ellipsoid, Ellipsoid.WGS84);\n  let height = defaultValue(options.height, 0);\n  const hasHeightArray = Array.isArray(height);\n\n  if (length < 1) {\n    return [];\n  } else if (length === 1) {\n    const p = ellipsoid.scaleToGeodeticSurface(positions[0], scaleFirst);\n    height = hasHeightArray ? height[0] : height;\n    if (height !== 0) {\n      const n = ellipsoid.geodeticSurfaceNormal(p, cartesian);\n      Cartesian3.multiplyByScalar(n, height, n);\n      Cartesian3.add(p, n, p);\n    }\n\n    return [p.x, p.y, p.z];\n  }\n\n  let minDistance = options.minDistance;\n  if (!defined(minDistance)) {\n    const granularity = defaultValue(\n      options.granularity,\n      CesiumMath.RADIANS_PER_DEGREE\n    );\n    minDistance = CesiumMath.chordLength(granularity, ellipsoid.maximumRadius);\n  }\n\n  let numPoints = 0;\n  let i;\n\n  for (i = 0; i < length - 1; i++) {\n    numPoints += PolylinePipeline.numberOfPoints(\n      positions[i],\n      positions[i + 1],\n      minDistance\n    );\n  }\n\n  const arrayLength = (numPoints + 1) * 3;\n  const newPositions = new Array(arrayLength);\n  let offset = 0;\n\n  for (i = 0; i < length - 1; i++) {\n    const p0 = positions[i];\n    const p1 = positions[i + 1];\n\n    const h0 = hasHeightArray ? height[i] : height;\n    const h1 = hasHeightArray ? height[i + 1] : height;\n\n    offset = generateCartesianArc(\n      p0,\n      p1,\n      minDistance,\n      ellipsoid,\n      h0,\n      h1,\n      newPositions,\n      offset\n    );\n  }\n\n  subdivideHeightsScratchArray.length = 0;\n\n  const lastPoint = positions[length - 1];\n  const carto = ellipsoid.cartesianToCartographic(lastPoint, carto1);\n  carto.height = hasHeightArray ? height[length - 1] : height;\n  const cart = ellipsoid.cartographicToCartesian(carto, cartesian);\n  Cartesian3.pack(cart, newPositions, arrayLength - 3);\n\n  return newPositions;\n};\n\nconst scratchCartographic0 = new Cartographic();\nconst scratchCartographic1 = new Cartographic();\n\n/**\n * Subdivides polyline and raises all points to the specified height using Rhumb lines.  Returns an array of numbers to represent the positions.\n * @param {Object} options Object with the following properties:\n * @param {Cartesian3[]} options.positions The array of type {Cartesian3} representing positions.\n * @param {Number|Number[]} [options.height=0.0] A number or array of numbers representing the heights of each position.\n * @param {Number} [options.granularity = CesiumMath.RADIANS_PER_DEGREE] The distance, in radians, between each latitude and longitude. Determines the number of positions in the buffer.\n * @param {Ellipsoid} [options.ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the positions lie.\n * @returns {Number[]} A new array of positions of type {Number} that have been subdivided and raised to the surface of the ellipsoid.\n *\n * @example\n * const positions = Cesium.Cartesian3.fromDegreesArray([\n *   -105.0, 40.0,\n *   -100.0, 38.0,\n *   -105.0, 35.0,\n *   -100.0, 32.0\n * ]);\n * const surfacePositions = Cesium.PolylinePipeline.generateRhumbArc({\n *   positons: positions\n * });\n */\nPolylinePipeline.generateRhumbArc = function (options) {\n  if (!defined(options)) {\n    options = {};\n  }\n  const positions = options.positions;\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(positions)) {\n    throw new DeveloperError(\"options.positions is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const length = positions.length;\n  const ellipsoid = defaultValue(options.ellipsoid, Ellipsoid.WGS84);\n  let height = defaultValue(options.height, 0);\n  const hasHeightArray = Array.isArray(height);\n\n  if (length < 1) {\n    return [];\n  } else if (length === 1) {\n    const p = ellipsoid.scaleToGeodeticSurface(positions[0], scaleFirst);\n    height = hasHeightArray ? height[0] : height;\n    if (height !== 0) {\n      const n = ellipsoid.geodeticSurfaceNormal(p, cartesian);\n      Cartesian3.multiplyByScalar(n, height, n);\n      Cartesian3.add(p, n, p);\n    }\n\n    return [p.x, p.y, p.z];\n  }\n\n  const granularity = defaultValue(\n    options.granularity,\n    CesiumMath.RADIANS_PER_DEGREE\n  );\n\n  let numPoints = 0;\n  let i;\n\n  let c0 = ellipsoid.cartesianToCartographic(\n    positions[0],\n    scratchCartographic0\n  );\n  let c1;\n  for (i = 0; i < length - 1; i++) {\n    c1 = ellipsoid.cartesianToCartographic(\n      positions[i + 1],\n      scratchCartographic1\n    );\n    numPoints += PolylinePipeline.numberOfPointsRhumbLine(c0, c1, granularity);\n    c0 = Cartographic.clone(c1, scratchCartographic0);\n  }\n\n  const arrayLength = (numPoints + 1) * 3;\n  const newPositions = new Array(arrayLength);\n  let offset = 0;\n\n  for (i = 0; i < length - 1; i++) {\n    const p0 = positions[i];\n    const p1 = positions[i + 1];\n\n    const h0 = hasHeightArray ? height[i] : height;\n    const h1 = hasHeightArray ? height[i + 1] : height;\n\n    offset = generateCartesianRhumbArc(\n      p0,\n      p1,\n      granularity,\n      ellipsoid,\n      h0,\n      h1,\n      newPositions,\n      offset\n    );\n  }\n\n  subdivideHeightsScratchArray.length = 0;\n\n  const lastPoint = positions[length - 1];\n  const carto = ellipsoid.cartesianToCartographic(lastPoint, carto1);\n  carto.height = hasHeightArray ? height[length - 1] : height;\n  const cart = ellipsoid.cartographicToCartesian(carto, cartesian);\n  Cartesian3.pack(cart, newPositions, arrayLength - 3);\n\n  return newPositions;\n};\n\n/**\n * Subdivides polyline and raises all points to the specified height. Returns an array of new {Cartesian3} positions.\n * @param {Object} options Object with the following properties:\n * @param {Cartesian3[]} options.positions The array of type {Cartesian3} representing positions.\n * @param {Number|Number[]} [options.height=0.0] A number or array of numbers representing the heights of each position.\n * @param {Number} [options.granularity = CesiumMath.RADIANS_PER_DEGREE] The distance, in radians, between each latitude and longitude. Determines the number of positions in the buffer.\n * @param {Ellipsoid} [options.ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the positions lie.\n * @returns {Cartesian3[]} A new array of cartesian3 positions that have been subdivided and raised to the surface of the ellipsoid.\n *\n * @example\n * const positions = Cesium.Cartesian3.fromDegreesArray([\n *   -105.0, 40.0,\n *   -100.0, 38.0,\n *   -105.0, 35.0,\n *   -100.0, 32.0\n * ]);\n * const surfacePositions = Cesium.PolylinePipeline.generateCartesianArc({\n *   positons: positions\n * });\n */\nPolylinePipeline.generateCartesianArc = function (options) {\n  const numberArray = PolylinePipeline.generateArc(options);\n  const size = numberArray.length / 3;\n  const newPositions = new Array(size);\n  for (let i = 0; i < size; i++) {\n    newPositions[i] = Cartesian3.unpack(numberArray, i * 3);\n  }\n  return newPositions;\n};\n\n/**\n * Subdivides polyline and raises all points to the specified height using Rhumb Lines. Returns an array of new {Cartesian3} positions.\n * @param {Object} options Object with the following properties:\n * @param {Cartesian3[]} options.positions The array of type {Cartesian3} representing positions.\n * @param {Number|Number[]} [options.height=0.0] A number or array of numbers representing the heights of each position.\n * @param {Number} [options.granularity = CesiumMath.RADIANS_PER_DEGREE] The distance, in radians, between each latitude and longitude. Determines the number of positions in the buffer.\n * @param {Ellipsoid} [options.ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the positions lie.\n * @returns {Cartesian3[]} A new array of cartesian3 positions that have been subdivided and raised to the surface of the ellipsoid.\n *\n * @example\n * const positions = Cesium.Cartesian3.fromDegreesArray([\n *   -105.0, 40.0,\n *   -100.0, 38.0,\n *   -105.0, 35.0,\n *   -100.0, 32.0\n * ]);\n * const surfacePositions = Cesium.PolylinePipeline.generateCartesianRhumbArc({\n *   positons: positions\n * });\n */\nPolylinePipeline.generateCartesianRhumbArc = function (options) {\n  const numberArray = PolylinePipeline.generateRhumbArc(options);\n  const size = numberArray.length / 3;\n  const newPositions = new Array(size);\n  for (let i = 0; i < size; i++) {\n    newPositions[i] = Cartesian3.unpack(numberArray, i * 3);\n  }\n  return newPositions;\n};\nexport default PolylinePipeline;\n"], "names": ["PolylinePipeline", "p0", "p1", "minDistance", "distance", "Cartesian3", "Math", "ceil", "granularity", "radiansDistanceSquared", "pow", "longitude", "latitude", "max", "sqrt", "cartoS<PERSON>ch", "Cartographic", "extractHeights", "positions", "ellipsoid", "length", "heights", "Array", "i", "p", "cartesianToCartographic", "height", "wrapLongitudeInversMatrix", "Matrix4", "wrapLongitude<PERSON><PERSON>in", "wrapLongitudeXZNormal", "wrapLongitudeXZPlane", "Plane", "UNIT_X", "wrapLongitudeYZNormal", "wrapLongitudeYZPlane", "wrapLongitudeIntersection", "wrapLongitudeOffset", "subdivideHeightsScratchArray", "subdivideHeights", "numPoints", "h0", "h1", "heightPerVertex", "h", "carto1", "carto2", "cartesian", "scaleFirst", "scaleLast", "ellipsoidGeodesic", "EllipsoidGeodesic", "ellipsoidRhumb", "EllipsoidRhumbLine", "generateCartesianArc", "array", "offset", "first", "scaleToGeodeticSurface", "last", "numberOfPoints", "start", "end", "setEndPoints", "surfaceDistanceBetweenPoints", "surfaceDistance", "index", "cart", "cartographicToCartesian", "pack", "carto", "interpolateUsingSurfaceDistance", "generateCartesianRhumbArc", "numberOfPointsRhumbLine", "equals", "undefined", "wrapLongitude", "modelMatrix", "cartesians", "segments", "defined", "defaultValue", "IDENTITY", "inverseModelMatrix", "inverseTransformation", "origin", "multiplyByPoint", "ZERO", "xzNormal", "normalize", "multiplyByPointAsVector", "UNIT_Y", "xzPlane", "fromPointNormal", "yzNormal", "yzPlane", "count", "push", "clone", "prev", "cur", "getPointDistance", "intersection", "IntersectionTests", "lineSegmentPlane", "multiplyByScalar", "negate", "add", "lengths", "generateArc", "options", "DeveloperError", "Ellipsoid", "WGS84", "hasHeightArray", "isArray", "n", "geodeticSurfaceNormal", "x", "y", "z", "CesiumMath", "RADIANS_PER_DEGREE", "chord<PERSON>ength", "maximumRadius", "array<PERSON>ength", "newPositions", "lastPoint", "scratchCartographic0", "scratchCartographic1", "generateRhumbArc", "c1", "c0", "numberArray", "size", "unpack"], "mappings": "8QAgBM,MAAAA,EAAmB,CAEzBA,eAAkC,SAAUC,EAAIC,EAAIC,GAClD,MAAMC,EAAWC,EAAUA,WAACD,SAASH,EAAIC,GACzC,OAAOI,KAAKC,KAAKH,EAAWD,EAC9B,EAEAH,wBAA2C,SAAUC,EAAIC,EAAIM,GAC3D,MAAMC,EACJH,KAAKI,IAAIT,EAAGU,UAAYT,EAAGS,UAAW,GACtCL,KAAKI,IAAIT,EAAGW,SAAWV,EAAGU,SAAU,GAEtC,OAAON,KAAKO,IACV,EACAP,KAAKC,KAAKD,KAAKQ,KAAKL,GAA0BD,EAAcA,KAEhE,GAEMO,EAAe,IAAIC,EAAAA,aACzBhB,EAAiBiB,eAAiB,SAAUC,EAAWC,GACrD,MAAMC,EAASF,EAAUE,OACnBC,EAAU,IAAIC,MAAMF,GAC1B,IAAK,IAAIG,EAAI,EAAGA,EAAIH,EAAQG,IAAK,CAC/B,MAAMC,EAAIN,EAAUK,GACpBF,EAAQE,GAAKJ,EAAUM,wBAAwBD,EAAGT,GAAcW,MACjE,CACD,OAAOL,CACT,EAEA,MAAMM,EAA4B,IAAIC,EAAAA,QAChCC,EAAsB,IAAIxB,EAAAA,WAC1ByB,EAAwB,IAAIzB,EAAAA,WAC5B0B,EAAuB,IAAIC,EAAKA,MAAC3B,EAAUA,WAAC4B,OAAQ,GACpDC,EAAwB,IAAI7B,EAAAA,WAC5B8B,EAAuB,IAAIH,EAAKA,MAAC3B,EAAUA,WAAC4B,OAAQ,GACpDG,EAA4B,IAAI/B,EAAAA,WAChCgC,EAAsB,IAAIhC,EAAAA,WAE1BiC,EAA+B,GAErC,SAASC,EAAiBC,EAAWC,EAAIC,GACvC,MAAMrB,EAAUiB,EAGhB,IAAIf,EACJ,GAHAF,EAAQD,OAASoB,EAGbC,IAAOC,EAAI,CACb,IAAKnB,EAAI,EAAGA,EAAIiB,EAAWjB,IACzBF,EAAQE,GAAKkB,EAEf,OAAOpB,CACR,CAED,MACMsB,GADUD,EAAKD,GACaD,EAElC,IAAKjB,EAAI,EAAGA,EAAIiB,EAAWjB,IAAK,CAC9B,MAAMqB,EAAIH,EAAKlB,EAAIoB,EACnBtB,EAAQE,GAAKqB,CACd,CAED,OAAOvB,CACT,CAEA,MAAMwB,EAAS,IAAI7B,EAAAA,aACb8B,EAAS,IAAI9B,EAAAA,aACb+B,EAAY,IAAI1C,EAAAA,WAChB2C,EAAa,IAAI3C,EAAAA,WACjB4C,EAAY,IAAI5C,EAAAA,WAChB6C,EAAoB,IAAIC,EAAAA,kBAC9B,IAAIC,EAAiB,IAAIC,EAAAA,mBAKzB,SAASC,EACPrD,EACAC,EACAC,EACAgB,EACAsB,EACAC,EACAa,EACAC,GAEA,MAAMC,EAAQtC,EAAUuC,uBAAuBzD,EAAI+C,GAC7CW,EAAOxC,EAAUuC,uBAAuBxD,EAAI+C,GAC5CT,EAAYxC,EAAiB4D,eAAe3D,EAAIC,EAAIC,GACpD0D,EAAQ1C,EAAUM,wBAAwBgC,EAAOZ,GACjDiB,EAAM3C,EAAUM,wBAAwBkC,EAAMb,GAC9CzB,EAAUkB,EAAiBC,EAAWC,EAAIC,GAEhDQ,EAAkBa,aAAaF,EAAOC,GACtC,MAAME,EACJd,EAAkBe,gBAAkBzB,EAEtC,IAAI0B,EAAQV,EACZK,EAAMnC,OAASe,EACf,IAAI0B,EAAOhD,EAAUiD,wBAAwBP,EAAOd,GACpD1C,EAAAA,WAAWgE,KAAKF,EAAMZ,EAAOW,GAC7BA,GAAS,EAET,IAAK,IAAI3C,EAAI,EAAGA,EAAIiB,EAAWjB,IAAK,CAClC,MAAM+C,EAAQpB,EAAkBqB,gCAC9BhD,EAAIyC,EACJlB,GAEFwB,EAAM5C,OAASL,EAAQE,GACvB4C,EAAOhD,EAAUiD,wBAAwBE,EAAOvB,GAChD1C,EAAAA,WAAWgE,KAAKF,EAAMZ,EAAOW,GAC7BA,GAAS,CACV,CAED,OAAOA,CACT,CAKA,SAASM,EACPvE,EACAC,EACAM,EACAW,EACAsB,EACAC,EACAa,EACAC,GAEA,MAAMK,EAAQ1C,EAAUM,wBAAwBxB,EAAI4C,GAC9CiB,EAAM3C,EAAUM,wBAAwBvB,EAAI4C,GAC5CN,EAAYxC,EAAiByE,wBACjCZ,EACAC,EACAtD,GAEFqD,EAAMnC,OAAS,EACfoC,EAAIpC,OAAS,EACb,MAAML,EAAUkB,EAAiBC,EAAWC,EAAIC,GAE3CU,EAAejC,UAAUuD,OAAOvD,KACnCiC,EAAiB,IAAIC,EAAkBA,wBAACsB,OAAWA,EAAWxD,IAEhEiC,EAAeW,aAAaF,EAAOC,GACnC,MAAME,EACJZ,EAAea,gBAAkBzB,EAEnC,IAAI0B,EAAQV,EACZK,EAAMnC,OAASe,EACf,IAAI0B,EAAOhD,EAAUiD,wBAAwBP,EAAOd,GACpD1C,EAAAA,WAAWgE,KAAKF,EAAMZ,EAAOW,GAC7BA,GAAS,EAET,IAAK,IAAI3C,EAAI,EAAGA,EAAIiB,EAAWjB,IAAK,CAClC,MAAM+C,EAAQlB,EAAemB,gCAC3BhD,EAAIyC,EACJlB,GAEFwB,EAAM5C,OAASL,EAAQE,GACvB4C,EAAOhD,EAAUiD,wBAAwBE,EAAOvB,GAChD1C,EAAAA,WAAWgE,KAAKF,EAAMZ,EAAOW,GAC7BA,GAAS,CACV,CAED,OAAOA,CACT,CAyBAlE,EAAiB4E,cAAgB,SAAU1D,EAAW2D,GACpD,MAAMC,EAAa,GACbC,EAAW,GAEjB,GAAIC,EAAOA,QAAC9D,IAAcA,EAAUE,OAAS,EAAG,CAC9CyD,EAAcI,EAAAA,aAAaJ,EAAajD,EAAOA,QAACsD,UAChD,MAAMC,EAAqBvD,EAAAA,QAAQwD,sBACjCP,EACAlD,GAGI0D,EAASzD,EAAAA,QAAQ0D,gBACrBH,EACA9E,EAAAA,WAAWkF,KACX1D,GAEI2D,EAAWnF,EAAAA,WAAWoF,UAC1B7D,EAAAA,QAAQ8D,wBACNP,EACA9E,EAAAA,WAAWsF,OACX7D,GAEFA,GAEI8D,EAAU5D,EAAAA,MAAM6D,gBACpBR,EACAG,EACAzD,GAEI+D,EAAWzF,EAAAA,WAAWoF,UAC1B7D,EAAAA,QAAQ8D,wBACNP,EACA9E,EAAAA,WAAW4B,OACXC,GAEFA,GAEI6D,EAAU/D,EAAAA,MAAM6D,gBACpBR,EACAS,EACA3D,GAGF,IAAI6D,EAAQ,EACZlB,EAAWmB,KAAK5F,aAAW6F,MAAMhF,EAAU,KAC3C,IAAIiF,EAAOrB,EAAW,GAEtB,MAAM1D,EAASF,EAAUE,OACzB,IAAK,IAAIG,EAAI,EAAGA,EAAIH,IAAUG,EAAG,CAC/B,MAAM6E,EAAMlF,EAAUK,GAGtB,GACES,EAAAA,MAAMqE,iBAAiBN,EAASI,GAAQ,GACxCnE,EAAAA,MAAMqE,iBAAiBN,EAASK,GAAO,EACvC,CAEA,MAAME,EAAeC,EAAAA,kBAAkBC,iBACrCL,EACAC,EACAR,EACAxD,GAEF,GAAI4C,EAAAA,QAAQsB,GAAe,CAEzB,MAAM9C,EAASnD,EAAAA,WAAWoG,iBACxBjB,EACA,KACAnD,GAEEL,EAAKA,MAACqE,iBAAiBT,EAASO,GAAQ,GAC1C9F,EAAAA,WAAWqG,OAAOlD,EAAQA,GAG5BsB,EAAWmB,KACT5F,EAAAA,WAAWsG,IAAIL,EAAc9C,EAAQ,IAAInD,EAAUA,aAErD0E,EAASkB,KAAKD,EAAQ,GAEtB3F,EAAAA,WAAWqG,OAAOlD,EAAQA,GAC1BsB,EAAWmB,KACT5F,EAAAA,WAAWsG,IAAIL,EAAc9C,EAAQ,IAAInD,EAAUA,aAErD2F,EAAQ,CACT,CACF,CAEDlB,EAAWmB,KAAK5F,aAAW6F,MAAMhF,EAAUK,KAC3CyE,IAEAG,EAAOC,CACR,CAEDrB,EAASkB,KAAKD,EACf,CAED,MAAO,CACL9E,UAAW4D,EACX8B,QAAS7B,EAEb,EAsBA/E,EAAiB6G,YAAc,SAAUC,GAClC9B,EAAAA,QAAQ8B,KACXA,EAAU,CAAA,GAEZ,MAAM5F,EAAY4F,EAAQ5F,UAE1B,IAAK8D,EAAAA,QAAQ9D,GACX,MAAM,IAAI6F,EAAAA,eAAe,kCAI3B,MAAM3F,EAASF,EAAUE,OACnBD,EAAY8D,EAAAA,aAAa6B,EAAQ3F,UAAW6F,EAAAA,UAAUC,OAC5D,IAAIvF,EAASuD,EAAYA,aAAC6B,EAAQpF,OAAQ,GAC1C,MAAMwF,EAAiB5F,MAAM6F,QAAQzF,GAErC,GAAIN,EAAS,EACX,MAAO,GACF,GAAe,IAAXA,EAAc,CACvB,MAAMI,EAAIL,EAAUuC,uBAAuBxC,EAAU,GAAI8B,GAEzD,GADAtB,EAASwF,EAAiBxF,EAAO,GAAKA,EACvB,IAAXA,EAAc,CAChB,MAAM0F,EAAIjG,EAAUkG,sBAAsB7F,EAAGuB,GAC7C1C,EAAAA,WAAWoG,iBAAiBW,EAAG1F,EAAQ0F,GACvC/G,EAAAA,WAAWsG,IAAInF,EAAG4F,EAAG5F,EACtB,CAED,MAAO,CAACA,EAAE8F,EAAG9F,EAAE+F,EAAG/F,EAAEgG,EACrB,CAED,IAAIrH,EAAc2G,EAAQ3G,YAC1B,IAAK6E,EAAAA,QAAQ7E,GAAc,CACzB,MAAMK,EAAcyE,EAAYA,aAC9B6B,EAAQtG,YACRiH,EAAAA,WAAWC,oBAEbvH,EAAcsH,EAAUA,WAACE,YAAYnH,EAAaW,EAAUyG,cAC7D,CAED,IACIrG,EADAiB,EAAY,EAGhB,IAAKjB,EAAI,EAAGA,EAAIH,EAAS,EAAGG,IAC1BiB,GAAaxC,EAAiB4D,eAC5B1C,EAAUK,GACVL,EAAUK,EAAI,GACdpB,GAIJ,MAAM0H,EAAgC,GAAjBrF,EAAY,GAC3BsF,EAAe,IAAIxG,MAAMuG,GAC/B,IAAIrE,EAAS,EAEb,IAAKjC,EAAI,EAAGA,EAAIH,EAAS,EAAGG,IAAK,CAO/BiC,EAASF,EANEpC,EAAUK,GACVL,EAAUK,EAAI,GAQvBpB,EACAgB,EAPS+F,EAAiBxF,EAAOH,GAAKG,EAC7BwF,EAAiBxF,EAAOH,EAAI,GAAKG,EAS1CoG,EACAtE,EAEH,CAEDlB,EAA6BlB,OAAS,EAEtC,MAAM2G,EAAY7G,EAAUE,EAAS,GAC/BkD,EAAQnD,EAAUM,wBAAwBsG,EAAWlF,GAC3DyB,EAAM5C,OAASwF,EAAiBxF,EAAON,EAAS,GAAKM,EACrD,MAAMyC,EAAOhD,EAAUiD,wBAAwBE,EAAOvB,GAGtD,OAFA1C,EAAUA,WAACgE,KAAKF,EAAM2D,EAAcD,EAAc,GAE3CC,CACT,EAEA,MAAME,EAAuB,IAAIhH,EAAAA,aAC3BiH,EAAuB,IAAIjH,EAAAA,aAsBjChB,EAAiBkI,iBAAmB,SAAUpB,GACvC9B,EAAAA,QAAQ8B,KACXA,EAAU,CAAA,GAEZ,MAAM5F,EAAY4F,EAAQ5F,UAE1B,IAAK8D,EAAAA,QAAQ9D,GACX,MAAM,IAAI6F,EAAAA,eAAe,kCAI3B,MAAM3F,EAASF,EAAUE,OACnBD,EAAY8D,EAAAA,aAAa6B,EAAQ3F,UAAW6F,EAAAA,UAAUC,OAC5D,IAAIvF,EAASuD,EAAYA,aAAC6B,EAAQpF,OAAQ,GAC1C,MAAMwF,EAAiB5F,MAAM6F,QAAQzF,GAErC,GAAIN,EAAS,EACX,MAAO,GACF,GAAe,IAAXA,EAAc,CACvB,MAAMI,EAAIL,EAAUuC,uBAAuBxC,EAAU,GAAI8B,GAEzD,GADAtB,EAASwF,EAAiBxF,EAAO,GAAKA,EACvB,IAAXA,EAAc,CAChB,MAAM0F,EAAIjG,EAAUkG,sBAAsB7F,EAAGuB,GAC7C1C,EAAAA,WAAWoG,iBAAiBW,EAAG1F,EAAQ0F,GACvC/G,EAAAA,WAAWsG,IAAInF,EAAG4F,EAAG5F,EACtB,CAED,MAAO,CAACA,EAAE8F,EAAG9F,EAAE+F,EAAG/F,EAAEgG,EACrB,CAED,MAAMhH,EAAcyE,EAAYA,aAC9B6B,EAAQtG,YACRiH,EAAAA,WAAWC,oBAGb,IACInG,EAMA4G,EAPA3F,EAAY,EAGZ4F,EAAKjH,EAAUM,wBACjBP,EAAU,GACV8G,GAGF,IAAKzG,EAAI,EAAGA,EAAIH,EAAS,EAAGG,IAC1B4G,EAAKhH,EAAUM,wBACbP,EAAUK,EAAI,GACd0G,GAEFzF,GAAaxC,EAAiByE,wBAAwB2D,EAAID,EAAI3H,GAC9D4H,EAAKpH,EAAAA,aAAakF,MAAMiC,EAAIH,GAG9B,MAAMH,EAAgC,GAAjBrF,EAAY,GAC3BsF,EAAe,IAAIxG,MAAMuG,GAC/B,IAAIrE,EAAS,EAEb,IAAKjC,EAAI,EAAGA,EAAIH,EAAS,EAAGG,IAAK,CAO/BiC,EAASgB,EANEtD,EAAUK,GACVL,EAAUK,EAAI,GAQvBf,EACAW,EAPS+F,EAAiBxF,EAAOH,GAAKG,EAC7BwF,EAAiBxF,EAAOH,EAAI,GAAKG,EAS1CoG,EACAtE,EAEH,CAEDlB,EAA6BlB,OAAS,EAEtC,MAAM2G,EAAY7G,EAAUE,EAAS,GAC/BkD,EAAQnD,EAAUM,wBAAwBsG,EAAWlF,GAC3DyB,EAAM5C,OAASwF,EAAiBxF,EAAON,EAAS,GAAKM,EACrD,MAAMyC,EAAOhD,EAAUiD,wBAAwBE,EAAOvB,GAGtD,OAFA1C,EAAUA,WAACgE,KAAKF,EAAM2D,EAAcD,EAAc,GAE3CC,CACT,EAsBA9H,EAAiBsD,qBAAuB,SAAUwD,GAChD,MAAMuB,EAAcrI,EAAiB6G,YAAYC,GAC3CwB,EAAOD,EAAYjH,OAAS,EAC5B0G,EAAe,IAAIxG,MAAMgH,GAC/B,IAAK,IAAI/G,EAAI,EAAGA,EAAI+G,EAAM/G,IACxBuG,EAAavG,GAAKlB,EAAUA,WAACkI,OAAOF,EAAiB,EAAJ9G,GAEnD,OAAOuG,CACT,EAsBA9H,EAAiBwE,0BAA4B,SAAUsC,GACrD,MAAMuB,EAAcrI,EAAiBkI,iBAAiBpB,GAChDwB,EAAOD,EAAYjH,OAAS,EAC5B0G,EAAe,IAAIxG,MAAMgH,GAC/B,IAAK,IAAI/G,EAAI,EAAGA,EAAI+G,EAAM/G,IACxBuG,EAAavG,GAAKlB,EAAUA,WAACkI,OAAOF,EAAiB,EAAJ9G,GAEnD,OAAOuG,CACT"}