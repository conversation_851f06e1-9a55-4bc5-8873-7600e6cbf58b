/*
 * @Author: songjimin
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
 */

export const ganttProps = {
  data: {
    type: Array,
    default: () => []
  },
  startTime: {
    type: [Number, String],
    default: Date.now()
  },
  endTime: {
    type: [Number, String],
    default: Date.now() + 60000
  },
  leftProp: {
    type: Object,
    default: () => ({ label: '名称', field: 'label' })
  },
  defaultExpandAll: {
    type: Boolean,
    default: false
  },
  labelField: {
    type: [String, Function],
    default: 'label'
  },
  startTimeField: {
    type: String,
    default: 'startTime'
  },
  endTimeField: {
    type: String,
    default: 'endTime'
  },
  childrenField: {
    type: String,
    default: 'children'
  },
  showCurrentTime: {
    type: Boolean,
    default: true
  },
  setTimeEvent: {
    type: Boolean,
    default: true
  },
  setMousewheel: {
    type: Boolean,
    default: false
  },
  currentTime: {
    type: [String, Number],
    default: Date.now()
  },
  min: {
    type: [Number],
    default: Date.now()
  },
  max: {
    type: [Number],
    default: Date.now()
  }
};

export const ganttEmits = {
  'update:startTime': () => true,
  'update:endTime': () => true,
  'update:currentTime': () => true,
  contextmenu: () => true,
  'left-click': () => true
};
