// @ts-nocheck
import * as Cesium from 'cesium';
import { Color, createPropertyTypeDescriptor, Entity, HeightReference } from 'cesium';

const { defaultValue, defined, DeveloperError, Event, createMaterialPropertyDescriptor, createPropertyDescriptor } = Cesium;

const defineProperties = Object.defineProperties;

export interface JammingRadarGraphicsOptions {
  show: Boolean; // 显隐
  color: Color; // 颜色
  pitchList: number[];
  horizontalList: number[];
  radiusList: number[];
  heightReference: HeightReference; // 高度计算方式
  lineShow: Boolean; // 线的颜色
  lineColor: Color; // 线的颜色
}

export interface JammingRadarGraphicsConstructor {
  new (options?: JammingRadarGraphicsOptions): JammingRadarGraphicsConstructor;
}

export const JammingRadarGraphics: JammingRadarGraphicsConstructor = function (options?: JammingRadarGraphicsOptions) {
  this._definitionChanged = new Event();

  this._show = undefined;
  this._color = undefined;
  this._pitchList = undefined;
  this._horizontalList = undefined;
  this._radiusList = undefined;
  this._heightReference = undefined;
  this._lineShow = undefined;
  this._lineColor = undefined;

  this.merge(defaultValue(options, defaultValue.EMPTY_OBJECT));
};

defineProperties(JammingRadarGraphics.prototype, {
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },

  show: createPropertyDescriptor('show'),
  color: createPropertyDescriptor('color'),
  pitchList: createPropertyDescriptor('pitchList'),
  horizontalList: createPropertyDescriptor('horizontalList'),
  radiusList: createPropertyDescriptor('radiusList'),
  heightReference: createPropertyDescriptor('heightReference'),
  lineShow: createPropertyDescriptor('lineShow'),
  lineColor: createPropertyDescriptor('lineColor')
});

JammingRadarGraphics.prototype.clone = function (result) {
  if (!defined(result)) {
    result = new JammingRadarGraphics();
  }

  result.show = this.show;
  result.color = this.color;
  result.pitchList = this.pitchList;
  result.horizontalList = this.horizontalList;
  result.radiusList = this.radiusList;
  result.heightReference = this.heightReference;
  result.lineShow = this.lineShow;
  result.lineColor = this.lineColor;

  return result;
};
JammingRadarGraphics.prototype.merge = function (source) {
  if (!defined(source)) {
    throw new DeveloperError('source is required.');
  }

  this.show = defaultValue(this.show, source.show);
  this.color = defaultValue(this.color, source.color);
  this.pitchList = defaultValue(this.pitchList, source.pitchList);
  this.horizontalList = defaultValue(this.horizontalList, source.horizontalList);
  this.radiusList = defaultValue(this.radiusList, source.radiusList);
  this.heightReference = defaultValue(this.heightReference, source.heightReference);
  this.lineShow = defaultValue(this.lineShow, source.lineShow);
  this.lineColor = defaultValue(this.lineColor, source.lineColor);
};
