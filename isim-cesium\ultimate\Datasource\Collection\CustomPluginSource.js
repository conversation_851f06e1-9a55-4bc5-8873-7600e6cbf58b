/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/24 10:51:46
 * @description update CustomPluginSource
 * @version 3.0
 * */

import AbstractSource from './AbstractSource.js';

// CustomPluginSource下创建图元后，所引用的entity的内部结构，新增字段
// entity:{
//   CustomPluginSource.name:{
//      PluginCollection.id:{
//        id:PluginCollection.id,
//        ...feature
//     }
//   }
// }

// CustomPluginSource是自定义dataSource，用于直接管理自定义primitive集合
export default class CustomPluginSource extends AbstractSource {
  constructor(name) {
    super(name);
  }

  // 缓存
  static cacheCustomPluginSource = new Map();
  // 通过createCustomPluginSource方法创建CustomPluginSource，完成全局单例化
  static createCustomPluginSource(name, viewer) {
    if (this.cacheCustomPluginSource.has(name)) return this.cacheCustomPluginSource.get(name);
    const source = new CustomPluginSource(name);
    this.cacheCustomPluginSource.set(name, source);
    viewer.sources.add(source);
    return source;
  }
  // 删除缓存
  static removeCacheByName(name) {
    return this.cacheCustomPluginSource.delete(name);
  }
}
