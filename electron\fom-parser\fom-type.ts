/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */

/**
 * 用于显示和溯源的数据格式
 */
export interface FomMetaData {
  from: string;
  parent?: string;
  type?: string;
}

/**
 * 用于显示和溯源的数据格式
 */
interface FomMetaDataFace {
  metaData: FomMetaData;
}

/**
 * 对象属性格式
 */
interface ObjectClassAttribute {
  name: string;
  dataType: string;
  updateType: string;
  updateCondition: string;
  ownership: string;
  sharing: string;
  transportation: string;
  order: string;
  semantics: string;
  _notes: string;
}

/**
 * 对象数据格式
 */
export interface ObjectClass extends FomMetaDataFace {
  name: string;
  sharing: string;
  semantics: string;
  attribute: ObjectClassAttribute[];
  objectClass?: ObjectClass[];
}

/**
 * 交互参数格式
 */
export interface InteractionParams {
  name: string;
  dataType: string;
  semantics: string;
}

/**
 * 交互数据格式
 */
export interface InteractionClass extends FomMetaDataFace {
  name: string;
  sharing: string;
  order: string;
  semantics: string;
  parameter: InteractionParams[];
  interactionClass?: InteractionClass[];
}

/**
 * 基础数据类型
 */
interface BasicData {
  name: string;
  representation: string;
  units: string;
  resolution: string;
  accuracy: string;
  semantics: string;
}
/**
 * 枚举数据类型
 */
export interface EnumeratedData {
  name: string;
  representation: string;
  semantics: string;
  enumerator: Array<{ name: string; value: string }>;
}

/**
 * 列表数据类型
 */
export interface ArrayData {
  name: string;
  dataType: string;
  cardinality: string;
  encoding: string;
  semantics: string;
}

/**
 * 简单的数据类型
 */
interface SimpleData {
  name: string;
  representation: string;
  units: string;
  resolution: string;
  accuracy: string;
  semantics: string;
}

export interface FixedRecordDataField {
  name: string;
  dataType: string;
  semantics: string;
}

/**
 * 复杂数据结构
 */
export interface FixedRecordData {
  name: string;
  encoding: string;
  semantics: string;
  field: FixedRecordDataField[];
}

export interface FomStruct {
  objectModel: {
    objects: {
      objectClass: ObjectClass;
    };
    dataTypes: {
      basicDataRepresentations: {
        basicData: BasicData[];
      };
      simpleDataTypes: {
        simpleData: SimpleData[];
      };
      enumeratedDataTypes: {
        enumeratedData: EnumeratedData[];
      };
      arrayDataTypes: {
        arrayData: ArrayData[];
      };
      fixedRecordDataTypes: {
        fixedRecordData: FixedRecordData[];
      };
      variantRecordDataTypes: {
        variantRecordData: any[];
      };
    };
    interactions: {
      interactionClass: InteractionClass;
    };
  };
}
