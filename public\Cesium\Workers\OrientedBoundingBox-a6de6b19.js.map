{"version": 3, "file": "OrientedBoundingBox-a6de6b19.js", "sources": ["../../../../Source/Core/OrientedBoundingBox.js"], "sourcesContent": ["import BoundingSphere from \"./BoundingSphere.js\";\nimport Cartesian2 from \"./Cartesian2.js\";\nimport Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport Check from \"./Check.js\";\nimport defaultValue from \"./defaultValue.js\";\nimport defined from \"./defined.js\";\nimport DeveloperError from \"./DeveloperError.js\";\nimport Ellipsoid from \"./Ellipsoid.js\";\nimport EllipsoidTangentPlane from \"./EllipsoidTangentPlane.js\";\nimport Intersect from \"./Intersect.js\";\nimport Interval from \"./Interval.js\";\nimport CesiumMath from \"./Math.js\";\nimport Matrix3 from \"./Matrix3.js\";\nimport Plane from \"./Plane.js\";\nimport Rectangle from \"./Rectangle.js\";\n\n/**\n * Creates an instance of an OrientedBoundingBox.\n * An OrientedBoundingBox of some object is a closed and convex cuboid. It can provide a tighter bounding volume than {@link BoundingSphere} or {@link AxisAlignedBoundingBox} in many cases.\n * @alias OrientedBoundingBox\n * @constructor\n *\n * @param {Cartesian3} [center=Cartesian3.ZERO] The center of the box.\n * @param {Matrix3} [halfAxes=Matrix3.ZERO] The three orthogonal half-axes of the bounding box.\n *                                          Equivalently, the transformation matrix, to rotate and scale a 0x0x0\n *                                          cube centered at the origin.\n *\n *\n * @example\n * // Create an OrientedBoundingBox using a transformation matrix, a position where the box will be translated, and a scale.\n * const center = new Cesium.Cartesian3(1.0, 0.0, 0.0);\n * const halfAxes = Cesium.Matrix3.fromScale(new Cesium.Cartesian3(1.0, 3.0, 2.0), new Cesium.Matrix3());\n *\n * const obb = new Cesium.OrientedBoundingBox(center, halfAxes);\n *\n * @see BoundingSphere\n * @see BoundingRectangle\n */\nfunction OrientedBoundingBox(center, halfAxes) {\n  /**\n   * The center of the box.\n   * @type {Cartesian3}\n   * @default {@link Cartesian3.ZERO}\n   */\n  this.center = Cartesian3.clone(defaultValue(center, Cartesian3.ZERO));\n  /**\n   * The transformation matrix, to rotate the box to the right position.\n   * @type {Matrix3}\n   * @default {@link Matrix3.ZERO}\n   */\n  this.halfAxes = Matrix3.clone(defaultValue(halfAxes, Matrix3.ZERO));\n}\n\n/**\n * The number of elements used to pack the object into an array.\n * @type {Number}\n */\nOrientedBoundingBox.packedLength =\n  Cartesian3.packedLength + Matrix3.packedLength;\n\n/**\n * Stores the provided instance into the provided array.\n *\n * @param {OrientedBoundingBox} value The value to pack.\n * @param {Number[]} array The array to pack into.\n * @param {Number} [startingIndex=0] The index into the array at which to start packing the elements.\n *\n * @returns {Number[]} The array that was packed into\n */\nOrientedBoundingBox.pack = function (value, array, startingIndex) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.typeOf.object(\"value\", value);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  Cartesian3.pack(value.center, array, startingIndex);\n  Matrix3.pack(value.halfAxes, array, startingIndex + Cartesian3.packedLength);\n\n  return array;\n};\n\n/**\n * Retrieves an instance from a packed array.\n *\n * @param {Number[]} array The packed array.\n * @param {Number} [startingIndex=0] The starting index of the element to be unpacked.\n * @param {OrientedBoundingBox} [result] The object into which to store the result.\n * @returns {OrientedBoundingBox} The modified result parameter or a new OrientedBoundingBox instance if one was not provided.\n */\nOrientedBoundingBox.unpack = function (array, startingIndex, result) {\n  //>>includeStart('debug', pragmas.debug);\n  Check.defined(\"array\", array);\n  //>>includeEnd('debug');\n\n  startingIndex = defaultValue(startingIndex, 0);\n\n  if (!defined(result)) {\n    result = new OrientedBoundingBox();\n  }\n\n  Cartesian3.unpack(array, startingIndex, result.center);\n  Matrix3.unpack(\n    array,\n    startingIndex + Cartesian3.packedLength,\n    result.halfAxes\n  );\n  return result;\n};\n\nconst scratchCartesian1 = new Cartesian3();\nconst scratchCartesian2 = new Cartesian3();\nconst scratchCartesian3 = new Cartesian3();\nconst scratchCartesian4 = new Cartesian3();\nconst scratchCartesian5 = new Cartesian3();\nconst scratchCartesian6 = new Cartesian3();\nconst scratchCovarianceResult = new Matrix3();\nconst scratchEigenResult = {\n  unitary: new Matrix3(),\n  diagonal: new Matrix3(),\n};\n\n/**\n * Computes an instance of an OrientedBoundingBox of the given positions.\n * This is an implementation of Stefan Gottschalk's Collision Queries using Oriented Bounding Boxes solution (PHD thesis).\n * Reference: http://gamma.cs.unc.edu/users/gottschalk/main.pdf\n *\n * @param {Cartesian3[]} [positions] List of {@link Cartesian3} points that the bounding box will enclose.\n * @param {OrientedBoundingBox} [result] The object onto which to store the result.\n * @returns {OrientedBoundingBox} The modified result parameter or a new OrientedBoundingBox instance if one was not provided.\n *\n * @example\n * // Compute an object oriented bounding box enclosing two points.\n * const box = Cesium.OrientedBoundingBox.fromPoints([new Cesium.Cartesian3(2, 0, 0), new Cesium.Cartesian3(-2, 0, 0)]);\n */\nOrientedBoundingBox.fromPoints = function (positions, result) {\n  if (!defined(result)) {\n    result = new OrientedBoundingBox();\n  }\n\n  if (!defined(positions) || positions.length === 0) {\n    result.halfAxes = Matrix3.ZERO;\n    result.center = Cartesian3.ZERO;\n    return result;\n  }\n\n  let i;\n  const length = positions.length;\n\n  const meanPoint = Cartesian3.clone(positions[0], scratchCartesian1);\n  for (i = 1; i < length; i++) {\n    Cartesian3.add(meanPoint, positions[i], meanPoint);\n  }\n  const invLength = 1.0 / length;\n  Cartesian3.multiplyByScalar(meanPoint, invLength, meanPoint);\n\n  let exx = 0.0;\n  let exy = 0.0;\n  let exz = 0.0;\n  let eyy = 0.0;\n  let eyz = 0.0;\n  let ezz = 0.0;\n  let p;\n\n  for (i = 0; i < length; i++) {\n    p = Cartesian3.subtract(positions[i], meanPoint, scratchCartesian2);\n    exx += p.x * p.x;\n    exy += p.x * p.y;\n    exz += p.x * p.z;\n    eyy += p.y * p.y;\n    eyz += p.y * p.z;\n    ezz += p.z * p.z;\n  }\n\n  exx *= invLength;\n  exy *= invLength;\n  exz *= invLength;\n  eyy *= invLength;\n  eyz *= invLength;\n  ezz *= invLength;\n\n  const covarianceMatrix = scratchCovarianceResult;\n  covarianceMatrix[0] = exx;\n  covarianceMatrix[1] = exy;\n  covarianceMatrix[2] = exz;\n  covarianceMatrix[3] = exy;\n  covarianceMatrix[4] = eyy;\n  covarianceMatrix[5] = eyz;\n  covarianceMatrix[6] = exz;\n  covarianceMatrix[7] = eyz;\n  covarianceMatrix[8] = ezz;\n\n  const eigenDecomposition = Matrix3.computeEigenDecomposition(\n    covarianceMatrix,\n    scratchEigenResult\n  );\n  const rotation = Matrix3.clone(eigenDecomposition.unitary, result.halfAxes);\n\n  let v1 = Matrix3.getColumn(rotation, 0, scratchCartesian4);\n  let v2 = Matrix3.getColumn(rotation, 1, scratchCartesian5);\n  let v3 = Matrix3.getColumn(rotation, 2, scratchCartesian6);\n\n  let u1 = -Number.MAX_VALUE;\n  let u2 = -Number.MAX_VALUE;\n  let u3 = -Number.MAX_VALUE;\n  let l1 = Number.MAX_VALUE;\n  let l2 = Number.MAX_VALUE;\n  let l3 = Number.MAX_VALUE;\n\n  for (i = 0; i < length; i++) {\n    p = positions[i];\n    u1 = Math.max(Cartesian3.dot(v1, p), u1);\n    u2 = Math.max(Cartesian3.dot(v2, p), u2);\n    u3 = Math.max(Cartesian3.dot(v3, p), u3);\n\n    l1 = Math.min(Cartesian3.dot(v1, p), l1);\n    l2 = Math.min(Cartesian3.dot(v2, p), l2);\n    l3 = Math.min(Cartesian3.dot(v3, p), l3);\n  }\n\n  v1 = Cartesian3.multiplyByScalar(v1, 0.5 * (l1 + u1), v1);\n  v2 = Cartesian3.multiplyByScalar(v2, 0.5 * (l2 + u2), v2);\n  v3 = Cartesian3.multiplyByScalar(v3, 0.5 * (l3 + u3), v3);\n\n  const center = Cartesian3.add(v1, v2, result.center);\n  Cartesian3.add(center, v3, center);\n\n  const scale = scratchCartesian3;\n  scale.x = u1 - l1;\n  scale.y = u2 - l2;\n  scale.z = u3 - l3;\n  Cartesian3.multiplyByScalar(scale, 0.5, scale);\n  Matrix3.multiplyByScale(result.halfAxes, scale, result.halfAxes);\n\n  return result;\n};\n\nconst scratchOffset = new Cartesian3();\nconst scratchScale = new Cartesian3();\nfunction fromPlaneExtents(\n  planeOrigin,\n  planeXAxis,\n  planeYAxis,\n  planeZAxis,\n  minimumX,\n  maximumX,\n  minimumY,\n  maximumY,\n  minimumZ,\n  maximumZ,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (\n    !defined(minimumX) ||\n    !defined(maximumX) ||\n    !defined(minimumY) ||\n    !defined(maximumY) ||\n    !defined(minimumZ) ||\n    !defined(maximumZ)\n  ) {\n    throw new DeveloperError(\n      \"all extents (minimum/maximum X/Y/Z) are required.\"\n    );\n  }\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new OrientedBoundingBox();\n  }\n\n  const halfAxes = result.halfAxes;\n  Matrix3.setColumn(halfAxes, 0, planeXAxis, halfAxes);\n  Matrix3.setColumn(halfAxes, 1, planeYAxis, halfAxes);\n  Matrix3.setColumn(halfAxes, 2, planeZAxis, halfAxes);\n\n  let centerOffset = scratchOffset;\n  centerOffset.x = (minimumX + maximumX) / 2.0;\n  centerOffset.y = (minimumY + maximumY) / 2.0;\n  centerOffset.z = (minimumZ + maximumZ) / 2.0;\n\n  const scale = scratchScale;\n  scale.x = (maximumX - minimumX) / 2.0;\n  scale.y = (maximumY - minimumY) / 2.0;\n  scale.z = (maximumZ - minimumZ) / 2.0;\n\n  const center = result.center;\n  centerOffset = Matrix3.multiplyByVector(halfAxes, centerOffset, centerOffset);\n  Cartesian3.add(planeOrigin, centerOffset, center);\n  Matrix3.multiplyByScale(halfAxes, scale, halfAxes);\n\n  return result;\n}\n\nconst scratchRectangleCenterCartographic = new Cartographic();\nconst scratchRectangleCenter = new Cartesian3();\nconst scratchPerimeterCartographicNC = new Cartographic();\nconst scratchPerimeterCartographicNW = new Cartographic();\nconst scratchPerimeterCartographicCW = new Cartographic();\nconst scratchPerimeterCartographicSW = new Cartographic();\nconst scratchPerimeterCartographicSC = new Cartographic();\nconst scratchPerimeterCartesianNC = new Cartesian3();\nconst scratchPerimeterCartesianNW = new Cartesian3();\nconst scratchPerimeterCartesianCW = new Cartesian3();\nconst scratchPerimeterCartesianSW = new Cartesian3();\nconst scratchPerimeterCartesianSC = new Cartesian3();\nconst scratchPerimeterProjectedNC = new Cartesian2();\nconst scratchPerimeterProjectedNW = new Cartesian2();\nconst scratchPerimeterProjectedCW = new Cartesian2();\nconst scratchPerimeterProjectedSW = new Cartesian2();\nconst scratchPerimeterProjectedSC = new Cartesian2();\n\nconst scratchPlaneOrigin = new Cartesian3();\nconst scratchPlaneNormal = new Cartesian3();\nconst scratchPlaneXAxis = new Cartesian3();\nconst scratchHorizonCartesian = new Cartesian3();\nconst scratchHorizonProjected = new Cartesian2();\nconst scratchMaxY = new Cartesian3();\nconst scratchMinY = new Cartesian3();\nconst scratchZ = new Cartesian3();\nconst scratchPlane = new Plane(Cartesian3.UNIT_X, 0.0);\n\n/**\n * Computes an OrientedBoundingBox that bounds a {@link Rectangle} on the surface of an {@link Ellipsoid}.\n * There are no guarantees about the orientation of the bounding box.\n *\n * @param {Rectangle} rectangle The cartographic rectangle on the surface of the ellipsoid.\n * @param {Number} [minimumHeight=0.0] The minimum height (elevation) within the tile.\n * @param {Number} [maximumHeight=0.0] The maximum height (elevation) within the tile.\n * @param {Ellipsoid} [ellipsoid=Ellipsoid.WGS84] The ellipsoid on which the rectangle is defined.\n * @param {OrientedBoundingBox} [result] The object onto which to store the result.\n * @returns {OrientedBoundingBox} The modified result parameter or a new OrientedBoundingBox instance if none was provided.\n *\n * @exception {DeveloperError} rectangle.width must be between 0 and pi.\n * @exception {DeveloperError} rectangle.height must be between 0 and pi.\n * @exception {DeveloperError} ellipsoid must be an ellipsoid of revolution (<code>radii.x == radii.y</code>)\n */\nOrientedBoundingBox.fromRectangle = function (\n  rectangle,\n  minimumHeight,\n  maximumHeight,\n  ellipsoid,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(rectangle)) {\n    throw new DeveloperError(\"rectangle is required\");\n  }\n  if (rectangle.width < 0.0 || rectangle.width > CesiumMath.TWO_PI) {\n    throw new DeveloperError(\"Rectangle width must be between 0 and 2*pi\");\n  }\n  if (rectangle.height < 0.0 || rectangle.height > CesiumMath.PI) {\n    throw new DeveloperError(\"Rectangle height must be between 0 and pi\");\n  }\n  if (\n    defined(ellipsoid) &&\n    !CesiumMath.equalsEpsilon(\n      ellipsoid.radii.x,\n      ellipsoid.radii.y,\n      CesiumMath.EPSILON15\n    )\n  ) {\n    throw new DeveloperError(\n      \"Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)\"\n    );\n  }\n  //>>includeEnd('debug');\n\n  minimumHeight = defaultValue(minimumHeight, 0.0);\n  maximumHeight = defaultValue(maximumHeight, 0.0);\n  ellipsoid = defaultValue(ellipsoid, Ellipsoid.WGS84);\n\n  let minX, maxX, minY, maxY, minZ, maxZ, plane;\n\n  if (rectangle.width <= CesiumMath.PI) {\n    // The bounding box will be aligned with the tangent plane at the center of the rectangle.\n    const tangentPointCartographic = Rectangle.center(\n      rectangle,\n      scratchRectangleCenterCartographic\n    );\n    const tangentPoint = ellipsoid.cartographicToCartesian(\n      tangentPointCartographic,\n      scratchRectangleCenter\n    );\n    const tangentPlane = new EllipsoidTangentPlane(tangentPoint, ellipsoid);\n    plane = tangentPlane.plane;\n\n    // If the rectangle spans the equator, CW is instead aligned with the equator (because it sticks out the farthest at the equator).\n    const lonCenter = tangentPointCartographic.longitude;\n    const latCenter =\n      rectangle.south < 0.0 && rectangle.north > 0.0\n        ? 0.0\n        : tangentPointCartographic.latitude;\n\n    // Compute XY extents using the rectangle at maximum height\n    const perimeterCartographicNC = Cartographic.fromRadians(\n      lonCenter,\n      rectangle.north,\n      maximumHeight,\n      scratchPerimeterCartographicNC\n    );\n    const perimeterCartographicNW = Cartographic.fromRadians(\n      rectangle.west,\n      rectangle.north,\n      maximumHeight,\n      scratchPerimeterCartographicNW\n    );\n    const perimeterCartographicCW = Cartographic.fromRadians(\n      rectangle.west,\n      latCenter,\n      maximumHeight,\n      scratchPerimeterCartographicCW\n    );\n    const perimeterCartographicSW = Cartographic.fromRadians(\n      rectangle.west,\n      rectangle.south,\n      maximumHeight,\n      scratchPerimeterCartographicSW\n    );\n    const perimeterCartographicSC = Cartographic.fromRadians(\n      lonCenter,\n      rectangle.south,\n      maximumHeight,\n      scratchPerimeterCartographicSC\n    );\n\n    const perimeterCartesianNC = ellipsoid.cartographicToCartesian(\n      perimeterCartographicNC,\n      scratchPerimeterCartesianNC\n    );\n    let perimeterCartesianNW = ellipsoid.cartographicToCartesian(\n      perimeterCartographicNW,\n      scratchPerimeterCartesianNW\n    );\n    const perimeterCartesianCW = ellipsoid.cartographicToCartesian(\n      perimeterCartographicCW,\n      scratchPerimeterCartesianCW\n    );\n    let perimeterCartesianSW = ellipsoid.cartographicToCartesian(\n      perimeterCartographicSW,\n      scratchPerimeterCartesianSW\n    );\n    const perimeterCartesianSC = ellipsoid.cartographicToCartesian(\n      perimeterCartographicSC,\n      scratchPerimeterCartesianSC\n    );\n\n    const perimeterProjectedNC = tangentPlane.projectPointToNearestOnPlane(\n      perimeterCartesianNC,\n      scratchPerimeterProjectedNC\n    );\n    const perimeterProjectedNW = tangentPlane.projectPointToNearestOnPlane(\n      perimeterCartesianNW,\n      scratchPerimeterProjectedNW\n    );\n    const perimeterProjectedCW = tangentPlane.projectPointToNearestOnPlane(\n      perimeterCartesianCW,\n      scratchPerimeterProjectedCW\n    );\n    const perimeterProjectedSW = tangentPlane.projectPointToNearestOnPlane(\n      perimeterCartesianSW,\n      scratchPerimeterProjectedSW\n    );\n    const perimeterProjectedSC = tangentPlane.projectPointToNearestOnPlane(\n      perimeterCartesianSC,\n      scratchPerimeterProjectedSC\n    );\n\n    minX = Math.min(\n      perimeterProjectedNW.x,\n      perimeterProjectedCW.x,\n      perimeterProjectedSW.x\n    );\n    maxX = -minX; // symmetrical\n\n    maxY = Math.max(perimeterProjectedNW.y, perimeterProjectedNC.y);\n    minY = Math.min(perimeterProjectedSW.y, perimeterProjectedSC.y);\n\n    // Compute minimum Z using the rectangle at minimum height, since it will be deeper than the maximum height\n    perimeterCartographicNW.height = perimeterCartographicSW.height = minimumHeight;\n    perimeterCartesianNW = ellipsoid.cartographicToCartesian(\n      perimeterCartographicNW,\n      scratchPerimeterCartesianNW\n    );\n    perimeterCartesianSW = ellipsoid.cartographicToCartesian(\n      perimeterCartographicSW,\n      scratchPerimeterCartesianSW\n    );\n\n    minZ = Math.min(\n      Plane.getPointDistance(plane, perimeterCartesianNW),\n      Plane.getPointDistance(plane, perimeterCartesianSW)\n    );\n    maxZ = maximumHeight; // Since the tangent plane touches the surface at height = 0, this is okay\n\n    return fromPlaneExtents(\n      tangentPlane.origin,\n      tangentPlane.xAxis,\n      tangentPlane.yAxis,\n      tangentPlane.zAxis,\n      minX,\n      maxX,\n      minY,\n      maxY,\n      minZ,\n      maxZ,\n      result\n    );\n  }\n\n  // Handle the case where rectangle width is greater than PI (wraps around more than half the ellipsoid).\n  const fullyAboveEquator = rectangle.south > 0.0;\n  const fullyBelowEquator = rectangle.north < 0.0;\n  const latitudeNearestToEquator = fullyAboveEquator\n    ? rectangle.south\n    : fullyBelowEquator\n    ? rectangle.north\n    : 0.0;\n  const centerLongitude = Rectangle.center(\n    rectangle,\n    scratchRectangleCenterCartographic\n  ).longitude;\n\n  // Plane is located at the rectangle's center longitude and the rectangle's latitude that is closest to the equator. It rotates around the Z axis.\n  // This results in a better fit than the obb approach for smaller rectangles, which orients with the rectangle's center normal.\n  const planeOrigin = Cartesian3.fromRadians(\n    centerLongitude,\n    latitudeNearestToEquator,\n    maximumHeight,\n    ellipsoid,\n    scratchPlaneOrigin\n  );\n  planeOrigin.z = 0.0; // center the plane on the equator to simpify plane normal calculation\n  const isPole =\n    Math.abs(planeOrigin.x) < CesiumMath.EPSILON10 &&\n    Math.abs(planeOrigin.y) < CesiumMath.EPSILON10;\n  const planeNormal = !isPole\n    ? Cartesian3.normalize(planeOrigin, scratchPlaneNormal)\n    : Cartesian3.UNIT_X;\n  const planeYAxis = Cartesian3.UNIT_Z;\n  const planeXAxis = Cartesian3.cross(\n    planeNormal,\n    planeYAxis,\n    scratchPlaneXAxis\n  );\n  plane = Plane.fromPointNormal(planeOrigin, planeNormal, scratchPlane);\n\n  // Get the horizon point relative to the center. This will be the farthest extent in the plane's X dimension.\n  const horizonCartesian = Cartesian3.fromRadians(\n    centerLongitude + CesiumMath.PI_OVER_TWO,\n    latitudeNearestToEquator,\n    maximumHeight,\n    ellipsoid,\n    scratchHorizonCartesian\n  );\n  maxX = Cartesian3.dot(\n    Plane.projectPointOntoPlane(\n      plane,\n      horizonCartesian,\n      scratchHorizonProjected\n    ),\n    planeXAxis\n  );\n  minX = -maxX; // symmetrical\n\n  // Get the min and max Y, using the height that will give the largest extent\n  maxY = Cartesian3.fromRadians(\n    0.0,\n    rectangle.north,\n    fullyBelowEquator ? minimumHeight : maximumHeight,\n    ellipsoid,\n    scratchMaxY\n  ).z;\n  minY = Cartesian3.fromRadians(\n    0.0,\n    rectangle.south,\n    fullyAboveEquator ? minimumHeight : maximumHeight,\n    ellipsoid,\n    scratchMinY\n  ).z;\n\n  const farZ = Cartesian3.fromRadians(\n    rectangle.east,\n    latitudeNearestToEquator,\n    maximumHeight,\n    ellipsoid,\n    scratchZ\n  );\n  minZ = Plane.getPointDistance(plane, farZ);\n  maxZ = 0.0; // plane origin starts at maxZ already\n\n  // min and max are local to the plane axes\n  return fromPlaneExtents(\n    planeOrigin,\n    planeXAxis,\n    planeYAxis,\n    planeNormal,\n    minX,\n    maxX,\n    minY,\n    maxY,\n    minZ,\n    maxZ,\n    result\n  );\n};\n\n/**\n * Duplicates a OrientedBoundingBox instance.\n *\n * @param {OrientedBoundingBox} box The bounding box to duplicate.\n * @param {OrientedBoundingBox} [result] The object onto which to store the result.\n * @returns {OrientedBoundingBox} The modified result parameter or a new OrientedBoundingBox instance if none was provided. (Returns undefined if box is undefined)\n */\nOrientedBoundingBox.clone = function (box, result) {\n  if (!defined(box)) {\n    return undefined;\n  }\n\n  if (!defined(result)) {\n    return new OrientedBoundingBox(box.center, box.halfAxes);\n  }\n\n  Cartesian3.clone(box.center, result.center);\n  Matrix3.clone(box.halfAxes, result.halfAxes);\n\n  return result;\n};\n\n/**\n * Determines which side of a plane the oriented bounding box is located.\n *\n * @param {OrientedBoundingBox} box The oriented bounding box to test.\n * @param {Plane} plane The plane to test against.\n * @returns {Intersect} {@link Intersect.INSIDE} if the entire box is on the side of the plane\n *                      the normal is pointing, {@link Intersect.OUTSIDE} if the entire box is\n *                      on the opposite side, and {@link Intersect.INTERSECTING} if the box\n *                      intersects the plane.\n */\nOrientedBoundingBox.intersectPlane = function (box, plane) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(box)) {\n    throw new DeveloperError(\"box is required.\");\n  }\n\n  if (!defined(plane)) {\n    throw new DeveloperError(\"plane is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const center = box.center;\n  const normal = plane.normal;\n  const halfAxes = box.halfAxes;\n  const normalX = normal.x,\n    normalY = normal.y,\n    normalZ = normal.z;\n  // plane is used as if it is its normal; the first three components are assumed to be normalized\n  const radEffective =\n    Math.abs(\n      normalX * halfAxes[Matrix3.COLUMN0ROW0] +\n        normalY * halfAxes[Matrix3.COLUMN0ROW1] +\n        normalZ * halfAxes[Matrix3.COLUMN0ROW2]\n    ) +\n    Math.abs(\n      normalX * halfAxes[Matrix3.COLUMN1ROW0] +\n        normalY * halfAxes[Matrix3.COLUMN1ROW1] +\n        normalZ * halfAxes[Matrix3.COLUMN1ROW2]\n    ) +\n    Math.abs(\n      normalX * halfAxes[Matrix3.COLUMN2ROW0] +\n        normalY * halfAxes[Matrix3.COLUMN2ROW1] +\n        normalZ * halfAxes[Matrix3.COLUMN2ROW2]\n    );\n  const distanceToPlane = Cartesian3.dot(normal, center) + plane.distance;\n\n  if (distanceToPlane <= -radEffective) {\n    // The entire box is on the negative side of the plane normal\n    return Intersect.OUTSIDE;\n  } else if (distanceToPlane >= radEffective) {\n    // The entire box is on the positive side of the plane normal\n    return Intersect.INSIDE;\n  }\n  return Intersect.INTERSECTING;\n};\n\nconst scratchCartesianU = new Cartesian3();\nconst scratchCartesianV = new Cartesian3();\nconst scratchCartesianW = new Cartesian3();\nconst scratchValidAxis2 = new Cartesian3();\nconst scratchValidAxis3 = new Cartesian3();\nconst scratchPPrime = new Cartesian3();\n\n/**\n * Computes the estimated distance squared from the closest point on a bounding box to a point.\n *\n * @param {OrientedBoundingBox} box The box.\n * @param {Cartesian3} cartesian The point\n * @returns {Number} The distance squared from the oriented bounding box to the point. Returns 0 if the point is inside the box.\n *\n * @example\n * // Sort bounding boxes from back to front\n * boxes.sort(function(a, b) {\n *     return Cesium.OrientedBoundingBox.distanceSquaredTo(b, camera.positionWC) - Cesium.OrientedBoundingBox.distanceSquaredTo(a, camera.positionWC);\n * });\n */\nOrientedBoundingBox.distanceSquaredTo = function (box, cartesian) {\n  // See Geometric Tools for Computer Graphics 10.4.2\n\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(box)) {\n    throw new DeveloperError(\"box is required.\");\n  }\n  if (!defined(cartesian)) {\n    throw new DeveloperError(\"cartesian is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const offset = Cartesian3.subtract(cartesian, box.center, scratchOffset);\n\n  const halfAxes = box.halfAxes;\n  let u = Matrix3.getColumn(halfAxes, 0, scratchCartesianU);\n  let v = Matrix3.getColumn(halfAxes, 1, scratchCartesianV);\n  let w = Matrix3.getColumn(halfAxes, 2, scratchCartesianW);\n\n  const uHalf = Cartesian3.magnitude(u);\n  const vHalf = Cartesian3.magnitude(v);\n  const wHalf = Cartesian3.magnitude(w);\n\n  let uValid = true;\n  let vValid = true;\n  let wValid = true;\n\n  if (uHalf > 0) {\n    Cartesian3.divideByScalar(u, uHalf, u);\n  } else {\n    uValid = false;\n  }\n\n  if (vHalf > 0) {\n    Cartesian3.divideByScalar(v, vHalf, v);\n  } else {\n    vValid = false;\n  }\n\n  if (wHalf > 0) {\n    Cartesian3.divideByScalar(w, wHalf, w);\n  } else {\n    wValid = false;\n  }\n\n  const numberOfDegenerateAxes = !uValid + !vValid + !wValid;\n  let validAxis1;\n  let validAxis2;\n  let validAxis3;\n\n  if (numberOfDegenerateAxes === 1) {\n    let degenerateAxis = u;\n    validAxis1 = v;\n    validAxis2 = w;\n    if (!vValid) {\n      degenerateAxis = v;\n      validAxis1 = u;\n    } else if (!wValid) {\n      degenerateAxis = w;\n      validAxis2 = u;\n    }\n\n    validAxis3 = Cartesian3.cross(validAxis1, validAxis2, scratchValidAxis3);\n\n    if (degenerateAxis === u) {\n      u = validAxis3;\n    } else if (degenerateAxis === v) {\n      v = validAxis3;\n    } else if (degenerateAxis === w) {\n      w = validAxis3;\n    }\n  } else if (numberOfDegenerateAxes === 2) {\n    validAxis1 = u;\n    if (vValid) {\n      validAxis1 = v;\n    } else if (wValid) {\n      validAxis1 = w;\n    }\n\n    let crossVector = Cartesian3.UNIT_Y;\n    if (crossVector.equalsEpsilon(validAxis1, CesiumMath.EPSILON3)) {\n      crossVector = Cartesian3.UNIT_X;\n    }\n\n    validAxis2 = Cartesian3.cross(validAxis1, crossVector, scratchValidAxis2);\n    Cartesian3.normalize(validAxis2, validAxis2);\n    validAxis3 = Cartesian3.cross(validAxis1, validAxis2, scratchValidAxis3);\n    Cartesian3.normalize(validAxis3, validAxis3);\n\n    if (validAxis1 === u) {\n      v = validAxis2;\n      w = validAxis3;\n    } else if (validAxis1 === v) {\n      w = validAxis2;\n      u = validAxis3;\n    } else if (validAxis1 === w) {\n      u = validAxis2;\n      v = validAxis3;\n    }\n  } else if (numberOfDegenerateAxes === 3) {\n    u = Cartesian3.UNIT_X;\n    v = Cartesian3.UNIT_Y;\n    w = Cartesian3.UNIT_Z;\n  }\n\n  const pPrime = scratchPPrime;\n  pPrime.x = Cartesian3.dot(offset, u);\n  pPrime.y = Cartesian3.dot(offset, v);\n  pPrime.z = Cartesian3.dot(offset, w);\n\n  let distanceSquared = 0.0;\n  let d;\n\n  if (pPrime.x < -uHalf) {\n    d = pPrime.x + uHalf;\n    distanceSquared += d * d;\n  } else if (pPrime.x > uHalf) {\n    d = pPrime.x - uHalf;\n    distanceSquared += d * d;\n  }\n\n  if (pPrime.y < -vHalf) {\n    d = pPrime.y + vHalf;\n    distanceSquared += d * d;\n  } else if (pPrime.y > vHalf) {\n    d = pPrime.y - vHalf;\n    distanceSquared += d * d;\n  }\n\n  if (pPrime.z < -wHalf) {\n    d = pPrime.z + wHalf;\n    distanceSquared += d * d;\n  } else if (pPrime.z > wHalf) {\n    d = pPrime.z - wHalf;\n    distanceSquared += d * d;\n  }\n\n  return distanceSquared;\n};\n\nconst scratchCorner = new Cartesian3();\nconst scratchToCenter = new Cartesian3();\n\n/**\n * The distances calculated by the vector from the center of the bounding box to position projected onto direction.\n * <br>\n * If you imagine the infinite number of planes with normal direction, this computes the smallest distance to the\n * closest and farthest planes from position that intersect the bounding box.\n *\n * @param {OrientedBoundingBox} box The bounding box to calculate the distance to.\n * @param {Cartesian3} position The position to calculate the distance from.\n * @param {Cartesian3} direction The direction from position.\n * @param {Interval} [result] A Interval to store the nearest and farthest distances.\n * @returns {Interval} The nearest and farthest distances on the bounding box from position in direction.\n */\nOrientedBoundingBox.computePlaneDistances = function (\n  box,\n  position,\n  direction,\n  result\n) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(box)) {\n    throw new DeveloperError(\"box is required.\");\n  }\n\n  if (!defined(position)) {\n    throw new DeveloperError(\"position is required.\");\n  }\n\n  if (!defined(direction)) {\n    throw new DeveloperError(\"direction is required.\");\n  }\n  //>>includeEnd('debug');\n\n  if (!defined(result)) {\n    result = new Interval();\n  }\n\n  let minDist = Number.POSITIVE_INFINITY;\n  let maxDist = Number.NEGATIVE_INFINITY;\n\n  const center = box.center;\n  const halfAxes = box.halfAxes;\n\n  const u = Matrix3.getColumn(halfAxes, 0, scratchCartesianU);\n  const v = Matrix3.getColumn(halfAxes, 1, scratchCartesianV);\n  const w = Matrix3.getColumn(halfAxes, 2, scratchCartesianW);\n\n  // project first corner\n  const corner = Cartesian3.add(u, v, scratchCorner);\n  Cartesian3.add(corner, w, corner);\n  Cartesian3.add(corner, center, corner);\n\n  const toCenter = Cartesian3.subtract(corner, position, scratchToCenter);\n  let mag = Cartesian3.dot(direction, toCenter);\n\n  minDist = Math.min(mag, minDist);\n  maxDist = Math.max(mag, maxDist);\n\n  // project second corner\n  Cartesian3.add(center, u, corner);\n  Cartesian3.add(corner, v, corner);\n  Cartesian3.subtract(corner, w, corner);\n\n  Cartesian3.subtract(corner, position, toCenter);\n  mag = Cartesian3.dot(direction, toCenter);\n\n  minDist = Math.min(mag, minDist);\n  maxDist = Math.max(mag, maxDist);\n\n  // project third corner\n  Cartesian3.add(center, u, corner);\n  Cartesian3.subtract(corner, v, corner);\n  Cartesian3.add(corner, w, corner);\n\n  Cartesian3.subtract(corner, position, toCenter);\n  mag = Cartesian3.dot(direction, toCenter);\n\n  minDist = Math.min(mag, minDist);\n  maxDist = Math.max(mag, maxDist);\n\n  // project fourth corner\n  Cartesian3.add(center, u, corner);\n  Cartesian3.subtract(corner, v, corner);\n  Cartesian3.subtract(corner, w, corner);\n\n  Cartesian3.subtract(corner, position, toCenter);\n  mag = Cartesian3.dot(direction, toCenter);\n\n  minDist = Math.min(mag, minDist);\n  maxDist = Math.max(mag, maxDist);\n\n  // project fifth corner\n  Cartesian3.subtract(center, u, corner);\n  Cartesian3.add(corner, v, corner);\n  Cartesian3.add(corner, w, corner);\n\n  Cartesian3.subtract(corner, position, toCenter);\n  mag = Cartesian3.dot(direction, toCenter);\n\n  minDist = Math.min(mag, minDist);\n  maxDist = Math.max(mag, maxDist);\n\n  // project sixth corner\n  Cartesian3.subtract(center, u, corner);\n  Cartesian3.add(corner, v, corner);\n  Cartesian3.subtract(corner, w, corner);\n\n  Cartesian3.subtract(corner, position, toCenter);\n  mag = Cartesian3.dot(direction, toCenter);\n\n  minDist = Math.min(mag, minDist);\n  maxDist = Math.max(mag, maxDist);\n\n  // project seventh corner\n  Cartesian3.subtract(center, u, corner);\n  Cartesian3.subtract(corner, v, corner);\n  Cartesian3.add(corner, w, corner);\n\n  Cartesian3.subtract(corner, position, toCenter);\n  mag = Cartesian3.dot(direction, toCenter);\n\n  minDist = Math.min(mag, minDist);\n  maxDist = Math.max(mag, maxDist);\n\n  // project eighth corner\n  Cartesian3.subtract(center, u, corner);\n  Cartesian3.subtract(corner, v, corner);\n  Cartesian3.subtract(corner, w, corner);\n\n  Cartesian3.subtract(corner, position, toCenter);\n  mag = Cartesian3.dot(direction, toCenter);\n\n  minDist = Math.min(mag, minDist);\n  maxDist = Math.max(mag, maxDist);\n\n  result.start = minDist;\n  result.stop = maxDist;\n  return result;\n};\n\nconst scratchBoundingSphere = new BoundingSphere();\n\n/**\n * Determines whether or not a bounding box is hidden from view by the occluder.\n *\n * @param {OrientedBoundingBox} box The bounding box surrounding the occludee object.\n * @param {Occluder} occluder The occluder.\n * @returns {Boolean} <code>true</code> if the box is not visible; otherwise <code>false</code>.\n */\nOrientedBoundingBox.isOccluded = function (box, occluder) {\n  //>>includeStart('debug', pragmas.debug);\n  if (!defined(box)) {\n    throw new DeveloperError(\"box is required.\");\n  }\n  if (!defined(occluder)) {\n    throw new DeveloperError(\"occluder is required.\");\n  }\n  //>>includeEnd('debug');\n\n  const sphere = BoundingSphere.fromOrientedBoundingBox(\n    box,\n    scratchBoundingSphere\n  );\n\n  return !occluder.isBoundingSphereVisible(sphere);\n};\n\n/**\n * Determines which side of a plane the oriented bounding box is located.\n *\n * @param {Plane} plane The plane to test against.\n * @returns {Intersect} {@link Intersect.INSIDE} if the entire box is on the side of the plane\n *                      the normal is pointing, {@link Intersect.OUTSIDE} if the entire box is\n *                      on the opposite side, and {@link Intersect.INTERSECTING} if the box\n *                      intersects the plane.\n */\nOrientedBoundingBox.prototype.intersectPlane = function (plane) {\n  return OrientedBoundingBox.intersectPlane(this, plane);\n};\n\n/**\n * Computes the estimated distance squared from the closest point on a bounding box to a point.\n *\n * @param {Cartesian3} cartesian The point\n * @returns {Number} The estimated distance squared from the bounding sphere to the point.\n *\n * @example\n * // Sort bounding boxes from back to front\n * boxes.sort(function(a, b) {\n *     return b.distanceSquaredTo(camera.positionWC) - a.distanceSquaredTo(camera.positionWC);\n * });\n */\nOrientedBoundingBox.prototype.distanceSquaredTo = function (cartesian) {\n  return OrientedBoundingBox.distanceSquaredTo(this, cartesian);\n};\n\n/**\n * The distances calculated by the vector from the center of the bounding box to position projected onto direction.\n * <br>\n * If you imagine the infinite number of planes with normal direction, this computes the smallest distance to the\n * closest and farthest planes from position that intersect the bounding box.\n *\n * @param {Cartesian3} position The position to calculate the distance from.\n * @param {Cartesian3} direction The direction from position.\n * @param {Interval} [result] A Interval to store the nearest and farthest distances.\n * @returns {Interval} The nearest and farthest distances on the bounding box from position in direction.\n */\nOrientedBoundingBox.prototype.computePlaneDistances = function (\n  position,\n  direction,\n  result\n) {\n  return OrientedBoundingBox.computePlaneDistances(\n    this,\n    position,\n    direction,\n    result\n  );\n};\n\n/**\n * Determines whether or not a bounding box is hidden from view by the occluder.\n *\n * @param {Occluder} occluder The occluder.\n * @returns {Boolean} <code>true</code> if the sphere is not visible; otherwise <code>false</code>.\n */\nOrientedBoundingBox.prototype.isOccluded = function (occluder) {\n  return OrientedBoundingBox.isOccluded(this, occluder);\n};\n\n/**\n * Compares the provided OrientedBoundingBox componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {OrientedBoundingBox} left The first OrientedBoundingBox.\n * @param {OrientedBoundingBox} right The second OrientedBoundingBox.\n * @returns {Boolean} <code>true</code> if left and right are equal, <code>false</code> otherwise.\n */\nOrientedBoundingBox.equals = function (left, right) {\n  return (\n    left === right ||\n    (defined(left) &&\n      defined(right) &&\n      Cartesian3.equals(left.center, right.center) &&\n      Matrix3.equals(left.halfAxes, right.halfAxes))\n  );\n};\n\n/**\n * Duplicates this OrientedBoundingBox instance.\n *\n * @param {OrientedBoundingBox} [result] The object onto which to store the result.\n * @returns {OrientedBoundingBox} The modified result parameter or a new OrientedBoundingBox instance if one was not provided.\n */\nOrientedBoundingBox.prototype.clone = function (result) {\n  return OrientedBoundingBox.clone(this, result);\n};\n\n/**\n * Compares this OrientedBoundingBox against the provided OrientedBoundingBox componentwise and returns\n * <code>true</code> if they are equal, <code>false</code> otherwise.\n *\n * @param {OrientedBoundingBox} [right] The right hand side OrientedBoundingBox.\n * @returns {Boolean} <code>true</code> if they are equal, <code>false</code> otherwise.\n */\nOrientedBoundingBox.prototype.equals = function (right) {\n  return OrientedBoundingBox.equals(this, right);\n};\nexport default OrientedBoundingBox;\n"], "names": ["OrientedBoundingBox", "center", "halfAxes", "this", "Cartesian3", "clone", "defaultValue", "ZERO", "Matrix3", "<PERSON><PERSON><PERSON><PERSON>", "pack", "value", "array", "startingIndex", "Check", "typeOf", "object", "defined", "unpack", "result", "scratchCartesian1", "scratchCartesian2", "scratchCartesian3", "scratchCartesian4", "scratchCartesian5", "scratchCartesian6", "scratchCovarianceResult", "scratchEigenResult", "unitary", "diagonal", "fromPoints", "positions", "length", "i", "meanPoint", "add", "invLength", "multiplyByScalar", "p", "exx", "exy", "exz", "eyy", "eyz", "ezz", "subtract", "x", "y", "z", "covarianceMatrix", "eigenDecomposition", "computeEigenDecomposition", "rotation", "v1", "getColumn", "v2", "v3", "u1", "Number", "MAX_VALUE", "u2", "u3", "l1", "l2", "l3", "Math", "max", "dot", "min", "scale", "multiplyByScale", "scratchOffset", "scratchScale", "fromPlaneExtents", "<PERSON><PERSON><PERSON><PERSON>", "planeXAxis", "planeYAxis", "planeZAxis", "minimumX", "maximumX", "minimumY", "maximumY", "minimumZ", "maximumZ", "DeveloperError", "setColumn", "centerOffset", "multiplyByVector", "scratchRectangleCenterCartographic", "Cartographic", "scratchRectangleCenter", "scratchPerimeterCartographicNC", "scratchPerimeterCartographicNW", "scratchPerimeterCartographicCW", "scratchPerimeterCartographicSW", "scratchPerimeterCartographicSC", "scratchPerimeterCartesianNC", "scratchPerimeterCartesianNW", "scratchPerimeterCartesianCW", "scratchPerimeterCartesianSW", "scratchPerimeterCartesianSC", "scratchPerimeterProjectedNC", "Cartesian2", "scratchPerimeterProjectedNW", "scratchPerimeterProjectedCW", "scratchPerimeterProjectedSW", "scratchPerimeterProjectedSC", "scratchPlane<PERSON><PERSON>in", "scratchPlaneNormal", "scratchPlaneXAxis", "scratchHorizonCartesian", "scratchHorizonProjected", "scratchMaxY", "scratchMinY", "scratchZ", "scratchPlane", "Plane", "UNIT_X", "fromRectangle", "rectangle", "minimumHeight", "maximumHeight", "ellipsoid", "width", "CesiumMath", "TWO_PI", "height", "PI", "equalsEpsilon", "radii", "EPSILON15", "minX", "maxX", "minY", "maxY", "minZ", "maxZ", "plane", "Ellipsoid", "WGS84", "tangentPointCartographic", "Rectangle", "tangentPoint", "cartographicToCartesian", "tangentPlane", "EllipsoidTangentPlane", "lonCenter", "longitude", "latCenter", "south", "north", "latitude", "perimeterCartographicNC", "fromRadians", "perimeterCartographicNW", "west", "perimeterCartographicCW", "perimeterCartographicSW", "perimeterCartographicSC", "perimeterCartesianNC", "perimeterCartesianNW", "perimeterCartesianCW", "perimeterCartesianSW", "perimeterCartesianSC", "perimeterProjectedNC", "projectPointToNearestOnPlane", "perimeterProjectedNW", "perimeterProjectedCW", "perimeterProjectedSW", "perimeterProjectedSC", "getPointDistance", "origin", "xAxis", "yAxis", "zAxis", "fullyAboveEquator", "fullyBelowEquator", "latitudeNearestToEquator", "centerLongitude", "planeNormal", "abs", "EPSILON10", "normalize", "UNIT_Z", "cross", "fromPointNormal", "horizonCartesian", "PI_OVER_TWO", "projectPointOntoPlane", "farZ", "east", "box", "intersectPlane", "normal", "normalX", "normalY", "normalZ", "radEffective", "COLUMN0ROW0", "COLUMN0ROW1", "COLUMN0ROW2", "COLUMN1ROW0", "COLUMN1ROW1", "COLUMN1ROW2", "COLUMN2ROW0", "COLUMN2ROW1", "COLUMN2ROW2", "distanceToPlane", "distance", "Intersect", "OUTSIDE", "INSIDE", "INTERSECTING", "scratchCartesianU", "scratchCartesianV", "scratchCartesianW", "scratchValidAxis2", "scratchValidAxis3", "scratchPPrime", "distanceSquaredTo", "cartesian", "offset", "u", "v", "w", "uHalf", "magnitude", "vHalf", "wHalf", "uValid", "vValid", "wValid", "divideByScalar", "numberOfDegenerateAxes", "validAxis1", "validAxis2", "validAxis3", "degenerateAxis", "crossVector", "UNIT_Y", "EPSILON3", "pPrime", "d", "distanceSquared", "scratchCorner", "scratchToCenter", "computePlaneDistances", "position", "direction", "Interval", "minDist", "POSITIVE_INFINITY", "maxDist", "NEGATIVE_INFINITY", "corner", "toCenter", "mag", "start", "stop", "scratchBoundingSphere", "BoundingSphere", "isOccluded", "occluder", "sphere", "fromOrientedBoundingBox", "isBoundingSphereVisible", "prototype", "equals", "left", "right"], "mappings": "yOAuCA,SAASA,EAAoBC,EAAQC,GAMnCC,KAAKF,OAASG,EAAAA,WAAWC,MAAMC,EAAYA,aAACL,EAAQG,EAAAA,WAAWG,OAM/DJ,KAAKD,SAAWM,EAAAA,QAAQH,MAAMC,EAAYA,aAACJ,EAAUM,EAAAA,QAAQD,MAC/D,CAMAP,EAAoBS,aAClBL,aAAWK,aAAeD,EAAOA,QAACC,aAWpCT,EAAoBU,KAAO,SAAUC,EAAOC,EAAOC,GAWjD,OATAC,EAAAA,MAAMC,OAAOC,OAAO,QAASL,GAC7BG,EAAAA,MAAMG,QAAQ,QAASL,GAGvBC,EAAgBP,EAAYA,aAACO,EAAe,GAE5CT,EAAUA,WAACM,KAAKC,EAAMV,OAAQW,EAAOC,GACrCL,UAAQE,KAAKC,EAAMT,SAAUU,EAAOC,EAAgBT,EAAAA,WAAWK,cAExDG,CACT,EAUAZ,EAAoBkB,OAAS,SAAUN,EAAOC,EAAeM,GAiB3D,OAfAL,EAAAA,MAAMG,QAAQ,QAASL,GAGvBC,EAAgBP,EAAYA,aAACO,EAAe,GAEvCI,EAAAA,QAAQE,KACXA,EAAS,IAAInB,GAGfI,EAAUA,WAACc,OAAON,EAAOC,EAAeM,EAAOlB,QAC/CO,EAAAA,QAAQU,OACNN,EACAC,EAAgBT,EAAUA,WAACK,aAC3BU,EAAOjB,UAEFiB,CACT,EAEA,MAAMC,EAAoB,IAAIhB,EAAAA,WACxBiB,EAAoB,IAAIjB,EAAAA,WACxBkB,EAAoB,IAAIlB,EAAAA,WACxBmB,EAAoB,IAAInB,EAAAA,WACxBoB,EAAoB,IAAIpB,EAAAA,WACxBqB,EAAoB,IAAIrB,EAAAA,WACxBsB,EAA0B,IAAIlB,EAAAA,QAC9BmB,EAAqB,CACzBC,QAAS,IAAIpB,EAAAA,QACbqB,SAAU,IAAIrB,EAAAA,SAgBhBR,EAAoB8B,WAAa,SAAUC,EAAWZ,GAKpD,GAJKF,EAAAA,QAAQE,KACXA,EAAS,IAAInB,IAGViB,EAAOA,QAACc,IAAmC,IAArBA,EAAUC,OAGnC,OAFAb,EAAOjB,SAAWM,EAAOA,QAACD,KAC1BY,EAAOlB,OAASG,EAAUA,WAACG,KACpBY,EAGT,IAAIc,EACJ,MAAMD,EAASD,EAAUC,OAEnBE,EAAY9B,EAAAA,WAAWC,MAAM0B,EAAU,GAAIX,GACjD,IAAKa,EAAI,EAAGA,EAAID,EAAQC,IACtB7B,EAAUA,WAAC+B,IAAID,EAAWH,EAAUE,GAAIC,GAE1C,MAAME,EAAY,EAAMJ,EACxB5B,EAAAA,WAAWiC,iBAAiBH,EAAWE,EAAWF,GAElD,IAMII,EANAC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EACNC,EAAM,EAGV,IAAKX,EAAI,EAAGA,EAAID,EAAQC,IACtBK,EAAIlC,EAAUA,WAACyC,SAASd,EAAUE,GAAIC,EAAWb,GACjDkB,GAAOD,EAAEQ,EAAIR,EAAEQ,EACfN,GAAOF,EAAEQ,EAAIR,EAAES,EACfN,GAAOH,EAAEQ,EAAIR,EAAEU,EACfN,GAAOJ,EAAES,EAAIT,EAAES,EACfJ,GAAOL,EAAES,EAAIT,EAAEU,EACfJ,GAAON,EAAEU,EAAIV,EAAEU,EAGjBT,GAAOH,EACPI,GAAOJ,EACPK,GAAOL,EACPM,GAAON,EACPO,GAAOP,EACPQ,GAAOR,EAEP,MAAMa,EAAmBvB,EACzBuB,EAAiB,GAAKV,EACtBU,EAAiB,GAAKT,EACtBS,EAAiB,GAAKR,EACtBQ,EAAiB,GAAKT,EACtBS,EAAiB,GAAKP,EACtBO,EAAiB,GAAKN,EACtBM,EAAiB,GAAKR,EACtBQ,EAAiB,GAAKN,EACtBM,EAAiB,GAAKL,EAEtB,MAAMM,EAAqB1C,EAAAA,QAAQ2C,0BACjCF,EACAtB,GAEIyB,EAAW5C,EAAAA,QAAQH,MAAM6C,EAAmBtB,QAAST,EAAOjB,UAElE,IAAImD,EAAK7C,EAAAA,QAAQ8C,UAAUF,EAAU,EAAG7B,GACpCgC,EAAK/C,EAAAA,QAAQ8C,UAAUF,EAAU,EAAG5B,GACpCgC,EAAKhD,EAAAA,QAAQ8C,UAAUF,EAAU,EAAG3B,GAEpCgC,GAAMC,OAAOC,UACbC,GAAMF,OAAOC,UACbE,GAAMH,OAAOC,UACbG,EAAKJ,OAAOC,UACZI,EAAKL,OAAOC,UACZK,EAAKN,OAAOC,UAEhB,IAAK1B,EAAI,EAAGA,EAAID,EAAQC,IACtBK,EAAIP,EAAUE,GACdwB,EAAKQ,KAAKC,IAAI9D,EAAUA,WAAC+D,IAAId,EAAIf,GAAImB,GACrCG,EAAKK,KAAKC,IAAI9D,EAAUA,WAAC+D,IAAIZ,EAAIjB,GAAIsB,GACrCC,EAAKI,KAAKC,IAAI9D,EAAUA,WAAC+D,IAAIX,EAAIlB,GAAIuB,GAErCC,EAAKG,KAAKG,IAAIhE,EAAUA,WAAC+D,IAAId,EAAIf,GAAIwB,GACrCC,EAAKE,KAAKG,IAAIhE,EAAUA,WAAC+D,IAAIZ,EAAIjB,GAAIyB,GACrCC,EAAKC,KAAKG,IAAIhE,EAAUA,WAAC+D,IAAIX,EAAIlB,GAAI0B,GAGvCX,EAAKjD,EAAAA,WAAWiC,iBAAiBgB,EAAI,IAAOS,EAAKL,GAAKJ,GACtDE,EAAKnD,EAAAA,WAAWiC,iBAAiBkB,EAAI,IAAOQ,EAAKH,GAAKL,GACtDC,EAAKpD,EAAAA,WAAWiC,iBAAiBmB,EAAI,IAAOQ,EAAKH,GAAKL,GAEtD,MAAMvD,EAASG,EAAAA,WAAW+B,IAAIkB,EAAIE,EAAIpC,EAAOlB,QAC7CG,EAAAA,WAAW+B,IAAIlC,EAAQuD,EAAIvD,GAE3B,MAAMoE,EAAQ/C,EAOd,OANA+C,EAAMvB,EAAIW,EAAKK,EACfO,EAAMtB,EAAIa,EAAKG,EACfM,EAAMrB,EAAIa,EAAKG,EACf5D,EAAAA,WAAWiC,iBAAiBgC,EAAO,GAAKA,GACxC7D,EAAOA,QAAC8D,gBAAgBnD,EAAOjB,SAAUmE,EAAOlD,EAAOjB,UAEhDiB,CACT,EAEA,MAAMoD,EAAgB,IAAInE,EAAAA,WACpBoE,EAAe,IAAIpE,EAAAA,WACzB,SAASqE,EACPC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAhE,GAGA,KACGF,EAAAA,QAAQ6D,IACR7D,EAAAA,QAAQ8D,IACR9D,EAAAA,QAAQ+D,IACR/D,EAAAA,QAAQgE,IACRhE,EAAAA,QAAQiE,IACRjE,EAAAA,QAAQkE,IAET,MAAM,IAAIC,EAAcA,eACtB,qDAKCnE,EAAAA,QAAQE,KACXA,EAAS,IAAInB,GAGf,MAAME,EAAWiB,EAAOjB,SACxBM,EAAOA,QAAC6E,UAAUnF,EAAU,EAAGyE,EAAYzE,GAC3CM,EAAOA,QAAC6E,UAAUnF,EAAU,EAAG0E,EAAY1E,GAC3CM,EAAOA,QAAC6E,UAAUnF,EAAU,EAAG2E,EAAY3E,GAE3C,IAAIoF,EAAef,EACnBe,EAAaxC,GAAKgC,EAAWC,GAAY,EACzCO,EAAavC,GAAKiC,EAAWC,GAAY,EACzCK,EAAatC,GAAKkC,EAAWC,GAAY,EAEzC,MAAMd,EAAQG,EACdH,EAAMvB,GAAKiC,EAAWD,GAAY,EAClCT,EAAMtB,GAAKkC,EAAWD,GAAY,EAClCX,EAAMrB,GAAKmC,EAAWD,GAAY,EAElC,MAAMjF,EAASkB,EAAOlB,OAKtB,OAJAqF,EAAe9E,EAAOA,QAAC+E,iBAAiBrF,EAAUoF,EAAcA,GAChElF,EAAAA,WAAW+B,IAAIuC,EAAaY,EAAcrF,GAC1CO,EAAAA,QAAQ8D,gBAAgBpE,EAAUmE,EAAOnE,GAElCiB,CACT,CAEA,MAAMqE,EAAqC,IAAIC,EAAAA,aACzCC,EAAyB,IAAItF,EAAAA,WAC7BuF,EAAiC,IAAIF,EAAAA,aACrCG,EAAiC,IAAIH,EAAAA,aACrCI,EAAiC,IAAIJ,EAAAA,aACrCK,EAAiC,IAAIL,EAAAA,aACrCM,EAAiC,IAAIN,EAAAA,aACrCO,EAA8B,IAAI5F,EAAAA,WAClC6F,EAA8B,IAAI7F,EAAAA,WAClC8F,EAA8B,IAAI9F,EAAAA,WAClC+F,EAA8B,IAAI/F,EAAAA,WAClCgG,EAA8B,IAAIhG,EAAAA,WAClCiG,EAA8B,IAAIC,EAAAA,WAClCC,EAA8B,IAAID,EAAAA,WAClCE,EAA8B,IAAIF,EAAAA,WAClCG,EAA8B,IAAIH,EAAAA,WAClCI,EAA8B,IAAIJ,EAAAA,WAElCK,EAAqB,IAAIvG,EAAAA,WACzBwG,EAAqB,IAAIxG,EAAAA,WACzByG,EAAoB,IAAIzG,EAAAA,WACxB0G,EAA0B,IAAI1G,EAAAA,WAC9B2G,EAA0B,IAAIT,EAAAA,WAC9BU,EAAc,IAAI5G,EAAAA,WAClB6G,EAAc,IAAI7G,EAAAA,WAClB8G,EAAW,IAAI9G,EAAAA,WACf+G,EAAe,IAAIC,EAAKA,MAAChH,EAAUA,WAACiH,OAAQ,GAiBlDrH,EAAoBsH,cAAgB,SAClCC,EACAC,EACAC,EACAC,EACAvG,GAGA,IAAKF,EAAAA,QAAQsG,GACX,MAAM,IAAInC,EAAAA,eAAe,yBAE3B,GAAImC,EAAUI,MAAQ,GAAOJ,EAAUI,MAAQC,EAAUA,WAACC,OACxD,MAAM,IAAIzC,EAAAA,eAAe,8CAE3B,GAAImC,EAAUO,OAAS,GAAOP,EAAUO,OAASF,EAAUA,WAACG,GAC1D,MAAM,IAAI3C,EAAAA,eAAe,6CAE3B,GACEnE,EAAAA,QAAQyG,KACPE,EAAUA,WAACI,cACVN,EAAUO,MAAMnF,EAChB4E,EAAUO,MAAMlF,EAChB6E,EAAAA,WAAWM,WAGb,MAAM,IAAI9C,EAAcA,eACtB,qEASJ,IAAI+C,EAAMC,EAAMC,EAAMC,EAAMC,EAAMC,EAAMC,EAExC,GANAjB,EAAgBlH,EAAYA,aAACkH,EAAe,GAC5CC,EAAgBnH,EAAYA,aAACmH,EAAe,GAC5CC,EAAYpH,EAAAA,aAAaoH,EAAWgB,EAASA,UAACC,OAI1CpB,EAAUI,OAASC,EAAUA,WAACG,GAAI,CAEpC,MAAMa,EAA2BC,EAAAA,UAAU5I,OACzCsH,EACA/B,GAEIsD,EAAepB,EAAUqB,wBAC7BH,EACAlD,GAEIsD,EAAe,IAAIC,EAAAA,sBAAsBH,EAAcpB,GAC7De,EAAQO,EAAaP,MAGrB,MAAMS,EAAYN,EAAyBO,UACrCC,EACJ7B,EAAU8B,MAAQ,GAAO9B,EAAU+B,MAAQ,EACvC,EACAV,EAAyBW,SAGzBC,EAA0B/D,EAAAA,aAAagE,YAC3CP,EACA3B,EAAU+B,MACV7B,EACA9B,GAEI+D,EAA0BjE,EAAAA,aAAagE,YAC3ClC,EAAUoC,KACVpC,EAAU+B,MACV7B,EACA7B,GAEIgE,EAA0BnE,EAAAA,aAAagE,YAC3ClC,EAAUoC,KACVP,EACA3B,EACA5B,GAEIgE,EAA0BpE,EAAAA,aAAagE,YAC3ClC,EAAUoC,KACVpC,EAAU8B,MACV5B,EACA3B,GAEIgE,EAA0BrE,EAAAA,aAAagE,YAC3CP,EACA3B,EAAU8B,MACV5B,EACA1B,GAGIgE,EAAuBrC,EAAUqB,wBACrCS,EACAxD,GAEF,IAAIgE,EAAuBtC,EAAUqB,wBACnCW,EACAzD,GAEF,MAAMgE,EAAuBvC,EAAUqB,wBACrCa,EACA1D,GAEF,IAAIgE,EAAuBxC,EAAUqB,wBACnCc,EACA1D,GAEF,MAAMgE,EAAuBzC,EAAUqB,wBACrCe,EACA1D,GAGIgE,EAAuBpB,EAAaqB,6BACxCN,EACA1D,GAEIiE,EAAuBtB,EAAaqB,6BACxCL,EACAzD,GAEIgE,EAAuBvB,EAAaqB,6BACxCJ,EACAzD,GAEIgE,EAAuBxB,EAAaqB,6BACxCH,EACAzD,GAEIgE,EAAuBzB,EAAaqB,6BACxCF,EACAzD,GA8BF,OA3BAyB,EAAOlE,KAAKG,IACVkG,EAAqBxH,EACrByH,EAAqBzH,EACrB0H,EAAqB1H,GAEvBsF,GAAQD,EAERG,EAAOrE,KAAKC,IAAIoG,EAAqBvH,EAAGqH,EAAqBrH,GAC7DsF,EAAOpE,KAAKG,IAAIoG,EAAqBzH,EAAG0H,EAAqB1H,GAG7D2G,EAAwB5B,OAAS+B,EAAwB/B,OAASN,EAClEwC,EAAuBtC,EAAUqB,wBAC/BW,EACAzD,GAEFiE,EAAuBxC,EAAUqB,wBAC/Bc,EACA1D,GAGFoC,EAAOtE,KAAKG,IACVgD,QAAMsD,iBAAiBjC,EAAOuB,GAC9B5C,QAAMsD,iBAAiBjC,EAAOyB,IAEhC1B,EAAOf,EAEAhD,EACLuE,EAAa2B,OACb3B,EAAa4B,MACb5B,EAAa6B,MACb7B,EAAa8B,MACb3C,EACAC,EACAC,EACAC,EACAC,EACAC,EACArH,EAEH,CAGD,MAAM4J,EAAoBxD,EAAU8B,MAAQ,EACtC2B,EAAoBzD,EAAU+B,MAAQ,EACtC2B,EAA2BF,EAC7BxD,EAAU8B,MACV2B,EACAzD,EAAU+B,MACV,EACE4B,EAAkBrC,EAAAA,UAAU5I,OAChCsH,EACA/B,GACA2D,UAIIzE,EAActE,EAAAA,WAAWqJ,YAC7ByB,EACAD,EACAxD,EACAC,EACAf,GAEFjC,EAAY1B,EAAI,EAChB,MAGMmI,EAFJlH,KAAKmH,IAAI1G,EAAY5B,GAAK8E,EAAUA,WAACyD,WACrCpH,KAAKmH,IAAI1G,EAAY3B,GAAK6E,EAAAA,WAAWyD,UAGnCjL,EAAAA,WAAWiH,OADXjH,aAAWkL,UAAU5G,EAAakC,GAEhChC,EAAaxE,EAAUA,WAACmL,OACxB5G,EAAavE,EAAAA,WAAWoL,MAC5BL,EACAvG,EACAiC,GAEF4B,EAAQrB,EAAKA,MAACqE,gBAAgB/G,EAAayG,EAAahE,GAGxD,MAAMuE,EAAmBtL,EAAAA,WAAWqJ,YAClCyB,EAAkBtD,EAAUA,WAAC+D,YAC7BV,EACAxD,EACAC,EACAZ,GAEFsB,EAAOhI,EAAUA,WAAC+D,IAChBiD,EAAAA,MAAMwE,sBACJnD,EACAiD,EACA3E,GAEFpC,GAEFwD,GAAQC,EAGRE,EAAOlI,EAAUA,WAACqJ,YAChB,EACAlC,EAAU+B,MACV0B,EAAoBxD,EAAgBC,EACpCC,EACAV,GACAhE,EACFqF,EAAOjI,EAAUA,WAACqJ,YAChB,EACAlC,EAAU8B,MACV0B,EAAoBvD,EAAgBC,EACpCC,EACAT,GACAjE,EAEF,MAAM6I,GAAOzL,EAAAA,WAAWqJ,YACtBlC,EAAUuE,KACVb,EACAxD,EACAC,EACAR,GAMF,OAJAqB,EAAOnB,EAAAA,MAAMsD,iBAAiBjC,EAAOoD,IACrCrD,EAAO,EAGA/D,EACLC,EACAC,EACAC,EACAuG,EACAhD,EACAC,EACAC,EACAC,EACAC,EACAC,EACArH,EAEJ,EASAnB,EAAoBK,MAAQ,SAAU0L,EAAK5K,GACzC,GAAKF,EAAAA,QAAQ8K,GAIb,OAAK9K,EAAAA,QAAQE,IAIbf,EAAUA,WAACC,MAAM0L,EAAI9L,OAAQkB,EAAOlB,QACpCO,EAAOA,QAACH,MAAM0L,EAAI7L,SAAUiB,EAAOjB,UAE5BiB,GANE,IAAInB,EAAoB+L,EAAI9L,OAAQ8L,EAAI7L,SAOnD,EAYAF,EAAoBgM,eAAiB,SAAUD,EAAKtD,GAElD,IAAKxH,EAAAA,QAAQ8K,GACX,MAAM,IAAI3G,EAAAA,eAAe,oBAG3B,IAAKnE,EAAAA,QAAQwH,GACX,MAAM,IAAIrD,EAAAA,eAAe,sBAI3B,MAAMnF,EAAS8L,EAAI9L,OACbgM,EAASxD,EAAMwD,OACf/L,EAAW6L,EAAI7L,SACfgM,EAAUD,EAAOnJ,EACrBqJ,EAAUF,EAAOlJ,EACjBqJ,EAAUH,EAAOjJ,EAEbqJ,EACJpI,KAAKmH,IACHc,EAAUhM,EAASM,EAAOA,QAAC8L,aACzBH,EAAUjM,EAASM,EAAOA,QAAC+L,aAC3BH,EAAUlM,EAASM,EAAOA,QAACgM,cAE/BvI,KAAKmH,IACHc,EAAUhM,EAASM,EAAOA,QAACiM,aACzBN,EAAUjM,EAASM,EAAOA,QAACkM,aAC3BN,EAAUlM,EAASM,EAAOA,QAACmM,cAE/B1I,KAAKmH,IACHc,EAAUhM,EAASM,EAAOA,QAACoM,aACzBT,EAAUjM,EAASM,EAAOA,QAACqM,aAC3BT,EAAUlM,EAASM,EAAOA,QAACsM,cAE3BC,EAAkB3M,EAAAA,WAAW+D,IAAI8H,EAAQhM,GAAUwI,EAAMuE,SAE/D,OAAID,IAAoBV,EAEfY,EAAAA,UAAUC,QACRH,GAAmBV,EAErBY,EAAAA,UAAUE,OAEZF,EAAAA,UAAUG,YACnB,EAEA,MAAMC,EAAoB,IAAIjN,EAAAA,WACxBkN,EAAoB,IAAIlN,EAAAA,WACxBmN,EAAoB,IAAInN,EAAAA,WACxBoN,EAAoB,IAAIpN,EAAAA,WACxBqN,EAAoB,IAAIrN,EAAAA,WACxBsN,EAAgB,IAAItN,EAAAA,WAe1BJ,EAAoB2N,kBAAoB,SAAU5B,EAAK6B,GAIrD,IAAK3M,EAAAA,QAAQ8K,GACX,MAAM,IAAI3G,EAAAA,eAAe,oBAE3B,IAAKnE,EAAAA,QAAQ2M,GACX,MAAM,IAAIxI,EAAAA,eAAe,0BAI3B,MAAMyI,EAASzN,EAAAA,WAAWyC,SAAS+K,EAAW7B,EAAI9L,OAAQsE,GAEpDrE,EAAW6L,EAAI7L,SACrB,IAAI4N,EAAItN,EAAAA,QAAQ8C,UAAUpD,EAAU,EAAGmN,GACnCU,EAAIvN,EAAAA,QAAQ8C,UAAUpD,EAAU,EAAGoN,GACnCU,EAAIxN,EAAAA,QAAQ8C,UAAUpD,EAAU,EAAGqN,GAEvC,MAAMU,EAAQ7N,EAAAA,WAAW8N,UAAUJ,GAC7BK,EAAQ/N,EAAAA,WAAW8N,UAAUH,GAC7BK,EAAQhO,EAAAA,WAAW8N,UAAUF,GAEnC,IAAIK,GAAS,EACTC,GAAS,EACTC,GAAS,EAETN,EAAQ,EACV7N,EAAAA,WAAWoO,eAAeV,EAAGG,EAAOH,GAEpCO,GAAS,EAGPF,EAAQ,EACV/N,EAAAA,WAAWoO,eAAeT,EAAGI,EAAOJ,GAEpCO,GAAS,EAGPF,EAAQ,EACVhO,EAAAA,WAAWoO,eAAeR,EAAGI,EAAOJ,GAEpCO,GAAS,EAGX,MAAME,GAA0BJ,GAAUC,GAAUC,EACpD,IAAIG,EACAC,EACAC,EAEJ,GAA+B,IAA3BH,EAA8B,CAChC,IAAII,EAAiBf,EACrBY,EAAaX,EACbY,EAAaX,EACRM,EAGOC,IACVM,EAAiBb,EACjBW,EAAab,IAJbe,EAAiBd,EACjBW,EAAaZ,GAMfc,EAAaxO,EAAUA,WAACoL,MAAMkD,EAAYC,EAAYlB,GAElDoB,IAAmBf,EACrBA,EAAIc,EACKC,IAAmBd,EAC5BA,EAAIa,EACKC,IAAmBb,IAC5BA,EAAIY,EAEV,MAAS,GAA+B,IAA3BH,EAA8B,CACvCC,EAAaZ,EACTQ,EACFI,EAAaX,EACJQ,IACTG,EAAaV,GAGf,IAAIc,EAAc1O,EAAUA,WAAC2O,OACzBD,EAAY9G,cAAc0G,EAAY9G,EAAUA,WAACoH,YACnDF,EAAc1O,EAAUA,WAACiH,QAG3BsH,EAAavO,EAAUA,WAACoL,MAAMkD,EAAYI,EAAatB,GACvDpN,EAAAA,WAAWkL,UAAUqD,EAAYA,GACjCC,EAAaxO,EAAUA,WAACoL,MAAMkD,EAAYC,EAAYlB,GACtDrN,EAAAA,WAAWkL,UAAUsD,EAAYA,GAE7BF,IAAeZ,GACjBC,EAAIY,EACJX,EAAIY,GACKF,IAAeX,GACxBC,EAAIW,EACJb,EAAIc,GACKF,IAAeV,IACxBF,EAAIa,EACJZ,EAAIa,EAEV,MAAwC,IAA3BH,IACTX,EAAI1N,EAAUA,WAACiH,OACf0G,EAAI3N,EAAUA,WAAC2O,OACff,EAAI5N,EAAUA,WAACmL,QAGjB,MAAM0D,EAASvB,EACfuB,EAAOnM,EAAI1C,EAAUA,WAAC+D,IAAI0J,EAAQC,GAClCmB,EAAOlM,EAAI3C,EAAUA,WAAC+D,IAAI0J,EAAQE,GAClCkB,EAAOjM,EAAI5C,EAAUA,WAAC+D,IAAI0J,EAAQG,GAElC,IACIkB,EADAC,EAAkB,EA2BtB,OAxBIF,EAAOnM,GAAKmL,GACdiB,EAAID,EAAOnM,EAAImL,EACfkB,GAAmBD,EAAIA,GACdD,EAAOnM,EAAImL,IACpBiB,EAAID,EAAOnM,EAAImL,EACfkB,GAAmBD,EAAIA,GAGrBD,EAAOlM,GAAKoL,GACde,EAAID,EAAOlM,EAAIoL,EACfgB,GAAmBD,EAAIA,GACdD,EAAOlM,EAAIoL,IACpBe,EAAID,EAAOlM,EAAIoL,EACfgB,GAAmBD,EAAIA,GAGrBD,EAAOjM,GAAKoL,GACdc,EAAID,EAAOjM,EAAIoL,EACfe,GAAmBD,EAAIA,GACdD,EAAOjM,EAAIoL,IACpBc,EAAID,EAAOjM,EAAIoL,EACfe,GAAmBD,EAAIA,GAGlBC,CACT,EAEA,MAAMC,EAAgB,IAAIhP,EAAAA,WACpBiP,EAAkB,IAAIjP,EAAAA,WAc5BJ,EAAoBsP,sBAAwB,SAC1CvD,EACAwD,EACAC,EACArO,GAGA,IAAKF,EAAAA,QAAQ8K,GACX,MAAM,IAAI3G,EAAAA,eAAe,oBAG3B,IAAKnE,EAAAA,QAAQsO,GACX,MAAM,IAAInK,EAAAA,eAAe,yBAG3B,IAAKnE,EAAAA,QAAQuO,GACX,MAAM,IAAIpK,EAAAA,eAAe,0BAItBnE,EAAAA,QAAQE,KACXA,EAAS,IAAIsO,EAAAA,UAGf,IAAIC,EAAUhM,OAAOiM,kBACjBC,EAAUlM,OAAOmM,kBAErB,MAAM5P,EAAS8L,EAAI9L,OACbC,EAAW6L,EAAI7L,SAEf4N,EAAItN,EAAAA,QAAQ8C,UAAUpD,EAAU,EAAGmN,GACnCU,EAAIvN,EAAAA,QAAQ8C,UAAUpD,EAAU,EAAGoN,GACnCU,EAAIxN,EAAAA,QAAQ8C,UAAUpD,EAAU,EAAGqN,GAGnCuC,EAAS1P,EAAAA,WAAW+B,IAAI2L,EAAGC,EAAGqB,GACpChP,EAAAA,WAAW+B,IAAI2N,EAAQ9B,EAAG8B,GAC1B1P,EAAAA,WAAW+B,IAAI2N,EAAQ7P,EAAQ6P,GAE/B,MAAMC,EAAW3P,EAAAA,WAAWyC,SAASiN,EAAQP,EAAUF,GACvD,IAAIW,EAAM5P,EAAUA,WAAC+D,IAAIqL,EAAWO,GAoFpC,OAlFAL,EAAUzL,KAAKG,IAAI4L,EAAKN,GACxBE,EAAU3L,KAAKC,IAAI8L,EAAKJ,GAGxBxP,EAAAA,WAAW+B,IAAIlC,EAAQ6N,EAAGgC,GAC1B1P,EAAAA,WAAW+B,IAAI2N,EAAQ/B,EAAG+B,GAC1B1P,EAAAA,WAAWyC,SAASiN,EAAQ9B,EAAG8B,GAE/B1P,EAAAA,WAAWyC,SAASiN,EAAQP,EAAUQ,GACtCC,EAAM5P,EAAAA,WAAW+D,IAAIqL,EAAWO,GAEhCL,EAAUzL,KAAKG,IAAI4L,EAAKN,GACxBE,EAAU3L,KAAKC,IAAI8L,EAAKJ,GAGxBxP,EAAAA,WAAW+B,IAAIlC,EAAQ6N,EAAGgC,GAC1B1P,EAAAA,WAAWyC,SAASiN,EAAQ/B,EAAG+B,GAC/B1P,EAAAA,WAAW+B,IAAI2N,EAAQ9B,EAAG8B,GAE1B1P,EAAAA,WAAWyC,SAASiN,EAAQP,EAAUQ,GACtCC,EAAM5P,EAAAA,WAAW+D,IAAIqL,EAAWO,GAEhCL,EAAUzL,KAAKG,IAAI4L,EAAKN,GACxBE,EAAU3L,KAAKC,IAAI8L,EAAKJ,GAGxBxP,EAAAA,WAAW+B,IAAIlC,EAAQ6N,EAAGgC,GAC1B1P,EAAAA,WAAWyC,SAASiN,EAAQ/B,EAAG+B,GAC/B1P,EAAAA,WAAWyC,SAASiN,EAAQ9B,EAAG8B,GAE/B1P,EAAAA,WAAWyC,SAASiN,EAAQP,EAAUQ,GACtCC,EAAM5P,EAAAA,WAAW+D,IAAIqL,EAAWO,GAEhCL,EAAUzL,KAAKG,IAAI4L,EAAKN,GACxBE,EAAU3L,KAAKC,IAAI8L,EAAKJ,GAGxBxP,EAAAA,WAAWyC,SAAS5C,EAAQ6N,EAAGgC,GAC/B1P,EAAAA,WAAW+B,IAAI2N,EAAQ/B,EAAG+B,GAC1B1P,EAAAA,WAAW+B,IAAI2N,EAAQ9B,EAAG8B,GAE1B1P,EAAAA,WAAWyC,SAASiN,EAAQP,EAAUQ,GACtCC,EAAM5P,EAAAA,WAAW+D,IAAIqL,EAAWO,GAEhCL,EAAUzL,KAAKG,IAAI4L,EAAKN,GACxBE,EAAU3L,KAAKC,IAAI8L,EAAKJ,GAGxBxP,EAAAA,WAAWyC,SAAS5C,EAAQ6N,EAAGgC,GAC/B1P,EAAAA,WAAW+B,IAAI2N,EAAQ/B,EAAG+B,GAC1B1P,EAAAA,WAAWyC,SAASiN,EAAQ9B,EAAG8B,GAE/B1P,EAAAA,WAAWyC,SAASiN,EAAQP,EAAUQ,GACtCC,EAAM5P,EAAAA,WAAW+D,IAAIqL,EAAWO,GAEhCL,EAAUzL,KAAKG,IAAI4L,EAAKN,GACxBE,EAAU3L,KAAKC,IAAI8L,EAAKJ,GAGxBxP,EAAAA,WAAWyC,SAAS5C,EAAQ6N,EAAGgC,GAC/B1P,EAAAA,WAAWyC,SAASiN,EAAQ/B,EAAG+B,GAC/B1P,EAAAA,WAAW+B,IAAI2N,EAAQ9B,EAAG8B,GAE1B1P,EAAAA,WAAWyC,SAASiN,EAAQP,EAAUQ,GACtCC,EAAM5P,EAAAA,WAAW+D,IAAIqL,EAAWO,GAEhCL,EAAUzL,KAAKG,IAAI4L,EAAKN,GACxBE,EAAU3L,KAAKC,IAAI8L,EAAKJ,GAGxBxP,EAAAA,WAAWyC,SAAS5C,EAAQ6N,EAAGgC,GAC/B1P,EAAAA,WAAWyC,SAASiN,EAAQ/B,EAAG+B,GAC/B1P,EAAAA,WAAWyC,SAASiN,EAAQ9B,EAAG8B,GAE/B1P,EAAAA,WAAWyC,SAASiN,EAAQP,EAAUQ,GACtCC,EAAM5P,EAAAA,WAAW+D,IAAIqL,EAAWO,GAEhCL,EAAUzL,KAAKG,IAAI4L,EAAKN,GACxBE,EAAU3L,KAAKC,IAAI8L,EAAKJ,GAExBzO,EAAO8O,MAAQP,EACfvO,EAAO+O,KAAON,EACPzO,CACT,EAEA,MAAMgP,GAAwB,IAAIC,EAAAA,eASlCpQ,EAAoBqQ,WAAa,SAAUtE,EAAKuE,GAE9C,IAAKrP,EAAAA,QAAQ8K,GACX,MAAM,IAAI3G,EAAAA,eAAe,oBAE3B,IAAKnE,EAAAA,QAAQqP,GACX,MAAM,IAAIlL,EAAAA,eAAe,yBAI3B,MAAMmL,EAASH,EAAAA,eAAeI,wBAC5BzE,EACAoE,IAGF,OAAQG,EAASG,wBAAwBF,EAC3C,EAWAvQ,EAAoB0Q,UAAU1E,eAAiB,SAAUvD,GACvD,OAAOzI,EAAoBgM,eAAe7L,KAAMsI,EAClD,EAcAzI,EAAoB0Q,UAAU/C,kBAAoB,SAAUC,GAC1D,OAAO5N,EAAoB2N,kBAAkBxN,KAAMyN,EACrD,EAaA5N,EAAoB0Q,UAAUpB,sBAAwB,SACpDC,EACAC,EACArO,GAEA,OAAOnB,EAAoBsP,sBACzBnP,KACAoP,EACAC,EACArO,EAEJ,EAQAnB,EAAoB0Q,UAAUL,WAAa,SAAUC,GACnD,OAAOtQ,EAAoBqQ,WAAWlQ,KAAMmQ,EAC9C,EAUAtQ,EAAoB2Q,OAAS,SAAUC,EAAMC,GAC3C,OACED,IAASC,GACR5P,EAAAA,QAAQ2P,IACP3P,EAAAA,QAAQ4P,IACRzQ,EAAAA,WAAWuQ,OAAOC,EAAK3Q,OAAQ4Q,EAAM5Q,SACrCO,EAAOA,QAACmQ,OAAOC,EAAK1Q,SAAU2Q,EAAM3Q,SAE1C,EAQAF,EAAoB0Q,UAAUrQ,MAAQ,SAAUc,GAC9C,OAAOnB,EAAoBK,MAAMF,KAAMgB,EACzC,EASAnB,EAAoB0Q,UAAUC,OAAS,SAAUE,GAC/C,OAAO7Q,EAAoB2Q,OAAOxQ,KAAM0Q,EAC1C"}