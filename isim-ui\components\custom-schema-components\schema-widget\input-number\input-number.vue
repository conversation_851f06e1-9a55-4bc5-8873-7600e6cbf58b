<template>
  <n-input-number ref="inputNumberRef" v-bind="$attrs" v-model:value="modelValue" class="input-number" :style="{ width }">
    <template #suffix> {{ unit }} </template>
  </n-input-number>
</template>

<script lang="ts" setup>
import { NInputNumber } from 'naive-ui';

defineOptions({
  name: 'InputNumber'
});

defineProps({
  unit: {
    type: String,
    default: '%'
  },
  width: {
    type: String,
    default: '100%'
  }
});

const modelValue = defineModel<number>();

const inputNumberRef = shallowRef<InstanceType<typeof NInputNumber>>();

defineExpose({ inputNumberRef });
</script>

<style scoped lang="less">
.input-number {
  :deep(.n-input) {
    &:hover {
      box-shadow: 0 0 0 1px var(--primary-color-hover) inset;
    }
  }
  :deep(.n-input-wrapper) {
    background: var(--select-bg-color);
    border-radius: var(--el-border-radius-base);
    border: 1px solid var(--border-color);
  }
}
</style>
