@import "./draggable-var.css";
@import './icon-button-var.css';
@import "./button-var.css";

:root {

  --isim-global-bgc-val: var(--model-bg-color);
  --isim-global-bgc: rgba(var(--isim-global-bgc-val), 1);

  --isim-bgc-color-val: var(--model-bg-color-val);
  --isim-bgc-color: rgba(var(--isim-bgc-color-val), 1);

  --isim-border-color-val: 0,0,0;
  --isim-border-color: rgba(var(--isim-border-color-val), 1);

  --isim-primary-color-val: var(--primary-color);
  --isim-primary-color: rgba(var(--isim-primary-color-val), 1);

  --isim-success-color-val: 11, 185, 0;
  --isim-success-color: rgba(var(--isim-success-color-val), 1);

  --isim-warn-color-val: 255, 174, 0;
  --isim-warn-color: rgba(var(--isim-warn-color-val), 1);

  --isim-error-color-val: 11, 185, 0;
  --isim-error-color: rgba(var(--isim-error-color-val), 1);

  --isim-danger-color-val: 11, 185, 0;
  --isim-danger-color: rgba(var(--isim-danger-color-val), 1);

  --isim-text-color-val: var(--text-color);
  --isim-text-color: rgba(var(--isim-text-color-val));
}
