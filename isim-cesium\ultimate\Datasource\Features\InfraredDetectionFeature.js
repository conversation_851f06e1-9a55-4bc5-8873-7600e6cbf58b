import VisibleLightDetectionFeature from './VisibleLightDetectionFeature';
import InfraredDetectionPrimitive from '../../Modules/VisualizationModel/InfraredDetectionVisualizer';

export default class InfraredDetectionFeature extends VisibleLightDetectionFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
  }

  create() {
    this.primitive = new InfraredDetectionPrimitive();
  }
}
