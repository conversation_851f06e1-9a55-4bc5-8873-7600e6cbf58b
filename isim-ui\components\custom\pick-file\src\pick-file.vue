<!--
* @Author: 宋计民
* @Date: 2024/1/24
* @Version: 1.0
* @Content: pick-file.vue
-->
<template>
  <span @click="handleDefaultClick">
    <slot> {{ label }} </slot>
    <input type="file" ref="fileRef" v-show="false" @change="changeEvent" :multiple="multiple" :accept="accept" />
  </span>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SimPickFile'
});
const modelValue = defineModel<File[]>({ default: [] });
const emits = defineEmits(['change']);
const props = withDefaults(
  defineProps<{
    label?: string;
    click?: boolean;
    multiple?: boolean;
    accept?: string;
  }>(),
  {
    label: '选择',
    click: true
  }
);
const fileRef = ref<HTMLInputElement>();

const handleDefaultClick = () => {
  if (props.click) {
    handleClick();
  }
};

const importType = ref('');
const handleClick = (e: string) => {
  importType.value = e;
  fileRef.value.click();
};
const changeEvent = (e: InputEvent) => {
  const target = e.target as HTMLInputElement;
  // console.log(target.files);
  modelValue.value.push(...target.files);
  emits('change', {
    fileList: target.files,
    type: importType.value
  });
  // 第二次选择相同的数据不触发change事件
  target.value = '';
};

defineExpose({
  handleClick
});
</script>

<style scoped lang="less"></style>
