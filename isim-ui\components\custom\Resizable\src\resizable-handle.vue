<!--
* @Author: 宋计民
* @Date: 2024/4/23
* @Version: 1.0
* @Content: resizable-left.vue
-->
<template>
  <div :class="['sim-resizable-handle', `sim-resizable-handle--${pos}`]" ref="resizeHandleRef"></div>
</template>

<script setup lang="ts">
import { MoveCallbackParam, useResizableHook } from 'isim-ui';
import { useResizableInjectHook } from './use-resizable-hook';
import { PropType } from 'vue';

defineOptions({
  name: 'SimResizableLeft'
});
const props = defineProps({
  pos: {
    type: String as PropType<'bottom' | 'right'>,
    default: 'right'
  },
  handleWidth: {
    type: String,
    default: '4px'
  },
  moveCallback: {
    type: Function as PropType<(opt: MoveCallbackParam) => void>,
    default: () => {}
  }
});
const resizeHandleRef = ref<HTMLDivElement>();

const { resizableRef } = useResizableInjectHook();

/**
 * 向下拖动 修改高度
 * @param event
 * @param resizeRect
 */
const bottomMove = ({ event, resizeRect }: MoveCallbackParam) => {
  resizableRef.value.style.height = `${event.clientY - resizeRect.top}px`;
};
/**
 * 向右拖动 修改宽度
 * @param event
 * @param resizeRect
 */
const rightMove = ({ event, resizeRect }: MoveCallbackParam) => {
  resizableRef.value.style.width = `${event.clientX - resizeRect.left}px`;
};
const posCallback = {
  bottom: bottomMove,
  right: rightMove
};

useResizableHook(resizeHandleRef, {
  resizeDom: resizableRef,
  moveCallback: posCallback[props.pos]
});
</script>

<style scoped lang="less">
.sim-resizable-handle {
  position: absolute;
  --handle-width: v-bind('props.handleWidth');
  --handle-pos: calc(var(--handle-width) / 2 * -1);
}
.sim-resizable-handle--right {
  top: 0;
  width: var(--handle-width);
  height: 100%;
  right: var(--handle-pos);
  cursor: col-resize;
}
.sim-resizable-handle--bottom {
  left: 0;
  width: 100%;
  bottom: var(--handle-pos);
  height: var(--handle-width);
  cursor: row-resize;
}
</style>
