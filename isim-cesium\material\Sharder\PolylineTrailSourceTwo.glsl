#extension GL_OES_standard_derivatives : enable
uniform vec4 color;
uniform float width;

czm_material czm_getMaterial(czm_materialInput materialInput)
{
    czm_material material = czm_getDefaultMaterial(materialInput);
    vec2 st = materialInput.st;
    float s = st.s / (abs(fwidth(st.s)) * width * czm_pixelRatio);
    s = s - czm_frameNumber;
    // float s = st.s;
    float t = st.t;
    vec4 colorImage = texture2D(image,vec2(fract(s),t));
    material.diffuse = colorImage.rgb;
    material.alpha = colorImage.a;

    return material;
}