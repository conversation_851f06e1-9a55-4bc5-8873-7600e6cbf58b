/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import SimButton from '../../Button/src/Button.vue';

type ButtonProps = InstanceType<typeof SimButton>['$props'];

type ButtonConfig = {
  label: ButtonProps['label'];
  type: ButtonProps['type'];
};
export interface DraggableV2Props {
  to?: string | HTMLElement;
  draggable?: boolean;
  allowDraggable?: boolean;
  title?: string;
  cancelButton?: ButtonConfig;
  submitButton?: ButtonConfig;
  width?: string;
  height?: string;
  footerShow?: boolean;
  headerShow?: boolean;
  filter?: boolean;
  closeShow?: boolean;
  minWidth?: string;
  minHeight?: string;
  handle?: HTMLElement;
  zIndex?: number;
  left?: string;
  top?: string;
}
