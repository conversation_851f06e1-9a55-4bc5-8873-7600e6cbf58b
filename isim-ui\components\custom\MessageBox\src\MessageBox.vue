<!--
* @Author: 张乐
* @Date: 2023/4/28/028 10:39
* @Version: 1.0
* @Content:
-->
<template>
  <sim-model
    v-bind="$attrs"
    width="436px"
    height="auto"
    custom-class="sim-message-box"
    :visible="state.visible"
    @handle-sure="handleAction('confirm')"
    @handle-cancel="handleAction('cancel')"
    @handle-close="handleAction('close')"
  >
    <div class="flex-wrap sim-message-box__content">
      <div class="content-message">
        <sim-icon :name="typeToIconMap[state.type]" :class="typeToIconMap[state.type]" />
        <div v-if="state.dangerouslyUseHTMLString" class="sim-message-box__message" v-html="state.message"></div>
        <div v-else-if="state.showInput" class="sim-message-box__container">
          <el-form ref="promptFormRef" :model="state" :rules="rules" label-position="top" label-width="120px" @submit.native.prevent>
            <el-form-item :label="state.message" prop="message">
              <sim-input v-model="state.inputValue" :placeholder="state.inputPlaceholder" @keyup.enter.native="handleAction('confirm')" />
            </el-form-item>
          </el-form>
        </div>
        <div v-else class="sim-message-box__message">{{ state.message }}</div>
      </div>
    </div>
  </sim-model>
</template>
<script lang="ts">
export default {
  name: 'SimMessageBox'
};
</script>
<script setup lang="ts">
// 第三方包
import { ref, reactive } from 'vue';
import { FormRules, ElForm, ElFormItem } from 'element-plus';

// 组件
import { SimInput, SimModel, SimIcon } from 'isim-ui';
import { MessageBoxState, Action } from './MessageBox.type';

// hooks

const emits = defineEmits(['vanish', 'action']);

/**
 * 消息类型，用于显示图标 与 svg 映射
 */
const typeToIconMap = {
  '': 'icon-tishi',
  success: 'wancheng',
  info: 'icon-tishi-fill',
  warning: 'tishi',
  error: 'jinggao'
};

const promptFormRef = ref();

const validate = (rule: any, value: any, callback: any) => {
  let editorErrorMessage = '';
  if (state.boxType === 'prompt') {
    const inputPattern = state.inputPattern;
    if (inputPattern && !inputPattern.test(state.inputValue || '')) {
      editorErrorMessage = state.inputErrorMessage || '';
      return callback(new Error(editorErrorMessage));
    }
    const inputValidator = state.inputValidator;
    if (typeof inputValidator === 'function') {
      const validateResult = inputValidator(state.inputValue);
      if (validateResult === false) {
        editorErrorMessage = state.inputErrorMessage || '';
        return callback(new Error(editorErrorMessage));
      }
      if (typeof validateResult === 'string') {
        editorErrorMessage = validateResult;
        return callback(new Error(editorErrorMessage));
      }
    }
  }
  return callback();
};

const rules = reactive<FormRules>({
  message: [{ validator: validate, trigger: 'change' }]
});

// @ts-ignore
const state: Partial<MessageBoxState> = reactive({
  visible: false,
  inputValue: '',
  action: '',
  beforeClose: null,
  type: '',
  message: '',
  boxType: ''
});

const doClose = () => {
  if (!state.visible) {
    return;
  }
  state.visible = false;
  nextTick(() => {
    if (state.action) {
      emits('action', state.action);
    }
  });
};

const handleAction = async (action: Action) => {
  if (state.boxType === 'prompt' && action === 'confirm') {
    let validStatu;
    await promptFormRef.value.validate((valid: boolean) => {
      validStatu = valid;
    });
    if (!validStatu) {
      return;
    }
  }
  state.action = action;
  if (state.beforeClose) {
    // 执行关闭之前操作
    state.beforeClose(action, state, doClose);
  } else {
    doClose();
  }
};

defineExpose({ state });
</script>

<style scoped lang="less">
.sim-message-box {
  &__content {
    position: relative;
    box-sizing: border-box;
    padding: 16px;
    height: 100%;
    min-height: 74px;
    border: 1px solid rgba(var(--primary-color-val), 0.5);
    .content-message {
      width: 100%;
      display: flex;
      // justify-content: center;
      align-items: center;
      .wancheng {
        :deep(.sim-icon__svg use) {
          fill: var(--success-color);
        }
      }
      .tishi {
        :deep(.sim-icon__svg use) {
          fill: var(--info-color);
        }
      }
      .jinggao {
        :deep(.sim-icon__svg use) {
          fill: var(--warn-color);
        }
      }
    }
  }

  &__message {
    //flex: 1;
    margin-left: 9px;
    height: 100%;
    //overflow: auto;
    font-size: 16px;
    // font-weight: 500;
    color: var(--text-color);
    line-height: 22px;
    max-width: calc(100% - 25px);
  }

  &__container {
    width: 100%;
    // label {
    //   width: 100%;
    //   display: inline-block;
    // }
    // .el-input {
    //   margin-top: 5px;
    // }
  }

  .message-box-line__top,
  .message-box-line__bottom {
    position: absolute;
    left: 0;
    width: 100%;
  }
  .message-box-line__left,
  .message-box-line__right {
    position: absolute;
    right: -7px;
  }
  .message-box-line__left {
    left: -7px;
  }
  .message-box-line__right {
    transform: rotate(180deg);
  }
  .message-box-line__top {
    top: 0;
  }

  .message-box-line__bottom {
    bottom: 0;
    transform: rotate(180deg);
  }
}
</style>
