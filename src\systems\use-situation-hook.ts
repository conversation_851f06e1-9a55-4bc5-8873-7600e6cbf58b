import {
  cartesian3ToDegrees,
  createEntitySource,
  DegreesPosType,
  getCurrentTimeJulian,
  getEntityById,
  getEntityProperties,
  getViewer,
  JammingRadarGraphicsOptions,
  synchronizeTime
} from 'isim-cesium';
import { SourceNameEnum } from '@/constant/operator';
import { createSimEntity, EntityData } from '@systems/situation/create-entity.ts';
import { PositionOpt, querySituationApi, query3DRadarApi, query2DRadarApi } from '@api/situation.ts';
import {
  CallbackProperty,
  Cartesian3,
  Color,
  ConstantProperty,
  CustomDataSource,
  Entity,
  HeadingPitchRoll,
  HeightReference,
  Math as CesiumMath,
  Matrix4,
  PolygonGraphics,
  PolygonHierarchy,
  PositionProperty,
  SceneMode,
  Transforms
} from 'cesium';
// @ts-ignore
import * as turf from '@turf/turf';

export const enum EntityType {
  RadarStation = 'radar-station',
  Ship = 'ship'
}

export interface PerformanceDataOpt {
  deviceId: string; // 设备id
  pitchList: number[];
  horizontalList: number[];
  radiusList: number[];
}

export interface EquipmentPerformanceOpt extends PerformanceDataOpt {
  platformEntity: Entity;
  source: CustomDataSource;
  color: string;
  lineColor: string;
}

export interface EntityPolygonOpt {
  positions: DegreesPosType[];
  fill?: boolean;
  color: string;
  outline?: boolean;
  withAlpha?: boolean;
  outlineWidth?: number;
  outlineColor: string;
}

export const cesiumColor = (color: string) => (color ? Color.fromCssColorString(color) : undefined);

/**
 * 根据距离、方位角计算点位
 * @param horizontal
 * @param radius
 * @param entity
 */
export const computePositions = (horizontal: number[], radius: number[], entity: Entity) => {
  const entityPos = entity.position?.getValue(getCurrentTimeJulian());
  const pos = cartesian3ToDegrees(entityPos!);
  const point = turf.point([pos.longitude, pos.latitude]);
  return horizontal.map((item, index) => {
    const {
      geometry: {
        coordinates: [x, y]
      }
    } = turf.destination(point, radius[index] || 0.001, item > 180 ? item - 360 : item, { units: 'meters' });
    return {
      longitude: x,
      latitude: y,
      height: 0
    };
  });
};

export function useSituationHook() {
  const { query } = useRoute();
  const seatId = query.seatId;
  const { source } = createEntitySource(SourceNameEnum.PLATFORM_SOURCE, true);
  const { source: sensorSource } = createEntitySource(SourceNameEnum.SENSOR_SOURCE, true);
  const { run } = synchronizeTime();

  const getColor = function (side: string) {
    if (!side) {
      return 'red';
    }
    if (side == 'red' || side == '红方') {
      return 'red';
    }
    if (side == 'blue' || side == '蓝方') {
      return 'blue';
    }
    return 'red';
  };
  const querySituation = async (isFirst = false) => {
    let entityDataList: EntityData[] = [];
    const { success, data } = await querySituationApi();
    if (success) {
      entityDataList = [];

      // @ts-ignore
      run({ logicTime: data.TimeStamp, actualSpeed: 1 });
      data.radarStatusList.forEach((item) => {
        entityDataList.push({
          id: item.entityId,
          name: item.entityName,
          type: EntityType.RadarStation,
          position: item.locationLLA,
          heading: item.heading || 0,
          pitch: item.pitch || 0,
          forceSide: getColor(item.side),
          time: data.TimeStamp,
          show: seatId === 'red' || !seatId
        });
      });
      data.shipStatusList.forEach((item) => {
        entityDataList.push({
          id: item.entityId,
          name: item.entityName,
          type: EntityType.Ship,
          position: item.locationLLA,
          heading: item.heading || 0,
          pitch: item.pitch || 0,
          forceSide: getColor(item.side),
          time: data.TimeStamp,
          show: seatId === 'blue' || !seatId
        });
      });

      entityDataList.forEach((item) => {
        createSimEntity(item, source.entities);
      });

      //判断是否存在实体已经被删除
      if (entityDataList.length != source.entities.values.length) {
        console.log('存在实体删除.');
        const ids = new Set();
        entityDataList.forEach((e) => {
          ids.add(e.id);
        });

        const deleted: Entity[] = [];
        source.entities.values.forEach((e) => {
          if (!ids.has(e.id)) {
            deleted.push(e);
          }
        });
        deleted.forEach((e) => source.entities.remove(e));
      }

      if (seatId) {
        const trackSet: Set<string> = new Set();
        data.trackList.forEach((item) => {
          const en = getEntityById(item.entityId, SourceNameEnum.PLATFORM_SOURCE);
          if (!en) {
            return;
          }
          const metaData = getEntityProperties(en)!.metaData;
          if (seatId === metaData.forceSide) {
            item.tracks.forEach((track) => {
              track.entities.forEach((target) => {
                const trackEntity = getEntityById(target.entityId, SourceNameEnum.PLATFORM_SOURCE);
                if (trackEntity) {
                  trackSet.add(target.entityId);
                  trackEntity.show = true;
                }
              });
            });
          }
        });

        source.entities.values.forEach((entity) => {
          const metaData = getEntityProperties(entity)!.metaData;
          if (metaData.forceSide !== seatId && !trackSet.has(entity.id)) {
            entity.show = false;
          }
        });
      }
    }
  };

  const queryRadar = async (params: { entityId: string; locationLLA: PositionOpt }) => {
    const scene3d = getViewer().scene.mode === SceneMode.SCENE3D;
    const { success, data } = scene3d ? await query3DRadarApi(params) : await query2DRadarApi(params);
    if (success) {
      const [pitchList, horizontalList, radiusList] = scene3d ? data : [[], ...data];
      createEquipmentPerformance({
        pitchList,
        horizontalList,
        radiusList,
        platformEntity: getEntityById(params.entityId, SourceNameEnum.PLATFORM_SOURCE)!,
        source: sensorSource,
        deviceId: params.entityId,
        color: '#FF000030',
        lineColor: '#FF000060'
      });
    }
  };

  const setEntityPolygon = (
    entity: Entity | Entity.ConstructorOptions,
    options: Omit<EntityPolygonOpt, 'positions'> & { horizontalList?: number[]; radiusList?: number[] }
  ) => {
    const { fill = true, outline = true, outlineWidth = 1, color, outlineColor, withAlpha = false, horizontalList = [], radiusList = [] } = options;
    const getCartesian3Positions = () => {
      const heading = entity.properties?.getValue(getCurrentTimeJulian())?.heading;
      const points = computePositions(horizontalList, radiusList, entity as Entity);
      let cartesian3Positions = points.map(({ longitude, latitude, height }) => Cartesian3.fromDegrees(longitude, latitude, height));
      if (heading) {
        const enuMatrix = Transforms.eastNorthUpToFixedFrame((entity as Entity).position!.getValue(getCurrentTimeJulian()));
        const enuMatrixInverse = Matrix4.inverse(enuMatrix, new Matrix4());
        // const rotationZ = CesiumMath.toRadians(-heading);
        cartesian3Positions = cartesian3Positions.map((item) => {
          const localPoint = Matrix4.multiplyByPoint(enuMatrixInverse, item, new Cartesian3());
          const x = localPoint.x * Math.cos(-heading) - localPoint.y * Math.sin(-heading);
          const y = localPoint.x * Math.sin(-heading) + localPoint.y * Math.cos(-heading);
          const cartesian3 = new Cartesian3(x, y, localPoint.z);
          return Matrix4.multiplyByPoint(enuMatrix, cartesian3, new Cartesian3());
        });
      }
      return cartesian3Positions;
    };

    entity.polygon = new PolygonGraphics({
      hierarchy: new CallbackProperty(() => new PolygonHierarchy(getCartesian3Positions()), false),
      fill,
      material: withAlpha ? cesiumColor(color)?.withAlpha(0.5) : cesiumColor(color),
      outline,
      outlineWidth,
      perPositionHeight: new ConstantProperty(outline),
      outlineColor: cesiumColor(outlineColor),
      heightReference: HeightReference.CLAMP_TO_GROUND,
      zIndex: -999
    });
  };

  const create2DEquipmentPerformance = (
    horizontalList: number[],
    radiusList: number[],
    options: {
      show: boolean;
      platformEntity: Entity;
      entity: Entity | (Entity.ConstructorOptions & { jammingRadar?: JammingRadarGraphicsOptions | undefined });
      lineShow: boolean;
      color: string;
      lineColor: string;
    }
  ) => {
    const { show, entity, lineShow, color, lineColor } = options;
    // entity.show = show;
    entity.jammingRadar = undefined;

    setEntityPolygon(entity, {
      fill: true,
      outline: lineShow,
      outlineWidth: 1,
      color,
      outlineColor: lineColor,
      horizontalList,
      radiusList
    });
  };

  const create3DEquipmentPerformance = (
    pitchList: number[],
    horizontalList: number[],
    radiusList: number[],
    options: {
      show: boolean;
      entity: Entity | (Entity.ConstructorOptions & { jammingRadar?: JammingRadarGraphicsOptions | undefined });
      lineShow: boolean;
      color: string;
      lineColor: string;
    }
  ) => {
    const { show, entity, lineShow, color, lineColor } = options;
    // entity.show = show;
    entity.polygon = new PolygonGraphics();
    entity.jammingRadar = {
      show,
      color: Color.fromCssColorString(color),
      pitchList,
      horizontalList,
      radiusList,
      lineShow,
      heightReference: HeightReference.RELATIVE_TO_GROUND,
      lineColor: Color.fromCssColorString(lineColor)
    };
  };

  const createEquipmentPerformance = (data: EquipmentPerformanceOpt) => {
    const { pitchList, horizontalList, radiusList, platformEntity, source, deviceId, color, lineColor } = data;

    const metaData = getEntityProperties(platformEntity)!.metaData;
    const entity: Entity | { parent: Entity; id: string; position: PositionProperty; properties: any } = source.entities.getById(deviceId) || {
      parent: platformEntity,
      id: deviceId,
      orientation: new CallbackProperty(
        () =>
          Transforms.headingPitchRollQuaternion(
            platformEntity.position!.getValue(getCurrentTimeJulian()),
            // @ts-ignore
            new HeadingPitchRoll(CesiumMath.toRadians(metaData.heading - 90), CesiumMath.toRadians(metaData.pitch), CesiumMath.toRadians(0))
          ),
        false
      ),
      position: platformEntity.position!,
      properties: new CallbackProperty(() => getEntityProperties(platformEntity)!.metaData, false)
    };

    if (!pitchList || pitchList.length <= 1) {
      create2DEquipmentPerformance(horizontalList, radiusList, {
        platformEntity,
        show: true,
        entity,
        lineShow: true,
        color,
        lineColor
      });
    } else {
      create3DEquipmentPerformance(pitchList, horizontalList, radiusList, {
        show: true,
        entity,
        color,
        lineColor,
        lineShow: true
      });
    }

    if (!(entity instanceof Entity)) {
      source.entities.add(entity);
    }
  };

  return {
    querySituation,
    queryRadar
  };
}
