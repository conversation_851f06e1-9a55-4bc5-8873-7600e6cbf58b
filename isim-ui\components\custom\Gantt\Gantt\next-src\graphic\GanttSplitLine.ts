import type { GanttRow } from './GanttRow';
import type { GanttGraphicAbstract } from './GanttGraphicAbstract';

export class GanttSplitLine implements GanttGraphicAbstract {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  constructor(
    private ctx: CanvasRenderingContext2D,
    private row: GanttRow
  ) {
    this.id = this.row.id + 'splintLine';
    this.x = 0;
    this.y = this.row.index * (this.row.rowHeight + this.row.rowGap) + this.row.rowHeight + this.row.rowGap + this.row.rowGap / 2;
    this.width = this.row.width;
    this.height = 0;
  }
  render() {
    const ctx = this.ctx;
    ctx.beginPath();
    const height = this.row.index * (this.row.rowHeight + this.row.rowGap) + this.row.rowHeight + this.row.rowGap + this.row.rowGap / 2;
    ctx.lineWidth = 1;
    ctx.moveTo(0, height);
    ctx.lineTo(this.row.width, height);
    ctx.fillStyle = '#000';
    ctx.stroke();
    ctx.closePath();
  }
}
