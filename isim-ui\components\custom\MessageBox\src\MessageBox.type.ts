import { AppContext, VNode } from 'vue';

export type MessageBoxShortcutMethod = (
  message: MessageBoxState['message'],
  title: MessageBoxState['title'],
  options?: MessageBoxState,
  appContext?: MessageBoxInfo['_context']
) => Promise<Callback>;

export interface MessageBoxInfo {
  _context: AppContext | null;
  alert: (
    message: string | VNode,
    title: string,
    options?: Partial<MessageBoxState> | string,
    appContext?: MessageBoxInfo['_context']
  ) => Promise<Action>;
  confirm: (
    message: string | VNode,
    title: string,
    options?: Partial<MessageBoxState> | string,
    appContext?: MessageBoxInfo['_context']
  ) => Promise<Action>;
  prompt: (message: string | VNode, title: string, options?: Partial<MessageBoxState>, appContext?: MessageBoxInfo['_context']) => Promise<any>;
  close: () => void;
}

export interface MessageBoxInputValidator {
  (value: string): boolean | string;
}

export interface MessageBoxState {
  visible: boolean;
  showInput: boolean;
  inputValue: string;
  inputPattern: RegExp;
  inputErrorMessage: string;
  inputPlaceholder?: string;
  inputValidator?: MessageBoxInputValidator;
  action: string;
  title: string;
  type: 'success' | 'info' | 'warning' | 'error' | '';
  message: string | VNode;
  appendTo: string | HTMLElement;

  footerShow: boolean; // 是否显示下面操作按钮
  showCancelButton: boolean; //是否显示取消按钮
  showConfirmButton: boolean; // 是否显示确定按钮
  cancelButtonText: string; // 取消按钮的文本内容
  confirmButtonText: string; // 确定按钮的文本内容
  cancelButtonClass: string; // 取消按钮的自定义类名
  confirmButtonClass: string; // 确定按钮的自定义类名
  closeOnClickModal: boolean; // 是否可通过点击遮罩关闭 MessageBox

  dangerouslyUseHTMLString: boolean; // message是否使用html
  distinguishCancelAndClose: boolean; // 是否区分关闭和取消

  callback: Callback | undefined;
  boxType: string;
  beforeClose: null | ((action: Action, instance: Partial<MessageBoxState>, done: () => void) => void);
}

export type Action = 'confirm' | 'close' | 'cancel';

export type Callback = ((value: string, action: Action) => any) | ((action: Action) => any);
