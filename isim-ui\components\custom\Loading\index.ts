/**
 * @Author: zhang<PERSON>e
 * @Date: 2023/4/27
 * @Version: 1.0
 * @Content: Loading 函数式调用
 */

import { App } from 'vue';

import { Loading } from './src/service';
import { simLoading } from './src/directive';
import './src/style.less';

export const SimLoading = {
  install(Vue: App) {
    Vue.directive('sim-loading', simLoading);
    // app.config.globalProperties.$loading = Loading
  },
  directive: simLoading,
  service: Loading
};

export { simLoading, Loading, simLoading as default };
