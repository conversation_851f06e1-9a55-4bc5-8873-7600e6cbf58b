/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/03/19 15:34:47
 * */
import RectangularSensorPrimitive from './RectangularSensorPrimitive.js';
import RectangularSensorVisualizer from './RectangularSensorVisualizer.js';
import RectangularSensorGraphics from './RectangularSensorGraphics.js';
import ConicSensorGraphics from './ConicSensorGraphics.js';
import ConicSensorVisualizer from './ConicSensorVisualizer.js';
import CustomSensorPrimitive from './CustomSensorPrimitive.js';
import CustomPatternSensorGraphics from './CustomSensorGraphics.js';
import CustomPatternSensorVisualizer from './CustomSensorVisualizer.js';
import * as Cesium from 'cesium';
import { Cartesian3, Color, defined, Spherical, TimeInterval, CzmlDataSource, DataSourceDisplay } from 'cesium';
const { processPacketData, processMaterialPacketData } = Cesium.CzmlDataSource;

const iso8601Scratch = {
  iso8601: undefined
};

// Cesium.RectangularSensorPrimitive = RectangularSensorPrimitive;
// Cesium.RectangularSensorVisualizer = RectangularSensorVisualizer;
// Cesium.RectangularSensorGraphics = RectangularSensorGraphics;
//
// Cesium.CustomSensorPrimitive = CustomSensorPrimitive;
// Cesium.CustomPatternSensorVisualizer = CustomPatternSensorVisualizer;
// Cesium.CustomPatternSensorGraphics = CustomPatternSensorGraphics;
//
// Cesium.ConicSensorVisualizer = ConicSensorVisualizer;
// Cesium.ConicSensorGraphics = ConicSensorGraphics;

initializeCzmlEntity();

function initializeCzmlEntity() {
  CzmlDataSource.updaters.push(processConicSensor, processCustomPatternSensor, processRectangularSensor);

  const originalDefaultVisualizersCallback = DataSourceDisplay.defaultVisualizersCallback;
  DataSourceDisplay.defaultVisualizersCallback = function (scene, entityCluster, dataSource) {
    const entities = dataSource.entities;
    const array = originalDefaultVisualizersCallback(scene, entityCluster, dataSource);
    return array.concat([
      new RectangularSensorVisualizer(scene, entities),
      new ConicSensorVisualizer(scene, entities),
      new CustomPatternSensorVisualizer(scene, entities)
    ]);
  };
}

/**
 *
 * @param {*} customPatternSensor
 * @param {*} directions
 * @param {*} interval
 * @param {*} sourceUri
 * @param {*} entityCollection
 */
function processDirectionData(customPatternSensor, directions, interval, sourceUri, entityCollection) {
  const values = [],
    unitSphericals = directions.unitSpherical,
    sphericals = directions.spherical,
    unitCartesians = directions.unitCartesian,
    cartesians = directions.cartesian;
  let i;
  let len;

  if (defined(unitSphericals)) {
    for (i = 0, len = unitSphericals.length; i < len; i += 2) {
      values.push(new Spherical(unitSphericals[i], unitSphericals[i + 1]));
    }
    directions.array = values;
  } else if (defined(sphericals)) {
    for (i = 0, len = sphericals.length; i < len; i += 3) {
      values.push(new Spherical(sphericals[i], sphericals[i + 1], sphericals[i + 2]));
    }
    directions.array = values;
  } else if (defined(unitCartesians)) {
    for (i = 0, len = unitCartesians.length; i < len; i += 3) {
      const tmp = Spherical.fromCartesian3(new Cartesian3(unitCartesians[i], unitCartesians[i + 1], unitCartesians[i + 2]));
      Spherical.normalize(tmp, tmp);
      values.push(tmp);
    }
    directions.array = values;
  } else if (defined(cartesians)) {
    for (i = 0, len = cartesians.length; i < len; i += 3) {
      values.push(Spherical.fromCartesian3(new Cartesian3(cartesians[i], cartesians[i + 1], cartesians[i + 2])));
    }
    directions.array = values;
  }
  processPacketData(Array, customPatternSensor, 'directions', directions, interval, sourceUri, entityCollection);
}

/**
 * CommonSensor
 * @param {*} sensor
 * @param {*} sensorData
 * @param {*} interval
 * @param {*} sourceUri
 * @param {*} entityCollection
 */
function processCommonSensorProperties(sensor, sensorData, interval, sourceUri, entityCollection) {
  processPacketData(Boolean, sensor, 'show', sensorData.show, interval, sourceUri, entityCollection);
  processPacketData(Number, sensor, 'radius', sensorData.radius, interval, sourceUri, entityCollection);
  processPacketData(Boolean, sensor, 'showIntersection', sensorData.showIntersection, interval, sourceUri, entityCollection);
  processPacketData(Color, sensor, 'intersectionColor', sensorData.intersectionColor, interval, sourceUri, entityCollection);
  processPacketData(Number, sensor, 'intersectionWidth', sensorData.intersectionWidth, interval, sourceUri, entityCollection);
  processMaterialPacketData(sensor, 'lateralSurfaceMaterial', sensorData.lateralSurfaceMaterial, interval, sourceUri, entityCollection);
}

/**
 *
 * @param {*} entity
 * @param {*} packet
 * @param {*} entityCollection
 * @param {*} sourceUri
 * @returns
 */
function processConicSensor(entity, packet, entityCollection, sourceUri) {
  const conicSensorData = packet.agi_conicSensor;
  if (!defined(conicSensorData)) {
    return;
  }

  let interval;
  const intervalString = conicSensorData.interval;
  if (defined(intervalString)) {
    iso8601Scratch.iso8601 = intervalString;
    interval = TimeInterval.fromIso8601(iso8601Scratch);
  }

  let conicSensor = entity.conicSensor;
  if (!defined(conicSensor)) {
    entity.addProperty('conicSensor');
    conicSensor = new ConicSensorGraphics();
    entity.conicSensor = conicSensor;
  }

  processCommonSensorProperties(conicSensor, conicSensorData, interval, sourceUri, entityCollection);
  processPacketData(Number, conicSensor, 'innerHalfAngle', conicSensorData.innerHalfAngle, interval, sourceUri, entityCollection);
  processPacketData(Number, conicSensor, 'outerHalfAngle', conicSensorData.outerHalfAngle, interval, sourceUri, entityCollection);
  processPacketData(Number, conicSensor, 'minimumClockAngle', conicSensorData.minimumClockAngle, interval, sourceUri, entityCollection);
  processPacketData(Number, conicSensor, 'maximumClockAngle', conicSensorData.maximumClockAngle, interval, sourceUri, entityCollection);
}

/**
 *
 * @param {*} entity
 * @param {*} packet
 * @param {*} entityCollection
 * @param {*} sourceUri
 * @returns
 */
function processCustomPatternSensor(entity, packet, entityCollection, sourceUri) {
  const customPatternSensorData = packet.agi_customPatternSensor;
  if (!defined(customPatternSensorData)) {
    return;
  }
  const intervalString = customPatternSensorData.interval;
  let interval;
  if (defined(intervalString)) {
    iso8601Scratch.iso8601 = intervalString;
    interval = TimeInterval.fromIso8601(iso8601Scratch);
  }

  let customPatternSensor = entity.customPatternSensor;
  if (!defined(customPatternSensor)) {
    entity.addProperty('customPatternSensor');
    customPatternSensor = new CustomPatternSensorGraphics();
    entity.customPatternSensor = customPatternSensor;
  }

  processCommonSensorProperties(customPatternSensor, customPatternSensorData, interval, sourceUri, entityCollection);

  // The directions property is a special case value that can be an array of unitSpherical or unit Cartesians.
  // We pre-process this into Spherical instances and then process it like any other array.
  const directions = customPatternSensorData.directions;
  if (defined(directions)) {
    if (Array.isArray(directions)) {
      const length = directions.length;
      for (let i = 0; i < length; i++) {
        processDirectionData(customPatternSensor, directions[i], interval, sourceUri, entityCollection);
      }
    } else {
      processDirectionData(customPatternSensor, directions, interval, sourceUri, entityCollection);
    }
  }
}

/**
 *
 * @param {*} entity
 * @param {*} packet
 * @param {*} entityCollection
 * @param {*} sourceUri
 * @returns
 */
function processRectangularSensor(entity, packet, entityCollection, sourceUri) {
  const rectangularSensorData = packet.agi_rectangularSensor;
  if (!defined(rectangularSensorData)) {
    return;
  }

  const intervalString = rectangularSensorData.interval;
  let interval;
  if (defined(intervalString)) {
    iso8601Scratch.iso8601 = intervalString;
    interval = TimeInterval.fromIso8601(iso8601Scratch);
  }

  let rectangularSensor = entity.rectangularSensor;
  if (!defined(rectangularSensor)) {
    entity.addProperty('rectangularSensor');
    rectangularSensor = new RectangularSensorGraphics();
    entity.rectangularSensor = rectangularSensor;
  }

  processRectangularSensorProperties(rectangularSensor, rectangularSensorData, interval, sourceUri, entityCollection);
  processPacketData(Number, rectangularSensor, 'xHalfAngle', rectangularSensorData.xHalfAngle, interval, sourceUri, entityCollection);
  processPacketData(Number, rectangularSensor, 'yHalfAngle', rectangularSensorData.yHalfAngle, interval, sourceUri, entityCollection);
}

/**
 * RectangularSensor
 * @param {*} sensor
 * @param {*} sensorData
 * @param {*} interval
 * @param {*} sourceUri
 * @param {*} entityCollection
 */
function processRectangularSensorProperties(sensor, sensorData, interval, sourceUri, entityCollection) {
  processPacketData(Boolean, sensor, 'show', sensorData.show, interval, sourceUri, entityCollection);
  processPacketData(Boolean, sensor, 'showScanPlane', sensorData.showScanPlane, interval, sourceUri, entityCollection);
  processPacketData(Number, sensor, 'radius', sensorData.radius, interval, sourceUri, entityCollection);
  processPacketData(Boolean, sensor, 'showIntersection', sensorData.showIntersection, interval, sourceUri, entityCollection);
  processPacketData(Boolean, sensor, 'showThroughEllipsoid', sensorData.showThroughEllipsoid, interval, sourceUri, entityCollection);
  processPacketData(Color, sensor, 'intersectionColor', sensorData.intersectionColor, interval, sourceUri, entityCollection);
  processPacketData(Color, sensor, 'scanPlaneColor', sensorData.scanPlaneColor, interval, sourceUri, entityCollection);
  processPacketData(Color, sensor, 'lineColor', sensorData.lineColor, interval, sourceUri, entityCollection);
  processPacketData(Number, sensor, 'intersectionWidth', sensorData.intersectionWidth, interval, sourceUri, entityCollection);
  processMaterialPacketData(sensor, 'lateralSurfaceMaterial', sensorData.lateralSurfaceMaterial, interval, sourceUri, entityCollection);
  processMaterialPacketData(sensor, 'material', sensorData.material, interval, sourceUri, entityCollection);
  processPacketData(Number, sensor, 'scanPlaneRate', sensorData.scanPlaneRate, interval, sourceUri, entityCollection);
}
