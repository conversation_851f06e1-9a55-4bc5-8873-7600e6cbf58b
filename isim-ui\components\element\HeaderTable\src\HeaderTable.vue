<!--
* @Author: 崔晓东
* @Date: 2023/5/18 9:20
* @Version: 1.0
* @Content: SimHeaderTable 多级表头表格
-->
<template>
  <el-table v-bind="$attrs" ref="tableRef" class="sim-header-table">
    <template v-for="(value, name) in $slots" #[name]>
      <slot :name="name"></slot>
    </template>
  </el-table>
</template>

<script lang="ts">
export default {
  name: 'SimHeaderTable'
};
</script>
<script lang="ts" setup>
import { ref } from 'vue';
import { ElTable } from 'element-plus';

const tableRef = ref<InstanceType<typeof ElTable>>();
defineExpose(tableRef.value);
</script>
<style lang="less">
.sim-header-table {
  --el-table-bg-color: transparent;
  --el-table-header-bg-color: rgba(var(--primary-color-val), 0.15);
  --el-table-tr-bg-color: rgba(0, 0, 0, 0.15);
  --el-table-row-hover-bg-color: rgba(var(--primary-color-val), 0.2);
  --el-table-text-color: white;
  --el-table-header-text-color: white;
  --el-table-border: rgba(var(--primary-color-val), 0.15);
  --sim-header-table-border-radius: 8px;

  border-width: 0;

  thead.is-group th.el-table__cell {
    background-color: var(--el-table-header-bg-color);
  }
  &::before,
  &::after {
    width: 0;
  }

  .el-table__inner-wrapper {
    --el-table-border-color: transparent;
  }

  .td-left-border-radius {
    &:first-child {
      border-top-left-radius: var(--sim-header-table-border-radius);
      border-bottom-left-radius: var(--sim-header-table-border-radius);
    }
  }
  .td-right-border-radius {
    &:last-child {
      border-top-right-radius: var(--sim-header-table-border-radius);
      border-bottom-right-radius: var(--sim-header-table-border-radius);
    }
  }

  .el-table__row {
    background: rgba(var(--text-color-val), 0.05);
    background-clip: content-box;
    height: 48px;
    border: 1px solid rgba(var(--text-color-val), 0.1);
    box-sizing: border-box;
    border-collapse: initial;
    .el-table__cell {
      padding: 4px 0;
      box-sizing: border-box;
      //border-bottom: none;
      //background-clip: content-box;
    }
  }
  .el-table__header-wrapper {
    margin-bottom: 4px;
    .el-table__header {
      overflow: hidden;
      border: 1px solid rgba(var(--primary-color-val), 0.15);
      border-radius: var(--sim-header-table-border-radius);
      .el-table__cell {
        //border-color: rgba(var(--primary-color-val), 0.3);
      }
    }
  }

  .el-table__body-wrapper tbody .el-table__row .el-table__cell {
    .td-left-border-radius();
    .td-right-border-radius();
  }

  .el-popper.is-dark {
    text-overflow: initial;
    height: auto;
    max-width: 50vw;
    word-break: break-all;
    white-space: break-spaces;
    background-color: var(--primary-color-dep);
    line-height: 1.5;
    font-weight: normal;
  }
}
.el-table--border .el-table__header-wrapper .el-table__cell {
  border-right: 2px solid var(--model-bg-color);
  border-bottom: 1px solid rgba(var(--primary-color-val), 0.2);
  &.is-leaf {
    border-bottom-width: 0;
  }
  &:last-child {
    border-right-width: 0;
  }
}
.el-table--border .el-table__body-wrapper .el-table__cell {
}
</style>
