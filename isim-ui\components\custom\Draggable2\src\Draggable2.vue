<!--
* <AUTHOR> 宋计民
* @Date :  2023/3/20
* @Version : 1.0
* @Content : Draggable
-->
<template>
  <Teleport :to="to!" :disabled="!to">
    <transition name="el-fade-in-linear">
      <div
        v-show="show"
        ref="dragRef"
        class="sim-draggable"
        :class="{
          'sim-draggable--filter': filter,
          'sim-draggable--notfooter': !footerShow,
          'sim-draggable--notheader': !headerShow,
          'sim-draggable--onlycontent': !footerShow && !headerShow
        }"
        @select.prevent
      >
        <div v-if="headerShow" ref="defaultHandleRef" :class="[!title && 'no-title', `sim-draggable__header`]">
          <span v-if="title" class="ellipsis">{{ title }}</span>
          <div class="sim-draggable__tool" @mousedown.prevent.stop>
            <slot name="tool"></slot>
            <sim-icon class="close" name="guanbi" v-if="closeShow" style="cursor: pointer" @click.prevent.stop="closeDraggable" />
          </div>
        </div>
        <div class="sim-draggable__content" @mousedown.stop>
          <slot></slot>
        </div>
        <div v-if="footerShow" class="sim-draggable__footer" @mousedown.stop>
          <slot name="footer">
            <sim-button v-bind="props.submitButton" @click.prevent.stop="handleSure" />
            <sim-button v-bind="props.cancelButton" @click.prevent.stop="handleCancel" />
          </slot>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<script lang="ts" setup>
import SimButton from '../../Button/src/Button.vue';
import { useDraggableTransformHook, useZIndex } from 'isim-ui';
defineOptions({
  name: 'SimDraggable2'
});

const props = defineProps({
  show: {
    type: Boolean,
    default: true
  },
  to: {
    type: [String, HTMLElement]
  },
  draggable: {
    type: Boolean,
    default: false
  },
  allowDraggable: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  cancelButton: {
    type: Object,
    default: () => ({
      label: '取消',
      type: 'default'
    })
  },
  submitButton: {
    type: Object,
    default: () => ({
      label: '确定',
      type: 'primary'
    })
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '100%'
  },
  footerShow: {
    type: Boolean,
    default: true
  },
  headerShow: {
    type: Boolean,
    default: true
  },
  filter: {
    type: Boolean,
    default: false
  },
  closeShow: {
    type: Boolean,
    default: true
  },
  minWidth: {
    type: String,
    default: '100px'
  },
  minHeight: {
    type: String,
    default: '100px'
  },
  handle: {
    type: [HTMLElement, null],
    default: null
  },
  zIndex: {
    type: Number,
    default: null
  },
  left: {
    type: String,
    default: '0px'
  },
  top: {
    type: String,
    default: '0px'
  }
});

const emits = defineEmits<{
  close: (e: MouseEvent) => void;
  submit: (e: MouseEvent) => void;
  'handle-close': (e: MouseEvent) => void;
  'handle-cancel': (e: MouseEvent) => void;
  'drag-start': (e: MouseEvent) => void;
}>();

const { currentZIndex, nextZIndex } = useZIndex();

const currentZIndexComputed = ref(props.zIndex ?? currentZIndex.value);

const defaultHandleRef = ref();
const dragRef = ref();

useDraggableTransformHook(props.handle ? toRef(props, 'handle') : defaultHandleRef, {
  dragBox: dragRef,
  sticky: true,
  mouseDownCallback: () => {
    currentZIndexComputed.value = nextZIndex();
    console.log(currentZIndexComputed.value);
  }
});

const closeDraggable = (e: MouseEvent) => {
  emits('handle-close', e);
  emits('close', e);
};
const handleSure = (e: MouseEvent) => {
  emits('submit', e);
};
const handleCancel = (e: MouseEvent) => {
  emits('handle-cancel', e);
  emits('close', e);
};
</script>

<style scoped lang="less">
@header-height: 32px;
@footer-height: 38px;

.sim-draggable {
  min-width: v-bind('props.minWidth');
  position: absolute;
  left: 0;
  top: 0;
  border: 1px solid var(--border-color);
  box-sizing: border-box;
  background-color: var(--primary-bg-color);

  --left: v-bind('props.left');
  --top: v-bind('props.top');

  z-index: v-bind('currentZIndexComputed');
  width: v-bind('props.width');
  height: v-bind('props.height');
  transform: translate(var(--left), var(--top));

  &__header {
    background-color: var(--secondary-header-bg-color);
    height: @header-height;
    line-height: @header-height;
    font-size: 16px;
    text-shadow: 0 0 0 var(--secondary-text-color);
    font-weight: 400;
    color: var(--isim-text-color);
    padding: 0 8px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(var(--isim-text-color-val), 0.5);
    transform: translate(0, 0);
    user-select: none;
  }

  &__tool {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    font-size: 12px;

    & > .close {
      margin-left: 8px;
    }
  }

  &__content {
    width: 100%;
    height: calc(100% - @header-height - @footer-height);
    overflow: auto;
    box-sizing: border-box;
  }

  &__footer {
    height: @footer-height;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 8px;
    box-sizing: border-box;
  }
}

.sim-draggable--filter {
  backdrop-filter: blur(10px);
}

.sim-draggable--notfooter {
  .sim-draggable__content {
    height: calc(100% - @header-height);
  }
}

.sim-draggable--notheader {
  .sim-draggable__content {
    height: calc(100% - @footer-height);
  }
}

.sim-draggable--onlycontent {
  .sim-draggable__content {
    height: 100%;
  }
}

.i_draggable__resize {
  position: absolute;
  height: 4px;
  width: 4px;

  &--tl {
    cursor: nw-resize;
    float: left;
    left: -2px;
    top: -2px;
  }

  &--tm {
    position: absolute;
    width: calc(100% - 4px);
    left: 2px;
    top: -2px;
    cursor: n-resize;
  }

  &--tr {
    float: left;
    top: -2px;
    right: -2px;
    cursor: ne-resize;
  }

  &--ml {
    height: calc(100% - 4px);
    top: 2px;
    left: -2px;
    cursor: e-resize;
  }

  &--mr {
    height: calc(100% - 4px);
    top: 2px;
    right: -2px;
    cursor: e-resize;
  }

  &--bl {
    bottom: -2px;
    left: -2px;
    cursor: sw-resize;
  }

  &--bm {
    width: calc(100% - 4px);
    left: 2px;
    bottom: -2px;
    cursor: s-resize;
  }

  &--br {
    bottom: -2px;
    right: -2px;
    cursor: se-resize;
  }
}
</style>
