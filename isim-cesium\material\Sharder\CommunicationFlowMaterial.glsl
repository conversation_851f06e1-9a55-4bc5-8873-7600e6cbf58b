uniform vec4 color;
uniform float repeat;
uniform float speed;
uniform float thickness;

czm_material czm_getMaterial(czm_materialInput materialInput)
{
    czm_material material = czm_getDefaultMaterial(materialInput);
    float offset = fract(czm_frameNumber * 45.0 / 10000.0);
    offset *= -speed;
    float sp = 1.0 / repeat;
    vec2 st = materialInput.st;
    float dis = distance(st, vec2(0.5));
    float m = mod(dis + offset, sp);
    float a = step(sp*(1.0 - thickness), m);
    material.diffuse = color.rgb;
    material.alpha = a * color.a;
    return material;
}