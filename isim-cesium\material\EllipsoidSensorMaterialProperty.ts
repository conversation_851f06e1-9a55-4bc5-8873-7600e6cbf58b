/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
// @ts-nocheck

import { Color, defaultValue, defined, Material, Property, createPropertyDescriptor, Event } from 'cesium';
import ShaderSource from './Sharder/EllipsoidSensorMaterial.glsl?raw';

// 材质的类型
const MaterialType = 'EllipsoidSensor';
Material.EllipsoidSensorType = MaterialType;

// 创建Material传参数据类型
type EllipsoidSensorOptions = {
  step: number;
  speed: number;
  color: Color;
};
// 默认的uniforms
const uniforms: EllipsoidSensorOptions = {
  step: 4,
  speed: 1,
  color: Color.RED
};

Material._materialCache.addMaterial(MaterialType, {
  fabric: {
    type: MaterialType,
    uniforms,
    source: ShaderSource
  },
  translucent: function () {
    return true;
  }
});
// 创建primitive使用的Material
export const createEllipsoidSensorMaterial = (newUniforms: EllipsoidSensorOptions | undefined) => {
  return new Material({
    fabric: {
      type: MaterialType,
      uniforms: {
        ...uniforms,
        ...newUniforms
      },
      source: ShaderSource
    },
    translucent: function () {
      return true;
    }
  });
};

export interface EllipsoidSensorMaterialPropertyConstructor {
  new (options?: EllipsoidSensorOptions): {};
}
// entity使用的MaterialProperty
export const EllipsoidSensorMaterialProperty: EllipsoidSensorMaterialPropertyConstructor = function (options?: EllipsoidSensorOptions) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();
  // 纹理数量
  this.step = options.step ?? uniforms.step;
  // 动态纹理速度，为0时不开启
  this.speed = options?.speed ?? uniforms.speed;
  // 纹理颜色
  this.color = options?.color ?? uniforms.color;
};

Object.defineProperties(EllipsoidSensorMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return false;
    }
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  }
});

EllipsoidSensorMaterialProperty.prototype.getType = function (_time) {
  return MaterialType;
};

EllipsoidSensorMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  // 需要使用Property.getValueOrDefaul(this.私有属性, time, uniforms.默认值, result.公共属性）
  result.step = this.step;
  result.speed = this.speed;
  result.color = this.color;
  return result;
};

// 非必要不修改，判断材质是否相同，cesium会使用这个方法做单例化
EllipsoidSensorMaterialProperty.prototype.equals = function (other) {
  return this === other;
};
