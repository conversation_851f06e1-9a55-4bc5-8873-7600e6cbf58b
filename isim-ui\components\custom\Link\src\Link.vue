<!--
* <AUTHOR> 宋计民
* @Date :  2022/11/6
* @Version : 1.0
* @Content : rtLink
-->
<template>
  <div :class="['sim-link', { 'sim-link--disabled': disabled, 'sim-link--hover': hasHover }]" @click="handleClick">
    <slot />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SimLink'
});
const emits = defineEmits(['click']);

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  hasHover: {
    type: Boolean,
    default: true
  }
});

const handleClick = (e) => {
  if (props.disabled) {
    return;
  }
  emits('click', e);
};
</script>
<style scoped lang="less">
.sim-link {
  display: inline-block;
  padding: 5px;
  border-radius: 3px;
  color: var(--ghost-color);
  cursor: pointer;
  white-space: nowrap;

  & + & {
    margin-left: 8px;
  }

  &:hover {
    color: var(--ghost-color-hover);
  }

  &:active {
    color: var(--ghost-color-active);
  }
}

.sim-link--hover {
  //&:hover {
  //  background: rgba(var(--primary-color-val), 0.5);
  //}
}

.sim-link--disabled {
  cursor: not-allowed;
  background-color: var(--ghost-color-disable);
}
</style>
