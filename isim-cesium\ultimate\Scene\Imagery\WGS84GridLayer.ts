/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/04/15 10:39:56
 * @description WGS84Grid Entrance
 * @version 1.0
 * */

import { Color, defined, destroyObject, GridImageryProvider, ImageryLayer, Viewer } from 'cesium';
import TileCoordinatesImageryProvider from './TileCoordinatesImageryProvider.js';
const DEF_COLOR = Color.fromCssColorString('#8aaec3');

export interface WGS84GridLayerOptType {
  viewer: Viewer;
  gridShow?: boolean;
  show?: boolean;
  color?: Color;
  labelColor?: Color;
}

export class WGS84GridLayer {
  private readonly _viewer: Viewer;
  private readonly _gridShow?: boolean;
  private _alpha: number;
  private _show: boolean;
  private readonly _color?: Color;
  private readonly _labelColor?: Color;
  _layer: {
    tileGrid?: ImageryLayer | undefined;
    grid: ImageryLayer | undefined;
  };
  /**
   * @param {Object} options
   * @param {Viewer} options.viewer
   * @param {Boolean|undefined} options.gridShow 子集网格是否显示
   * @param {Boolean|undefined} options.show 是否显示网格
   * @param {Color|undefined} options.color 网格颜色
   * @param {Color|undefined} options.labelColor label颜色
   */
  constructor(options: WGS84GridLayerOptType) {
    const { viewer, gridShow = true, show = false, color = Color.WHITE, labelColor = Color.WHITE } = options ?? {};
    this._alpha = 1;
    this._viewer = viewer;
    this._layer = {
      tileGrid: undefined,
      grid: undefined
    };
    this._show = show;
    if (!defined(this._viewer)) {
      console.warn(`This Viewer is ` + this._viewer);
      return;
    }
    this._gridShow = gridShow;
    this._show = show;
    this._color = color;
    this._labelColor = labelColor;

    this._init();
  }

  _init() {
    const imageryLayers = this._viewer.imageryLayers;
    // @ts-ignore
    this._layer.tileGrid = imageryLayers.addImageryProvider(new TileCoordinatesImageryProvider({ color: this._color, labelColor: this._labelColor }));
    if (this._gridShow) {
      this._layer.grid = imageryLayers.addImageryProvider(
        new GridImageryProvider({
          color: this._color,
          backgroundColor: Color.TRANSPARENT,
          glowColor: Color.TRANSPARENT,
          cells: 8,
          glowWidth: 1
        })
      );
    }
    this.show = this._show;
  }

  isDestroyed() {
    return !1;
  }
  set alpha(value: number) {
    if (this._layer.grid) {
      this._layer.grid.alpha = value;
      this._layer.tileGrid!.alpha = value;
    }
    this._alpha = value;
  }

  destroy() {
    const imageryLayers = this._viewer.imageryLayers;
    this._layer.tileGrid && imageryLayers.remove(this._layer.tileGrid);
    this._layer.grid && imageryLayers.remove(this._layer.grid);
    return destroyObject(this);
  }

  set show(value: boolean) {
    this._layer.tileGrid && (this._layer.tileGrid.show = value);
    this._layer.grid && (this._layer.grid.show = value);
    this._show = value;
  }

  get layer() {
    return this._layer;
  }
}
