import { Gantt } from './Gantt';
import { GanttGraphicCollection } from './GanttGraphicCollection';
import { GanttRangeGraphic } from './GanttRangeGraphic';
import { type GanttDataBase, isBothMode, isFlatMode } from './GanttType';
import { GanttOption } from './GanttOption';
import { GanttSplitLine } from './GanttSplitLine';
import type { GanttGraphicAbstract } from './GanttGraphicAbstract';

export class GanttRow<T extends GanttDataBase = GanttDataBase> extends GanttOption implements GanttGraphicAbstract {
  id: string;
  gantt: Gantt;
  data: T;
  index: number;
  graphicCollection = new GanttGraphicCollection<GanttRangeGraphic>();
  splitLineGraphic?: GanttSplitLine;
  x: number;
  y: number;

  constructor(data: T, gantt: Gantt) {
    super(gantt.options);
    this.data = data;
    this.id = data.id;
    this.gantt = gantt;
    this.index = gantt.rowCollection.size;
    this.gantt.rowCollection.add(this);
    this.setData(data);
    this.x = 0;
    this.y = 0;
    if (this.showSplitLine) {
      this.splitLineGraphic = new GanttSplitLine(this.gantt.ctx, this);
      // @ts-ignore
      this.gantt.graphicCollection.add(this.splitLineGraphic);
      // @ts-ignore
      this.graphicCollection.add(this.splitLineGraphic);
    }
  }

  setData(data: T) {
    if (isBothMode(this.mode)) {
      this.gantt.graphicCollection.add(new GanttRangeGraphic(data, this));
      this.graphicCollection.add(new GanttRangeGraphic(data, this));
      data.flatData?.forEach((item) => {
        this.gantt.graphicCollection.add(new GanttRangeGraphic(item, this, 'point'));
        this.graphicCollection.add(new GanttRangeGraphic(item, this, 'point'));
      });
      return;
    }
    if (isFlatMode(this.mode) && data.flatData) {
      data.flatData.forEach((item) => {
        this.gantt.graphicCollection.add(new GanttRangeGraphic(item, this, 'point'));
        this.graphicCollection.add(new GanttRangeGraphic(item, this, 'point'));
      });
      return;
    }
    this.graphicCollection.add(new GanttRangeGraphic(data, this));
    this.gantt.graphicCollection.add(new GanttRangeGraphic(data, this));
  }

  render() {
    this.graphicCollection.render();
    this.splitLineGraphic?.render();
  }
}
