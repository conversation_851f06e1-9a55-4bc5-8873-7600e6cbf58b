<!--
* @Author: 宋计民
* @Date: 2024/5/28
* @Version: 1.0
* @Content: search-input.vue
-->
<template>
  <Teleport to="body" v-if="show">
    <div class="search-mask" ref="boxRef">
      <el-input class="search-input" v-model="searchName" placeholder="请输入名称" @keydown.enter="change" />
      <div class="search-list" ref="searchListRef">
        <div
          class="search-item"
          :class="{ 'search-item-active': i === currentRowIndex }"
          v-for="(item, i) in showList"
          :key="item.id"
          @click="currentRowIndex = i"
          @dblclick="change"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts" generic="T extends GanttDataBase">
import { GanttDataBase, GanttDataType } from '../../graphic';
import { TreeDataToListData } from '@/utils';
import { useEventListener, onClickOutside } from '@vueuse/core';

defineOptions({ name: 'SearchModal' });

const props = defineProps({
  data: {
    type: Object as PropType<GanttDataType<T>[]>,
    default: () => {
      return [];
    }
  }
});
const emits = defineEmits(['change']);

const show = defineModel<boolean>({ default: false });

const boxRef = ref();
const searchListRef = ref();

const searchName = ref('');
const currentRowIndex = ref(0);
const list = computed(() => TreeDataToListData(props.data));
const showList = computed(() => {
  let data = list.value;
  if (searchName.value) {
    data = data.filter((item) => {
      return item.label.includes(searchName.value);
    });
  }
  currentRowIndex.value = 0;
  return data;
});

const change = () => {
  const row = showList.value[currentRowIndex.value];
  if (row) {
    // console.log('跳转到任务', currentRowIndex.value);
    emits('change', row);
  }
};

onClickOutside(boxRef, (event: MouseEvent) => {
  event.stopPropagation();
  show.value = false;
});
useEventListener(document, 'keydown', (e) => {
  if (!show.value) return;
  const el = toValue(searchListRef);

  if (e.key == 'ArrowDown') {
    e.preventDefault();
    if (currentRowIndex.value >= showList.value.length - 1) return;
    currentRowIndex.value++;
    el.scrollTop = 30 * currentRowIndex.value;
  }
  if (e.key == 'ArrowUp') {
    e.preventDefault();
    if (currentRowIndex.value == 0) return;
    currentRowIndex.value--;
    el.scrollTop = 30 * currentRowIndex.value;
  }

  if (e.key == 'Enter') {
    e.preventDefault();
    change();
  }
});
</script>

<style scoped lang="less">
.search-mask {
  --item-height: 30px;

  width: 400px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  background-color: #263448;
  border-radius: 5px;
  border: 1px solid #4c5f7b;
  overflow: hidden;

  .search-list {
    height: 500px;
    overflow-y: scroll;
    .search-item {
      box-sizing: border-box;
      height: var(--item-height);
      border: 1px solid #4c5f7b;
    }
    .search-item-active {
      background-color: #4c5f7b;
    }
  }
}
</style>
