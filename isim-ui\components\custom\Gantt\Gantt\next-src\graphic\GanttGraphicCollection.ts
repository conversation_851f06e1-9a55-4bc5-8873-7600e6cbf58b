import type { GanttGraphicAbstract } from './GanttGraphicAbstract';

export class GanttGraphicCollection<T extends GanttGraphicAbstract> {
  collectionMap = new Map<string, T>();
  collectionSet = new Set<T>();

  constructor() {}

  get size() {
    return this.collectionSet.size;
  }

  add(graphic: T) {
    this.collectionMap.set(graphic.id, graphic);
    this.collectionSet.add(graphic);
  }
  remove(graphic: T) {
    this.collectionMap.delete(graphic.id);
    this.collectionSet.delete(graphic);
  }
  getById(id: string) {
    const graphic = this.collectionMap.get(id);
    if (graphic) {
      return graphic;
    }
  }

  has(id: string) {
    return this.collectionMap.has(id);
  }

  removeAll() {
    this.collectionMap.clear();
    this.collectionSet.clear();
  }

  values() {
    return [...this.collectionSet];
  }

  render() {
    this.collectionSet.forEach((graphic) => {
      graphic.render();
    });
  }
}
