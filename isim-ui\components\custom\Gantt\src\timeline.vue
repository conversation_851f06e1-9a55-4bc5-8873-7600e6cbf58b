<!--
* <AUTHOR> 宋计民
* @Date :  2023/3/27
* @Version : 1.0
* @Content : timeline
-->
<template>
  <div class="sim-gantt-timeline" @mouseenter="handleMouseEnter">
    <span
      v-for="(item, dex) in dateTextList"
      :key="dex"
      :style="{
        left: item.left
      }"
      class="sim-gantt-time-text"
      >{{ item.time }}</span
    >
    <div class="sim-gantt-scale sim-gantt-long"></div>
    <div class="sim-gantt-scale sim-gantt-short"></div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'SimGanttTimeline'
};
</script>
<script lang="ts" setup>
import dayjs from 'dayjs';
import { GanttNode } from './methods/ganttNode';
import { useGanttEvent, useGanttInject } from './gantt';

const { startTime, endTime, interval, timeRange } = useGanttInject();
const { updateTimeRangeUp, updateTimeRangeDown } = useGanttEvent();
// 计算时间刻度
const dateTextList = computed(() => {
  const arr: any[] = [];
  // 如果数据为空的时候 _ganttHanlde.minTime 始终为0
  // if (timeRange.value === 0 || !_ganttHandle.minTime) return arr;
  // console.log(timeRange, interval)
  for (let i = 0; i <= timeRange.value; i++) {
    const timeSecond = GanttNode.getTime(startTime.value) + interval * i;
    const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');
    const left = 200 * i + 'px';
    arr.push({ timeSecond, time, left });
  }
  return arr;
});

const handleMouseWheel = (e: unknown) => {
  const event = e as WheelEvent;
  const deltaY = event.deltaY;
  // if (deltaY > 0) {
  //   return updateTimeRangeUp()
  // }
  // return updateTimeRangeDown()
};

const handleMouseEnter = (e: MouseEvent) => {
  const target = e.target as HTMLDivElement;
  target.addEventListener('mousewheel', handleMouseWheel);
};
</script>
<style scoped lang="less">
.sim-gantt-timeline {
  height: 100%;
  .sim-gantt-time-text {
    position: absolute;
    top: -3px;
    white-space: nowrap;
    font-size: 13px;
  }
  .sim-gantt-scale {
    position: absolute;
    left: 0;
    bottom: 3px;
    width: 100%;
    background-image: linear-gradient(90deg, fade(white, 50%) 1px, transparent 1px, transparent 100%);
    background-repeat: repeat-x;
  }
  .sim-gantt-long {
    height: 10px;
    background-size: 200px 100%;
  }
  .sim-gantt-short {
    height: 5px;
    background-size: 5px 100%;
  }
}
</style>
