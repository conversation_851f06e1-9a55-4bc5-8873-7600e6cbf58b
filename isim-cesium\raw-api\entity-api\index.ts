import { ConstantPositionProperty, CustomDataSource, CzmlDataSource, Entity, EntityCollection, PropertyBag, SampledPositionProperty } from 'cesium';
import { getCurrentTimeJulian, getEntitySource, getViewerName, onViewerCreated, isString, getViewer } from 'isim-cesium';
export function createEntity(entityOpt: Entity.ConstructorOptions, entityCollection?: EntityCollection | string) {
  if (isString(entityCollection)) {
    return onViewerCreated(
      (viewer) => {
        return viewer.entities.add(entityOpt);
      },
      { viewerName: getViewerName(entityCollection) }
    );
  }
  return onViewerCreated(() => {
    return entityCollection!.add(entityOpt);
  });
}

export function isCustomDatasource(data: any): data is CustomDataSource {
  return data instanceof CustomDataSource;
}

export function isCzmlDatasource(data: any): data is CzmlDataSource {
  return data instanceof CzmlDataSource;
}

/**
 * 查找entityId 在对应的source中查找entity
 * @param id
 * @param sourceName
 */
export function getEntityById(id: string, sourceName: string | CustomDataSource | CzmlDataSource | undefined) {
  if (!sourceName) {
    console.error('sourceName is Required');
    return undefined;
  }
  if (isCustomDatasource(sourceName) || isCzmlDatasource(sourceName)) {
    return sourceName.entities.getById(id);
  }
  const source = getEntitySource(sourceName);
  if (source) {
    return source.entities.getById(id);
  }
  return undefined;
}

/**
 * 获取entity的自动properties属性
 * @param entity
 * @param viewerName
 */
export function getEntityProperties(entity: Entity, viewerName?: string): PropertyBag | undefined {
  return entity.properties?.getValue(getCurrentTimeJulian(viewerName));
}

/**
 * 获取properties中的属性
 * @param entity
 * @param field
 */
export function getEntityPropertiesField<T>(entity: Entity, field: string) {
  return getEntityProperties(entity)?.[field] as T;
}

// 获取position
export function getEntityPosition<T>(entity: Entity): T {
  return entity.position as unknown as T;
}

export function getEntityConstantPosition(entity: Entity) {
  return getEntityPosition<ConstantPositionProperty | undefined>(entity);
}

export function getEntitySampledPosition(entity: Entity) {
  return getEntityPosition<SampledPositionProperty | undefined>(entity);
}

/**
 * 设置position
 * @param entity
 * @param position
 */
export function setEntityPosition(entity: Entity | Entity.ConstructorOptions, position: any) {
  entity.position = position;
}

/**
 * 根据id获取推演中的实体
 * @param id
 */
export function getDeduceEntityById(id: string) {
  return getViewer().entities.getById(id);
}
