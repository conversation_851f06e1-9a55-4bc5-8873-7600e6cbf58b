import MineFieldVisualizer from '../../Modules/VisualizationModel/MineFieldVisualizer';
import { defaultValue, Cartesian3 } from 'cesium';
import StaticFeature from './StaticFeature';

export default class MineFieldFeature extends StaticFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this.create();
  }

  create() {
    const center = turf.center(turf.points(this._options.positions));
    const pos = center.geometry.coordinates;
    if (!pos) {
      console.warn('Please check the input positions.');
      return;
    }
    this._entity.position = Cartesian3.fromDegrees(...pos, 10);
    this.primitive = new MineFieldVisualizer({
      ...this._options
    });
  }
}
