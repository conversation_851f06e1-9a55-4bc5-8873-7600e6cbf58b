<template>
  <n-modal v-bind="$attrs" :preset="preset" @close="handleClose">
    <template #header>
      {{ title }}
    </template>
    <slot></slot>
    <template v-if="footerShow" #action>
      <slot name="footer">
        <n-button :loading="loading" @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSure">确定</n-button>
      </slot>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SimNModal'
});
withDefaults(
  defineProps<{
    title: string;
    preset?: 'dialog' | 'card';
    loading?: boolean;
    footerShow?: boolean;
  }>(),
  {
    title: '',
    preset: 'dialog',
    loading: false,
    footerShow: true
  }
);

const emits = defineEmits<{
  'handle-close': [e: MouseEvent];
  'handle-cancel': [e: <PERSON>Event];
  'handle-sure': [e: MouseEvent];
}>();

const handleCancel = (e: MouseEvent) => {
  emits('handle-cancel', e);
};

const handleClose = (e: MouseEvent) => {
  emits('handle-close', e);
};

const handleSure = (e: MouseEvent) => {
  emits('handle-sure', e);
};
</script>

<style></style>
