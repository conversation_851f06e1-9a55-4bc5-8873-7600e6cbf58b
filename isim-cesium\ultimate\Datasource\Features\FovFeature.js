/**
 * <AUTHOR>
 * @email jayjay<PERSON>@yeah.net
 * @date 2022/05/28 10:40:03
 * @description FovFeature
 * @version 1.0
 * */
import FovPrimitive from '../../Scene/Primitives/FovPrimitive.js';
import AbstractFeature from './AbstractFeature.js';

export default class FovFeature extends AbstractFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this.create();
  }

  create() {
    const { heightReference, scene, heightOffset, lineOptions, fillOptions } = this._options;
    this.primitive = new FovPrimitive({
      heightReference,
      scene,
      heightOffset,
      lineOptions,
      fillOptions
    });
  }

  update(time) {
    this._options.position = this.position.getValue(time);
    if (this.primitive) {
      this._updateOptions(time, this.primitive);
    }
  }
}
