/**
 * @Author: 宋计民
 * @Date: 2023-08-29 20:06
 * @Version: 1.0
 * @Content: Draw-Common.ts
 */
import { RectangleAnnotation } from './use-rectangle-annotations';
import { PenAnnotation } from './use-pen-annotation';
import { ArrowAnnotation, CircleAnnotation, TextAnnotation } from './index';

type GraphicType = RectangleAnnotation | PenAnnotation | TextAnnotation | ArrowAnnotation | CircleAnnotation;

const cacheGraphic = new Set<GraphicType>();

export function setAnnotations(graphicInstance: GraphicType) {
  cacheGraphic.add(graphicInstance);
}

export function clearAnnotationGraphic() {
  cacheGraphic.clear();
}

export function refreshAnnotations() {
  cacheGraphic.forEach((item) => {
    item.draw();
  });
}

export interface AnnotationOptionsType {
  color: string;
  startX: number;
  startY: number;
  width: number;
}

export interface AnnotationConfig {
  color: string;
  width: 3;
}

export abstract class AnnotationGraphic {
  abstract ctx: CanvasRenderingContext2D;
  abstract color: string;
  abstract startX: number;
  abstract startY: number;
  abstract width: number;
  abstract draw(): void;
}
