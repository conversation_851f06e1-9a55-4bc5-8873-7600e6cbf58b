/**
 * @Author: 宋计民
 * @Date: 2023-09-14 16:39
 * @Version: 1.0
 * @Content: 右键菜单
 */
import { Cartesian2, Entity } from 'cesium';
import { isArray, isString } from '@/utils';
import { HTMLAttributes } from 'vue';

export interface RightMenuData {
  position: Cartesian2;
  entity?: Entity;
}

export type RightMenuEntityData = Required<RightMenuData>;

export interface RightMenuItem<T = RightMenuData> {
  label: string;
  key: string;

  filter?(data: T): boolean;

  click?(data: T, e?: MouseEvent): void;

  props?: HTMLAttributes;
  children?: RightMenuItem<T>[];
}

interface RightMenuOption {
  rightMenuSelectData: unknown;
  menuShow: boolean;
  menuX: number;
  menuY: number;
  cartesian2: Cartesian2;
  entityOption: Record<string, RightMenuItem>;
  emptyOption: Record<string, RightMenuItem>;
  rightClickCallback: Set<Function>;
}

export const useRightMenuOptionStore = defineStore('situationRightMenu', {
  state() {
    return {
      rightMenuSelectData: null,
      menuShow: false,
      menuX: 0,
      menuY: 0,
      cartesian2: new Cartesian2(0, 0),
      entityOption: {},
      emptyOption: {},
      rightClickCallback: new Set()
    } as RightMenuOption;
  },
  getters: {
    getterEntityMenuOptions: (state) => {
      return Object.values(state.entityOption);
    },
    getterEmptyMenuOptions: (state) => {
      return Object.values(state.emptyOption);
    }
  },
  actions: {
    pushEntityOption(
      data: RightMenuItem<RightMenuEntityData> | RightMenuItem<RightMenuEntityData>[],
      opt?: {
        replace: boolean;
      }
    ) {
      if (!isArray(data)) {
        data = [data];
      }
      const entityOption = this.entityOption;
      data.forEach((item) => {
        if (opt?.replace) {
          entityOption[item.key] = item;
          return;
        }
        if (entityOption[item.key]) {
          console.warn(`右键菜单的key必须是唯一值 ${item.key}`);
          return;
        }
        entityOption[item.key] = item;
      });
    },
    removeEntityOptionByKey(key: string | string[]) {
      if (isString(key)) {
        delete this.entityOption[key];
        return;
      }
      key.forEach((item) => {
        delete this.entityOption[item];
      });
    },
    pushEmptyOption(
      data: RightMenuItem | RightMenuItem[],
      opt?: {
        replace: boolean;
      }
    ) {
      if (!isArray(data)) {
        data = [data];
      }
      const emptyOption = this.emptyOption;
      data.forEach((item) => {
        if (opt?.replace) {
          emptyOption[item.key] = item;
          return;
        }
        if (emptyOption[item.key]) {
          console.warn(`右键菜单的key必须是唯一值 ${item.key}`);
          return;
        }
        emptyOption[item.key] = item;
      });
    },
    removeEmptyOptionByKey(key: string | string[]) {
      if (isString(key)) {
        delete this.emptyOption[key];
        return;
      }
      key.forEach((item) => {
        delete this.emptyOption[item];
      });
    },
    clearOptions() {
      this.entityOption = {};
      this.emptyOption = {};
      this.rightClickCallback.clear();
    }
  }
});
