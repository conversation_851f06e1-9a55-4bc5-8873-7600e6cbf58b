import VisibleLightDetectionFeature from './VisibleLightDetectionFeature';
import WiredCommunicationPrimitive from '../../Modules/VisualizationModel/WiredCommunicationVisualizer';

export default class WiredCommunicationFeature extends VisibleLightDetectionFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
    this._targetEnitity = options.targetEntity;
  }
  create() {
    this.primitive = new WiredCommunicationPrimitive();
  }

  update(time) {
    const startPosition = this.position.getValue(time);
    const endPosition = this._targetEnitity.position.getValue(time);
    if (this.primitive) {
      this.primitive.startPosition = startPosition;
      this.primitive.endPosition = endPosition;
      this._updateOptions(time, this.primitive);
    }
  }
}
