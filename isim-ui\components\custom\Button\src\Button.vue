<template>
  <div
    :class="[
      'sim-button',
      {
        'sim-button--primary': props.type === 'primary',
        'sim-is--disabled': props.disabled
      },
      'sim-button__' + size
    ]"
    @click="onClick"
  >
    <button class="sim-button__btn" type="button" :disabled="props.disabled">
      <slot> {{ label }} </slot>
    </button>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';

defineOptions({
  name: 'SimButton'
});
const props = defineProps({
  type: {
    type: String as PropType<'primary' | 'default'>,
    default: 'default'
  },
  size: {
    type: String as PropType<'medium' | 'small'>,
    default: 'medium'
  },
  label: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click']);

const onClick = (e: MouseEvent) => {
  if (props.disabled) {
    return;
  }
  (e.target as HTMLElement).blur();

  emit('click', e);
};
</script>

<style lang="less">
.sim-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  --button-height: 32px;
  min-width: 96px;
  height: var(--button-height);
  box-sizing: border-box;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  padding: 2px 5px;
  background: var(--secondary-color);
  overflow: hidden;
  color: var(--text-color);
  --button-font-size: 14px;

  &:hover {
    background: var(--secondary-color-hover);
  }
  &:active {
    background: var(--secondary-color-active);
  }
  & + & {
    margin-left: 8px;
  }

  &__btn {
    width: 100%;
    height: var(--button-height);
    display: contents;
    border: none;
    background: transparent;
    padding: 0;
    margin: 0;
    outline: none;
    font: inherit;
    text-decoration: none;
    font-size: var(--button-font-size);
    color: inherit;
    cursor: inherit;
  }
  &__small {
    min-width: 80px;
    padding: 0 3px;
    font-size: 12px;
    font-weight: 400;
    --button-font-size: 12px;
    --button-height: 28px;
  }
}

.sim-button--primary {
  color: var(--secondary-text-color);
  background: var(--primary-color-linear);
  &:hover {
    background: var(--primary-color-hover-linear);
  }
  &:active {
    background: var(--primary-color-active-linear);
  }
}

.sim-is--disabled {
  color: var(--secondary-text-color);
  background: var(--disabled-bg-color);
  cursor: no-drop;
  &:hover {
    background: var(--disabled-bg-color);
  }
  &:active {
    background: var(--disabled-bg-color);
  }
  .sim-button__btn {
    cursor: no-drop;
  }
}
</style>
