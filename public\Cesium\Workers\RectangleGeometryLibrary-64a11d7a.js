define(["exports","./Matrix2-57f130bc","./when-4bbc8319","./RuntimeError-1349fdaf","./Transforms-4b8effe1","./ComponentDatatype-17ffa790"],(function(t,n,a,e,o,r){"use strict";const s=Math.cos,i=Math.sin,c=Math.sqrt,h={computePosition:function(t,n,e,o,r,h,g){const u=n.radiiSquared,l=t.nwCorner,C=t.boundingRectangle;let M=l.latitude-t.granYCos*o+r*t.granXSin;const S=s(M),d=i(M),w=u.z*d;let m=l.longitude+o*t.granYSin+r*t.granXCos;const f=S*s(m),p=S*i(m),R=u.x*f,X=u.y*p,Y=c(R*f+X*p+w*d);if(h.x=R/Y,h.y=X/Y,h.z=w/Y,e){const n=t.stNwCorner;a.defined(n)?(M=n.latitude-t.stGranYCos*o+r*t.stGranXSin,m=n.longitude+o*t.stGranYSin+r*t.stGranXCos,g.x=(m-t.stWest)*t.lonScalar,g.y=(M-t.stSouth)*t.latScalar):(g.x=(m-C.west)*t.lonScalar,g.y=(M-C.south)*t.latScalar)}}},g=new n.Matrix2;let u=new n.Cartesian3;const l=new n.Cartographic;let C=new n.Cartesian3;const M=new o.GeographicProjection;function S(t,a,e,o,r,s,i){const c=Math.cos(a),h=o*c,l=e*c,S=Math.sin(a),d=o*S,w=e*S;u=M.project(t,u),u=n.Cartesian3.subtract(u,C,u);const m=n.Matrix2.fromRotation(a,g);u=n.Matrix2.multiplyByVector(m,u,u),u=n.Cartesian3.add(u,C,u),s-=1,i-=1;const f=(t=M.unproject(u,t)).latitude,p=f+s*w,R=f-h*i,X=f-h*i+s*w,Y=Math.max(f,p,R,X),O=Math.min(f,p,R,X),_=t.longitude,x=_+s*l,G=_+i*d,P=_+i*d+s*l;return{north:Y,south:O,east:Math.max(_,x,G,P),west:Math.min(_,x,G,P),granYCos:h,granYSin:d,granXCos:l,granXSin:w,nwCorner:t}}h.computeOptions=function(t,a,o,s,i,c,h){let g,u=t.east,d=t.west,w=t.north,m=t.south,f=!1,p=!1;w===r.CesiumMath.PI_OVER_TWO&&(f=!0),m===-r.CesiumMath.PI_OVER_TWO&&(p=!0);const R=w-m;g=d>u?r.CesiumMath.TWO_PI-d+u:u-d;const X=Math.ceil(g/a)+1,Y=Math.ceil(R/a)+1,O=g/(X-1),_=R/(Y-1),x=n.Rectangle.northwest(t,c),G=n.Rectangle.center(t,l);0===o&&0===s||(G.longitude<x.longitude&&(G.longitude+=r.CesiumMath.TWO_PI),C=M.project(G,C));const P=_,W=O,y=n.Rectangle.clone(t,i),I={granYCos:P,granYSin:0,granXCos:W,granXSin:0,nwCorner:x,boundingRectangle:y,width:X,height:Y,northCap:f,southCap:p};if(0!==o){const t=S(x,o,O,_,0,X,Y);if(w=t.north,m=t.south,u=t.east,d=t.west,w<-r.CesiumMath.PI_OVER_TWO||w>r.CesiumMath.PI_OVER_TWO||m<-r.CesiumMath.PI_OVER_TWO||m>r.CesiumMath.PI_OVER_TWO)throw new e.DeveloperError("Rotated rectangle is invalid.  It crosses over either the north or south pole.");I.granYCos=t.granYCos,I.granYSin=t.granYSin,I.granXCos=t.granXCos,I.granXSin=t.granXSin,y.north=w,y.south=m,y.east=u,y.west=d}if(0!==s){o-=s;const t=n.Rectangle.northwest(y,h),a=S(t,o,O,_,0,X,Y);I.stGranYCos=a.granYCos,I.stGranXCos=a.granXCos,I.stGranYSin=a.granYSin,I.stGranXSin=a.granXSin,I.stNwCorner=t,I.stWest=a.west,I.stSouth=a.south}return I},t.RectangleGeometryLibrary=h}));
//# sourceMappingURL=RectangleGeometryLibrary-64a11d7a.js.map
