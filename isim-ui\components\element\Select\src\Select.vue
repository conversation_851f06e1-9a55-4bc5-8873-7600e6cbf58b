<!--
* <AUTHOR> 宋计民
* @Date :  2023/4/7
* @Version : 1.0
* @Content : Select
-->
<template>
  <el-select ref="selectRef" v-bind="$attrs" filterable fit-input-width :suffix-icon="SimSuffix" :disabled="$attrs.disabled || $attrs.readonly">
    <template v-for="(value, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData" />
    </template>
  </el-select>
</template>

<script lang="ts" setup>
import { SimSuffix } from 'isim-ui';
import { ElSelect } from 'element-plus';

const selectRef = ref<InstanceType<typeof ElSelect>>();
defineOptions({
  name: 'SimSelect'
});
</script>
<style scoped lang="less">
.sim-select {
  padding: 0;
  height: 32px;
  display: flex;
  align-items: center;
  width: 100%;
  .el-select {
    border: none;
  }
}

:deep(.el-input__wrapper) {
  background-color: rgba(var(--primary-color-val), 0);
  box-shadow: none;
  //border-radius: 8px;
}

:deep(.el-input .el-input__wrapper.is-focus) {
  box-shadow: none !important;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select:hover) {
  --el-select-border-color-hover: none;
}

.el-select {
  width: 100%;
}
</style>
