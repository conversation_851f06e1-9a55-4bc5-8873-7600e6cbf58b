import { Cartesian3, Cartographic, defaultValue, HeadingPitchRoll, Math as CesiumMath, PrimitiveCollection } from 'cesium';
import { createModel } from '../VisualizerMethod.js';
import AbstractPrimitive from '../AbstractVisualizer.js';

const BLOCK_WALL = {
  width: 4.15,
  height: 3.7
};

export default class ModelLinePrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._url = options.url;
    this._positions = options.positions;
    this._gapSpace = defaultValue(options.gapSpace, BLOCK_WALL.width);
    this._update = true;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive = this._primitive && this._primitive.destroy();
      this._primitive = this._updateModel();
    }

    this._primitive?.update(frameState);
  }

  _updateModel() {
    if (!this._url) {
      return;
    }
    const primitives = new PrimitiveCollection();
    const positions = this._positions;
    let pos, posNext, distance, t, posNew, carto, cartoNext, hpr;
    for (let i = 0; i < positions.length - 1; i++) {
      pos = positions[i];
      posNext = positions[i + 1];
      carto = Cartographic.fromCartesian(pos);
      cartoNext = Cartographic.fromCartesian(posNext);
      hpr = HeadingPitchRoll.fromDegrees(
        turf.bearing(
          turf.point([CesiumMath.toDegrees(carto.longitude), CesiumMath.toDegrees(carto.latitude)]),
          turf.point([CesiumMath.toDegrees(cartoNext.longitude), CesiumMath.toDegrees(cartoNext.latitude)])
        ),
        0,
        0
      );

      if (i === 0) {
        createModel(primitives, this._url, pos, hpr);
      }
      distance = Cartesian3.distance(pos, posNext);
      t = Math.round(distance / this._gapSpace);
      for (let j = 1 / t; j <= 1.0; j += 1 / t) {
        posNew = new Cartesian3();
        Cartesian3.lerp(pos, posNext, j, posNew);
        createModel(primitives, this._url, posNew, hpr);
      }
    }
    return primitives;
  }
}
