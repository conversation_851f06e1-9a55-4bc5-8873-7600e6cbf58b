{"version": 3, "file": "WallGeometryLibrary-1e758b06.js", "sources": ["../../../../Source/Core/WallGeometryLibrary.js"], "sourcesContent": ["import arrayRemoveDuplicates from \"./arrayRemoveDuplicates.js\";\nimport Cartesian3 from \"./Cartesian3.js\";\nimport Cartographic from \"./Cartographic.js\";\nimport defined from \"./defined.js\";\nimport CesiumMath from \"./Math.js\";\nimport PolylinePipeline from \"./PolylinePipeline.js\";\n\n/**\n * @private\n */\nconst WallGeometryLibrary = {};\n\nfunction latLonEquals(c0, c1) {\n  return (\n    CesiumMath.equalsEpsilon(c0.latitude, c1.latitude, CesiumMath.EPSILON10) &&\n    CesiumMath.equalsEpsilon(c0.longitude, c1.longitude, CesiumMath.EPSILON10)\n  );\n}\n\nconst scratchCartographic1 = new Cartographic();\nconst scratchCartographic2 = new Cartographic();\nfunction removeDuplicates(ellipsoid, positions, topHeights, bottomHeights) {\n  positions = arrayRemoveDuplicates(positions, Cartesian3.equalsEpsilon);\n\n  const length = positions.length;\n  if (length < 2) {\n    return;\n  }\n\n  const hasBottomHeights = defined(bottomHeights);\n  const hasTopHeights = defined(topHeights);\n\n  const cleanedPositions = new Array(length);\n  const cleanedTopHeights = new Array(length);\n  const cleanedBottomHeights = new Array(length);\n\n  const v0 = positions[0];\n  cleanedPositions[0] = v0;\n\n  const c0 = ellipsoid.cartesianToCartographic(v0, scratchCartographic1);\n  if (hasTopHeights) {\n    c0.height = topHeights[0];\n  }\n\n  cleanedTopHeights[0] = c0.height;\n\n  if (hasBottomHeights) {\n    cleanedBottomHeights[0] = bottomHeights[0];\n  } else {\n    cleanedBottomHeights[0] = 0.0;\n  }\n\n  const startTopHeight = cleanedTopHeights[0];\n  const startBottomHeight = cleanedBottomHeights[0];\n  let hasAllSameHeights = startTopHeight === startBottomHeight;\n\n  let index = 1;\n  for (let i = 1; i < length; ++i) {\n    const v1 = positions[i];\n    const c1 = ellipsoid.cartesianToCartographic(v1, scratchCartographic2);\n    if (hasTopHeights) {\n      c1.height = topHeights[i];\n    }\n    hasAllSameHeights = hasAllSameHeights && c1.height === 0;\n\n    if (!latLonEquals(c0, c1)) {\n      cleanedPositions[index] = v1; // Shallow copy!\n      cleanedTopHeights[index] = c1.height;\n\n      if (hasBottomHeights) {\n        cleanedBottomHeights[index] = bottomHeights[i];\n      } else {\n        cleanedBottomHeights[index] = 0.0;\n      }\n      hasAllSameHeights =\n        hasAllSameHeights &&\n        cleanedTopHeights[index] === cleanedBottomHeights[index];\n\n      Cartographic.clone(c1, c0);\n      ++index;\n    } else if (c0.height < c1.height) {\n      // two adjacent positions are the same, so use whichever has the greater height\n      cleanedTopHeights[index - 1] = c1.height;\n    }\n  }\n\n  if (hasAllSameHeights || index < 2) {\n    return;\n  }\n\n  cleanedPositions.length = index;\n  cleanedTopHeights.length = index;\n  cleanedBottomHeights.length = index;\n\n  return {\n    positions: cleanedPositions,\n    topHeights: cleanedTopHeights,\n    bottomHeights: cleanedBottomHeights,\n  };\n}\n\nconst positionsArrayScratch = new Array(2);\nconst heightsArrayScratch = new Array(2);\nconst generateArcOptionsScratch = {\n  positions: undefined,\n  height: undefined,\n  granularity: undefined,\n  ellipsoid: undefined,\n};\n\n/**\n * @private\n */\nWallGeometryLibrary.computePositions = function (\n  ellipsoid,\n  wallPositions,\n  maximumHeights,\n  minimumHeights,\n  granularity,\n  duplicateCorners\n) {\n  const o = removeDuplicates(\n    ellipsoid,\n    wallPositions,\n    maximumHeights,\n    minimumHeights\n  );\n\n  if (!defined(o)) {\n    return;\n  }\n\n  wallPositions = o.positions;\n  maximumHeights = o.topHeights;\n  minimumHeights = o.bottomHeights;\n\n  const length = wallPositions.length;\n  const numCorners = length - 2;\n  let topPositions;\n  let bottomPositions;\n\n  const minDistance = CesiumMath.chordLength(\n    granularity,\n    ellipsoid.maximumRadius\n  );\n\n  const generateArcOptions = generateArcOptionsScratch;\n  generateArcOptions.minDistance = minDistance;\n  generateArcOptions.ellipsoid = ellipsoid;\n\n  if (duplicateCorners) {\n    let count = 0;\n    let i;\n\n    for (i = 0; i < length - 1; i++) {\n      count +=\n        PolylinePipeline.numberOfPoints(\n          wallPositions[i],\n          wallPositions[i + 1],\n          minDistance\n        ) + 1;\n    }\n\n    topPositions = new Float64Array(count * 3);\n    bottomPositions = new Float64Array(count * 3);\n\n    const generateArcPositions = positionsArrayScratch;\n    const generateArcHeights = heightsArrayScratch;\n    generateArcOptions.positions = generateArcPositions;\n    generateArcOptions.height = generateArcHeights;\n\n    let offset = 0;\n    for (i = 0; i < length - 1; i++) {\n      generateArcPositions[0] = wallPositions[i];\n      generateArcPositions[1] = wallPositions[i + 1];\n\n      generateArcHeights[0] = maximumHeights[i];\n      generateArcHeights[1] = maximumHeights[i + 1];\n\n      const pos = PolylinePipeline.generateArc(generateArcOptions);\n      topPositions.set(pos, offset);\n\n      generateArcHeights[0] = minimumHeights[i];\n      generateArcHeights[1] = minimumHeights[i + 1];\n\n      bottomPositions.set(\n        PolylinePipeline.generateArc(generateArcOptions),\n        offset\n      );\n\n      offset += pos.length;\n    }\n  } else {\n    generateArcOptions.positions = wallPositions;\n    generateArcOptions.height = maximumHeights;\n    topPositions = new Float64Array(\n      PolylinePipeline.generateArc(generateArcOptions)\n    );\n\n    generateArcOptions.height = minimumHeights;\n    bottomPositions = new Float64Array(\n      PolylinePipeline.generateArc(generateArcOptions)\n    );\n  }\n\n  return {\n    bottomPositions: bottomPositions,\n    topPositions: topPositions,\n    numCorners: numCorners,\n  };\n};\nexport default WallGeometryLibrary;\n"], "names": ["WallGeometryLibrary", "latLonEquals", "c0", "c1", "CesiumMath", "equalsEpsilon", "latitude", "EPSILON10", "longitude", "scratchCartographic1", "Cartographic", "scratchCartographic2", "positionsArrayScratch", "Array", "heightsArrayScratch", "generateArcOptionsScratch", "positions", "undefined", "height", "granularity", "ellipsoid", "computePositions", "wallPositions", "maximumHeights", "minimumHeights", "duplicateCorners", "o", "topHeights", "bottomHeights", "length", "arrayRemoveDuplicates", "Cartesian3", "hasBottomHeights", "defined", "hasTopHeights", "cleanedPositions", "cleanedTopHeights", "cleanedBottomHeights", "v0", "cartesianToCartographic", "hasAllSameHeights", "index", "i", "v1", "clone", "removeDuplicates", "numCorners", "topPositions", "bottomPositions", "minDistance", "chord<PERSON>ength", "maximumRadius", "generateArcOptions", "count", "PolylinePipeline", "numberOfPoints", "Float64Array", "generateArcPositions", "generateArcHeights", "offset", "pos", "generateArc", "set"], "mappings": "8LAUM,MAAAA,EAAsB,CAAG,EAE/B,SAASC,EAAaC,EAAIC,GACxB,OACEC,EAAUA,WAACC,cAAcH,EAAGI,SAAUH,EAAGG,SAAUF,EAAUA,WAACG,YAC9DH,EAAUA,WAACC,cAAcH,EAAGM,UAAWL,EAAGK,UAAWJ,EAAUA,WAACG,UAEpE,CAEA,MAAME,EAAuB,IAAIC,EAAAA,aAC3BC,EAAuB,IAAID,EAAAA,aAiFjC,MAAME,EAAwB,IAAIC,MAAM,GAClCC,EAAsB,IAAID,MAAM,GAChCE,EAA4B,CAChCC,eAAWC,EACXC,YAAQD,EACRE,iBAAaF,EACbG,eAAWH,GAMbjB,EAAoBqB,iBAAmB,SACrCD,EACAE,EACAC,EACAC,EACAL,EACAM,GAEA,MAAMC,EApGR,SAA0BN,EAAWJ,EAAWW,EAAYC,GAG1D,MAAMC,GAFNb,EAAYc,EAAAA,sBAAsBd,EAAWe,EAAUA,WAAC1B,gBAE/BwB,OACzB,GAAIA,EAAS,EACX,OAGF,MAAMG,EAAmBC,UAAQL,GAC3BM,EAAgBD,UAAQN,GAExBQ,EAAmB,IAAItB,MAAMgB,GAC7BO,EAAoB,IAAIvB,MAAMgB,GAC9BQ,EAAuB,IAAIxB,MAAMgB,GAEjCS,EAAKtB,EAAU,GACrBmB,EAAiB,GAAKG,EAEtB,MAAMpC,EAAKkB,EAAUmB,wBAAwBD,EAAI7B,GAC7CyB,IACFhC,EAAGgB,OAASS,EAAW,IAGzBS,EAAkB,GAAKlC,EAAGgB,OAGxBmB,EAAqB,GADnBL,EACwBJ,EAAc,GAEd,EAK5B,IAAIY,EAFmBJ,EAAkB,KACfC,EAAqB,GAG3CI,EAAQ,EACZ,IAAK,IAAIC,EAAI,EAAGA,EAAIb,IAAUa,EAAG,CAC/B,MAAMC,EAAK3B,EAAU0B,GACfvC,EAAKiB,EAAUmB,wBAAwBI,EAAIhC,GAC7CuB,IACF/B,EAAGe,OAASS,EAAWe,IAEzBF,EAAoBA,GAAmC,IAAdrC,EAAGe,OAEvCjB,EAAaC,EAAIC,GAeXD,EAAGgB,OAASf,EAAGe,SAExBkB,EAAkBK,EAAQ,GAAKtC,EAAGe,SAhBlCiB,EAAiBM,GAASE,EAC1BP,EAAkBK,GAAStC,EAAGe,OAG5BmB,EAAqBI,GADnBT,EAC4BJ,EAAcc,GAEd,EAEhCF,EACEA,GACAJ,EAAkBK,KAAWJ,EAAqBI,GAEpD/B,EAAAA,aAAakC,MAAMzC,EAAID,KACrBuC,EAKL,CAED,OAAID,GAAqBC,EAAQ,OAAjC,GAIAN,EAAiBN,OAASY,EAC1BL,EAAkBP,OAASY,EAC3BJ,EAAqBR,OAASY,EAEvB,CACLzB,UAAWmB,EACXR,WAAYS,EACZR,cAAeS,GAEnB,CAsBYQ,CACRzB,EACAE,EACAC,EACAC,GAGF,IAAKS,EAAAA,QAAQP,GACX,OAGFJ,EAAgBI,EAAEV,UAClBO,EAAiBG,EAAEC,WACnBH,EAAiBE,EAAEE,cAEnB,MAAMC,EAASP,EAAcO,OACvBiB,EAAajB,EAAS,EAC5B,IAAIkB,EACAC,EAEJ,MAAMC,EAAc7C,EAAAA,WAAW8C,YAC7B/B,EACAC,EAAU+B,eAGNC,EAAqBrC,EAI3B,GAHAqC,EAAmBH,YAAcA,EACjCG,EAAmBhC,UAAYA,EAE3BK,EAAkB,CACpB,IACIiB,EADAW,EAAQ,EAGZ,IAAKX,EAAI,EAAGA,EAAIb,EAAS,EAAGa,IAC1BW,GACEC,EAAAA,iBAAiBC,eACfjC,EAAcoB,GACdpB,EAAcoB,EAAI,GAClBO,GACE,EAGRF,EAAe,IAAIS,aAAqB,EAARH,GAChCL,EAAkB,IAAIQ,aAAqB,EAARH,GAEnC,MAAMI,EAAuB7C,EACvB8C,EAAqB5C,EAC3BsC,EAAmBpC,UAAYyC,EAC/BL,EAAmBlC,OAASwC,EAE5B,IAAIC,EAAS,EACb,IAAKjB,EAAI,EAAGA,EAAIb,EAAS,EAAGa,IAAK,CAC/Be,EAAqB,GAAKnC,EAAcoB,GACxCe,EAAqB,GAAKnC,EAAcoB,EAAI,GAE5CgB,EAAmB,GAAKnC,EAAemB,GACvCgB,EAAmB,GAAKnC,EAAemB,EAAI,GAE3C,MAAMkB,EAAMN,EAAAA,iBAAiBO,YAAYT,GACzCL,EAAae,IAAIF,EAAKD,GAEtBD,EAAmB,GAAKlC,EAAekB,GACvCgB,EAAmB,GAAKlC,EAAekB,EAAI,GAE3CM,EAAgBc,IACdR,EAAgBA,iBAACO,YAAYT,GAC7BO,GAGFA,GAAUC,EAAI/B,MACf,CACL,MACIuB,EAAmBpC,UAAYM,EAC/B8B,EAAmBlC,OAASK,EAC5BwB,EAAe,IAAIS,aACjBF,EAAgBA,iBAACO,YAAYT,IAG/BA,EAAmBlC,OAASM,EAC5BwB,EAAkB,IAAIQ,aACpBF,EAAgBA,iBAACO,YAAYT,IAIjC,MAAO,CACLJ,gBAAiBA,EACjBD,aAAcA,EACdD,WAAYA,EAEhB"}