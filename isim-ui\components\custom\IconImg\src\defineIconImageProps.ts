/**
 * @Author: 宋计民
 * @Date: 2023/4/14
 * @Version: 1.0
 * @Content:
 */

import { PropType } from 'vue';

export type IconImageType =
  | 'caijueguize'
  | 'execel'
  | 'exe'
  | 'fangzhenchengxumoxing'
  | 'jar'
  | 'jiaohumoxing'
  | 'json'
  | 'junshigainianmoxing'
  | 'mp4'
  | 'ocl'
  | 'pdf'
  | 'pinggumoxing'
  | 'ppt'
  | 'rar'
  | 'renwumoxing'
  | 'shitimoxing'
  | 'shujumoxing'
  | 'shujuyuan'
  | 'shuxueluojimoxing'
  | 'tree'
  | 'txt'
  | 'uml'
  | 'wenjianjia'
  | 'word'
  | 'wuliguize'
  | 'xingdongmoxing'
  | 'xingweiguize'
  | 'xml'
  | 'zip'
  | 'zuozhananli'
  | 'zuozhanshiyanmoxing'
  | 'zuozhanxiangding'
  | 'muban'
  | 'file'
  | 'evafile'
  | 'experiment-design-template'
  | 'experiment-type-zuozhan'
  | 'experiment-type-wuqi'
  | 'experiment-type-junshi'
  | 'experiment-type-zhanfa'
  | 'project'
  | 'indicate'
  | 'indicate-sys'
  | 'evaplan'
  | 'dataset'
  | 'reportModule'
  | 'cpu'
  | 'disk'
  | 'progress'
  | 'memory'
  | 'zuozhanguize'
  | 'algorithm';
export function defineIconImageProps() {
  return {
    type: {
      required: true,
      type: String as PropType<IconImageType>,
      default: '*'
    }
  };
}
