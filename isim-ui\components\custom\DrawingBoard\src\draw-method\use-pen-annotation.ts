/**
 * @Author: 宋计民
 * @Date: 2023-08-29 20:06
 * @Version: 1.0
 * @Content: Free-Line.ts
 */
import { AnnotationConfig, AnnotationGraphic, AnnotationOptionsType, setAnnotations } from './annotation-graphic';
import { useAnnotationsInject } from '../use-annotations-inject';
export function usePenAnnotation(config: AnnotationConfig) {
  const { ctx, drawBoxRef, bindMouseUpAndLeave } = useAnnotationsInject();
  const start = () => {
    const canvas = drawBoxRef.value;
    canvas.onmousedown = (e) => {
      const startX = e.offsetX;
      const startY = e.offsetY;
      const penAnnotation = new PenAnnotation(ctx.value, { ...toRaw(config), startX, startY });
      setAnnotations(penAnnotation);
      canvas.onmousemove = (e) => {
        penAnnotation.addPointWidthPush(e.offsetX, e.offsetY);
      };
      bindMouseUpAndLeave();
    };
  };
  const stop = () => {
    drawBoxRef.value.onmousedown = null;
  };
  return {
    start,
    stop
  };
}

export class PenAnnotation extends AnnotationGraphic {
  readonly ctx: CanvasRenderingContext2D;
  readonly color: string;
  readonly startX: number;
  readonly startY: number;
  readonly width: number;
  private points: Array<[number, number]> = [];
  constructor(ctx: CanvasRenderingContext2D, options: AnnotationOptionsType) {
    super();
    const { color = '#f00', startX, startY, width = 1 } = options;
    this.ctx = ctx;
    this.color = color;
    this.startX = startX;
    this.startY = startY;
    this.width = width;
    this.firstDrawWidthPush(startX, startY);
  }

  addPointWidthPush(x: number, y: number) {
    this.points.push([x, y]);
    this.drawPoints(x, y);
  }
  drawPoints(x: number, y: number) {
    this.ctx.lineTo(x, y);
    this.ctx.stroke();
  }

  firstDraw(startX: number, startY: number) {
    this.ctx.beginPath();
    this.ctx.lineWidth = this.width;
    this.ctx.moveTo(startX, startY);
    this.ctx.strokeStyle = this.color;
  }

  firstDrawWidthPush(startX: number, startY: number) {
    this.points.push([startX, startY]);
    this.firstDraw(startX, startY);
  }

  draw() {
    const [[x, y], ...others] = this.points;
    this.firstDraw(x, y);
    others.forEach(([x, y]) => {
      this.drawPoints(x, y);
    });
  }
}
