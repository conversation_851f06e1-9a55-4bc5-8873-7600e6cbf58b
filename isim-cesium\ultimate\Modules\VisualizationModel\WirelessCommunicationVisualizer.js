import { Cartesian3, Color, defaultValue, JulianDate, Material } from 'cesium';
import PolylinePrimitive from '../../Scene/Primitives/PolylinePrimitive.js';
import AbstractPrimitive from './AbstractVisualizer.js';

export default class WirelessCommunicationPrimitive extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._color = defaultValue(options.color, Color.YELLOW);
    this._width = defaultValue(options.width, 3);
    this._duration = defaultValue(options.duration, undefined);
    this._interval = defaultValue(options.interval, undefined);

    this._uniforms = {
      speed: 10,
      percent: 0.05,
      gradient: 0.1,
      number: 5,
      ...options.uniforms
    };
    this._startPosition = options.startPosition;
    this._endPosition = options.endPosition;
    this._materialType = Material.PolylineFlowType;
    this._startTime = undefined;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._startTime = frameState.time.clone();
      this._primitive = this._primitive && this._primitive.destroy();
      this._primitive = this._createPrimitive();
      console.log(frameState);
    }

    if (this._duration) {
      const duration = JulianDate.secondsDifference(frameState.time, this._startTime);
      if (duration < this._duration) {
        this._primitive?.update(frameState);
      } else if (this._interval) {
        if (duration >= this._interval + this._duration) {
          this._startTime = frameState.time.clone();
        }
      }
      return;
    }
    this._primitive?.update(frameState);
  }

  _createPrimitive() {
    if (!this._startPosition || !this._endPosition) {
      return;
    }

    return new PolylinePrimitive({
      color: this._color,
      width: this._width,
      materialType: this._materialType,
      uniforms: this._uniforms,
      positions: [this._startPosition, this._endPosition]
    });
  }

  set color(value) {
    this._color = value;
    this._primitive && (this._primitive.color = value);
  }

  set width(value) {
    this._width = value;
    this._primitive && (this._primitive.width = value);
  }

  set uniforms(value) {
    this._uniforms = {
      ...this._uniforms,
      ...value
    };
    this._primitive && (this._primitive.uniforms = value);
  }

  set duration(value) {
    this._duration = value;
  }

  set interval(value) {
    this._interval = value;
  }

  set startPosition(value) {
    if (Cartesian3.equals(this._startPosition, value)) {
      return;
    }

    this._startPosition = value;
    if (this._primitive) {
      this._primitive.positions = [this._startPosition, this._endPosition];
    }
  }

  set endPosition(value) {
    if (Cartesian3.equals(this._endPosition, value)) {
      return;
    }
    this._endPosition = value;
    if (this._primitive) {
      this._primitive.positions = [this._startPosition, this._endPosition];
    }
  }
}
