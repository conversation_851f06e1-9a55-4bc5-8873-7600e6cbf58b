/*
 * @Author: songjimin
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
 */

export class GanttNode {
  constructor(props) {
    this.data = props.data;
    this.labelField = props.labelField;
    this.startTimeField = props.startTimeField;
    this.endTimeField = props.endTimeField;
    (this.childrenField = props.childrenField), (this.defaultExpandAll = props.defaultExpandAll);
    this.maxTime = 0;
    this.minTime = 0;
  }

  handleData(data = this.data) {
    return data.map((item) => {
      item.expand = this.defaultExpandAll;
      item._startTime = this.getStartTime(item);
      item._endTime = this.getEndTime(item);
      // 计算整个数据中的最早时间
      if (this.minTime === 0) {
        this.minTime = item._startTime;
      } else if (this.minTime > item._startTime) {
        this.minTime = item._startTime;
      }

      // 计算数据重的最晚时间
      if (this.maxTime < item._endTime) {
        this.maxTime = item._endTime;
      }
      if (item.children) {
        item.children = this.handleData(item.children);
      }

      return item;
    });
  }

  getStartTime(data) {
    return this.parseTime(data[this.startTimeField]);
  }

  getEndTime(data) {
    return this.parseTime(data[this.endTimeField]);
  }

  getLabel(data) {
    if (typeof this.labelField === 'function') {
      return this.labelField(data);
    }
    return data[this.labelField];
  }
  getChildren(data) {
    return data[this.childrenField];
  }

  parseTime(time) {
    if (typeof time === 'string') {
      return new Date(time).getTime();
    }
    return time;
  }
  static getTime(time) {
    if (typeof time === 'string') {
      return new Date(time).getTime();
    }
    return time;
  }
}
