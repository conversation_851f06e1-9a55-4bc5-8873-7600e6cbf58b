uniform vec4 color;
uniform float spacing;
uniform float size;
czm_material czm_getMaterial(czm_materialInput materialInput) {
    czm_material material = czm_getDefaultMaterial(materialInput);

    vec2 st = materialInput.st;

    float time = fract(czm_frameNumber * 0.02); // 动态时间变量

    // 计算点网格的位置
    vec2 gridPosition = fract(st / spacing) - 0.5; // 归一化到点中心为原点
    float dist = length(gridPosition); // 计算像素到点中心的距离

    // 判断是否在点内并应用闪烁效果
    float alpha = step(dist, size) * abs(sin(time * 3.14159));

    // 设置材质颜色和透明度
    material.diffuse = color.rgb; // 斑点颜色

    material.alpha = alpha; // 透明度随时间闪烁

    return material;
}
