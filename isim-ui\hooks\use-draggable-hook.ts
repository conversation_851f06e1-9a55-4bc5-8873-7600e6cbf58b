/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { MaybeRef, Ref } from 'vue';

interface UseDraggableOption {
  parent: MaybeRef<HTMLElement>;
  dragBox: Ref<HTMLElement>;
  sticky: boolean; //  是否允许吸附
  stickyDistance: number | [number, number]; // 吸附间距
  eventDom: Ref<HTMLElement>;
  mouseDownCallback: (e: MouseEvent) => void;
  mouseMoveCallback: (e: MouseEvent) => void;
  allowOut: boolean;
}

export function useDraggableTransformHook<T extends Ref<HTMLElement>>(handle: T, option?: Partial<UseDraggableOption>) {
  const { parent, sticky, stickyDistance = 20, eventDom, mouseDownCallback } = option ?? {};
  const dragBox = option?.dragBox ?? handle;
  const getParentDom = (): HTMLElement => {
    return parent ? toValue(parent) : (toValue(dragBox).parentNode as HTMLElement);
  };

  const getEventDom = () => {
    return eventDom ? toValue(eventDom) : document.documentElement;
  };

  const setTranslate = (x: number, y: number) => {
    toValue(dragBox).style.transform = `translate(${x}px, ${y}px)`;
  };

  /**
   * 设置吸附功能
   * @param disLeft
   * @param disTop
   */
  const setSticky = (disLeft: number, disTop: number) => {
    const dragBoxDom = toValue(dragBox);
    const [xDistance, yDistance] = Array.isArray(stickyDistance) ? stickyDistance : [stickyDistance, stickyDistance];
    const parentDom = getParentDom();
    const dragBoxWidth = dragBoxDom.clientWidth;
    const dragBoxHeight = dragBoxDom.clientHeight;

    const isLeft = disLeft <= dragBoxWidth / 2;
    const isTop = disTop <= dragBoxHeight / 2;

    if (isLeft) {
      if (Math.abs(disLeft) < xDistance) {
        disLeft = 0;
      }
    } else {
      if (Math.abs(parentDom.clientWidth - (dragBoxDom.clientWidth + disLeft)) < xDistance) {
        disLeft = parentDom.clientWidth - dragBoxDom.clientWidth;
      }
    }

    if (isTop) {
      if (Math.abs(disTop) < yDistance) {
        disTop = 0;
      }
    } else {
      if (Math.abs(parentDom.clientHeight - (dragBoxDom.clientHeight + disTop)) < yDistance) {
        disTop = parentDom.clientHeight - dragBoxDom.clientHeight;
      }
    }
    setTranslate(disLeft, disTop);
  };

  /**
   * 鼠标按下开启拖拽
   * @param ev
   */
  const mouseDown = (ev: MouseEvent) => {
    mouseDownCallback?.(ev);
    const eventBindDom = getEventDom();
    const _handle = toValue(dragBox);
    const _handleRect = getDomBoundingRect(_handle);
    // 记住鼠标按下的位置距离操作DOM的距离
    const distanceX = ev.clientX - _handleRect.left;
    const distanceY = ev.clientY - _handleRect.top;
    const parentRect = getDomBoundingRect(_handle.parentNode as HTMLElement);
    let disLeft = ev.clientX - parentRect.left - distanceX;
    let disTop = ev.clientY - parentRect.top - distanceY;
    const move = (e: MouseEvent) => {
      const { clientX, clientY } = e;

      const xStrict = clientX < parentRect.left || clientX > parentRect.left + parentRect.width;
      const yStrict = clientY < parentRect.top || clientY > parentRect.top + parentRect.height;
      // 防止整个元素全部超出父级元素范围
      if (xStrict || yStrict) {
        return;
      }
      disLeft = clientX - parentRect.left - distanceX;
      disTop = clientY - parentRect.top - distanceY;
      setTranslate(disLeft, disTop);
    };
    const up = () => {
      eventBindDom.removeEventListener('mousemove', move);
      // 吸附功能
      if (sticky) {
        setSticky(disLeft, disTop);
      }
      eventBindDom.removeEventListener('mouseup', up);
    };
    eventBindDom.addEventListener('mousemove', move);
    eventBindDom.addEventListener('mouseup', up);
  };
  const bindMouseDown = () => {
    handle.value.addEventListener('mousedown', mouseDown);
  };
  onMounted(() => {
    bindMouseDown();
  });
  onBeforeUnmount(() => {
    handle.value.removeEventListener('mousedown', mouseDown);
  });
}

export function getDomBoundingRect(dom: HTMLElement) {
  return dom.getBoundingClientRect();
}

export function getTransformMatrix(box: HTMLElement) {
  const transform = window.getComputedStyle(box).getPropertyValue('transform');
  if (transform === 'none') {
    return {
      left: 0,
      top: 0
    };
  }
  const match = transform.match(/matrix\(([^&)]+)\)/);
  if (match) {
    const values = match[1].split(',').map(Number);
    const translateX = values[4];
    const translateY = values[5];
    return {
      left: translateX,
      top: translateY
    };
  }
  return {
    left: 0,
    top: 0
  };
}
