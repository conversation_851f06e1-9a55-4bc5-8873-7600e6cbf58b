/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
// @ts-nocheck

import { Color, defaultValue, defined, Material, Property, createPropertyDescriptor, Event } from 'cesium';
import ShaderSource from './Sharder/LinearGradientCircleMaterial.glsl?raw';

const MaterialType = 'LinearGradientCircle';

Material.LinearGradientCircleType = MaterialType;

type LinearGradientCircleOptions = {
  color1: Color;
  color2: Color;
};

const uniforms: LinearGradientCircleOptions = {
  color1: Color.RED,
  color2: Color.YELLOW
};
Material._materialCache.addMaterial(MaterialType, {
  fabric: {
    type: MaterialType,
    uniforms,
    source: ShaderSource
  },
  translucent: function () {
    return true;
  }
});

export interface LinearGradientCircleMaterialPropertyConstructor {
  new (options?: LinearGradientCircleOptions): {};
}

export const LinearGradientCircleMaterialProperty: LinearGradientCircleMaterialPropertyConstructor = function (
  options?: LinearGradientCircleOptions
) {
  options = defaultValue(options, defaultValue.EMPTY_OBJECT);

  this._definitionChanged = new Event();

  this._color1 = undefined;
  this._color2 = undefined;
  this.color1 = options?.color1 ?? uniforms.color1;
  this.color2 = options?.color2 ?? uniforms.color2;
};

Object.defineProperties(LinearGradientCircleMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return false;
    }
  },

  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    }
  },
  color1: createPropertyDescriptor('color1'),
  color2: createPropertyDescriptor('color2')
});

/**
 * Gets the {@link Material} type at the provided time.
 *
 * @param {JulianDate} _time The time for which to retrieve the type.
 * @returns {String} The type of material.
 */
LinearGradientCircleMaterialProperty.prototype.getType = function (_time) {
  return MaterialType;
};

LinearGradientCircleMaterialProperty.prototype.getValue = function (time, result) {
  if (!defined(result)) {
    result = {};
  }
  result.color1 = Property.getValueOrDefault(this._color1, time, uniforms.color1, result.color1);
  result.color2 = Property.getValueOrDefault(this._color2, time, uniforms.color2, result.color2);

  // console.log('result', result);
  return result;
};

LinearGradientCircleMaterialProperty.prototype.equals = function (other) {
  return this === other;
};
