define(["exports","./Transforms-4b8effe1","./Matrix2-57f130bc","./RuntimeError-1349fdaf","./when-4bbc8319","./EllipsoidTangentPlane-42b68837","./ComponentDatatype-17ffa790","./Plane-0f8ffca6"],(function(e,a,t,n,r,i,s,o){"use strict";function c(e,a){this.center=t.Cartesian3.clone(r.defaultValue(e,t.Cartesian3.ZERO)),this.halfAxes=t.Matrix3.clone(r.defaultValue(a,t.Matrix3.ZERO))}c.packedLength=t.Cartesian3.packedLength+t.Matrix3.packedLength,c.pack=function(e,a,i){return n.Check.typeOf.object("value",e),n.Check.defined("array",a),i=r.defaultValue(i,0),t.Cartesian3.pack(e.center,a,i),t.Matrix3.pack(e.halfAxes,a,i+t.Cartesian3.packedLength),a},c.unpack=function(e,a,i){return n.Check.defined("array",e),a=r.defaultValue(a,0),r.defined(i)||(i=new c),t.Cartesian3.unpack(e,a,i.center),t.Matrix3.unpack(e,a+t.Cartesian3.packedLength,i.halfAxes),i};const d=new t.Cartesian3,C=new t.Cartesian3,l=new t.Cartesian3,u=new t.Cartesian3,h=new t.Cartesian3,f=new t.Cartesian3,m=new t.Matrix3,x={unitary:new t.Matrix3,diagonal:new t.Matrix3};c.fromPoints=function(e,a){if(r.defined(a)||(a=new c),!r.defined(e)||0===e.length)return a.halfAxes=t.Matrix3.ZERO,a.center=t.Cartesian3.ZERO,a;let n;const i=e.length,s=t.Cartesian3.clone(e[0],d);for(n=1;n<i;n++)t.Cartesian3.add(s,e[n],s);const o=1/i;t.Cartesian3.multiplyByScalar(s,o,s);let p,M=0,w=0,g=0,y=0,b=0,E=0;for(n=0;n<i;n++)p=t.Cartesian3.subtract(e[n],s,C),M+=p.x*p.x,w+=p.x*p.y,g+=p.x*p.z,y+=p.y*p.y,b+=p.y*p.z,E+=p.z*p.z;M*=o,w*=o,g*=o,y*=o,b*=o,E*=o;const N=m;N[0]=M,N[1]=w,N[2]=g,N[3]=w,N[4]=y,N[5]=b,N[6]=g,N[7]=b,N[8]=E;const O=t.Matrix3.computeEigenDecomposition(N,x),P=t.Matrix3.clone(O.unitary,a.halfAxes);let T=t.Matrix3.getColumn(P,0,u),I=t.Matrix3.getColumn(P,1,h),A=t.Matrix3.getColumn(P,2,f),R=-Number.MAX_VALUE,S=-Number.MAX_VALUE,D=-Number.MAX_VALUE,L=Number.MAX_VALUE,U=Number.MAX_VALUE,q=Number.MAX_VALUE;for(n=0;n<i;n++)p=e[n],R=Math.max(t.Cartesian3.dot(T,p),R),S=Math.max(t.Cartesian3.dot(I,p),S),D=Math.max(t.Cartesian3.dot(A,p),D),L=Math.min(t.Cartesian3.dot(T,p),L),U=Math.min(t.Cartesian3.dot(I,p),U),q=Math.min(t.Cartesian3.dot(A,p),q);T=t.Cartesian3.multiplyByScalar(T,.5*(L+R),T),I=t.Cartesian3.multiplyByScalar(I,.5*(U+S),I),A=t.Cartesian3.multiplyByScalar(A,.5*(q+D),A);const v=t.Cartesian3.add(T,I,a.center);t.Cartesian3.add(v,A,v);const z=l;return z.x=R-L,z.y=S-U,z.z=D-q,t.Cartesian3.multiplyByScalar(z,.5,z),t.Matrix3.multiplyByScale(a.halfAxes,z,a.halfAxes),a};const p=new t.Cartesian3,M=new t.Cartesian3;function w(e,a,i,s,o,d,C,l,u,h,f){if(!(r.defined(o)&&r.defined(d)&&r.defined(C)&&r.defined(l)&&r.defined(u)&&r.defined(h)))throw new n.DeveloperError("all extents (minimum/maximum X/Y/Z) are required.");r.defined(f)||(f=new c);const m=f.halfAxes;t.Matrix3.setColumn(m,0,a,m),t.Matrix3.setColumn(m,1,i,m),t.Matrix3.setColumn(m,2,s,m);let x=p;x.x=(o+d)/2,x.y=(C+l)/2,x.z=(u+h)/2;const w=M;w.x=(d-o)/2,w.y=(l-C)/2,w.z=(h-u)/2;const g=f.center;return x=t.Matrix3.multiplyByVector(m,x,x),t.Cartesian3.add(e,x,g),t.Matrix3.multiplyByScale(m,w,m),f}const g=new t.Cartographic,y=new t.Cartesian3,b=new t.Cartographic,E=new t.Cartographic,N=new t.Cartographic,O=new t.Cartographic,P=new t.Cartographic,T=new t.Cartesian3,I=new t.Cartesian3,A=new t.Cartesian3,R=new t.Cartesian3,S=new t.Cartesian3,D=new t.Cartesian2,L=new t.Cartesian2,U=new t.Cartesian2,q=new t.Cartesian2,v=new t.Cartesian2,z=new t.Cartesian3,_=new t.Cartesian3,B=new t.Cartesian3,V=new t.Cartesian3,k=new t.Cartesian2,W=new t.Cartesian3,X=new t.Cartesian3,j=new t.Cartesian3,Z=new o.Plane(t.Cartesian3.UNIT_X,0);c.fromRectangle=function(e,a,c,d,C){if(!r.defined(e))throw new n.DeveloperError("rectangle is required");if(e.width<0||e.width>s.CesiumMath.TWO_PI)throw new n.DeveloperError("Rectangle width must be between 0 and 2*pi");if(e.height<0||e.height>s.CesiumMath.PI)throw new n.DeveloperError("Rectangle height must be between 0 and pi");if(r.defined(d)&&!s.CesiumMath.equalsEpsilon(d.radii.x,d.radii.y,s.CesiumMath.EPSILON15))throw new n.DeveloperError("Ellipsoid must be an ellipsoid of revolution (radii.x == radii.y)");let l,u,h,f,m,x,p;if(a=r.defaultValue(a,0),c=r.defaultValue(c,0),d=r.defaultValue(d,t.Ellipsoid.WGS84),e.width<=s.CesiumMath.PI){const n=t.Rectangle.center(e,g),r=d.cartographicToCartesian(n,y),s=new i.EllipsoidTangentPlane(r,d);p=s.plane;const M=n.longitude,z=e.south<0&&e.north>0?0:n.latitude,_=t.Cartographic.fromRadians(M,e.north,c,b),B=t.Cartographic.fromRadians(e.west,e.north,c,E),V=t.Cartographic.fromRadians(e.west,z,c,N),k=t.Cartographic.fromRadians(e.west,e.south,c,O),W=t.Cartographic.fromRadians(M,e.south,c,P),X=d.cartographicToCartesian(_,T);let j=d.cartographicToCartesian(B,I);const Z=d.cartographicToCartesian(V,A);let Y=d.cartographicToCartesian(k,R);const G=d.cartographicToCartesian(W,S),F=s.projectPointToNearestOnPlane(X,D),H=s.projectPointToNearestOnPlane(j,L),J=s.projectPointToNearestOnPlane(Z,U),K=s.projectPointToNearestOnPlane(Y,q),Q=s.projectPointToNearestOnPlane(G,v);return l=Math.min(H.x,J.x,K.x),u=-l,f=Math.max(H.y,F.y),h=Math.min(K.y,Q.y),B.height=k.height=a,j=d.cartographicToCartesian(B,I),Y=d.cartographicToCartesian(k,R),m=Math.min(o.Plane.getPointDistance(p,j),o.Plane.getPointDistance(p,Y)),x=c,w(s.origin,s.xAxis,s.yAxis,s.zAxis,l,u,h,f,m,x,C)}const M=e.south>0,Y=e.north<0,G=M?e.south:Y?e.north:0,F=t.Rectangle.center(e,g).longitude,H=t.Cartesian3.fromRadians(F,G,c,d,z);H.z=0;const J=Math.abs(H.x)<s.CesiumMath.EPSILON10&&Math.abs(H.y)<s.CesiumMath.EPSILON10?t.Cartesian3.UNIT_X:t.Cartesian3.normalize(H,_),K=t.Cartesian3.UNIT_Z,Q=t.Cartesian3.cross(J,K,B);p=o.Plane.fromPointNormal(H,J,Z);const $=t.Cartesian3.fromRadians(F+s.CesiumMath.PI_OVER_TWO,G,c,d,V);u=t.Cartesian3.dot(o.Plane.projectPointOntoPlane(p,$,k),Q),l=-u,f=t.Cartesian3.fromRadians(0,e.north,Y?a:c,d,W).z,h=t.Cartesian3.fromRadians(0,e.south,M?a:c,d,X).z;const ee=t.Cartesian3.fromRadians(e.east,G,c,d,j);return m=o.Plane.getPointDistance(p,ee),x=0,w(H,Q,K,J,l,u,h,f,m,x,C)},c.clone=function(e,a){if(r.defined(e))return r.defined(a)?(t.Cartesian3.clone(e.center,a.center),t.Matrix3.clone(e.halfAxes,a.halfAxes),a):new c(e.center,e.halfAxes)},c.intersectPlane=function(e,i){if(!r.defined(e))throw new n.DeveloperError("box is required.");if(!r.defined(i))throw new n.DeveloperError("plane is required.");const s=e.center,o=i.normal,c=e.halfAxes,d=o.x,C=o.y,l=o.z,u=Math.abs(d*c[t.Matrix3.COLUMN0ROW0]+C*c[t.Matrix3.COLUMN0ROW1]+l*c[t.Matrix3.COLUMN0ROW2])+Math.abs(d*c[t.Matrix3.COLUMN1ROW0]+C*c[t.Matrix3.COLUMN1ROW1]+l*c[t.Matrix3.COLUMN1ROW2])+Math.abs(d*c[t.Matrix3.COLUMN2ROW0]+C*c[t.Matrix3.COLUMN2ROW1]+l*c[t.Matrix3.COLUMN2ROW2]),h=t.Cartesian3.dot(o,s)+i.distance;return h<=-u?a.Intersect.OUTSIDE:h>=u?a.Intersect.INSIDE:a.Intersect.INTERSECTING};const Y=new t.Cartesian3,G=new t.Cartesian3,F=new t.Cartesian3,H=new t.Cartesian3,J=new t.Cartesian3,K=new t.Cartesian3;c.distanceSquaredTo=function(e,a){if(!r.defined(e))throw new n.DeveloperError("box is required.");if(!r.defined(a))throw new n.DeveloperError("cartesian is required.");const i=t.Cartesian3.subtract(a,e.center,p),o=e.halfAxes;let c=t.Matrix3.getColumn(o,0,Y),d=t.Matrix3.getColumn(o,1,G),C=t.Matrix3.getColumn(o,2,F);const l=t.Cartesian3.magnitude(c),u=t.Cartesian3.magnitude(d),h=t.Cartesian3.magnitude(C);let f=!0,m=!0,x=!0;l>0?t.Cartesian3.divideByScalar(c,l,c):f=!1,u>0?t.Cartesian3.divideByScalar(d,u,d):m=!1,h>0?t.Cartesian3.divideByScalar(C,h,C):x=!1;const M=!f+!m+!x;let w,g,y;if(1===M){let e=c;w=d,g=C,m?x||(e=C,g=c):(e=d,w=c),y=t.Cartesian3.cross(w,g,J),e===c?c=y:e===d?d=y:e===C&&(C=y)}else if(2===M){w=c,m?w=d:x&&(w=C);let e=t.Cartesian3.UNIT_Y;e.equalsEpsilon(w,s.CesiumMath.EPSILON3)&&(e=t.Cartesian3.UNIT_X),g=t.Cartesian3.cross(w,e,H),t.Cartesian3.normalize(g,g),y=t.Cartesian3.cross(w,g,J),t.Cartesian3.normalize(y,y),w===c?(d=g,C=y):w===d?(C=g,c=y):w===C&&(c=g,d=y)}else 3===M&&(c=t.Cartesian3.UNIT_X,d=t.Cartesian3.UNIT_Y,C=t.Cartesian3.UNIT_Z);const b=K;b.x=t.Cartesian3.dot(i,c),b.y=t.Cartesian3.dot(i,d),b.z=t.Cartesian3.dot(i,C);let E,N=0;return b.x<-l?(E=b.x+l,N+=E*E):b.x>l&&(E=b.x-l,N+=E*E),b.y<-u?(E=b.y+u,N+=E*E):b.y>u&&(E=b.y-u,N+=E*E),b.z<-h?(E=b.z+h,N+=E*E):b.z>h&&(E=b.z-h,N+=E*E),N};const Q=new t.Cartesian3,$=new t.Cartesian3;c.computePlaneDistances=function(e,i,s,o){if(!r.defined(e))throw new n.DeveloperError("box is required.");if(!r.defined(i))throw new n.DeveloperError("position is required.");if(!r.defined(s))throw new n.DeveloperError("direction is required.");r.defined(o)||(o=new a.Interval);let c=Number.POSITIVE_INFINITY,d=Number.NEGATIVE_INFINITY;const C=e.center,l=e.halfAxes,u=t.Matrix3.getColumn(l,0,Y),h=t.Matrix3.getColumn(l,1,G),f=t.Matrix3.getColumn(l,2,F),m=t.Cartesian3.add(u,h,Q);t.Cartesian3.add(m,f,m),t.Cartesian3.add(m,C,m);const x=t.Cartesian3.subtract(m,i,$);let p=t.Cartesian3.dot(s,x);return c=Math.min(p,c),d=Math.max(p,d),t.Cartesian3.add(C,u,m),t.Cartesian3.add(m,h,m),t.Cartesian3.subtract(m,f,m),t.Cartesian3.subtract(m,i,x),p=t.Cartesian3.dot(s,x),c=Math.min(p,c),d=Math.max(p,d),t.Cartesian3.add(C,u,m),t.Cartesian3.subtract(m,h,m),t.Cartesian3.add(m,f,m),t.Cartesian3.subtract(m,i,x),p=t.Cartesian3.dot(s,x),c=Math.min(p,c),d=Math.max(p,d),t.Cartesian3.add(C,u,m),t.Cartesian3.subtract(m,h,m),t.Cartesian3.subtract(m,f,m),t.Cartesian3.subtract(m,i,x),p=t.Cartesian3.dot(s,x),c=Math.min(p,c),d=Math.max(p,d),t.Cartesian3.subtract(C,u,m),t.Cartesian3.add(m,h,m),t.Cartesian3.add(m,f,m),t.Cartesian3.subtract(m,i,x),p=t.Cartesian3.dot(s,x),c=Math.min(p,c),d=Math.max(p,d),t.Cartesian3.subtract(C,u,m),t.Cartesian3.add(m,h,m),t.Cartesian3.subtract(m,f,m),t.Cartesian3.subtract(m,i,x),p=t.Cartesian3.dot(s,x),c=Math.min(p,c),d=Math.max(p,d),t.Cartesian3.subtract(C,u,m),t.Cartesian3.subtract(m,h,m),t.Cartesian3.add(m,f,m),t.Cartesian3.subtract(m,i,x),p=t.Cartesian3.dot(s,x),c=Math.min(p,c),d=Math.max(p,d),t.Cartesian3.subtract(C,u,m),t.Cartesian3.subtract(m,h,m),t.Cartesian3.subtract(m,f,m),t.Cartesian3.subtract(m,i,x),p=t.Cartesian3.dot(s,x),c=Math.min(p,c),d=Math.max(p,d),o.start=c,o.stop=d,o};const ee=new a.BoundingSphere;c.isOccluded=function(e,t){if(!r.defined(e))throw new n.DeveloperError("box is required.");if(!r.defined(t))throw new n.DeveloperError("occluder is required.");const i=a.BoundingSphere.fromOrientedBoundingBox(e,ee);return!t.isBoundingSphereVisible(i)},c.prototype.intersectPlane=function(e){return c.intersectPlane(this,e)},c.prototype.distanceSquaredTo=function(e){return c.distanceSquaredTo(this,e)},c.prototype.computePlaneDistances=function(e,a,t){return c.computePlaneDistances(this,e,a,t)},c.prototype.isOccluded=function(e){return c.isOccluded(this,e)},c.equals=function(e,a){return e===a||r.defined(e)&&r.defined(a)&&t.Cartesian3.equals(e.center,a.center)&&t.Matrix3.equals(e.halfAxes,a.halfAxes)},c.prototype.clone=function(e){return c.clone(this,e)},c.prototype.equals=function(e){return c.equals(this,e)},e.OrientedBoundingBox=c}));
//# sourceMappingURL=OrientedBoundingBox-a6de6b19.js.map
