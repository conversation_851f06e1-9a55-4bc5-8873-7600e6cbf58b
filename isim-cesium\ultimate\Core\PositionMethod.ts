import { Cartesian2, defined, Ray, Scene, SceneMode } from 'cesium';

const rayScratch = new Ray();

/**
 * @private
 * @param {Scene} scene
 * @param {*} eventPos
 */
function getWorldPosition(scene: Scene, eventPos: Cartesian2) {
  if (scene.pickPositionSupported && scene.mode !== SceneMode.SCENE2D) {
    return scene.pickPosition(eventPos);
  }
  if (defined(scene.globe)) {
    const ray = scene.camera.getPickRay(eventPos, rayScratch);
    const position = scene.globe.pick(ray, scene);
    if (position) {
      return position;
    }
    return scene.camera.pickEllipsoid(eventPos, scene.globe.ellipsoid);
  }
}

export { getWorldPosition };
