/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/08/08 18:18:19
 * @description content
 * @version 1.0
 * */

import RadarDetectionPrimitive from '../../Modules/VisualizationModel/RadarDetectionVisualizer.js';
import VisibleLightDetectionFeature from './VisibleLightDetectionFeature.js';

export default class RadarDetectionFeature extends VisibleLightDetectionFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
  }

  create() {
    this.primitive = new RadarDetectionPrimitive();
  }
}
