/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/01/17 15:24:22
 * @description Polyline Primitive
 * @version 2.0
 * */

import {
  ArcType,
  BoundingSphere,
  ClassificationType,
  Color,
  ColorGeometryInstanceAttribute,
  defaultValue,
  defined,
  Ellipsoid,
  GeometryInstance,
  GroundPolylineGeometry,
  GroundPolylinePrimitive,
  Material,
  PolylineColorAppearance,
  PolylineGeometry,
  PolylineMaterialAppearance,
  Primitive
} from 'cesium';

import AbstractPrimitive from './AbstractPrimitive';

export default class PolylinePrimitive extends AbstractPrimitive {
  constructor(options) {
    super(options);
    this._ellipsoid = defaultValue(options.ellipsoid, Ellipsoid.WGS84);
    this._width = defaultValue(options.width, 3);
    this._depthFailShow = defaultValue(options.depthFailShow, true);
    this._color = Color.clone(defaultValue(options.color, Color.WHITE));
    this._depthFailColor = Color.clone(defaultValue(options.depthFailColor, this._color));
    this._positions = defaultValue(options.positions, []);
    this._materialType = options.materialType;
    this._depthFailMaterialType = defaultValue(options.depthFailMaterialType, this._materialType);
    this._uniforms = defaultValue(options.uniforms, undefined);
    this._depthFailUniforms = defaultValue(options.depthFailUniforms, this._uniforms);
    this._loop = defaultValue(options.loop, !1);
    this._clampToGround = defaultValue(options.clampToGround, false);
    this._classificationType = defaultValue(options.classificationType, ClassificationType.BOTH);
    this._allowPicking = defaultValue(options.allowPicking, true);
    this._boundingSphere = new BoundingSphere();
    this._update = true;
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    const positions = this._positions;
    if (!defined(positions) || positions.length < 2) {
      this._primitive = this._primitive && this._primitive.destroy();
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive = this._primitive && this._primitive.destroy();
      this._primitive = this._clampToGround ? this._createGroundPrimitive() : this._createPrimitive();
      this._boundingSphere = BoundingSphere.fromPoints(positions, this._boundingSphere);
    }

    this._primitive.update(frameState);
  }

  _createPrimitive() {
    let positions = this._positions;
    if (this._loop && positions.length > 2) {
      positions = positions.slice();
      positions.push(positions[0]);
    }
    return new Primitive({
      geometryInstances: new GeometryInstance({
        geometry: new PolylineGeometry({
          positions,
          width: this._width,
          vertexFormat: PolylineMaterialAppearance.VERTEX_FORMAT,
          ellipsoid: this._ellipsoid,
          arcType: ArcType.NONE
        }),
        attributes: {
          color: ColorGeometryInstanceAttribute.fromColor(this._color),
          depthFailColor: this._depthFailShow ? ColorGeometryInstanceAttribute.fromColor(this._depthFailColor) : undefined
        },
        id: this._id
      }),
      appearance: createAppearance(this._materialType, this._color, this._uniforms),
      depthFailAppearance: this._depthFailShow
        ? createAppearance(this._depthFailMaterialType, this._depthFailColor, this._depthFailUniforms)
        : undefined,
      asynchronous: !1,
      allowPicking: this._allowPicking
    });
  }

  _createGroundPrimitive() {
    let positions = this._positions;
    if (this._loop && positions.length > 2) {
      positions = positions.slice();
      positions.push(positions[0]);
    }

    return new GroundPolylinePrimitive({
      geometryInstances: new GeometryInstance({
        geometry: new GroundPolylineGeometry({
          positions,
          width: this._width,
          vertexFormat: PolylineMaterialAppearance.VERTEX_FORMAT,
          ellipsoid: this._ellipsoid,
          arcType: ArcType.GEODESIC
        }),
        attributes: { color: ColorGeometryInstanceAttribute.fromColor(this._color) },
        id: this._id
      }),
      appearance: createAppearance(this._materialType, this._color, this._uniforms),
      asynchronous: !1,
      allowPicking: this._allowPicking,
      classificationType: this._classificationType
    });
  }

  get positions() {
    return this._positions;
  }

  set positions(value) {
    this._positions = value;
    this._update = true;
  }

  get color() {
    return this._color;
  }

  set color(value) {
    if (!Color.equals(this._color, value)) {
      this._color = Color.clone(value, this._color);
      if (defined(this._primitive)) {
        let color = this._primitive.getGeometryInstanceAttributes(this._id).color;
        color = value.toBytes(color);
        this._primitive.getGeometryInstanceAttributes(this._id).color = color;
      }
    }
  }

  get depthFailColor() {
    return this._depthFailColor;
  }

  set depthFailColor(value) {
    if (!Color.equals(this._depthFailColor, value)) {
      this._depthFailColor = Color.clone(value, this._depthFailColor);
      if (defined(this._primitive) && !this._clampToGround) {
        let color = this._primitive.getGeometryInstanceAttributes(this._id).depthFailColor;
        color = value.toBytes(color);
        this._primitive.getGeometryInstanceAttributes(this._id).depthFailColor = color;
      }
    }
  }

  get materialType() {
    return this._materialType;
  }

  set materialType(value) {
    if (this._materialType !== value) {
      this._materialType = value;
      this._update = !0;
    }
  }

  get depthFailMaterialType() {
    return this._depthFailMaterialType;
  }

  set depthFailMaterialType(value) {
    if (this._depthFailMaterialType !== value) {
      this._depthFailMaterialType = value;
      this._update = !0;
    }
  }

  get uniforms() {
    return this._uniforms;
  }

  set uniforms(value) {
    if (this._uniforms !== value) {
      this._uniforms = value;
      if (defined(value) && defined(this._primitive)) {
        const uniforms = this._primitive.appearance.material.uniforms;
        for (let i in value) {
          if (value.hasOwnProperty(i)) {
            uniforms[i] = value[i];
          }
        }
      }
    }
  }

  get depthFailUniforms() {
    return this._depthFailUniforms;
  }

  set depthFailUniforms(value) {
    if (this._depthFailUniforms !== value) {
      this._depthFailUniforms = value;
      if (defined(value) && defined(this._primitive)) {
        const uniforms = this._primitive.depthFailAppearance.material.uniforms;
        for (let i in value) {
          if (value.hasOwnProperty(i)) {
            uniforms[i] = value[i];
          }
        }
      }
    }
  }

  get boundingVolume() {
    return this._boundingSphere;
  }

  get width() {
    return this._width;
  }

  set width(value) {
    this._width = value;
    this._update = true;
  }

  get ellipsoid() {
    return this._ellipsoid;
  }

  get loop() {
    return this._loop;
  }

  get clampToGround() {
    return this._clampToGround;
  }

  get classificationType() {
    return this._classificationType;
  }

  set classificationType(value) {
    this._classificationType = value;
    this._update = !0;
  }

  get allowPicking() {
    return this._allowPicking;
  }
}

/**
 * @private
 */
function createAppearance(materialType, color, uniforms) {
  if (defined(materialType)) {
    uniforms = defined(uniforms) ? uniforms : {};
    uniforms.color = color;
    return new PolylineMaterialAppearance({ material: Material.fromType(materialType, uniforms) });
  }
  return new PolylineColorAppearance();
}
