/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/05/24 11:26:05
 * @description create new PluginCollection
 * @test add entity plugin links （It's not good idea）
 * @version 3.0
 * */
import { defaultValue, Entity } from 'cesium';
import BaseRadarFeature from '../Features/BaseRadarFeature.js';
import FovFeature from '../Features/FovFeature.js';
import AbstractCollection from './AbstractCollection.js';
import ModelFeature from '../Features/ModelFeature';
import BombFeature from '../Features/BombFeature';
import VisibleLightDetectionFeature from '../Features/VisibleLightDetectionFeature.js';
import RadarDetectionFeature from '../Features/RadarDetectionFeature.js';
import InfraredDetectionFeature from '../Features/InfraredDetectionFeature';
import WiredCommunicationFeature from '../Features/WiredCommunicationFeature';
import WirelessCommunicationFeature from '../Features/WirelessCommunicationFeature';
import ObstructWallFeature from '../Features/ObstructWallFeature';
import ObstructLineFeature from '../Features/ObstructLineFeature';
import MineFieldFeature from '../Features/MineFieldFeature';
import ConeFeature from 'isim-cesium/ultimate/Datasource/Features/ConeFeature';

// PluginCollection作为一个所有自定义图元的集合
// 每个PluginCollection，可以创建每种自定义图元各一个
export default class PluginCollection extends AbstractCollection {
  constructor(pluginOption, owner) {
    super(pluginOption);

    owner && this.init(owner);
  }
  // 初始化
  init(owner) {
    super.init(owner);
    this.registerEvent();
    this.pluginOption && this.add(this.pluginOption);
  }

  // 注册与entity绑定事件,entity被删除时，PluginCollection一并删除
  registerEvent() {
    const entity = this.entity;
    const listener = entity.entityCollection.collectionChanged.addEventListener(
      (_entityCollection, _addEntityList, _removeEntityList, _changedEntityList) => {
        if (_removeEntityList.includes(entity)) {
          // console.log('监听删除');
          this.destroy();
          listener();
        }
      }
    );
  }

  /**
   * 添加图元
   * @param {object} plugin
   * @param {string} plugin.id
   * @param {Entity} plugin.entity
   * @param {object | undefined} plugin.key keyof featureMap
   */
  add(plugin) {
    super.add(plugin);
    processFeatures(plugin, this);

    // console.log('this.pluginOption', this.pluginOption);
    return this;
  }
}
const featureMap = {
  baseRadarFeature: BaseRadarFeature,
  fovFeature: FovFeature,
  modelFeature: ModelFeature,
  bombFeature: BombFeature,
  visibleLightDVFeature: VisibleLightDetectionFeature,
  radarDVFeature: RadarDetectionFeature,
  infraredDVFeature: InfraredDetectionFeature,
  wiredCVFeature: WiredCommunicationFeature,
  wirelessCVFeature: WirelessCommunicationFeature,
  obstructWallFeature: ObstructWallFeature,
  obstructLineFeature: ObstructLineFeature,
  mineFieldFeature: MineFieldFeature,
  coneRadarFeature: ConeFeature
};

function processFeatures(plugin, pluginCollection) {
  if (!pluginCollection?.entity) {
    return;
  }
  const features = pluginCollection.features;

  for (let key in featureMap) {
    if (!plugin[key]) continue;

    // 创建时采用覆盖式，如果对应图元已经存在，则先删除，再创建新的
    if (features[key]) {
      pluginCollection.remove(features[key].primitive);
      features[key] = undefined;
    }

    plugin[key].show = defaultValue(plugin[key].show, true);
    const FeatureClass = featureMap[key];
    let feature = plugin[key];
    if (!(feature instanceof FeatureClass)) {
      feature = new FeatureClass(feature, pluginCollection);
      // feature = new FeatureClass(plugin, pluginCollection);
    } else {
      pluginCollection.pluginOption[key] = feature._options;
    }

    feature.setPluginCollection(pluginCollection);
    features[key] = feature;
    pluginCollection._primitives.add(feature.primitive);
  }
}
