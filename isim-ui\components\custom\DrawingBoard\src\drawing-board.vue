<!--
* <AUTHOR> 宋计民
* @Date :  2023-07-12 10:33
* @Version : 1.0
* @Content : 画板组件
-->
<template>
  <div id="sim-drawing-board" class="sim-drawing-board">
    <handle-tools @close="close" />
    <div ref="drawBoxRef" class="sim-drawing-board__content">
      <canvas ref="drawingBoardRef" class="sim-drawing-board__canvas" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

import HandleTools from './handle-tools/handle-tools.vue';
import { DRAW_BOX_KEY, DRAW_CTX_KEY } from './use-annotations-inject';
defineOptions({
  name: 'SimDrawingBoard'
});

const isShow = defineModel<boolean>('isShow', {
  default: true
});

defineProps({
  width: {
    type: String as PropType<string>,
    default: '100%'
  }
});

const drawingBoardRef = ref<HTMLCanvasElement>();

const drawBoxRef = ref<HTMLDivElement>();

const ctx = shallowRef<CanvasRenderingContext2D | null>(null);

provide(DRAW_CTX_KEY, ctx);
provide(DRAW_BOX_KEY, drawBoxRef);

const initCanvas = () => {
  const _canvas = drawingBoardRef.value!;
  const box = drawBoxRef.value!;
  _canvas.width = box.clientWidth;
  _canvas.height = box.clientHeight;
  ctx.value = _canvas.getContext('2d');
};

const close = () => {
  console.log(112);
  isShow.value = false;
};

nextTick(() => {
  initCanvas();
});
</script>

<style scoped lang="less">
.width-100p {
  width: 100%;
}
.sim-drawing-board {
  .width-100p;
  position: absolute;
  inset: 0;
  height: 100%;
  --board-tool: 50px;

  &__content {
    .width-100p;
    height: 100%;
  }
  &__canvas {
    font-size: 16px;
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="less">
.annotation-text__input {
  position: absolute;
  width: auto;
  min-width: 100px;
  min-height: 50px;
  border: 1px dashed var(--model-bg-color-8);
  transform: translateY(-50%);
}
</style>
