/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */

import { GanttRange, GanttRangePoint } from './GanttRow.ts';

/**
 * default 普通模式
 * flat 平铺模式
 * both 两种模式都支持 同时显示flat数据和普通模式的数据
 */
export type GanttMode = 'default' | 'flat' | 'both';

function generateGanttMode(modeCondition: GanttMode) {
  return function (mode: GanttMode) {
    return mode == modeCondition;
  };
}

export const isFlatMode = generateGanttMode('flat');

export const isBothMode = generateGanttMode('both');

export const enum GanttGraphicType {
  range,
  rangePoint,
  point,
  line,
  splintLine,
  scrollBar
}
export function isRangeGraphicType(graphic: any): graphic is GanttRange {
  return graphic.type === GanttGraphicType.range;
}

export function isRangePointGraphicType(graphic: any): graphic is GanttRangePoint {
  return graphic.type === GanttGraphicType.rangePoint;
}
