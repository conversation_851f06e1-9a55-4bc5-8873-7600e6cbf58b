/**
 * @Author: 宋计民
 * @Date: 2023-08-12 10:07
 * @Version: 1.0
 * @Content: time-select-format.ts
 */
import { durationFormat } from '@/utils';

export const enum TIME_UNIT {
  day = 'd',
  hour = 'h'
}

export const TIME_UNIT_MAP: Record<string, string> = {
  [TIME_UNIT.day]: '天',
  [TIME_UNIT.hour]: '时'
};

export const unitArray: string[] = [TIME_UNIT.day, TIME_UNIT.hour];

export function parseTimeValue(time: string) {
  function getTimeValue(timeVal: string) {
    return isNaN(Number(timeVal)) ? 0 : Number(timeVal);
  }
  const timeSplit = time.split('-');
  const _time = timeSplit[0];
  let _unit = timeSplit[1];
  if (!unitArray.includes(_unit)) {
    _unit = TIME_UNIT.day;
  }
  return {
    time: getTimeValue(_time),
    unit: _unit
  };
}

export function timeSelectShowFormat(value: string) {
  if (!value) {
    return '';
  }
  if (!value.includes('-')) {
    // 老数据
    return durationFormat(value);
  }
  const { time, unit } = parseTimeValue(value);
  return `${time}${TIME_UNIT_MAP[unit]}`;
}
