<!--
* @Author: 宋计民
* @Date: 2024/1/18
* @Version: 1.0
* @Content: right-menu.vue
-->
<template>
  <n-dropdown
    class="sim-cesium-rightmenu"
    placement="bottom-start"
    trigger="manual"
    :show="hasMenuOptionsShow"
    :options="menuOptions"
    :x="menuX"
    :y="menuY"
    @contextmenu.prevent
    @select="handleSelected"
  />
</template>

<script setup lang="ts">
import { useRightMenu } from 'isim-cesium';
import { onLeftDown, onRightClick } from 'isim-cesium';

defineOptions({
  name: 'SimRightMenu2'
});

const rightStore = useRightMenu();
const { executeFilter } = rightStore;
const { eventList, rightClickEvent, menuOptions, cachePosition, posOffset } = storeToRefs(rightStore);

const hasMenuOptionsShow = computed(() => {
  // @ts-ignore
  return isShow.value && menuOptions.value.some((item) => item?.isShow?.({ data: item, position: cachePosition.value }));
});

const isShow = ref(false);
const menuX = ref(0);
const menuY = ref(0);
const close = onRightClick((options) => {
  const position = options.position.position;
  const offset = toValue(posOffset);
  executeFilter(position);
  menuX.value = position.x - offset.x;
  menuY.value = position.y - offset.y;
  isShow.value = true;
  rightClickEvent.value.forEach((item) => item({ position }));
});

const leftClose = onLeftDown(() => {
  isShow.value = false;
});

const handleSelected = (key: string, option) => {
  eventList.value[key]?.({ ...option, position: cachePosition });
  isShow.value = false;
};

onBeforeUnmount(() => {
  close();
  leftClose();
});
</script>

<style lang="less">
.sim-cesium-rightmenu {
  min-width: 100px;
}
</style>
