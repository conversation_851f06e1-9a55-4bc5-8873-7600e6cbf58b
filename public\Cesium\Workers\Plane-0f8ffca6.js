define(["exports","./Matrix2-57f130bc","./RuntimeError-1349fdaf","./when-4bbc8319","./ComponentDatatype-17ffa790"],(function(e,n,t,a,r){"use strict";function i(e,a){if(t.Check.typeOf.object("normal",e),!r.CesiumMath.equalsEpsilon(n.Cartesian3.magnitude(e),1,r.CesiumMath.EPSILON6))throw new t.DeveloperError("normal must be normalized.");t.Check.typeOf.number("distance",a),this.normal=n.Cartesian3.clone(e),this.distance=a}i.fromPointNormal=function(e,o,s){if(t.Check.typeOf.object("point",e),t.Check.typeOf.object("normal",o),!r.CesiumMath.equalsEpsilon(n.Cartesian3.magnitude(o),1,r.CesiumMath.EPSILON6))throw new t.DeveloperError("normal must be normalized.");const c=-n.Cartesian3.dot(o,e);return a.defined(s)?(n.Cartesian3.clone(o,s.normal),s.distance=c,s):new i(o,c)};const o=new n.Cartesian3;i.fromCartesian4=function(e,s){t.Check.typeOf.object("coefficients",e);const c=n.Cartesian3.fromCartesian4(e,o),l=e.w;if(!r.CesiumMath.equalsEpsilon(n.Cartesian3.magnitude(c),1,r.CesiumMath.EPSILON6))throw new t.DeveloperError("normal must be normalized.");return a.defined(s)?(n.Cartesian3.clone(c,s.normal),s.distance=l,s):new i(c,l)},i.getPointDistance=function(e,a){return t.Check.typeOf.object("plane",e),t.Check.typeOf.object("point",a),n.Cartesian3.dot(e.normal,a)+e.distance};const s=new n.Cartesian3;i.projectPointOntoPlane=function(e,r,o){t.Check.typeOf.object("plane",e),t.Check.typeOf.object("point",r),a.defined(o)||(o=new n.Cartesian3);const c=i.getPointDistance(e,r),l=n.Cartesian3.multiplyByScalar(e.normal,c,s);return n.Cartesian3.subtract(r,l,o)};const c=new n.Matrix4,l=new n.Cartesian4,f=new n.Cartesian3;i.transform=function(e,a,r){t.Check.typeOf.object("plane",e),t.Check.typeOf.object("transform",a);const o=e.normal,s=e.distance,C=n.Matrix4.inverseTranspose(a,c);let m=n.Cartesian4.fromElements(o.x,o.y,o.z,s,l);m=n.Matrix4.multiplyByVector(C,m,m);const u=n.Cartesian3.fromCartesian4(m,f);return m=n.Cartesian4.divideByScalar(m,n.Cartesian3.magnitude(u),m),i.fromCartesian4(m,r)},i.clone=function(e,r){return t.Check.typeOf.object("plane",e),a.defined(r)?(n.Cartesian3.clone(e.normal,r.normal),r.distance=e.distance,r):new i(e.normal,e.distance)},i.equals=function(e,a){return t.Check.typeOf.object("left",e),t.Check.typeOf.object("right",a),e.distance===a.distance&&n.Cartesian3.equals(e.normal,a.normal)},i.ORIGIN_XY_PLANE=Object.freeze(new i(n.Cartesian3.UNIT_Z,0)),i.ORIGIN_YZ_PLANE=Object.freeze(new i(n.Cartesian3.UNIT_X,0)),i.ORIGIN_ZX_PLANE=Object.freeze(new i(n.Cartesian3.UNIT_Y,0)),e.Plane=i}));
//# sourceMappingURL=Plane-0f8ffca6.js.map
