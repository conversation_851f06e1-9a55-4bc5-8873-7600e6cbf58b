<!--
* @Author: 张乐
* @Date: 2023/5/17 10:09
* @Version: 1.0
* @Content: BpmnEditor
-->
<template>
  <div class="flex-wrap bpmn-editor">
    <div class="flex-wrap bpmn-editor__simulation">
      模拟：
      <sim-switch :value="isSimulation" :width="48" @update:value="processSimulation">
        <template #checked>是</template>
        <template #unchecked>否</template>
      </sim-switch>
    </div>

    <div id="bpmn-canvas"></div>
    <custom-properties :business-object="businessObject" @change="handleChangeProp" />
  </div>
</template>

<script lang="ts" setup>
//@ts-ignore
import BpmnModeler from 'bpmn-js/lib/Modeler';

// 组件
import CustomProperties from './custom-properties.vue';

import 'bpmn-js/dist/assets/bpmn-js.css';
import 'bpmn-js/dist/assets/diagram-js.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
import 'bpmn-js-properties-panel/dist/assets/properties-panel.css';
//@ts-ignore
import TokenSimulationModule from 'bpmn-js-token-simulation';

// import RewriteRenderer from '@components/json-schema/field/experiment/experiment-process/components/select-process/RewriteRenderer'
defineOptions({
  name: 'BpmnEditor'
});
const props = defineProps({
  xml: {
    type: String,
    default: ''
  }
});

// bpmn 实例
let bpmnModeler: any;

let curElement: any;
const businessObject = ref({});

/**
 * 初始化
 */
const initBpmn = () => {
  console.log(document.querySelector('#properties-panel-wrap'));
  bpmnModeler = new BpmnModeler({
    container: '#bpmn-canvas',
    keyboard: { bindTo: document.documentElement },
    additionalModules: [TokenSimulationModule]
  });

  bpmnModeler.on('selection.changed', (e: any) => {
    const tempElement = e && e.newSelection && e.newSelection[0];
    if (tempElement && tempElement.type === 'bpmn:UserTask') {
      curElement = tempElement;
      const { name, id, $attrs } = tempElement.businessObject;
      businessObject.value = { name, id, assignee: $attrs['camunda:assignee'] };
    } else {
      curElement = null;
      businessObject.value = {};
    }
  });

  importXml(props.xml);
};

/**
 * 加载xml数据
 * @param xml
 */
const importXml = (xml: string) => {
  bpmnModeler?.importXML(xml, (err: boolean) => {
    if (err) {
      bpmnModeler.createDiagram();
      console.log('加载失败 ==>', err);
    } else {
      console.log('加载成功');
    }
  });
};

watch(
  () => props.xml,
  (newVal) => {
    console.log(newVal, 'newVal');
    newVal && importXml(newVal);
  }
);

/**
 * 属性改变 同步更新xml
 * @param name
 * @param assignee
 */
const handleChangeProp = async ({ name, assignee }: Record<string, string>) => {
  if (!curElement) {
    return false;
  }
  const modeling = bpmnModeler?.get('modeling');
  modeling.updateProperties(curElement, {
    name,
    'camunda:assignee': assignee
  });
};

const isSimulation = ref(false);
const processSimulation = () => {
  console.log(123123123);
  bpmnModeler?.get('toggleMode').toggleMode();
  isSimulation.value = !isSimulation.value;
};

/**
 * 保存xml
 */
const saveXml = async () => {
  const { err, xml } = await bpmnModeler?.saveXML();
  if (err) {
    return false;
  }
  return xml;
};

onMounted(() => {
  initBpmn();
});

defineExpose({ saveXml, processSimulation });
</script>
<style>
@import 'bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css';
</style>
<style scoped lang="less">
.bpmn-editor {
  position: relative;
  width: 100%;
  height: 100%;

  #canvas {
    flex: 1;
    height: 100%;
  }

  &__simulation {
    position: absolute;
    right: 328px;
    top: 6px;
    z-index: 9999;
  }

  :deep(.bjs-container) {
    .bjs-powered-by,
    .djs-palette-entries .group[data-group='create'] {
      display: none;
    }

    .djs-palette {
      background: rgba(var(--text-color-val), 0.05);
      border-color: rgba(var(--text-color-val), 0.1);
      border-radius: 4px;
    }

    .new-parent {
      background: transparent !important;
    }

    .djs-visual {
      //fill: transparent !important;
      //stroke: #fff !important;
    }

    .djs-visual > * {
      //fill: transparent !important;
      //stroke: #fff !important;
    }

    .djs-palette.two-column.open {
      width: 48px;
    }

    .djs-connection-preview path {
      //fill: transparent !important;
      //stroke: #fff !important;
    }

    .djs-palette .entry,
    .djs-palette .djs-palette-toggle {
      color: #fff;
    }

    .djs-context-pad .entry {
      box-sizing: border-box;
      background: rgba(var(--text-color-val), 0.1);
      border: 1px solid rgba(var(--text-color-val), 0.3);
      box-shadow: none;
      border-radius: 4px;
    }

    .djs-popup {
      background: rgba(var(--text-color-val), 0.05);
      border: 1px solid rgba(var(--text-color-val), 0.1);
      border-radius: 8px;
    }

    .djs-popup-body .entry-header {
      color: #fff;
    }

    .djs-direct-editing-parent {
      background: rgba(var(--text-color-val), 0.05) !important;
      font-width: 400 !important;
    }

    .djs-popup .entry.selected {
      background: rgba(var(--text-color-val), 0.1);
    }

    .djs-popup-search input {
      background: rgba(var(--text-color-val), 0.05);
      border: 1px solid rgba(var(--text-color-val), 0.1);
      color: #fff;
    }

    .djs-popup-search-icon {
      stroke: #fff;
    }

    marker path {
      stroke: #fff !important;
      fill: #fff !important;
    }

    &.simulation {
      border: 1px solid rgba(var(--primary-color-val), 0.1);
    }
    .bts-toggle-mode {
      display: none;
    }

    .bts-notification,
    .bts-log {
      background: rgba(var(--text-color-val), 0.05);
      color: #fff;
    }

    .bts-icon {
      color: var(--text-color);
    }

    .bts-entry,
    .bts-header {
      background: rgba(var(--primary-color-val), 0.15);
    }

    .bts-set-animation-speed {
      background: rgba(var(--primary-color-val), 0.15);
      .bts-animation-speed-button {
        background: transparent;
        color: #fff;

        &.active .bts-icon {
          color: var(--primary-color);
        }
      }
    }
  }
}
</style>
