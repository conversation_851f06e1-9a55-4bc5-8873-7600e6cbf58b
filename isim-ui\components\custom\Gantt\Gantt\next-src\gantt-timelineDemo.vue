<!--
* @Author: 宋计民
* @Date: 2024/4/29
* @Version: 1.0
* @Content: gantt-content.vue
-->
<template>
  <sim-button @click="hiddenConnect">{{ connectLine ? '不显示连线' : '显示连线' }}</sim-button>
  <div>当前时间： {{ dayjs(currentTime).format('YYYY/MM/DD HH:mm:ss') }}</div>

  <div class="gantt-content">
    <sim-gantt-content
      v-model:currentTime="currentTime"
      :data="data"
      :min-time="defaultStartTime.valueOf()"
      :max-time="defaultStartTime.add(11, 'h').valueOf()"
      :left-row-text-format="leftRowTextFormat"
      :task-text-format="taskTextFormat"
      :connect-line="connectLine"
      @node-click="handleClick"
    />
  </div>
</template>

<script setup lang="ts">
import { createUUIdV4 } from '@utils/uuid-utils.ts';
import type { GanttDataType } from 'isim-ui';
import dayjs from 'dayjs';

defineOptions({
  name: 'GanttContent'
});

const defaultStartTime = dayjs('2024/4/29 08:00:00');

const currentTime = ref<number>(defaultStartTime.valueOf());

const data = Array.from({ length: 10 }, (_, index) => {
  const dex = index > 5 ? 5 : index;
  const start = defaultStartTime.add(dex, 'h').valueOf();
  const end = defaultStartTime.add(dex + 1, 'h').valueOf();

  let target = Math.floor(Math.random() * 20) + 1;
  if (target == index) target -= 1;

  return {
    id: index + '',
    startTime: start,
    endTime: end,
    target: index < 2 ? ['2'] : [],
    // target: index < 18 ? [target + ''] : [],
    label: `任务${index}`,
    level: 1,
    children: [
      {
        id: createUUIdV4(),
        startTime: start,
        endTime: end,
        label: `任务${index}-子任务1`,
        level: 2,
        children: [
          {
            id: createUUIdV4(),
            startTime: start,
            endTime: end,
            label: `任务${index}-子任务1-1`,
            level: 2
          }
        ]
      },
      {
        id: createUUIdV4(),
        startTime: start,
        endTime: end,
        label: `任务${index}-子任务2`,
        level: 2
      }
    ]
  } as GanttDataType;
});

data.push(
  ...[
    {
      id: '100' + createUUIdV4(),
      startTime: defaultStartTime.add(2, 'h').valueOf(),
      endTime: defaultStartTime.add(4, 'h').valueOf(),
      label: '任务-last',
      level: 1,
      flatData: [
        {
          id: createUUIdV4(),
          startTime: defaultStartTime.add(2, 'h').valueOf(),
          endTime: defaultStartTime.add(3, 'h').valueOf(),
          label: '任务-last-1',
          level: 1
        },
        {
          id: createUUIdV4(),
          startTime: defaultStartTime.add(4, 'h').valueOf(),
          endTime: defaultStartTime.add(3, 'h').valueOf(),
          label: '任务-last-2',
          level: 1
        }
      ],
      children: [
        {
          id: createUUIdV4(),
          startTime: defaultStartTime.add(2, 'h').valueOf(),
          endTime: defaultStartTime.add(3, 'h').valueOf(),
          label: `任务-last-子任务1`,
          level: 2
        }
      ]
    }
  ]
);

const connectLine = ref(true);
const hiddenConnect = () => (connectLine.value = !connectLine.value);

const handleClick = (_list: any) => {
  // console.log('当前选中节点', _list);
};

const leftRowTextFormat = (data: GanttDataType) => {
  return data.label + '-------------后缀';
};
const taskTextFormat = (data: GanttDataType) => {
  return data.label + '----任务';
};
</script>

<style lang="less">
.gantt-content {
  width: 90%;
  //width: 1000px;
  //height: 300px;
  height: 90%;
}
</style>
