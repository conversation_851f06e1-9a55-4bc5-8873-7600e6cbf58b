import WirelessCommunicationPrimitive from '../../Modules/VisualizationModel/WirelessCommunicationVisualizer';
import WiredCommunicationFeature from './WiredCommunicationFeature';

export default class WirelessCommunicationFeature extends WiredCommunicationFeature {
  constructor(options, pluginCollection) {
    super(options, pluginCollection);
  }
  create() {
    this.primitive = new WirelessCommunicationPrimitive();
  }
}
