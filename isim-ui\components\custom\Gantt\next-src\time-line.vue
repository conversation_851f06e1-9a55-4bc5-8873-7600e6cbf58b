<!--
* @Author: 宋计民
* @Date: 2024/5/8
* @Version: 1.0
* @Content: time-line.vue
-->
<template>
  <span
    class="sim-gantt-timeline__text"
    v-for="(item, i) in timeRange"
    :key="item.timeSecond"
    :style="{
      left: `${(i * 100) / timeNumber}%`
    }"
  >
    {{ item.time }}
  </span>
  <div class="sim-gantt-timeline__tick sim-gantt-timeline__long"></div>
  <div class="sim-gantt-timeline__tick sim-gantt-timeline__short"></div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';

defineOptions({
  name: 'TimeLine'
});
const props = defineProps({
  minTime: {
    type: Number,
    default: 0
  },
  maxTime: {
    type: Number,
    default: 0
  },
  timeNumber: {
    type: Number,
    default: 7
  }
});

const timeNumber = props.timeNumber; //时间段数量

const timeRange: Ref<
  {
    timeSecond: number;
    time: string;
  }[]
> = ref([]);

// 初始化时间轴
const initTimeRange = () => {
  const arr = [];
  const currentTimeInterval = props.maxTime - props.minTime;
  for (let i = 0; i <= timeNumber; i++) {
    const timeSecond = props.minTime + (currentTimeInterval * i) / timeNumber;
    const time = dayjs(timeSecond).format('YYYY/MM/DD HH:mm:ss');

    arr.push({ timeSecond, time });
  }
  timeRange.value = arr;
};
watch(
  () => [props.minTime, props.maxTime],
  () => {
    initTimeRange();
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
.sim-gantt-timeline__text {
  width: calc(var(--text-width) * 1%);
  height: 100%;
  position: absolute;
  top: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: #fff;
  //transform: translateX(-50%);
  //border: 1px solid red;
  //text-align: center;
}
.sim-gantt-timeline__tick {
  position: absolute;
  left: 0;
  bottom: 3px;
  width: 100%;
  background-repeat: repeat-x;
  //border: 1px solid red;
}

.sim-gantt-timeline__long {
  background-image: linear-gradient(90deg, fade(white, 50%) 1px, transparent 1px, transparent 100%);
  height: 10px;
  background-size: calc(var(--long-range) * 1%) 100%;
  pointer-events: none;
}
.sim-gantt-timeline__short {
  background-image: linear-gradient(90deg, fade(white, 50%) 1px, transparent 1px, transparent 100%);
  height: 5px;
  background-size: calc(var(--long-range) / 10 * 1%) 100%;
  pointer-events: none;
}
</style>
