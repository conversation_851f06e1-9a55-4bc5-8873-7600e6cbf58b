<!--
* @Author: 任强
* @Date: 2023/4/10 9:17
* @Version: 1.0
* @Content: 
-->
<template>
  <svg
    width="17.000000"
    height="264.000000"
    viewBox="0 0 17 264"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <g>
      <g>
        <path
          d="M16 264L17 264L17 0L16 0L8 8L8 24L11 21L11 28L8 31L8 37L11 40L11 47L8 44L8 115L15 108L15 158L8 151L8 217L8 220L11 217L11 224L8 227L8 233L11 236L11 243L8 240L8 256L16 264Z"
          fill-rule="nonzero"
          fill="url(#paint_linear_15_2686_0)"
        />
      </g>
      <g>
        <path
          d="M2 122L1 122L6 117L6 118L2 122ZM7 150L7 149L3 145L2 145L7 150ZM10 44L10 41L6 37L6 31L10 27L10 24L3 31L3 37L10 44ZM6 233L6 236L10 240L10 237L6 233ZM10 220L10 223L6 227L6 224L10 220Z"
          clip-rule="evenodd"
          fill-rule="evenodd"
          fill="var(--primary-color)"
          fill-opacity="1.000000"
        />
      </g>
      <g class="hover" @click="changeCollapse">
        <g>
          <path d="M0 125L14 111L14 155L0 141L0 125Z" fill-rule="evenodd" fill="url(#paint_linear_15_2694_0)" />
          <path
            d="M0 125L14 111L14 155L0 141L0 125ZM12.9995 113.415L12.9995 152.585L1.0005 140.586L1.0005 125.414L12.9995 113.415Z"
            fill-rule="evenodd"
            fill="var(--primary-color)"
          />
        </g>
        <g>
          <path
            d="M6 28L3 31L0 31L0 37L3 37L6 40L6 37L7 37L7 31L6 31L6 28Z"
            clip-rule="evenodd"
            fill-rule="evenodd"
            fill="var(--primary-color)"
            fill-opacity="1.000000"
          />
        </g>
        <g>
          <path
            d="M6 224L3 227L0 227L0 233L3 233L6 236L6 233L7 233L7 227L6 227L6 224Z"
            clip-rule="evenodd"
            fill-rule="evenodd"
            fill="var(--primary-color)"
            fill-opacity="1.000000"
          />
        </g>
        <g v-show="collapse">
          <g>
            <path d="M5 126L5 129L9 133L5 137L5 140L12 133L5 126Z" fill-rule="evenodd" fill="#FFFFFF" />
          </g>
          <g>
            <path d="M4.99998 130L4.99998 136L8 133L4.99998 130Z" fill-rule="evenodd" fill="#FFFFFF" />
          </g>
        </g>
        <g v-show="!collapse">
          <g>
            <path d="M11 126L11 129L7 133L11 137L11 140L4 133L11 126Z" fill-rule="evenodd" fill="#FFFFFF" />
          </g>
          <g>
            <path d="M11 130L11 136L8 133L11 130Z" fill-rule="evenodd" fill="#FFFFFF" />
          </g>
        </g>
      </g>
      <g>
        <path d="M0 14L2 14L4 16L4 28L1 31L1 37L4 40.5L4 43L4 52L2 54L0 54L0 14Z" fill-rule="evenodd" fill="url(#paint_linear_15_2702_0)" />
      </g>
      <g>
        <path
          d="M0 210L2 210L4 212L4 224L1 227L1 233L4 236.5L4 239L4 248L2 250L0 250L0 210Z"
          fill-rule="evenodd"
          fill="url(#paint_linear_15_2703_0)"
        />
      </g>
    </g>
    <defs>
      <linearGradient id="paint_linear_15_2686_0" x1="6.000000" y1="132.000000" x2="17.000000" y2="132.000000" gradientUnits="userSpaceOnUse">
        <stop stop-color="#FFFFFF" />
        <stop offset="0.450617" stop-color="#A4A4A4" />
        <stop offset="1.000000" stop-color="#FFFFFF" />
      </linearGradient>
      <linearGradient id="paint_linear_15_2694_0" x1="7.000000" y1="111.000000" x2="7.000000" y2="155.000000" gradientUnits="userSpaceOnUse">
        <stop stop-color="rgb(var(--btn-top-color-val))" />
        <stop offset="0.600976" stop-color="rgb(var(--btn-center-color-val))" />
        <stop offset="1.000000" stop-color="rgb(var(--btn-bottom-color-val))" />
      </linearGradient>
      <linearGradient id="paint_linear_15_2702_0" x1="0.000000" y1="33.999981" x2="4.000000" y2="33.999981" gradientUnits="userSpaceOnUse">
        <stop stop-color="#FFFFFF" />
        <stop offset="0.450617" stop-color="#A4A4A4" />
        <stop offset="1.000000" stop-color="#FFFFFF" />
      </linearGradient>
      <linearGradient id="paint_linear_15_2703_0" x1="0.000000" y1="229.999985" x2="4.000000" y2="229.999985" gradientUnits="userSpaceOnUse">
        <stop stop-color="#FFFFFF" />
        <stop offset="0.450617" stop-color="#A4A4A4" />
        <stop offset="1.000000" stop-color="#FFFFFF" />
      </linearGradient>
    </defs>
  </svg>
</template>

<script lang="ts">
export default {
  name: 'NavLeft'
};
</script>
<script setup lang="ts">
defineProps({
  collapse: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(['change-collapse']);

const changeCollapse = () => {
  emits('change-collapse');
};
</script>

<style lang="less" scoped>
.hover {
  &:hover {
    cursor: pointer;
    opacity: 0.8;
  }
}
</style>
