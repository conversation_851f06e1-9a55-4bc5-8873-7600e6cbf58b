/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
export const calculationTime = ({
  minTime,
  maxTime,
  currentStartTime,
  currentEndTime,
  deltaY
}: {
  minTime: number;
  maxTime: number;
  currentStartTime: number;
  currentEndTime: number;
  deltaY: number;
}) => {
  let step = (maxTime - minTime) / 100;
  step = deltaY > 0 ? step : -step;

  let startTime = currentStartTime + step;
  let endTime = currentEndTime + step;
  if (startTime < minTime) {
    endTime += minTime - startTime;
    startTime = minTime;
  }
  if (endTime > maxTime) {
    startTime -= maxTime - endTime;
    endTime = maxTime;
  }
  return {
    startTime,
    endTime
  };
};
