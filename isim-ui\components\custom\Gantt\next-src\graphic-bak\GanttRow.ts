/**
 * @Author: 宋计民
 * @Date: 2023-08-24 15:34
 * @Version: 1.0
 * @Content: index.ts
 */
import { GanttGraphic, GanttGraphicOptions } from './GanttGraphic';
import { GanttGraphicType, isBothMode, isFlatMode, isRangeGraphicType } from './gantt-utils.ts';

export interface GanttBaseDataType<T = any> {
  startTime: number;
  endTime: number;
  id: string;
  height?: number;
  label: string;
  target?: string[];
  flatData?: GanttDataType<T>[];
  children?: GanttDataType<T>[];
  level: number;
}
export type GanttDataType<T = Record<string, any>> = GanttBaseDataType<T> & T;

interface GanttRowRenderOption extends GanttGraphicOptions {
  index?: number;
}
export class GanttRow {
  id: string;
  data: GanttDataType;
  width: number = 0;
  height: number = 0;
  startPos: {
    x: number;
    y: number;
  } = { x: 0, y: 0 };
  stopPos: {
    x: number;
    y: number;
  } = { x: 0, y: 0 };
  index: number;
  ctx: CanvasRenderingContext2D;
  rangeCollection: Array<GanttRange | GanttRangePoint>;
  splitGraphic?: GanttSplitLine;
  options: GanttGraphicOptions;
  graphic: GanttGraphic;
  constructor(data: GanttDataType, config: { ctx: CanvasRenderingContext2D; index: number }, graphic: GanttGraphic) {
    this.ctx = config.ctx;
    this.graphic = graphic;
    this.options = graphic.options;
    this.data = data;
    this.id = data.id;
    this.index = config.index;
    this.rangeCollection = this.createGraphic();
    if (this.options.splitLine) {
      this.splitGraphic = new GanttSplitLine(this.data, { ctx: this.ctx }, this.options);
    }

    // console.log('this.rangeCollection', this.rangeCollection);
  }

  createGraphic() {
    const config = {
      ctx: this.ctx,
      index: this.index
    };
    if (isFlatMode(this.options.mode) && this.data.flatData) {
      return this.data.flatData.map((item) => new GanttRangePoint(item, config, this.options));
    }
    if (isBothMode(this.options.mode)) {
      const rangeCollection: any = [new GanttRange(this.data, config, this.options)];
      if (this.data.flatData) {
        this.data.flatData.forEach((item) => {
          rangeCollection.push(new GanttRangePoint(item, config, this.options));
        });
      }
      return rangeCollection;
    }
    const range = new GanttRange(this.data, config, this.options);
    this.setPos(range);
    return [range];
  }
  get row() {
    return this.options.row;
  }
  get mode() {
    return this.options.mode;
  }
  // 连线源是自己的 连线
  get sourceLine() {
    return Array.from(this.graphic.connectLineList.values()).filter((item) => item.source === this);
  }
  // 连线目标包含自己的 连线
  get targetLine() {
    return Array.from(this.graphic.connectLineList.values()).filter((item) => item.target.includes(this));
  }

  setPos(range: GanttRange) {
    this.startPos = {
      x: range.x,
      y: range.y
    };
    this.stopPos = {
      x: range.x + range.width,
      y: range.y + this.options.row.height
    };
  }

  pick(x: number, y: number) {
    if (x >= this.startPos.x && x <= this.stopPos.x && y >= this.startPos.y && y <= this.stopPos.y) {
      return true;
    }
    return false;
  }

  render() {
    this.rangeCollection.forEach((item) => {
      item.render();
      if (isRangeGraphicType(item)) {
        this.setPos(item);
      }
    });
    this.splitGraphic?.render();
  }
}

interface GraphicConfig {
  index?: number;
  ctx: CanvasRenderingContext2D;
}
class GanttGraphicBase {
  _data: GanttDataType;
  x: number = 0;
  y: number = 0;
  type?: GanttGraphicType;
  options: GanttRowRenderOption;
  style: {
    fillStyle?: string;
    strokeStyle?: string;
  };
  width: number = 0;
  config: GraphicConfig;
  isPointInPath(x: number, _y: number) {
    return x >= this.x && x <= this.x + this.width;
  }
  clear() {
    this.config.ctx.clearRect(this.x, this.y, this.x + this.width, this.y + this.options.row.height);
  }
  constructor(data: GanttDataType, config: GraphicConfig, options: GanttRowRenderOption) {
    this.config = config;
    this._data = data;
    this.options = options;
    this.style = {};
  }
  get index() {
    return this.config.index;
  }
  get data() {
    return this._data;
  }
  get label() {
    return this._data.label;
  }
  get row() {
    return this.options.row;
  }
  calcX() {
    const left = (this.data.startTime - this.options.showStartTime) / this.options.interval;
    if (this.type === GanttGraphicType.rangePoint) {
      this.x = left + 1.5;
      return;
    }
    this.x = left;
  }
  calcY() {
    this.y = (this.row.height + this.row.gap) * (this.index as number) + (this.options.y as number);
  }
  calcPos() {
    this.calcX();
    this.calcY();
  }
}

export class GanttRange extends GanttGraphicBase {
  hoverStyle: {
    rectFillStyle?: string;
  };
  constructor(data: GanttDataType, config: GraphicConfig, options: GanttRowRenderOption) {
    super(data, config, options);
    this.style.fillStyle = '#18576a';
    this.type = GanttGraphicType.range;
    this.hoverStyle = {
      rectFillStyle: '#1a9ac4'
    };
  }
  draw(hover: boolean) {
    const ctx = this.config.ctx;
    const options = this.options;
    ctx.beginPath();
    ctx.fillStyle = hover ? (this.hoverStyle.rectFillStyle as string) : (this.style.fillStyle as string);
    this.calcPos();
    const width = (this.data.endTime - this.data.startTime) / options.interval;
    this.width = width;
    ctx.beginPath();
    ctx.roundRect(this.x, this.y, width, this.row.height, 3);
    ctx.fill();
    ctx.fillStyle = '#fff';
    ctx.textBaseline = 'middle';
    ctx.fillText(this.label, this.x + 5, this.y + this.row.height / 2);
  }
  hover() {
    this.draw(true);
  }
  render() {
    this.draw(false);
  }
}

export class GanttRangePoint extends GanttGraphicBase {
  constructor(data: GanttDataType, config: GraphicConfig, options: GanttRowRenderOption) {
    super(data, config, options);
    this._data = data;
    this.style.fillStyle = '#f00';
    this.type = GanttGraphicType.rangePoint;
  }
  render() {
    const ctx = this.config.ctx;
    ctx.beginPath();
    ctx.fillStyle = this.style.fillStyle as string;
    this.calcPos();
    ctx.arc(this.x, this.y + this.row.height / 2, 5, 0, 2 * Math.PI);
    ctx.fill();
  }
}

/**
 * 分割线
 * 暂未使用 目前使用背景代替
 */
export class GanttSplitLine extends GanttGraphicBase {
  constructor(data: GanttDataType, config: GraphicConfig, options: GanttRowRenderOption) {
    super(data, config, options);
    this.style.strokeStyle = '#eadddd';
    this.type = GanttGraphicType.splintLine;
  }
  render() {
    const ctx = this.config.ctx;
    ctx.beginPath();
    ctx.strokeStyle = this.style.strokeStyle as string;
    this.calcPos();
    ctx.lineWidth = 0.5;
    const y = this.y + this.row.gap / 2 + this.row.height;
    ctx.moveTo(0, y);
    ctx.lineTo(this.options.width, y);
    ctx.stroke();
  }
}

/**
 * 连线
 */
export class GanttConnectLine {
  source: GanttRow;
  target: GanttRow[];
  constructor(source: GanttRow, target: GanttRow[]) {
    this.source = source;
    this.target = target;
  }
  get sourceStartX() {
    return this.source.startPos.x;
  }
  get sourceStartY() {
    return this.source.startPos.y;
  }

  get sourceStopX() {
    return this.source.stopPos.x;
  }

  get sourceStopY() {
    return this.source.stopPos.y;
  }

  getTargetX(target: GanttRow) {
    return target.startPos.x;
  }
  getTargetY(target: GanttRow) {
    return target.startPos.y;
  }
  getAngle(target: GanttRow) {
    const dx = this.getTargetX(target) - this.sourceStopX;
    const dy = this.getTargetY(target) - this.sourceStopY;
    return Math.atan2(dy, dx);
  }
  drawArrow(ctx: CanvasRenderingContext2D, item: GanttRow, type: 'fill' | 'border' = 'fill') {
    ctx.save();
    // const angle = this.getAngle(item);
    ctx.translate(this.getTargetX(item), this.getTargetY(item) + this.source.row.height / 2);
    ctx.rotate(-(Math.PI / 180) * 45);
    const arrowHeight = 10;
    const arrowWidth = 10;
    ctx.beginPath();
    if (type === 'fill') {
      ctx.moveTo(0, 0);
      ctx.lineTo(0, -arrowHeight);
      ctx.lineTo(-arrowWidth, 0);
      ctx.fill();
    } else {
      ctx.moveTo(-arrowWidth, 0);
      ctx.lineTo(0, 0);
      ctx.lineTo(0, -arrowHeight);
      ctx.stroke();
    }
    ctx.restore();
  }
  // 绘制直线
  drawStraightLine(ctx: CanvasRenderingContext2D, startX: number, startY: number, stopX: number, stopY: number) {
    ctx.moveTo(startX, startY);
    ctx.lineTo(startX + 5, startY);
    if (stopX > startX) {
      ctx.lineTo(startX + 5, stopY);
    } else {
      ctx.lineTo(startX + 5, startY + this.source.row.height);
      ctx.lineTo(stopX - 5, startY + this.source.row.height);
      ctx.lineTo(stopX - 5, stopY);
    }

    ctx.lineTo(stopX, stopY);
  }

  borderArrow() {}
  render(ctx: CanvasRenderingContext2D) {
    this.target.forEach((item) => {
      ctx.lineJoin = 'round';
      ctx.beginPath();
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 1;

      const startX = this.sourceStopX;
      const startY = this.sourceStartY + this.source.row.height / 2;
      const stopX = this.getTargetX(item);
      const stopY = this.getTargetY(item) + this.source.row.height / 2;

      this.drawStraightLine(ctx, startX, startY, stopX, stopY);

      ctx.stroke();
      ctx.closePath();
      this.drawArrow(ctx, item, 'fill');
    });
  }
}

type onScrollFunctionType = (scrollTop: number, percentY: number) => void;

export class ScrollBar {
  type: GanttGraphicType;
  width: number;
  x: number;
  y: number;
  options: GanttGraphicOptions;
  config: GraphicConfig;
  graphic: GanttGraphic;
  scrollEvent: onScrollFunctionType[];

  constructor(_data: GanttDataType[], config: { ctx: CanvasRenderingContext2D; graphic: GanttGraphic }, options: GanttGraphicOptions) {
    this.graphic = config.graphic;
    this.config = config;
    this.width = 7;
    this.y = 0;
    this.x = options.width - this.width;
    this.type = GanttGraphicType.scrollBar;
    this.options = options;
    this.scrollEvent = [];

    this.registerDragEvent();
  }

  /**
   * 滚动效果
   * @param deltaY
   */
  scrollTo(deltaY: number) {
    const scrollStep = deltaY / 10;
    this.setScrollY(this.y + scrollStep);
  }

  setScrollY(currentY: number) {
    const maxY = this.options.height - this.height;
    const maxOptionY = -(this.totalHeight - this.options.height);

    if (currentY != this.y) {
      this.y = currentY;
      this.options.y = (this.y * maxOptionY) / maxY;
    }

    if (this.y <= 0) {
      this.y = 0;
      this.graphic.options.y = 0;
    } else if (this.y >= maxY) {
      this.y = maxY;
      this.graphic.options.y = maxOptionY;
    }

    this.emitScroll();
  }

  onScroll(fn: onScrollFunctionType) {
    this.scrollEvent.push(fn);
  }
  emitScroll() {
    const maxY = this.options.height - this.height;
    const percentY = this.y / maxY;
    this.scrollEvent.forEach((fn) => {
      fn(this.y, percentY);
    });
  }

  get length() {
    return this.graphic.graphicList.size;
  }

  get totalHeight() {
    return this.length * (this.options.row.height + this.options.row.gap);
  }

  get height() {
    return (this.options.height / this.totalHeight) * this.options.height;
  }

  pick(x: number, y: number) {
    return x >= this.x && x <= this.x + this.width && y >= this.y && y <= this.y + this.height;
  }

  // 注册拖动滚动条事件
  registerDragEvent() {
    const dom = this.graphic.dom;
    let clientY = 0;
    let currentY = 0;
    // let currentOptionY = 0;

    dom.addEventListener('mousedown', (e: MouseEvent) => {
      if (!this.pick(e.offsetX, e.offsetY)) return;
      currentY = this.y;
      // currentOptionY = this.y;
      clientY = e.clientY;
      document.addEventListener('mousemove', mouseMove);
      document.addEventListener('mouseup', mouseUp);
    });
    const mouseMove = (e: MouseEvent) => {
      const offsetY = e.clientY - clientY;
      this.setScrollY(currentY + offsetY);
      this.graphic.render();
    };

    const mouseUp = (_e: MouseEvent) => {
      document.removeEventListener('mousemove', mouseMove);
      document.removeEventListener('mouseup', mouseUp);
    };
  }

  render() {
    if (this.totalHeight <= this.options.height) return;
    const ctx = this.config.ctx;
    ctx.fillStyle = '#cbcbcb';
    ctx.fillRect(this.x, this.y, this.width, this.height);
  }
}
