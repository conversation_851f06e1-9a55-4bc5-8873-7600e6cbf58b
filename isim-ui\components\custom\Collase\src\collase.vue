<!--
* <AUTHOR> 王磊磊
* @Date :  2023/4/12 14:13
* @Version : 1.0
* @Content :
-->
<template>
  <el-collapse v-bind="$attrs">
    <slot></slot>
  </el-collapse>
</template>

<script lang="ts">
export default {
  name: 'SimCollapse'
};
</script>

<script lang="ts" setup>
// const activeNames = ref<string>('1')
defineProps({
  title: {
    type: String,
    default: '标题'
  }
});
</script>

<style scoped lang="less">
:deep(.el-collapse-item__arrow) {
  padding-right: 2px;
  background: url('./y-icon-zhankai.svg') no-repeat;
  position: absolute;
  left: 8px;
  transform: rotate(-90deg);
}

:deep(.el-collapse-item__arrow svg) {
  display: none;
}

:deep(.el-collapse-item__header) {
  padding: 0 30px;
  color: rgba(var(--text-color));
  background-color: rgba(var(--primary-color-val), 0.15);
  position: relative;
}
:deep(.el-collapse-item__header:hover) {
  background-color: rgba(var(--primary-color-val), 0.3);
}
:deep(.el-collapse-item__arrow.is-active) {
  transform: rotate(0deg);
}
.sim-collapse {
}
</style>
