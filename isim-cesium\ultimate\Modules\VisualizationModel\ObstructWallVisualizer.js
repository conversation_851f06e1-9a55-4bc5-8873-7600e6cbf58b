import AbstractPrimitive from './AbstractVisualizer';
import {
  ArcType,
  Cartesian2,
  Cartesian3,
  Color,
  defaultValue,
  GeometryInstance,
  Material,
  PolylineGeometry,
  PolylineMaterialAppearance,
  Primitive,
  SceneMode
} from 'cesium';
import ModelWallPrimitive from './Components/ModelWallPrimitive';

export default class ObstructWallVisualizer extends AbstractPrimitive {
  constructor(options) {
    options = defaultValue(options, defaultValue.EMPTY_OBJECT);
    super(options);
    this._url = options.url;
    this._positions = options.positions;
    this._gapSpace = defaultValue(options.gapSpace, 1);
    this._heading = defaultValue(options.heading, 0);
    this._modelHeight = defaultValue(options.modelHeight, 1.5);
    this._wallImage = options.wallImage;

    this._color = defaultValue(options.color, Color.YELLOW);
    this._image = options.image;
    this._width = defaultValue(options.width, 20);
  }

  update(frameState) {
    if (!this.show) {
      return;
    }

    if (this._update) {
      this._update = false;
      this._primitive3D = this._primitive3D && this._primitive3D.destroy();
      this._primitive2D = this._primitive2D && this._primitive2D.destroy();
      this._update3D();
      this._update2D();
    }

    if (frameState.mode === SceneMode.SCENE3D) {
      this._primitive3D?.update(frameState);
    } else if (frameState.mode === SceneMode.SCENE2D) {
      this._primitive2D?.update(frameState);
    }
  }

  _update3D() {
    if (!this._positions || this._positions?.length < 2) {
      return;
    }
    this._primitive3D = new ModelWallPrimitive({
      url: this._url,
      gapSpace: this._gapSpace,
      positions: this._positions,
      heading: this._heading,
      modelHeight: this._modelHeight,
      image: this._wallImage
    });
  }

  _update2D() {
    if (!this._positions || this._positions?.length < 2) {
      return;
    }

    const positions = this._positions;
    let dis = 0;
    for (let i = 0; i < positions.length - 1; i++) {
      dis += Cartesian3.distance(positions[i], positions[i + 1]);
    }

    this._primitive2D = new Primitive({
      geometryInstances: new GeometryInstance({
        geometry: new PolylineGeometry({
          positions: this._positions,
          width: this._width,
          arcType: ArcType.NONE
        })
      }),
      appearance: new PolylineMaterialAppearance({
        material: Material.fromType(Material.ImageType, {
          image: this._image,
          repeat: new Cartesian2(dis / this._gapSpace, 1.0),
          color: this._color
        })
      })
    });
  }
}
