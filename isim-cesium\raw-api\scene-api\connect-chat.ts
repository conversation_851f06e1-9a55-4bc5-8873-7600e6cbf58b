/**
 * @Author: 宋计民
 * @Date: 2023-09-05 14:59
 * @Version: 1.0
 * @Content: 接图表数据加载
 */

import { ArcType, Color, GeoJsonDataSource, PolylineGraphics } from 'cesium';
import { getCurrentTimeJulian, getViewer, getViewerName } from 'isim-cesium';

const connectChartMap = new Map<string, GeoJsonDataSource>();

/**
 * 原生获得 json 请求
 * @param {object} options
 * @param {object} options.url geoserver url： http://192.168.100.116:9700/geoserver
 * @param {string} options.workspace 工作空间
 * @param {string} options.layerName 图层名称
 * @param {string} options.searchField 查询字段
 * @param {string} options.searchValue 查询值
 * @param {string} options.maxFeatures 查询的数量
 */
interface LocationFuzzyQueryOption {
  //url: string // geoserver url： http://192.168.100.116:9700/geoserver
  workspace: string; // 工作空间
  layerName: string; // 图层名称
  searchField?: string; // 查询字段
  searchValue?: string; // 查询值
  maxFeatures?: number; // 查询的数量
}
export function locationFuzzyQuery(options: LocationFuzzyQueryOption) {
  const { workspace, layerName, searchField, searchValue, maxFeatures = 50 } = options;
  const WFS_OPTIONS: Record<string, any> = {
    version: '1.0.0',
    request: 'GetFeature',
    outputFormat: 'application/json',
    typeName: `${workspace}:${layerName}`
  };

  if (maxFeatures) {
    WFS_OPTIONS.maxFeatures = maxFeatures;
  }

  if (searchField) {
    WFS_OPTIONS.cql_filter = encodeURIComponent(`${searchField} like '%${searchValue}%'`);
  }

  let queryStr = '';
  Object.keys(WFS_OPTIONS).forEach((k) => {
    queryStr += `&${k}=${WFS_OPTIONS[k]}`;
  });

  const requestUrl = `/vector/geoserver/${workspace}/ows?service=WFS${queryStr}`;

  return fetch(requestUrl)
    .then((res) => {
      return res.json();
    })
    .then((data) => {
      return Promise.resolve(data);
    })
    .catch((err) => {
      return Promise.reject(err);
    });
}

interface AddNavigationChartsOption {
  clampToGround: boolean;
  color: Color;
  width: number;
}
/**
 * // 接图表 加载方法
 * add Navigational Charts
 * @param {object} options
 * @param viewerName
 * @param {boolean} options.clampToGround
 * @param {Color} options.color
 * @param {number} options.width
 */
export async function addConnectChart(options?: AddNavigationChartsOption, viewerName = getViewerName()) {
  if (connectChartMap.has(viewerName)) {
    return;
  }
  const data = await locationFuzzyQuery({ workspace: 'charts', layerName: 'navigational_charts' });
  data.features = data.features.reverse();
  const { clampToGround = true, color = Color.RED, width = 2 } = options ?? {};
  const source = await GeoJsonDataSource.load(data, {
    clampToGround: clampToGround,
    fill: Color.TRANSPARENT,
    stroke: color,
    strokeWidth: width
  });

  if (clampToGround) {
    source.entities.values.forEach((item) => {
      if (item.polygon) {
        const hierarchy = item.polygon.hierarchy?.getValue(getCurrentTimeJulian());
        const positions = hierarchy.positions;
        item.polyline = new PolylineGraphics({
          positions: [...positions, positions[0]],
          clampToGround,
          material: color,
          width: width,
          arcType: ArcType.RHUMB
        });
      }
    });
  }
  const viewer = getViewer(viewerName);
  viewer.dataSources.add(source);
  connectChartMap.set(viewerName, source);
  return source;
}

export function removeConnectChart(viewerName = getViewerName()) {
  const viewer = getViewer(viewerName);
  if (connectChartMap.has(viewerName)) {
    const source = connectChartMap.get(viewerName)!;
    source?.entities.removeAll();
    viewer.dataSources.remove(source, true);
    connectChartMap.delete(viewerName);
  }
}
