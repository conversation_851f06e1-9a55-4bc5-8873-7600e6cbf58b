/**
 * @Author: 宋计民
 * @Date: 2023-07-31 11:14
 * @Version: 1.0
 * @Content: clock-time-api.ts
 */
import { getClock, getClockField, getViewerName, onViewerCreated, ViewerNameType } from 'isim-cesium';
import dayjs from 'dayjs';
import { JulianDate } from 'cesium';

type ClockTimeType = 'currentTime' | 'startTime' | 'stopTime';

// 当前时间
export const getCurrentTimeJulian = getClockField('currentTime');
export const getCurrentTimeDayJs = getDayjsTimeByField('currentTime');
export const setCurrentTimeJulian = setClockTime('currentTime');
export const setCurrentTime = setCurrentTimeJulian;

// 开始时间
export const getStartTimeJulian = getClockField('startTime');
export const getStartTimeDayJs = getDayjsTimeByField('startTime');

export const setStartTimeJulian = setClockTime('startTime');
export const setStartTime = setStartTimeJulian;

// 结束时间

export const getStopTimeJulian = getClockField('stopTime');

export const getStopTimeDayJs = getDayjsTimeByField('stopTime');

export const setStopTimeJulian = setClockTime('stopTime');
export const setStopTime = setStopTimeJulian;

export function getInfinityTime() {
  return '9999/12/31 23:59:59';
}

/**
 * 设置无穷大结束时间
 */
export function setStopTimeInfinity(viewerName?: ViewerNameType) {
  setStopTime(getInfinityTime(), viewerName);
}

function getDayjsTimeByField<T extends ClockTimeType>(field: T) {
  return function (viewerName?: ViewerNameType) {
    return dayjs(JulianDate.toDate(getClock(viewerName)[field]));
  };
}

function setClockTime<T extends ClockTimeType>(field: T) {
  return function (value: string | number | Date | JulianDate, viewerName?: ViewerNameType) {
    let _value: JulianDate;
    if (typeof value === 'string' || typeof value === 'number') {
      _value = JulianDate.fromDate(new Date(value));
    } else if (value instanceof Date) {
      _value = JulianDate.fromDate(value);
    } else {
      _value = value;
    }
    getClock(viewerName)[field] = _value;
  };
}
type WatchCurrentTimeFn = (time: number, julianDate: JulianDate) => void;

/**
 * 监听当前仿真时间
 * @param cb
 * @param viewerName
 */
export function watchCurrentTime(cb: WatchCurrentTimeFn, viewerName = getViewerName()) {
  onViewerCreated(
    (viewer) => {
      // @ts-ignore
      Cesium.knockout.getObservable(viewer.clockViewModel, 'currentTime').subscribe((val) => {
        const _time = JulianDate.toDate(val).getTime();
        cb?.(_time, val);
      });
    },
    {
      viewerName
    }
  );
}
