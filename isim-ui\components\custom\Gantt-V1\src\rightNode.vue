<!--
 * @Author: songjimin
 * @Date: 2022-04-18 17:49:20
 * @Version: 0.0.1
 * @Content:
-->
<template>
  <div :class="ns.b()">
    <div :class="ns.e('current')">
      <slot name="range" :data="props.data" :offset="offsetPixel" :width="widthPixel" :update-range-fn="updateRangeFn">
        <div :class="[ns.e('range')]" :style="rangeStyle" @dblclick.stop="rangeDoubleClick(data)" @contextmenu="rangeContextmenu($event)">
          <span>{{ rangeText }}</span>
        </div>
      </slot>
    </div>
    <y-gantt-right v-for="(item, dex) in data[childrenField]" v-show="data.expand" :key="dex" :data="item" :deep="deep + 1">
      <template #range="{ data: rangeData, offset, width, updateRangeFn: rangeUpdateRangeFn }">
        <slot name="range" :data="rangeData" :offset="offset" :width="width" :update-range-fn="rangeUpdateRangeFn"></slot>
      </template>
    </y-gantt-right>
  </div>
</template>

<script>
export default {
  name: 'YGanttRight'
};
</script>

<script setup>
import { inject, computed } from 'vue';
import { useNamespace } from './methods/namespace.js';
import { GanttNode } from './methods/ganttNode';
const props = defineProps({
  data: {
    required: true,
    type: Object
  },
  deep: {
    type: Number,
    default: 0
  }
});

const { startTime, endTime, rangeDoubleClick, rightWidth, labelField, childrenField } = inject('gantt');

const rangeText = computed(() => {
  return typeof labelField.value === 'function' ? labelField.value(props.data) : props.data[labelField.value];
});

const ns = useNamespace('gantt-right');

const updateRangeFn = (currentTime) => {
  const timeBarSecondsSpan = endTime.value - startTime.value;
  const second = GanttNode.getTime(currentTime) - GanttNode.getTime(startTime.value);
  const xPos = Math.round((second * rightWidth.value) / timeBarSecondsSpan);
  return xPos;
};
const offsetPixel = computed(() => {
  return updateRangeFn(props.data._startTime);
});
const widthPixel = computed(() => {
  return updateRangeFn(props.data._endTime) - offsetPixel.value;
});
const rangeStyle = computed(() => {
  // TODO 此处有修改
  // const _statTime = props.data._startTime < startTime.value ? startTime.value : props.data._startTime;
  // const tranlateX = getPxFromSecond(props.data._startTime - GanttNode.getTime(startTime.value));

  // const timeBarSecondsSpan = endTime.value - startTime.value
  // const second = props.data._startTime - GanttNode.getTime(startTime.value)
  // const xPos = Math.round(
  //     (second * rightWidth.value) / timeBarSecondsSpan
  // );
  // const xPos = updateRangeFn(props.data._startTime)
  // const _width = updateRangeFn(props.data._endTime) - xPos
  // const _width = getPxFromSecond(props.data._endTime - GanttNode.getTime(_statTime));
  // const _width = Math.round(
  //     ((props.data._endTime - GanttNode.getTime(_statTime)) * rightWidth.value) / timeBarSecondsSpan
  // );
  return {
    transform: `translateX(${offsetPixel.value + 1}px)`,
    width: widthPixel.value + 'px'
  };
});

const { handleContextmenu } = inject('event');

const rangeContextmenu = (event) => {
  handleContextmenu(event, props.data);
};
</script>
